#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎲 GÉNÉRATEUR BACCARAT AUTONOME
===============================

Générateur séparé identique au générateur intégré du programme principal.
Produit des données dans le même format exact que le programme principal.

Usage: python baccarat_generator_standalone.py
"""

import random
import json
import time
import gc
import os
import re
from datetime import datetime
from typing import Dict, List, Any
from collections import Counter

class BaccaratGeneratorStandalone:
    """Générateur de parties baccarat autonome - identique au générateur intégré"""

    def __init__(self):
        self.shoe = []
        self.position = 0
        self.burn_cards = []
        self.cut_card_position = 312  # 3/4 de 416 cartes (75% de pénétration)

    def reset_shoe(self):
        """Crée un nouveau sabot de 8 jeux mélangé"""
        self.shoe = []
        for _ in range(8):  # 8 jeux
            for suit in ['♠', '♥', '♦', '♣']:  # 4 couleurs
                for value in ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']:  # 13 valeurs
                    self.shoe.append(value)

        random.shuffle(self.shoe)
        self.position = 0
        self.burn_cards = []
        self._burn_cards()

    def _burn_cards(self):
        """Brûle les cartes initiales selon les règles du baccarat"""
        if len(self.shoe) < 12:  # Assurer qu'on a assez pour le maximum (11 cartes)
            raise Exception("Sabot insuffisant pour brûlage")

        # ÉTAPE 1: Première carte détermine combien d'AUTRES cartes brûler
        first_card = self.shoe[self.position]
        self.position += 1

        # Valeur de la première carte = nombre d'AUTRES cartes à brûler
        if first_card in ['J', 'Q', 'K']:
            additional_burn_count = 10  # 10 AUTRES cartes
        elif first_card == 'A':
            additional_burn_count = 1   # 1 AUTRE carte
        else:
            additional_burn_count = int(first_card)  # N AUTRES cartes

        # ÉTAPE 2: Ajouter la première carte aux cartes brûlées
        self.burn_cards.append(first_card)

        # ÉTAPE 3: Brûler les cartes supplémentaires
        for _ in range(additional_burn_count):
            if self.position < len(self.shoe):
                self.burn_cards.append(self.shoe[self.position])
                self.position += 1
            else:
                raise Exception("Sabot épuisé pendant le brûlage")

        # VÉRIFICATION: Total brûlé = 1 (première) + additional_burn_count
        actual_burned = len(self.burn_cards)
        expected_total = 1 + additional_burn_count

        if actual_burned != expected_total:
            raise Exception(f"Brûlage incorrect: {actual_burned} cartes, attendu {expected_total}")

        # VÉRIFICATION: Assurer que nous respectons les limites 2-11
        if actual_burned < 2 or actual_burned > 11:
            raise Exception(f"Brûlage invalide: {actual_burned} cartes (doit être 2-11)")

    def _draw_card(self):
        """Tire une carte du sabot"""
        if self.position >= self.cut_card_position:
            raise Exception("Cut card atteinte - Nouveau sabot nécessaire")

        if self.position >= len(self.shoe):
            raise Exception("Sabot épuisé")

        card = self.shoe[self.position]
        self.position += 1
        return card

    def _card_value(self, card: str) -> int:
        """Retourne la valeur baccarat d'une carte"""
        if card in ['J', 'Q', 'K']:
            return 0
        elif card == 'A':
            return 1
        else:
            return int(card)

    def _calculate_total(self, cards: List[str]) -> int:
        """Calcule le total baccarat (modulo 10)"""
        return sum(self._card_value(card) for card in cards) % 10

    def play_hand(self) -> Dict[str, Any]:
        """Joue une manche complète de baccarat"""
        # Distribution initiale
        player_cards = [self._draw_card(), self._draw_card()]
        banker_cards = [self._draw_card(), self._draw_card()]

        player_total = self._calculate_total(player_cards)
        banker_total = self._calculate_total(banker_cards)

        cards_used = 4

        # Règles de tirage de la troisième carte - BACCARAT OFFICIEL

        # ÉTAPE 1: Player tire si total <= 5
        player_drew_third = False
        player_third_card_value = None

        if player_total <= 5:
            player_cards.append(self._draw_card())
            player_total = self._calculate_total(player_cards)
            cards_used += 1
            player_drew_third = True
            player_third_card_value = self._card_value(player_cards[2])

        # ÉTAPE 2: Règles du Banker (dépendent de si Player a tiré)
        if player_drew_third:
            # Player a tiré une 3ème carte - Règles complexes du Banker
            if banker_total <= 2:
                # Banker tire toujours avec 0, 1, 2
                banker_cards.append(self._draw_card())
                cards_used += 1
            elif banker_total == 3:
                # Banker tire sauf si 3ème carte Player = 8
                if player_third_card_value != 8:
                    banker_cards.append(self._draw_card())
                    cards_used += 1
            elif banker_total == 4:
                # Banker tire si 3ème carte Player = 2,3,4,5,6,7
                if player_third_card_value in [2, 3, 4, 5, 6, 7]:
                    banker_cards.append(self._draw_card())
                    cards_used += 1
            elif banker_total == 5:
                # Banker tire si 3ème carte Player = 4,5,6,7
                if player_third_card_value in [4, 5, 6, 7]:
                    banker_cards.append(self._draw_card())
                    cards_used += 1
            elif banker_total == 6:
                # Banker tire si 3ème carte Player = 6,7
                if player_third_card_value in [6, 7]:
                    banker_cards.append(self._draw_card())
                    cards_used += 1
            # Banker avec 7, 8, 9 ne tire jamais
        else:
            # Player n'a PAS tiré - Banker tire si total <= 5
            if banker_total <= 5:
                banker_cards.append(self._draw_card())
                cards_used += 1

        # Recalculer le total final du Banker
        banker_total = self._calculate_total(banker_cards)

        # Déterminer le résultat
        if player_total > banker_total:
            result = 'PLAYER'
        elif banker_total > player_total:
            result = 'BANKER'
        else:
            result = 'TIE'

        return {
            'result': result,
            'player_total': player_total,
            'banker_total': banker_total,
            'cards_used': cards_used,
            'player_cards': player_cards,
            'banker_cards': banker_cards
        }

    def is_game_pure_parity(self, game_data: Dict[str, Any], target_parity: str) -> bool:
        """
        Vérifie si une partie est 100% de la parité spécifiée

        Args:
            game_data: Données de la partie
            target_parity: 'PAIR' ou 'IMPAIR'

        Returns:
            True si toutes les manches ont la parité cible
        """
        parities = [hand['parity'] for hand in game_data['hands']]
        return all(parity == target_parity for parity in parities) and len(parities) > 0

    def generate_game_data(self, target_pb_hands: int = 60) -> Dict[str, Any]:
        """Génère une partie complète avec données exploitables"""
        self.reset_shoe()

        hands = []
        pb_count = 0
        total_hands = 0

        # Données de brûlage
        burn_parity = 'PAIR' if len(self.burn_cards) % 2 == 0 else 'IMPAIR'

        # État de synchronisation initial
        current_sync = 'SYNC' if burn_parity == 'PAIR' else 'DESYNC'

        # Jouer jusqu'au cut card ou limite de sécurité
        while pb_count < target_pb_hands:
            try:
                hand_data = self.play_hand()
                total_hands += 1

                # Déterminer la parité
                parity = 'PAIR' if hand_data['cards_used'] % 2 == 0 else 'IMPAIR'

                # Mettre à jour l'état de synchronisation
                if parity == 'IMPAIR':
                    current_sync = 'DESYNC' if current_sync == 'SYNC' else 'SYNC'

                # Compter les manches P/B
                if hand_data['result'] in ['PLAYER', 'BANKER']:
                    pb_count += 1
                    hand_data['pb_hand_number'] = pb_count
                else:
                    hand_data['pb_hand_number'] = pb_count  # TIE garde le même numéro

                # Ajouter les métadonnées
                hand_data['parity'] = parity
                hand_data['sync_state'] = current_sync

                hands.append(hand_data)

                # Limite de sécurité
                if total_hands > 100:
                    break

            except Exception:
                # Cut card atteinte ou sabot épuisé
                break

        # Calculer les conversions S/O
        self._calculate_so_conversions(hands)

        return {
            'burn_cards_count': len(self.burn_cards),
            'burn_parity': burn_parity,
            'total_hands': total_hands,
            'pb_hands': pb_count,
            'tie_hands': total_hands - pb_count,
            'hands': hands
        }

    def _calculate_so_conversions(self, hands: List[Dict]):
        """Calcule les conversions S/O pour toutes les manches"""
        for i, hand in enumerate(hands):
            if hand['result'] in ['PLAYER', 'BANKER'] and i > 0:
                # Trouver la manche P/B précédente
                prev_pb_result = None
                for j in range(i-1, -1, -1):
                    if hands[j]['result'] in ['PLAYER', 'BANKER']:
                        prev_pb_result = hands[j]['result']
                        break

                if prev_pb_result:
                    if hand['result'] == prev_pb_result:
                        hand['so_conversion'] = 'S'  # Same
                    else:
                        hand['so_conversion'] = 'O'  # Opposite
                else:
                    hand['so_conversion'] = '--'  # Première manche P/B
            else:
                hand['so_conversion'] = '--'  # TIE ou première manche

# ============================================================================
# FONCTIONS DE LECTURE DES COMBINAISONS CALCULÉES
# ============================================================================

def load_burn_combinations():
    """Charge les combinaisons de brûlage depuis le fichier sauvegardé"""

    # Chercher le fichier de combinaisons le plus récent
    combination_files = [f for f in os.listdir('.') if f.startswith('combinaisons_brulage_') and f.endswith('.txt')]

    if not combination_files:
        print("❌ Aucun fichier de combinaisons trouvé !")
        print("🔧 Exécutez d'abord: python calculateur_combinaisons_brulage.py")
        return None

    # Prendre le plus récent
    latest_file = max(combination_files)
    print(f"📁 Chargement des combinaisons depuis: {latest_file}")

    combinations = {}

    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Parser le contenu pour extraire les combinaisons
        combinations = parse_combinations_file(content)

        print(f"✅ Combinaisons chargées avec succès !")
        print(f"📊 Brûlages disponibles: {list(combinations.keys())}")

        return combinations

    except Exception as e:
        print(f"❌ Erreur lors du chargement: {e}")
        return None

def parse_combinations_file(content):
    """Parse le fichier de combinaisons pour extraire les données"""

    combinations = {}

    # Regex pour trouver les sections de brûlage
    burn_pattern = r'🔥 BRÛLAGE (\d+) CARTES \((\w+)\)'
    burn_matches = re.finditer(burn_pattern, content)

    for match in burn_matches:
        burn_count = int(match.group(1))
        burn_parity = match.group(2)

        combinations[burn_count] = {
            'burn_parity': burn_parity,
            'pair_combinations': [],
            'impair_combinations': []
        }

        # Trouver la section correspondante
        start_pos = match.end()

        # Chercher les combinaisons PAIR
        pair_section = re.search(r'COMBINAISONS 100% PAIR:(.*?)(?=COMBINAISONS 100% IMPAIR:|🔥|\Z)',
                                content[start_pos:], re.DOTALL)
        if pair_section:
            pair_combos = parse_combination_section(pair_section.group(1), 'PAIR')
            combinations[burn_count]['pair_combinations'] = pair_combos

        # Chercher les combinaisons IMPAIR
        impair_section = re.search(r'COMBINAISONS 100% IMPAIR:(.*?)(?=🔥|\Z)',
                                  content[start_pos:], re.DOTALL)
        if impair_section:
            impair_combos = parse_combination_section(impair_section.group(1), 'IMPAIR')
            combinations[burn_count]['impair_combinations'] = impair_combos

    return combinations

def parse_combination_section(section_text, combo_type):
    """Parse une section de combinaisons"""

    combinations = []

    # Regex pour extraire les combinaisons
    if combo_type == 'PAIR':
        # Format: "1. 0×4c + 1×6c = 1 manches, 6 cartes"
        pattern = r'(\d+)\.\s+(\d+)×4c \+ (\d+)×6c = (\d+) manches, (\d+) cartes'
    else:
        # Format: "1. 1×5c = 1 manches, 5 cartes"
        pattern = r'(\d+)\.\s+(\d+)×5c = (\d+) manches, (\d+) cartes'

    matches = re.finditer(pattern, section_text)

    for match in matches:
        if combo_type == 'PAIR':
            combo = {
                'hands_4': int(match.group(2)),
                'hands_5': 0,
                'hands_6': int(match.group(3)),
                'total_hands': int(match.group(4)),
                'total_cards': int(match.group(5))
            }
        else:
            combo = {
                'hands_4': 0,
                'hands_5': int(match.group(2)),
                'hands_6': 0,
                'total_hands': int(match.group(3)),
                'total_cards': int(match.group(4))
            }

        combinations.append(combo)

    return combinations

def generate_deterministic_game(generator, burn_count, combination, target_parity):
    """Génère une partie déterministe basée sur une combinaison spécifique"""

    try:
        # Créer une partie avec le brûlage spécifié
        # Pour l'instant, on utilise la génération normale et on vérifie
        # TODO: Implémenter la construction déterministe exacte

        max_attempts = 1000  # Limite pour éviter les boucles infinies

        for attempt in range(max_attempts):
            # Générer une partie avec contraintes
            game_data = generator.generate_game_data(999)  # Pas de limite artificielle

            # Vérifier si elle correspond à la combinaison demandée
            if game_data['burn_cards_count'] == burn_count:
                # Compter les manches par type
                hands_4 = sum(1 for hand in game_data['hands'] if hand['cards_used'] == 4)
                hands_5 = sum(1 for hand in game_data['hands'] if hand['cards_used'] == 5)
                hands_6 = sum(1 for hand in game_data['hands'] if hand['cards_used'] == 6)

                # Vérifier si ça correspond à la combinaison
                if (hands_4 == combination['hands_4'] and
                    hands_5 == combination['hands_5'] and
                    hands_6 == combination['hands_6']):

                    # Vérifier la pureté
                    parities = [hand['parity'] for hand in game_data['hands']]

                    if target_parity == 'PAIR':
                        if all(p == 'PAIR' for p in parities) and len(parities) > 0:
                            return game_data
                    elif target_parity == 'IMPAIR':
                        if all(p == 'IMPAIR' for p in parities) and len(parities) > 0:
                            return game_data

        # Si on n'a pas trouvé de correspondance exacte, retourner None
        return None

    except Exception as e:
        return None

def generate_massive_data_tabular(num_games: int = 100000, hands_per_game: int = 60, output_filename: str = None) -> str:
    """
    Génère un grand nombre de parties au format tabulaire lisible

    Args:
        num_games: Nombre de parties à générer
        hands_per_game: Nombre de manches P/B par partie
        output_filename: Nom du fichier de sortie (optionnel)

    Returns:
        Nom du fichier créé
    """
    print(f"🎲 GÉNÉRATION DE {num_games:,} PARTIES BACCARAT - FORMAT TABULAIRE")
    print("=" * 70)
    print(f"Mode: Générateur autonome - Format texte lisible")
    print(f"Manches P/B par partie: {hands_per_game}")
    print()

    generator = BaccaratGeneratorStandalone()

    # Statistiques globales
    total_hands_generated = 0
    total_pb_hands = 0
    total_tie_hands = 0
    total_so_conversions = 0

    start_time = time.time()

    # Créer le fichier de sortie
    if not output_filename:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"baccarat_tabular_{num_games}games_{timestamp}.txt"

    print(f"💾 Création du fichier: {output_filename}")

    with open(output_filename, 'w', encoding='utf-8') as f:
        # En-tête du fichier
        f.write("🎲 DONNÉES BACCARAT - FORMAT TABULAIRE\n")
        f.write("=" * 80 + "\n")
        f.write(f"Générateur: Baccarat Standalone v1.0\n")
        f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Parties générées: {num_games:,}\n")
        f.write(f"Objectif manches P/B par partie: {hands_per_game}\n")
        f.write("=" * 80 + "\n\n")

        # En-tête des colonnes
        f.write("FORMAT DES DONNÉES:\n")
        f.write("-" * 50 + "\n")
        f.write("Chaque partie contient :\n")
        f.write("• BRÛLAGE (M0): Parité du brûlage + état SYNC/DESYNC initial déterminé\n")
        f.write("• 5 INDEX PARALLÈLES pour chaque manche (M1, M2, M3...):\n")
        f.write("  1. PAIR/IMPAIR: Parité des cartes utilisées par manche\n")
        f.write("  2. SYNC/DESYNC: État de synchronisation évolutif par manche\n")
        f.write("  3. COMBINÉ: Fusion des index 1+2 (PAIR_SYNC, PAIR_DESYNC, IMPAIR_SYNC, IMPAIR_DESYNC)\n")
        f.write("  4. S/O: Conversions Same/Opposite (-- pour TIE ou première manche P/B)\n")
        f.write("  5. MANCHES: Résultats des manches (P=Player, B=Banker, T=Tie)\n")
        f.write("\n")
        f.write("RÈGLES IMPORTANTES:\n")
        f.write("• Brûlage PAIR → État initial SYNC\n")
        f.write("• Brûlage IMPAIR → État initial DESYNC\n")
        f.write("• Chaque manche IMPAIR change l'état SYNC ↔ DESYNC\n")
        f.write("• Chaque manche PAIR maintient l'état actuel\n")
        f.write("\n")
        f.write("THÉORIE AZR - PRÉDICTIONS:\n")
        f.write("• MANCHE 1: Pas de S/O (première manche P/B)\n")
        f.write("• MANCHE 2: Premier S/O disponible, mais données insuffisantes\n")
        f.write("• MANCHE 3: PREMIÈRE PRÉDICTION AZR POSSIBLE (toutes données de base)\n")
        f.write("  → Séquence PAIR/IMPAIR complète depuis brûlage\n")
        f.write("  → États SYNC/DESYNC établis\n")
        f.write("  → Deux résultats P/B + premier S/O disponibles\n")
        f.write("\n" + "=" * 80 + "\n\n")

        for game_num in range(num_games):
            # Affichage du progrès
            if (game_num + 1) % 1000 == 0:
                progress = (game_num + 1) / num_games * 100
                elapsed = time.time() - start_time
                speed = (game_num + 1) / elapsed
                print(f"📊 Progression: {game_num + 1:,}/{num_games:,} ({progress:.1f}%) - {speed:.0f} parties/sec")

                # Libération mémoire périodique
                if (game_num + 1) % 10000 == 0:
                    gc.collect()

            # Générer une partie
            game_data = generator.generate_game_data(hands_per_game)

            # Mise à jour des statistiques
            total_hands_generated += game_data['total_hands']
            total_pb_hands += game_data['pb_hands']
            total_tie_hands += game_data['tie_hands']

            # Extraire les séquences pour format tabulaire
            parity_sequence = []
            sync_sequence = []
            combined_sequence = []  # NOUVEL INDEX COMBINÉ
            so_sequence = []
            result_sequence = []

            for hand in game_data['hands']:
                # Parité
                parity_sequence.append(hand['parity'])

                # État de synchronisation
                sync_sequence.append(hand['sync_state'])

                # INDEX COMBINÉ : PAIR/IMPAIR + SYNC/DESYNC
                parity = hand['parity']
                sync_state = hand['sync_state']

                if parity == 'PAIR' and sync_state == 'SYNC':
                    combined_sequence.append('PAIR_SYNC')
                elif parity == 'PAIR' and sync_state == 'DESYNC':
                    combined_sequence.append('PAIR_DESYNC')
                elif parity == 'IMPAIR' and sync_state == 'SYNC':
                    combined_sequence.append('IMPAIR_SYNC')
                else:  # IMPAIR + DESYNC
                    combined_sequence.append('IMPAIR_DESYNC')

                # Conversion S/O
                so_sequence.append(hand['so_conversion'])

                # Résultat (P/B/T)
                if hand['result'] == 'PLAYER':
                    result_sequence.append('P')
                elif hand['result'] == 'BANKER':
                    result_sequence.append('B')
                else:
                    result_sequence.append('T')

            # Compter les conversions S/O valides
            so_count = len([so for so in so_sequence if so in ['S', 'O']])
            total_so_conversions += so_count

            # Déterminer l'état initial de synchronisation
            initial_sync = 'SYNC' if game_data['burn_parity'] == 'PAIR' else 'DESYNC'

            # Marquer la position de la première prédiction AZR possible
            azr_marker = ""
            if game_data['pb_hands'] >= 2:  # Au moins 2 manches P/B = données pour prédire la 3ème
                azr_marker = " ← 1ère prédiction AZR possible ici"

            # Écrire la partie au format tabulaire
            f.write(f"Partie {game_num + 1:>6}:\n")
            f.write(f"  BRÛLAGE (M0)   : {game_data['burn_parity']} → État initial: {initial_sync}\n")
            f.write(f"  PAIR/IMPAIR    : {', '.join(parity_sequence)}\n")
            f.write(f"  SYNC/DESYNC    : {', '.join(sync_sequence)}\n")
            f.write(f"  COMBINÉ        : {', '.join(combined_sequence)}\n")
            f.write(f"  S/O            : {', '.join(so_sequence)}{azr_marker}\n")
            f.write(f"  MANCHES        : {', '.join(result_sequence)}\n")
            f.write(f"  STATS          : {game_data['total_hands']} manches total, {game_data['pb_hands']} P/B, {game_data['tie_hands']} TIE, {so_count} S/O\n")
            f.write("\n")

        # Statistiques finales dans le fichier
        generation_time = time.time() - start_time

        f.write("=" * 80 + "\n")
        f.write("📊 STATISTIQUES GLOBALES\n")
        f.write("=" * 80 + "\n")
        f.write(f"Parties générées: {num_games:,}\n")
        f.write(f"Total manches: {total_hands_generated:,}\n")
        f.write(f"Manches P/B: {total_pb_hands:,}\n")
        f.write(f"Manches TIE: {total_tie_hands:,}\n")
        f.write(f"Conversions S/O: {total_so_conversions:,}\n")
        f.write(f"Taux TIE: {total_tie_hands/total_hands_generated:.1%}\n")
        f.write(f"Taux P/B: {total_pb_hands/total_hands_generated:.1%}\n")
        f.write(f"Moyenne manches/partie: {total_hands_generated/num_games:.1f}\n")
        f.write(f"Durée de génération: {generation_time:.1f} secondes\n")
        f.write(f"Vitesse: {num_games/generation_time:.0f} parties/seconde\n")
        f.write("=" * 80 + "\n")

    # Rapport final console
    print(f"\n✅ GÉNÉRATION TERMINÉE")
    print("=" * 70)
    print(f"📁 Fichier créé: {output_filename}")
    print(f"📊 Parties générées: {num_games:,}")
    print(f"🎲 Total manches: {total_hands_generated:,}")
    print(f"🎯 Manches P/B: {total_pb_hands:,}")
    print(f"🔄 Manches TIE: {total_tie_hands:,}")
    print(f"⚖️  Conversions S/O: {total_so_conversions:,}")
    print(f"📈 Taux TIE: {total_tie_hands/total_hands_generated:.1%}")
    print(f"⏱️  Durée: {generation_time:.1f} secondes")
    print(f"⚡ Vitesse: {num_games/generation_time:.0f} parties/seconde")
    print(f"💽 Taille fichier: {os.path.getsize(output_filename) / (1024*1024):.1f} MB")
    print()
    print(f"🔍 Format parfait pour analyse manuelle et statistique!")

    return output_filename

def generate_all_pure_parity_games(target_parity: str = "ALL") -> str:
    """
    Génère TOUTES les parties 100% PAIR et/ou 100% IMPAIR possibles

    Args:
        target_parity: 'PAIR', 'IMPAIR', ou 'ALL' pour les deux

    Returns:
        Nom du fichier créé
    """
    print(f"🎯 GÉNÉRATION DÉTERMINISTE : TOUTES LES PARTIES 100% {target_parity}")
    print("=" * 80)

    # Charger les combinaisons calculées
    print("📁 Chargement des combinaisons calculées...")
    combinations = load_burn_combinations()

    if not combinations:
        print("❌ Impossible de charger les combinaisons !")
        print("🔧 Exécutez d'abord: python calculateur_combinaisons_brulage.py")
        return None

    # Compter les combinaisons disponibles
    total_pair_combinations = sum(len(data['pair_combinations']) for data in combinations.values())
    total_impair_combinations = sum(len(data['impair_combinations']) for data in combinations.values())

    print(f"📊 Combinaisons disponibles:")
    print(f"• Combinaisons PAIR: {total_pair_combinations:,}")
    print(f"• Combinaisons IMPAIR: {total_impair_combinations:,}")
    print(f"• TOTAL: {total_pair_combinations + total_impair_combinations:,}")
    print()

    if target_parity == "ALL":
        print("Objectif: Générer TOUTES les parties pures possibles")
        target_count_pair = total_pair_combinations
        target_count_impair = total_impair_combinations
    elif target_parity == "PAIR":
        print("Objectif: Générer TOUTES les parties 100% PAIR")
        target_count_pair = total_pair_combinations
        target_count_impair = 0
    else:
        print("Objectif: Générer TOUTES les parties 100% IMPAIR")
        target_count_pair = 0
        target_count_impair = total_impair_combinations

    print("🔧 MÉTHODE: Génération déterministe basée sur les combinaisons calculées")
    print("📋 ÉTAPES:")
    print("1. Charger toutes les combinaisons possibles")
    print("2. Pour chaque combinaison, construire la partie correspondante")
    print("3. Générer les parties de manière systématique")
    print()

    generator = BaccaratGeneratorStandalone()

    # Créer le fichier de sortie
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    if target_parity == "ALL":
        output_filename = f"baccarat_ALL_PURE_GAMES_DETERMINISTIC_{timestamp}.txt"
    else:
        output_filename = f"baccarat_ALL_{target_parity}_GAMES_DETERMINISTIC_{timestamp}.txt"

    all_pure_games = []
    total_generated = 0
    start_time = time.time()

    print("🎲 Début de la génération déterministe...")
    print()

    found_pair = 0
    found_impair = 0

    # Génération déterministe basée sur les combinaisons
    print(f"🎯 OBJECTIF:")
    if target_parity == "ALL":
        print(f"   • Générer {target_count_pair:,} parties 100% PAIR")
        print(f"   • Générer {target_count_impair:,} parties 100% IMPAIR")
        print(f"   • TOTAL: {target_count_pair + target_count_impair:,} parties pures")
    elif target_parity == "PAIR":
        print(f"   • Générer {target_count_pair:,} parties 100% PAIR")
    else:
        print(f"   • Générer {target_count_impair:,} parties 100% IMPAIR")
    print()

    # Générer systématiquement toutes les combinaisons
    for burn_count in range(2, 12):  # Brûlage 2 à 11 cartes
        burn_data = combinations[burn_count]
        burn_parity = burn_data['burn_parity']

        print(f"🔥 Traitement brûlage {burn_count} cartes ({burn_parity})...")

        # Générer les parties PAIR si demandées
        if target_parity in ["ALL", "PAIR"] and burn_data['pair_combinations']:
            print(f"   📊 Génération de {len(burn_data['pair_combinations'])} parties PAIR...")

            for i, combo in enumerate(burn_data['pair_combinations']):
                try:
                    # Construire une partie basée sur cette combinaison
                    game_data = generate_deterministic_game(generator, burn_count, combo, 'PAIR')

                    if game_data:
                        game_data['pure_type'] = 'PAIR'
                        all_pure_games.append(game_data)
                        found_pair += 1
                        total_generated += 1

                        if found_pair % 100 == 0:
                            print(f"   ✅ PAIR {found_pair:,}/{target_count_pair:,} générée")

                except Exception as e:
                    print(f"   ⚠️ Erreur génération PAIR {i+1}: {e}")
                    continue

        # Générer les parties IMPAIR si demandées
        if target_parity in ["ALL", "IMPAIR"] and burn_data['impair_combinations']:
            print(f"   📊 Génération de {len(burn_data['impair_combinations'])} parties IMPAIR...")

            for i, combo in enumerate(burn_data['impair_combinations']):
                try:
                    # Construire une partie basée sur cette combinaison
                    game_data = generate_deterministic_game(generator, burn_count, combo, 'IMPAIR')

                    if game_data:
                        game_data['pure_type'] = 'IMPAIR'
                        all_pure_games.append(game_data)
                        found_impair += 1
                        total_generated += 1

                        if found_impair % 10 == 0:
                            print(f"   ✅ IMPAIR {found_impair:,}/{target_count_impair:,} générée")

                except Exception as e:
                    print(f"   ⚠️ Erreur génération IMPAIR {i+1}: {e}")
                    continue

    print(f"🏁 Génération déterministe terminée !")
    print(f"📊 Parties générées: {total_generated:,}")
    print(f"   • PAIR: {found_pair:,}")
    print(f"   • IMPAIR: {found_impair:,}")

    # Écrire les résultats
    elapsed_time = time.time() - start_time

    with open(output_filename, 'w', encoding='utf-8') as f:
        # En-tête du fichier
        if target_parity == "ALL":
            f.write(f"🎯 BASE DE DONNÉES COMPLÈTE - TOUTES LES PARTIES PURES BACCARAT\n")
            f.write("=" * 80 + "\n")
            f.write(f"Générateur: Baccarat Standalone v1.0 - Mode Génération Complète\n")
            f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Parties 100% PAIR trouvées: {found_pair:,}\n")
            f.write(f"Parties 100% IMPAIR trouvées: {found_impair:,}\n")
            f.write(f"TOTAL parties pures: {len(all_pure_games):,}\n")
        else:
            f.write(f"🎯 BASE DE DONNÉES COMPLÈTE - PARTIES BACCARAT 100% {target_parity}\n")
            f.write("=" * 80 + "\n")
            f.write(f"Générateur: Baccarat Standalone v1.0 - Mode Génération Complète\n")
            f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Parité générée: {target_parity}\n")
            f.write(f"Parties trouvées: {len(all_pure_games):,}\n")

        f.write(f"Parties générées au total: {total_generated:,}\n")
        f.write(f"Taux de découverte: {len(all_pure_games)/total_generated:.4%}\n")
        f.write(f"Durée de génération: {elapsed_time:.1f} secondes\n")
        f.write("=" * 80 + "\n\n")

        f.write("MÉTHODE D'EXPLORATION:\n")
        f.write("-" * 30 + "\n")
        f.write("• Approche: Échantillonnage intensif de l'espace des possibles\n")
        f.write("• Espace exploré: 416 cartes - cartes brûlées - cartes après cut card\n")
        f.write("• Génération: Parties naturelles selon les règles baccarat\n")
        f.write("• Filtrage: Identification des combinaisons 100% de la parité cible\n")
        f.write("• Objectif: Découvrir toutes les séquences possibles\n")
        f.write("\n")
        if target_parity == 'PAIR':
            f.write("PARITÉ PAIR RECHERCHÉE:\n")
            f.write("• Toutes les manches utilisent 4 ou 6 cartes (nombres pairs)\n")
            f.write("• 4 cartes: Aucune 3ème carte tirée (naturels, stands)\n")
            f.write("• 6 cartes: Player ET Banker tirent une 3ème carte\n")
            f.write("• Exclus: Manches à 5 cartes (nombre impair)\n")
        else:
            f.write("PARITÉ IMPAIR RECHERCHÉE:\n")
            f.write("• Toutes les manches utilisent 5 cartes (nombre impair)\n")
            f.write("• 5 cartes: Soit Player OU Banker tire une 3ème carte\n")
            f.write("• Exclus: Manches à 4 ou 6 cartes (nombres pairs)\n")
        f.write("\n" + "=" * 70 + "\n\n")

        # Séparer les parties PAIR et IMPAIR
        pair_games = [game for game in all_pure_games if game.get('pure_type') == 'PAIR']
        impair_games = [game for game in all_pure_games if game.get('pure_type') == 'IMPAIR']

        # SECTION 1: TOUTES LES PARTIES 100% PAIR
        if target_parity in ["ALL", "PAIR"] and pair_games:
            f.write("🎯 SECTION 1: TOUTES LES PARTIES 100% PAIR\n")
            f.write("=" * 80 + "\n")
            f.write(f"Nombre de parties PAIR trouvées: {len(pair_games):,}\n")
            f.write("Caractéristique: Toutes les manches utilisent 4 ou 6 cartes (nombres pairs)\n")
            f.write("=" * 80 + "\n\n")

            for i, game_data in enumerate(pair_games, 1):
                write_game_details(f, game_data, i, "PAIR")

        # SECTION 2: TOUTES LES PARTIES 100% IMPAIR
        if target_parity in ["ALL", "IMPAIR"] and impair_games:
            f.write("\n" + "🎯 SECTION 2: TOUTES LES PARTIES 100% IMPAIR\n")
            f.write("=" * 80 + "\n")
            f.write(f"Nombre de parties IMPAIR trouvées: {len(impair_games):,}\n")
            f.write("Caractéristique: Toutes les manches utilisent 5 cartes (nombre impair)\n")
            f.write("=" * 80 + "\n\n")

            for i, game_data in enumerate(impair_games, 1):
                write_game_details(f, game_data, i, "IMPAIR")

        # Statistiques finales
        discovery_rate = len(all_pure_games) / total_generated if total_generated > 0 else 0

        f.write("=" * 80 + "\n")
        f.write("📊 STATISTIQUES FINALES\n")
        f.write("=" * 80 + "\n")
        if target_parity == "ALL":
            f.write(f"Parties 100% PAIR trouvées: {found_pair:,}\n")
            f.write(f"Parties 100% IMPAIR trouvées: {found_impair:,}\n")
            f.write(f"TOTAL parties pures: {len(all_pure_games):,}\n")
        else:
            f.write(f"Parties 100% {target_parity} trouvées: {len(all_pure_games):,}\n")

        f.write(f"Parties générées au total: {total_generated:,}\n")
        f.write(f"Taux de découverte: {discovery_rate:.4%}\n")
        f.write(f"Durée de génération: {elapsed_time:.1f} secondes\n")
        f.write(f"Vitesse de génération: {total_generated/elapsed_time:.0f} parties/seconde\n")
        f.write(f"Objectif théorique: 20,748 parties pures au total\n")
        if target_parity == "ALL":
            completion = len(all_pure_games) / 20748 * 100
            f.write(f"Complétude: {completion:.1f}% de toutes les parties pures\n")
        f.write("=" * 80 + "\n")

    # Rapport final
    print(f"\n✅ GÉNÉRATION COMPLÈTE TERMINÉE")
    print("=" * 80)
    print(f"📁 Fichier créé: {output_filename}")
    if target_parity == "ALL":
        print(f"🎯 Parties générées: TOUTES LES PARTIES PURES")
        print(f"📊 Parties 100% PAIR: {found_pair:,}")
        print(f"📊 Parties 100% IMPAIR: {found_impair:,}")
        print(f"📊 TOTAL: {len(all_pure_games):,} parties pures")
        completion = len(all_pure_games) / 20748 * 100
        print(f"🎉 Complétude: {completion:.1f}% de l'objectif théorique (20,748)")
    else:
        print(f"🎯 Parité générée: {target_parity}")
        print(f"📊 Parties trouvées: {len(all_pure_games):,}")

    print(f"🎲 Parties générées au total: {total_generated:,}")
    print(f"📈 Taux de découverte: {discovery_rate:.4%}")
    print(f"⏱️  Durée: {elapsed_time:.1f} secondes")
    print(f"⚡ Vitesse: {total_generated/elapsed_time:.0f} parties/seconde")

    return output_filename

def write_game_details(f, game_data, index, section_type):
    """Écrit les détails d'une partie dans le fichier"""
    # Extraire les séquences
    parity_sequence = []
    sync_sequence = []
    combined_sequence = []
    so_sequence = []
    result_sequence = []
    cards_used_sequence = []

    for hand in game_data['hands']:
        parity_sequence.append(hand['parity'])
        sync_sequence.append(hand['sync_state'])
        so_sequence.append(hand['so_conversion'])
        cards_used_sequence.append(str(hand['cards_used']))

        # INDEX COMBINÉ
        parity = hand['parity']
        sync_state = hand['sync_state']

        if parity == 'PAIR' and sync_state == 'SYNC':
            combined_sequence.append('PAIR_SYNC')
        elif parity == 'PAIR' and sync_state == 'DESYNC':
            combined_sequence.append('PAIR_DESYNC')
        elif parity == 'IMPAIR' and sync_state == 'SYNC':
            combined_sequence.append('IMPAIR_SYNC')
        else:
            combined_sequence.append('IMPAIR_DESYNC')

        # Résultat
        if hand['result'] == 'PLAYER':
            result_sequence.append('P')
        elif hand['result'] == 'BANKER':
            result_sequence.append('B')
        else:
            result_sequence.append('T')

    # Écrire la partie
    initial_sync = 'SYNC' if game_data['burn_parity'] == 'PAIR' else 'DESYNC'
    so_count = len([so for so in so_sequence if so in ['S', 'O']])

    f.write(f"Partie {section_type} {index:>4}: ✅ 100% {section_type}\n")
    f.write(f"  BRÛLAGE (M0)   : {game_data['burn_parity']} → État initial: {initial_sync}\n")
    f.write(f"  CARTES UTILISÉES: {', '.join(cards_used_sequence)}\n")
    f.write(f"  PAIR/IMPAIR    : {', '.join(parity_sequence)}\n")
    f.write(f"  SYNC/DESYNC    : {', '.join(sync_sequence)}\n")
    f.write(f"  COMBINÉ        : {', '.join(combined_sequence)}\n")
    f.write(f"  S/O            : {', '.join(so_sequence)}\n")
    f.write(f"  MANCHES        : {', '.join(result_sequence)}\n")
    f.write(f"  STATS          : {game_data['total_hands']} manches total, {game_data['pb_hands']} P/B, {game_data['tie_hands']} TIE, {so_count} S/O\n")
    f.write("\n")

if __name__ == "__main__":
    import os

    print("🎲 GÉNÉRATEUR BACCARAT AUTONOME")
    print("=" * 50)
    print("1. Format TABULAIRE (100 000 parties) - RECOMMANDÉ")
    print("2. Format TABULAIRE personnalisé")
    print("3. Format JSON (compatible analyseur)")
    print("4. TEST - Une seule partie pour vérification")
    print("5. 🎯 GÉNÉRER TOUTES LES PARTIES 100% PAIR")
    print("6. 🎯 GÉNÉRER TOUTES LES PARTIES 100% IMPAIR")
    print("7. 🎯 GÉNÉRER TOUTES LES PARTIES PURES (PAIR + IMPAIR)")

    choice = input("Votre choix (1-7): ").strip()

    if choice == "1":
        filename = generate_massive_data_tabular(100000, 60)
    elif choice == "2":
        num_games = int(input("Nombre de parties: ") or "100000")
        hands_per_game = int(input("Manches P/B par partie (60): ") or "60")
        filename = generate_massive_data_tabular(num_games, hands_per_game)
    elif choice == "3":
        # Garder l'ancienne fonction pour compatibilité JSON
        print("Mode JSON pour compatibilité avec l'analyseur...")
        num_games = int(input("Nombre de parties: ") or "100000")
        hands_per_game = int(input("Manches P/B par partie (60): ") or "60")
        filename = generate_massive_data_json(num_games, hands_per_game)
    elif choice == "5":
        # Génération complète PAIR
        print("🎯 MODE GÉNÉRATION COMPLÈTE - TOUTES LES PARTIES 100% PAIR")
        print("=" * 70)
        print("Objectif: Générer TOUTES les 20,141 parties 100% PAIR possibles")
        print("• Toutes manches utilisent 4 ou 6 cartes (nombres pairs)")
        print("• Base de données complète des parties PAIR")
        print("• Génération exhaustive systématique")
        print("⚠️  ATTENTION: Processus potentiellement très long")
        confirm = input("Continuer ? (o/N): ").strip().lower()
        if confirm == 'o':
            filename = generate_all_pure_parity_games("PAIR")
        else:
            print("Opération annulée.")
            filename = None
    elif choice == "6":
        # Génération complète IMPAIR
        print("🎯 MODE GÉNÉRATION COMPLÈTE - TOUTES LES PARTIES 100% IMPAIR")
        print("=" * 70)
        print("Objectif: Générer TOUTES les 607 parties 100% IMPAIR possibles")
        print("• Toutes manches utilisent 5 cartes (nombre impair)")
        print("• Base de données complète des parties IMPAIR")
        print("• Génération exhaustive systématique")
        print("⚠️  ATTENTION: Processus potentiellement long")
        confirm = input("Continuer ? (o/N): ").strip().lower()
        if confirm == 'o':
            filename = generate_all_pure_parity_games("IMPAIR")
        else:
            print("Opération annulée.")
            filename = None
    elif choice == "7":
        # Génération complète TOUTES
        print("🎯 MODE GÉNÉRATION COMPLÈTE - TOUTES LES PARTIES PURES")
        print("=" * 70)
        print("Objectif: Générer TOUTES les 20,748 parties pures possibles")
        print("• 20,141 parties 100% PAIR (4 ou 6 cartes)")
        print("• 607 parties 100% IMPAIR (5 cartes)")
        print("• Base de données complète et exhaustive")
        print("• Génération systématique de toutes les combinaisons")
        print("⚠️  ATTENTION: Processus très long (plusieurs heures)")
        confirm = input("Continuer ? (o/N): ").strip().lower()
        if confirm == 'o':
            filename = generate_all_pure_parity_games("ALL")
        else:
            print("Opération annulée.")
            filename = None
    else:
        # TEST avec une seule partie
        print("🧪 MODE TEST - Génération d'une seule partie")
        filename = generate_massive_data_tabular(1, 10, "test_single_game.txt")

        # Afficher le contenu généré
        print("\n📄 CONTENU GÉNÉRÉ :")
        print("=" * 60)
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)

        # Tester l'analyseur
        print("\n🔍 TEST DE L'ANALYSEUR :")
        print("=" * 60)
        try:
            from baccarat_tabular_analyzer import BaccaratTabularAnalyzer
            analyzer = BaccaratTabularAnalyzer(filename)

            print(f"✅ Analyseur chargé avec succès")
            print(f"📊 Parties détectées: {len(analyzer.games_data)}")

            if analyzer.games_data:
                game = analyzer.games_data[0]
                print(f"🎲 Partie 1 parsée:")
                print(f"  - Brûlage: {game.get('burn_parity', 'N/A')}")
                print(f"  - PAIR/IMPAIR: {game.get('parity_sequence', [])}")
                print(f"  - SYNC/DESYNC: {game.get('sync_sequence', [])}")
                print(f"  - COMBINÉ: {game.get('combined_sequence', [])}")
                print(f"  - S/O: {game.get('so_sequence', [])}")
                print(f"  - MANCHES: {game.get('result_sequence', [])}")

                # Test de cohérence
                consistency = analyzer.verify_pb_so_consistency()
                print(f"\n🔍 Test de cohérence P/B ↔ S/O:")
                print(f"  - Cohérence: {consistency['consistency_rate']:.1%}")
                if consistency['inconsistent_games'] > 0:
                    print(f"  - ⚠️ Erreurs détectées: {consistency['inconsistent_games']}")
                else:
                    print(f"  - ✅ Parfaitement cohérent")

                # Test rapport rapide
                print(f"\n📋 Génération du rapport rapide...")
                report = analyzer.generate_quick_report()
                print("✅ Rapport généré avec succès")

        except Exception as e:
            print(f"❌ Erreur lors du test de l'analyseur: {e}")
            import traceback
            traceback.print_exc()

    if filename:
        print(f"\n🎉 Fichier prêt: {filename}")
    else:
        print(f"\n⚠️  Aucun fichier généré.")

def generate_massive_data_json(num_games: int = 100000, hands_per_game: int = 60, output_filename: str = None) -> str:
    """Version JSON pour compatibilité avec l'analyseur existant"""

    print(f"🎲 GÉNÉRATION DE {num_games:,} PARTIES BACCARAT - FORMAT JSON")
    print("=" * 60)

    generator = BaccaratGeneratorStandalone()
    all_games = []

    # Statistiques globales
    total_hands_generated = 0
    total_pb_hands = 0
    total_tie_hands = 0

    start_time = time.time()

    for game_num in range(num_games):
        # Affichage du progrès
        if (game_num + 1) % 1000 == 0:
            progress = (game_num + 1) / num_games * 100
            elapsed = time.time() - start_time
            speed = (game_num + 1) / elapsed
            print(f"📊 Progression: {game_num + 1:,}/{num_games:,} ({progress:.1f}%) - {speed:.0f} parties/sec")

            # Libération mémoire périodique
            if (game_num + 1) % 10000 == 0:
                gc.collect()

        # Générer une partie
        game_data = generator.generate_game_data(hands_per_game)

        # Mise à jour des statistiques
        total_hands_generated += game_data['total_hands']
        total_pb_hands += game_data['pb_hands']
        total_tie_hands += game_data['tie_hands']

        # Format identique au programme principal
        formatted_game = {
            'game_number': game_num + 1,
            'initialization': {
                'burn_cards_count': game_data['burn_cards_count'],
                'burn_parity': game_data['burn_parity'],
                'initial_sync_state': 'SYNC' if game_data['burn_parity'] == 'PAIR' else 'DESYNC'
            },
            'statistics': {
                'total_hands': game_data['total_hands'],
                'pb_hands': game_data['pb_hands'],
                'tie_hands': game_data['tie_hands'],
                'so_conversions': len([h for h in game_data['hands'] if h.get('so_conversion', '--') != '--'])
            },
            'hands': game_data['hands']
        }

        all_games.append(formatted_game)

    generation_time = time.time() - start_time

    # Créer le fichier de sortie
    if not output_filename:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"baccarat_standalone_{num_games}games_{timestamp}.json"

    # Métadonnées identiques au programme principal
    final_data = {
        'metadata': {
            'purpose': 'ANALYSE_STATISTIQUE_STANDALONE',
            'total_games': len(all_games),
            'generation_date': datetime.now().isoformat(),
            'hands_per_game_target': hands_per_game,
            'generator_version': 'STANDALONE_v1.0',
            'global_statistics': {
                'total_hands_generated': total_hands_generated,
                'total_pb_hands': total_pb_hands,
                'total_tie_hands': total_tie_hands,
                'average_hands_per_game': total_hands_generated / len(all_games),
                'tie_rate': total_tie_hands / total_hands_generated,
                'pb_rate': total_pb_hands / total_hands_generated
            },
            'analysis_ready': True,
            'recommended_analyzer': 'baccarat_data_analyzer.py'
        },
        'games': all_games
    }

    # Sauvegarder
    print(f"💾 Sauvegarde en cours...")
    with open(output_filename, 'w', encoding='utf-8') as f:
        json.dump(final_data, f, indent=2, ensure_ascii=False)

    # Rapport final
    print(f"\n✅ GÉNÉRATION TERMINÉE")
    print("=" * 60)
    print(f"📁 Fichier créé: {output_filename}")
    print(f"📊 Parties générées: {num_games:,}")
    print(f"🎲 Total manches: {total_hands_generated:,}")
    print(f"🎯 Manches P/B: {total_pb_hands:,}")
    print(f"🔄 Manches TIE: {total_tie_hands:,}")
    print(f"📈 Taux TIE: {total_tie_hands/total_hands_generated:.1%}")
    print(f"⏱️  Durée: {generation_time:.1f} secondes")
    print(f"⚡ Vitesse: {num_games/generation_time:.0f} parties/seconde")
    print(f"💽 Taille fichier: {os.path.getsize(output_filename) / (1024*1024):.1f} MB")
    print()
    print(f"🔍 Analysez avec: python baccarat_data_analyzer.py {output_filename}")

    return output_filename
