# 🏗️ MODULE 2.1 : ARCHITECTURE GÉNÉRALE DU SYSTÈME AZR

## 🎯 **OBJECTIFS D'APPRENTISSAGE**

À la fin de cette section, vous maîtriserez :
- ✅ L'architecture complète du système AZR
- ✅ Les interactions entre les composants
- ✅ Le flux de données et de contrôle
- ✅ Les principes de conception modulaire

---

## 🏛️ **VUE D'ENSEMBLE DE L'ARCHITECTURE**

### **🎭 Architecture Duale Unifiée**

```
                    🧠 MODÈLE AZR UNIFIÉ
                           |
                    ┌──────┴──────┐
                    |             |
              🎭 PROPOSEUR    🔧 RÉSOLVEUR
                    |             |
                    ↓             ↓
              Génère Tâches   Résout Tâches
                    |             |
                    ↓             ↓
              📊 ÉVALUATION ←→ 📈 APPRENTISSAGE
                    |             |
                    ↓             ↓
              🔄 MISE À JOUR DES PARAMÈTRES
```

### **🔗 Composants Principaux**

1. **🧠 Modèle Unifié** : Réseau neuronal partagé
2. **🎭 Tête Proposeur** : Génération de tâches
3. **🔧 Tête Résolveur** : Résolution de problèmes
4. **📊 Système d'Évaluation** : Mesure de performance
5. **🔄 Mécanisme d'Apprentissage** : Mise à jour des poids
6. **💾 Mémoire Épisodique** : Stockage des expériences
7. **🎯 Contrôleur de Difficulté** : Adaptation dynamique

---

## 🧠 **MODÈLE UNIFIÉ - CŒUR DU SYSTÈME**

### **🏗️ Architecture Neuronale**

```python
class AZRUnifiedModel(nn.Module):
    def __init__(self, config):
        super().__init__()
        
        # 🧠 Encodeur partagé
        self.shared_encoder = TransformerEncoder(
            d_model=config.hidden_size,
            num_layers=config.num_layers,
            num_heads=config.num_attention_heads
        )
        
        # 🎭 Tête Proposeur
        self.proposer_head = ProposerHead(
            hidden_size=config.hidden_size,
            vocab_size=config.vocab_size
        )
        
        # 🔧 Tête Résolveur
        self.solver_head = SolverHead(
            hidden_size=config.hidden_size,
            output_size=config.output_size
        )
        
        # 📊 Évaluateur interne
        self.evaluator = InternalEvaluator(
            hidden_size=config.hidden_size
        )
```

### **🔄 Flux de Traitement**

```python
def forward(self, mode, input_data):
    # 1. Encodage partagé
    shared_repr = self.shared_encoder(input_data)
    
    if mode == "propose":
        # 2a. Génération de tâche
        task = self.proposer_head(shared_repr)
        difficulty = self.evaluator.estimate_difficulty(task)
        return task, difficulty
        
    elif mode == "solve":
        # 2b. Résolution de tâche
        solution = self.solver_head(shared_repr)
        confidence = self.evaluator.estimate_confidence(solution)
        return solution, confidence
```

---

## 🎭 **COMPOSANT PROPOSEUR**

### **🎯 Mission et Responsabilités**

**Objectif Principal :** Générer des tâches optimales pour l'apprentissage

**Responsabilités :**
- 🎨 **Créativité** : Inventer de nouveaux types de problèmes
- ⚖️ **Calibrage** : Ajuster la difficulté appropriée
- 🎯 **Ciblage** : Identifier les lacunes d'apprentissage
- 📈 **Progression** : Maintenir une courbe d'apprentissage optimale

### **🏗️ Architecture Détaillée**

```python
class ProposerHead(nn.Module):
    def __init__(self, hidden_size, vocab_size):
        super().__init__()
        
        # 🎨 Générateur de contenu
        self.content_generator = nn.Sequential(
            nn.Linear(hidden_size, hidden_size * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size * 2, vocab_size)
        )
        
        # ⚖️ Estimateur de difficulté
        self.difficulty_estimator = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()  # Difficulté entre 0 et 1
        )
        
        # 🎯 Classificateur de type
        self.type_classifier = nn.Sequential(
            nn.Linear(hidden_size, len(TASK_TYPES)),
            nn.Softmax(dim=-1)
        )
```

### **🔄 Processus de Génération**

```python
def generate_task(self, context, target_difficulty=None):
    # 1. Analyser le contexte actuel
    context_embedding = self.encode_context(context)
    
    # 2. Déterminer le type de tâche optimal
    task_type_probs = self.type_classifier(context_embedding)
    task_type = sample_from_distribution(task_type_probs)
    
    # 3. Générer le contenu de la tâche
    task_content = self.content_generator(context_embedding)
    
    # 4. Estimer et ajuster la difficulté
    estimated_difficulty = self.difficulty_estimator(context_embedding)
    
    if target_difficulty:
        task_content = self.adjust_difficulty(
            task_content, 
            estimated_difficulty, 
            target_difficulty
        )
    
    return Task(
        content=task_content,
        type=task_type,
        difficulty=estimated_difficulty
    )
```

---

## 🔧 **COMPOSANT RÉSOLVEUR**

### **🎯 Mission et Responsabilités**

**Objectif Principal :** Résoudre efficacement les tâches proposées

**Responsabilités :**
- 🧠 **Raisonnement** : Analyser et comprendre les problèmes
- 🔍 **Recherche** : Explorer l'espace des solutions
- 🎯 **Précision** : Générer des réponses correctes
- 📊 **Auto-évaluation** : Estimer la qualité des solutions

### **🏗️ Architecture Détaillée**

```python
class SolverHead(nn.Module):
    def __init__(self, hidden_size, output_size):
        super().__init__()
        
        # 🧠 Analyseur de problème
        self.problem_analyzer = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=0.1
        )
        
        # 🔍 Générateur de solution
        self.solution_generator = nn.Sequential(
            nn.Linear(hidden_size, hidden_size * 2),
            nn.GELU(),
            nn.LayerNorm(hidden_size * 2),
            nn.Linear(hidden_size * 2, output_size)
        )
        
        # 📊 Estimateur de confiance
        self.confidence_estimator = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
```

### **🔄 Processus de Résolution**

```python
def solve_task(self, task):
    # 1. Analyser la tâche
    task_embedding = self.encode_task(task)
    
    # 2. Attention sur les éléments clés
    analyzed_task, attention_weights = self.problem_analyzer(
        task_embedding, task_embedding, task_embedding
    )
    
    # 3. Générer la solution
    solution = self.solution_generator(analyzed_task)
    
    # 4. Estimer la confiance
    confidence = self.confidence_estimator(analyzed_task)
    
    # 5. Post-traitement si nécessaire
    solution = self.post_process_solution(solution, task.type)
    
    return Solution(
        content=solution,
        confidence=confidence,
        reasoning=attention_weights
    )
```

---

## 📊 **SYSTÈME D'ÉVALUATION**

### **🎯 Évaluation Duale**

Le système évalue simultanément :
1. **🎭 Qualité des Tâches** : Pertinence, difficulté, diversité
2. **🔧 Qualité des Solutions** : Justesse, efficacité, créativité

### **📈 Métriques de Performance**

```python
class PerformanceMetrics:
    def __init__(self):
        self.proposer_metrics = {
            'task_diversity': DiversityMetric(),
            'difficulty_calibration': CalibrationMetric(),
            'learning_efficiency': EfficiencyMetric()
        }
        
        self.solver_metrics = {
            'accuracy': AccuracyMetric(),
            'confidence_calibration': ConfidenceMetric(),
            'reasoning_quality': ReasoningMetric()
        }
    
    def evaluate_episode(self, task, solution, outcome):
        # Évaluer la tâche
        task_quality = self.evaluate_task_quality(task, outcome)
        
        # Évaluer la solution
        solution_quality = self.evaluate_solution_quality(
            solution, task, outcome
        )
        
        return {
            'task_quality': task_quality,
            'solution_quality': solution_quality,
            'overall_performance': (task_quality + solution_quality) / 2
        }
```

---

## 🔄 **MÉCANISME D'APPRENTISSAGE**

### **🎯 Optimisation Multi-Objectif**

```python
def compute_loss(self, episode_batch):
    total_loss = 0
    
    for episode in episode_batch:
        # 🎭 Perte du Proposeur
        proposer_loss = self.compute_proposer_loss(
            episode.task, 
            episode.solver_performance
        )
        
        # 🔧 Perte du Résolveur
        solver_loss = self.compute_solver_loss(
            episode.solution, 
            episode.ground_truth
        )
        
        # ⚖️ Combinaison pondérée
        episode_loss = (
            self.proposer_weight * proposer_loss +
            self.solver_weight * solver_loss
        )
        
        total_loss += episode_loss
    
    return total_loss / len(episode_batch)
```

### **📈 Mise à Jour Adaptative**

```python
def update_parameters(self, loss):
    # 1. Calcul des gradients
    loss.backward()
    
    # 2. Clipping des gradients
    torch.nn.utils.clip_grad_norm_(
        self.parameters(), 
        max_norm=1.0
    )
    
    # 3. Mise à jour avec taux adaptatif
    self.optimizer.step()
    
    # 4. Ajustement des poids de combinaison
    self.adjust_component_weights()
    
    # 5. Reset des gradients
    self.optimizer.zero_grad()
```

---

## 💾 **MÉMOIRE ÉPISODIQUE**

### **🗃️ Structure de Stockage**

```python
class EpisodicMemory:
    def __init__(self, capacity=10000):
        self.capacity = capacity
        self.episodes = deque(maxlen=capacity)
        self.index = {}  # Index pour recherche rapide
    
    def store_episode(self, episode):
        # Stocker l'épisode
        self.episodes.append(episode)
        
        # Indexer par caractéristiques
        self.index_episode(episode)
        
        # Maintenir la diversité
        self.maintain_diversity()
    
    def retrieve_similar(self, query, k=5):
        # Recherche par similarité
        similarities = self.compute_similarities(query)
        top_k_indices = similarities.argsort()[-k:]
        
        return [self.episodes[i] for i in top_k_indices]
```

---

## 🎯 **CONTRÔLEUR DE DIFFICULTÉ**

### **📊 Adaptation Dynamique**

```python
class DifficultyController:
    def __init__(self):
        self.target_success_rate = 0.7  # Zone d'apprentissage optimal
        self.adaptation_rate = 0.1
        self.current_difficulty = 0.5
    
    def update_difficulty(self, recent_performance):
        success_rate = np.mean(recent_performance)
        
        if success_rate > self.target_success_rate + 0.1:
            # Trop facile, augmenter la difficulté
            self.current_difficulty += self.adaptation_rate
        elif success_rate < self.target_success_rate - 0.1:
            # Trop difficile, diminuer la difficulté
            self.current_difficulty -= self.adaptation_rate
        
        # Maintenir dans les bornes
        self.current_difficulty = np.clip(
            self.current_difficulty, 0.1, 0.9
        )
        
        return self.current_difficulty
```

---

## 📝 **POINTS CLÉS À RETENIR**

1. **🏗️ Architecture Unifiée** : Un modèle, deux têtes spécialisées
2. **🔄 Boucle Fermée** : Évaluation continue et adaptation
3. **📊 Métriques Duales** : Performance des tâches ET des solutions
4. **💾 Mémoire Intelligente** : Stockage et récupération optimisés
5. **🎯 Adaptation Dynamique** : Ajustement automatique de la difficulté

---

## 🎯 **EXERCICE PRATIQUE**

**Implémentation :** Créez une version simplifiée du modèle unifié avec :
- Un encodeur partagé (2 couches)
- Une tête proposeur (génération de séquences simples)
- Une tête résolveur (classification binaire)

**Code de départ :**
```python
import torch
import torch.nn as nn

class SimpleAZR(nn.Module):
    def __init__(self, input_size, hidden_size):
        super().__init__()
        # TODO: Implémenter l'architecture
        pass
    
    def forward(self, x, mode):
        # TODO: Implémenter le forward pass
        pass
```

---

**➡️ Prochaine section : [2.2 - Rôle Proposeur Détaillé](02_Role_Proposeur.md)**
