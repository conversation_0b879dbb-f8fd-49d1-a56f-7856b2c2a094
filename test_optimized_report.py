#!/usr/bin/env python3
"""
Test de validation du rapport optimisé Rollout 1 → Rollout 2

Vérifie que les nouvelles sections sont correctement générées :
1. signals_summary : Signaux exploitables classés
2. generation_guidance : Directives pour génération
3. quick_access : Accès rapide aux données critiques
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import AZRBaccaratPredictor, BaccaratHand

def create_rich_test_sequence():
    """
    Crée une séquence riche pour tester les nouvelles sections
    
    Séquence avec patterns forts pour valider la détection de signaux
    """
    hands = []
    
    # Séquence avec pattern PAIR_SYNC → O fort
    sequences = [
        # Manches 1-4: Pattern PAIR_SYNC → O
        ('P', 'IMPAIR', 'SYNC'),    # Manche 1
        ('B', 'PAIR', 'SYNC'),      # Manche 2 - PAIR_SYNC
        ('P', 'IMPAIR', 'DESYNC'),  # Manche 3 - Opposite de B
        ('P', 'PAIR', 'DESYNC'),    # Manche 4 - PAIR_DESYNC
        
        # Manches 5-8: Ré<PERSON><PERSON>tition pattern
        ('B', 'IMPAIR', 'DESYNC'),  # Manche 5
        ('P', 'PAIR', 'SYNC'),      # Manche 6 - PAIR_SYNC
        ('B', 'IMPAIR', 'DESYNC'),  # Manche 7 - Opposite de P
        ('B', 'PAIR', 'DESYNC'),    # Manche 8
        
        # Manches 9-12: Confirmation pattern
        ('P', 'IMPAIR', 'DESYNC'),  # Manche 9
        ('B', 'PAIR', 'SYNC'),      # Manche 10 - PAIR_SYNC
        ('P', 'IMPAIR', 'DESYNC'),  # Manche 11 - Opposite de B
        ('P', 'PAIR', 'DESYNC'),    # Manche 12
        
        # Manches 13-16: Pattern IMPAIR → Player
        ('P', 'IMPAIR', 'SYNC'),    # Manche 13 - IMPAIR
        ('B', 'PAIR', 'SYNC'),      # Manche 14
        ('P', 'IMPAIR', 'DESYNC'),  # Manche 15 - IMPAIR
        ('B', 'PAIR', 'DESYNC'),    # Manche 16
        
        # Manches 17-20: Confirmation patterns
        ('P', 'IMPAIR', 'DESYNC'),  # Manche 17 - IMPAIR
        ('P', 'PAIR', 'DESYNC'),    # Manche 18 - PAIR_DESYNC
        ('P', 'IMPAIR', 'SYNC'),    # Manche 19 - IMPAIR
        ('B', 'PAIR', 'SYNC'),      # Manche 20 - PAIR_SYNC
    ]
    
    for i, (result, parity, sync_state) in enumerate(sequences):
        hands.append(BaccaratHand(
            pb_hand_number=i + 1,
            result=result,
            parity=parity,
            sync_state=sync_state,
            so_conversion='--'  # Sera calculé automatiquement
        ))
    
    return hands

def test_optimized_report_sections():
    """Test principal des nouvelles sections du rapport"""
    
    print("🧪 TEST RAPPORT OPTIMISÉ ROLLOUT 1 → ROLLOUT 2")
    print("=" * 55)
    
    # Créer séquence test riche
    hands = create_rich_test_sequence()
    
    # Créer prédicteur
    predictor = AZRBaccaratPredictor()
    
    # Préparer données standardisées
    standardized_sequence = {
        'hands_history': hands,
        'total_hands': len(hands),
        'last_hand': hands[-1] if hands else None
    }
    
    # Exécuter Rollout 1 Analyseur
    print("\n🔍 Exécution Rollout 1 Analyseur...")
    cluster = predictor.azr_master.clusters[0]
    analyzer_report = cluster._rollout_analyzer(standardized_sequence)
    
    if 'error' in analyzer_report:
        print(f"❌ Erreur: {analyzer_report['error']}")
        return False
    
    # ================================================================
    # TEST 1 : SIGNALS_SUMMARY
    # ================================================================
    print("\n📊 TEST 1 : SIGNALS_SUMMARY")
    print("-" * 35)
    
    signals_summary = analyzer_report.get('signals_summary', {})
    
    print(f"✅ Section présente: {'signals_summary' in analyzer_report}")
    print(f"📊 Nombre de signaux détectés: {len(signals_summary.get('top_signals', []))}")
    print(f"📊 Stratégie recommandée: {signals_summary.get('recommended_strategy', 'N/A')}")
    print(f"📊 Confiance globale: {signals_summary.get('overall_confidence', 0):.3f}")
    print(f"📊 Exploitation prête: {signals_summary.get('exploitation_ready', False)}")
    
    # Afficher top 3 signaux
    top_signals = signals_summary.get('top_signals', [])
    for i, signal in enumerate(top_signals[:3]):
        print(f"   🎯 Signal {i+1}: {signal.get('signal_name', 'N/A')}")
        print(f"      Force: {signal.get('strength', 0):.3f}")
        print(f"      Confiance: {signal.get('confidence', 0):.3f}")
        print(f"      Stratégie: {signal.get('strategy', 'N/A')}")
    
    # ================================================================
    # TEST 2 : GENERATION_GUIDANCE
    # ================================================================
    print("\n🎯 TEST 2 : GENERATION_GUIDANCE")
    print("-" * 35)
    
    generation_guidance = analyzer_report.get('generation_guidance', {})
    
    print(f"✅ Section présente: {'generation_guidance' in analyzer_report}")
    print(f"📊 Focus principal: {generation_guidance.get('primary_focus', 'N/A')}")
    print(f"📊 Focus secondaire: {generation_guidance.get('secondary_focus', 'N/A')}")
    print(f"📊 Stratégie exploitation: {generation_guidance.get('exploitation_strategy', 'N/A')}")
    print(f"📊 Niveau de risque: {generation_guidance.get('risk_level', 'N/A')}")
    print(f"📊 Longueur séquence optimale: {generation_guidance.get('optimal_sequence_length', 'N/A')}")
    
    # Patterns à éviter
    avoid_patterns = generation_guidance.get('avoid_patterns', [])
    print(f"📊 Patterns à éviter: {len(avoid_patterns)} patterns")
    for pattern in avoid_patterns[:3]:
        print(f"   ⚠️  {pattern}")
    
    # Seuils de confiance
    thresholds = generation_guidance.get('confidence_thresholds', {})
    print(f"📊 Seuils confiance: Haut={thresholds.get('high', 'N/A')}, "
          f"Moyen={thresholds.get('medium', 'N/A')}, Bas={thresholds.get('low', 'N/A')}")
    
    # ================================================================
    # TEST 3 : QUICK_ACCESS
    # ================================================================
    print("\n⚡ TEST 3 : QUICK_ACCESS")
    print("-" * 30)
    
    quick_access = analyzer_report.get('quick_access', {})
    
    print(f"✅ Section présente: {'quick_access' in analyzer_report}")
    print(f"📊 État actuel: {quick_access.get('current_state', 'N/A')}")
    print(f"📊 Prédiction P/B: {quick_access.get('next_prediction_pb', 'N/A')}")
    print(f"📊 Prédiction S/O: {quick_access.get('next_prediction_so', 'N/A')}")
    print(f"📊 Confiance prédiction: {quick_access.get('prediction_confidence', 0):.3f}")
    print(f"📊 Niveau d'alerte: {quick_access.get('alert_level', 'N/A')}")
    print(f"📊 Exploitation prête: {quick_access.get('exploitation_ready', False)}")
    
    # Analyse dernière manche
    last_hand = quick_access.get('last_hand_analysis', {})
    if last_hand:
        print(f"📊 Dernière manche: #{last_hand.get('hand_number', 'N/A')} - "
              f"{last_hand.get('result', 'N/A')} - {last_hand.get('combined_state', 'N/A')}")
    
    # Signaux immédiats
    immediate_signals = quick_access.get('immediate_signals', {})
    print(f"📊 Signaux immédiats: {len(immediate_signals)} signaux")
    for key, signal in immediate_signals.items():
        print(f"   🚀 {signal.get('name', 'N/A')}: {signal.get('strength', 0):.3f}")
    
    # ================================================================
    # TEST 4 : COMPATIBILITÉ AVEC STRUCTURE EXISTANTE
    # ================================================================
    print("\n🔧 TEST 4 : COMPATIBILITÉ STRUCTURE EXISTANTE")
    print("-" * 50)
    
    # Vérifier que les sections existantes sont toujours présentes
    existing_sections = ['indices_analysis', 'synthesis', 'sequence_metadata']
    for section in existing_sections:
        present = section in analyzer_report
        print(f"✅ {section}: {'Présent' if present else '❌ MANQUANT'}")
    
    # Vérifier structure indices_analysis
    indices_analysis = analyzer_report.get('indices_analysis', {})
    expected_indices = ['impair_pair', 'desync_sync', 'combined', 'pbt', 'so']
    for index in expected_indices:
        present = index in indices_analysis
        print(f"   📊 {index}: {'Présent' if present else '❌ MANQUANT'}")
    
    # ================================================================
    # RÉSULTAT FINAL
    # ================================================================
    print("\n🎯 RÉSULTAT FINAL")
    print("-" * 20)
    
    # Vérifications critiques
    checks = [
        ('signals_summary' in analyzer_report, "Section signals_summary présente"),
        ('generation_guidance' in analyzer_report, "Section generation_guidance présente"),
        ('quick_access' in analyzer_report, "Section quick_access présente"),
        (len(signals_summary.get('top_signals', [])) > 0, "Signaux détectés"),
        (signals_summary.get('recommended_strategy') != 'conservative', "Stratégie optimisée détectée"),
        (quick_access.get('current_state') != 'unknown', "État actuel identifié"),
        ('indices_analysis' in analyzer_report, "Compatibilité structure existante")
    ]
    
    passed_checks = sum(1 for check, _ in checks if check)
    total_checks = len(checks)
    
    for check, description in checks:
        status = "✅" if check else "❌"
        print(f"{status} {description}")
    
    success_rate = passed_checks / total_checks
    print(f"\n📊 Taux de réussite: {passed_checks}/{total_checks} ({success_rate:.1%})")
    
    if success_rate >= 0.85:
        print("🎉 RAPPORT OPTIMISÉ VALIDÉ - Prêt pour production !")
        return True
    else:
        print("⚠️  AMÉLIORATIONS NÉCESSAIRES")
        return False

if __name__ == "__main__":
    try:
        success = test_optimized_report_sections()
        if success:
            print("\n🎉 TEST RÉUSSI - Rapport optimisé fonctionnel !")
        else:
            print("\n❌ TEST ÉCHOUÉ")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 ERREUR TEST: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
