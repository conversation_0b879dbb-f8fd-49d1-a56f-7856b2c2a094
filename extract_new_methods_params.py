#!/usr/bin/env python3
"""
Extraction des paramètres des nouvelles méthodes optimisées du Rollout 2

Ce script identifie toutes les valeurs codées en dur dans les nouvelles méthodes
optimisées pour les centraliser dans AZRConfig.
"""

import re
from typing import Dict, List, Tu<PERSON>

def extract_hardcoded_values_in_new_methods(file_content: str) -> Dict[str, List[Dict]]:
    """Extrait les valeurs codées en dur dans les nouvelles méthodes optimisées"""
    
    # Nouvelles méthodes optimisées à analyser
    new_methods = [
        '_define_optimized_generation_space',
        '_generate_sequences_from_signals',
        '_generate_sequence_from_signal',
        '_generate_fallback_sequences',
        '_classify_confidence_level'
    ]
    
    hardcoded_values = {}
    
    for method_name in new_methods:
        hardcoded_values[method_name] = []
        
        # Trouver la méthode dans le fichier
        method_pattern = rf'def {re.escape(method_name)}\(.*?\):(.*?)(?=def |\Z)'
        match = re.search(method_pattern, file_content, re.DOTALL)
        
        if match:
            method_content = match.group(1)
            lines = method_content.split('\n')
            
            # Patterns de valeurs codées en dur
            patterns = [
                (r'\b0\.[0-9]+\b', 'decimal'),
                (r'\b[1-9][0-9]*\b', 'integer'),
                (r'> 0\.[0-9]+', 'threshold'),
                (r'< 0\.[0-9]+', 'threshold'),
                (r'>= 0\.[0-9]+', 'threshold'),
                (r'<= 0\.[0-9]+', 'threshold'),
                (r'== 0\.[0-9]+', 'comparison'),
                (r'!= 0\.[0-9]+', 'comparison')
            ]
            
            for line_num, line in enumerate(lines, 1):
                # Ignorer les commentaires et docstrings
                if line.strip().startswith('#') or '"""' in line or "'''" in line:
                    continue
                
                # Ignorer les lignes avec self.config
                if 'self.config.' in line:
                    continue
                
                # Chercher les patterns
                for pattern, value_type in patterns:
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        # Exclure certains cas légitimes
                        if any(exclude in line for exclude in [
                            'range(len(',
                            'enumerate(',
                            'time.time()',
                            'logger.',
                            'print(',
                            'format(',
                            'f"',
                            "f'",
                            'len(',
                            'max(',
                            'min(',
                            'abs(',
                            'sum(',
                            'int(',
                            'float(',
                            'str(',
                            'bool('
                        ]):
                            continue
                        
                        hardcoded_values[method_name].append({
                            'line_number': line_num,
                            'line_content': line.strip(),
                            'value': match.group(),
                            'type': value_type,
                            'pattern': pattern,
                            'context': line.strip()
                        })
    
    return hardcoded_values

def categorize_parameters(hardcoded_values: Dict) -> Dict[str, List[Dict]]:
    """Catégorise les paramètres par type d'usage"""
    
    categories = {
        'confidence_thresholds': [],    # Seuils de confiance (0.8, 0.6, 0.4)
        'default_values': [],          # Valeurs par défaut (0.5)
        'probability_limits': [],      # Limites de probabilité (0.95, 0.9)
        'weight_factors': [],          # Facteurs de pondération
        'sequence_parameters': [],     # Paramètres de séquence
        'classification_bounds': [],   # Bornes de classification
        'other_constants': []          # Autres constantes
    }
    
    for method_name, values in hardcoded_values.items():
        for value_info in values:
            value = value_info['value']
            context = value_info['context'].lower()
            
            # Catégoriser selon le contexte
            if 'confidence' in context and ('>' in context or '<' in context):
                categories['confidence_thresholds'].append({
                    'method': method_name,
                    'value': value,
                    'context': context,
                    'suggested_param': f'rollout2_confidence_threshold_{value.replace(".", "_")}'
                })
            elif 'get(' in context and value == '0.5':
                categories['default_values'].append({
                    'method': method_name,
                    'value': value,
                    'context': context,
                    'suggested_param': 'rollout2_default_confidence'
                })
            elif ('0.9' in value or '0.95' in value) and ('min(' in context or 'max(' in context):
                categories['probability_limits'].append({
                    'method': method_name,
                    'value': value,
                    'context': context,
                    'suggested_param': f'rollout2_max_confidence_{value.replace(".", "_")}'
                })
            elif 'weight' in context or 'factor' in context:
                categories['weight_factors'].append({
                    'method': method_name,
                    'value': value,
                    'context': context,
                    'suggested_param': f'rollout2_weight_{value.replace(".", "_")}'
                })
            elif 'length' in context or 'sequence' in context:
                categories['sequence_parameters'].append({
                    'method': method_name,
                    'value': value,
                    'context': context,
                    'suggested_param': f'rollout2_sequence_param_{value}'
                })
            elif 'level' in context or 'class' in context:
                categories['classification_bounds'].append({
                    'method': method_name,
                    'value': value,
                    'context': context,
                    'suggested_param': f'rollout2_classification_{value.replace(".", "_")}'
                })
            else:
                categories['other_constants'].append({
                    'method': method_name,
                    'value': value,
                    'context': context,
                    'suggested_param': f'rollout2_constant_{value.replace(".", "_")}'
                })
    
    return categories

def generate_config_additions(categories: Dict) -> str:
    """Génère les ajouts à faire dans AZRConfig"""
    
    config_additions = []
    
    config_additions.append("    # ========================================================================")
    config_additions.append("    # 🎯 ROLLOUT 2 - PARAMÈTRES NOUVELLES MÉTHODES OPTIMISÉES")
    config_additions.append("    # ========================================================================")
    config_additions.append("    #")
    config_additions.append("    # ⚠️  SECTION CRITIQUE - PARAMÈTRES NOUVELLES MÉTHODES CENTRALISÉS")
    config_additions.append("    # Tous les paramètres des nouvelles méthodes optimisées du Rollout 2")
    config_additions.append("    # ========================================================================")
    config_additions.append("")
    
    for category_name, params in categories.items():
        if params:
            category_title = category_name.replace('_', ' ').title()
            config_additions.append(f"    # {category_title}")
            
            for param in params:
                value = param['value']
                suggested_param = param['suggested_param']
                context = param['context'][:60] + "..." if len(param['context']) > 60 else param['context']
                
                # Déterminer le type
                if '.' in value:
                    param_type = 'float'
                else:
                    param_type = 'int'
                
                config_additions.append(f"    {suggested_param}: {param_type} = {value}  # {context}")
            
            config_additions.append("")
    
    return '\n'.join(config_additions)

def analyze_new_methods_parameters():
    """Analyse principale des paramètres des nouvelles méthodes"""
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return
    
    print("🔍 ANALYSE DES PARAMÈTRES NOUVELLES MÉTHODES ROLLOUT 2")
    print("=" * 55)
    
    # 1. Extraire les valeurs codées en dur
    print("\n📊 1. EXTRACTION DES VALEURS CODÉES EN DUR")
    print("-" * 45)
    
    hardcoded_values = extract_hardcoded_values_in_new_methods(file_content)
    
    total_values = sum(len(values) for values in hardcoded_values.values())
    print(f"📈 Total valeurs trouvées : {total_values}")
    
    for method_name, values in hardcoded_values.items():
        if values:
            print(f"📋 {method_name} : {len(values)} valeurs")
            for value in values[:3]:  # Afficher les 3 premières
                print(f"   • {value['value']} - {value['context'][:50]}...")
            if len(values) > 3:
                print(f"   ... et {len(values) - 3} autres")
    
    # 2. Catégoriser les paramètres
    print("\n🏷️  2. CATÉGORISATION DES PARAMÈTRES")
    print("-" * 35)
    
    categories = categorize_parameters(hardcoded_values)
    
    for category_name, params in categories.items():
        if params:
            category_title = category_name.replace('_', ' ').title()
            print(f"📂 {category_title} : {len(params)} paramètres")
            for param in params[:2]:  # Afficher les 2 premiers
                print(f"   • {param['value']} → {param['suggested_param']}")
            if len(params) > 2:
                print(f"   ... et {len(params) - 2} autres")
    
    # 3. Générer les ajouts de configuration
    print("\n⚙️  3. GÉNÉRATION DES AJOUTS CONFIG")
    print("-" * 35)
    
    config_additions = generate_config_additions(categories)
    
    # Sauvegarder les ajouts
    with open('config_additions_new_methods.txt', 'w', encoding='utf-8') as f:
        f.write(config_additions)
    
    print("✅ Ajouts de configuration générés dans 'config_additions_new_methods.txt'")
    
    # 4. Statistiques finales
    print(f"\n📊 STATISTIQUES FINALES")
    print("-" * 25)
    
    total_params = sum(len(params) for params in categories.values())
    unique_values = len(set(param['value'] for params in categories.values() for param in params))
    
    print(f"Paramètres à centraliser : {total_params}")
    print(f"Valeurs uniques         : {unique_values}")
    print(f"Méthodes analysées      : {len([m for m, v in hardcoded_values.items() if v])}")
    
    if total_params > 0:
        print("\n💡 RECOMMANDATIONS")
        print("-" * 20)
        print("1. ✅ Ajouter les paramètres dans AZRConfig")
        print("2. 🔄 Remplacer les valeurs codées en dur")
        print("3. 🧪 Tester les modifications")
        print("4. 📝 Documenter les nouveaux paramètres")
    else:
        print("\n🏆 EXCELLENT : Aucune valeur codée en dur trouvée !")

if __name__ == "__main__":
    analyze_new_methods_parameters()
