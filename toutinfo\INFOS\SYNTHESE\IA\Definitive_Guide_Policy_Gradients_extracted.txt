# EXTRACTION TEXTUELLE - Definitive_Guide_Policy_Gradients.pdf
# Généré automatiquement le 2025-05-28 10:25:25
# Source: Definitive_Guide_Policy_Gradients.pdf
# ================================================================


--- PAGE 1 ---
The Definitive Guide to Policy Gradients in Deep Reinforcement
Learning:
Theory, Algorithms and Implementations
MatthiasLehmann
UniversityofCologne
ABSTRACT
Inrecentyears,variouspowerfulpolicygradientalgorithmshavebeenproposedindeepreinforcement
learning. While all these algorithms build on the Policy Gradient Theorem, the specific design
choicesdiffersignificantlyacrossalgorithms. Weprovideaholisticoverviewofon-policypolicy
gradient algorithms to facilitate the understanding of both their theoretical foundations and their
practicalimplementations. Inthisoverview,weincludeadetailedproofofthecontinuousversion
ofthePolicyGradientTheorem,convergenceresultsandacomprehensivediscussionofpractical
algorithms. Wecomparethemostprominentalgorithmsoncontinuouscontrolenvironmentsand
provideinsightsonthebenefitsofregularization. Allcodeisavailableathttps://github.com/
Matt00n/PolicyGradientsJax.
Contents
1 Introduction 1
2 Preliminaries 2
2.1 Notation . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2
2.2 ReinforcementLearning . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2
2.2.1 ProblemSetting. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2
2.2.2 ValueFunctions . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 3
2.2.3 On-PolicyPolicyGradientMethods . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 4
2.3 DeepLearning . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 5
3 TheoreticalFoundationsofPolicyGradients 8
3.1 PolicyGradientTheorem . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 8
3.2 ValueFunctionEstimationwithBaselines . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 12
3.3 ImportanceSampling . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 14
4 PolicyGradientAlgorithms 14
4.1 REINFORCE . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 15
4.2 A3C . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 15
4.3 TRPO . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 16
4.4 PPO . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 19
4.5 V-MPO . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 21
4.6 ComparingDesignChoicesinPolicyGradientAlgorithms . . . . . . . . . . . . . . . . . . . . . . . 23
5 ConvergenceResults 25
5.1 LiteratureOverview . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 25
5.2 MirrorLearning . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 25
5.2.1 FundamentalsofMirrorLearning . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 25
5.2.2 PolicyGradientAlgorithmsasInstancesofMirrorLearning . . . . . . . . . . . . . . . . . . 26
4202
raM
1
]GL.sc[
2v26631.1042:viXra
--- PAGE 2 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
5.2.3 ConvergenceProof . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 28
6 NumericalExperiments 33
7 Conclusion 34
Appendices 41
A Hyperparameters 41
B ExtendedExperiments 41
B.1 ComparisontoRLframeworks . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 41
B.2 EntropyBonusinA2C . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 42
B.3 A2CandREINFORCEwithMultipleUpdateEpochs . . . . . . . . . . . . . . . . . . . . . . . . . . 42
C V-MPO:DerivationDetails 42
D AuxiliaryTheory 46
1 Introduction
Reinforcement Learning (RL) is a powerful set of methods for an agent to learn how to act optimally in a given
environmenttomaximizesomerewardsignal. Incontrasttoothermethodssuchasdynamicprogramming,RLachieves
thistaskoflearninganoptimalpolicy,whichdictatestheoptimalbehavior,viaatrial-and-errorprocessofinteracting
withtheenvironment[75]. MostearlysuccessfulapplicationsofRLusevalue-basedmethods(e.g.,[84,79,55]),which
estimatetheexpectedfuturerewardstoinformtheagent’sdecisions. However,thesemethodsonlyindirectlyoptimize
thetrueobjectiveoflearninganoptimalpolicy[82]andarenon-trivialtoapplyinsettingswithcontinuousaction
spaces[75].
Inthiswork,wediscusspolicygradientalgorithms[75]asanalternativeapproach,whichaimstodirectlylearnan
optimalpolicy. Policygradientalgorithmsarebynomeansnew[7,78,87,88],butthissubfieldonlygainedtraction
inrecentyearsfollowingtheemergenceofdeepRL[55]withthedevelopmentofvariouspowerfulalgorithms(e.g.,
[54,71,73]). DeepRLisasubfieldofRL,whichusesneuralnetworksandotherdeeplearningmethods. Theincreased
interestinpolicygradientalgorithmsisduetoseveralappealingpropertiesofthisclassofalgorithms. Theycanbe
usednativelyincontinuousactionspaceswithoutcompromisingtheapplicabilitytodiscretespaces[77]. Incontrast
to value-based methods, policy gradient algorithms inherently learn stochastic policies, which results in smoother
searchspacesandpartlyremediestheexplorationproblemofhavingtoacquireknowledgeabouttheenvironmentin
ordertooptimizethepolicy[75,77]. Insomesettings,theoptimalpolicymayalsobestochasticitself[75]. Lastly,
policygradientmethodsenablesmootherchangesinthepolicyduringthelearningprocess,whichmayresultinbetter
convergenceproperties[77].
Ourgoalistopresentaholisticoverviewofpolicygradientalgorithms. Indoingso,welimitthescopetoon-policy
algorithms,whichwewilldefineinSection2. Thus,weexcludesomepopularalgorithmsincludingDDPG[50],TD3
[24]andSAC[28]. SeeFigure1foranoverviewofRLandthesubfieldswecover. Ourcontributionsareasfollows:
• Wegiveacomprehensiveintroductiontothetheoreticalfoundationsofpolicygradientalgorithmsincludinga
detailedproofofthecontinuousversionofthePolicyGradientTheorem.
• Wederiveandcomparethemostprominentpolicygradientalgorithmsandprovidehighqualitypseudocodeto
facilitateunderstanding.
• Wereleasecompetitiveimplementationsofthesealgorithms,includingthe,tothebestofourknowledge,first
publiclyavailableV-MPOimplementationdisplayingperformanceonparwiththeresultsintheoriginalpaper.
Theremainderofthispaperisorganizedasfollows. Section2introducesfundamentaldefinitionsinRLaswellasan
overviewofdeeplearning. Section3derivesthetheoreticalfoundationsofpolicygradientalgorithmswithaspecial
focusonprovingthePolicyGradientTheorem,basedonwhichwewillconstructseveralexistingpracticalalgorithmsin
Section4. InSection5,wediscussconvergenceresultsfromliterature. Section6,presentstheresultsofournumerical
experimentscomparingthediscussedalgorithms. Section7concludes.
1
--- PAGE 3 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
RLAlgorithms
Model-FreeRL Model-BasedRL
Dyna-Q
PolicyGradientAlgorithms Value-BasedAlgorithms
MuZero
DQN
...
On-PolicyAlgorithms Off-PolicyAlgorithms
SARSA
REINFORCE DDPG ...
A3C/A2C SAC
...
TRPO
PPO
V-MPO
...
Figure1: SimplifiedtaxonomyofRLalgorithms. SubfieldsofRLwefocusonarehighlightedingray.
2 Preliminaries
Inthissection,wepresentprerequisitesforsubsequentchapters. Specifically,weintroduceournotationinSection2.1
andpresentoverviewsofRLinSection2.2andofdeeplearninginSection2.3. Furthermore,welistseveralwell-known
definitionsandresultsfromprobabilitytheory,measuretheoryandanalysis,whichweuseinourpaper,inAppendix
AppendixD.
2.1 Notation
WedenotethesetofnaturalnumbersbyN,naturalnumbersincludingzerobyN ,realnumbersbyRandpositivereal
0
numbersbyR . Wedenoted-dimensionalreal-numberedvectorspacesasRd. ByP(A),wedenotethepowersetofa
+
setA. Wherepossible,wedenoterandomvariableswithcapitallettersandtheirrealizationswiththecorresponding
lowercaseletters. ForanyprobabilitymeasureP,wedenotetheprobabilityofaneventX =xasP(X =x). Similarly,
wewriteP(X =x|Y =y)forconditionalprobabilities. Whenitisclear,whichrandomvariableisreferredto,we
regularlyomitittoshortennotation,i.e. P(X = x) = P(x). Weidentifymeasurablespaces(A,Σ)justbytheset
AaswealwaysusetherespectivepowersetP(A)fordiscretesetsandtheBorelalgebraforintervalsinRd asthe
respectiveσ-algebraΣ. WeexpressmostLebesgueintegralsw.r.t. theLebesguemeasureλusingTheoremD.9. To
(cid:82) (cid:82)
simplifynotation,wewriteintegralsformeasurablefunctionsf onAas f(a)da:= f(a)dλ(a). Wedenote
a∈A a∈A
thatarandomvariableX followsaprobabilitydistributionpbyX ∼p. ForanyrandomvariableX ∼p,wedenote
byE [X]andVar [X]itsexpectationandvariance. Wedenotethesetofprobabilitydistributionsoversome
X∼p X∼p
(cid:82)
measurablespaceAas∆(A). Wewrite|A|forthecardinalityofafinitesetAorareaofaregion da. Forany
a∈A
variableorfunctionx,wecommonlydenoteapproximationstoitbyxˆ.
2.2 ReinforcementLearning
Inthefollowing,weformallydescribethegeneralproblemsettingencounteredinRL,definefundamentalfunctionsand
introducethesubfieldsofRLourworkisfurtherconcernedwith. Sections2.2.1and2.2.2arebasedon[75],Chapter3.
2.2.1 ProblemSetting
EachprobleminstanceinRLconsistsofanagentandanenvironmentwithwhichheinteractstoachievesomespecific
goal. TheenvironmentcompriseseverythingexternaltotheagentandcanbeformalizedasaMarkovDecisionProcess
(MDP). Let an action space A be the set of all actions the agent can take and let a state space S be the set of all
possiblestates,i.e. snapshotsoftheenvironmentatanygivenpointintime. Stateandactionspacescanbediscreteor
continuous1andweassumebothtobecompactandmeasurable. WewriteanMDPasatupleM=(S,A,P,γ,p ),
0
whereP: S×A→∆(S×R)istheenvironment’stransitionfunction,whichdefinestheprobability2P(s′,r |s,a)
1Here,wecallastate/actionspacecontinuousifitisanintervalinRdford∈N.
2Technically,thisisthevalueoftheprobabilitydensityfunctionforcontinuousdistributions.However,weunifyterminologyby
referringtothevaluesofprobabilitydensityfunctionsasprobabilitieshereandinthefollowing.
2
--- PAGE 4 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
oftransitioningtoanewenvironmentstates′ andreceivingrewardr ∈ Rwhentheagentusesactionainstates,
γ ∈ [0,1] is a discount rate and p ∈ ∆(S) is a probability distribution over potential starting states. We assume
0
rewardsrtobebounded. Inthefollowing,ournotationassumesstateandactionspacestobecontinuous.
Wecallsequencesofstates,actionsandrewards(s ,a ,r ,s ,a ,r ,...,
t t t+1 t+1 t+1 t+2
s ,a ,r ,s )trajectories. Aone-steptrajectory,i.e. atuple(s ,a ,r ,s )iscalledatransition. In
t+k−1 t+k−1 t+k t+k t t t+1 t+1
thiswork,welimitourselvestoepisodicsettings,wheretheagentonlyinteractswiththeenvironmentforafinitenumber
ofatmostT stepsafterwhichtheenvironmentisresettoastartingstate. AnepisodemayhoweverbeshorterthanT ifa
terminalstateisreached.Thereforeeachepisodeconsistsofatrajectory(s ,a ,r ,s ,a ,r ,...,s ,a ,r ,s ),
0 0 1 1 1 2 T˜−1 T˜−1 T˜ T˜
withT˜ ≤T. Rewardsareoccasionallyomittedfromthetrajectorynotationsincetheydonotinfluencefuturestates.
Correspondingly,wealsocancomputethealternativetransitionprobabilitiesP(s′ |s,a)= (cid:82) P(s′,r |s,a)dr.
r∈R
Themaingoalinreinforcementlearningistosolvethecontrolproblemoflearningapolicyπ: S →∆(A)tomaximize
theexpectedreturn. ThereturnG := (cid:80)T γkr isthediscountedsumofrewardsfromtimesteptonwards.
t k=0 t+k+1
NotethatG isboundedsincerewardsarebounded. Wedenotetheprobabilityoftakingactionainstatesunderpolicy
t
πwithπ(a|s). Forapolicyπ,itsstationarystatedistributiondπ determinestheprobabilityofbeinginaspecificstate
s∈S atanypointintimewhenfollowingπ.
Let Π be the set of all possible policies. RL algorithms A: Π → Π for the control problem now iteratively learn
policiesbyinteractingwiththeenvironmentusingthecurrentpolicytosampletransitions, whicharethenusedto
updatethepolicy. WewilldiscusshowtheseupdatescanlooklikeinSection2.2.3. AkeycharacteristicofmanyRL
problemsisanecessarytrade-offbetweenexplorationandexploitationinthislearningprocess. Theagenthasnoprior
knowledgeoftheenvironmentandthusneedstoexploredifferenttransitionsinordertolearnwhichstatesandactions
aredesirable. Asstateandactionspacesaretypicallylargehowever,exploitingthealreadyacquiredknowledgeabout
theenvironmentisalsocrucialtoguidethesearchprocessforanoptimalpolicytosubspacesthatholdmostpromise. A
commonapproachtothisexplorationproblemistoaddnoisetothepolicy.
2.2.2 ValueFunctions
Basedonthereturn,wedefinethevalueandaction-valuefunctions,whicharefundamentalinRL.Thevaluefunction
V (s):=E (cid:2) G |S =s (cid:3) (1)
π π t t
givestheexpectedreturnfromstatesonwardswhenfollowingpolicyπ,whichselectsallsubsequentactions. Thus,the
valuefunctionstateshowgooditistobeinaspecificstatesgivenapolicyπ. Notethatherewefollowthegeneral
conventiontowritethisjustasanexpectationoverπ. However, itshouldbenotedthatthisexpectationintegrates
overallsubsequentstatesandactionsthatareobtainedbyfollowingpolicyπ,i.e. Equation(1)computestheexpected
returngiventhatallsubsequentactionsaresampledfromπandallrewardsandnextstatesaresampledfromP. Thisis
implicitinournotationhereaswellasinfurtherexpectations.
Next,wedefinetheaction-valuefunction
Q (s,a):=E (cid:2) G |S =s,A =a (cid:3) ,
π π t t t
which differs from the value function in that the very first action a is provided as an input to the function and not
determinedbythepolicy. WeobservethefollowingrelationbetweenV andQ :
π π
(cid:90)
V (s)= π(a|s)Q (s,a)da.
π π
a∈A
Further,wecall
A (s,a):=Q (s,a)−V (s)
π π π
theadvantagefunction,whichdetermineshowgoodanactionaisinstatesinrelationtootherpossibleactions.
FromthedefinitionsofV andQ wecanderivetheso-calledBellmanequations[9]. StartingfromEquation(1),
π π
weusethedefinitionofthereturnG ,explicitlywriteouttheexpectationforthefirsttransitionandthenapplythe
t
definitionofV again:
π
V (s)=E (cid:2) G |S =s (cid:3)
π π t t
=E (cid:2) R +γG |S =s (cid:3)
π t+1 t+1 t
(cid:90) (cid:90) (cid:90) (cid:18) (cid:104) (cid:105)(cid:19)
= π(a|s) P(s′,r |s,a) r+γE G |S =s′ drds′da
π t+1 t+1
a∈A s′∈Sr∈R
(cid:90) (cid:90) (cid:90)
= π(a|s) P(s′,r |s,a) (cid:0) r+γV (s′) (cid:1) drds′da
π
a∈A s′∈Sr∈R
3
--- PAGE 5 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Thus,wefindaformulationofthevaluefunction,whichdependsonthevalueofsubsequentstates. Collapsingthe
expectationagainyieldstheformknownastheBellmanequationofthevaluefunction:
V (s)=E (cid:2) R +γV (S ) (cid:3) .
π π t+1 π t+1
Similarly,wecanfindtheBellmanequationfortheaction-valuefunction:
Q (s,a)=E (cid:2) R +γQ (S ,A ) (cid:3) . (2)
π π t+1 π t+1 t+1
Now, we can formally define what optimality means in RL. An optimal policy π∗ is defined by V (s) ≥ V (s)
π∗ π
for all states s and policies π, i.e. any optimal policy maximizes the expected return. It can be shown that in
every finite MDP, a deterministic optimal policy exists [56]. All optimal policies share the same optimal value
functionV∗(s):=max V (s)andoptimalaction-valuefunctionQ∗(s,a):=max Q (s,a)andselectactions
π∈Π π π∈Π π
a∈argmax Q∗(s,a′)foreverystate. ApplyingthistoEquation(2)yieldstheBellmanoptimalityequation
a′
Q∗(s,a)=E (cid:2) R +γQ∗(S ,A ) (cid:3)
π∗ t+1 t+1 t+1
=E(cid:2) R +γmaxQ∗(S ,a′) (cid:3)
t+1 t+1
a′∈A
Wecitethefollowingresultwithoutprooffrom[75]onhowtoobtainanoptimalpolicy,whichwewillrevisitinSection
5.
Theorem2.1. (GeneralizedPolicyIteration)Letπ bethecurrentpolicy. Then,GeneralizedPolicyIterationupdates
old
itspolicyby
π ∈argmaxE (cid:2) Q (s,A) (cid:3)
new A∼π πold
π∈Π
(cid:0) (cid:1)∞
foralls∈S.Let π beasequenceofpoliciesobtainedthroughGeneralizedPolicyIteration. Then,thissequence
n n=0
convergestoanoptimalpolicy,i.e.
lim π =π∗
n
n→∞
and
lim Q =Q∗.
n→∞
πn
2.2.3 On-PolicyPolicyGradientMethods
Finally,wewilldelineatethesubfieldsofRLonwhichourworkfocuses. Inthiscontext,wewillsuccessivelyintroduce
functionapproximation,policygradientmethodsandtheon-policyparadigm.
RLalgorithmsaremostlyconcernedwithlearningfunctionssuchasπ,V orQ . Earlyreinforcementmethodslearn
π π
exactrepresentationsofthesebymaintaininglookuptableswithentriesforeachpossiblefunctioninput[75]. While
thisapproachyieldstheoreticalconvergenceguarantees[56],itispracticallyverylimited. Similarstatesaretreated
independentlysuchthatlearningsdonotgeneralizefromonestatetootherswhilespecificstatesareonlyrarelyvisited
inlargestatespaces[75]. Moreover,thisapproachisnotapplicabletocontinuousspaces. Functionapproximation
remediestheseshortcomingsbyparameterizingthefunctiontobelearned. Letf (x)bethislearnablefunction,where
θ
θarethefunction’sparameters,whichareadjustedoverthecourseoflearning,andxarethefunctionsinputssuch
asstatesandactionsorrepresentationsthereof. Bychoosingf tobecontinuousinitsinputs,wecanensurethatf
θ θ
generalizesacrossitsinputswhenwefitittosampledtransitions[75]. f canbeassimpleasalinearmapping,i.e.
θ
f (x)=θTx,howeverrecentworksmostlyuseneuralnetworksasfunctionapproximators(e.g.,[55,72]). Thefield
θ
usingneuralnetworksasfunctionapproximatorsiscoineddeepRL[55]. Fortheremainderofthispaper,youcan
consideranylearnedfunctiontobeaneuralnetworkunlessexplicitlystatedotherwise,althoughallourstatements
applytoanydifferentiablefunctionapproximators. Wewillintroducedeeplearningandneuralnetworksindetailin
Section2.3.
Policygradientmethodsposeanalternativetovalue-basedmethodsinRL.MostearlysuccessesinRLusevalue-based
methodssuchasQ-Learning[84]orSARSA[67],thataimatlearningasequenceofvaluefunctionsconvergingtothe
optimalvaluefunction,fromwhichanoptimalpolicycanthenbeinferred. Incontrast,policy-basedRL,whichwe
focusoninthiswork,directlylearnsaparameterizedpolicyπ . Themainideainthislearningprocessistoincreasethe
θ
probabilityofthoseactionsthatleadtohigherreturnsuntilwereachan(approximately)optimalpolicy[75]. While
thisoptimizationproblemcanbeapproachedinseveralways,gradient-basedmethodsaremostcommonlyused[82].
Following[77],wedefinepolicygradientmethodsasfollows.
Definition 2.2. (Policy Gradient Algorithm) Let π : S → ∆(A) be a fully differentiable function with learnable
θ
parametersθ ∈Rd mappingstatestoaprobabilitydistributionoveractions. LetJ: Rd →Rbesomeperformance
4
--- PAGE 6 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
measureoftheparameters. Wecallanylearningalgorithmapolicygradientalgorithmifitlearnsitspolicyπ by
θ
updatingθviagradientascent(ordescent)onJ,i.e. itsupdateshavethegeneralform
θ ←θ+α∇ J(θ), (3)
new θ
whereα∈Risastepsizeparameterofthealgorithm.
Inpolicy-basedRL,twodistinctwaysexisttohavethepolicyoutputaprobabilitydistributionoveractions,fromwhich
actionscanbesampled[75]. Fordiscreteactionspaces,weconstructadiscretedistributionovertheactionspaceby
normalizingthepolicies’rawoutputsviaasoftmaxfunction[26]. Inthiscase,wehave
exp(π (a|s))
π(a|s)= θ .
(cid:80) exp(π (a′ |s))
a′∈A θ
Forcontinuousactionspaces,weletπ outputthemeanµandstandarddeviationσ ofaGaussiandistribution,i.e.
(cid:0) (cid:1) θ
π (s)= µ (s),σ (s) suchthat
θ θ θ
(cid:32) (cid:0) (cid:1)2(cid:33)
1 a−µ (s)
θ
π(a|s)= √ exp − .
σ θ (s) 2π 2σ θ (s)2
ThisparameterizationofaGaussiandistributionforthepolicywasfirstintroducedby[87,88]. Asactionspacesare
commonlybounded,theactionssampledfromsuchaGaussianaretypicallytransformedtobewithinthesebounds
eitherbyclippingorbyapplyingasquashingdistribution[5]. Further,wehighlightthatthepolicieslearnedbypolicy
gradient methods in both the discrete and the continuous case are generally stochastic. This stands in contrast to
value-basedmethodswhichgenerallylearndeterministicpolicies[77]. Policygradientmethodsarethecorefocusof
thispaperandwillbediscussedin-depthinsubsequentsections.
Lastly,wedelineateon-policyfromoff-policyalgorithms. InRL,wedistinguishbetweenbehaviorandtargetpolicies
[75]. Abehaviorpolicyisapolicywhichgeneratesthedatainformoftrajectoriesfromwhichwewanttolearn,i.e.
thisisthepolicyfromwhichwesampleactionswheninteractingwiththeenvironment. Conversely,thetargetpolicyis
thepolicywhichwewanttolearnabouttoevaluatehowgooditisinthegivenenvironmentandimproveit. Algorithms
wherebehaviorandtargetpolicyarenotidentical,e.g. Q-Learning[84]orDQN[55],arereferredtoasoff-policy
algorithms. In this work, we onlydiscuss on on-policy algorithms, where behavior and target policy are identical.
Hence,whenspeakingofpolicygradientalgorithmsinthefollowing,wealwaysimplicitlymeanon-policypolicy
gradientalgorithmsifnotmentionedotherwise.
2.3 DeepLearning
Inthissection,weintroducedeeplearningasasubfieldofmachinelearningsinceitsmethodsarecommonlyused
inpolicygradientalgorithms. Inrecentyears,deeplearninghasemergedasthepremiermachinelearningmethod
invariousfields,enablingstate-of-the-artperformanceindomainssuchascomputervision(e.g.,[44,29,22])and
naturallanguageprocessing(e.g.,[83,14]. Following[47]and[26],wedefinedeeplearningasasetoftechniques
tosolvepredictiontasksbylearningmultiplelevelsofrepresentationsfromrawdatausingacompositionofsimple
non-linearfunctions. Thiscompositionoffunctions,thatwewilldescribeindetaillater,isreferredtoas(deep)neural
network. Deeplearningstandsincontrasttoconventionalmachinelearningtechniqueslikelogisticregressions,which
typicallyrequirehand-engineeredrepresentationsasinputstobeeffective[47]. Inthefollowing,weintroducethe
generalproblemsettingofdeeplearningusingthenotationof[10],formalizeneuralnetworksanddescribehowthey
aretrained.
ConsidermeasurablespacesX andY. Z :=X×Y isthedataspacewitheachelementz =(x,y)∈Z beingatupleof
inputfeaturesx∈X andalabely ∈Y. LetM(X,Y)bethesetofmeasurablefunctionsfromX toY. Theproblems
weencounterindeeplearningarepredictiontasks. Thus,thegoalistolearnamappingf ∈M(X,Y)frominputsto
labelsbyminimizingsomelossfunctionLovertrainingdataS ={z(1),...,z(m)}suchthatitgeneralizestounseen
dataz ∈ Z. Tolearnthefunctionf, wefirstselectahypothesissetF ⊂ M(X,Y). Deeplearningthenprovides
learningalgorithmsA: Z →F thatusetrainingdataS tolearnthedesiredfunctionf =A(S). Beforewediscussthis
learningprocess,wewillfirstfurthercharacterizethemappingtobelearned.
Indeeplearning,functionsinthehypothesissetF representinstancesofneuralnetworks. Notethatherewelimit
ourselvestofeedforwardnetworks,alsocalledmultilayerperceptrons(MLP),andwillnotdiscusstransformers[83],
recurrent(RNN)[32]orconvolutionalneuralnetworks(CNN)[44].
Definition2.3. (FeedforwardNeuralNetwork)Afeedforwardneuralnetworkf: X →Y isacompositionoffunctions
f =f(n+1)◦···◦f(1)
5
--- PAGE 7 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
a(1)
1
a(2)
1
x 1 a( 2 1) yˆ 1
a(2)
2
x
2
f(1) a(1) f(2) f(3)
3
a(2)
3
x 3 a( 4 1) yˆ 2
a(2)
4
a(1)
5
Figure2: Aneuralnetworkwithhiddenlayersofsizes5and4asadirectedgraph.
that is differentiable almost everywhere. We refer to f(i), i = 1,...,n as hidden layers, whereas f(n+1) is the
non-hiddenoutputlayer. Consequently,ndenotesthenumberofofhiddenlayersinthenetwork. Eachhiddenlayeris
characterizedbyalayerwidthN . LetN andN furtherbethesizeoftheinputandoutputvectorsrespectively.
i 0 n+1
Then,wecanwriteeachlayeras
(cid:16) (cid:17)
f(i)(x)=g W(i)x+b(i) ,
wherexistheoutputofthepreviouslayerorthenetwork’sinputsfori=1,W(i) ∈RNi×Ni−1 andb(i) ∈RNi arethe
layer’sweightmatrixandbiasvectorrespectivelyandg: R→Risadifferentiableactivationfunctionintroducing
non-linearity. gisappliedelement-wise.
AnMLPcanbecharacterizedbyitsarchitecturea= (cid:0) (N )n+1, g (cid:1) ,consistingoflayersizesandtheactivationfunction
i i=0
tobeused. Thenumberoflayersn+1isalsoreferredtoasthedepthofthenetwork. TheUniversalApproximation
Theorem[17,35]underpinstheexpressivityofneuralnetworks: atwo-layernetworkcanalreadyapproximateany
measurablefunctionarbitrarilywellunderweakconditionsontheactivationfunction. Technically,eachlayercan
featureadifferentactivationfunctionalbeitthisisuncommon. Theactivationfunctionoftheoutputlayerisnotdefined
bythearchitectureabutisderivedfromthepredictiontask. Thestandardchoiceforactivationfunctionsinthehidden
layersisarectifiedlinearunit(ReLU)3 [57], duetotypicallyfastlearning[25]. See[48]foranoverviewofother
commonlyusedactivationfunctions. Theoutputlayertypicallyusesnoactivationfunctionforregressiontasksand
sigmoidorsoftmaxfunctionsforclassificationtasks. Eachelementofalayerf(i) iscalledaneuron. Theoutputs
a(i) = (cid:0) f(i)◦···◦f(1)(cid:1) (x)ofanylayerarethelearnedrepresentationsoftheinputsx. Wedenotetheoutputsf(x),
i.e. thepredictions,oftheneuralnetworkwithyˆ. Figure2depictsaneuralnetworkasanacyclicdirectedgraph.
SelectingahypothesissetF isdoneimplicitlybyspecifyinganarchitecturea. Hence,wedenotethehypothesisset
forarchitecturea,whoseelementsareallMLPswiththatarchitecture,byF . TheMLPsinF thereforedifferonly
a a
intheirweightsandbiases. Wecallthesethe(learnable)parametersofthenetworkandtypicallycollectthemina
flattenedparametervectorθ ∈Rd. WedenoteanMLPwithparametersθasf .
θ
GivenahypothesissetF ,wenowaimtolearnaneuralnetworkf ∈ F ,i.e. tolearnparametersθ,suchthatwe
a θ a
reducetheexpectedlossorrisk,
(cid:90)
R(f):=E Z∼P Z (cid:2) L(f,Z) (cid:3) = L(f,z)dP Z (z),
z∈Z
over the data distribution P for some appropriately chosen differentiable loss function L: F ×Z → R [10, 26].
Z
HereP istheimagemeasureofZ onZ,fromwhichthetrainingdataS = {z(1),...,z(m)}andunknownout-of-
Z
sampledatazisdrawn. Wegenerallyassumethattrainingz(1),...,z(m)andout-of-sampledatazarerealizationsof
i.i.d. randomvariablesZ(1),...,Z(m),Z ∼ P [10]. ForagivenMLPf = A(S)trainedonS,theriskbecomes
Z θ
3NotethattheReLUfunctionisnotdifferentiableat0.Inpractice,thisiscircumventedbyusingitssub-derivatives.
6
--- PAGE 8 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
R(f θ ) = E Z∼P Z (cid:2) L(f θ ,Z) | S (cid:3) . Inpractice,noisydataresultsinapositivelowerboundonrisk,i.e. anirreducible
error[10]. Commonlossfunctionsarebinarycross-entropyloss,
(cid:0) (cid:1)
L(f,(x,y))=− y·ln(f(x))+(1−y)·ln(1−f(x)) ,
for(binary)classificationandmeansquarederror(MSE),
(cid:0) (cid:1)2
L(f,(x,y))= y−f(x) ,
forregressiontasks. Sometimes,lossfunctionsareaugmentedbyregularizationtermsΩ(θ)suchasanL2-penaltyof
theparameters,i.e. β∥θ∥2withβ ∈R[26].
2
ThedatadistributionP isgenerallyunknown. Hence,wereplaceitbyanempiricaldistributionbasedonthesampled
Z
trainingdataS anduseempiricalriskminimization(ERM)asthelearningalgorithmtominimizeit[26,10].
Definition 2.4. (Empirical Risk). Given training data S = {z(1),...,z(m)} and a function f ∈ M(X,Y), the
θ
empiricalriskisdefinedby
m
Rˆ (f ):= 1 (cid:88) L(f ,z(i)). (4)
S θ m θ
i=1
Definition2.5. (ERMlearningalgorithm). GivenhypothesissetF andtrainingdataS,anempiricalriskminimization
a
algorithmAermterminateswithan(approximate4)minimizerfˆ ∈F ofempiricalrisk:
S a
Aerm(S)=fˆ ∈argminRˆ (f).
S S
f∈Fa
We approximately minimize empirical risk typically via gradient-based methods due to efficient computation of
point-wisederivativesviathebackpropagationalgorithm[66,40]. Backpropagationmeansthepracticalapplication
of the chain rule to neural networks. The gradient of the objective function L with respects to the i-th layer’s
inputsa(i−1) canbecomputedbyworkingbackwardsfromthegradientwithrespectstothelayer’soutputsa(i) as
∇ L= (cid:80) (∇ a(i)) ∂L . Fromthesegradients,thegradientswithrespectstotheweightsandbiasesineach
a(i−1) j a(i−1) j ∂a(i)
j
layercanbecalculatedsimilarly. Duetothisflowofinformationfromtheobjectivefunctiontoeachofthelayers,the
optimizationofMLPsisalsoreferredtoasbackwardspass,incontrasttotheforwardpassofcalculatingyˆ=f(x).
ThefullbackpropagationalgorithmforMLPsisformulatedinAlgorithm1.
Algorithm1Backpropagation,pseudocodetakenfrom[26]
Require: labels y, regularizer Ω(θ), network outputs yˆ, activated and unactivated layer outputs a(k) and h(k) for
k =1,...,n,activationfunctiong,lossL
δ ←∇ L
yˆ
fork =n,...,1do
δ ←∇ L=δ⊙g′(h(k)) ▷hadamardproductifgiselement-wise
h(k)
∇ L←δ+∇ Ω(θ)
b(k) b(k)
∇ L←δh(k−1)T+∇ Ω(θ)
W(k) W(k)
δ ←∇ L=W(k)Tδ
a(k−1)
endfor
Thegradientscomputedviabackpropagationareusedtoupdatetheparametersineachlayerusinggradientdescent.
However,duetotheprohibitivecomputationalcostsofevaluatingtheexpectationinEquation(4),computinggradients
onlyonasubsetofthetrainingdataisgenerallypreferredandtypicallyalsoresultsinfasterconvergence[26]. At
eachiteration,abatchS′ofdatawithsizem′ ≤m(typicallym′ ≪m)israndomlysampledfromthetrainingdatato
conducttheupdate[10]
1 (cid:88) (cid:16) (cid:17)
Θ(k) :=Θ(k−1)−α ∇ L f ,z . (5)
km′ θ Θ(k−1)
z∈S′
Here,Θisarandomvariablewhoserealizationsareneuralnetworkparametersθ. α isthestepsizeorlearningrateon
k
thek-thoptimizationstep. Thelearningrateiscommonlydecayedoverthetrainingprocesstohelpconvergence[26].
4Inpractice,theempiricalriskisgenerallyhighlynon-convexprohibitingguaranteedconvergencetoaglobalminimum[10].
7
--- PAGE 9 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
TheprocedureusingupdatesasinEquation(5)isknownasstochastic(minibatch)gradientdescent(SGD)5[26]and
datesbackto[63,41]. UsingSGDhastheadditionalbenefitofintroducingrandomfluctuationswhichenableescaping
saddlepoints[10]. SGDinitsgeneralformisdepictedinAlgorithm2,whereinourcontextr(θ) = Rˆ (f ). The
S θ
neuralnetworkparametersθaresettobetherealizationofthefinalΘ(K)oraconvexcombinationof (cid:0) Θ(k)(cid:1)K [10].
k=1
Algorithm2StochasticGradientDescent,pseudocodefrom[10]
Require: Differentiablefunctionr: Rd →R,stepsizesα ∈(0,∞),k =1,...,K,Rd-valuedrandomvariableΘ(0)
k
fork =1,...,K do
LetDk bearandomvariablesuchthatE(cid:2) D(k) |Θ(k−1)(cid:3) =∇r(Θ(k−1))
Θ(k) ←Θ(k−1)−α D(k)
k
endfor
DespitethestochasticityofSGDandhighlynon-convexlosslandscapes,SGD’sconvergencecanbeguaranteedinsome
regimes[10],anditexhibitsstrongperformanceinpractice[26]. Hence,SGDanditsvariantsarethedefaultchoice
tooptimizeneuralnetworks. ThemostprominentlyusedvariantisAdam[42],whichusesmomentum[59]andan
adaptivescalingofgradientstostabilizelearning. Nonetheless,theinitializationofθisalsoimportantforconvergence.
Biasesarecommonlyinitializedto0whereasweightsarerandomlyinitializedcloseto0usingvariousstrategies[26].
Finally,notethatregardlessofthenon-convexityofthelosslandscapes,localminimaarenotconsideredproblematicif
theneuralnetworksarelargeenough[18,15]
In practice, the training of neural networks is an iterative process. We alternate between choosing the network
architectureaaswellasfurtherhyperparametersofthelearningalgorithmsuchasthelearningratesα,andapproximately
minimizing the empirical risk for this set of hyperparameters. This is generally a trial-and-error process to find a
suitablesetofhyperparameterstomaximizegeneralizationperformance,i.e. tominimizerisk. Toapproximaterisk,
thetrainedmodelsaretypicallyevaluatedbytheempiricalriskonaheld-outtestdataset,whichwasnotseenduring
training[26]. Theachievedempiricalriskcanbedecomposedintoageneralizationerror,anoptimizationerror,an
approximationerrorandtheirreducibleerror[10]. Thegeneralizationerroristhedifferencebetweenempiricaland
actualriskstemmingfromtherandomsamplingoftrainingdata,whichmaynotberepresentativeoftheactualdata
distributionP . Theoptimizationerroristheresultofpotentiallynotfindingaglobalmimimumduringthelearning
Z
process. TheapproximationerroristhedifferencebetweentheminimumachievableriskoverfunctionsinF andover
a
allf ∈M(X,Y).
3 TheoreticalFoundationsofPolicyGradients
Having introduced the fundamentals of deep RL, we can now discuss policy gradient algorithms in detail. In this
section,wederivetheirtheoreticalfoundations. OurmainfocusisgoingtobethePolicyGradientTheorem,onwhich
allpolicygradientalgorithmsbuild. ThistheoremwillbediscussedinSection3.1. Furthermore,Sections3.2and3.3
introducethetheoreticaljustificationsforadditionalmethodsthatarefrequentlyusedinpolicygradientalgorithms.
3.1 PolicyGradientTheorem
GivenanMDPM=(S,A,P,γ,p ),consideraparameterizedpolicyπ ,whichisdifferentiablealmosteverywhere,
0 θ
andthefollowingobjectivefunctionJ formaximizingtheexpectedepisodicreturn:
J(θ)=E (cid:2) G (cid:3)
S0∼p0,πθ 0
=E (cid:104) E (cid:2) G |S =S (cid:3)(cid:105)
S0∼p0 πθ t t 0
(cid:104) (cid:105)
=E V (S )
S0∼p0 πθ 0
TheideaofpolicygradientalgorithmsistomaximizeJ(θ)overtheparametersθbyperforminggradientascent[75].
Hence,werequirethegradients∇ J(θ),howeveritisapriorinotobvioushowtheright-handsideE (cid:2) G (cid:3)
θ S0∼p0,πθ 0
dependsonθaschangesinthepolicyπalsoaffectthestatedistributiondπ. ThePolicyGradientTheorem[76,52]
yieldsananalyticformof∇ J(θ)fromwhichwecansamplegradientsthatdoesnotinvolvethederivativeofdπ.
θ
Here,wefocusontheundiscountedcase,i.e. γ =1. Notethatanydiscountedprobleminstancecanbereducedtothe
undiscountedcasebylettingtherewardfunctionabsorbthediscountfactor[70].
5SometimesSGDreferstoupdateswhichonlyinvolveasingledatapoint.Wehoweverfollowthenowadayscommonterminology
ofcallinganysample-basedgradientdescentstochastic.
8
--- PAGE 10 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Theorem3.1. (PolicyGradientTheorem)ForagivenMDP,letπ bedifferentiablew.r.t. θand∇ π bebounded,let
θ θ θ
Q bedifferentiablew.r.t. θand∇ Q beboundedforalls∈S anda∈A. Then,thereexistsaconstantηsuchthat
πθ θ πθ
(cid:104) (cid:105)
∇ θ J(θ)=ηE S∼dπθ,A∼πθ Q πθ (S,A)∇ θ lnπ θ (A|S) . (6)
Proof. Welargelyfollowtheproofby[75]albeitinamoredetailedformandextendedtocontinuousstateandaction
spaces. Toenhancereadability,weomitsubscriptsθforthepolicyπandallgradients∇butbothalwaysdependonthe
parametersθ.
Startingfromthedefinitionoftheobjectivefunction,weexplicitlywriteouttheexpectationoverstartingstates,usethe
(cid:82)
relationshipbetweenvalueandaction-valuefunction,V (s)= π(a|s)Q (s,a)da,anddifferentiatebyparts.
π a∈A π
∇J(θ)=∇E (cid:2) V (S) (cid:3)
S∼p0 π
(cid:90)
=∇ p (s)V (s)ds
0 π
s∈S
(cid:90) (cid:90)
=∇ p (s) π(a|s)Q (s,a)dads
0 π
s∈S a∈A
(cid:90) (cid:18) (cid:90) (cid:90) (cid:19)
(cid:0) (cid:1)
= p (s) ∇π(a|s) Q (s,a)da+ π(a|s)∇Q (s,a)da ds. (7)
0 π π
s∈S a∈A a∈A
Note that in the last step via used the Leibniz integral rule (Theorem D.10) to swap the order of integration and
differentiationpriortoapplyingtheproductrule. TheconditionsforLeibnizaresatisfiedsinceπ(· | s)Q (s,·)is
π
integrableforanys∈S anditspartialderivativesexistandareboundedforalls∈S anda∈AsinceπandQ are
π
boundedand∇Q and∇πexistandareboundedbyassumption.
π
Now,considertherecursiveformulationoftheaction-valuefunction
(cid:90) (cid:90)
Q (s,a)= P(s′,r |s,a) (cid:0) r+V (s′) (cid:1) drds′.
π π
s′∈Sr∈R
Duetotheidentity (cid:82) P(s′,r |s,a)dr =P(s′ |s,a)andsincerealizedrewardsrandenvironmenttransitionsfora
r∈R
givenactionnolongerdependonthepolicy,wecanreformulatethegradientsofQ w.r.t. θ,againusingtheLeibniz
π
integralrule.
(cid:90) (cid:90)
∇Q (s,a)=∇ P(s′,r |s,a) (cid:0) r+V (s′) (cid:1) drds′
π π
s′∈Sr∈R
(cid:90) (cid:90)
= P(s′,r |s,a)∇ (cid:0) r+V (s′) (cid:1) drds′
π
s′∈Sr∈R
(cid:90) (cid:90)
= P(s′,r |s,a)∇V (s′)drds′
π
s′∈Sr∈R
(cid:90) (cid:18)(cid:90) (cid:19)
= P(s′,r |s,a)dr ∇V (s′)ds′
π
s′∈S r∈R
(cid:90)
= P(s′ |s,a)∇V (s′)ds′. (8)
π
s′∈S
Further,notethatforalls∈S
(cid:90)
∇V (s)=∇ π(a|s)Q (s,a)da
π π
a∈A
(cid:90) (cid:90)
(cid:0) (cid:1)
= ∇π(a|s) Q (s,a)da+ π(a|s)∇Q (s,a)da, (9)
π π
a∈A a∈A
9
--- PAGE 11 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
whichisequivalenttotheinnerexpressioninEquation(7). Byusing(8)and(9),wecantransform(7)intoarecursive
form,whichwearethengoingtounrollsubsequentlytoyieldanexplicitform. Inthefollowing,wesimplynotationby
defining
(cid:90)
(cid:0) (cid:1)
ϕ(s):= ∇π(a|s) Q (s,a)da. (10)
π
a∈A
Applying(10)and(8)to(7)inorderandrearrangingtheintegralsgives
(cid:90) (cid:18) (cid:90) (cid:90) (cid:19)
(cid:0) (cid:1)
∇J(θ)= p (s) ∇π(a|s) Q (s,a)da+ π(a|s)∇Q (s,a)da ds
0 π π
s∈S a∈A a∈A
(cid:90) (cid:18) (cid:90) (cid:19)
= p (s) ϕ(s)+ π(a|s)∇Q (s,a)da ds
0 π
s∈S a∈A
(cid:90) (cid:18) (cid:90) (cid:90) (cid:19)
= p (s) ϕ(s)+ π(a|s) P(s′ |s,a)∇V (s′)ds′da ds
0 π
s∈S a∈A s′∈S
(cid:90) (cid:18) (cid:90) (cid:90) (cid:19)
= p (s) ϕ(s)+ π(a|s)P(s′ |s,a)da∇V (s′)ds′ ds (11)
0 π
s∈S s′∈Sa∈A
Inthefinalstep,weswitchedtheorderofintegrationusingFubini’sTheorem(TheoremD.11),whichisapplicable
since∇V isboundedandπ(· | s)P(· | s,·)isaprobabilitymeasureonS ×Asuchthat|π(· | s)P(· | s,·)∇V |is
π π
integrableovertheproductspaceS ×A. TounrollEquation(11)acrosstime,weintroducenotationformulti-step
transitionprobabilities. Letρ (s→s′,k)betheprobabilityoftransitioningfromstatestos′afterkstepsunderpolicy
π
π. Wehavethat
(cid:26) 1 ifs=s′,
ρ (s→s′,0):=
π 0 else
andρ (s→s′,1):= (cid:82) π(a|s)P(s′ |s,a)da. Now,wecanrecursivelywrite
π a∈A
(cid:90)
ρ (s→s′′,k+1)= ρ (s→s′,k)ρ (s′ →s′′,1)ds′.
π π π
s′∈S
10
--- PAGE 12 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Usingthisnotation,iterativelysubstitutingin(8)and(9)andapplyingFubini,wecanunroll(11):
(cid:90) (cid:18) (cid:90) (cid:90) (cid:19)
∇J(θ)= p (s) ϕ(s)+ π(a|s)P(s′ |s,a)da∇V (s′)ds′ ds
0 π
s∈S s′∈Sa∈A
(cid:90) (cid:18) (cid:90) (cid:19)
= p (s) ϕ(s)+ ρ (s→s′,1)∇V (s′)ds′ ds
0 π π
s∈S s′∈S
(cid:90) (cid:32) (cid:90) (cid:18) (cid:90) (cid:19) (cid:33)
= p (s) ϕ(s)+ ρ (s→s′,1) ϕ(s′)+ π(a|s′)∇Q (s′,a)da ds′ ds
0 π π
s∈S s′∈S a∈A
(cid:90) (cid:32) (cid:90) (cid:18) (cid:90) (cid:19) (cid:33)
= p (s) ϕ(s)+ ρ (s→s′,1) ϕ(s′)+ ρ (s′ →s′′,1)∇V (s′′)ds′′ ds′ ds
0 π π π
s∈S s′∈S s′′∈S
(cid:32)
(cid:90) (cid:90)
= p (s) ϕ(s)+ ρ (s→s′,1)ϕ(s′)ds′
0 π
s∈S s′∈S
(cid:90) (cid:18) (cid:90) (cid:19) (cid:33)
+ ρ (s→s′,1)ρ (s′ →s′′,1)ds′ ∇V (s′′)ds′′ ds
π π π
s′′∈S s′∈S
(cid:90) (cid:18) (cid:90) (cid:90) (cid:19)
= p (s) ϕ(s)+ ρ (s→s′,1)ϕ(s′)ds′+ ρ (s→s′′,2)∇V (s′′)ds′′ ds
0 π π π
s∈S s′∈S s′′∈S
(cid:90) (cid:18) (cid:90)
= p (s) ρ (s→s,0)ϕ(s)+ ρ (s→s′,1)ϕ(s′)ds′
0 π π
s∈S s′∈S
(cid:90) (cid:90) (cid:19)
+ ρ (s→s′′,2)ϕ(s′′)ds′′+ ρ (s→s′′′,3)∇V (s′′′)ds′′′ ds
π π π
s′′∈S s′′′∈S
.
.
.
(cid:90) (cid:90) T
(cid:88)
= p (s) ρ (s→s′,t)ϕ(s′)ds′ds
0 π
s∈S s′∈S t=0
Wesetη (s′):= (cid:80)T ρ (s→s′,t),rearrangetheintegralsandmultiplyby1toobtain
s t=0 π
(cid:90) (cid:90) T
(cid:88)
∇ J(θ)= p (s) ρ (s→s′,t)ϕ(s′)ds′ds
θ 0 π
s∈S s′∈S t=0
(cid:90) (cid:90)
= p (s)η (s′)ϕ(s′)dsds′
0 s
s′∈Ss∈S
(cid:82) (cid:82) p (s)η (s′′)dsds′′ (cid:90) (cid:90)
= (cid:82) s′′∈S (cid:82) s∈S p 0 (s)η s (s′′)dsds′′ p 0 (s)η s (s′)dsϕ(s′)ds′
s′′∈S s∈S 0 s
s′∈Ss∈S
(cid:90) (cid:90) (cid:90) (cid:82) p (s)η (s′)ds
= p 0 (s)η s (s′′)dsds′′ (cid:82) (cid:82) s∈S p 0 (s)η s (s′′)dsds′′ ϕ(s′)ds′
s′′∈S s∈S 0 s
s′′∈Ss∈S s′∈S
(cid:90) (cid:90) (cid:90)
= p (s) η (s′′)ds′′ds dπ(s′)ϕ(s′)ds′. (12)
0 s
s∈S s′′∈S s′∈S
Inthefinalstep,weusedtheidentity
(cid:82) p (s)η (s′)ds
dπ(s′)= (cid:82) (cid:82) s∈S 0 s ,
p (s)η (s′′)dsds′′
s′′∈S s∈S 0 s
11
--- PAGE 13 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
whichcanbeseenasη (s′)istheaccumulatesumoverprobabilitiesofreachings′afteranynumberofstepsforagiven
s
startingstate. Integratingoverthestartingstatedistributionandnormalizinghenceyieldstheprobabilityofvisiting
states′andtherebythestationarydistributiondπ overstatesunderthecurrentpolicy.
Finally,wecanderivethecanonicalformofthePolicyGradientTheoremfrom(12)byusingthedefinitionofϕ(s),
setting
(cid:90) (cid:90)
η := p (s) η (s′′)ds′′ds
0 s
s∈S s′′∈S
andmultiplyingwith1:
(cid:90) (cid:90) (cid:90)
∇J(θ)= p (s) η (s′′)ds′′ds dπ(s′)ϕ(s′)ds′
0 s
s∈S s′′∈S s′∈S
(cid:90) (cid:90)
=η dπ(s′) (cid:0) ∇π(a|s′) (cid:1) Q (s′,a)dads′
π
s′∈S a∈A
(cid:90) (cid:90) ∇π(a|s′)
=η dπ(s′) π(a|s′) Q (s′,a)dads′
π(a|s′) π
s′∈S a∈A
(cid:90) (cid:90)
=η dπ(s′) π(a|s′) (cid:0) ∇lnπ(a|s′) (cid:1) Q (s′,a)dads′
π
s′∈S a∈A
(cid:20) (cid:104) (cid:105)(cid:21)
=ηE E Q (S,A)∇lnπ(A|S) .
S∼dπ A∼π π
ThePolicyGradientTheoremprovidesuswithanexplicitformofthepolicygradientsfromwhichwecansample
gradients.Thisallowstheuseofgradient-basedoptimizationtodirectlyoptimizethepolicyusingthemethodspresented
inSection2.3. Thus,thetheoremservesasthefoundationforthepolicygradientalgorithmswhichwewilldiscussin
Section4.
WeconcludethissectionwithsomefurtherremarksonEquation(6). First,wenotethatforanystartingstates∈S,we
havethat
(cid:90) (cid:90) (cid:90) (cid:90) T
(cid:88)
η = p (s) η (s′)ds′ds= p (s) ρ (s→s′,t)ds′ds
0 s 0 π
s∈S s′∈S s∈S s′∈S t=0
(cid:34) T (cid:90) (cid:35)
(cid:88)
=E ρ (S →s′,t)ds′ ,
S∼p0 π
t=0 s′∈S
whichistheaverageepisodelength6underpolicyπ[75]. Second,theuseofgradient-basedmethodsmakesitsufficient
tosamplegradientswhichareonlyproportionaltotheactualgradientssinceanyconstantofproportionalitycanbe
absorbedbythelearningrateparameteroftheoptimizationalgorithms. Hence,ηiscommonlyomitted[75],i.e.
(cid:104) (cid:105)
∇ θ J(θ)∝E S∼dπθ,A∼πθ Q πθ (S,A)∇ θ lnπ θ (A|S) . (13)
Weobservethatalltermsontherighthandsideareknownorcanbeestimatedviasampling.
3.2 ValueFunctionEstimationwithBaselines
Inpractice,theresultingestimatesofthepolicygradientscanbecomeverynoisywhensamplingfromEquation(13).
Therefore,amainpracticalchallengeofpolicygradientalgorithmsistointroducemeasurestoreducethevariance
ofthegradientswhilekeepingthebiaslow[77]. Inthiscontext,awell-knownandwidelyusedtechniqueistousea
baseline[88]whensamplinganestimateoftheaction-valuefunctionQ [27]. Inthissection,weshowthatusingan
π
appropriatelychosenbaselinedoesnotbiastheestimatebutcangreatlyreducethevarianceofthesampledgradients.
Let Qˆ(s,a) be a sampled estimate of Q (s,a), assuming E(cid:2) Qˆ(s,a) (cid:3) = Q (s,a). Then, we can construct a new
π π
estimatorQˆ (s,a)bysubtractingsomebaselineb: S →R,i.e. Qˆ (s,a)=Qˆ(s,a)−b(s). Ouronlyconditiontowards
b b
6Notethatρ (s →s ,1)=0iftheepisodealreadyterminatedduetoreachingaterminalstateonanypreviousstep.
π t t*****
--- PAGE 14 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
bisthatitdoesnotdependontheactiona,thoughitcandependonthestatesandevenbearandomvariable[75]. Our
sampledestimateofthegradient∇ J(θ)becomes
θ
∇ˆ J(θ)=∇ lnπ (a|s) (cid:0) Qˆ(s,a)−b(s) (cid:1) .
θ θ θ
Inexpectationoverthepolicyπ,thisyields
E (cid:104) ∇ˆ J(θ) (cid:105) =E (cid:104) ∇ lnπ (A|S) (cid:0) Qˆ(S,A)−b(S) (cid:1)(cid:105)
π θ π θ θ
(cid:104) (cid:105) (cid:104) (cid:105)
=E ∇ lnπ (A|S)Qˆ(S,A) −E ∇ lnπ (A|S)b(S)
π θ θ π θ θ
usingthelinearityoftheexpectation. Now,weshowthatthesecondpartis0. UsingtheLeibnizintegralrule,wehave
that
(cid:104) (cid:105) (cid:90) (cid:90)
E ∇ lnπ (A|S)b(S) = dπ(s) π (a|s)∇ lnπ (a|s)b(s)dads
S∼dπθ,A∼πθ θ θ θ θ θ
s∈S a∈A
(cid:90) (cid:90)
= dπ(s)b(s) π (a|s)∇ lnπ (a|s)dads
θ θ θ
s∈S a∈A
(cid:90) (cid:90) ∇ π (a|s)
= dπ(s)b(s) π (a|s) θ θ dads
θ π (a|s)
θ
s∈S a∈A
(cid:90) (cid:90)
= dπ(s)b(s)∇ π (a|s)dads
θ θ
s∈S a∈A
(cid:90)
= dπ(s)b(s)∇ 1ds
θ
s∈S
=0
sinceπ(· | s)isaprobabilitydistributionoveractions. Thus,subtractinganaction-independentbaselinebfroman
action-valuefunctionestimatorQˆ doesindeednotaddanybiastothegradientestimate. Whileherewehaveshownthis
forabaselinewhichonlydependsonthecurrentstate,thisresultcanbeextendedtobaselineswhichdependonthe
currentandallsubsequentstates[70].
Next,weanalyzetheeffectonthevarianceofthegradientestimates. Here,weonlyprovideanapproximateexplanation,
see[27]foramorethoroughanalysiswhichderivesboundsofthetruevariance. Wecancomputethevarianceusing
Var[X]=E[X2]−E[X]2. Duetotheabove,E[X]2isindependentofthebaselineinourcase. Thisyields
argminVar (cid:104) ∇ lnπ (A|S) (cid:0) Qˆ(S,A)−b(S) (cid:1)(cid:105) =argminE (cid:104)(cid:16) ∇ lnπ (A|S) (cid:0) Qˆ(S,A)−b(S) (cid:1)(cid:17)2(cid:105)
π θ θ π θ θ
b b
(cid:18) (cid:19)
≈argmin E (cid:2)(cid:0) ∇ lnπ (A|S) (cid:1)2(cid:3) ·E (cid:2)(cid:0) Qˆ(S,A)−b(S) (cid:1)2(cid:3) ,
π θ θ π
b
where we approximated the variance by assuming independence of the two terms in the second step. Under this
approximation,thevarianceofsampledgradientscanbeminimizedbyminimizingE (cid:2)(cid:0) Qˆ(S,A)−b(S) (cid:1)2(cid:3) . Thisisa
π
commonleastsquaresproblemresultingintheoptimalchoiceofb(s)=E [Qˆ(s,A)](seeTheoremD.8). Thisresult
π
indicatesthatanappropriatelychosenbaselinecanpotentiallysignificantlyreducevarianceofthegradients. Usingthis
choiceforthebaseline,wewouldliketocomputegradientsforsampledstatesandactionsas
∇ lnπ (a|s) (cid:0) Q (s,a)−E [Q (s,A)] (cid:1) =∇ lnπ (a|s) (cid:0) Q (s,a)−V (s) (cid:1)
θ θ π A∼πθ π θ θ π π
=∇ lnπ (a|s)A (s,a).
θ θ π
Here,weusedtherelationofthevaluefunctionV toQ andthedefinitionoftheadvantagefunctionA . Despite
π π π
ourapproximations,thischoiceofabaselineturnsouttoyieldalmostthelowestpossiblevarianceofthegradients
[70]. However,notethatinpracticetheadvantagefunctionmustalsobeestimated. Learningthisestimatetypically
introducesbias[43,76].
13
--- PAGE 15 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
3.3 ImportanceSampling
Importance sampling is a technique to calculate expectations under one distribution given samples from another
[65,31,75]. Traditionally,thisisonlyneededinoff-policyRL,wherewesampletransitionsusingabehaviorpolicyβ
butwanttocalculateexpectationsoverthetargetpolicyπ. However,insomeimplementationsofon-policyalgorithms
thepolicymaybeupdatedbeforealldatageneratedbyitisprocessed. Thismakestheseimplementationsslightly
off-policyandthusimportancesamplingbecomesrelevantevenfortheoreticallyon-policyalgorithms[85]. Webuild
ourpresentationofimportancesamplingon[75],Section5.5.
Givenabehaviorpolicyβ,wewanttoestimatethevaluefunctionV ofourtargetpolicyπ. Generally,wehave
π
V (s)=E (cid:2) G |S =s (cid:3) ̸=V (s).
β β t t π
Wecancalculatetheprobabilityofatrajectory(a ,s ,a ,...,a ,s )underanypolicyπas
t t+1 t+1 T−1 T
T−1
(cid:89)
π(a |s )P(s |s ,a ).
k k k+1 k k
k=t
Now,wecandefinetheimportancesamplingratio.
Definition 3.2. (Importance Sampling Ratio) Given a target policy π, a behavior policy β and a trajectory τ =
(a ,s ,a ,...,s )generatedbyβ,theimportancesamplingratioisdefinedas
t t+1 t+1 T
(cid:81)T−1π(a
|s )P(s |s ,a )
(cid:81)T−1π(a
|s )
ρ := k=t k k k+1 k k = k=t k k .
t:T−1 (cid:81)T−1β(a
|s )P(s |s ,a )
(cid:81)T−1β(a
|s )
k=t k k k+1 k k k=t k k
LetT bethesetofpossibletrajectories. Bymultiplyingreturnsoftrajectoriesτ ∈T generatedbythebehaviorpolicy
β withtheimportancesamplingratioρweget
E (cid:2) ρ G |S =s (cid:3) =E (cid:2) ρ G(τ)|S =s (cid:3)
β t:T−1 t t β t:T−1 t
T−1
(cid:88) (cid:89)
= ρ G(τ) β(a |s )P(s |s ,a )
t:T−1 k k k+1 k k
τ∈T k=t
= (cid:88) (cid:81)T k= − t 1π(a k |s k ) G(τ) T (cid:89) −1 β(a |s )P(s |s ,a )
(cid:81)T−1β(a
|s )
k k k+1 k k
τ∈T k=t k k k=t
T−1
= (cid:88) G(τ) (cid:89) π(a k |s k ) β(a |s )P(s |s ,a )
β(a |s ) k k k+1 k k
k k
τ∈T k=t
T−1
(cid:88) (cid:89)
= G(τ) π(a |s )P(s |s ,a )
k k k+1 k k
τ∈T k=t
=E (cid:2) G |S =s (cid:3)
π t t
=V (s).
π
Theintuitionbehindthisimportancesamplingcorrectionisthat,toevaluateπ,wewanttoweighreturnsmoreheavily
thataremorelikelyunderπ thanunderβ andviceversa. Asanextensionofthederivationabove,wealsogetthe
per-decisionimportancesamplingratioρ:= π(a|s) [75].
β(a|s)
Usingimportancesampling,wecanderivethefollowingapproximatepolicygradientsofthetargetpolicyπ inan
θ
off-policysettingwithbehaviorpolicyβ:
(cid:20) (cid:21)
π (A|S)
∇ J(θ)≈ηE θ Q (S,A)∇ lnπ (A|S) .
θ S∼dβ,A∼β β(A|S) πθ θ θ
See[19]foraproof. Notethatηnowistheaverageepisodelengthunderβ.
4 PolicyGradientAlgorithms
Building on Theorem 3.1, several policy gradient algorithms have been proposed, which compute sample-based
estimates∇ˆ J(θ)oftheactualpolicygradients∇ J(θ). ThisisdonebyconstructingsurrogateobjectivesJ suchthat
θ θ ∗
14
--- PAGE 16 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
∇ˆ J(θ) = ∇ J (θ). Additionally,mostalgorithmsfocusonstabilizinglearningbyregularizingthepolicy[5]and
θ θ ∗
reducingthevarianceof∇ˆ J(θ)[77]. Inthissection,wederivethemostprominent7algorithmsbeforethancomparing
θ
theminthefinalsubsection.
4.1 REINFORCE
REINFORCE(REwardIncrement=Non-negativeFactor×OffsetReinforcement×CharacteristicEligibility)[88]is
theearliestpolicygradientalgorithm. WhilethisalgorithmprecedestheformulationofthePolicyGradientTheorem,
REINFORCEcanbeseenasastraightforwardapplicationofit. ByusingMonteCarlomethods[75]toestimateQ in
π
Equation(13),i.e. bysamplingentireepisodestocomputethesamplereturnsG = (cid:80)T γkr ,REINFORCE
t k=0 t+k+1
samplespolicygradients
∇ˆ J(θ)=G ∇ lnπ (a |s ).
θ t θ θ t t
UsingthegenericpolicygradientupdatefromEquation(3)resultsinthegradientascendupdates
θ =θ+αG ∇ lnπ (a |s )
new t θ θ t t
whereα∈(0,1]isthelearningratedeterminingthestepsizeofthegradientstepsandissetasahyperparameter. At
times,REINFORCEisextendedbysubtractingsomebaselinevaluefromG toreducevariance[88]. Thepseudocode
t
forREINFORCEispresentedinAlgorithm3.
Algorithm3REINFORCE
Require: α∈(0,1],γ ∈[0,1]
Initializeθatrandom
forallepisodesdo
Generatetrajectorys ,a ,r ,s ...,s underpolicyπ
0 0 1 1 T θ
fort=1,...,T do
G ← (cid:80)T γk−tr ▷estimateexpectedreturnQ
t k=t k π
θ ←θ+αG ∇ lnπ (a |s ) ▷updatepolicyparameters
t θ θ t t
endfor
endfor
4.2 A3C
InsteadofestimatingQ directlyviasamplingasinREINFORCE,wecanalternativelylearnsuchanestimatevia
π
functionapproximation. Algorithmsthatusethisapproachtolearnaparameterizedaction-valuefunctionQˆ orvalue
ϕ
functionVˆ (calledcritic)withparameters ϕinadditiontolearningtheparameterizedpolicy π (calledactor)are
ϕ θ
referredtoasactor-criticalgorithms[75]. Notethatinpracticetheactorandthecriticmayalsoshareparameters.
ThemostarchetypicalrepresentativeofthisclassofalgorithmsisAsynchronousAdvantageActor-Critic(A3C)[54].
A3Cbuildsontwomainideasfromwhichthealgorithm’snameoriginates. First,assuggestedbytheresultsfrom
Section3.2,A3ClearnsanestimateAˆ oftheadvantagefunctionindirectlybylearninganestimateVˆ ofthevalue
ϕ ϕ
function. Second,A3Cintroducestheconceptofusingmultipleparallelactorstointeractwiththeenvironmentto
stabilizetraining. Wewilldiscussbothideasindetailbelow. Thealgorithmsamplespolicygradients
∇ˆ J(θ)= 1 (cid:88) Aˆ (s,a)∇ lnπ (a|s),
θ |D| ϕ θ θ
s,a∈D
whereDisabatchoftransitionscollectedbytheactors. ThepseudocodeforA3CispresentedinAlgorithm4.
Intheoriginalwork[55],theadvantagefunctionisestimatedvia
k−1
Aˆ (s ,a )=
(cid:16)(cid:88)
γir +γkVˆ (s )
(cid:17)
−Vˆ (s ). (14)
ϕ t t t+i ϕ t+k ϕ t
i=0
7Asdeterminedbytheirimpactonsubsequentresearchandtheadoptionratebyusers.
15
--- PAGE 17 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Tounderstandthisestimate,observethat
A (s ,a )=Q (s ,a )−V (s )
π t t π t t π t
(cid:104) (cid:105)
=E R +γV (S )|S =s ,A =a −V (s )
π t+1 π t+1 t t t t π t
(cid:104) (cid:105)
=E R +γR +γ2V (S )|S =s ,A =a −V (s )
π t+1 t+2 π t+2 t t t t π t
.
.
.
k−1
(cid:104)(cid:88) (cid:105)
=E γiR +γkV (S )|S =s ,A =a −V (s ),
π t+i π t+k t t t t π t
i=0
foranyk ∈N,whichfollowsfromthedefinitionofthevalueandaction-valuefunctionsaswellastheirrelationship.
Samplingthisn-steptemporaldifference[75]expressionandreplacingV withourlearnedVˆ yieldsEquation(14).
π ϕ
Simultaneouslytoupdatingπ ,welearnVˆ byminimizingthemeansquarederrorloss
θ ϕ
1 (cid:88) (cid:18)(cid:16) k (cid:88) −1 γir +γkVˆ (s ) (cid:17) −Vˆ (s ) (cid:19)2
|D| t+i ϕ t+k ϕ t
D i=0
overϕviaSGD.NotethattheinnerexpressionisidenticaltotherighthandsideinEquation(14). InEquation(14),we
computethedifferencebetweentheestimatedreturnwhenchoosingactiona instates andtheestimatedreturnwhen
t t
instates ,underpolicyπrespectively. However,a issampledfromπsuchthatinexpectationthisdifferenceshould
t t
be0forthetruevaluefunctionV . Hence,weminimizethissquareddifferencetooptimizeϕbytreatingthefirstterm,
π
(cid:80)k−1γir +γkVˆ (s ),asindependentofϕ.
i=0 t+i ϕ t+k
Theuseofmultipleparallelactorsisjustifiedasfollows. DeepRLisnotoriouslyunstable,whichwasfirstresolved
byoff-policyalgorithmsusingreplaybuffersthatstoreandreusesampledtransitionsformultipleupdates[55]. As
analternative,[54]proposeusingseveralactorsπ(1),...,π(k)todecreasenoisebyaccumulatingthegradientsover
θ θ
multiple trajectories. These accumulated gradients are applied to a centrally maintained copy of θ, which is then
redistributedtoeachactor. Bydoingthisasynchronously,eachactorhasapotentiallyuniquesetofparametersatany
pointintimecomparedtotheotheractors. Thisdecreasesthecorrelationofthesampledtrajectoriesacrossactors,
whichcanfurtherstabilizelearning.
Asafinalimplementationdetail,thepolicylossfunctionofA3C,fromwhichthepolicygradientsareobtained,is
typicallyaugmentedwithanentropybonusforthepolicy. Thus,thepolicygradientsbecome
(cid:32)(cid:18)k−1 (cid:19) (cid:33)
∇ˆ J(θ)= 1 (cid:88) (cid:88) γir +γkVˆ (s )−Vˆ (s ) ∇ lnπ (a |s )+β∇ H(π (·|s )) ,
θ |D| t+i ϕ t+k ϕ t θ θ t t θ θ t
D i=0
whereH istheentropy(seeDefinitionD.4)andtheentropycoefficientβ isahyperparameter. Thisentropybonus,
firstproposedby[89],regularizesthepolicysuchthatitdoesnotprematurelyconvergestoasuboptimalpolicy. By
rewardingentropy,thepolicyisencouragedtospreadprobabilitymassoveractionswhichimprovesexploration[54].
4.3 TRPO
ExcessivelylargechangesinthepolicycanresultininstabilitiesduringthetrainingofRLalgorithms. Evensmall
changesinpolicyparametersθ canleadtosignificantchangesintheresultingpolicyanditsperformance. Hence,
smallstepsizesduringgradientascentcannotfullyremedythisproblemandwouldimpairthesampleefficiencyof
thealgorithm[3]. TrustRegionPolicyOptimization(TRPO)[69]mitigatestheseissuesbyimposingatrustregion
constraintontheKullback-Leibler(KL)divergence(seeDefinitionD.5)betweenconsecutivepolicies. Inaddition,
TRPO uses an off-policy correction through importance sampling as discussed in Section 3.3 to account for the
interleavedoptimizationandcollectionoftransitions.
TRPOsamplesgradients
∇ˆ J(θ)= 1 (cid:88) Aˆ (s,a)∇ π θ (a|s)
θ |D| ϕ θπ (a|s)
old
s,a∈D
andpostprocessesthemasdetailedbelowtosolvetheapproximatetrustregionoptimizationproblem
(cid:18) (cid:20) (cid:21)(cid:19)
π (A|S)
max J (θ)=E Aˆ (S,A) θ
θ TRPO S∼dπold,A∼πold ϕ π old (A|S)
subjectto E S∼dπold (cid:2) D KL (π old (·|S)∥π θ (·|S)) (cid:3) ≤δ
16
--- PAGE 18 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Algorithm4A3C
Require: n∈N,α∈(0,1],γ ∈[0,1],t ∈N,T ∈N
MAX MAX
Initializeθandϕatrandom
fori=1,...,ndo ▷inparallel
whileT ≤T do
MAX
dθ ←0, dϕ←0 ▷resetgradients
θ(i) ←θ, ϕ(i) ←ϕ ▷synchronizeparametersonactors
s ∼p ▷samplestartstate
t 0
t ←t
start
whiles notterminalandt−t ≤t do
t start MAX
a ∼π ▷sampleaction
t θ(i)
s ,r ∼P(s ,a ) ▷samplenextstateandreward
t+1 t+1 t t
t←t+1, T ←T +1
endwhile
(cid:26)
0 ifs isterminal
R← t ▷bootstrapifnecessary
V (s ) else
ϕ(i) t
forj =t−1,...,t do
start
R←r +γR
j
A=R−V (s )
ϕ(i) j
dθ ←dθ+∇ lnπ (a |s )A ▷accumulategradients
θ(i) θ(i) j j
dϕ←dϕ+∇ (R−V (s ))2 ▷accumulategradients
ϕ(i) ϕ(i) j
endfor
updateθandϕusingdθanddϕviagradientascent/descent
endwhile
endfor
where π = π is the previous policy and θ the corresponding parameters. This optimization problem is an
old θold old
approximationtoanobjectivewithconvergenceguarantees,whichwewillshowinthefollowing.Westartbypresenting
[69]’smaintheoreticalresult. ConsidertheobjectiveofmaximizingtheexpectedreturnE (cid:2) G (cid:3) underpolicyπ,
S0∼p0,π 0
whichwedenoteasη(π)here. LetL bethefollowinglocalapproximationofη:
π
(cid:90) (cid:90)
L (π˜)=η(π)+ dπ(s) π˜(a|s)A (s,a)dads,
π π
s∈S a∈A
withL (π )=η(π )and∇ L (π )| =∇ η(π )| [38]. BasedonthetotalvariationdivergenceD (see
πθ θ θ θ πθ0 θ θ=θ0 θ θ θ=θ0 TV
DefinitionD.6),wedefine
Dmax(π,π˜):=maxD (π(·|s)∥π˜(·|s)).
TV TV
s∈S
Then,wehave[69]:
Theorem4.1. Letα=Dmax(π ,π ),then
TV old new
4εγ
η(π )≥L (π )− α2, (15)
new πold new (1−γ)2
whereε=max |A (s,a)|.
s∈S,a∈A π
Seetheappendixin[69]foraproof. ByusingtherelationshipbetweentotalvariationdivergenceandKLdivergence
D (π∥π˜)2 ≤ D (π∥π˜)[58]andsettingDmax(π,π˜) := max D (π(· | s)∥π˜(· | s))andC = 4εγ , we
TV KL KL s∈S KL (1−γ)2
derivethefollowinglowerboundfortheobjectivefromEquation(15):
η(π )≥L (π )−CDmax(π ,π ) (16)
new πold new KL old new
Iteratively maximizing the right-hand side yields a sequence of policies π ,π ,π ,... with the monotonic im-
i i+1 i+2
provementguaranteeη(π )≤η(π )≤η(π )≤.... Thisisbecausewehaveequalityin(16)forπ =π and
i i+1 i+2 new old
hence
(cid:16) (cid:17) (cid:16) (cid:17)
η(π )−η(π )≥ L (π )−CDmax(π ,π ) − L (π )−CDmax(π ,π ) ,
i+1 i πi i+1 KL i i+1 πi i KL i i
whichisnon-negativeaswemaximizeoverπeachiteration. Thus,wecouldconstructaMinorization-Maximization-
typealgorithm[37]whichmaximizestheright-handsideofInequality(16)ateachiterationandistherebyguaranteed
toconvergetoanoptimumastheobjectiveisbounded.
17
--- PAGE 19 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Suchanalgorithmwouldbeimpracticalasitrequiresevaluatingtheadvantagefunctionateverypointinthestate-action
productspaceS×AandtheKLpenaltyateverypointinthestatespaceS. Hence,[69]applyseveralapproximations
totheobjectivestemmingfromInequality(16). WereplacetheKLpenalty,whichwouldyieldrestrictivelysmallstep
sizesgivenbyC,byatrustregionconstraint:
max L (π )
θ
πold θ
subjectto Dmax(π ,π )≤δ.
KL old θ
ToavoidcomputingDmax,weusetheaverageKLdivergence
KL
D¯
K
πo
L
ld(π∥π˜):=E
S∼dπold
(cid:2) D
KL
(π(·|S)∥π˜(·|S)) (cid:3)
betweenpoliciesasheuristicconstraint,whichwecansample.Further,werewritethesurrogateobjectivemax L (π )
θ πold θ
asanexpectationovertheoldpolicyπ viaimportancesampling. Notethatη(π )isaconstantw.r.tθ:
old old
(cid:18) (cid:90) (cid:90) (cid:19)
argmaxL
πold
(π
θ
)=argmax η(π
old
)+ dπold(s) π
θ
(a|s)A
πold
(s,a)dads
θ θ
s∈S a∈A
(cid:90) (cid:90)
=argmax dπold(s) π
θ
(a|s)A
πold
(s,a)dads
θ
s∈S a∈A
(cid:90) (cid:90) π (a|s)
=argmax dπold(s)
π
old
(a|s)
π
θ
(a|s)A
πold
(s,a)dads
θ old
s∈S a∈A
(cid:20) (cid:21)
π (A|S)
=argmaxE θ A (S,A) .
S∼dπold,A∼πold π (A|S) πold
θ old
Usingthesemodifications,wearenowleftwithsolvingthetrustregionproblem
(cid:20) (cid:21)
π (A|S)
max E θ A (S,A)
θ S∼dπold,A∼πold π old (A|S) πold (17)
(cid:104) (cid:105)
subjectto E S∼dπold D KL (π old (·|S)∥π θ (·|S)) ≤δ.
To approximately solve this constrained problem, [69] use backtracking line search, where the search direc-
tion is computed by Taylor-expanding (see Theorem D.12) the objective function and the constraint. Let g =
(cid:104) (cid:105)
∇ θ E S∼dπold,A∼πold π π o θ ld ( ( A A | | S S ) ) A πold (S,A) . ApproximatingL πold (π θ )tofirstorderaroundθ old yields
L (π )≈gT(θ−θ ),
πold θ old
whereweagainignoredtheconstantη(π ). Thequadraticapproximationoftheconstraintatθ is
old old
1
D¯πold(π∥π˜)≈ (θ−θ )TH(θ−θ ),
KL 2 old old
whereH istheFisherinformationmatrix,whichisestimatedvia
Hˆ = 1 (cid:88) ∂2 D (π (·|s)∥π(·∥s)),
i,j |D| ∂θ ∂θ KL old
i j
s∈D
albeit the full matrix is not required. We solve the resulting approximate optimization problem analytically using
Lagrangiandualitymethods[12]leadingto
(cid:115)
2δ
θ =θ + Hˆ−1g.
new old gTHˆ−1g
However,duetotheTaylorapproximations,thissolutionmaynotsatisfytheoriginaltrustregionconstraintormaynot
improvethesurrogateobjectiveofProblem(17). Therefore,TRPOemploysbacktrackinglinesearchalongthesearch
directionH−1gwithsearchparameterβ ∈(0,1):
(cid:115)
2δ
θ =θ +βm H−1g.
new old gTH−1g
18
--- PAGE 20 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Algorithm5TRPO
Require: δ ∈R,b∈(0,1),K ∈N,α∈(0,1],U ∈N,T ∈N
Initializeθandϕatrandom
t←0
whilet≤T do
fori=1,...,U do
a∼π ▷sampleaction
θ
β(a|s)←π (a|s)
θ
s,r ∼P(s,a) ▷samplenextstateandreward
t←t+1
Store(a,s,r,β(a|s))inD
endfor
forallepochsdo
ComputereturnsRandadvantagesA
g ← 1 (cid:80) ∇ πθ(a|s)A
|D| D θ β(a|s)
ComputeHˆ astheHessianofthesampleaverageKL-divergence
Computed≈Hˆ−1gviaconjugategradientalgorithm
m←0
repeat
(cid:113)
θ ←θ +bm 2δ d
old dTHˆd
m←m+1
until(samplelossimprovesandKLconstraintsatisfied)orm>K
dϕ← 1 (cid:80) ∇ (R−V (s))2
|D| D ϕ ϕ
Updateϕusingdϕviagradientdescent
endfor
endwhile
Wechoosetheexponentmasthesmallestnon-negativeintegersuchthatthetrustregionconstraintissatisfiedand
thesurrogateobjectiveimproves. WecircumventthecomputationallyexpensivematrixinversionofH forthesearch
directiond≈H−1gbycomputingdviatheconjugategradientalgorithm[30]. Tofurtherreducethecomputational
costs,theFisher-vectorproductsinthisprocesscanalsobeonlycalculatedonasubsetofthedatasetDofsampled
transitions.
[69] do not specify an advantage estimator to be used in TRPO. The algorithm is commonly used with either the
estimatorusedbyA3Cortheonewhichwepresentinthenextsubsection. TRPOistypicallyusedwithmultipleparallel
actorsasA3C.ThepseudocodeforTRPOispresentedinAlgorithm5. Weremarkthatwhilebeingapolicy-based
algorithm,TRPOdoesnotstrictlyadheretoDefinition2.2asitsolvesaconstrainedoptimizationproblemvialine
search. Yet,itdoescomputegradientsofitsobjectivefunctionw.r.t. thepolicyparametersandthereforewetreatitasa
policygradientalgorithm.
4.4 PPO
GiventhecomplexityofTRPO,ProximalPolicyOptimization(PPO)[71]isdesignedtoenforcecomparableconstraints
onthedivergencebetweenconsecutivepoliciesduringthelearningprocesswhilesimplifyingthealgorithmtonot
requiresecond-ordermethods. Thisisachievedbyheuristicallyflatteningthegradientsoutsideofanapproximatetrust
regionaroundtheoldpolicy. Inaddition,PPOusesanovelmethodtolearnanestimateoftheadvantagefunction.
Letr (a|s)= πθ(a|s). Then,PPOusesthefollowingestimateofthepolicygradients:
θ πold(a|s)
∇ˆ J(θ)= 1 (cid:88) Aˆ (s,a)∇ min (cid:26) r (a|s),clip (cid:16) r (a|s),1−ε,1+ε (cid:17)(cid:27) . (18)
θ |D| ϕ θ θ θ
s,a∈D
Here,theclip-functionclip: R×R×R→Risdefinedby

a ifx<a,

clip(x,a,b)= x ifa≤x≤b,
b ifb<x.
andisappliedelement-wisetor . εisahyperparameter.
θ
19
--- PAGE 21 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
0 1 1+ε
r
θ
)θ(
J
OPP
0 1+ε 1
r
θ
(a)A>0
)θ(
J
OPP
(b)A<0
Figure3: IllustrationoftheconservativeclippingofPPO’sobjectivefunction,whichisshownasafunctionoftheratio
r forasingletransitiondependingonwhethertheadvantagesarepositive(a)ornegative(b). Replicatedfrom[71].
θ
Thisclippedobjectiveconservativelyremovestheincentiveformovingthenewpolicytofarawayfromtheoldone.
Intuitively,thiscanbeseenasfollows.Wedistinguishtwocases:positiveandnegativeestimatedadvantagesAˆ(s,a),i.e.
whetheractionaisgoodorbad. IfAˆ(s,a)>0,thesurrogateobjectiveJ (θ)increaseswhenabecomesmorelikely.
PPO
Similarly,ifAˆ(s,a)<0,J (θ)increaseswhenabecomeslesslikely. Hence,wewanttoadjustthepolicyparameters
PPO
θaccordingly. However,byclippingthepolicyratior ,thispositiveeffectontheobjectivefunctiondisappearsonce
θ
wemoveoutsidethecliprange. Thisclippingprocessisconservativeasweonlyclipiftheobjectivefunctionwould
improve. IfthepolicyischangedintheoppositedirectionsuchthatJ (θ)decreases,r isnotclippedduetotaking
PPO θ
theminimuminEquation(18). Figure3illustratesthisexplanation. ThepseudocodeforPPOispresentedinAlgorithm
6.
Algorithm6PPO
Require: ε∈R,α∈(0,1],γ ∈[0,1],λ∈[0,1],U ∈N,T ∈N
Initializeθandϕatrandom
t←0
whilet≤T do
fori=1,...,U do
a∼π ▷sampleaction
θ
β(a|s)←π (a|s)
θ
s,r ∼P(s,a) ▷samplenextstateandreward
t←t+1
Store(a,s,r,β(a|s))inD
endfor
forallepochsdo
R,A←computeGAE(v,r,λ,γ) ▷Computereturnsandadvantages
dθ ←∇ 1 (cid:80) min (cid:0)π(a|s),clip(π(a|s),1−ε,1+ε) (cid:1) A
θ|D| D β(a|s) β(a|s)
dϕ←∇ 1 (cid:80) (R−V (s))2
ϕ|D| D ϕ
updateθandϕusingdθanddϕviagradientascent/descent
endfor
endwhile
TocomputetheestimateAˆ oftheadvantagefunction, PPOusesgeneralizedadvantageestimation(GAE)[70]to
ϕ
furtherreducethevarianceofgradients. GAEcomputestheestimatedadvantageas
T−1
Aˆ (s ,a )= (cid:88) (γλ)i−tδ , (19)
ϕ t t i
i=t
20
--- PAGE 22 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
whereδ =r +γVˆ (s )−Vˆ (s ). ThevaluefunctionestimateVˆ islearnedbyminimizing
i i ϕ i+1 ϕ i ϕ
1 (cid:88)(cid:16)(cid:0) Aˆ (s,a)+Vˆ (s) (cid:1) −Vˆ (s) (cid:17)2 ,
|D| ϕ ϕ ϕ
D
wherethefirsttermistreatedasindependentofϕ. GAErelatestotheideaofeligibilitytraces[74]touseboththe
sampledrewardsandthecurrentvaluefunctionestimateoneverytimestep. Bycomputingsuchanexponentially
weightedestimator,GAEreducesthevarianceofthepolicygradientsatthecostofintroducingaslightbiastothe
valuefunctionestimate[70]. Thehyperparametersγ andλbothadjustthisbias-variancetradeoff. γ doessobyscaling
the value function estimate Vˆ whereas λ controls the dependence on delayed rewards. Note that GAE is a strict
generalizationofA3C’sadvantageestimateasEquation(19)reducestoEquation(14)whenλ=1. Thepseudocode
forGAEispresentedinAlgorithm7.
Algorithm7GAE
Require: γ ∈[0,1],λ∈[0,1]
Require: rewards(r )t+n,values(v )t+n+1
k k=t k t=k
A ,...,A ←0
t t+n
x←0
fori=t+n,...,tdo
iftransitionwasterminalthen
ω ←1
else
ω ←0
endif
δ ←r +γ·v ·(1−ω)−v
i i+1 i
x←δ+γ·λ·(1−ω)·x
A ←x
i
endfor
fori=t,...,t+ndo
R ←A +v
i i i
endfor
Beyondthesemaininnovations,PPOusesseveralimplementationaldetailstoimprovelearning. PPOconductsmultiple
update epochs for each batch of data such that several gradient descent steps are based on the same transitions to
increasesampleefficiencyandspeeduplearning. Moreover,PPOcommonlyaugmentsitssurrogateobjectivewithan
entropybonusH(π (· | s))andusesmultipleactorssimilarlytoA3C.Lastly,wenotethatfurtheralgorithmshave
θ
beenproposedasmodificationsofPPO,e.g. PhasicPolicyGradients[16]andRobustPolicyOptimization[61],which
wewillnotdiscussfurtherastheyonlymodifyminordetails.
4.5 V-MPO
Inthepreviousalgorithms,welearnapolicyfromthecontrolperspectivebyselectingactionstomaximizeexpected
rewards. Inthissubsection,weconsideranalternativeformulationofRLproblems,whichcaststhemasprobabilistic
inferenceproblemsofestimatingposteriorpoliciesthatareconsistentwithadesiredoutcome[1]. Thisproblemis
thensolvedviaExpectationMaximization(EM)[20]. Thisprocedurewasfirstproposedintheoff-policyalgorithm
Maximuma-posterioriPolicyOptimization(MPO)[2,1]. Here,wediscussitson-policyvariantV-MPO[73],where
the"V"inthenamereferstolearningthevaluefunctionV insteadofQ asinMPO.
π π
ThemainideaofV-MPOistofindamaximumaposterioriestimateofthepolicybysequentiallyfindingatightlower
boundontheposteriorandthenmaximizingthislowerbound. Thisproblemcanbetransformedintotheobjective
function
J (θ,η,ν)=L (θ)+L (η)+L (θ,ν),
V-MPO π η ν
whereL isthepolicyloss
π
exp (cid:16) Aˆ ϕ(s,a) (cid:17)
(cid:88) η
L (θ)=− lnπ (a|s), (20)
π (cid:80)
exp
(cid:16) Aˆ ϕ(s′,a′) (cid:17) θ
a,s∈D˜ a′,s′∈D˜ η
L isthetemperatureloss
η
L (η)=ηε +ηln (cid:34) 1 (cid:88) exp (cid:18) Aˆ ϕ (s,a) (cid:19)(cid:35) (21)
η η |D˜| η
a,s∈D˜
21
--- PAGE 23 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
andL isthetrust-regionloss
ν
1 (cid:88) (cid:18) (cid:18) (cid:104)(cid:104) (cid:105)(cid:105)(cid:19) (cid:2)(cid:2) (cid:3)(cid:3) (cid:0) (cid:1) (cid:19)
L ν (θ,ν)= |D| ν ε ν −sg D KL (π old (·|s)∥π θ (·|s)) +sg ν D KL π old (·|s)∥π θ (·|s) . (22)
s∈D
Here,sg[[·]]isastop-gradientoperator,meaningitsargumentsaretreatedasconstantswhencomputinggradients,ηisa
learnabletemperatureparameter,ν isalearnableKL-penaltyparameter,ε andε arehyperparameters,Disabatch
ν η
oftransitionsandD˜ ⊂Disthehalfofthesetransitionswiththelargestadvantages. Wewillprovideasketchofhow
toderivethisobjectivefunctioninthefollowing. WerefertheinterestedreadertoAppendixAppendixCforamore
detailedderivation.
Letp
θ
(s,a)=π
θ
(a|s)dπθ(s)denotethejointstate-actiondistributionunderpolicyπ
θ
conditionalontheparameters
θ. LetI beabinaryrandomvariablewhethertheupdatedpolicyπ isanimprovementovertheoldpolicyπ ,i.e.
θ old
I =1ifitisanimprovement. Weassumetheconditionalprobabilityofπ beinganimprovementgivenastatesand
θ
anactionaisproportionaltothefollowingexpression
(cid:16)A (s,a)(cid:17)
p (I =1|s,a)∝exp πold . (23)
θ η
GiventhedesiredoutcomeI =1,weseektheposteriordistributionconditionedonthisevent. Specifically,weseekthe
maximumaposterioriestimate
θ∗ =argmax (cid:2) p (I =1)ρ(θ) (cid:3)
θ
θ
(24)
(cid:2) (cid:3)
=argmax lnp (I =1)+lnρ(θ) ,
θ
θ
whereρissomepriordistribution. lnp (I =1)canberewrittenas
θ
(cid:20) (cid:21)
lnp (I =1)=E ln p θ (I =1,S,A) +D (cid:0) ψ∥p (·,·|I =1) (cid:1) (25)
θ S,A∼ψ ψ(S,A) KL θ
forsomedistributionψoverS×A. Observethat,sincetheKLdivergenceisnon-negative,thefirsttermisalower
boundforlnp (I = 1). AkintoEMalgorithms,V-MPOnowiteratesbychoosingthevariationaldistributionψ in
θ
theexpectation(E)steptominimizetheKLdivergenceinEquation(25)tomakethelowerboundastightaspossible.
Inthemaximization(M)step,wemaximizethislowerboundandthepriorlnρ(θ)toobtainanewestimateofθ∗via
Equation(24).
First,weconsidertheE-step. Undertheproportionalityassumption(23),weturntheproblemoffindingavariational
distributionψtominimizeD (ψ∥p (·,·|I =1))intoanoptimizationproblemoverthetemperatureη. Thisis
KL θold
formulatedasaconstrainedproblemsubjecttoaboundontheKLdivergencebetweenψandthepreviousstate-action
distributionp whileensuringthatψisastate-actiondistribution. Toenableoptimizingηviagradientdescent,we
θold
transformthisconstrainedproblemintoanunconstrainedproblemviaLagrangianrelaxation,whichemitsboththe
formofthevariationaldistribution
p (s,a)p (I =1|s,a)
ψ(s,a)= (cid:82) (cid:82) θold θold
p (s,a)p (I =1|s,a)dads
s∈S a∈A θold θold
andthetemperatureloss(21)
(cid:18)(cid:90) (cid:90) (cid:16)A (s,a)(cid:17) (cid:19)
L (η)=ηε +ηln exp πold dads .
η η η
s∈Sa∈A
[73]findthatusingonlythehighest50%ofadvantagesperbatchwhensamplingtheseexpressions,i.e. replacingD
withD˜,substantiallyimprovesthealgorithm. TheadvantagefunctionA isestimatedbyAˆ ,whichislearnedasin
π ϕ
A3C.
Then,intheM-Stepwesolvethemaximumaposteriorestimationproblem(24)overthepolicyparametersθforthe
constructedvariationaldistributionψ(s,a)andthetherebyimpliedlowerbound. Thislowerbound,i.e. thefirsttermin
Equation(25),becomestheweightedmaximumlikelihoodpolicyloss(20)
(cid:90) (cid:90)
L (θ)=− ψ(s,a)lnπ (a|s)dads
π θ
s∈Sa∈A
22
--- PAGE 24 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
afterdroppingtermsindependentofθ. ThislossiscomputedoverthesamereducedbatchD˜ asthetemperatureloss,
effectivelyassigningout-of-sampletransitionsaweightofzero. Simultaneously,wewanttomaximizethepriorρ(θ)
accordingtothemaximizationproblem(24). V-MPOfollowsTRPOandPPOtochooseapriorsuchthatthenewpolicy
iskeptclosetothepreviousone,i.e.
ρ(θ)=−νE (cid:2) D (cid:0) π (·|S)∥π (·|S) (cid:1)(cid:3) ,
S∼dπold KL old θ
withlearnableparameterν. However,optimizingtheresultingsample-basedmaximumlikelihoodobjectivedirectly
tendstoresultinoverfitting. Hence,asequenceoftransformationsisapplied. First,theprioristransformedintoa
hardconstraintontheKLdivergencewhenoptimizingthepolicyloss. Toemploygradient-basedoptimization,we
useLagrangianrelaxationtotransformthisconstrainedoptimizationproblembackintoanunconstrainedproblem
anduseacoordinate-descentstrategytosimultaneouslyoptimizeforθandν. Thiscanequivalentlybewrittenviathe
stop-gradientoperatoryieldingthetrust-regionloss(22).
Algorithm8V-MPO
Require: η ∈R,ν ∈R,ε ∈R,ε ∈R,U ∈N,T ∈N
η ν
Initializeθandϕatrandom
t←0
whilet≤T do
fori=1,...,U do
a∼π ▷sampleaction
θ
β(a|s)←π (a|s)
θ
s,r ∼P(s,a) ▷samplenextstateandreward
t←t+1
Store(a,s,r,β(a|s))inD
endfor
forallepochsdo
ComputereturnsRandadvantagesA
ComputeD˜
L ← 1 (cid:80) ν(ε −sg(D (π ∥π )))+sg(ν)D (π ∥π ) ▷KLloss
ν |D| D ν KL old θ KL old θ
L ←− 1 (cid:80) lnπ (a|s)ψ(s,a) ▷Policyloss
π |D˜| D˜ θ
L ←ηε +ηln( 1 (cid:80) expA) ▷Temperatureloss
η η |D˜| D˜ η
dθ ←∇ (L +L ),dη ← ∂ L ,dν ← ∂ L ▷Computegradients
θ π ν ∂η η ∂ν ν
dϕ← 1 (cid:80) ∇ (R−V (s))2
|D| D ϕ ϕ
updateθ,η,ν andϕusingdθ,dη,dν anddϕviagradientascent/descent
endfor
endwhile
Thelearnableparametersηandν areLagrangianmultipliersandhencemustbepositive. Weenforcethisbyprojecting
thecomputedvaluestosmallpositivevaluesη andν respectivelyifnecessary. ThepseudocodeforV-MPOis
min min
depictedinAlgorithm8. Asimplementationaldetails,V-MPOtypicallyusesdecoupledKLconstraintsforthemean
andcovarianceofthepolicyincontinuousactionspacesfollowing[2]. Thisenablesbetterexplorationwithoutmoving
thepolicymeanaswellasfastlearningbyrapidlychangingthemeanwithoutresultinginacollapseofthepolicydue
tovanishingstandarddeviations. Inaddition,V-MPOcanbeusedwithanoff-policycorrectionviaanimportance
samplingratiosimilarlytoTRPOandPPOandusesmultipleactorsfollowingA3C.
4.6 ComparingDesignChoicesinPolicyGradientAlgorithms
Havingoutlinedthemainon-policypolicygradientalgorithms,wewanttoshortlycomparethemtocharacterizethe
maindesignchoicesinconstructingsuchalgorithms.
Thepredominantdifferencesacrosspolicygradientalgorithmslieintheestimators∇ˆ J(θ)ofthepolicygradients. We
θ
summarizetheseestimatesinTable18. Thealgorithmscanbedistinguishedalongseveraldimensionswithrespects
tothegradients. First,theyusedifferentvariancereductiontechniques,whichareespeciallyreflectedinhowQ in
π
thepolicygradientformula(13)isestimated. Second, variouspolicyregularizationstrategiesareused. Third, the
algorithmsemployfurtherlower-leveldetailstostabilizelearning. Wewilldiscusseachofthesedimensionsinthe
following.
8ForV-MPO,wefocusonthepolicyloss,thusignoringthegradientoftheKLlossL w.r.t.thepolicyparametersθhere.
ν
23
--- PAGE 25 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Algorithm Gradientestimator
REINFORCE G∇ lnπ (a|s)
θ θ
A3C 1 (cid:80) Aˆ∇ lnπ (a|s)
|D| D θ θ
TRPO 1 (cid:80) Aˆ∇ πθ(a|s)
|D| D θπold(a|s)
(cid:18) (cid:16) (cid:17)(cid:19)
PPO 1 (cid:80) Aˆ∇ min πθ(a|s),clip πθ(a|s),1−ε,1+ε
|D| D θ πold(a|s) πold(a|s)
V-MPO (cid:80) D˜ex 1 p (cid:0) A
η
ˆ (cid:1) (cid:80) D˜ exp (cid:0)A η ˆ(cid:1) ∇ θ lnπ θ (a|s)
Table1: Policygradientestimatesusedbyvariouspolicygradientalgorithms.
Reducingvarianceisimportanttostabilizelearningandspeedupconvergence[77]. However,whilehighvariance
meansalgorithmsrequiremoresamplestoconverge,biasintheestimatesisnotresolvableevenwithinfinitesamples
[70]. Allcontemporarypolicygradientalgorithms,i.e. allpresentedalgorithmsexceptREINFORCE,makeuseof
baselinestoreducevarianceasdiscussedinSection3.2whenapproximatingtheunknownQ . WhileREINFORCE
π
samplesreturnsasanunbiasedbuthigh-varianceestimate[75],theotheralgorithmslearnavaluefunctionVˆ toestimate
theadvantagefunctionAˆ. Notably,thisreducesvarianceatthecostofintroducingbias[43,76,70]. Furtherdifferences
arisefromhowadvantagesareestimated,albeitthesestrategiescanbeeasilytransferredbetweenalgorithms. PPO
usesGAEtoestimateadvantages,whichgeneralizesthen-steptemporaldifferenceestimatesusedinA3C,TRPO
andV-MPO.Inaddition,V-MPOscalestheadvantagesviathelearnedtemperatureηandonlyusesthetop50%of
advantagesperbatch.
Tostabilizelearningbeyondvariancereduction,severalregularizationtechniquesareproposedbyTRPO,PPOand
V-MPOtolimitthechangeinpoliciesacrossiterations. TRPOimposesaconstraintontheKLdivergencebetween
thenewlylearnedandthepreviouspolicy. Thus, thepolicygradientsarenotdirectlyappliedtoupdatethepolicy
parameters θ but instead they are postprocessed to yield an approximate solution to this constrained optimization
problem. Thiscomesatthecostofalgorithmiccomplexityhowever. Whereastheotheralgorithmsdirectlycomputethe
estimatedpolicygradientsviaautomaticdifferentiation[86,53],TRPOrequirestheestimationofahessianandthe
applicationoftheconjugategradientalgorithmfollowedbyalinesearch. PPOavoidssuchcomplexitybyintroducinga
heuristic,whichboundstheprobabilityratio πθ(a|s),intoitsobjectivefunction. Byconservativelyclippingthisratio,
πold(a|s)
largepolicychangesinducedbyoverfittingtheadvantagefunctionareprevented. ThisalsoenablesPPOtoconduct
multipleupdatesonthesamedatatoacceleratelearning[71]. V-MPOtoolimitstheKLdivergenceacrosspolicies.
ThepriordistributionisselectedsuchthatV-MPOarrivesatasimilaroptimizationproblemwithapenaltyontheKL
divergenceasTRPO.FollowingTRPO,V-MPOtransformsthisintoaconstrainedoptimizationproblem,albeitnow
withthegoalofautomaticallytuningthepenaltyparameterbyapplyingcoordinate-descenttotheLagrangianrelaxation
ofthisconstrainedoptimizationproblem.
Lastly,wepointoutthatdifferentlower-leveldetailsareemployedbythediscussedalgorithm. ExceptforREINFORCE,
allalgorithmsuseseveralactors,whicharepotentiallyupdatedasynchronously,andaveragegradientsoverbatchesof
transitionstofurtherreducetheirvariance. Here,V-MPOslightlydivergesfromtheotheralgorithmsasitcomputesa
weightedaveragebasedontheadvantagesofthetransitions,i.e. withweights
exp
(cid:16)Aπθold (s,a)(cid:17)
η
.
(cid:80)
a,s∈D˜ exp
(cid:16)Aπθol
η d
(s,a)(cid:17)
A3CandPPOcommonlyuseanentropybonustopreventprematureconvergencetoasuboptimalpolicybyincentivizing
ahigherstandarddeviationoftheGaussianoutputbythepolicy. WeobservethatV-MPOdoesnotuseanentropybonus
butachievesacomparableeffectincontinuousactionspacesbyconstrainingthepolicymeanandstandarddeviation
separately. Lastly,TRPOandPPOincludeanimportancesamplingcorrectiontocompensatefortheslightoff-policy
natureofthealgorithmsinducedbyusingmultipleasynchronousworkers. Thisisalsomentionedasanoptionfor
V-MPO[73].
24
--- PAGE 26 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
5 ConvergenceResults
In this section, we discuss convergence results for policy gradient algorithms from literature. First, we present an
overviewofdifferentconvergenceproofsinSection5.1. Then,wethoroughlypresentoneselectedresultinSection5.2.
5.1 LiteratureOverview
Severalconvergenceresultshavebeenproposedforpolicygradientalgorithms. Theydifferalongvariousdimensions:
thespecificalgorithmscovered,theshownstrengthofconvergence,theproblemsettingsandtheemployedproving
techniques. Thefollowingoverviewofconvergenceresultsisnotintendedtobecompletebutrathershallshowcase
thesedifferences.
As previously discussed, REINFORCE uses an unbiased estimator of the policy gradients, which in expectation
thereforepointinthedirectionofthetruegradients. Hence,undercommonstochasticapproximationassumptions
towardsthestepsizes,REINFORCEcanbeshowntoconvergetoalocallyoptimalpolicy[75]. Inthegeneralform
ofpolicygradientalgorithms,agnostictothespecificestimatorofQ ,showingconvergenceismorecomplexasthe
π
estimatedgradientsaretypicallybiasedwhenusingalearnedvaluefunction[43,76]. [4]and[11]considerthesimplest
casewherestateandactionspacesS andAarefiniteandnofunctionapproximationisused,i.e. thepolicyusesa
tabularparameterizationwithoneparameterforeachstate-actioncombination. Usingthattheexactpolicygradientscan
becalculatedinthiscase,bothstudiesshowtheglobalconvergencetoanoptimalpolicywithalinearconvergencerate.
[76]and[90]generalizetheseresultstosettingswithfunctionapproximation,albeitunderimpracticalconditionsonthe
approximators,whicharerequiredtobelinearintheirinputs. Theextensiontofunctionapproximationcomesatthe
costofonlybeingabletoprooflocalconvergenceusingstochasticapproximationandtheSupermartingaleConvergence
Theorem[64].
Finally,someconvergenceresultsexistforthespecificalgorithmssuchasTRPOandPPO.TRPOisbasedonTheorem
4.1,whichcomeswithmonotonicimprovementguarantees. However,TRPOisonlyanapproximationtothealgorithm
stemmingfromTheorem4.1,sothatnosuchguaranteeholdsforTRPOinpractice. WefurtherremarkthatPPOis
similarlyintendedasanheuristicofthistheoreticalalgorithm[71]. Nonetheless,effortshavebeenmadetoproofthe
convergenceofthesepracticalalgorithms. [51]showthataslightlymodifiedversionofPPOconvergestoaglobally
optimalpolicyatasublinearrateunderspecificassumptions. Inparticular,theyrequireanoverparameterizedneural
network as the function approximator such that they can use infinite-dimensional mirror-descent [8] to proof the
convergence. [34]provideaproofusingtwotime-scalestochasticapproximation[39]thatPPOconvergestoalocally
optimalpolicyundermorerealisticassumptionsakintotypicallearningscenarios.
5.2 MirrorLearning
Inthissubsection,wefocusontheconvergenceproofprovidedby[46]. Whileprimarilyoftheoreticalinterest,we
choosetodiscussthisparticularresultasitisagnostictotheselectedalgorithmandparameterizationandcanhenceby
appliedtoarangeofpolicygradientalgorithms. [46]introduceaframeworkcalledmirrorlearning,whichcomeswith
globalconvergenceguaranteesforallpolicygradientalgorithmsthatadheretoaspecificform. Inthefollowing,we
follow[46]inderivingtheirresults. Westartbygivingsomedefinitions,basedonwhichwethenpresentthegeneral
formofmirrorlearningupdates. Weshowthatthediscussedalgorithmslargelyadheretothisform. Finally,weproof
thatthisimpliestheconvergencetoanoptimalpolicy.
5.2.1 FundamentalsofMirrorLearning
Fromhereonwards,wedonotexplicitlywritedownthepolicyparameters,i.e. weomitthesubscriptθwhendescribing
apolicyπ. [46]definethedriftDandtheneighborhoodoperatorN asfollows.
Definition5.1. (Drift)Adriftfunctional
D: Π×S →{D (·|s): ∆(A)→R}
π
isamapwhichsatisfiesthefollowingconditionsforalls∈S andπ,π¯ ∈Π:
1. D (π¯ |s)≥D (π |s)=0, (non-negativity)
π π
2. D (π¯ | s)haszerogradientwithrespectstoπ¯(· | s)atπ¯(· | s) = π(· | s),morepreciselyallitsGâteaux
π
derivatives9arezero, (zerogradient)
(cid:0) (cid:1)
whereweusedD π¯(·|s)|s :=D (π¯ |s).
π π
Foranystatedistributionνπ¯ ∈∆(S),thatcandependonbothπ¯ andπ,thedriftfromπ¯ toπisgivenby
π
Dν(π¯):=E (cid:2) D (π¯ |s) (cid:3) .
π s∼ν π π¯ π
9SeeDefinitionD.16.
25
--- PAGE 27 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Werequireνπ¯ tobesuchthatthisexpectationiscontinuousinπ¯ andπ. WecalladrifttrivialifDν(π¯) = 0forall
π π
π,π¯ ∈Π.
Definition5.2. (NeighborhoodOperator)Amapping
N: Π→P(Π)
isaneighborhoodoperatorifitsatisfiesthefollowingconditions:
1. N iscontinuous, (continuity)
2. N(π)iscompactforallπ ∈Π, (compactness)
3. Thereexistsametricχ: Π×Π→Rsuchthatχ(π,π¯)≤ζ impliesπ¯ ∈N(π)forallπ,π¯ ∈Πgivensome
ζ ∈R . (closedball)
+
WecallN(·)=Πthetrivialneighborhoodoperator.
Withthesedefinitions,wecandefinethemirrorlearningupdaterule.
Definition5.3. (MirrorLearningUpdate)Letπ
old
bethepreviouspolicyanddπold thestatedistributionunderπ
old
.
Further,let
(cid:104) Mπ¯V (cid:105) (s):=E (cid:2) Q (s,A) (cid:3) − ν π π¯ D (π¯ |s)
D π A∼π¯ π dπ π
bethemirrorlearningoperator. Then,themirrorlearningupdatechoosesthenewpolicyπ as
new
(cid:20)(cid:104) (cid:105) (cid:21)
π new ∈ argmaxE S∼dπold Mπ D ¯V πold (S) . (26)
π¯∈N(πold)
Underthelightofthismirrorlearningupdate,thedriftDfromonepolicytothenextinducessomepenaltyonthe
objectivewhiletheneighborhoodoperatorputsahardconstraintonthedivergenceofsubsequentpolicies.
5.2.2 PolicyGradientAlgorithmsasInstancesofMirrorLearning
Beforeprovingtheconvergenceofmirrorlearningtoanoptimalpolicy,wefirstshowthatthediscussedpolicygradient
algorithmscanpartlybeseenasinstancesofmirrorlearning,i.e. useupdatesoftheform
(cid:20) νπ (cid:21)
π new ∈ π a ∈ rg N m (π a old x ) E S∼dπold E A∼π (cid:2) Q πold (S,A) (cid:3) − d π π o o l l d d D πold (π |S) .
A3C
A3CisadirectapplicationofthePolicyGradientTheorem,albeitwithalearnedadvantagefunction. Thus,ateach
iterationitapproximatelysolvestheoptimizationproblem
π ∈argmaxE (cid:104) A (S,A) (cid:105) =argmaxE (cid:104) E (cid:2) Q (S,A) (cid:3)(cid:105) .
new S∼dπold,A∼π πold S∼dπold A∼π πold
π∈Π π∈Π
ThisisthemosttrivialinstantiationofmirrorlearningbyusingthetrivialdriftD(·|·)=0andthetrivialneighborhood
operatorN(·)=Π. ThesameargumentationalsoappliestoREINFORCE.Notethatinpracticehowever,wemaximize
theexpectationoverπ ratherthanπ. Forthisreason,thesearenotexactinstancesofmirrorlearning.
old
TRPO
TRPO’sconstrainedoptimizationproblems
(cid:20) (cid:21)
π(A|S)
π ∈argmaxE A (S,A)
new S∼dπold,A∼πold π (A|S) πold
π∈Π old
subjectto E S∼dπold (cid:2) D KL (π old (·|S)∥π(·|S)) (cid:3) ≤δ
canberewrittenas
π ∈ argmax E (cid:104) E (cid:2) Q (S,A) (cid:3)(cid:105)
new S∼dπold A∼π πold
π∈NTRPO(πold)
withtheaverage-KLballastheneighborhoodoperator,i.e.
N (π )= (cid:8) π |E (cid:2) D (π (·|S)∥π(·|S)) (cid:3) ≤δ (cid:9) .
TRPO old S∼dπold KL old
26
--- PAGE 28 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Here,weusedthat
(cid:20) π(A|s) (cid:21) (cid:90) π(a|s)
E A (s,A) = π (a|s) A (s,a)da
A∼πold π (A|s) πold old π (a|s) πold
old old
a∈A
(cid:104) (cid:105)
=E A (s,A)
A∼π πold
andthatmaximizingovertheaction-valuefunctionisidenticaltomaximizingovertheadvantagefunctionfollowing
thediscussioninSection3.2. Thus,TRPOisamirrorlearninginstancewiththetrivialdriftD(·|·)=0.
PPO
Eachiteration,PPOsearchesfor
(cid:20) (cid:26) (cid:27) (cid:21)
π ∈argmaxE min r (A|S),clip (cid:0) r (A|S),1−ε,1+ε (cid:1) A (S,A) ,
new πold π π πold
π∈Π
wherewewriter (a|s)for π(a|s) . Wecanrewritetheexpectationoveractionsbyaddingzeroas
π πold(a|s)
(cid:20) (cid:26) (cid:27)(cid:21)
E min r (A|s)A (s,A),clip (cid:0) r (A|s),1−ε,1+ε (cid:1) A (s,A)
A∼πold π πold π πold
(cid:20) (cid:21) (cid:20)
=E r (A|s)A (s,A) −E r (A|s)A (s,A)
A∼πold π πold A∼πold π πold
(cid:26) (cid:27)(cid:21)
(cid:0) (cid:1)
−min r (A|s)A (s,A),clip r (A|s),1−ε,1+ε A (s,A) .
π πold π πold
Usingthesametechniqueasbefore,wecanwritethefirstexpectationequivalentlyasE (cid:2) A (s,A) (cid:3) . Wenow
A∼π πold
focusonthesecondexpectation. Wereplacetheminoperatorwithamaxandpushthefirstterminsidethemaxto
obtain
(cid:20) (cid:26) (cid:27)(cid:21)
E r (A|s)A (s,A)−min r (A|s)A (s,A),clip (cid:0) r (A|s),1−ε,1+ε (cid:1) A (s,A)
A∼πold π πold π πold π πold
(cid:20) (cid:26) (cid:27)(cid:21)
=E r (A|s)A (s,A)+max −r (A|s)A (s,A),−clip (cid:0) r (A|s),1−ε,1+ε (cid:1) A (s,A)
A∼πold π πold π πold π πold
(cid:20) (cid:26)
=E max r (A|s)A (s,A)−r (A|s)A (s,A),
A∼πold π πold π πold
(cid:27)(cid:21)
(cid:0) (cid:1)
r (A|s)A (s,A)−clip r (A|s),1−ε,1+ε A (s,A)
π πold π πold
=E (cid:20) max (cid:26) 0, (cid:16) r (A|s)−clip (cid:0) r (A|s),1−ε,1+ε (cid:1)(cid:17) A (s,A) (cid:27)(cid:21) .
A∼πold π π πold
Thisfinalexpressionisnon-negative. Moreover,itiszeroforπsufficientlyclosetoπ ,i.e. suchthatforallactions
old
a∈Awehaver (a|s)= π(a|s) ∈[1−ε,1+ε],becausethentheclip-functionreducestotheidentityfunction
π πold(a|s)
w.r.t. itsfirstargument. Thus, thederivativesofthisexpressionmustalsobezeroatπ(· | s) = π (· | s). These
old
propertiesaretheexactconditionsfora mappingtobeconsideredadriftinthesenseofDefinition5.1. Withthis
preparation,wecannowwritethePPOupdateas
(cid:20) (cid:104) (cid:105) (cid:21)
π ∈argmaxE E A (S,A) −D (π |S) ,
new S∼dπold A∼π πold πold
π∈Π
whereD isadriftgivenby
πold
D (π |s)=E (cid:20) max (cid:26) 0, (cid:16) r (A|s)−clip (cid:0) r (A|s),1−ε,1+ε (cid:1)(cid:17) A (s,A) (cid:27)(cid:21) .
πold A∼πold π π πold
ThisisaninstanceofthemirrorlearningupdatewiththetrivialneighborhoodoperatorN(·)=Πandνπ =dπold.
πold
27
--- PAGE 29 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
5.2.3 ConvergenceProof
Now,wepresentthemaintheoreticalresultof[46].
Theorem5.4. LetDν beadrift,N aneighborhoodoperatoranddπ thesamplingdistribution,allcontinuousinπ. Let
theobjective,i.e. theexpectedreturnsunderapolicyπ,bewrittenasJ(π)=E (cid:2) G (cid:3) . Letπ ∈Πbetheinitial
policyandthesequenceofpolicies (cid:0) π (cid:1)∞ beobtainedthroughthemirrorle S a 0 r ∼ n p i 0 n , g π upd 0 aterule 0 (26)underDν,N
n n=0
anddπ. Then,
1. (Strictmonotonicimprovement)
(cid:20) νπn+1(S) (cid:21)
J(π )≥J(π )+E πn D (π |S) ∀n∈N .
n+1 n S∼p0 dπn(S) πn n+1 0
2. (Valuefunctionoptimality)
lim V =V∗.
n→∞
πn
3. (Maximumattainablereturn)
lim J(π )=maxJ(π).
n
n→∞ π∈Π
4. (Policyoptimality)
lim π =π∗.
n
n→∞
Proof. Westructuretheproofby[46]infivesteps. Instep1,westartbyshowingthatmirrorlearningupdatesleadto
improvementsunderthemirrorlearningoperatorMπnV ,whichimpliesimprovementsinthevaluefunctionV .
D π (cid:0)n−1 (cid:1)∞ πn
Instep2,weprovethatthesequenceofvaluefunctions V convergestosomelimit. Instep3,weshowthe
πn n=0
(cid:0) (cid:1)∞
existenceoflimitpointsofthesequenceofpolicies π ,whicharefixedpointsofthemirrorlearningupdate(26).
n n=0
Instep4,weprovethattheselimitpointsarealsofixedpointsofGeneralizedPolicyIteration(GPI)[75],fromwhich
weconcludethattheselimitpointsareoptimalpoliciesinstep5. Forsimplicity,weproofTheorem5.4fordiscrete
stateandactionsspaces. However,theresultsarestraightforwardtoextendedtothecontinuouscases(seetheappendix
in[46]fordetails).
Step1
Westartbyshowingbycontradictionthatforalln∈N andforalls∈S:
0
(cid:2) Mπn+1V (cid:3) (s)≥ (cid:2) MπnV (cid:3) (s). (27)
D πn D πn
Supposethereexistss ∈S,whichviolates(27). Wedefineapolicyπˆ with
0
(cid:26)
π (·|s) ifs̸=s ,
πˆ(·|s)= n+1 0
π (·|s) ifs=s .
n 0
This way, we guarantee πˆ ∈ N(π ) because π ∈ N(π ) is forced by the mirror learning update (26) and the
n n+1 n
distancebetweenπˆ andπ issimilartothedistancebetweenπ andπ ateverys̸=s butsmallerats=s .
n n+1 n 0 0
Byassumption,wehaveats that
0
(cid:2) MπˆV (cid:3) (s )=E (cid:104) Q (s ,A) (cid:105) − ν π πˆ nD (πˆ |s )
D πn 0 A∼πˆ πn 0 dπn πn 0
(cid:104) (cid:105) νπn
=E Q (s ,A) − πnD (π |s )
A∼πn πn 0 dπn πn n 0
= (cid:2) MπnV (cid:3) (s )
D πn 0
> (cid:2) Mπn+1V (cid:3) (s ).
D πn 0
Itfollowsthat
(cid:20)(cid:104) (cid:105) (cid:21) (cid:20)(cid:104) (cid:105) (cid:21) (cid:18)(cid:104) (cid:105) (cid:104) (cid:105) (cid:19)
E
S∼dπn
Mπ
D
ˆV
πn
(S) −E
S∼dπn
Mπ
D
n+1V
πn
(S) =dπn(s
0
) Mπ
D
ˆV
πn
(s
0
)− Mπ
D
n+1V
πn
(s
0
) >0,
28
--- PAGE 30 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
(cid:104) (cid:105) (cid:104) (cid:105)
whereweusedthat MπˆV (s)= Mπn+1V (s)fors̸=s . Thus,
D πn D πn 0
(cid:20)(cid:104) (cid:105) (cid:21) (cid:20)(cid:104) (cid:105) (cid:21)
E MπˆV (S) >E Mπn+1V (S) ,
S∼dπn D πn S∼dπn D πn
whichcontradictsthemirrorlearningupdaterule,i.e. that
(cid:20)(cid:104) (cid:105) (cid:21) (cid:20)(cid:104) (cid:105) (cid:21)
E Mπn+1V (S) = max E Mπ¯V (S) .
S∼dπn D πn
π¯∈N(πn)
S∼dπn D πn
(cid:0) (cid:1)∞
Hence,wehaveshownthatthesequenceofpolicies π createdbythemirrorlearningupdatesmonotonically
n n=0
increases the mirror learning operator at every state. Next, we show that this property, i.e. (cid:2) Mπn+1V (cid:3) (s) ≥
(cid:2) MπnV (cid:3) (s),impliesthemonotonicimprovementinthevaluefunction
D πn
D πn
V (s)≥V (s) (28)
πn+1 πn
foralls ∈S andn∈N .
0
ByusingthedefinitionsofthevaluefunctionV ,theaction-valuefunctionQ ,themirrorlearningoperatorMπ¯V
π π D π
andtheidentityD (π |s)=0,addingzerosandrearranging,weobtain
π
(cid:104) (cid:105) (cid:104) (cid:105)
V (s)−V (s)=E R+γV (S′) −E R+γV (S′)
πn+1 πn πn+1 πn+1 πn πn
(cid:104) (cid:105) (cid:104) (cid:105)
=E R+γV (S′) −E R+γV (S′)
πn+1 πn+1 πn πn
νπn+1(s) νπn+1(s)
+
πn
D (π |s)−
πn
D (π |s)
dπn(s) πn n+1 dπn(s) πn n+1
(cid:104) (cid:105) (cid:104) (cid:105)
=E R+γV (S′)+γV (S′)−γV (S′) −E R+γV (S′)
πn+1 πn+1 πn πn πn πn
νπn+1(s) νπn+1(s)
+
πn
D (π |s)−
πn
D (π |s)
dπn(s) πn n+1 dπn(s) πn n+1
(cid:18) (cid:104) (cid:105) νπn+1(s) (cid:19)
= E R+γV (S′) − πn D (π |s)
πn+1 πn dπn(s) πn n+1
(cid:18) (cid:104) (cid:105) νπn(s) (cid:19)
− E R+γV (S′) − πn D (π |s)
πn πn dπn(s) πn n
(cid:104) (cid:105) νπn+1(s)
+γE V (S′)−V (S′) + πn D (π |s)
πn+1 πn+1 πn dπn(s) πn n+1
(cid:18) (cid:104) (cid:105) νπn+1(s) (cid:19)
= E Q (s,A) − πn D (π |s)
πn+1 πn dπn(s) πn n+1
(cid:18) (cid:104) (cid:105) νπn(s) (cid:19)
− E Q (s,A) − πn D (π |s)
πn πn dπn(s) πn n
(cid:104) (cid:105) νπn+1(s)
+γE V (S′)−V (S′) + πn D (π |s)
πn+1 πn+1 πn dπn(s) πn n+1
= (cid:2) Mπn+1V (cid:3) − (cid:2) MπnV (cid:3)
D πn D πn
(cid:104) (cid:105) νπn+1(s) (29)
+γE V (S′)−V (S′) + πn D (π |s)
πn+1 πn+1 πn dπn(s) πn n+1
(cid:104) (cid:105) νπn+1(s)
≥γE V (S′)−V (S′) + πn D (π |s),
πn+1 πn+1 πn dπn(s) πn n+1
where we used Inequality (27) in the final step. We take the infimum over states and replace the expectation with
anotherinfimumoverstatesasalowerbound:
(cid:104) (cid:105) (cid:20) (cid:104) (cid:105) νπn+1(s) (cid:21)
inf V (s)−V (s) ≥ inf γE V (S′)−V (S′) + πn D (π |s)
s∈S πn+1 πn s∈S πn+1 πn+1 πn dπn(s) πn n+1
(cid:20) (cid:104) (cid:105) νπn+1(s) (cid:21)
≥ inf γ inf V (s′)−V (s′) + πn D (π |s)
s∈S s′∈S πn+1 πn dπn(s) πn n+1
(cid:104) (cid:105) (cid:20) νπn+1(s) (cid:21)
=γ inf V (s′)−V (s′) + inf πn D (π |s) .
s′∈S πn+1 πn s∈S dπn(s) πn n+1
29
--- PAGE 31 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Fromthisexpression,weobtain
(cid:104) (cid:105) 1 (cid:20) νπn+1(s) (cid:21)
inf V (s)−V (s) ≥ inf
πn
D (π |s) ≥0,
s∈S πn+1 πn 1−γ s∈S dπn(s) πn n+1
since ν
π
π
n
n+1(s) and dπn(s) are probabilities and the drift D is non-negative. Thus, we have proven the monotonic
improvement of value functions V (s) ≥ V (s). We observe that this already implies the strict monotonic
πn+1 πn
improvementproperty
(cid:20) νπn+1(S) (cid:21)
J(π )≥J(π )+E πn D (π |S)
n+1 n S∼p0 dπn(S) πn n+1
foralln ∈N sinceapplying(28)and(27)sequentiallyto(29)yieldsforalls∈S
0
V (s)−V (s)= (cid:2) Mπn+1V (cid:3) − (cid:2) MπnV (cid:3)
πn+1 πn D πn D πn
(cid:104) (cid:105) νπn+1(s)
+γE V (S′)−V (S′) + πn D (π |s)
πn+1 πn+1 πn dπn(s) πn n+1
≥ (cid:2) Mπn+1V (cid:3) − (cid:2) MπnV (cid:3) + ν π π n n+1(s) D (π |s)
D πn D πn dπn(s) πn n+1
νπn+1(s)
≥
πn
D (π |s).
dπn(s) πn n+1
WeobtainthedesiredinequalitybytakingtheexpectationoverS ∼p .
0
Step2
Fromstep1,weknowthatthevaluefunctionsincreaseuniformlyoverthestatespace,i.e. V (s)−V (s) ≥ 0,
foralls∈S,n∈N . Astherewardsrareboundedbyassumptionandweconsidertheepisod π i n c + c 1 asewhe π r n eepisode
0
lengthsarealsoboundedbyT (albeitthesameargumentappliesforinfinitetimehorizonsviadiscounting),thevalue
functions V (s) = E (cid:2)(cid:80)T γkR | S = s (cid:3) are also uniformly bounded. Via the Monotone Convergence
π π k=0 t+k+1 t
(cid:0) (cid:1)∞
Theorem(TheoremD.13),thesequenceofvaluefunctions V mustthereforeconvergetosomelimitV.
πn n=0
Step3
(cid:0) (cid:1)∞
Now,weshowtheexistenceoflimitpointsofthesequenceofpolicies π andprovebycontradictionthatthese
n n=0
arefixedpointsofthemirrorlearningupdate(26).
(cid:0) (cid:1)∞
Thesequence π isbounded,thustheBolzano-WeierstrassTheorem(TheoremD.14)yieldstheexistenceof
n n=0
(cid:0) (cid:1)∞
limitsπ¯ towhichsomerespectivesubsequence π converges. WedenotethissetoflimitpointsasLΠ. Foreach
ni i=0
(cid:0) (cid:1)∞
elementofsuchaconvergentsubsequence π ,mirrorlearningsolvestheoptimizationproblem
ni i=0
π∈
m
N
a
(π
x
ni )
E S∼dπni (cid:104)(cid:2) Mπ D V πni (cid:3) (S) (cid:105) (30)
Thisexpressioniscontinuousinπ duetothecontinuityofthevaluefunction[45],thedriftandneighborhoodoperator
ni
(by definition) and the sampling distribution (by assumption). Let π¯ = lim π . Berge’s Maximum Theorem
i→∞ ni
(TheoremD.15)[6]nowguaranteestheconvergenceoftheaboveexpression,yielding
i
l
→
im
∞π∈
m
N
a
(π
x
ni )
E S∼dπni (cid:104)(cid:2) Mπ D V πni (cid:3) (S) (cid:105) =
π∈
m
N
a
(
x
π¯)
E S∼dπ¯ (cid:104)(cid:2) Mπ D V π¯ (cid:3) (S) (cid:105) . (31)
Foralli∈N ,weobtainthenextpolicyπ astheargmaxofExpression(30). Sincethisexpressionconvergesto
thelimitin(3 0 1),theremustexistsomesub n s i e + q 1 uence (cid:0) π (cid:1)∞ of (cid:0) π (cid:1)∞ whichconvergestosomepolicyπ′,
nik +1 k=0 ni+1 i=0
whichisthesolutiontotheoptimizationproblem(31). Wenowshowbycontradictionthatπ′ =π¯,whichimpliesthat
π¯ isafixedpointofthemirrorlearningupdaterule.
Supposeπ′ ̸=π¯. Asπ′isinducedbythemirrorlearningupdaterule,themonotonicimprovementresultsfromstep1
yield
(cid:104) (cid:105) (cid:104) (cid:105)
Q (s,a)=E R+γV (S′) ≥E R+γV (S′) =Q (s,a) (32)
π′ R,S′∼P π′ R,S′∼P π¯ π¯
and
(cid:2) Mπ′ V (cid:3) (s)≥ (cid:2) Mπ¯V (cid:3) (s).
D π¯ D π¯
30
--- PAGE 32 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Suppose
E (cid:104)(cid:2) Mπ′ V (cid:3) (S) (cid:105) >E (cid:104)(cid:2) Mπ¯V (cid:3) (S) (cid:105) ,
S∼dπ¯ D π¯ S∼dπ¯ D π¯
thenwehaveforsomestates
(cid:2) Mπ′ V (cid:3) (s)=E (cid:104) Q (s,A) (cid:105) − ν π¯ π′(s) D (π′ |s)
D π¯ π′ π¯ dπ¯(s) π¯
> (cid:2) Mπ¯V (cid:3) (s)=E (cid:104) Q (s,A) (cid:105) − ν π¯ π¯(s) D (π¯ |s)
D π¯ π¯ π¯ dπ¯(s) π¯
(cid:104) (cid:105)
=E Q (s,A) =V (s)=V(s).
π¯ π¯ π¯
Inthelastequality,weusedthatthesequenceofvaluefunctionsconvergestosomeuniquelimitV,whichimplies
V =V. Weobtainthefollowingviathisresult,Inequality(32),whichmustbestrictfors,andthenon-negativityof
π¯
thedriftD:
V (s)=E (cid:2) Q (s,A) (cid:3)
π′ π′ π′
>E (cid:2) Q (s,A) (cid:3)
π′ π¯
>E (cid:2) Q (s,A) (cid:3) − ν π¯ π′(s) D (π′ |s)
π′ π¯ dπ¯(s) π¯
>V(s).
HoweverduetoV (s)=lim V ,thiscontradictstheuniquenessofthevaluelimit,whichgivesV =V.
π′ k→∞ πnik +1 π′
Therefore,wehaveshownbycontradictionthat
π¯ ∈argmaxE (cid:104)(cid:2) MπV (cid:3) (S) (cid:105) .
S∼dπ¯ D π¯
π∈N(π¯)
Step4
(cid:0) (cid:1)∞
Followingstep3,letπ¯ bealimitpointof π . Wewillshowbycontradictionthatπ¯ isalsoafixedpointofGPI
n n=0
(seeTheorem2.1),i.e. thatforalls∈S
π¯ ∈argmaxE (cid:2) A (s,A) (cid:3) =argmaxE (cid:2) Q (s,A) (cid:3) . (33)
A∼π π¯ A∼π π¯
π∈Π π∈Π
Fromstep3,weknowthat
(cid:20) (cid:104) νπ(S) (cid:105)(cid:21)
π¯ ∈argmax E Q (S,A)− π¯ D (π |S)
S∼dπ¯,A∼π π¯ dπ¯(S) π¯
π∈Π
(cid:20) (cid:104) νπ(S) (cid:105)(cid:21)
=argmax E A (S,A)− π¯ D (π |S) (34)
S∼dπ¯,A∼π π¯ dπ¯(S) π¯
π∈Π
assubtractinganaction-independentbaselinedoesnotaffecttheargmax. Now,weassumetheexistenceofapolicyπ′
andstateswith
E (cid:2) A (s,A) (cid:3) >E (cid:2) A (s,A) (cid:3) =0. (35)
A∼π′ π¯ A∼π¯ π¯
(cid:0)
Letm=|A|denotethesizeoftheactionspace. Then,wecanwriteforanypolicyπ,π(·|s)= x ,...,x ,1−
1 m−1
(cid:80)m−1x (cid:1)
. Withthisnotation,wehave
i=1 i
m
E (cid:2) A (s,A) (cid:3) = (cid:88) π(a |s)A (s,a )
A∼π π¯ i π¯ i
i=1
m−1 m−1
(cid:88) (cid:16) (cid:88) (cid:17)
= x A (s,a )+ 1− x A (s,a )
i π¯ i i π¯ m
i=1 i=1
m−1
(cid:88) (cid:16) (cid:17)
= x A (s,a )−A (s,a ) +A (s,a ).
i π¯ i π¯ m π¯ m
i=1
31
--- PAGE 33 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
ThisshowsthatE (cid:2) A (s,A) (cid:3) isanaffinefunctionofπ(· | s),whichimpliesthatallitsGâteauxderivativesare
A∼π π¯
constant in ∆(A) for fixed directions. Due to Inequality (35), this further implies that the Gâteaux derivatives in
directionfromπ¯ toπ′arestrictlypositive. Additionally,wehavethattheGâteauxderivativesof ν d π π π ¯ ¯( ( s s ) )D π¯ (π |s)are
zeroatπ = π¯. Weseethisbyestablishinglowerandupperbounds,whichbothhavederivativesofzeroduetothe
independenceofπandthezero-gradientpropertyofthedrift:
1 νπ¯(s) νπ(s) 1
D (π¯ |s)= π¯ D (π¯ |s)=0≤ π¯ D (π |s)≤ D (π |s)
dπ¯(s) π¯ dπ¯(s) π¯ dπ¯(s) π¯ dπ¯(s) π¯
recallingthatD (π¯ |s)=0foranys∈S andusingνπ(s)≤1. Incombination,weobtainthattheGâteauxderivative
π¯ π¯
ofE A∼π (cid:2) A π¯ (s,A) (cid:3) −ν d π π π ¯ ¯( ( s s ) )D π¯ (π |s)isstrictlypositiveaswell. Therefore,wecanfindsomepolicyπˆ(·|s)bytaking
asufficientlysmallstepfromπ¯(·|s)inthedirectionofπ′(·|s)suchthatπˆ ∈N(π¯)and
E (cid:2) A (s,A) (cid:3) − ν π¯ πˆ(s) D (πˆ |s)>E (cid:2) A (s,A) (cid:3) − ν π¯ π¯(s) D (π¯ |s)=0.
A∼πˆ π¯ dπ¯(s) π¯ A∼π¯ π¯ dπ¯(s) π¯
Withthis,wecanconstructapolicywhichcontradictsEquation(34). Letπ˜ bedefinedsuchthat
(cid:26)
π¯(·|x) ifx̸=s,
π˜(·|x)=
πˆ(·|x) ifx=s.
Thisguaranteesπ˜ ∈N(π¯)and
E (cid:20) E (cid:2) A (S,A) (cid:3) − ν π¯ π˜(S) D (π˜ |S) (cid:21)
S∼dπ¯ A∼π˜ π¯ dπ¯(S) π¯
=dπ¯(s) (cid:18) E (cid:2) A (s,A) (cid:3) − ν π¯ π˜(S) D (π˜ |s) (cid:19)
A∼π˜ π¯ dπ¯(s) π¯
=dπ¯(s) (cid:18) E (cid:2) A (s,A) (cid:3) − ν π¯ πˆ(s) D (πˆ |s) (cid:19)
A∼πˆ π¯ dπ¯(s) π¯
>0,
whichcontradictsEquation(34),sotheassumption(35)mustbewrong,proving
π¯ =argmaxE (cid:2) A (s,A) (cid:3) =argmaxE (cid:2) Q (s,A) (cid:3) .
A∼π π¯ A∼π π¯
π∈Π π∈Π
Step5
(cid:0) (cid:1)
The main result (33) from step 4 shows that any limit point π¯ of π is also a fixed point of GPI. Thus, as
n n∈N
corollariesallpropertiesinducedbyGPI(seeTheorem2.1)applytoπ¯ ∈LΠ. Particularly,wehavetheoptimalityofπ¯,
thevaluefunctionoptimalityV =V =V∗andtherebyalsothemaximalityofreturnsas
π¯
lim J(π )= lim E (cid:2) V (S) (cid:3) =E (cid:2) V∗(S) (cid:3) =maxJ(π).
n→∞
n
n→∞
S∼p0 πn S∼p0
π∈Π
Thus,wehaveshownallpropertiesasclaimedbyTheorem5.4.
Weclosethissectionwithsomeremarks. Inpractice,exactupdatesaccordingtothemirrorlearningupdaterule(26)
aregenerallyinfeasible. Instead,wecansampletheexpectationtoobtainbatchestimatorsoverabatchDoftransitions
1 (cid:88) (cid:16) Q (s,a)− ν π π o n l e d w D (π |s) (cid:17) ,
|D| πold dπold πold new
s,a∈D
whereQ hastobeestimatedaswell. Thesebatchestimatorscanalsoonlybeapproximatelyoptimizedeachiteration
πold
viagradientascenttoupdatethepolicy. Giventheseapproximationsandtheat-bestlocalconvergenceofgradient
ascent,theoutlaidconvergencepropertiesremaintheoretical.
32
--- PAGE 34 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
**************
**************
1000
0
0 1 2 3 4 5 6 7 8
Environment steps (million)
drawer
edosipE
HalfCheetah
REINFORCE 6000
A2C
TRPO
PPO 5000
V-MPO
**************
1000
0 1 2 3 4 5 6 7 8
Environment steps (million)
drawer
edosipE
Ant
REINFORCE
A2C
TRPO
PPO
V-MPO
**************
2000
0
0 1 2 3 4 5 6 7 8
Environment steps (million)
drawer
edosipE
Humanoid
REINFORCE 4000
A2C
TRPO
PPO 3000
V-MPO
2000
1000
0
0 1 2 3 4 5 6 7 8
Environment steps (million)
drawer
edosipE
Hopper
REINFORCE
A2C
TRPO
PPO
V-MPO
Figure4: ComparisonofrewardsperepisodeduringtrainingonseveralMuJoCotasks. Foreachalgorithm,wereport
meansandstandarddeviationsofthreerunswithdifferentrandomseeds.
6 NumericalExperiments
Now,weempiricallycomparethediscussedpolicygradientalgorithms. Consistentwiththeoriginalworks[54,69,
71,73],wecomparethemontheestablishedMuJoCotasksuite[80],accessedthroughtheGymnasiumlibrary[81].
MuJoCofeaturesroboticssimulations,wherethetasksaretocontrolandmoverobotsofdifferentshapesbyapplying
torquestoeachjoint.
OurimplementationsbuildonthePPOimplementationfromtheBRAXlibrary[23]andarewritteninJAX[13]. For
enhancedcomparability,allalgorithmsthatestimateadvantagesuseGAEsimilarlytoPPO.InsteadofA3C,weuse
itssynchronousvariantA2Cduetoitssimplerimplementation. NotethatA2Cexhibitscomparableperformanceas
A3C[85]andonlydiffersinthatitwaitsforallactorstocollecttransitionstoupdatethemsynchronously. Wemodify
REINFORCEtoaveragegradientsoverbatchesoftransitionssimilarlyasintheotheralgorithmssincecomputingone
updateperenvironmentstepiscomputationallyverycostly. Notethatthisishoweverlikelytoimprovetheperformance
comparedtoanaiveimplementationofREINFORCE.Wedonottunehyperparametersandkeepchoicesconsistent
acrossalgorithmswherepossible. SeeAppendixAppendixAforthehyperparametersweuse. Theexperimentswere
runonastandardconsumerCPU.Allourimplementedalgorithmsandthecodeforrunningtheexperimentscanbe
foundathttps://github.com/Matt00n/PolicyGradientsJax.
Inourmainexperiment, wecomparetheperformanceofthealgorithmsintermsoftheachievedepisodicrewards
overthecourseoftraining. TheperformancesindifferentMuJoCotasksarepresentedinFigure4. Weobservethat
PPOoutperformstheotheralgorithmsinthreeoffourtasksbyachievinghigherepisodicrewardswhilelearninggood
policiesquickly. TheperformancedifferenceismostprevalentontheHumanoid-task,themostchallengingofthe
four,wherePPOlearnsmuchstrongerpoliciesthantheotheralgorithms. Inaddition,wefindourimplementationof
PPOtobecompetitivewithcommonRLlibrariesasshowninAppendixB.1. V-MPOandTRPOarecomparablein
performance,witheachofthetwoslightlyoutperformingtheotherontwooutoffourenvironments. Wenotethat
V-MPOisintendedfortrainingforbillionsofenvironmentsteps,suchthatitslowerperformancecomparedtoPPOin
ourexperimentsisexpected10[73]. A2Crequiresmoreinteractionswiththeenvironmenttoreachsimilarperformance
10Alsoseethediscussionsathttps://openreview.net/forum?id=SylOlp4FvHonthis.
33
--- PAGE 35 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
levelsasV-MPOandTRPObutfailstolearnanyusefulpolicyintheAnt-task. Thisslowerlearning11isatleastpartially
causedbyA2Conlyusingasingleupdateepochperbatch. REINFORCEperformanceworstonallenvironments,
whichisunsurprisinggivingthehighvarianceofgradientsinREINFORCE[75]. Thisalsohighlightsthebenefitsof
thebias-variancetrade-offbytheotheralgorithmsasdiscussedinSection4.6. Wefindourperformance-basedranking
ofthealgorithmstobeconsistentwithliterature(e.g.,[71,73,5]).
Moreover,weremarkthatA2Cistheonlyalgorithmforwhichweusedanentropybonusbecausethelearnedpolicies
collapsedwithoutit. WeshowcasethisinourexpendedexperimentsinAppendixB.2. Thisunderlinestheusefulnessof
the(heuristic)constraintsofV-MPO,PPOandTRPOontheKLdivergence,whichavoidsuchcollapsesevenwithout
anyentropybonuses. Tofurtherinvestigatethis,weshowtheaverageKLdivergencesbetweenconsecutivepolicies
throughouttraininginFigure5. Here,weapproximatedtheKLdivergenceusingtheunbiasedestimator[68]
(cid:20) (cid:21)
Dˆ (cid:0) π (·|s)∥π (·|s) (cid:1) =E π new (A|s) −1−ln π new (A|s)
KL old new A∼πold π (A|s) π (A|s)
old old
forallalgorithmsexceptTRPO,whichanalyticallycalculatestheexactKLdivergencesinceitisusedwithinthealgo-
rithm. WeseethattheKLdivergencesremainrelativelyconstantforallalgorithmsaftersomeinitialmovement. TRPO
displaysthemostconstantKLdivergence,whichisexplainedbyitshardconstraint. Withthechosenhyperparameters,
V-MPOusesthesameboundontheKLdivergenceasTRPO,howeverwithoutstrictlyenforcingitasoutlinedinthe
derivationofV-MPO.Thus,V-MPO’sKLdivergenceexhibitsslightlymorevariancethenTRPOandalsofrequently
exceedsthisbound. PPO’sclippingheuristicachievesasimilareffectresultinginacomparablepicture. Duetothe
lackofconstraintsontheKLdivergence,A2CandREINFORCEshowslightlymorevariance. Interestingly,their
KLdivergencesareordersofmagnitudeslowerthanfortheotheralgorithms,especiallyforREINFORCE(notethe
logarithmic scale in Figure 5). We reason this with A2C and REINFORCE using only a singly update epoch per
batch,whereasthePPOandV-MPOusemultipleepochsandTRPOusesadifferentupdateschemevialinesearch.
InAppendixB.3,weprovideexperimentalevidenceforthishypothesis. Additionally,wenoteagainthattheentropy
bonusalsostabilizesandlimitstheKLdivergenceforA2CasshowninAppendixB.2.
ThesefindingshighlightthebenefitsofregularizationthroughconstrainingtheKLdivergenceandincentivizingentropy.
Regularizationstabilizeslearningandpreventsacollapseofthepolicy. Atthesametime, itallowsmorefrequent
updatesthroughmultipleepochsperbatch,whichdrasticallyincreasesthesampleefficiencyofthealgorithmsand
speedsuplearning.
7 Conclusion
Inthiswork,wepresentedaholisticoverviewofon-policypolicygradientmethodsinreinforcementlearning. We
derivedthetheoreticalfoundationsofpolicygradientalgorithms,primarilyintheformofthePolicyGradientTheorem.
Wehaveshownhowthemostprominentpolicygradientalgorithmscanbederivedbasedonthistheorem. Wediscussed
commontechniquesusedbythesealgorithmstostabilizetrainingincludinglearninganadvantagefunctiontolimit
thevarianceofestimatedpolicygradients,constrainingthedivergencebetweenpoliciesandregularizingthepolicy
throughentropybonuses. Subsequently,wepresentedevidencefromliteratureontheconvergencebehaviorofpolicy
gradientalgorithms,whichsuggestthattheymayfindatleastlocallyoptimalpolicies. Finally,weconductednumerical
experimentsonwell-establishedbenchmarkstofurthercomparethebehaviorofthediscussedalgorithms. Here,we
foundthatPPOoutperformstheotheralgorithmsinthemajorityoftheconsideredtasksandweprovidedevidencefor
thenecessityofregularization,byconstrainingKLdivergenceorbyincentivizingentropy,tostabilizetraining.
Weacknowledgeseverallimitationsofourwork. First, wedeliberatelylimitedourscopetoon-policyalgorithms,
whichexcludescloselyrelatedoff-policypolicygradientalgorithmsandthenoveltiesintroducedbythem. Second,we
presentedanincompleteoverviewofon-policypolicygradientalgorithmsasother,albeitlessestablished,algorithms
exist(e.g.,[61,16])andthedevelopmentoffurtheralgorithmsremainsanactiveresearchfield. Here,wefocusedon
the,inourview,mostprominentalgorithmsasdeterminedbytheirimpact,usageandintroducednovelties. Third,the
convergenceresultswereferencedrestonassumptionsthatarequicklyviolatedinpractice. Inparticular,wewant
tounderlinethattheresultsbasedmirrorlearningrelyontheinfeasibleassumptionoffindingaglobalmaximizer
eachiteration. Fourth,whilewecomparedthediscussedalgorithmsempiricallyandfoundresultstobeconsistent
withexistingliterature, ouranalysisislimitedtothespecificsettingweused. Differentresultsmayariseonother
benchmarks,withdifferenthyperparametersorgenerallydifferentimplementations.
Finally,wenotethatstillmanyquestionsremaintobeansweredinthefieldofon-policypolicygradientalgorithm.
So far, our understanding of which algorithm performs best under which circumstances is still limited. Moreover,
11Slowintermsoftherequiredenvironmentsteps.NotehoweverthatA2CrunssignificantlyfasterthanPPO,TRPOandV-MPO
inabsolutetimeduetousinglessepochsperbatch.
34
--- PAGE 36 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
100
10 2
10 4
10 6
10 8
10 10
0 1 2 3 4 5 6 7 8
Environment steps (million)
ecnegrevid
LK
HalfCheetah
10 1
10 3
REINFORCE A2C 10 5
TRPO
PPO 10 7
V-MPO
10 9
10 11
0 1 2 3 4 5 6 7 8
Environment steps (million)
ecnegrevid
LK
Ant
REINFORCE
A2C
TRPO
PPO
V-MPO
100
10 2
10 4
10 6
10 8
10 10
0 1 2 3 4 5 6 7 8
Environment steps (million)
ecnegrevid
LK
Humanoid
10 3
10 5
10 7
REINFORCE 10 9 A2C
TRPO
PPO 10 11
V-MPO
0 1 2 3 4 5 6 7 8
Environment steps (million)
ecnegrevid
LK
Hopper
REINFORCE
A2C
TRPO
PPO
V-MPO
Figure5: ComparisonoftheaverageKLdivergenceacrosspoliciesduringtraining.
it is unclear whether the best possible policy gradient algorithm has yet been discovered, which is why algorithm
developmentremainsofinterest. Similarly,comprehensiveempiricalcomparisonswithotherclassesofRLalgorithms
mayyieldfurtherinsightsonthepracticaladvantagesanddisadvantagesofpolicygradientalgorithmsandhowtheir
performancedependsontheproblemsettings. Finally,weobservethatstillonlyalimitednumberofconvergence
resultsexistandnotevenalldiscussedalgorithmsarecoveredbythese,e.g.,noconvergenceresultsexistforV-MPOto
thebestofourknowledge. Here,furtherresearchisneededtoenhanceourunderstandingoftheconvergencebehavior
ofpolicygradientalgorithms.
35
--- PAGE 37 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
References
[1] AbbasAbdolmaleki,JostTobiasSpringenberg,JonasDegrave,StevenBohez,YuvalTassa,DanBelov,Nicolas
Heess,andMartinRiedmiller. Relativeentropyregularizedpolicyiteration. arXivpreprintarXiv:1812.02256,
2018.
[2] AbbasAbdolmaleki,JostTobiasSpringenberg,YuvalTassa,RemiMunos,NicolasHeess,andMartinRiedmiller.
Maximumaposterioripolicyoptimisation. arXivpreprintarXiv:1806.06920,2018.
[3] JoshuaAchiam. SpinningUpinDeepReinforcementLearning. 2018.
[4] AlekhAgarwal,ShamMKakade,JasonDLee,andGauravMahajan. Optimalityandapproximationwithpolicy
gradientmethodsinmarkovdecisionprocesses. InJacobAbernethyandShivaniAgarwal,editors,Proceedingsof
ThirtyThirdConferenceonLearningTheory,volume125ofProceedingsofMachineLearningResearch,pages
64–66.PMLR,09–12Jul2020.
[5] MarcinAndrychowicz,AntonRaichuk,PiotrStan´czyk,ManuOrsini,SertanGirgin,RaphaelMarinier,Léonard
Hussenot,MatthieuGeist,OlivierPietquin,MarcinMichalski,etal. Whatmattersinon-policyreinforcement
learning? alarge-scaleempiricalstudy. arXivpreprintarXiv:2006.05990,2020.
[6] LawrenceMAusubelandRaymondJDeneckere. Ageneralizedtheoremofthemaximum. EconomicTheory,
3(1):99–107,1993.
[7] Andrew G Barto, Richard S Sutton, and Charles W Anderson. Neuronlike adaptive elements that can solve
difficultlearningcontrolproblems. IEEEtransactionsonsystems,man,andcybernetics,(5):834–846,1983.
[8] AmirBeckandMarcTeboulle. Mirrordescentandnonlinearprojectedsubgradientmethodsforconvexoptimiza-
tion. OperationsResearchLetters,31(3):167–175,2003.
[9] RichardBellman. Dynamicprogramming. Science,153(3731):34–37,1966.
[10] JuliusBerner,PhilippGrohs,GittaKutyniok,andPhilippPetersen. Themodernmathematicsofdeeplearning.
arXivpreprintarXiv:2105.04026,pages86–114,2021.
[11] Jalaj Bhandari and Daniel Russo. On the linear convergence of policy gradient methods for finite mdps. In
InternationalConferenceonArtificialIntelligenceandStatistics,pages2386–2394.PMLR,2021.
[12] StephenPBoydandLievenVandenberghe. Convexoptimization. Cambridgeuniversitypress,2004.
[13] JamesBradbury,RoyFrostig,PeterHawkins,MatthewJamesJohnson,ChrisLeary,DougalMaclaurin,George
Necula,AdamPaszke,JakeVanderPlas,SkyeWanderman-Milne,andQiaoZhang. JAX:composabletransforma-
tionsofPython+NumPyprograms,2018.
[14] TomBrown,BenjaminMann,NickRyder,MelanieSubbiah,JaredDKaplan,PrafullaDhariwal,ArvindNee-
lakantan,PranavShyam,GirishSastry,AmandaAskell,etal. Languagemodelsarefew-shotlearners. Advances
inneuralinformationprocessingsystems,33:1877–1901,2020.
[15] AnnaChoromanska,MikaelHenaff,MichaelMathieu,GérardBenArous,andYannLeCun. Thelosssurfacesof
multilayernetworks. InArtificialintelligenceandstatistics,pages192–204.PMLR,2015.
[16] Karl W Cobbe, Jacob Hilton, Oleg Klimov, and John Schulman. Phasic policy gradient. In International
ConferenceonMachineLearning,pages2020–2027.PMLR,2021.
[17] GeorgeCybenko. Approximationbysuperpositionsofasigmoidalfunction. Mathematicsofcontrol,signalsand
systems,2(4):303–314,1989.
[18] Yann N Dauphin, Razvan Pascanu, Caglar Gulcehre, Kyunghyun Cho, Surya Ganguli, and Yoshua Bengio.
Identifyingandattackingthesaddlepointprobleminhigh-dimensionalnon-convexoptimization. Advancesin
neuralinformationprocessingsystems,27,2014.
[19] ThomasDegris,MarthaWhite,andRichardSSutton. Off-policyactor-critic. arXivpreprintarXiv:1205.4839,
2012.
[20] ArthurPDempster,NanMLaird,andDonaldBRubin. Maximumlikelihoodfromincompletedataviatheem
algorithm. Journaloftheroyalstatisticalsociety: seriesB(methodological),39(1):1–22,1977.
[21] PrafullaDhariwal,ChristopherHesse,OlegKlimov,AlexNichol,MatthiasPlappert,AlecRadford,JohnSchulman,
SzymonSidor,YuhuaiWu,andPeterZhokhov. Openaibaselines. https://github.com/openai/baselines,
2017.
36
--- PAGE 38 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
[22] AlexeyDosovitskiy,LucasBeyer,AlexanderKolesnikov,DirkWeissenborn,XiaohuaZhai,ThomasUnterthiner,
MostafaDehghani,MatthiasMinderer,GeorgHeigold,SylvainGelly,etal. Animageisworth16x16words:
Transformersforimagerecognitionatscale. arXivpreprintarXiv:2010.11929,2020.
[23] C. Daniel Freeman, Erik Frey, Anton Raichuk, Sertan Girgin, Igor Mordatch, and Olivier Bachem. Brax - a
differentiablephysicsengineforlargescalerigidbodysimulation,2021.
[24] ScottFujimoto,HerkeHoof,andDavidMeger. Addressingfunctionapproximationerrorinactor-criticmethods.
InInternationalconferenceonmachinelearning,pages1587–1596.PMLR,2018.
[25] XavierGlorot,AntoineBordes,andYoshuaBengio. Deepsparserectifierneuralnetworks. InProceedingsofthe
fourteenthinternationalconferenceonartificialintelligenceandstatistics,pages315–323.JMLRWorkshopand
ConferenceProceedings,2011.
[26] IanGoodfellow,YoshuaBengio,andAaronCourville. Deeplearning. MITpress,2016.
[27] EvanGreensmith,PeterLBartlett,andJonathanBaxter. Variancereductiontechniquesforgradientestimatesin
reinforcementlearning. JournalofMachineLearningResearch,5(9),2004.
[28] TuomasHaarnoja,AurickZhou,PieterAbbeel,andSergeyLevine. Softactor-critic: Off-policymaximumentropy
deepreinforcementlearningwithastochasticactor. InInternationalconferenceonmachinelearning, pages
1861–1870.PMLR,2018.
[29] KaimingHe,XiangyuZhang,ShaoqingRen,andJianSun. Deepresiduallearningforimagerecognition. In
ProceedingsoftheIEEEconferenceoncomputervisionandpatternrecognition,pages770–778,2016.
[30] MagnusRHestenes,EduardStiefel,etal. Methodsofconjugategradientsforsolvinglinearsystems. Journalof
researchoftheNationalBureauofStandards,49(6):409–436,1952.
[31] TimothyClassenHesterberg. Advancesinimportancesampling. StanfordUniversity,1988.
[32] SeppHochreiterandJürgenSchmidhuber. Longshort-termmemory. Neuralcomputation,9(8):1735–1780,1997.
[33] MatthewW.Hoffman,BobakShahriari,JohnAslanides,GabrielBarth-Maron,NikolaMomchev,DanilaSinopal-
nikov, Piotr Stan´czyk, Sabela Ramos, Anton Raichuk, Damien Vincent, Léonard Hussenot, Robert Dadashi,
GabrielDulac-Arnold,ManuOrsini,AlexisJacq,JohanFerret,NinoVieillard,SeyedKamyarSeyedGhasemipour,
SertanGirgin,OlivierPietquin,FeryalBehbahani,TamaraNorman,AbbasAbdolmaleki,AlbinCassirer,FanYang,
KateBaumli,SarahHenderson,AbeFriesen,RubaHaroun,AlexNovikov,SergioGómezColmenarejo,Serkan
Cabi,CaglarGulcehre,TomLePaine,SrivatsanSrinivasan,AndrewCowie,ZiyuWang,BilalPiot,andNando
deFreitas. Acme: Aresearchframeworkfordistributedreinforcementlearning. arXivpreprintarXiv:2006.00979,
2020.
[34] MarkusHolzleitner,LukasGruber,JoséArjona-Medina,JohannesBrandstetter,andSeppHochreiter.Convergence
proofforactor-criticmethodsappliedtoppoandrudder. InTransactionsonLarge-ScaleData-andKnowledge-
CenteredSystemsXLVIII:SpecialIssueInMemoryofUniv.Prof.Dr.RolandWagner,pages105–130.Springer,
2021.
[35] KurtHornik,MaxwellStinchcombe,andHalbertWhite. Multilayerfeedforwardnetworksareuniversalapproxi-
mators. Neuralnetworks,2(5):359–366,1989.
[36] ShengyiHuang,RousslanFernandJulienDossa,ChangYe,JeffBraga,DipamChakraborty,KinalMehta,and
JoãoG.M.Araújo. Cleanrl: High-qualitysingle-fileimplementationsofdeepreinforcementlearningalgorithms.
JournalofMachineLearningResearch,23(274):1–18,2022.
[37] DavidRHunterandKennethLange. Atutorialonmmalgorithms. TheAmericanStatistician,58(1):30–37,2004.
[38] ShamKakadeandJohnLangford. Approximatelyoptimalapproximatereinforcementlearning. InProceedingsof
theNineteenthInternationalConferenceonMachineLearning,pages267–274,2002.
[39] PrasenjitKarmakarandShalabhBhatnagar. Twotime-scalestochasticapproximationwithcontrolledmarkov
noiseandoff-policytemporal-differencelearning. MathematicsofOperationsResearch,43(1):130–151,2018.
[40] HenryJKelley. Gradienttheoryofoptimalflightpaths. ArsJournal,30(10):947–954,1960.
[41] JackKieferandJacobWolfowitz. Stochasticestimationofthemaximumofaregressionfunction. TheAnnalsof
MathematicalStatistics,pages462–466,1952.
[42] DiederikPKingmaandJimmyBa. Adam: Amethodforstochasticoptimization. arXivpreprintarXiv:1412.6980,
2014.
37
--- PAGE 39 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
[43] VijayRKondaandJohnNTsitsiklis. Onactor-criticalgorithms. SIAMjournalonControlandOptimization,
42(4):1143–1166,2003.
[44] AlexKrizhevsky,IlyaSutskever,andGeoffreyEHinton. Imagenetclassificationwithdeepconvolutionalneural
networks. Advancesinneuralinformationprocessingsystems,25,2012.
[45] JakubGrudzienKuba,RuiqingChen,MuningWen,YingWen,FangleiSun,JunWang,andYaodongYang. Trust
regionpolicyoptimisationinmulti-agentreinforcementlearning. arXivpreprintarXiv:2109.11251,2021.
[46] JakubGrudzienKuba,ChristianSchroederdeWitt,andJakobFoerster. Mirrorlearning: Aunifyingframeworkof
policyoptimisation. arXivpreprintarXiv:2201.02373,2022.
[47] YannLeCun,YoshuaBengio,andGeoffreyHinton. Deeplearning. nature,521(7553):436–444,2015.
[48] Johannes Lederer. Activation functions in artificial neural networks: A systematic overview. arXiv preprint
arXiv:2101.09957,2021.
[49] Eric Liang, Richard Liaw, Robert Nishihara, Philipp Moritz, Roy Fox, Ken Goldberg, Joseph E. Gonzalez,
MichaelI.Jordan,andIonStoica. RLlib: Abstractionsfordistributedreinforcementlearning. InInternational
ConferenceonMachineLearning(ICML),2018.
[50] TimothyPLillicrap,JonathanJHunt,AlexanderPritzel,NicolasHeess,TomErez,YuvalTassa,DavidSilver,and
DaanWierstra. Continuouscontrolwithdeepreinforcementlearning. arXivpreprintarXiv:1509.02971,2015.
[51] BoyiLiu,QiCai,ZhuoranYang,andZhaoranWang. Neuralproximal/trustregionpolicyoptimizationattains
globallyoptimalpolicy. arXivpreprintarXiv:1906.10306,2019.
[52] Peter Marbach and John N Tsitsiklis. Simulation-based optimization of markov reward processes. IEEE
TransactionsonAutomaticControl,46(2):191–209,2001.
[53] CharlesCMargossian. Areviewofautomaticdifferentiationanditsefficientimplementation. Wileyinterdisci-
plinaryreviews: dataminingandknowledgediscovery,9(4):e1305,2019.
[54] VolodymyrMnih,AdriaPuigdomenechBadia,MehdiMirza,AlexGraves,TimothyLillicrap,TimHarley,David
Silver, and Koray Kavukcuoglu. Asynchronous methods for deep reinforcement learning. In International
conferenceonmachinelearning,pages1928–1937.PMLR,2016.
[55] VolodymyrMnih,KorayKavukcuoglu,DavidSilver,AndreiA.Rusu,JoelVeness,MarcG.Bellemare,Alex
Graves,MartinRiedmiller,AndreasK.Fidjeland,GeorgOstrovski,StigPetersen,CharlesBeattie,AmirSadik,
IoannisAntonoglou,HelenKing,DharshanKumaran,DaanWierstra,ShaneLegg,andDemisHassabis. Human-
levelcontrolthroughdeepreinforcementlearning. Nature,518(7540):529–533,2015.
[56] Mehryar Mohri, Afshin Rostamizadeh, and Ameet Talwalkar. Foundations of Machine Learning. Adaptive
ComputationandMachineLearning.MITPress,Cambridge,MA,2edition,2018.
[57] VinodNairandGeoffreyEHinton. Rectifiedlinearunitsimproverestrictedboltzmannmachines. InProceedings
ofthe27thinternationalconferenceonmachinelearning(ICML-10),pages807–814,2010.
[58] David Pollard. Asymptopia: an exposition of statistical asymptotic theory. In Asymptopia: an exposition of
statisticalasymp-totictheory,2000.
[59] Boris T Polyak. Some methods of speeding up the convergence of iteration methods. Ussr computational
mathematicsandmathematicalphysics,4(5):1–17,1964.
[60] AntoninRaffin,AshleyHill,AdamGleave,AnssiKanervisto,MaximilianErnestus,andNoahDormann. Stable-
baselines3:Reliablereinforcementlearningimplementations.JournalofMachineLearningResearch,22(268):1–8,
2021.
[61] Md Masudur Rahman and Yexiang Xue. Robust policy optimization in deep reinforcement learning. arXiv
preprintarXiv:2212.07536,2022.
[62] Prajit Ramachandran, Barret Zoph, and Quoc V Le. Searching for activation functions. arXiv preprint
arXiv:1710.05941,2017.
[63] HerbertRobbinsandSuttonMonro. Astochasticapproximationmethod. Theannalsofmathematicalstatistics,
pages400–407,1951.
[64] HerbertRobbinsandDavidSiegmund. Aconvergencetheoremfornonnegativealmostsupermartingalesand
someapplications. InOptimizingmethodsinstatistics,pages233–257.Elsevier,1971.
38
--- PAGE 40 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
[65] ReuvenY.Rubinstein. SimulationandtheMonteCarloMethod. Wiley,NewYork,firstedition,1981.
[66] David E Rumelhart, Geoffrey E Hinton, Ronald J Williams, et al. Learning internal representations by error
propagation,1985.
[67] GavinARummeryandMahesanNiranjan.On-lineQ-learningusingconnectionistsystems,volume37.University
ofCambridge,DepartmentofEngineeringCambridge,UK,1994.
[68] JohnSchulman. Approximatingkldivergence. JohnSchulman’sHomepage,2020.
[69] JohnSchulman,SergeyLevine,PieterAbbeel,MichaelJordan,andPhilippMoritz. Trustregionpolicyoptimiza-
tion. InInternationalconferenceonmachinelearning,pages1889–1897.PMLR,2015.
[70] JohnSchulman,PhilippMoritz,SergeyLevine,MichaelJordan,andPieterAbbeel. High-dimensionalcontinuous
controlusinggeneralizedadvantageestimation. arXivpreprintarXiv:1506.02438,2015.
[71] JohnSchulman,FilipWolski,PrafullaDhariwal,AlecRadford,andOlegKlimov. Proximalpolicyoptimization
algorithms. arXivpreprintarXiv:1707.06347,2017.
[72] David Silver, Aja Huang, Chris J. Maddison, Arthur Guez, Laurent Sifre, George van den Driessche, Julian
Schrittwieser, Ioannis Antonoglou, Veda Panneershelvam, Marc Lanctot, Sander Dieleman, Dominik Grewe,
JohnNham,NalKalchbrenner,IlyaSutskever,TimothyLillicrap,MadeleineLeach,KorayKavukcuoglu,Thore
Graepel,andDemisHassabis. Masteringthegameofgowithdeepneuralnetworksandtreesearch. Nature,
529(7587):484–489,2016.
[73] HFrancisSong,AbbasAbdolmaleki,JostTobiasSpringenberg,AidanClark,HubertSoyer,JackWRae,Seb
Noury,ArunAhuja,SiqiLiu,DhruvaTirumala,etal.V-mpo:On-policymaximumaposterioripolicyoptimization
fordiscreteandcontinuouscontrol. arXivpreprintarXiv:1909.12238,2019.
[74] RichardSSuttonandAndrewGBarto. Towardamoderntheoryofadaptivenetworks: expectationandprediction.
Psychologicalreview,88(2):135,1981.
[75] RichardS.SuttonandAndrewG.Barto. ReinforcementLearning: AnIntroduction. TheMITPress, second
edition,2018.
[76] Richard S Sutton, David McAllester, Satinder Singh, and Yishay Mansour. Policy gradient methods for re-
inforcement learning with function approximation. Advances in neural information processing systems, 12,
1999.
[77] RichardSSutton,SatinderSingh,andDavidMcAllester. Comparingpolicy-gradientalgorithms. IEEETransac-
tionsonSystems,Man,andCybernetics,2000.
[78] Richard Stuart Sutton. Temporal credit assignment in reinforcement learning. University of Massachusetts
Amherst,1984.
[79] GeraldTesauroetal. Temporaldifferencelearningandtd-gammon. CommunicationsoftheACM,38(3):58–68,
1995.
[80] EmanuelTodorov,TomErez,andYuvalTassa. Mujoco: Aphysicsengineformodel-basedcontrol. In2012
IEEE/RSJInternationalConferenceonIntelligentRobotsandSystems,pages5026–5033.IEEE,2012.
[81] MarkTowers,JordanK.Terry,ArielKwiatkowski,JohnU.Balis,GianlucadeCola,TristanDeleu,ManuelGoulão,
AndreasKallinteris,ArjunKG,MarkusKrimmel,RodrigoPerez-Vicente,AndreaPierré,SanderSchulhoff,JunJet
Tai,AndrewTanJinShen,andOmarG.Younis. Gymnasium,March2023.
[82] HadovanHasselt. Reinforcementlearninglecture5: Model-freeprediction,October2021.
[83] AshishVaswani,NoamShazeer,NikiParmar,JakobUszkoreit,LlionJones,AidanNGomez,ŁukaszKaiser,and
IlliaPolosukhin. Attentionisallyouneed. Advancesinneuralinformationprocessingsystems,30,2017.
[84] ChristopherJohnCornishHellabyWatkins. Learningfromdelayedrewards. 1989.
[85] LilianWeng. Policygradientalgorithms. lilianweng.github.io,2018.
[86] Robert Edwin Wengert. A simple automatic derivative evaluation program. Communications of the ACM,
7(8):463–464,1964.
[87] RonaldJWilliams. Reinforcement-learningconnectionistsystems. CollegeofComputerScience,Northeastern
University,1987.
39
--- PAGE 41 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
[88] RonaldJWilliams. Simplestatisticalgradient-followingalgorithmsforconnectionistreinforcementlearning.
Reinforcementlearning,pages5–32,1992.
[89] RonaldJWilliamsandJingPeng. Functionoptimizationusingconnectionistreinforcementlearningalgorithms.
ConnectionScience,3(3):241–268,1991.
[90] KaiqingZhang,AlecKoppel,HaoZhu,andTamerBasar. Globalconvergenceofpolicygradientmethodsto
(almost)locallyoptimalpolicies. SIAMJournalonControlandOptimization,58(6):3586–3612,2020.
40
--- PAGE 42 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Value
Hyperparameter REINFORCE A2C TRPO PPO V-MPO
Learningrate 3·10−4 3·10−4 3·10−4 3·10−4 3·10−4
Num. minibatches 1 8 8 8 8
Num. epochs 1 1 112 10 10
Discount(γ) — 0.99 0.99 0.99 0.99
GAEparameter(λ) — 0.95 0.95 0.95 0.95
Normalizeadvantages — True True True False
Entropybonuscoef. 0 0.1 0 0 0
Max. grad. norm 0.5 0.5 0.5 0.5 0.5
Unrolllength — ************** 2048
KLtarget(δ) — — 0.01 — —
CGdamping — — 0.1 — —
CGmax. iterations — — 10 — —
Linesearchmax. iterations — — 10 — —
Linesearchshrinkagefactor — — 0.8 — —
PPOclipping(ε) — — — 0.2 —
Min. temp. (η ) — — — — 10−8
min
Min. KLpen. (ν ) — — — — 10−8
min
Init. temp. (η ) — — — — 1
init
Init. KLpen. (mean)(ν ) — — — — 1
µinit
Init. KLpen. (std)(ν ) — — — — 1
σinit
KLtarget(mean)(ε ) — — — — 0.01
νµ
KLtarget(std)(ε ) — — — — 5·10−5
νσ
KLtarget(temp.) (ε ) — — — — 0.01
η
Table2: Algorithmhyperparameters.
Appendices
A Hyperparameters
WereportthehyperparametersweuseinourmainexperimentsinTable2. Allalgorithmsuseseparatepolicyandvalue
networks. Policynetworksuse4hiddenlayerswith32neuronsrespectively. Valuenetworksuse5layerswith256
neuronseach. Weuseswish-activationfunctions[62]throughoutbothnetworks. Policyoutputsaretransformedtofit
theboundsoftheactionsspacesviaasquashingfunction. WeusetheAdamoptimizer[42]withgradientclippinganda
slightlineardecayofthelearningrates. Further,wepreprocessobservationsandrewardsbynormalizingthemusing
runningmeansandstandarddeviationsandclippingthemtotheinterval[−10,10]. AllalgorithmsexceptREINFORCE
use8parallelenvironmentstocollectexperience. Weuseindependentenvironmentstoevaluatetheagentsthroughout
training. Intheevaluations,agentsselectactionsdeterministicallyasthemodeoftheconstructeddistribution.
B ExtendedExperiments
Here,wepresentresultsfromfurtherexperiments. Unlessindicatedotherwise,weusethehyperparametersasreported
inAppendixAppendixA.
B.1 ComparisontoRLframeworks
InTable3,wecomparetheperformanceofourimplementationofPPOwithpopularRLframeworks. Notethatwedid
nottuneanyhyperparametersforourimplementationssuchthatthereportedscoresshouldbeunderstoodaslower
bounds. WecomparePPOsinceitisthemostpopularandcommonlyimplementedofthediscussedalgorithmsacross
frameworks. Incontrast,especiallyTRPOandV-MPOarerarelyfound.
12TRPOusesoneepochforitspolicyupdatesbut10epochsperbatchforupdatingthevaluenetwork.
13Numbersreadapproximatelyfromplotsinthepaper.
41
--- PAGE 43 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Framework
CleanRL Baselines SB3 RLlib ACME13 Ours Ours
[36] [21] [60] [49] [33]
MuJoCoversion v4 v1 v3 v2 v2 v4 v4
Stepsinmillion 1 1 1 44 10 1 8
HalfCheetah ************** ************** 6414
Hopper ************** — 2550 895 2616
Humanoid 742 — — — 6600 700 7633
Ant — — 1327 — **************
Table3: ComparisonofthemeanperformanceofourPPOimplementationwithpopularRLframeworks. Scoresforthe
frameworksareshownasreportedintherespectivepaperordocumentation.
**************
1000
0
1000
0 1 2 3 4 5 6 7 8
Environment steps (million)
drawer
edosipE
0.1 1012
0.01
0 109
106
103
100
10 3
0 1 2 3 4 5 6 7 8
Environment steps (million)
ecnegrevid
LK
0.1
0.01
0
Figure6: Wecomparetheepisodereward(left)andKLdivergence(right)fordifferentvaluesoftheentropycoefficient
forA2ConHalfCheetah.
B.2 EntropyBonusinA2C
InFigure6,weshowthatusinganentropybonusimprovestheperformanceofA2Cbystabilizinglearning.Inparticular,
insufficientlylowvaluesoftheentropycoefficientresultinacollapseofthepolicyaftersometime. Thisisvisibleina
drasticincreaseintheKLdivergences(notethelogarithmicscale).
B.3 A2CandREINFORCEwithMultipleUpdateEpochs
InFigure7,weshowcasethattheKLdivergenceislowforA2CandREINFORCEduetousingonlyasingleupdate
epochperbatch. Onthecontrary,whenusingmultipleepochs,thepoliciescollapseforbothalgorithmsasvisiblebythe
divergingKLdivergenceandabruptperformanceloss. Note,thathereweshowthisbehaviorforfiveepochs,however
inourtestsA2CandREINFORCEdisplaysimilarbehaviorsalreadywhenonlyusingtwoepochs,albeitthepolicies
thenonlycollapseafteranextendedperiodoftime. Further,notethatoverthedisplayedrangeofenvironmentsteps,
thealgorithmsdonotyetlearnanyusefulpolicieswhenusingasingleepoch. However,performanceimprovesforboth
A2CandREINFORCEwhengivenmoretimeasdepictedinFigure4.
C V-MPO:DerivationDetails
Inthefollowing,weprovideamoredetailedderivationoftheobjectivefunctionofV-MPO
J (θ,η,ν)=L (θ)+L (η)+L (θ,ν),
V-MPO π η ν
whereL isthepolicyloss
π
exp (cid:16) Aˆ ϕ(s,a) (cid:17)
(cid:88) η
L (θ)=− lnπ (a|s), (36)
π (cid:80)
exp
(cid:16) Aˆ ϕ(s′,a′) (cid:17) θ
a,s∈D˜ a′,s′∈D˜ η
42
--- PAGE 44 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
800
600
400
200
0
200
400
600
0.0 0.2 0.4 0.6 0.8 1.0
Environment steps (million)
drawer
edosipE
A2C, 1 epoch 1012
A2C, 5 epochs
REINFORCE, 1 epoch
109
REINFORCE, 5 epochs 106
103
100
10 3
10 6
10 9
0.0 0.2 0.4 0.6 0.8 1.0
Environment steps (million)
ecnegrevid
LK
A2C, 1 epoch
A2C, 5 epochs
REINFORCE, 1 epoch
REINFORCE, 5 epochs
Figure7: Wecomparetheepisodereward(left)andKLdivergence(right)fordifferentnumbersofupdateepochsfor
A2CandREINFORCEonHalfCheetah.
L isthetemperatureloss
η
L (η)=ηε +ηln (cid:34) 1 (cid:88) exp (cid:18) Aˆ ϕ (s,a) (cid:19)(cid:35) (37)
η η |D˜| η
a,s∈D˜
andL isthetrust-regionloss
ν
1 (cid:88) (cid:18) (cid:18) (cid:104)(cid:104) (cid:105)(cid:105)(cid:19) (cid:2)(cid:2) (cid:3)(cid:3) (cid:0) (cid:1) (cid:19)
L ν (θ,ν)= |D| ν ε ν −sg D KL (π old (·|s)∥π θ (·|s)) +sg ν D KL π old (·|s)∥π θ (·|s) . (38)
s∈D
Letp
θ
(s,a)=π
θ
(a|s)dπθ(s)denotethejointstate-actiondistributionunderpolicyπ
θ
conditionalontheparameters
θ. LetI beabinaryrandomvariablewhethertheupdatedpolicyπ isanimprovementovertheoldpolicyπ ,i.e.
θ old
I =1ifitisanimprovement. Weassumetheprobabilityofπ beinganimprovementisproportionaltothefollowing
θ
expression
(cid:16)A (s,a)(cid:17)
p (I =1|s,a)∝exp πold (39)
θ η
GiventhedesiredoutcomeI =1,weseektheposteriordistributionconditionedonthisevent. Specifically,weseekthe
maximumaposterioriestimate
θ∗ =argmax (cid:2) p (I =1)ρ(θ) (cid:3)
θ
θ
(40)
(cid:2) (cid:3)
=argmax lnp (I =1)+lnρ(θ) ,
θ
θ
whereρissomepriordistributiontobespecified. UsingTheoremD.7,weobtain
(cid:20) (cid:21)
lnp (I =1)=E ln p θ (I =1,S,A) +D (cid:0) ψ∥p (·,·|I =1) (cid:1) , (41)
θ S,A∼ψ ψ(S,A) KL θ
whereψisadistributionoverS×A. Observethat,sincetheKL-divergenceisnon-negative,thefirsttermisalower
boundforlnp (I =1). AkintoEMalgorithms,V-MPOnowiteratesbetweenanexpectation(E)andamaximization
θ
(M)step. IntheE-stepwechoosethevariationaldistributionψ tominimizetheKLdivergenceinEquation(41)to
makethelowerboundastightaspossible. IntheM-step,wemaximizethislowerboundandthepriorlnρ(θ)toobtain
anewestimateofθ∗viaEquation(40).
First,weconsidertheE-step. MinimizingD (ψ∥p (·,·|I =1))w.r.t. ψleadsto
KL θold
ψ(s,a)=p (s,a|I =1)
θold
p (s,a)p (I =1|s,a)
= θold θold
p (I =1)
θold
p (s,a)p (I =1|s,a)
= (cid:82) (cid:82) θold θold
p (s,a)p (I =1|s,a)dads
s∈S a∈A θold θold
43
--- PAGE 45 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
usingBayes’Theorem(TheoremD.2). Samplingfromright-handsideof(39)thusyields
(cid:16) (cid:17)
exp
Aπold (s,a)
ψˆ(s,a)= η ,
(cid:16) (cid:17)
(cid:80)
exp
Aπold (s,a)
a,s∈D η
whichisthevariationaldistributionfoundinthepolicyloss(37).[73]findthatusingonlythehighest50%ofadvantages
perbatch,i.e. replacingDwithD˜,substantiallyimprovesthealgorithm. TheadvantagefunctionA isestimatedby
π
Aˆ ,whichislearnedidenticallyasinA3C.
ϕ
Wederivethetemperaturelosstoautomaticallyadjustthetemperatureηbyapplying(39)totheKLtermin(41),which
wewanttominimize:
(cid:16) (cid:17) (cid:18) p (S,A)p (I =1|S,A) (cid:19)
D ψ∥p(·,·|I =1) =D ψ∥ θold θold
KL KL p (I =1)
θold
(cid:16) (cid:17)
(cid:32) p
θold
(S,A)exp Aπold
η
(S,A) (cid:33)
=D ψ∥
KL p (I =1)
θold
(cid:16) (cid:17)
(cid:90) (cid:90) (cid:32)p
θold
(s,a)exp Aπold
η
(s,a) (cid:33)
=− ψ(s,a)ln dads
ψ(s,a)p (I =1)
θold
s∈Sa∈A
Byapplyingthelogarithmtotheindividualterms,rearrangingandmultiplyingthroughbyηweget
(cid:16) (cid:17) (cid:90) (cid:90) (cid:18) A (s,a)
D ψ∥p(·,·|I =1) =− ψ(s,a) πold +lnp (s,a)
KL η θold
s∈Sa∈A
(cid:19)
−lnp (I =1)−lnψ(s,a) dads
θold
(cid:90) (cid:90) (cid:18)
∝− ψ(s,a) A (s,a)+ηlnp (s,a)−ηlnp (I =1)
πold θold θold
s∈Sa∈A
(cid:19)
−ηlnψ(s,a) dads
(cid:90) (cid:90) (cid:90) (cid:90) ψ(s,a)
=− ψ(s,a)A (s,a)dads+η ψ(s,a)ln dads
πold p (s,a)
θold
s∈Sa∈A s∈Sa∈A
(cid:90) (cid:90)
+λ ψ(s,a)dads
s∈Sa∈A
with λ = ηlnp (I = 1). To optimize η while minimizing the KL term, we transform this into a constrained
θold
optimizationproblemwithaboundontheKLdivergence
(cid:90) (cid:90)
argmax ψ(s,a)A (s,a)dads
πold
ψ
s∈Sa∈A
(cid:90) (cid:90) ψ(s,a)
subjectto ψ(s,a)ln dads≤ε ,
p (s,a) η
θold
s∈Sa∈A
(cid:90) (cid:90)
ψ(s,a)dads=1
s∈Sa∈A
andthenbackintoanunconstrainedproblemviaLagrangianrelaxation,yieldingtheobjectivefunction
(cid:90) (cid:90) (cid:18)
J(ψ,η,λ)= ψ(s,a)A (s,a)dads+η ε
πold η
s∈Sa∈A
(cid:90) (cid:90) ψ(s,a) (cid:19) (cid:18) (cid:90) (cid:90) (cid:19)
− ψ(s,a)ln dads +λ 1− ψ(s,a)dads .
p (s,a)
θold
s∈Sa∈A s∈Sa∈A
44
--- PAGE 46 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Differentiatingw.r.t. ψ(s,a)andsettingtozeroyields
(cid:18) (cid:19) (cid:18) (cid:19)
A (s,a) λ
ψ(s,a)=p (s,a)exp πold exp −1−
θold η η
Normalizingoversandaconfirmsthealreadyattainedsolution
ψ(s,a)=
p
θold
(s,a)exp
(cid:0)Aπold
η
(s,a)(cid:1)
, (42)
(cid:82)
s∈S
(cid:82)
a∈A
p
θold
(s,a)exp
(cid:0)Aπold
η
(s,a)(cid:1)
dads
butnowwecanalsofindtheoptimalη bysubstitutingthissolutionintoJ(ψ,η,λ). Doingsoanddroppingterms
independentofηleadsto
(cid:18) (cid:90) (cid:90) ψ(s,a) (cid:19)
η ε − ψ(s,a)ln dads
η p (s,a)
θold
s∈Sa∈A
(43)
(cid:90) (cid:90) (cid:90) (cid:90)
=ηε +η ψ(s,a)lnp (s,a)dads−η ψ(s,a)lnψ(s,a)dads.
η θold
s∈Sa∈A s∈Sa∈A
BecauseofEquation(42),wehave
(cid:16) (cid:17)
p
θold
(s,a)exp
Aπold
η
(s,a)
ηψ(s,a)lnψ(s,a)=ηψ(s,a)ln
(cid:16) (cid:17)
(cid:82)
s∈S
(cid:82)
a∈A
p
θold
(s,a)exp
Aπold
η
(s,a)
dads
(cid:32) (cid:90) (cid:90) (cid:18) A (s,a) (cid:19) (cid:33)
=ψ(s,a) ηlnp (s,a)+A (s,a)−ηln p (s,a)exp πold dads ,
θold πold θold η
s∈Sa∈A
wherethefirstsummandcancelsoutthesecondtermin(43)andthesecondsummandnolongerdependsonηandthus
canbedropped. Hence,weobtainthetemperaturelossfunction
(cid:18)(cid:90) (cid:90) (cid:18) A (s,a) (cid:19) (cid:19)
L (η)=ηε +ηln exp πold dads (44)
η η η
s∈Sa∈A
throughwhichwecanoptimizeηusinggradientdescent.
Giventhenon-parametricsample-basedvariationaldistributionψ(s,a),theM-stepnowoptimizesthepolicyparameters
θ. Basedon(40),wewanttomaximizethediscussedlowerbound,i.e. minimize
(cid:90) (cid:90) p (I =1,s,a)
− ψ(s,a)ln θ dads−lnp(θ)
ψ(s,a)
s∈Sa∈A
tofindnewpolicyparametersθ. UsingEquations(42)and(39),thefirsttermbecomes
(cid:90) (cid:90) p (I =1,s,a)
− ψ(s,a)ln θ dads
ψ(s,a)
s∈Sa∈A
(cid:90) (cid:90) p (I =1|s,a)p (s,a)
=− ψ(s,a)ln θ θ dads
ψ(s,a)
s∈Sa∈A
=− (cid:90) (cid:90) ψ(s,a)ln (cid:18) exp (cid:0)Aπold η (s,a)(cid:1) p θ (s,a) 1 (cid:19) dads
s∈Sa∈A
p
θold
(s,a)exp
(cid:0)Aπold
η
(s,a)(cid:1)(cid:82)
s∈S
(cid:82)
a∈A
p
θold
(s,a)exp
(cid:0)Aπold
η
(s,a)(cid:1)
dads
(cid:90) (cid:90) (cid:18) p (s,a) 1 (cid:19)
=− ψ(s,a)ln θ dads.
s∈Sa∈A
p
θold
(s,a)(cid:82)
s∈S
(cid:82)
a∈A
p
θold
(s,a)exp
(cid:0)Aπold
η
(s,a)(cid:1)
dads
45
--- PAGE 47 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
Usingp
θ
(s,a)=π
θ
(a|s)dπθ(s),assumingthestatedistributiondπ tobeindependentofθanddroppingtermsthatdo
notdependonθyields
(cid:18) (cid:90) (cid:90) p (I =1,s,a) (cid:19) (cid:18) (cid:90) (cid:90) (cid:19)
argmin − ψ(s,a)ln θ dads =argmin − ψ(s,a)lnp (s,a)dads
ψ(s,a) θ
θ θ
s∈Sa∈A s∈Sa∈A
(cid:18) (cid:90) (cid:90) (cid:19)
=argmin − ψ(s,a)lnπ (a|s)dads ,
θ
θ
s∈Sa∈A
whichistheweightedmaximumlikelihoodpolicylossasin(36),thatwecomputeonsampledtransitions,effectively
assigningout-of-sampletransitionsaweightofzero.
Ausefulpriorρ(θ)inEquation(40)istokeepthenewpolicyclosetothepreviousoneasinTRPOandPPO.This
translatesto
ρ(θ)≈−νE (cid:2) D (π (·|S)∥π (·|S)) (cid:3) .
S∼dπold KL old θ
Sinceoptimizingtheresultingsample-basedmaximumlikelihoodobjectivedirectlytendstoresultinoverfitting,this
priorisinsteadtransformedintoaconstraintontheKL-divergencewithboundε ,i.e.
ν
(cid:18) (cid:90) (cid:90) p (I =1,s,a) (cid:19)
argmin − ψ(s,a)ln θ dads
ψ(s,a)
θ
s∈Sa∈A
subjectto E S∼dπold (cid:104) D KL (cid:0) π old (·|S)∥π θ (·|S) (cid:1)(cid:105) ≤ε ν .
Toemploygradient-basedoptimization,weuseLagrangianrelaxationtotransformthisconstraintoptimizationproblem
backintotheunconstrainedproblem
J(θ,ν)=L π (θ)+ν (cid:0) ε ν −E S∼dπold (cid:2) D KL (π old (·|S)∥π θ (·|S)) (cid:3)(cid:1) . (45)
Thisproblemissolvedbyalternatingbetweenoptimizingforθ andν viagradientdescentinacoordinate-descent
strategy. Usingthestop-gradientoperatorsg[[·]],theobjectivecanequivalentlytothisstrategyberewrittenforas
L (θ,ν)=ν (cid:18) ε −E (cid:20) sg (cid:104)(cid:104) D (cid:0) π (·|S)∥π (·|S) (cid:1)(cid:105)(cid:105)(cid:21)(cid:19) +sg (cid:2)(cid:2) ν (cid:3)(cid:3)E (cid:104) D (cid:0) π (·|S)∥π (·|S) (cid:1)(cid:105) .
ν ν S∼dπold KL θold θ S∼dπold KL θold θ
SamplingthisgivesEquation(38). ηandν areLagrangianmultipliersandhencemustbepositive. Weenforcethisby
projectingthecomputedvaluestosmallpositivevaluesη andν respectivelyifnecessary.
min min
D AuxiliaryTheory
Here,welistarangeofwell-knowndefinitionsandresultsthatweuseinourwork.
DefinitionD.1. (CompactSpace)AtopologicalspaceX iscalledcompactifforeverysetS ofopencoversofX,there
existsafinitesubsetS′ ⊂S thatalsoisanopencoverofX.
TheoremD.2. (Bayes’Theorem)Let(Ω,A,P)beaprobabilityspaceand (cid:83) B beadisjointandfinitepartitionof
i∈I i
ΩwithB ∈AandP(B )>0fori ∈I. Then,forallA∈Aandallk ∈I
i i
P(A|B )P(B )
P(B |A)= k k .
k (cid:80) P(A |B )P(B )
i∈I i i
TheoremD.3. LetX bearandomvariable. Then,
Var[X]=E(cid:2) X2(cid:3) −E(cid:2)
X
(cid:3)2
.
DefinitionD.4. (Entropy)Let(Ω,A,P)beaprobabilityspaceandX ∼Pbearandomvariable. TheentropyofX is
givenby
H(X):=E X∼P (cid:2) −lnP(X) (cid:3) .
46
--- PAGE 48 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
DefinitionD.5. (Kullback-LeiblerDivergence)ForanymeasurablespaceAandprobabilitydensitiespandqofthe
respectivedistributionsP andQ,theKullback-LeiblerdivergenceorrelativeentropyfromQtoP isgivenby
(cid:90) p(a)
D (p∥q):= p(a)ln da.
KL q(a)
a∈A
DefinitionD.6. (TotalVariationDivergence)ForanymeasurablespaceAandprobabilitydensitiespandq ofthe
respectivedistributionsP andQ,thetotalvariationvariancefromQtoP isgivenby
1 (cid:90)
D (p∥q):= p(a)−q(a)da.
TV 2
a∈A
TheoremD.7. Let(Ω,A)beameasurablespaceandpandψbeprobabilitymeasuresonthatspace. LetandX ∈A
andZ ∈A. Then,
(cid:20) (cid:21)
p(X,Z)
lnp(X)=E ln +D (ψ∥p(·|X)).
Z∼ψ ψ(Z) KL
TheoremD.8. LetX bearandomvariable. Then,
minE(cid:2) (X−a)2(cid:3) =E[X].
a
TheoremD.9. Let(A,Σ)beameasurablespacewithσ-finitemeasuresµandν suchthatν isabsolutelycontinuous
(cid:82)
inµ. LetgbeaRadon-Nikodymderivativeofν w.r.t. µ,i.e. ν(A)= gdµforallA∈Σ. Let,f beaν-integrable
A
function. Then,
(cid:90) (cid:90)
f dν = (f ·g)dµ.
A A
Theorem D.10. (Leibniz Integral Rule) Let X be an open subset of Rd, d ∈ N. Let A be a measurable set and
f: X×A→Rbeafunctionwhichsatisfies
1. f(x,a)isaLebesgue-integrablefunctionofaforallx∈X.
2. Foralmostalla∈A,allpartialderivativesexistforallx∈X.
3. There exists some integrable function g: A → R with |∇ f(x,a)| ≤ g(a) for all x ∈ X and almost all
x
a∈A.
Then,forallx∈X wehave
(cid:90) (cid:90)
∇ f(x,a)da= ∇ f(x,a)da
x x
a∈A a∈A
TheoremD.11. (Fubini’sTheorem)LetA andA bemeasurablespaceswithmeasuresµ andµ andf: A ×A →
1 2 1 2 1 2
Rbemeasurableandintegrablew.r.t. theproductmeasureµ ⊗µ ,i.e. (cid:82) |f|d(µ ⊗µ )<∞orf ≥0almost
1 2 A1×A2 1 2
everywhere. Then,f(x,y)isintegrableforalmostallxandyand
(cid:90) (cid:90) (cid:90) (cid:90)
f(x,y)dµ (x)dµ (y)= f(x,y)dµ (y)dµ (x)
1 2 2 1
A1A2 A2A1
47
--- PAGE 49 ---
TheDefinitiveGuidetoPolicyGradientsinDeepReinforcementLearning
TheoremD.12. (Taylor’sTheorem-one-dimensional)Letk ∈Nandletf: R→Rbek-timesdifferentiableata∈R.
Then,thereexistsafunctionh : R→Rsuchthat
k
(cid:88) k f(i)(a)
f(x)= (x−a)i+h (x)(x−a)k.
i! k
i=0
TheoremD.13. (MonotoneConvergenceTheorem)Let (cid:0) x (cid:1)∞ ⊂ Rbeaboundedandmonotonicallyincreasing
n n=0
sequence. Then,thesequenceconverges,i.e. lim x existsandisfinite.
n→∞ n
TheoremD.14. (Bolzano-WeierstrassTheorem)Let (cid:0) x (cid:1)∞ ⊂Rd,d∈Nbeaboundedsequence. Then,thereexists
n n=0
(cid:0) (cid:1)∞
someconvergentsubsequence x .
ni i=0
TheoremD.15. (Berge’sMaximumTheorem)LetX andΘbetopologicalspaces,f: X×Θ→Rbecontinuouson
X×ΘandC: Θ⇒X beacompact-valuedcorrespondencewithC(θ)̸=∅forallθ ∈Θ. Let
f∗(θ)=sup (cid:8) f(x,θ)|x ∈C(θ) (cid:9)
and
C∗(θ)=argmax (cid:8) f(x,θ)|x∈C(θ) (cid:9) = (cid:8) x∈C(θ)|f(x,θ)=f∗(θ) (cid:9) .
IfC iscontinuousatθ,thenf∗iscontinuousandC∗isupperhemicontinuouswithnonemptyandcompactvalues.
DefinitionD.16. (GâteauxDerivative)LetX andY belocallyconvextopologicalspaces,letU beanopensubsetof
X andF: U →Y. TheGâteauxderivativeofF atx∈U inthedirectiond∈X isdefinedas
F(x+rd)−F(x)
dF(x,d)= lim .
h→0 r
48