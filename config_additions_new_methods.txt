    # ========================================================================
    # 🎯 ROLLOUT 2 - PARAMÈTRES NOUVELLES MÉTHODES OPTIMISÉES
    # ========================================================================
    #
    # ⚠️  SECTION CRITIQUE - PARAMÈTRES NOUVELLES MÉTHODES CENTRALISÉS
    # Tous les paramètres des nouvelles méthodes optimisées du Rollout 2
    # ========================================================================

    # Confidence Thresholds
    rollout2_confidence_threshold_0_8: float = 0.8  # if signal_confidence > 0.8:
    rollout2_confidence_threshold_8: int = 8  # if signal_confidence > 0.8:
    rollout2_confidence_threshold_> 0_8: float = > 0.8  # if signal_confidence > 0.8:
    rollout2_confidence_threshold_0_6: float = 0.6  # elif signal_confidence > 0.6:
    rollout2_confidence_threshold_6: int = 6  # elif signal_confidence > 0.6:
    rollout2_confidence_threshold_> 0_6: float = > 0.6  # elif signal_confidence > 0.6:
    rollout2_confidence_threshold_0_4: float = 0.4  # elif signal_confidence > 0.4:
    rollout2_confidence_threshold_4: int = 4  # elif signal_confidence > 0.4:
    rollout2_confidence_threshold_> 0_4: float = > 0.4  # elif signal_confidence > 0.4:
    rollout2_confidence_threshold_0_8: float = 0.8  # if signal_confidence > 0.8:
    rollout2_confidence_threshold_8: int = 8  # if signal_confidence > 0.8:
    rollout2_confidence_threshold_> 0_8: float = > 0.8  # if signal_confidence > 0.8:
    rollout2_confidence_threshold_0_6: float = 0.6  # elif signal_confidence > 0.6:
    rollout2_confidence_threshold_6: int = 6  # elif signal_confidence > 0.6:
    rollout2_confidence_threshold_> 0_6: float = > 0.6  # elif signal_confidence > 0.6:
    rollout2_confidence_threshold_0_4: float = 0.4  # elif signal_confidence > 0.4:
    rollout2_confidence_threshold_4: int = 4  # elif signal_confidence > 0.4:
    rollout2_confidence_threshold_> 0_4: float = > 0.4  # elif signal_confidence > 0.4:

    # Default Values
    rollout2_default_confidence: float = 0.5  # signal_confidence = signal.get('confidence', 0.5)
    rollout2_default_confidence: float = 0.5  # signal_confidence = signal.get('confidence', 0.5)

    # Other Constants
    rollout2_constant_5: int = 5  # signal_confidence = signal.get('confidence', 0.5)
    rollout2_constant_5: int = 5  # signal_confidence = signal.get('confidence', 0.5)
    rollout2_constant_2: int = 2  # position_type = 'impair' if hand_number % 2 == 1 else 'pair'
    rollout2_constant_1: int = 1  # position_type = 'impair' if hand_number % 2 == 1 else 'pair'
    rollout2_constant_1: int = 1  # pbt_outcome = hands_data[hand_number - 1].pbt_result
    rollout2_constant_2: int = 2  # position_type = 'impair' if hand_number % 2 == 1 else 'pair'
    rollout2_constant_1: int = 1  # position_type = 'impair' if hand_number % 2 == 1 else 'pair'
    rollout2_constant_1: int = 1  # pbt_outcome = hands_data[hand_number - 1].pbt_result
