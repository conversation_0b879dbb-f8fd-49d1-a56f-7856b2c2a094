#!/usr/bin/env python3
"""
Test final de vérification de la centralisation des rollouts et clusters

Ce script vérifie que toutes les valeurs hardcodées critiques pour les rollouts
et clusters sont bien centralisées dans AZRConfig.
"""

import sys
import os

def test_centralisation_finale():
    """Test final de la centralisation"""
    
    print("🏁 TEST FINAL DE CENTRALISATION ROLLOUTS & CLUSTERS")
    print("=" * 70)
    
    try:
        from azr_baccarat_predictor import AZRConfig
        config = AZRConfig()
        
        # Test des paramètres rollouts critiques
        rollout_params_critiques = [
            # Rollout 1
            'rollout1_impair_consecutive_common',
            'rollout1_impair_consecutive_rare', 
            'rollout1_pair_consecutive_very_common',
            'rollout1_global_strength_threshold',
            
            # Rollout 2 - <PERSON><PERSON><PERSON> critiques
            'rollout2_exploitation_threshold_high',
            'rollout2_exploitation_threshold_medium',
            'rollout2_signal_detection_threshold',
            'rollout2_signal_strength_threshold',
            'rollout2_exploitation_readiness_threshold',
            'rollout2_consistency_weight',
            'rollout2_correlation_weight',
            
            # Rollout 2 - Confiances
            'rollout2_base_confidence_high',
            'rollout2_base_confidence_medium',
            'rollout2_base_confidence_low',
            'rollout2_confidence_limit_max',
            'rollout2_confidence_limit_min',
            
            # Rollout 2 - Priorités
            'rollout2_priority_weight_1',
            'rollout2_priority_weight_2',
            'rollout2_priority_weight_3',
            'rollout2_priority_weight_4',
            
            # Rollout 3
            'rollout3_cluster_confidence_default',
            'rollout3_neutral_evaluation_value',
            'rollout3_cluster_confidence_threshold_high'
        ]
        
        # Test des paramètres clusters critiques
        cluster_params_critiques = [
            # Confiances clusters
            'cluster_base_confidence_high',
            'cluster_base_confidence_medium',
            'cluster_base_confidence_low',
            'cluster_confidence_limit_max',
            'cluster_confidence_limit_medium',
            
            # Ajustements clusters
            'cluster_confidence_adjustment_small',
            'cluster_confidence_adjustment_medium',
            'cluster_confidence_adjustment_large',
            
            # Poids clusters
            'cluster_confidence_sequence_weight',
            'cluster_confidence_evaluation_weight',
            'cluster_confidence_analysis_weight',
            'cluster_confidence_signals_weight',
            
            # Seuils clusters
            'cluster_consensus_agreement_threshold',
            'cluster_accuracy_save_threshold',
            'cluster_test_accuracy_high_threshold',
            'cluster_test_accuracy_low_threshold',
            
            # Facteurs clusters
            'cluster_weight_factor_high',
            'cluster_weight_factor_medium',
            'cluster_boost_factor'
        ]
        
        # Tester chaque catégorie
        print(f"\n🎯 ROLLOUTS - PARAMÈTRES CRITIQUES")
        print("-" * 50)
        
        rollouts_ok = 0
        for param in rollout_params_critiques:
            if hasattr(config, param):
                value = getattr(config, param)
                print(f"  ✅ {param}: {value}")
                rollouts_ok += 1
            else:
                print(f"  ❌ {param}: MANQUANT")
        
        rollout_pourcentage = (rollouts_ok / len(rollout_params_critiques)) * 100
        print(f"  📊 Rollouts: {rollouts_ok}/{len(rollout_params_critiques)} ({rollout_pourcentage:.0f}%)")
        
        print(f"\n🏗️ CLUSTERS - PARAMÈTRES CRITIQUES")
        print("-" * 50)
        
        clusters_ok = 0
        for param in cluster_params_critiques:
            if hasattr(config, param):
                value = getattr(config, param)
                print(f"  ✅ {param}: {value}")
                clusters_ok += 1
            else:
                print(f"  ❌ {param}: MANQUANT")
        
        cluster_pourcentage = (clusters_ok / len(cluster_params_critiques)) * 100
        print(f"  📊 Clusters: {clusters_ok}/{len(cluster_params_critiques)} ({cluster_pourcentage:.0f}%)")
        
        # Résultat global
        total_params = len(rollout_params_critiques) + len(cluster_params_critiques)
        total_ok = rollouts_ok + clusters_ok
        pourcentage_global = (total_ok / total_params) * 100
        
        print(f"\n" + "=" * 70)
        print(f"🏁 RÉSULTAT FINAL")
        print("=" * 70)
        print(f"📊 Paramètres centralisés: {total_ok}/{total_params} ({pourcentage_global:.1f}%)")
        
        if pourcentage_global >= 95:
            print("🎉 CENTRALISATION EXCELLENTE!")
            print("✅ Quasi-totalité des paramètres rollouts/clusters centralisés!")
            return True
        elif pourcentage_global >= 85:
            print("👍 CENTRALISATION TRÈS BONNE!")
            print("✅ Majorité des paramètres rollouts/clusters centralisés!")
            return True
        elif pourcentage_global >= 70:
            print("⚠️ CENTRALISATION CORRECTE")
            print("🔧 Quelques paramètres restent à centraliser")
            return False
        else:
            print("❌ CENTRALISATION INSUFFISANTE")
            print("🚨 Beaucoup de paramètres restent à centraliser")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def test_utilisation_parametres():
    """Test que les paramètres sont bien utilisés dans le code"""
    
    print(f"\n🔍 TEST D'UTILISATION DES PARAMÈTRES")
    print("=" * 50)
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Compter les utilisations de self.config
        utilisations_config = contenu.count('self.config.')
        
        # Compter les valeurs hardcodées restantes (approximatif)
        import re
        valeurs_hardcodees = len(re.findall(r'= 0\.[0-9]+|> 0\.[0-9]+|< 0\.[0-9]+', contenu))
        
        print(f"✅ Utilisations self.config: {utilisations_config}")
        print(f"⚠️ Valeurs hardcodées restantes: {valeurs_hardcodees}")
        
        ratio = utilisations_config / max(valeurs_hardcodees, 1)
        
        if ratio > 0.5:
            print("🎯 EXCELLENT ratio config/hardcodé!")
            return True
        elif ratio > 0.3:
            print("👍 BON ratio config/hardcodé")
            return True
        else:
            print("⚠️ Ratio config/hardcodé à améliorer")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test d'utilisation: {e}")
        return False

def main():
    """Fonction principale"""
    
    # Test de centralisation
    centralisation_ok = test_centralisation_finale()
    
    # Test d'utilisation
    utilisation_ok = test_utilisation_parametres()
    
    # Résultat final
    print(f"\n" + "=" * 80)
    print("🏆 BILAN FINAL DE LA CENTRALISATION")
    print("=" * 80)
    
    if centralisation_ok and utilisation_ok:
        print("🎉 SUCCÈS TOTAL!")
        print("✅ Centralisation excellente des rollouts et clusters")
        print("✅ Utilisation optimale des paramètres centralisés")
        print("🚀 Système AZR parfaitement organisé!")
        return True
    elif centralisation_ok:
        print("👍 SUCCÈS PARTIEL")
        print("✅ Centralisation excellente")
        print("⚠️ Utilisation des paramètres à optimiser")
        return True
    else:
        print("⚠️ AMÉLIORATIONS NÉCESSAIRES")
        print("🔧 Centralisation à compléter")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
