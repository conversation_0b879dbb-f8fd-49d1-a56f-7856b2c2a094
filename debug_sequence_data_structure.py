#!/usr/bin/env python3
"""
Debug de la structure des données de séquence

Ce script analyse la structure exacte des données retournées par chaque rollout
pour identifier pourquoi le score d'évaluation reste à 0.000.
"""

import sys
import json
from typing import Dict, List

class MockHand:
    """Classe mock pour simuler un objet Hand compatible avec les rollouts"""
    
    def __init__(self, hand_number: int, result: str):
        self.hand_number = hand_number
        self.result = result
    
    @property
    def pbt_result(self) -> str:
        """Alias pour result - compatibilité avec les rollouts"""
        return self.result

def create_test_data() -> Dict:
    """Crée des données de test simples"""
    
    results = ['P', 'B', 'P', 'B', 'P']
    hands_history = []
    for i, result in enumerate(results, 1):
        hand = MockHand(hand_number=i, result=result)
        hands_history.append(hand)
    
    return {
        'hands_history': hands_history,
        'metadata': {
            'total_hands': len(hands_history),
            'session_id': 'debug_session'
        }
    }

def debug_rollout_data_structures():
    """Debug principal des structures de données"""
    
    print("🔍 DEBUG STRUCTURES DONNÉES ROLLOUTS")
    print("=" * 40)
    
    try:
        from azr_baccarat_predictor import AZRCluster, AZRConfig
        
        config = AZRConfig()
        cluster = AZRCluster(cluster_id=1, config=config)
        sequence_data = create_test_data()
        
        # ÉTAPE 1 : ROLLOUT 1
        print("\n📊 ÉTAPE 1 : STRUCTURE ROLLOUT 1")
        print("-" * 35)
        
        analyzer_report = cluster._rollout_analyzer(sequence_data)
        
        print(f"Type analyzer_report: {type(analyzer_report)}")
        print(f"Clés principales: {list(analyzer_report.keys())}")
        
        if 'synthesis' in analyzer_report:
            synthesis = analyzer_report['synthesis']
            print(f"Type synthesis: {type(synthesis)}")
            print(f"Analysis quality: {synthesis.get('analysis_quality', 'N/A')}")
            print(f"Dominant correlations: {len(synthesis.get('dominant_correlations', []))}")
        
        # ÉTAPE 2 : ROLLOUT 2
        print("\n🎲 ÉTAPE 2 : STRUCTURE ROLLOUT 2")
        print("-" * 35)
        
        generated_sequences = cluster._rollout_generator(analyzer_report)
        
        print(f"Type generated_sequences: {type(generated_sequences)}")
        print(f"Nombre de séquences: {len(generated_sequences)}")
        
        if generated_sequences:
            first_sequence = generated_sequences[0]
            print(f"Type première séquence: {type(first_sequence)}")
            
            if isinstance(first_sequence, list):
                print(f"Longueur première séquence: {len(first_sequence)}")
                print("Contenu première séquence:")
                for i, item in enumerate(first_sequence[:3]):
                    print(f"  Item {i}: {type(item)} = {item}")
                    if isinstance(item, dict):
                        print(f"    Clés: {list(item.keys())}")
        
        # ÉTAPE 3 : ROLLOUT 3 - AVANT TRAITEMENT
        print("\n🎯 ÉTAPE 3A : ROLLOUT 3 - AVANT TRAITEMENT")
        print("-" * 45)
        
        # Simuler le traitement du Rollout 3
        if generated_sequences:
            sequence = generated_sequences[0]
            print(f"Séquence brute reçue: {type(sequence)}")
            
            if isinstance(sequence, list):
                summary = {}
                actual_sequence_data = []
                
                for item in sequence:
                    if isinstance(item, dict) and item.get('type') == 'sequence_enrichment_summary':
                        summary = item
                        print(f"Summary trouvé: {summary}")
                    elif isinstance(item, dict) and 'predicted_pbt' in item:
                        pbt_value = item.get('predicted_pbt', 'B')
                        actual_sequence_data.append(pbt_value)
                        print(f"PBT extrait: {pbt_value} de {item}")
                    elif isinstance(item, str):
                        actual_sequence_data.append(item)
                        print(f"String directe: {item}")
                
                print(f"Sequence_data finale: {actual_sequence_data}")
                print(f"Type des éléments: {[type(x) for x in actual_sequence_data]}")
        
        # ÉTAPE 3B : ROLLOUT 3 - ÉVALUATION
        print("\n🎯 ÉTAPE 3B : ROLLOUT 3 - ÉVALUATION")
        print("-" * 40)
        
        final_prediction = cluster._rollout_predictor(generated_sequences, analyzer_report)
        
        print(f"Type final_prediction: {type(final_prediction)}")
        print(f"Clés: {list(final_prediction.keys())}")
        print(f"Sequence: {final_prediction.get('sequence', 'N/A')}")
        print(f"Evaluation score: {final_prediction.get('evaluation_score', 'N/A')}")
        print(f"Next hand prediction: {final_prediction.get('next_hand_prediction', 'N/A')}")
        print(f"Type next hand: {type(final_prediction.get('next_hand_prediction', 'N/A'))}")
        
        # ÉTAPE 4 : DIAGNOSTIC DÉTAILLÉ
        print("\n🔍 ÉTAPE 4 : DIAGNOSTIC DÉTAILLÉ")
        print("-" * 35)
        
        # Vérifier si l'évaluation est correctement calculée
        if 'evaluation_score' in final_prediction:
            eval_score = final_prediction['evaluation_score']
            print(f"Evaluation score détaillé: {eval_score} (type: {type(eval_score)})")
            
            # Essayer de tracer d'où vient le problème
            if eval_score == 0.0:
                print("❌ PROBLÈME: Evaluation score = 0.0")
                print("Causes possibles:")
                print("1. Méthode _evaluate_sequence_quality retourne 0")
                print("2. Clé 'evaluation.total_score' manquante")
                print("3. Conversion de type incorrecte")
        
        # Vérifier la structure de next_hand_prediction
        next_hand = final_prediction.get('next_hand_prediction')
        if isinstance(next_hand, dict):
            print("❌ PROBLÈME: next_hand_prediction est un dict au lieu d'une string")
            print(f"Contenu: {next_hand}")
        elif isinstance(next_hand, str):
            print(f"✅ next_hand_prediction correct: '{next_hand}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du debug : {type(e).__name__}: {e}")
        import traceback
        print(f"Détails : {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 LANCEMENT DEBUG STRUCTURES DONNÉES")
    print("=" * 40)
    
    success = debug_rollout_data_structures()
    
    if success:
        print("\n✅ DEBUG TERMINÉ")
    else:
        print("\n❌ DEBUG ÉCHOUÉ")
        sys.exit(1)
