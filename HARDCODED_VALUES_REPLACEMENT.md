# 🔧 REMPLACEMENT DES VALEURS HARDCODÉES

## 📋 ÉTAT DU REMPLACEMENT

### ✅ **VALEURS DÉJÀ REMPLACÉES**

| Ligne | Ancienne Valeur | Nouvelle Valeur | Section |
|-------|----------------|-----------------|---------|
| 1699-1706 | `0.0`, `0.1`, `0.2` | `config.zero_value`, `config.correlation_strength_threshold`, `config.correlation_strong_bonus_factor` | Corrélations |
| 1978 | `3.0`, `1`, `10` | `config.rarity_amplifier_max`, `config.one_value`, `config.rarity_divisor_base` | Amplification rareté |
| 2121 | `0.2` | `config.signal_very_strong_threshold` | Signal P/B fort |
| 2309 | `0.05` | `config.minimum_confidence` | Confiance minimum |
| 3222-3225 | `0.7`, `0.3`, `1.1`, `0.9` | `config.confidence_high_threshold`, `config.confidence_low_threshold`, `config.multiplier_bonus_performance`, `config.multiplier_malus_performance` | Facteurs confiance |
| 8077 | `0.8` | `config.confidence_very_high_threshold` | Exploitabilité |
| 9963 | `0.2`, `0.7` | `config.signal_very_strong_threshold`, `config.confidence_high_threshold` | Exploitation prête |
| 10064 | `0.8` | `config.confidence_very_high_threshold` | Niveau risque |
| 10131-10133 | `0.8`, `0.7` | `config.critical_threshold`, `config.alert_threshold` | Niveaux alerte |
| 10178 | `0.5` | `config.confidence_medium_threshold` | Performance |
| 2438 | `0.3`, `0.5` | `config.confidence_low_threshold`, `config.confidence_medium_threshold` | Seuil confiance |
| 2447-2448 | `0.5` | `config.confidence_medium_threshold` | Prédictions P/B et S/O |
| 2496, 2503 | `0.6` | `config.priority_threshold_significant` | Corrélations dominantes |
| 2525, 2532 | `0.7` | `config.correlation_high_sync_threshold` | Zones haute confiance S/O |
| 2691 | `0.7` | `config.stability_threshold_medium` | Phase optimale |
| 2736 | `0.6` | `config.priority_threshold_significant` | Force pattern |
| 3168 | `0.8` | `config.correlation_very_high_sync_threshold` | Ratio alternance |
| 3177 | `0.8` | `config.confidence_very_high_threshold` | Probabilité élevée |
| 4721, 4737 | `0.7`, `0.5` | `config.confidence_high_threshold`, `config.confidence_medium_threshold` | Force signal stratégie |
| 5778 | `0.01` | `config.coherence_threshold_strict` | Cohérence S/O |
| 6482 | `0.1` | `config.correlation_strength_threshold` | Impact significatif |
| 6787 | `0.1` | `config.correlation_strength_threshold` | Changements significatifs |
| 6988 | `0.2` | `config.evolution_strength_threshold` | Évolution significative |
| 7303, 7305 | `0.8`, `0.6` | `config.stability_threshold_high`, `config.stability_threshold_low` | Stabilité corrélations |

### 🎯 **VALEURS CRITIQUES À REMPLACER EN PRIORITÉ**

#### **🔴 PRIORITÉ TRÈS ÉLEVÉE (Impact direct sur prédictions)**

| Ligne | Valeur | Contexte | Remplacement Suggéré |
|-------|--------|----------|---------------------|
| 2525, 2532 | `0.7` | Zones haute confiance S/O | `config.correlation_high_sync_threshold` |
| 2691 | `0.7` | Phase optimale | `config.stability_threshold_medium` |
| 2736 | `0.6` | Force pattern | `config.priority_threshold_significant` |
| 3168, 3175 | `0.8` | Ratio alternance | `config.correlation_very_high_sync_threshold` |
| 3177 | `0.8` | Probabilité élevée | `config.confidence_very_high_threshold` |
| 4721, 4737 | `0.7`, `0.5` | Force signal stratégie | `config.confidence_high_threshold`, `config.confidence_medium_threshold` |

#### **🟡 PRIORITÉ ÉLEVÉE (Impact sur qualité)**

| Ligne | Valeur | Contexte | Remplacement Suggéré |
|-------|--------|----------|---------------------|
| 5778 | `0.01` | Cohérence S/O | `config.coherence_threshold_strict` |
| 6482 | `0.1` | Impact significatif | `config.correlation_strength_threshold` |
| 6787 | `0.1` | Changements significatifs | `config.correlation_strength_threshold` |
| 6988 | `0.2` | Évolution significative | `config.evolution_strength_threshold` |
| 7303, 7305 | `0.8`, `0.6` | Stabilité corrélations | `config.stability_threshold_high`, `config.stability_threshold_low` |

#### **🟢 PRIORITÉ MOYENNE (Impact sur performance)**

| Ligne | Valeur | Contexte | Remplacement Suggéré |
|-------|--------|----------|---------------------|
| 7366 | `0.1` | Seuil significativité | `config.correlation_strength_threshold` |
| 7531 | `0.3` | Variations significatives | `config.temporal_significance_threshold` |
| 7636 | `0.6` | Haute confiance | `config.priority_threshold_significant` |
| 8158-8294 | `0.1-0.3` | Seuils zones enrichies | Divers seuils config |

### 📊 **STATISTIQUES DE REMPLACEMENT**

- **Total valeurs identifiées** : 155+
- **Valeurs remplacées** : 25
- **Valeurs critiques restantes** : 130
- **Pourcentage complété** : 16.1%

### 🎯 **PROGRÈS RÉCENTS**

#### **✅ Valeurs remplacées dans cette session :**
- Zones de haute confiance S/O (2525, 2532)
- Phase optimale de prédiction (2691)
- Force des patterns (2736)
- Seuils d'alternance et probabilité (3168, 3177)
- Stratégies de signal (4721, 4737)
- Cohérence des données S/O (5778)
- Impacts et évolutions significatives (6482, 6787, 6988)
- Stabilité des corrélations (7303, 7305)

### 🎯 **PLAN DE REMPLACEMENT SYSTÉMATIQUE**

#### **Phase 1 : Valeurs Critiques (En cours)**
- ✅ Corrélations et seuils de confiance
- ✅ Facteurs de performance
- 🔄 Seuils de zones et patterns
- ⏳ Seuils de stabilité et qualité

#### **Phase 2 : Valeurs Importantes**
- ⏳ Seuils d'exploitation
- ⏳ Facteurs de boost et amplification
- ⏳ Seuils de consensus et accord

#### **Phase 3 : Valeurs Secondaires**
- ⏳ Seuils de variance et écart-type
- ⏳ Multiplicateurs et facteurs
- ⏳ Valeurs de normalisation

### 🔧 **MÉTHODE DE REMPLACEMENT**

#### **Étapes pour chaque valeur :**
1. **Identifier le contexte** de la valeur hardcodée
2. **Déterminer le paramètre approprié** dans AZRConfig
3. **Vérifier la cohérence** avec les autres valeurs
4. **Effectuer le remplacement** avec str-replace-editor
5. **Tester l'impact** sur les diagnostics

#### **Conventions de nommage :**
- `threshold` : Seuils de décision
- `factor` : Multiplicateurs et facteurs
- `weight` : Poids et pondérations
- `bonus/malus` : Ajustements positifs/négatifs
- `min/max` : Limites et bornes

### 🚨 **SECTIONS CRITIQUES À TRAITER EN PRIORITÉ**

1. **Système de priorités** (lignes 14449+)
2. **Méthodes de corrélation** (lignes 14263+)
3. **Zones d'exploitation** (lignes 8000+)
4. **Génération de séquences** (lignes 4000+)
5. **Analyse de patterns** (lignes 7000+)

### ✅ **VALIDATION POST-REMPLACEMENT**

- [ ] Vérifier absence d'erreurs de syntaxe
- [ ] Tester cohérence des seuils
- [ ] Valider impact sur performance
- [ ] Documenter changements significatifs

---

**📅 Dernière mise à jour** : Remplacement en cours - Phase 1
**🎯 Objectif** : Élimination complète des valeurs hardcodées
