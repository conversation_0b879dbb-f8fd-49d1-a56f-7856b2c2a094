#!/usr/bin/env python3
"""
Analyse des méthodes inutilisées dans azr_baccarat_predictor.py

Ce script identifie toutes les méthodes définies mais jamais appelées.
"""

import re
import sys
from typing import Set, List, Tuple

def extract_method_definitions(file_content: str) -> Set[str]:
    """Extrait toutes les définitions de méthodes"""
    method_pattern = r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
    methods = set()
    
    for match in re.finditer(method_pattern, file_content):
        method_name = match.group(1)
        # Exclure les méthodes spéciales Python
        if not method_name.startswith('__') or method_name in ['__init__', '__post_init__']:
            methods.add(method_name)
    
    return methods

def extract_method_calls(file_content: str) -> Set[str]:
    """Extrait tous les appels de méthodes"""
    calls = set()
    
    # Pattern pour self.method_name()
    self_call_pattern = r'self\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
    for match in re.finditer(self_call_pattern, file_content):
        calls.add(match.group(1))
    
    # Pattern pour object.method_name()
    obj_call_pattern = r'[a-zA-Z_][a-zA-Z0-9_]*\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
    for match in re.finditer(obj_call_pattern, file_content):
        calls.add(match.group(1))
    
    # Pattern pour method_name() direct
    direct_call_pattern = r'(?<![a-zA-Z0-9_\.])([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
    for match in re.finditer(direct_call_pattern, file_content):
        method_name = match.group(1)
        # Exclure les mots-clés Python et fonctions built-in
        if method_name not in ['if', 'for', 'while', 'def', 'class', 'return', 'print', 
                              'len', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple',
                              'range', 'enumerate', 'zip', 'map', 'filter', 'sum', 'max', 'min',
                              'abs', 'round', 'sorted', 'reversed', 'any', 'all', 'isinstance',
                              'hasattr', 'getattr', 'setattr', 'type', 'super', 'open', 'time']:
            calls.add(method_name)
    
    return calls

def find_method_line_numbers(file_content: str, method_name: str) -> List[int]:
    """Trouve les numéros de ligne où une méthode est définie"""
    lines = file_content.split('\n')
    line_numbers = []
    
    pattern = rf'def\s+{re.escape(method_name)}\s*\('
    
    for i, line in enumerate(lines, 1):
        if re.search(pattern, line):
            line_numbers.append(i)
    
    return line_numbers

def analyze_unused_methods():
    """Analyse principale des méthodes inutilisées"""
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return
    
    print("🔍 ANALYSE DES MÉTHODES INUTILISÉES")
    print("=" * 50)
    
    # Extraire méthodes définies et appelées
    defined_methods = extract_method_definitions(file_content)
    called_methods = extract_method_calls(file_content)
    
    print(f"📊 Méthodes définies: {len(defined_methods)}")
    print(f"📊 Méthodes appelées: {len(called_methods)}")
    
    # Identifier méthodes inutilisées
    unused_methods = defined_methods - called_methods
    
    print(f"\n⚠️  MÉTHODES POTENTIELLEMENT INUTILISÉES: {len(unused_methods)}")
    print("-" * 50)
    
    if unused_methods:
        # Trier par ordre alphabétique
        sorted_unused = sorted(unused_methods)
        
        for i, method in enumerate(sorted_unused, 1):
            line_numbers = find_method_line_numbers(file_content, method)
            line_info = f"ligne {line_numbers[0]}" if line_numbers else "ligne inconnue"
            print(f"{i:3d}. {method:<40} ({line_info})")
    
    # Méthodes utilisées
    used_methods = defined_methods & called_methods
    print(f"\n✅ MÉTHODES UTILISÉES: {len(used_methods)}")
    print("-" * 30)
    
    # Analyse par catégorie
    print(f"\n📊 ANALYSE PAR CATÉGORIE")
    print("-" * 30)
    
    categories = {
        'Rollout': [m for m in unused_methods if 'rollout' in m.lower()],
        'Analyze': [m for m in unused_methods if 'analyze' in m.lower()],
        'Generate': [m for m in unused_methods if 'generate' in m.lower()],
        'Calculate': [m for m in unused_methods if 'calculate' in m.lower()],
        'Extract': [m for m in unused_methods if 'extract' in m.lower()],
        'Interface': [m for m in unused_methods if any(x in m.lower() for x in ['interface', 'create', 'gui'])],
        'Utility': [m for m in unused_methods if any(x in m.lower() for x in ['get_', 'set_', 'load_', 'save_'])],
        'Other': []
    }
    
    # Classer les méthodes non catégorisées
    categorized = set()
    for cat_methods in categories.values():
        categorized.update(cat_methods)
    
    categories['Other'] = [m for m in unused_methods if m not in categorized]
    
    for category, methods in categories.items():
        if methods:
            print(f"\n{category}: {len(methods)} méthodes")
            for method in sorted(methods)[:5]:  # Afficher les 5 premières
                line_numbers = find_method_line_numbers(file_content, method)
                line_info = f"L{line_numbers[0]}" if line_numbers else "L?"
                print(f"  - {method} ({line_info})")
            if len(methods) > 5:
                print(f"  ... et {len(methods) - 5} autres")
    
    # Statistiques finales
    print(f"\n📈 STATISTIQUES FINALES")
    print("-" * 25)
    print(f"Taux d'utilisation: {len(used_methods)/len(defined_methods)*100:.1f}%")
    print(f"Méthodes inutilisées: {len(unused_methods)/len(defined_methods)*100:.1f}%")
    
    return unused_methods, used_methods

if __name__ == "__main__":
    analyze_unused_methods()
