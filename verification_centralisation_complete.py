#!/usr/bin/env python3
"""
Vérification exhaustive de la centralisation des valeurs hardcodées
pour les rollouts et clusters dans AZRConfig

Ce script identifie TOUTES les valeurs hardcodées spécifiquement liées aux
rollouts et clusters qui ne sont PAS encore centralisées.
"""

import re
import sys
import os

def extraire_valeurs_azrconfig():
    """Extrait toutes les valeurs définies dans AZRConfig avec leurs noms"""
    valeurs_config = {}
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Trouver la classe AZRConfig
        match_config = re.search(r'class AZRConfig.*?(?=class|\Z)', contenu, re.DOTALL)
        if not match_config:
            print("❌ Classe AZRConfig non trouvée")
            return valeurs_config
        
        config_content = match_config.group(0)
        
        # Extraire toutes les valeurs numériques avec leurs noms
        pattern_valeurs = r'(\w+):\s*float\s*=\s*([0-9]*\.?[0-9]+)'
        matches = re.findall(pattern_valeurs, config_content)
        
        for param_name, value in matches:
            valeurs_config[float(value)] = param_name
        
        print(f"✅ {len(valeurs_config)} valeurs uniques trouvées dans AZRConfig")
        return valeurs_config
        
    except Exception as e:
        print(f"❌ Erreur lors de l'extraction des valeurs config: {e}")
        return valeurs_config

def identifier_contexte_rollout_cluster(ligne, num_ligne):
    """Identifie si une ligne concerne les rollouts ou clusters"""
    ligne_lower = ligne.lower()
    
    # Mots-clés pour rollouts
    rollout_keywords = [
        'rollout', 'analyzer', 'generator', 'predictor',
        'impair', 'pair', 'sync', 'desync', 'combined',
        'priority', 'exploitation', 'generation', 'prediction',
        'signal', 'correlation', 'cross_impact', 'enrichment',
        'temporal', 'evolution', 'pattern', 'sequence'
    ]
    
    # Mots-clés pour clusters
    cluster_keywords = [
        'cluster', 'consensus', 'confidence', 'calibration',
        'accuracy', 'test_accuracy', 'agreement', 'evaluation',
        'analysis_quality', 'risk_factors', 'uncertainty'
    ]
    
    # Vérifier si c'est lié aux rollouts
    for keyword in rollout_keywords:
        if keyword in ligne_lower:
            return 'ROLLOUT'
    
    # Vérifier si c'est lié aux clusters
    for keyword in cluster_keywords:
        if keyword in ligne_lower:
            return 'CLUSTER'
    
    return None

def analyser_valeurs_rollouts_clusters():
    """Analyse spécifiquement les valeurs hardcodées dans rollouts et clusters"""
    
    print("🔍 VÉRIFICATION EXHAUSTIVE ROLLOUTS & CLUSTERS")
    print("=" * 70)
    
    # Extraire les valeurs déjà centralisées
    valeurs_config = extraire_valeurs_azrconfig()
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            lignes = f.readlines()
        
        # Patterns pour détecter les valeurs hardcodées
        patterns_hardcoded = [
            r'([><=]+)\s*([0-9]*\.?[0-9]+)',  # Comparaisons
            r'([+\-*/])\s*([0-9]*\.?[0-9]+)', # Opérations arithmétiques
            r'=\s*([0-9]*\.?[0-9]+)',         # Assignations
            r'\[\s*([0-9]*\.?[0-9]+)',        # Dans des listes
            r',\s*([0-9]*\.?[0-9]+)',         # Paramètres de fonction
        ]
        
        valeurs_rollouts_clusters = {}
        
        for i, ligne in enumerate(lignes, 1):
            # Ignorer les lignes de configuration
            if 'class AZRConfig' in ligne or ': float =' in ligne or ': int =' in ligne:
                continue
            
            # Ignorer les commentaires purs
            if ligne.strip().startswith('#'):
                continue
            
            # Vérifier si la ligne concerne rollouts ou clusters
            contexte = identifier_contexte_rollout_cluster(ligne, i)
            if not contexte:
                continue
                
            for pattern in patterns_hardcoded:
                matches = re.finditer(pattern, ligne)
                for match in matches:
                    try:
                        # Extraire la valeur numérique
                        if len(match.groups()) == 2:
                            valeur = float(match.group(2))
                        else:
                            valeur = float(match.group(1))
                        
                        # Ignorer les valeurs triviales
                        if valeur in [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 10.0, 100.0]:
                            continue
                            
                        # Vérifier si cette valeur est déjà centralisée
                        if valeur not in valeurs_config:
                            if valeur not in valeurs_rollouts_clusters:
                                valeurs_rollouts_clusters[valeur] = {
                                    'rollout': [],
                                    'cluster': []
                                }
                            
                            valeurs_rollouts_clusters[valeur][contexte.lower()].append({
                                'ligne': i,
                                'contenu': ligne.strip(),
                                'contexte': match.group(0)
                            })
                    except (ValueError, IndexError):
                        continue
        
        return valeurs_rollouts_clusters, valeurs_config
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return {}, {}

def analyser_methodes_specifiques():
    """Analyse les méthodes spécifiques aux rollouts et clusters"""
    
    methodes_rollouts = [
        '_calculate_impair_bias', '_calculate_pair_bias', '_calculate_sync_bias',
        '_calculate_combined_bias', '_generate_sequences', '_predict_next_hands',
        '_calculate_cluster_confidence', '_calculate_priority_synthesis',
        '_enrich_cross_impact', '_detect_temporal_signals', '_evaluate_sequence'
    ]
    
    valeurs_methodes = {}
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        for methode in methodes_rollouts:
            # Trouver la méthode
            pattern_methode = rf'def {methode}.*?(?=def |\Z)'
            match_methode = re.search(pattern_methode, contenu, re.DOTALL)
            
            if match_methode:
                methode_content = match_methode.group(0)
                
                # Chercher les valeurs hardcodées dans cette méthode
                pattern_valeurs = r'([0-9]*\.?[0-9]+)'
                matches = re.findall(pattern_valeurs, methode_content)
                
                for match in matches:
                    try:
                        valeur = float(match)
                        if 0.01 <= valeur <= 1.0 and valeur not in [1.0]:  # Valeurs de seuils probables
                            if valeur not in valeurs_methodes:
                                valeurs_methodes[valeur] = []
                            valeurs_methodes[valeur].append(methode)
                    except ValueError:
                        continue
    
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse des méthodes: {e}")
    
    return valeurs_methodes

def afficher_rapport_verification(valeurs_rollouts_clusters, valeurs_config, valeurs_methodes):
    """Affiche un rapport détaillé de vérification"""
    
    print(f"\n📊 RAPPORT DE VÉRIFICATION EXHAUSTIVE")
    print("=" * 80)
    
    # Compter les valeurs non centralisées par catégorie
    rollout_non_centralise = 0
    cluster_non_centralise = 0
    
    for valeur, occurrences in valeurs_rollouts_clusters.items():
        if occurrences['rollout']:
            rollout_non_centralise += 1
        if occurrences['cluster']:
            cluster_non_centralise += 1
    
    print(f"\n🎯 ROLLOUTS - VALEURS NON CENTRALISÉES")
    print("-" * 50)
    
    if rollout_non_centralise == 0:
        print("✅ AUCUNE valeur hardcodée rollout non centralisée!")
    else:
        for valeur, occurrences in sorted(valeurs_rollouts_clusters.items()):
            if occurrences['rollout']:
                print(f"\n  ❌ VALEUR: {valeur}")
                for occ in occurrences['rollout'][:3]:  # Afficher 3 premières
                    ligne_courte = occ['contenu'][:60] + "..." if len(occ['contenu']) > 60 else occ['contenu']
                    print(f"     L{occ['ligne']:4d}: {ligne_courte}")
                if len(occurrences['rollout']) > 3:
                    print(f"     ... et {len(occurrences['rollout']) - 3} autres")
    
    print(f"\n🏗️ CLUSTERS - VALEURS NON CENTRALISÉES")
    print("-" * 50)
    
    if cluster_non_centralise == 0:
        print("✅ AUCUNE valeur hardcodée cluster non centralisée!")
    else:
        for valeur, occurrences in sorted(valeurs_rollouts_clusters.items()):
            if occurrences['cluster']:
                print(f"\n  ❌ VALEUR: {valeur}")
                for occ in occurrences['cluster'][:3]:  # Afficher 3 premières
                    ligne_courte = occ['contenu'][:60] + "..." if len(occ['contenu']) > 60 else occ['contenu']
                    print(f"     L{occ['ligne']:4d}: {ligne_courte}")
                if len(occurrences['cluster']) > 3:
                    print(f"     ... et {len(occurrences['cluster']) - 3} autres")
    
    print(f"\n🔍 ANALYSE DES MÉTHODES SPÉCIFIQUES")
    print("-" * 50)
    
    if not valeurs_methodes:
        print("✅ AUCUNE valeur suspecte dans les méthodes rollouts/clusters!")
    else:
        for valeur, methodes in sorted(valeurs_methodes.items()):
            if valeur not in valeurs_config:
                print(f"  ⚠️  VALEUR {valeur} dans: {', '.join(methodes)}")
    
    # Résultat final
    total_non_centralise = rollout_non_centralise + cluster_non_centralise
    
    print(f"\n" + "=" * 80)
    print(f"🏁 RÉSULTAT FINAL DE LA VÉRIFICATION")
    print("=" * 80)
    print(f"📊 Valeurs AZRConfig: {len(valeurs_config)}")
    print(f"🎯 Rollouts non centralisés: {rollout_non_centralise}")
    print(f"🏗️ Clusters non centralisés: {cluster_non_centralise}")
    print(f"📈 Total non centralisé: {total_non_centralise}")
    
    if total_non_centralise == 0:
        print("\n🎉 CENTRALISATION PARFAITE!")
        print("✅ TOUTES les valeurs rollouts et clusters sont centralisées!")
        print("🏆 MISSION ACCOMPLIE AVEC EXCELLENCE!")
        return True
    else:
        print(f"\n⚠️ {total_non_centralise} valeurs nécessitent encore une centralisation")
        print("🔧 Corrections recommandées pour atteindre la perfection")
        return False

def main():
    """Fonction principale"""
    
    # Analyser les valeurs rollouts et clusters
    valeurs_rollouts_clusters, valeurs_config = analyser_valeurs_rollouts_clusters()
    
    # Analyser les méthodes spécifiques
    valeurs_methodes = analyser_methodes_specifiques()
    
    # Afficher le rapport de vérification
    centralisation_parfaite = afficher_rapport_verification(
        valeurs_rollouts_clusters, valeurs_config, valeurs_methodes
    )
    
    return centralisation_parfaite

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
