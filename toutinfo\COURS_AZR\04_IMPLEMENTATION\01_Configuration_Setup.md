# ⚙️ MODULE 4.1 - CONFIGURATION ET SETUP

## 🎯 **OBJECTIFS DU MODULE**

À la fin de ce module, vous saurez :
- ✅ Installer et configurer un environnement AZR complet
- ✅ Paramétrer les hyperparamètres optimaux
- ✅ Préparer les composants nécessaires
- ✅ Valider votre installation

---

## 🛠️ **INSTALLATION DE L'ENVIRONNEMENT**

### 📦 **Prérequis Système**

#### **Matériel Recommandé**
```yaml
CPU: 8+ cœurs (Intel i7/AMD Ryzen 7+)
RAM: 32GB+ (64GB pour modèles 13B+)
GPU: NVIDIA RTX 3080+ (24GB VRAM recommandé)
Stockage: 500GB+ SSD NVMe
```

#### **Logiciels de Base**
```bash
# Python 3.8+ avec pip
python --version  # >= 3.8.0

# Git pour le versioning
git --version

# CUDA pour GPU (optionnel mais recommandé)
nvidia-smi  # Vérifier la version CUDA
```

### 🐍 **Installation Python et Dépendances**

#### **Création de l'Environnement Virtuel**
```bash
# Création de l'environnement
python -m venv azr_env

# Activation (Linux/Mac)
source azr_env/bin/activate

# Activation (Windows)
azr_env\Scripts\activate

# Vérification
which python  # Doit pointer vers azr_env
```

#### **Installation des Dépendances Core**
```bash
# PyTorch avec support CUDA
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Transformers et Hugging Face
pip install transformers>=4.30.0
pip install datasets>=2.12.0
pip install accelerate>=0.20.0

# Outils de développement
pip install jupyter>=1.0.0
pip install tensorboard>=2.13.0
pip install wandb>=0.15.0

# Utilitaires
pip install numpy>=1.24.0
pip install pandas>=2.0.0
pip install matplotlib>=3.7.0
pip install seaborn>=0.12.0
pip install tqdm>=4.65.0
```

#### **Dépendances Spécifiques AZR**
```bash
# Exécution sécurisée de code
pip install RestrictedPython>=6.0
pip install docker>=6.1.0

# Métriques de code
pip install radon>=6.0.1
pip install lizard>=1.17.10

# Parsing et AST
pip install ast-tools>=0.1.0
pip install astunparse>=1.6.3

# Tests et validation
pip install pytest>=7.3.0
pip install pytest-timeout>=2.1.0
```

### 🔧 **Configuration Docker (Recommandé)**

#### **Dockerfile pour AZR**
```dockerfile
FROM nvidia/cuda:11.8-devel-ubuntu20.04

# Variables d'environnement
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# Installation des dépendances système
RUN apt-get update && apt-get install -y \
    python3.9 \
    python3.9-pip \
    python3.9-dev \
    git \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Création de l'utilisateur AZR
RUN useradd -m -s /bin/bash azr
USER azr
WORKDIR /home/<USER>

# Installation des dépendances Python
COPY requirements.txt .
RUN pip3 install --user -r requirements.txt

# Configuration de l'environnement
ENV PATH="/home/<USER>/.local/bin:$PATH"
ENV PYTHONPATH="/home/<USER>/azr:$PYTHONPATH"

# Point d'entrée
CMD ["python3", "-m", "azr.main"]
```

#### **Docker Compose pour Développement**
```yaml
version: '3.8'

services:
  azr-dev:
    build: .
    volumes:
      - ./azr:/home/<USER>/azr
      - ./data:/home/<USER>/data
      - ./logs:/home/<USER>/logs
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - WANDB_API_KEY=${WANDB_API_KEY}
    ports:
      - "8888:8888"  # Jupyter
      - "6006:6006"  # TensorBoard
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
```

---

## ⚙️ **CONFIGURATION DES HYPERPARAMÈTRES**

### 📊 **Configuration Principale**

#### **Fichier config.yaml**
```yaml
# Configuration AZR Optimale
azr_config:
  # Modèle de base
  model:
    name: "codellama/CodeLlama-7b-Python-hf"
    max_length: 4096
    device: "cuda"
    dtype: "float16"  # Économie mémoire
    
  # Hyperparamètres d'entraînement
  training:
    learning_rate: 1e-6
    batch_size: 32
    max_iterations: 10000
    gradient_clip_norm: 1.0
    
  # Paramètres AZR spécifiques
  azr:
    lambda_coefficient: 1.0
    n_rollouts: 5
    temperature_propose: 0.8
    temperature_solve: 0.6
    
  # Récompenses
  rewards:
    learnability_trials: 5
    diversity_window: 10
    baseline_alpha: 0.99
    
  # Environnement d'exécution
  execution:
    timeout: 5.0
    memory_limit: 128  # MB
    max_output_length: 1000
    
  # Buffer et mémoire
  memory:
    task_buffer_size: 1000
    context_size: 5
    save_frequency: 100
    
  # Monitoring
  logging:
    log_level: "INFO"
    wandb_project: "azr-training"
    tensorboard_dir: "./logs"
    checkpoint_dir: "./checkpoints"
```

### 🎛️ **Classe de Configuration Python**

#### **Configuration Structurée**
```python
from dataclasses import dataclass
from typing import Optional, Dict, Any
import yaml

@dataclass
class ModelConfig:
    name: str = "codellama/CodeLlama-7b-Python-hf"
    max_length: int = 4096
    device: str = "cuda"
    dtype: str = "float16"

@dataclass
class TrainingConfig:
    learning_rate: float = 1e-6
    batch_size: int = 32
    max_iterations: int = 10000
    gradient_clip_norm: float = 1.0
    warmup_steps: int = 100

@dataclass
class AZRConfig:
    lambda_coefficient: float = 1.0
    n_rollouts: int = 5
    temperature_propose: float = 0.8
    temperature_solve: float = 0.6

@dataclass
class RewardConfig:
    learnability_trials: int = 5
    diversity_window: int = 10
    baseline_alpha: float = 0.99
    min_difficulty: float = 0.1
    max_difficulty: float = 0.9

@dataclass
class ExecutionConfig:
    timeout: float = 5.0
    memory_limit: int = 128  # MB
    max_output_length: int = 1000
    sandbox_type: str = "docker"

@dataclass
class MemoryConfig:
    task_buffer_size: int = 1000
    context_size: int = 5
    save_frequency: int = 100
    cleanup_threshold: float = 0.8

@dataclass
class LoggingConfig:
    log_level: str = "INFO"
    wandb_project: str = "azr-training"
    tensorboard_dir: str = "./logs"
    checkpoint_dir: str = "./checkpoints"

@dataclass
class AZRFullConfig:
    model: ModelConfig
    training: TrainingConfig
    azr: AZRConfig
    rewards: RewardConfig
    execution: ExecutionConfig
    memory: MemoryConfig
    logging: LoggingConfig
    
    @classmethod
    def from_yaml(cls, yaml_path: str) -> 'AZRFullConfig':
        """Charge la configuration depuis un fichier YAML"""
        with open(yaml_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        
        return cls(
            model=ModelConfig(**config_dict['azr_config']['model']),
            training=TrainingConfig(**config_dict['azr_config']['training']),
            azr=AZRConfig(**config_dict['azr_config']['azr']),
            rewards=RewardConfig(**config_dict['azr_config']['rewards']),
            execution=ExecutionConfig(**config_dict['azr_config']['execution']),
            memory=MemoryConfig(**config_dict['azr_config']['memory']),
            logging=LoggingConfig(**config_dict['azr_config']['logging'])
        )
    
    def validate(self) -> bool:
        """Valide la cohérence de la configuration"""
        issues = []
        
        # Validation des paramètres critiques
        if self.training.learning_rate <= 0:
            issues.append("Learning rate doit être positif")
        
        if self.azr.lambda_coefficient < 0:
            issues.append("Lambda coefficient doit être non-négatif")
        
        if self.execution.timeout <= 0:
            issues.append("Timeout doit être positif")
        
        if self.memory.task_buffer_size <= 0:
            issues.append("Task buffer size doit être positif")
        
        # Validation des plages
        if not (0.0 <= self.azr.temperature_propose <= 2.0):
            issues.append("Temperature propose doit être entre 0 et 2")
        
        if not (0.0 <= self.azr.temperature_solve <= 2.0):
            issues.append("Temperature solve doit être entre 0 et 2")
        
        if issues:
            for issue in issues:
                print(f"❌ Configuration Error: {issue}")
            return False
        
        print("✅ Configuration validée avec succès")
        return True
```

---

## 🔧 **SETUP DES COMPOSANTS**

### 🐍 **Environnement d'Exécution Sécurisé**

#### **Configuration Docker Sandbox**
```python
import docker
from typing import Dict, Any

class DockerSandbox:
    def __init__(self, config: ExecutionConfig):
        self.client = docker.from_env()
        self.config = config
        self.image_name = "python:3.9-slim"
        
    def setup(self):
        """Prépare l'environnement Docker"""
        
        # Pull de l'image de base
        print("📦 Téléchargement de l'image Python...")
        self.client.images.pull(self.image_name)
        
        # Création de l'image personnalisée
        dockerfile = """
        FROM python:3.9-slim
        
        # Installation des packages autorisés
        RUN pip install numpy pandas matplotlib
        
        # Création d'un utilisateur non-privilégié
        RUN useradd -m -s /bin/bash sandbox
        USER sandbox
        WORKDIR /home/<USER>
        
        # Limitation des ressources
        RUN ulimit -v {memory_limit}000  # Limite mémoire en KB
        """.format(memory_limit=self.config.memory_limit * 1024)
        
        # Build de l'image
        print("🔨 Construction de l'image sandbox...")
        self.client.images.build(
            fileobj=io.BytesIO(dockerfile.encode()),
            tag="azr-sandbox",
            rm=True
        )
        
        print("✅ Sandbox Docker configuré")
    
    def execute_code(self, code: str, input_data: str = "") -> Dict[str, Any]:
        """Exécute du code dans le sandbox"""
        
        try:
            # Création du container
            container = self.client.containers.run(
                image="azr-sandbox",
                command=f"python -c '{code}'",
                stdin_open=True,
                stdout=True,
                stderr=True,
                detach=True,
                mem_limit=f"{self.config.memory_limit}m",
                cpu_quota=50000,  # 50% CPU
                network_disabled=True,  # Pas d'accès réseau
                remove=True
            )
            
            # Attente avec timeout
            result = container.wait(timeout=self.config.timeout)
            
            # Récupération des outputs
            stdout = container.logs(stdout=True, stderr=False).decode()
            stderr = container.logs(stdout=False, stderr=True).decode()
            
            return {
                'success': result['StatusCode'] == 0,
                'stdout': stdout,
                'stderr': stderr,
                'exit_code': result['StatusCode']
            }
            
        except docker.errors.ContainerError as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': str(e),
                'exit_code': -1
            }
        except Exception as e:
            return {
                'success': False,
                'stdout': '',
                'stderr': f"Execution error: {str(e)}",
                'exit_code': -2
            }
```

### 📊 **Monitoring et Logging**

#### **Configuration Weights & Biases**
```python
import wandb
from typing import Dict, Any

class AZRLogger:
    def __init__(self, config: LoggingConfig):
        self.config = config
        self.setup_wandb()
        self.setup_tensorboard()
    
    def setup_wandb(self):
        """Configure Weights & Biases"""
        wandb.init(
            project=self.config.wandb_project,
            config={
                "architecture": "AZR",
                "model": "CodeLlama-7B",
                "framework": "PyTorch"
            },
            tags=["azr", "self-play", "reasoning"]
        )
        
        print("✅ Weights & Biases configuré")
    
    def setup_tensorboard(self):
        """Configure TensorBoard"""
        from torch.utils.tensorboard import SummaryWriter
        
        self.tb_writer = SummaryWriter(
            log_dir=self.config.tensorboard_dir
        )
        
        print("✅ TensorBoard configuré")
    
    def log_metrics(self, metrics: Dict[str, Any], step: int):
        """Log des métriques"""
        
        # Weights & Biases
        wandb.log(metrics, step=step)
        
        # TensorBoard
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                self.tb_writer.add_scalar(key, value, step)
    
    def log_code_sample(self, code: str, task_type: str, step: int):
        """Log d'un échantillon de code généré"""
        
        wandb.log({
            f"code_sample_{task_type}": wandb.Html(
                f"<pre><code>{code}</code></pre>"
            )
        }, step=step)
```

---

## ✅ **VALIDATION DE L'INSTALLATION**

### 🧪 **Script de Test Complet**

#### **test_installation.py**
```python
#!/usr/bin/env python3
"""
Script de validation de l'installation AZR
"""

import sys
import torch
import transformers
import docker
from azr.config import AZRFullConfig
from azr.sandbox import DockerSandbox

def test_python_environment():
    """Test de l'environnement Python"""
    print("🐍 Test de l'environnement Python...")
    
    # Version Python
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    else:
        print(f"❌ Python {version.major}.{version.minor} (requis: 3.8+)")
        return False
    
    return True

def test_pytorch_installation():
    """Test de PyTorch et CUDA"""
    print("🔥 Test de PyTorch...")
    
    # Version PyTorch
    print(f"✅ PyTorch {torch.__version__}")
    
    # Support CUDA
    if torch.cuda.is_available():
        print(f"✅ CUDA disponible: {torch.cuda.get_device_name(0)}")
        print(f"✅ VRAM: {torch.cuda.get_device_properties(0).total_memory // 1024**3} GB")
    else:
        print("⚠️ CUDA non disponible (CPU seulement)")
    
    return True

def test_transformers():
    """Test de Transformers"""
    print("🤗 Test de Transformers...")
    
    try:
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        print(f"✅ Transformers {transformers.__version__}")
        return True
    except Exception as e:
        print(f"❌ Erreur Transformers: {e}")
        return False

def test_docker():
    """Test de Docker"""
    print("🐳 Test de Docker...")
    
    try:
        client = docker.from_env()
        client.ping()
        print("✅ Docker connecté")
        return True
    except Exception as e:
        print(f"❌ Erreur Docker: {e}")
        return False

def test_azr_config():
    """Test de la configuration AZR"""
    print("⚙️ Test de la configuration AZR...")
    
    try:
        config = AZRFullConfig.from_yaml("config.yaml")
        if config.validate():
            print("✅ Configuration AZR valide")
            return True
        else:
            print("❌ Configuration AZR invalide")
            return False
    except Exception as e:
        print(f"❌ Erreur configuration: {e}")
        return False

def test_sandbox():
    """Test du sandbox d'exécution"""
    print("🏖️ Test du sandbox...")
    
    try:
        sandbox = DockerSandbox(ExecutionConfig())
        sandbox.setup()
        
        # Test d'exécution simple
        result = sandbox.execute_code("print('Hello AZR!')")
        
        if result['success'] and "Hello AZR!" in result['stdout']:
            print("✅ Sandbox fonctionnel")
            return True
        else:
            print("❌ Sandbox non fonctionnel")
            return False
    except Exception as e:
        print(f"❌ Erreur sandbox: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 VALIDATION DE L'INSTALLATION AZR")
    print("=" * 50)
    
    tests = [
        test_python_environment,
        test_pytorch_installation,
        test_transformers,
        test_docker,
        test_azr_config,
        test_sandbox
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Erreur inattendue: {e}")
            results.append(False)
        print()
    
    # Résumé
    passed = sum(results)
    total = len(results)
    
    print("=" * 50)
    print(f"📊 RÉSULTATS: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Installation AZR complète et fonctionnelle !")
        return True
    else:
        print("⚠️ Problèmes détectés. Vérifiez les erreurs ci-dessus.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

### 🏃‍♂️ **Exécution du Test**

```bash
# Exécution du script de validation
python test_installation.py

# Si tout est OK, vous devriez voir :
# 🎉 Installation AZR complète et fonctionnelle !
```

---

## 🎓 **RÉCAPITULATIF DU MODULE**

### ✅ **Ce que vous avez configuré :**

1. **Environnement Python** complet avec toutes les dépendances
2. **Configuration YAML** structurée et validée
3. **Sandbox Docker** sécurisé pour l'exécution de code
4. **Monitoring** avec Weights & Biases et TensorBoard
5. **Tests de validation** pour vérifier l'installation

### 🎯 **Prochaine étape :**
[Module 4.2 - Code Principal](./02_Code_Principal.md) pour implémenter le cœur du système AZR.

---

**🎉 Parfait ! Votre environnement AZR est maintenant configuré et prêt pour l'implémentation !**
