#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 ANALYSEUR DE DONNÉES BACCARAT - FORMAT TABULAIRE
===================================================

Analyseur spécialisé pour les fichiers texte tabulaires générés par le générateur autonome.
Optimisé pour l'analyse rapide et la lecture humaine des patterns.

Usage: python baccarat_tabular_analyzer.py [fichier_donnees.txt]
"""

import sys
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
from collections import Counter, defaultdict

class BaccaratTabularAnalyzer:
    """Analyseur spécialisé pour le format tabulaire"""

    def __init__(self, filename: str):
        """
        Initialise l'analyseur avec un fichier tabulaire
        
        Args:
            filename: Chemin vers le fichier TXT tabulaire
        """
        self.filename = filename
        self.games_data = []
        self.global_stats = {}
        
        self._load_tabular_data()
        print(f"✅ Analyseur tabulaire initialisé avec {len(self.games_data)} parties")

    def _load_tabular_data(self):
        """Charge les données depuis le fichier tabulaire"""
        try:
            if not os.path.exists(self.filename):
                raise FileNotFoundError(f"Fichier non trouvé: {self.filename}")
            
            with open(self.filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parser le contenu tabulaire
            self._parse_tabular_content(content)
            
            if not self.games_data:
                raise ValueError("Aucune partie trouvée dans les données")
                
        except Exception as e:
            raise Exception(f"Erreur lors du chargement: {e}")

    def _parse_tabular_content(self, content: str):
        """Parse le contenu du fichier tabulaire"""
        
        lines = content.split('\n')
        current_game = None
        
        for line in lines:
            line = line.strip()
            
            # Détecter le début d'une partie
            if line.startswith('Partie ') and ':' in line:
                if current_game:
                    self.games_data.append(current_game)
                
                game_num = int(re.search(r'Partie\s+(\d+)', line).group(1))
                current_game = {
                    'game_number': game_num,
                    'burn_parity': None,
                    'parity_sequence': [],
                    'sync_sequence': [],
                    'combined_sequence': [],  # NOUVEL INDEX COMBINÉ
                    'so_sequence': [],
                    'result_sequence': [],
                    'stats': {}
                }
            
            elif current_game and line.startswith('BRÛLAGE'):
                # Parser "BRÛLAGE (M0) : PAIR → État initial: SYNC"
                burn_info = line.split(':')[1].strip()
                if '→' in burn_info:
                    burn_parity = burn_info.split('→')[0].strip()
                    initial_sync = burn_info.split('→')[1].replace('État initial:', '').strip()
                else:
                    burn_parity = burn_info
                    initial_sync = 'SYNC' if burn_parity == 'PAIR' else 'DESYNC'

                current_game['burn_parity'] = burn_parity
                current_game['initial_sync_state'] = initial_sync
            
            elif current_game and line.startswith('PAIR/IMPAIR'):
                sequence = line.split(':')[1].strip()
                current_game['parity_sequence'] = [x.strip() for x in sequence.split(',')]
            
            elif current_game and line.startswith('SYNC/DESYNC'):
                sequence = line.split(':')[1].strip()
                current_game['sync_sequence'] = [x.strip() for x in sequence.split(',')]

            elif current_game and line.startswith('COMBINÉ'):
                sequence = line.split(':')[1].strip()
                current_game['combined_sequence'] = [x.strip() for x in sequence.split(',')]

            elif current_game and line.startswith('S/O'):
                sequence = line.split(':')[1].strip()
                # Nettoyer le marqueur AZR s'il est présent
                if '←' in sequence:
                    sequence = sequence.split('←')[0].strip()
                current_game['so_sequence'] = [x.strip() for x in sequence.split(',')]
            
            elif current_game and line.startswith('MANCHES'):
                sequence = line.split(':')[1].strip()
                current_game['result_sequence'] = [x.strip() for x in sequence.split(',')]
            
            elif current_game and line.startswith('STATS'):
                stats_text = line.split(':')[1].strip()
                # Parser les statistiques
                current_game['stats'] = self._parse_stats_line(stats_text)
        
        # Ajouter la dernière partie
        if current_game:
            self.games_data.append(current_game)

    def _parse_stats_line(self, stats_text: str) -> Dict:
        """Parse une ligne de statistiques"""
        stats = {}
        
        # Extraire les nombres avec regex
        total_match = re.search(r'(\d+) manches total', stats_text)
        pb_match = re.search(r'(\d+) P/B', stats_text)
        tie_match = re.search(r'(\d+) TIE', stats_text)
        so_match = re.search(r'(\d+) S/O', stats_text)
        
        if total_match:
            stats['total_hands'] = int(total_match.group(1))
        if pb_match:
            stats['pb_hands'] = int(pb_match.group(1))
        if tie_match:
            stats['tie_hands'] = int(tie_match.group(1))
        if so_match:
            stats['so_conversions'] = int(so_match.group(1))
        
        return stats

    # ========================================================================
    # 📈 ANALYSES RAPIDES SPÉCIALISÉES
    # ========================================================================

    def quick_parity_so_analysis(self) -> Dict[str, Any]:
        """Analyse rapide de l'influence PAIR/IMPAIR → S/O"""
        
        pair_s_count = 0
        pair_total = 0
        impair_s_count = 0
        impair_total = 0
        
        for game in self.games_data:
            parity_seq = game['parity_sequence']
            so_seq = game['so_sequence']
            
            # Analyser chaque position
            for i, (parity, so) in enumerate(zip(parity_seq, so_seq)):
                if so in ['S', 'O']:
                    if parity == 'PAIR':
                        pair_total += 1
                        if so == 'S':
                            pair_s_count += 1
                    else:  # IMPAIR
                        impair_total += 1
                        if so == 'S':
                            impair_s_count += 1
        
        # Calculer les taux
        pair_s_rate = pair_s_count / pair_total if pair_total > 0 else 0
        impair_s_rate = impair_s_count / impair_total if impair_total > 0 else 0
        difference = abs(pair_s_rate - impair_s_rate)
        
        return {
            'pair_analysis': {
                'total': pair_total,
                's_count': pair_s_count,
                's_rate': pair_s_rate,
                'bias_strength': abs(pair_s_rate - 0.5)
            },
            'impair_analysis': {
                'total': impair_total,
                's_count': impair_s_count,
                's_rate': impair_s_rate,
                'bias_strength': abs(impair_s_rate - 0.5)
            },
            'comparison': {
                'difference': difference,
                'significant': difference > 0.05,  # Seuil 5%
                'sample_size': pair_total + impair_total
            }
        }

    def verify_pb_so_consistency(self) -> Dict[str, Any]:
        """Vérifie la cohérence entre les index P/B/T et S/O"""

        consistent_games = 0
        inconsistent_games = 0
        inconsistency_details = []
        total_pb_hands = 0
        total_so_conversions = 0

        for game in self.games_data:
            result_seq = game.get('result_sequence', [])
            so_seq = game.get('so_sequence', [])
            game_number = game.get('game_number', 0)

            if len(result_seq) != len(so_seq):
                inconsistent_games += 1
                inconsistency_details.append({
                    'game': game_number,
                    'issue': 'length_mismatch',
                    'result_length': len(result_seq),
                    'so_length': len(so_seq)
                })
                continue

            game_consistent = True
            game_errors = []
            last_pb_result = None
            pb_position = 0

            for i, (result, so) in enumerate(zip(result_seq, so_seq)):
                if result in ['P', 'B']:  # Manche P/B
                    pb_position += 1
                    total_pb_hands += 1

                    if pb_position == 1:  # Première manche P/B
                        if so != '--':
                            game_consistent = False
                            game_errors.append({
                                'position': i + 1,
                                'issue': 'first_pb_should_be_dash',
                                'expected': '--',
                                'found': so,
                                'result': result
                            })
                    else:  # Manches P/B suivantes
                        if so in ['S', 'O']:
                            total_so_conversions += 1
                            # Vérifier la logique S/O
                            if last_pb_result:
                                if result == last_pb_result and so != 'S':
                                    game_consistent = False
                                    game_errors.append({
                                        'position': i + 1,
                                        'issue': 'should_be_S',
                                        'current_result': result,
                                        'previous_pb': last_pb_result,
                                        'expected': 'S',
                                        'found': so
                                    })
                                elif result != last_pb_result and so != 'O':
                                    game_consistent = False
                                    game_errors.append({
                                        'position': i + 1,
                                        'issue': 'should_be_O',
                                        'current_result': result,
                                        'previous_pb': last_pb_result,
                                        'expected': 'O',
                                        'found': so
                                    })
                        elif so != '--':
                            game_consistent = False
                            game_errors.append({
                                'position': i + 1,
                                'issue': 'invalid_so_value',
                                'expected': 'S, O, or --',
                                'found': so,
                                'result': result
                            })

                    last_pb_result = result

                elif result == 'T':  # TIE
                    if so != '--':
                        game_consistent = False
                        game_errors.append({
                            'position': i + 1,
                            'issue': 'tie_should_be_dash',
                            'expected': '--',
                            'found': so,
                            'result': result
                        })
                else:
                    game_consistent = False
                    game_errors.append({
                        'position': i + 1,
                        'issue': 'invalid_result',
                        'expected': 'P, B, or T',
                        'found': result
                    })

            if game_consistent:
                consistent_games += 1
            else:
                inconsistent_games += 1
                if len(inconsistency_details) < 10:  # Limiter les exemples
                    inconsistency_details.append({
                        'game': game_number,
                        'issue': 'logic_errors',
                        'errors': game_errors[:3]  # Premiers 3 erreurs
                    })

        total_games = len(self.games_data)
        consistency_rate = consistent_games / total_games if total_games > 0 else 0

        return {
            'total_games': total_games,
            'consistent_games': consistent_games,
            'inconsistent_games': inconsistent_games,
            'consistency_rate': consistency_rate,
            'total_pb_hands': total_pb_hands,
            'total_so_conversions': total_so_conversions,
            'inconsistency_details': inconsistency_details,
            'summary': {
                'major_issues': len([d for d in inconsistency_details if d['issue'] in ['length_mismatch', 'logic_errors']]),
                'most_common_error': self._find_most_common_error(inconsistency_details)
            }
        }

    def _find_most_common_error(self, inconsistency_details: List[Dict]) -> str:
        """Trouve le type d'erreur le plus fréquent"""
        error_counts = defaultdict(int)

        for detail in inconsistency_details:
            if detail['issue'] == 'logic_errors':
                for error in detail.get('errors', []):
                    error_counts[error['issue']] += 1
            else:
                error_counts[detail['issue']] += 1

        if error_counts:
            return max(error_counts.items(), key=lambda x: x[1])[0]
        return 'none'

    def verify_burn_sync_consistency(self) -> Dict[str, Any]:
        """Vérifie la cohérence brûlage → état initial SYNC/DESYNC"""

        consistent_games = 0
        inconsistent_games = 0
        inconsistent_details = []

        for game in self.games_data:
            burn_parity = game.get('burn_parity')
            initial_sync = game.get('initial_sync_state')
            sync_sequence = game.get('sync_sequence', [])

            # Vérifier la règle : PAIR → SYNC, IMPAIR → DESYNC
            expected_initial = 'SYNC' if burn_parity == 'PAIR' else 'DESYNC'

            # Vérifier avec l'état initial déclaré
            if initial_sync == expected_initial:
                # Vérifier aussi avec le premier état de la séquence
                if sync_sequence and sync_sequence[0] == expected_initial:
                    consistent_games += 1
                else:
                    inconsistent_games += 1
                    inconsistent_details.append({
                        'game': game['game_number'],
                        'burn_parity': burn_parity,
                        'expected_initial': expected_initial,
                        'declared_initial': initial_sync,
                        'first_sync_state': sync_sequence[0] if sync_sequence else 'N/A',
                        'issue': 'sequence_mismatch'
                    })
            else:
                inconsistent_games += 1
                inconsistent_details.append({
                    'game': game['game_number'],
                    'burn_parity': burn_parity,
                    'expected_initial': expected_initial,
                    'declared_initial': initial_sync,
                    'issue': 'initial_state_mismatch'
                })

        return {
            'total_games': len(self.games_data),
            'consistent_games': consistent_games,
            'inconsistent_games': inconsistent_games,
            'consistency_rate': consistent_games / len(self.games_data) if self.games_data else 0,
            'inconsistent_details': inconsistent_details[:5]  # Premiers 5 exemples
        }

    def analyze_burn_influence_quick(self) -> Dict[str, Any]:
        """Analyse rapide de l'influence du brûlage"""
        
        burn_pair_s_rates = []
        burn_impair_s_rates = []
        
        for game in self.games_data:
            burn_parity = game['burn_parity']
            so_seq = game['so_sequence']
            
            # Calculer le taux S pour cette partie
            valid_so = [so for so in so_seq if so in ['S', 'O']]
            if valid_so:
                s_rate = valid_so.count('S') / len(valid_so)
                
                if burn_parity == 'PAIR':
                    burn_pair_s_rates.append(s_rate)
                else:
                    burn_impair_s_rates.append(s_rate)
        
        # Calculer les moyennes
        pair_avg = sum(burn_pair_s_rates) / len(burn_pair_s_rates) if burn_pair_s_rates else 0
        impair_avg = sum(burn_impair_s_rates) / len(burn_impair_s_rates) if burn_impair_s_rates else 0
        
        return {
            'burn_pair': {
                'games': len(burn_pair_s_rates),
                'avg_s_rate': pair_avg,
                'bias_strength': abs(pair_avg - 0.5)
            },
            'burn_impair': {
                'games': len(burn_impair_s_rates),
                'avg_s_rate': impair_avg,
                'bias_strength': abs(impair_avg - 0.5)
            },
            'difference': abs(pair_avg - impair_avg),
            'significant': abs(pair_avg - impair_avg) > 0.02
        }

    def find_parity_patterns(self, pattern_length: int = 3) -> Dict[str, Any]:
        """Trouve les patterns de parité les plus fréquents"""
        
        pattern_counts = defaultdict(int)
        pattern_so_following = defaultdict(list)
        
        for game in self.games_data:
            parity_seq = game['parity_sequence']
            so_seq = game['so_sequence']
            
            # Chercher les patterns
            for i in range(len(parity_seq) - pattern_length + 1):
                pattern = tuple(parity_seq[i:i+pattern_length])
                pattern_counts[pattern] += 1
                
                # Chercher le S/O qui suit
                for j in range(i + pattern_length, len(so_seq)):
                    if so_seq[j] in ['S', 'O']:
                        pattern_so_following[pattern].append(so_seq[j])
                        break
        
        # Analyser les patterns les plus fréquents
        top_patterns = {}
        for pattern, count in sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            following_so = pattern_so_following[pattern]
            if following_so:
                s_count = following_so.count('S')
                s_rate = s_count / len(following_so)
                
                top_patterns[pattern] = {
                    'occurrences': count,
                    'following_so_count': len(following_so),
                    's_rate': s_rate,
                    'bias_strength': abs(s_rate - 0.5),
                    'significant': abs(s_rate - 0.5) > 0.15
                }
        
        return {
            'pattern_length': pattern_length,
            'total_patterns_found': len(pattern_counts),
            'top_patterns': top_patterns,
            'significant_patterns': {k: v for k, v in top_patterns.items() if v['significant']}
        }

    def extract_all_parity_sequences(self) -> Dict[str, Any]:
        """NOUVELLE APPROCHE: Extrait et catalogue toutes les séquences PAIR/IMPAIR trouvées"""

        print("🔍 EXTRACTION DE TOUTES LES SÉQUENCES PAIR/IMPAIR")
        print("=" * 60)

        all_sequences = {}  # Dictionnaire des séquences uniques
        sequence_so_influence = defaultdict(list)  # Influence sur S/O
        sequence_stats = defaultdict(int)  # Compteurs

        total_sequences_found = 0

        for game in self.games_data:
            parity_seq = game.get('parity_sequence', [])
            so_seq = game.get('so_sequence', [])
            burn_parity = game.get('burn_parity')

            # Extraire toutes les sous-séquences possibles
            for length in range(1, min(len(parity_seq) + 1, 11)):  # Séquences de 1 à 10
                for start in range(len(parity_seq) - length + 1):
                    # Extraire la séquence
                    sequence = tuple([burn_parity] + parity_seq[start:start+length])
                    sequence_key = '_'.join(sequence)

                    # Compter cette séquence
                    sequence_stats[sequence_key] += 1
                    total_sequences_found += 1

                    # Chercher l'influence S/O qui suit
                    following_so = []
                    for i in range(start + length, len(so_seq)):
                        if so_seq[i] in ['S', 'O']:
                            following_so.append(so_seq[i])
                            break

                    if following_so:
                        sequence_so_influence[sequence_key].extend(following_so)

                    # Stocker les détails de la séquence
                    if sequence_key not in all_sequences:
                        all_sequences[sequence_key] = {
                            'sequence': sequence,
                            'length': length + 1,  # +1 pour le brûlage
                            'pattern': sequence_key,
                            'occurrences': 0,
                            'so_following': [],
                            's_count': 0,
                            'o_count': 0,
                            's_rate': 0.0,
                            'bias_strength': 0.0
                        }

        # Calculer les statistiques pour chaque séquence
        for seq_key, seq_data in all_sequences.items():
            seq_data['occurrences'] = sequence_stats[seq_key]
            seq_data['so_following'] = sequence_so_influence[seq_key]
            seq_data['s_count'] = seq_data['so_following'].count('S')
            seq_data['o_count'] = seq_data['so_following'].count('O')

            total_so = seq_data['s_count'] + seq_data['o_count']
            if total_so > 0:
                seq_data['s_rate'] = seq_data['s_count'] / total_so
                seq_data['bias_strength'] = abs(seq_data['s_rate'] - 0.5)

        print(f"✅ Extraction terminée:")
        print(f"   • Séquences uniques trouvées: {len(all_sequences):,}")
        print(f"   • Total occurrences: {total_sequences_found:,}")
        print()

        return {
            'total_unique_sequences': len(all_sequences),
            'total_occurrences': total_sequences_found,
            'all_sequences': all_sequences,
            'sequence_stats': dict(sequence_stats),
            'games_analyzed': len(self.games_data)
        }

    def analyze_sequence_influence_patterns(self, sequences_data: Dict) -> Dict[str, Any]:
        """Analyse les patterns d'influence des séquences sur S/O"""

        print("📊 ANALYSE DES PATTERNS D'INFLUENCE")
        print("=" * 60)

        all_sequences = sequences_data['all_sequences']

        # Filtrer les séquences avec suffisamment de données
        significant_sequences = {}
        for seq_key, seq_data in all_sequences.items():
            if len(seq_data['so_following']) >= 10:  # Au moins 10 occurrences
                significant_sequences[seq_key] = seq_data

        # Trier par force du biais
        sorted_by_bias = sorted(
            significant_sequences.items(),
            key=lambda x: x[1]['bias_strength'],
            reverse=True
        )

        # Analyser les patterns les plus influents
        top_influential = {}
        for seq_key, seq_data in sorted_by_bias[:20]:  # Top 20
            top_influential[seq_key] = seq_data

        # Analyser par longueur de séquence
        by_length = defaultdict(list)
        for seq_key, seq_data in significant_sequences.items():
            by_length[seq_data['length']].append(seq_data)

        length_analysis = {}
        for length, sequences in by_length.items():
            if sequences:
                avg_bias = sum(seq['bias_strength'] for seq in sequences) / len(sequences)
                max_bias = max(seq['bias_strength'] for seq in sequences)
                length_analysis[length] = {
                    'count': len(sequences),
                    'avg_bias_strength': avg_bias,
                    'max_bias_strength': max_bias,
                    'significant_count': len([s for s in sequences if s['bias_strength'] > 0.1])
                }

        print(f"✅ Analyse terminée:")
        print(f"   • Séquences significatives: {len(significant_sequences):,}")
        print(f"   • Séquences très influentes: {len(top_influential)}")
        print()

        return {
            'significant_sequences': significant_sequences,
            'top_influential': top_influential,
            'length_analysis': length_analysis,
            'summary': {
                'total_significant': len(significant_sequences),
                'highly_influential': len([s for s in significant_sequences.values() if s['bias_strength'] > 0.15]),
                'avg_bias_all': sum(s['bias_strength'] for s in significant_sequences.values()) / len(significant_sequences) if significant_sequences else 0
            }
        }

    def discover_pure_sequences(self, sequences_data: Dict) -> Dict[str, Any]:
        """Découvre les séquences pures (100% PAIR ou 100% IMPAIR) et leur influence"""

        print("🎯 DÉCOUVERTE DES SÉQUENCES PURES")
        print("=" * 60)

        all_sequences = sequences_data['all_sequences']

        pure_pair_sequences = {}
        pure_impair_sequences = {}

        for seq_key, seq_data in all_sequences.items():
            sequence = seq_data['sequence']

            # Vérifier si c'est 100% PAIR (excluant le brûlage)
            if len(sequence) > 1:  # Au moins brûlage + 1 manche
                manches_only = sequence[1:]  # Exclure le brûlage

                if all(parity == 'PAIR' for parity in manches_only):
                    pure_pair_sequences[seq_key] = seq_data
                elif all(parity == 'IMPAIR' for parity in manches_only):
                    pure_impair_sequences[seq_key] = seq_data

        # Analyser l'influence des séquences pures
        def analyze_pure_influence(pure_sequences, purity_type):
            if not pure_sequences:
                return {'count': 0, 'avg_s_rate': 0, 'avg_bias': 0}

            s_rates = []
            biases = []

            for seq_data in pure_sequences.values():
                if seq_data['so_following']:
                    s_rates.append(seq_data['s_rate'])
                    biases.append(seq_data['bias_strength'])

            return {
                'count': len(pure_sequences),
                'with_so_data': len(s_rates),
                'avg_s_rate': sum(s_rates) / len(s_rates) if s_rates else 0,
                'avg_bias_strength': sum(biases) / len(biases) if biases else 0,
                'max_bias': max(biases) if biases else 0,
                'sequences': pure_sequences
            }

        pair_analysis = analyze_pure_influence(pure_pair_sequences, 'PAIR')
        impair_analysis = analyze_pure_influence(pure_impair_sequences, 'IMPAIR')

        print(f"✅ Découverte terminée:")
        print(f"   • Séquences 100% PAIR: {pair_analysis['count']}")
        print(f"   • Séquences 100% IMPAIR: {impair_analysis['count']}")
        print()

        return {
            'pure_pair': pair_analysis,
            'pure_impair': impair_analysis,
            'comparison': {
                'pair_vs_impair_bias': abs(pair_analysis['avg_bias_strength'] - impair_analysis['avg_bias_strength']),
                'pair_vs_impair_s_rate': abs(pair_analysis['avg_s_rate'] - impair_analysis['avg_s_rate'])
            }
        }

    def analyze_azr_prediction_readiness(self) -> Dict[str, Any]:
        """Analyse la disponibilité des données pour prédictions AZR"""

        games_with_azr_data = 0
        games_insufficient_data = 0
        azr_ready_examples = []

        for game in self.games_data:
            parity_seq = game.get('parity_sequence', [])
            sync_seq = game.get('sync_sequence', [])
            so_seq = game.get('so_sequence', [])
            result_seq = game.get('result_sequence', [])
            burn_parity = game.get('burn_parity')

            # Compter les manches P/B (exclure TIE)
            pb_count = sum(1 for result in result_seq if result in ['P', 'B'])

            if pb_count >= 2:  # Au moins 2 manches P/B = données pour prédire la 3ème
                games_with_azr_data += 1

                # Créer un exemple des données disponibles à la manche 3
                if len(azr_ready_examples) < 3:
                    # Données disponibles pour prédire la manche 3
                    available_data = {
                        'game_number': game['game_number'],
                        'burn_parity': burn_parity,
                        'parity_sequence_m1_m2': parity_seq[:2] if len(parity_seq) >= 2 else parity_seq,
                        'sync_sequence_m1_m2': sync_seq[:2] if len(sync_seq) >= 2 else sync_seq,
                        'pb_results_m1_m2': [r for r in result_seq[:2] if r in ['P', 'B']],
                        'first_so': so_seq[1] if len(so_seq) > 1 and so_seq[1] in ['S', 'O'] else None,
                        'complete_sequence_for_m3': {
                            'full_parity': [burn_parity] + parity_seq[:2],
                            'full_sync': ['SYNC' if burn_parity == 'PAIR' else 'DESYNC'] + sync_seq[:2],
                            'pb_sequence': [r for r in result_seq if r in ['P', 'B']][:2]
                        }
                    }
                    azr_ready_examples.append(available_data)
            else:
                games_insufficient_data += 1

        return {
            'total_games': len(self.games_data),
            'games_with_azr_data': games_with_azr_data,
            'games_insufficient_data': games_insufficient_data,
            'azr_readiness_rate': games_with_azr_data / len(self.games_data) if self.games_data else 0,
            'azr_ready_examples': azr_ready_examples,
            'theory_validation': {
                'manche_1_has_so': False,  # Par définition
                'manche_2_has_so': True,   # Premier S/O disponible
                'manche_3_prediction_ready': games_with_azr_data > 0
            }
        }

    def analyze_combined_index_influence(self) -> Dict[str, Any]:
        """Analyse l'influence de l'index combiné PAIR/IMPAIR+SYNC/DESYNC → S/O"""

        combined_analysis = {
            'PAIR_SYNC': {'s_count': 0, 'total': 0},
            'PAIR_DESYNC': {'s_count': 0, 'total': 0},
            'IMPAIR_SYNC': {'s_count': 0, 'total': 0},
            'IMPAIR_DESYNC': {'s_count': 0, 'total': 0}
        }

        for game in self.games_data:
            combined_seq = game.get('combined_sequence', [])
            so_seq = game.get('so_sequence', [])

            # Analyser chaque position
            for combined_state, so in zip(combined_seq, so_seq):
                if so in ['S', 'O'] and combined_state in combined_analysis:
                    combined_analysis[combined_state]['total'] += 1
                    if so == 'S':
                        combined_analysis[combined_state]['s_count'] += 1

        # Calculer les taux et significativité
        analysis_results = {}
        for state, data in combined_analysis.items():
            if data['total'] > 0:
                s_rate = data['s_count'] / data['total']
                analysis_results[state] = {
                    'total': data['total'],
                    's_count': data['s_count'],
                    'o_count': data['total'] - data['s_count'],
                    's_rate': s_rate,
                    'o_rate': 1 - s_rate,
                    'bias_strength': abs(s_rate - 0.5),
                    'significant': abs(s_rate - 0.5) > 0.1 and data['total'] >= 10
                }

        # Identifier les patterns les plus significatifs
        significant_states = {k: v for k, v in analysis_results.items() if v.get('significant', False)}

        # Calculer les différences entre états opposés
        comparisons = {}
        if 'PAIR_SYNC' in analysis_results and 'IMPAIR_DESYNC' in analysis_results:
            comparisons['PAIR_SYNC_vs_IMPAIR_DESYNC'] = {
                'difference': abs(analysis_results['PAIR_SYNC']['s_rate'] - analysis_results['IMPAIR_DESYNC']['s_rate']),
                'pair_sync_rate': analysis_results['PAIR_SYNC']['s_rate'],
                'impair_desync_rate': analysis_results['IMPAIR_DESYNC']['s_rate']
            }

        if 'PAIR_DESYNC' in analysis_results and 'IMPAIR_SYNC' in analysis_results:
            comparisons['PAIR_DESYNC_vs_IMPAIR_SYNC'] = {
                'difference': abs(analysis_results['PAIR_DESYNC']['s_rate'] - analysis_results['IMPAIR_SYNC']['s_rate']),
                'pair_desync_rate': analysis_results['PAIR_DESYNC']['s_rate'],
                'impair_sync_rate': analysis_results['IMPAIR_SYNC']['s_rate']
            }

        return {
            'combined_states_analysis': analysis_results,
            'significant_states': significant_states,
            'state_comparisons': comparisons,
            'total_samples': sum(data['total'] for data in analysis_results.values()),
            'most_biased_state': max(analysis_results.items(), key=lambda x: x[1]['bias_strength']) if analysis_results else None
        }

    def find_combined_patterns(self, pattern_length: int = 3) -> Dict[str, Any]:
        """Trouve les patterns de l'index combiné les plus influents sur S/O"""

        pattern_counts = defaultdict(int)
        pattern_so_following = defaultdict(list)

        for game in self.games_data:
            combined_seq = game.get('combined_sequence', [])
            so_seq = game.get('so_sequence', [])

            # Chercher les patterns
            for i in range(len(combined_seq) - pattern_length + 1):
                pattern = tuple(combined_seq[i:i+pattern_length])
                pattern_counts[pattern] += 1

                # Chercher le S/O qui suit
                for j in range(i + pattern_length, len(so_seq)):
                    if so_seq[j] in ['S', 'O']:
                        pattern_so_following[pattern].append(so_seq[j])
                        break

        # Analyser les patterns les plus fréquents
        significant_patterns = {}
        for pattern, count in sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)[:15]:
            following_so = pattern_so_following[pattern]
            if len(following_so) >= 5:  # Minimum pour analyse
                s_count = following_so.count('S')
                s_rate = s_count / len(following_so)
                bias_strength = abs(s_rate - 0.5)

                if bias_strength > 0.15:  # Seuil de significativité
                    significant_patterns[pattern] = {
                        'occurrences': count,
                        'following_so_count': len(following_so),
                        's_count': s_count,
                        's_rate': s_rate,
                        'bias_strength': bias_strength,
                        'bias_direction': 'S' if s_rate > 0.5 else 'O'
                    }

        return {
            'pattern_length': pattern_length,
            'total_patterns_found': len(pattern_counts),
            'significant_patterns': significant_patterns,
            'top_pattern': max(significant_patterns.items(), key=lambda x: x[1]['bias_strength']) if significant_patterns else None
        }

    def analyze_sync_parity_combinations(self) -> Dict[str, Any]:
        """Analyse les 4 combinaisons SYNC/DESYNC + PAIR/IMPAIR"""
        
        combinations = {
            'SYNC_PAIR': {'s_count': 0, 'total': 0},
            'SYNC_IMPAIR': {'s_count': 0, 'total': 0},
            'DESYNC_PAIR': {'s_count': 0, 'total': 0},
            'DESYNC_IMPAIR': {'s_count': 0, 'total': 0}
        }
        
        for game in self.games_data:
            parity_seq = game['parity_sequence']
            sync_seq = game['sync_sequence']
            so_seq = game['so_sequence']
            
            for parity, sync, so in zip(parity_seq, sync_seq, so_seq):
                if so in ['S', 'O']:
                    combo_key = f"{sync}_{parity}"
                    if combo_key in combinations:
                        combinations[combo_key]['total'] += 1
                        if so == 'S':
                            combinations[combo_key]['s_count'] += 1
        
        # Calculer les taux et significativité
        analysis = {}
        for combo, data in combinations.items():
            if data['total'] > 0:
                s_rate = data['s_count'] / data['total']
                analysis[combo] = {
                    'total': data['total'],
                    's_count': data['s_count'],
                    's_rate': s_rate,
                    'bias_strength': abs(s_rate - 0.5),
                    'significant': abs(s_rate - 0.5) > 0.1 and data['total'] >= 10
                }
        
        return {
            'combinations': analysis,
            'significant_combinations': {k: v for k, v in analysis.items() if v.get('significant', False)},
            'total_samples': sum(data['total'] for data in analysis.values())
        }

    # ========================================================================
    # 📊 RAPPORT RAPIDE
    # ========================================================================

    def generate_quick_report(self) -> str:
        """Génère un rapport rapide et lisible"""
        
        report = []
        report.append("📊 RAPPORT RAPIDE - ANALYSE TABULAIRE BACCARAT")
        report.append("=" * 70)
        report.append(f"Fichier analysé: {self.filename}")
        report.append(f"Date d'analyse: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Parties analysées: {len(self.games_data):,}")
        report.append("")

        # Vérification de cohérence P/B ↔ S/O
        pb_so_consistency = self.verify_pb_so_consistency()
        report.append("🔍 COHÉRENCE INDEX P/B/T ↔ S/O")
        report.append("-" * 40)
        report.append(f"Parties cohérentes: {pb_so_consistency['consistent_games']:,}/{pb_so_consistency['total_games']:,} "
                     f"({pb_so_consistency['consistency_rate']:.1%})")
        report.append(f"Total manches P/B vérifiées: {pb_so_consistency['total_pb_hands']:,}")
        report.append(f"Total conversions S/O vérifiées: {pb_so_consistency['total_so_conversions']:,}")

        if pb_so_consistency['inconsistent_games'] > 0:
            report.append(f"⚠️  Incohérences détectées: {pb_so_consistency['inconsistent_games']}")
            report.append(f"Erreur la plus fréquente: {pb_so_consistency['summary']['most_common_error']}")

            # Exemples d'erreurs
            for detail in pb_so_consistency['inconsistency_details'][:2]:
                if detail['issue'] == 'logic_errors':
                    report.append(f"  Partie {detail['game']}: erreurs de logique S/O")
                    for error in detail['errors'][:1]:
                        report.append(f"    Position {error['position']}: {error['issue']}")
                elif detail['issue'] == 'length_mismatch':
                    report.append(f"  Partie {detail['game']}: longueurs différentes "
                                 f"(P/B/T: {detail['result_length']}, S/O: {detail['so_length']})")
        else:
            report.append("✅ Tous les index P/B/T et S/O sont parfaitement cohérents")
        report.append("")

        # Vérification de cohérence brûlage
        consistency = self.verify_burn_sync_consistency()
        report.append("🔍 VÉRIFICATION COHÉRENCE BRÛLAGE")
        report.append("-" * 40)
        report.append(f"Parties cohérentes: {consistency['consistent_games']:,}/{consistency['total_games']:,} "
                     f"({consistency['consistency_rate']:.1%})")
        if consistency['inconsistent_games'] > 0:
            report.append(f"⚠️  Incohérences détectées: {consistency['inconsistent_games']}")
            for detail in consistency['inconsistent_details'][:2]:
                report.append(f"  Partie {detail['game']}: {detail['burn_parity']} → "
                             f"attendu {detail['expected_initial']}, trouvé {detail.get('declared_initial', 'N/A')}")
        report.append("")

        # Analyse de disponibilité des données AZR
        azr_readiness = self.analyze_azr_prediction_readiness()
        report.append("🎯 DISPONIBILITÉ DONNÉES POUR PRÉDICTIONS AZR")
        report.append("-" * 40)
        report.append(f"Parties avec données suffisantes: {azr_readiness['games_with_azr_data']:,}/{azr_readiness['total_games']:,} "
                     f"({azr_readiness['azr_readiness_rate']:.1%})")
        report.append(f"Parties données insuffisantes: {azr_readiness['games_insufficient_data']:,}")

        if azr_readiness['azr_ready_examples']:
            report.append("\nExemple de données disponibles à la manche 3:")
            example = azr_readiness['azr_ready_examples'][0]
            report.append(f"  Partie {example['game_number']}:")
            report.append(f"    Brûlage: {example['burn_parity']}")
            report.append(f"    PAIR/IMPAIR M1-M2: {example['parity_sequence_m1_m2']}")
            report.append(f"    SYNC/DESYNC M1-M2: {example['sync_sequence_m1_m2']}")
            report.append(f"    Résultats P/B: {example['pb_results_m1_m2']}")
            report.append(f"    Premier S/O: {example['first_so']}")
            report.append(f"    → Séquence complète: {example['complete_sequence_for_m3']['full_parity']}")
        report.append("")

        # Analyse PAIR/IMPAIR → S/O
        parity_analysis = self.quick_parity_so_analysis()
        report.append("🎯 INFLUENCE PAIR/IMPAIR → S/O")
        report.append("-" * 40)
        report.append(f"Échantillon: {parity_analysis['comparison']['sample_size']:,} manches P/B")
        report.append(f"PAIR → S: {parity_analysis['pair_analysis']['s_rate']:.1%} "
                     f"({parity_analysis['pair_analysis']['total']:,} échantillons)")
        report.append(f"IMPAIR → S: {parity_analysis['impair_analysis']['s_rate']:.1%} "
                     f"({parity_analysis['impair_analysis']['total']:,} échantillons)")
        report.append(f"Différence: {parity_analysis['comparison']['difference']:.1%}")
        report.append(f"Influence significative: {'OUI' if parity_analysis['comparison']['significant'] else 'NON'}")
        report.append("")
        
        # Influence du brûlage
        burn_analysis = self.analyze_burn_influence_quick()
        report.append("🔥 INFLUENCE DU BRÛLAGE")
        report.append("-" * 40)
        report.append(f"Parties brûlage PAIR: {burn_analysis['burn_pair']['games']:,} "
                     f"(taux S moyen: {burn_analysis['burn_pair']['avg_s_rate']:.1%})")
        report.append(f"Parties brûlage IMPAIR: {burn_analysis['burn_impair']['games']:,} "
                     f"(taux S moyen: {burn_analysis['burn_impair']['avg_s_rate']:.1%})")
        report.append(f"Différence: {burn_analysis['difference']:.1%}")
        report.append(f"Influence significative: {'OUI' if burn_analysis['significant'] else 'NON'}")
        report.append("")
        
        # Patterns de parité
        patterns = self.find_parity_patterns(3)
        report.append("🔍 PATTERNS DE PARITÉ (longueur 3)")
        report.append("-" * 40)
        report.append(f"Patterns trouvés: {patterns['total_patterns_found']}")
        report.append(f"Patterns significatifs: {len(patterns['significant_patterns'])}")
        
        if patterns['significant_patterns']:
            report.append("Top 3 patterns significatifs:")
            for i, (pattern, data) in enumerate(list(patterns['significant_patterns'].items())[:3], 1):
                pattern_str = "→".join(pattern)
                report.append(f"  {i}. {pattern_str}: {data['s_rate']:.1%} "
                             f"(biais: {data['bias_strength']:.1%}, {data['occurrences']} fois)")
        report.append("")
        
        # INDEX COMBINÉ - Analyse principale
        combined_analysis = self.analyze_combined_index_influence()
        report.append("🔗 INDEX COMBINÉ (PAIR/IMPAIR + SYNC/DESYNC)")
        report.append("-" * 40)
        report.append(f"Échantillon total: {combined_analysis['total_samples']:,}")
        report.append(f"États significatifs: {len(combined_analysis['significant_states'])}")

        for state, data in combined_analysis['combined_states_analysis'].items():
            significant_mark = " ⭐" if data.get('significant', False) else ""
            report.append(f"{state}: {data['s_rate']:.1%} ({data['total']:,} échantillons){significant_mark}")

        # Comparaisons d'états opposés
        if combined_analysis['state_comparisons']:
            report.append("\nComparaisons d'états opposés:")
            for comp_name, comp_data in combined_analysis['state_comparisons'].items():
                report.append(f"  {comp_name}: différence {comp_data['difference']:.1%}")

        # État le plus biaisé
        if combined_analysis['most_biased_state']:
            most_biased = combined_analysis['most_biased_state']
            report.append(f"\nÉtat le plus biaisé: {most_biased[0]} (biais: {most_biased[1]['bias_strength']:.1%})")
        report.append("")

        # Patterns de l'index combiné
        combined_patterns = self.find_combined_patterns(3)
        report.append("🎯 PATTERNS INDEX COMBINÉ (longueur 3)")
        report.append("-" * 40)
        report.append(f"Patterns significatifs trouvés: {len(combined_patterns['significant_patterns'])}")

        if combined_patterns['significant_patterns']:
            report.append("Top 3 patterns les plus influents:")
            for i, (pattern, data) in enumerate(list(combined_patterns['significant_patterns'].items())[:3], 1):
                pattern_str = " → ".join(pattern)
                report.append(f"  {i}. {pattern_str}")
                report.append(f"     {data['s_rate']:.1%} vers {data['bias_direction']} (biais: {data['bias_strength']:.1%}, {data['occurrences']} fois)")

        if combined_patterns['top_pattern']:
            top = combined_patterns['top_pattern']
            pattern_str = " → ".join(top[0])
            report.append(f"\nPattern le plus influent: {pattern_str}")
            report.append(f"  Biais: {top[1]['bias_strength']:.1%} vers {top[1]['bias_direction']}")
        report.append("")
        
        report.append("✅ FIN DU RAPPORT RAPIDE")
        
        return "\n".join(report)

    def save_quick_report(self, output_filename: str = None) -> str:
        """Sauvegarde le rapport rapide"""
        
        if not output_filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_name = os.path.splitext(os.path.basename(self.filename))[0]
            output_filename = f"rapport_rapide_{base_name}_{timestamp}.txt"
        
        report = self.generate_quick_report()
        
        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 Rapport rapide sauvegardé: {output_filename}")
        return output_filename

def run_new_sequence_analysis(analyzer):
    """NOUVELLE APPROCHE: Analyse massive des séquences PAIR/IMPAIR"""

    print("\n🎯 NOUVELLE APPROCHE: ANALYSE MASSIVE DES SÉQUENCES")
    print("=" * 70)
    print("Objectif: Découvrir l'influence des séquences PAIR/IMPAIR sur les S/O")
    print()

    # Étape 1: Extraction de toutes les séquences
    print("ÉTAPE 1: Extraction de toutes les séquences...")
    sequences_data = analyzer.extract_all_parity_sequences()

    # Étape 2: Analyse des patterns d'influence
    print("ÉTAPE 2: Analyse des patterns d'influence...")
    influence_data = analyzer.analyze_sequence_influence_patterns(sequences_data)

    # Étape 3: Découverte des séquences pures
    print("ÉTAPE 3: Découverte des séquences pures...")
    pure_data = analyzer.discover_pure_sequences(sequences_data)

    # Affichage des résultats
    print("\n🎉 RÉSULTATS DE L'ANALYSE MASSIVE")
    print("=" * 70)

    print(f"📊 STATISTIQUES GLOBALES:")
    print(f"   • Parties analysées: {sequences_data['games_analyzed']:,}")
    print(f"   • Séquences uniques trouvées: {sequences_data['total_unique_sequences']:,}")
    print(f"   • Total occurrences: {sequences_data['total_occurrences']:,}")
    print()

    print(f"🔍 SÉQUENCES SIGNIFICATIVES:")
    print(f"   • Avec données suffisantes (10+ occurrences): {influence_data['summary']['total_significant']:,}")
    print(f"   • Très influentes (biais > 15%): {influence_data['summary']['highly_influential']}")
    print(f"   • Biais moyen: {influence_data['summary']['avg_bias_all']:.1%}")
    print()

    print(f"🎯 SÉQUENCES PURES DÉCOUVERTES:")
    print(f"   • Séquences 100% PAIR: {pure_data['pure_pair']['count']}")
    print(f"   • Séquences 100% IMPAIR: {pure_data['pure_impair']['count']}")
    print(f"   • Différence d'influence PAIR vs IMPAIR: {pure_data['comparison']['pair_vs_impair_s_rate']:.1%}")
    print()

    # Top 10 des séquences les plus influentes
    print(f"🏆 TOP 10 SÉQUENCES LES PLUS INFLUENTES:")
    print("-" * 70)
    print("Rang | Séquence | Occurrences | Taux S | Biais")
    print("-" * 70)

    top_sequences = sorted(
        influence_data['top_influential'].items(),
        key=lambda x: x[1]['bias_strength'],
        reverse=True
    )[:10]

    for i, (seq_key, seq_data) in enumerate(top_sequences, 1):
        sequence_display = seq_key.replace('_', ' → ')
        print(f"{i:4d} | {sequence_display:<20} | {seq_data['occurrences']:11d} | {seq_data['s_rate']:6.1%} | {seq_data['bias_strength']:5.1%}")

    print("-" * 70)
    print()

    # Analyse par longueur
    print(f"📏 INFLUENCE PAR LONGUEUR DE SÉQUENCE:")
    print("-" * 50)
    print("Longueur | Nombre | Biais Moyen | Biais Max")
    print("-" * 50)

    for length in sorted(influence_data['length_analysis'].keys()):
        data = influence_data['length_analysis'][length]
        print(f"{length:8d} | {data['count']:6d} | {data['avg_bias_strength']:11.1%} | {data['max_bias_strength']:9.1%}")

    print("-" * 50)
    print()

    # Conclusion
    print(f"💡 CONCLUSIONS:")
    if pure_data['pure_pair']['count'] > 0 or pure_data['pure_impair']['count'] > 0:
        print(f"✅ Des séquences pures ont été découvertes naturellement !")
        print(f"✅ Contournement réussi du problème des 20,748 parties")
    else:
        print(f"⚠️  Aucune séquence pure découverte - augmenter la taille de l'échantillon")

    if influence_data['summary']['highly_influential'] > 0:
        print(f"✅ {influence_data['summary']['highly_influential']} séquences très influentes identifiées")
        print(f"✅ Patterns d'influence PAIR/IMPAIR → S/O confirmés")
    else:
        print(f"⚠️  Peu d'influence détectée - analyser plus de données")

    # Sauvegarder les séquences pour analyse détaillée
    save_sequences_for_analysis(sequences_data, influence_data, pure_data)

    return {
        'sequences_data': sequences_data,
        'influence_data': influence_data,
        'pure_data': pure_data
    }

def save_sequences_for_analysis(sequences_data, influence_data, pure_data):
    """Sauvegarde les séquences dans des fichiers pour analyse détaillée"""

    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    print(f"\n💾 SAUVEGARDE DES SÉQUENCES POUR ANALYSE...")

    # 1. Sauvegarder TOUTES les séquences uniques
    all_sequences_file = f"sequences_uniques_{timestamp}.txt"
    with open(all_sequences_file, 'w', encoding='utf-8') as f:
        f.write("🔍 TOUTES LES SÉQUENCES UNIQUES DÉCOUVERTES\n")
        f.write("=" * 80 + "\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total séquences: {len(sequences_data['all_sequences'])}\n")
        f.write(f"Parties analysées: {sequences_data['games_analyzed']:,}\n")
        f.write("=" * 80 + "\n\n")

        # Trier par nombre d'occurrences
        sorted_sequences = sorted(
            sequences_data['all_sequences'].items(),
            key=lambda x: x[1]['occurrences'],
            reverse=True
        )

        for i, (seq_key, seq_data) in enumerate(sorted_sequences, 1):
            f.write(f"SÉQUENCE #{i}\n")
            f.write("-" * 40 + "\n")
            f.write(f"Pattern: {seq_key}\n")
            f.write(f"Séquence: {' → '.join(seq_data['sequence'])}\n")
            f.write(f"Longueur: {seq_data['length']}\n")
            f.write(f"Occurrences: {seq_data['occurrences']:,}\n")
            f.write(f"S/O suivants: {len(seq_data['so_following'])}\n")
            if seq_data['so_following']:
                f.write(f"Taux S: {seq_data['s_rate']:.1%}\n")
                f.write(f"Biais: {seq_data['bias_strength']:.1%}\n")
            f.write("\n")

    # 2. Sauvegarder les séquences PURES
    pure_sequences_file = f"sequences_pures_{timestamp}.txt"
    with open(pure_sequences_file, 'w', encoding='utf-8') as f:
        f.write("🎯 SÉQUENCES PURES DÉCOUVERTES\n")
        f.write("=" * 80 + "\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Séquences 100% PAIR: {pure_data['pure_pair']['count']}\n")
        f.write(f"Séquences 100% IMPAIR: {pure_data['pure_impair']['count']}\n")
        f.write("=" * 80 + "\n\n")

        # Séquences PAIR
        f.write("📊 SÉQUENCES 100% PAIR\n")
        f.write("-" * 50 + "\n")
        pair_sequences = sorted(
            pure_data['pure_pair']['sequences'].items(),
            key=lambda x: x[1]['length']
        )

        for i, (seq_key, seq_data) in enumerate(pair_sequences, 1):
            f.write(f"PAIR #{i}\n")
            f.write(f"Pattern: {seq_key}\n")
            f.write(f"Séquence: {' → '.join(seq_data['sequence'])}\n")
            f.write(f"Longueur: {seq_data['length']}\n")
            f.write(f"Occurrences: {seq_data['occurrences']:,}\n")
            if seq_data['so_following']:
                f.write(f"Taux S: {seq_data['s_rate']:.1%}\n")
                f.write(f"Biais: {seq_data['bias_strength']:.1%}\n")
            f.write("\n")

        # Séquences IMPAIR
        f.write("\n📊 SÉQUENCES 100% IMPAIR\n")
        f.write("-" * 50 + "\n")
        impair_sequences = sorted(
            pure_data['pure_impair']['sequences'].items(),
            key=lambda x: x[1]['length']
        )

        for i, (seq_key, seq_data) in enumerate(impair_sequences, 1):
            f.write(f"IMPAIR #{i}\n")
            f.write(f"Pattern: {seq_key}\n")
            f.write(f"Séquence: {' → '.join(seq_data['sequence'])}\n")
            f.write(f"Longueur: {seq_data['length']}\n")
            f.write(f"Occurrences: {seq_data['occurrences']:,}\n")
            if seq_data['so_following']:
                f.write(f"Taux S: {seq_data['s_rate']:.1%}\n")
                f.write(f"Biais: {seq_data['bias_strength']:.1%}\n")
            f.write("\n")

    # 3. Sauvegarder les séquences les plus influentes
    top_sequences_file = f"sequences_influentes_{timestamp}.txt"
    with open(top_sequences_file, 'w', encoding='utf-8') as f:
        f.write("🏆 SÉQUENCES LES PLUS INFLUENTES\n")
        f.write("=" * 80 + "\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Critère: Biais > 15% OU Top 50 par biais\n")
        f.write("=" * 80 + "\n\n")

        # Top 50 par biais
        top_sequences = sorted(
            influence_data['significant_sequences'].items(),
            key=lambda x: x[1]['bias_strength'],
            reverse=True
        )[:50]

        for i, (seq_key, seq_data) in enumerate(top_sequences, 1):
            f.write(f"TOP #{i}\n")
            f.write("-" * 30 + "\n")
            f.write(f"Pattern: {seq_key}\n")
            f.write(f"Séquence: {' → '.join(seq_data['sequence'])}\n")
            f.write(f"Longueur: {seq_data['length']}\n")
            f.write(f"Occurrences: {seq_data['occurrences']:,}\n")
            f.write(f"S/O suivants: {len(seq_data['so_following'])}\n")
            f.write(f"Taux S: {seq_data['s_rate']:.1%}\n")
            f.write(f"Biais: {seq_data['bias_strength']:.1%}\n")
            f.write("\n")

    print(f"✅ Séquences sauvegardées:")
    print(f"   📁 Toutes les séquences: {all_sequences_file}")
    print(f"   🎯 Séquences pures: {pure_sequences_file}")
    print(f"   🏆 Séquences influentes: {top_sequences_file}")
    print()

def main():
    """Fonction principale"""
    
    if len(sys.argv) < 2:
        print("Usage: python baccarat_tabular_analyzer.py <fichier_donnees.txt>")
        print("\nFichiers disponibles:")
        txt_files = [f for f in os.listdir('.') if f.endswith('.txt') and 'baccarat' in f.lower()]
        for i, file in enumerate(txt_files, 1):
            print(f"  {i}. {file}")
        
        if txt_files:
            choice = input(f"\nChoisir un fichier (1-{len(txt_files)}): ").strip()
            try:
                filename = txt_files[int(choice) - 1]
            except (ValueError, IndexError):
                print("❌ Choix invalide")
                return
        else:
            print("❌ Aucun fichier TXT baccarat trouvé")
            return
    else:
        filename = sys.argv[1]
    
    try:
        # Créer l'analyseur
        analyzer = BaccaratTabularAnalyzer(filename)

        # Menu de choix
        print("\n🎯 CHOISISSEZ LE TYPE D'ANALYSE:")
        print("1. 📊 Analyse rapide standard")
        print("2. 🎯 NOUVELLE APPROCHE: Analyse massive des séquences PAIR/IMPAIR")

        choice = input("Votre choix (1-2): ").strip()

        if choice == "2":
            # Nouvelle approche
            run_new_sequence_analysis(analyzer)
        else:
            # Analyse standard
            print("\n" + analyzer.generate_quick_report())

            # Sauvegarder le rapport
            report_file = analyzer.save_quick_report()

            print(f"\n🎉 Analyse rapide terminée!")
            print(f"📄 Rapport détaillé: {report_file}")

    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
