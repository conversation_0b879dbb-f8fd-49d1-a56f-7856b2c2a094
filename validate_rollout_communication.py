#!/usr/bin/env python3
"""
Validation de la communication entre les rollouts

Ce script vérifie que le format du rapport du Rollout 1 est correctement
traité par le Rollout 2, et que les nouvelles sections optimisées sont utilisées.
"""

import re
from typing import Dict, List, Tuple

def analyze_rollout1_output_format(file_content: str) -> Dict:
    """Analyse le format de sortie du Rollout 1"""
    
    # Rechercher la structure du rapport analyzer_report
    analyzer_report_pattern = r"analyzer_report\s*=\s*\{(.*?)\}"
    match = re.search(analyzer_report_pattern, file_content, re.DOTALL)
    
    output_format = {
        'has_analyzer_report': bool(match),
        'sections_found': [],
        'optimized_sections': [],
        'legacy_sections': []
    }
    
    if match:
        report_content = match.group(1)
        
        # Sections optimisées pour le Rollout 2
        optimized_sections = [
            'signals_summary',
            'generation_guidance', 
            'quick_access'
        ]
        
        # Sections legacy
        legacy_sections = [
            'indices_analysis',
            'synthesis',
            'sequence_metadata'
        ]
        
        for section in optimized_sections:
            if section in report_content:
                output_format['optimized_sections'].append(section)
                output_format['sections_found'].append(section)
        
        for section in legacy_sections:
            if section in report_content:
                output_format['legacy_sections'].append(section)
                output_format['sections_found'].append(section)
    
    return output_format

def analyze_rollout2_input_processing(file_content: str) -> Dict:
    """Analyse comment le Rollout 2 traite le rapport reçu"""
    
    # Rechercher la méthode _rollout_generator
    generator_method_pattern = r"def _rollout_generator\(self, analyzer_report.*?\):(.*?)(?=def|\Z)"
    match = re.search(generator_method_pattern, file_content, re.DOTALL)
    
    input_processing = {
        'has_generator_method': bool(match),
        'uses_optimized_sections': False,
        'uses_legacy_sections': False,
        'extraction_methods': [],
        'generation_methods': []
    }
    
    if match:
        method_content = match.group(1)
        
        # Vérifier l'utilisation des sections optimisées
        optimized_extractions = [
            'signals_summary',
            'generation_guidance',
            'quick_access'
        ]
        
        for section in optimized_extractions:
            if f"analyzer_report.get('{section}'" in method_content:
                input_processing['uses_optimized_sections'] = True
                input_processing['extraction_methods'].append(section)
        
        # Vérifier l'utilisation des sections legacy
        legacy_extractions = [
            'indices_analysis',
            'synthesis', 
            'sequence_metadata'
        ]
        
        for section in legacy_extractions:
            if f"analyzer_report.get('{section}'" in method_content:
                input_processing['uses_legacy_sections'] = True
                input_processing['extraction_methods'].append(section)
        
        # Vérifier les méthodes de génération
        generation_methods = [
            '_define_optimized_generation_space',
            '_generate_sequences_from_signals',
            '_generate_fallback_sequences'
        ]
        
        for method in generation_methods:
            if method in method_content:
                input_processing['generation_methods'].append(method)
    
    return input_processing

def check_optimized_methods_existence(file_content: str) -> Dict:
    """Vérifie l'existence des nouvelles méthodes optimisées"""
    
    required_methods = [
        '_define_optimized_generation_space',
        '_generate_sequences_from_signals', 
        '_generate_sequence_from_signal',
        '_generate_fallback_sequences',
        '_generate_signals_summary',
        '_generate_generation_guidance',
        '_generate_quick_access'
    ]
    
    methods_status = {}
    
    for method in required_methods:
        pattern = rf"def {re.escape(method)}\("
        if re.search(pattern, file_content):
            methods_status[method] = True
        else:
            methods_status[method] = False
    
    return methods_status

def analyze_communication_flow(file_content: str) -> Dict:
    """Analyse le flux de communication complet"""
    
    communication_flow = {
        'rollout1_generates_optimized': False,
        'rollout2_uses_optimized': False,
        'fallback_mechanism': False,
        'communication_quality': 'unknown'
    }
    
    # Vérifier que le Rollout 1 génère les sections optimisées
    if all(section in file_content for section in [
        '_generate_signals_summary',
        '_generate_generation_guidance', 
        '_generate_quick_access'
    ]):
        communication_flow['rollout1_generates_optimized'] = True
    
    # Vérifier que le Rollout 2 utilise les sections optimisées
    if '_define_optimized_generation_space' in file_content and '_generate_sequences_from_signals' in file_content:
        communication_flow['rollout2_uses_optimized'] = True
    
    # Vérifier le mécanisme de fallback
    if '_generate_fallback_sequences' in file_content:
        communication_flow['fallback_mechanism'] = True
    
    # Évaluer la qualité de communication
    if communication_flow['rollout1_generates_optimized'] and communication_flow['rollout2_uses_optimized']:
        if communication_flow['fallback_mechanism']:
            communication_flow['communication_quality'] = 'excellent'
        else:
            communication_flow['communication_quality'] = 'good'
    elif communication_flow['rollout1_generates_optimized'] or communication_flow['rollout2_uses_optimized']:
        communication_flow['communication_quality'] = 'partial'
    else:
        communication_flow['communication_quality'] = 'poor'
    
    return communication_flow

def validate_rollout_communication():
    """Validation principale de la communication entre rollouts"""
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return
    
    print("🔄 VALIDATION DE LA COMMUNICATION ROLLOUT 1 → ROLLOUT 2")
    print("=" * 60)
    
    # 1. Analyser le format de sortie du Rollout 1
    print("\n📤 1. ANALYSE DU FORMAT DE SORTIE ROLLOUT 1")
    print("-" * 45)
    
    rollout1_output = analyze_rollout1_output_format(file_content)
    
    if rollout1_output['has_analyzer_report']:
        print("✅ Structure analyzer_report trouvée")
        print(f"📊 Sections trouvées : {len(rollout1_output['sections_found'])}")
        
        if rollout1_output['optimized_sections']:
            print("✅ Sections optimisées :")
            for section in rollout1_output['optimized_sections']:
                print(f"   ✓ {section}")
        else:
            print("❌ Aucune section optimisée trouvée")
        
        if rollout1_output['legacy_sections']:
            print("📋 Sections legacy :")
            for section in rollout1_output['legacy_sections']:
                print(f"   ○ {section}")
    else:
        print("❌ Structure analyzer_report non trouvée")
    
    # 2. Analyser le traitement par le Rollout 2
    print("\n📥 2. ANALYSE DU TRAITEMENT ROLLOUT 2")
    print("-" * 40)
    
    rollout2_input = analyze_rollout2_input_processing(file_content)
    
    if rollout2_input['has_generator_method']:
        print("✅ Méthode _rollout_generator trouvée")
        
        if rollout2_input['uses_optimized_sections']:
            print("✅ Utilise les sections optimisées")
            print("📊 Sections extraites :")
            for method in rollout2_input['extraction_methods']:
                if method in ['signals_summary', 'generation_guidance', 'quick_access']:
                    print(f"   ✓ {method}")
        else:
            print("❌ N'utilise PAS les sections optimisées")
        
        if rollout2_input['generation_methods']:
            print("🔧 Méthodes de génération :")
            for method in rollout2_input['generation_methods']:
                print(f"   ✓ {method}")
        else:
            print("⚠️  Aucune méthode de génération optimisée")
    else:
        print("❌ Méthode _rollout_generator non trouvée")
    
    # 3. Vérifier l'existence des méthodes optimisées
    print("\n🔧 3. VÉRIFICATION DES MÉTHODES OPTIMISÉES")
    print("-" * 45)
    
    methods_status = check_optimized_methods_existence(file_content)
    
    existing_methods = sum(1 for exists in methods_status.values() if exists)
    total_methods = len(methods_status)
    
    print(f"📊 Méthodes implémentées : {existing_methods}/{total_methods}")
    
    for method, exists in methods_status.items():
        status = "✅" if exists else "❌"
        print(f"{status} {method}")
    
    # 4. Analyser le flux de communication global
    print("\n🔄 4. ANALYSE DU FLUX DE COMMUNICATION")
    print("-" * 40)
    
    communication_flow = analyze_communication_flow(file_content)
    
    print(f"📤 Rollout 1 génère optimisé : {'✅' if communication_flow['rollout1_generates_optimized'] else '❌'}")
    print(f"📥 Rollout 2 utilise optimisé : {'✅' if communication_flow['rollout2_uses_optimized'] else '❌'}")
    print(f"🔄 Mécanisme fallback      : {'✅' if communication_flow['fallback_mechanism'] else '❌'}")
    
    quality = communication_flow['communication_quality']
    if quality == 'excellent':
        print("🏆 Qualité communication : EXCELLENTE")
    elif quality == 'good':
        print("👍 Qualité communication : BONNE")
    elif quality == 'partial':
        print("⚠️  Qualité communication : PARTIELLE")
    else:
        print("❌ Qualité communication : DÉFAILLANTE")
    
    # 5. Recommandations
    print("\n💡 5. RECOMMANDATIONS")
    print("-" * 25)
    
    if communication_flow['communication_quality'] == 'excellent':
        print("🏆 PARFAIT : Communication optimisée fonctionnelle !")
        print("✅ Le Rollout 2 exploite efficacement les signaux du Rollout 1")
        print("✅ Mécanisme de fallback en place pour la robustesse")
    elif communication_flow['communication_quality'] == 'good':
        print("👍 BON : Communication fonctionnelle")
        if not communication_flow['fallback_mechanism']:
            print("⚠️  Ajouter un mécanisme de fallback pour plus de robustesse")
    else:
        print("⚠️  AMÉLIORATION NÉCESSAIRE :")
        if not communication_flow['rollout1_generates_optimized']:
            print("❌ Le Rollout 1 doit générer les sections optimisées")
        if not communication_flow['rollout2_uses_optimized']:
            print("❌ Le Rollout 2 doit utiliser les sections optimisées")
        if not communication_flow['fallback_mechanism']:
            print("❌ Implémenter un mécanisme de fallback")
    
    # 6. Score final
    print(f"\n📊 SCORE FINAL")
    print("-" * 15)
    
    score = 0
    if rollout1_output['has_analyzer_report']:
        score += 20
    if len(rollout1_output['optimized_sections']) >= 3:
        score += 25
    if rollout2_input['uses_optimized_sections']:
        score += 25
    if existing_methods >= 6:
        score += 20
    if communication_flow['communication_quality'] == 'excellent':
        score += 10
    
    print(f"Score communication : {score}/100")
    
    if score >= 90:
        print("🏆 EXCELLENT")
    elif score >= 70:
        print("👍 BON")
    elif score >= 50:
        print("⚠️  ACCEPTABLE")
    else:
        print("❌ NÉCESSITE AMÉLIORATION")

if __name__ == "__main__":
    validate_rollout_communication()
