# EXTRACTION TEXTUELLE - AlphaZero_MCTS_Reference.pdf
# Généré automatiquement le 2025-05-28 10:23:53
# Source: AlphaZero_MCTS_Reference.pdf
# ================================================================


--- PAGE 1 ---
NPFL122, Lecture 10
UCB, Monte Carlo Tree Search,
AlphaZero
Milan Straka
December 07, 2020
Charles University in Prague
Faculty of Mathematics and Physics
Institute of Formal and Applied Linguistics
unless otherwise stated
--- PAGE 2 ---
Upper Confidence Bound
Revisiting multi-armed bandits with ε-greedy exploration, we note that using same epsilon for
all actions in ε-greedy method seems inefficient.
One possible improvement is to select action according to upper confidence bound (instead of
choosing a random action with probability ε):
ln t
def
A = arg max Q (a) + c ,
t+1 t
N (a)
a [ t ]
where:
t is the number of times any action has been taken;
N (a) is the number of times the action a has been taken;
t
if N (a) = 0 , the right expression is frequently assumed to have a value of ∞ .
t
The updates are then performed as before (e.g., using averaging, or fixed learning rate α).
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 2/27
--- PAGE 3 ---
Motivation Behind Upper Confidence Bound
Actions with little average reward are probably selected too often.
Instead of simple ε-greedy approach, we might try selecting an action as little as possible, but
still enough to converge.
Assuming that random variables X bounded by [0, 1] and X ˉ = 1 ∑ N X ,
i N i=1 i
(Chernoff-)Hoeffding's inequality states that
ˉ ˉ −2Nδ2
P(E [X] − X ≥ δ) ≤ e .
Our goal is to choose δ such that for every action,
α
1
P(Q (a) ≤ q (a) − δ) ≤ .
t ∗
( t )
α
We can fulfil the required inequality if e−2N
t
(a)δ2
≤ (
1
)
, which yields
t
δ ≥ α/2 ⋅ (ln t)/N (a).
t
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 3/27
--- PAGE 4 ---
Asymptotical Optimality of UCB
We define regret as the difference of maximum of what we could get (i.e., repeatedly using the
action with maximum expectation) and what a strategy yields, i.e.,
N
def
regret = N max q (a) − [R ].
N ∗ E i
a
∑
i=1
It can be shown that regret of UCB is asymptotically optimal, see Lai and Robbins (1985),
Asymptotically Efficient Adaptive Allocation Rules.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 4/27
--- PAGE 5 ---
Upper Confidence Bound Results
Figure 2.4 of "Reinforcement Learning: An Introduction, Second Edition".
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 5/27
--- PAGE 6 ---
Multi-armed Bandits Comparison
Figure 2.6 of "Reinforcement Learning: An Introduction, Second Edition".
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 6/27
--- PAGE 7 ---
AlphaZero
On 7 December 2018, the AlphaZero paper came out in Science journal. It demonstrates
learning chess, shogi and go, tabula rasa – without any domain-specific human knowledge or
data, only using self-play. The evaluation is performed against strongest programs available.
Figure 2 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 7/27
--- PAGE 8 ---
AlphaZero – Overview
AlphaZero uses a neural network predicting (p(s), v(s)) = f(s; θ) for a given state s, where:
p(s) is a vector of move probabilities, and
v(s) is expected outcome of the game in range [−1, 1] .
Instead of the usual alpha-beta search used by classical game playing programs, AlphaZero uses
Monte Carlo Tree Search (MCTS).
By a sequence of simulated self-play games, the search can improve the estimate of p and v,
and can be considered a powerful policy evaluation operator – given a network f predicting
policy p and value estimate v, MCTS produces a more accurate policy π and better value
estimate w for a given state s:
(π(s), w(s)) ← MCTS(p(s), v(s), f) for (p(s), v(s)) = f(s; θ).
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 8/27
--- PAGE 9 ---
AlphaZero – Overview
The network is trained from self-play games.
A game is played by repeatedly running MCTS from a state s and choosing a move a ∼ π ,
t t t
until a terminal position s is encountered, which is then scored according to game rules as
T
z ∈ {−1, 0, 1} .
Finally, the network parameters are trained to minimize the error between the predicted outcome
v and the simulated outcome z, and maximize the similarity of the policy vector p and the
search probabilities π (in other words, we want to find a fixed point of the MCTS):
L = def (z − v) 2 + π T log p + c∥θ∥ 2 .
The loss is a combination of:
a mean squared error for the value functions;
a crossentropy/KL divergence for the action distribution;
L2 regularization.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 9/27
--- PAGE 10 ---
AlphaZero – Monte Carlo Tree Search
MCTS keeps a tree of currently explored states from a fixed root state. Each node corresponds
to a game state and to every non-root node we got by performing an action a from the parent
state. Each state-action pair (s, a) stores the following set of statistics:
visit count N(s, a) ,
total action-value W(s, a) ,
mean action value Q(s, a) = def W(s, a)/N(s, a) , which is usually not stored explicitly,
prior probability P(s, a) of selecting action a in state s.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 10/27
--- PAGE 11 ---
AlphaZero – Monte Carlo Tree Search
Each simulation starts in the root node and finishes in a leaf node s . In a state s , an action
L t
is selected using a variant of PUCT algorithm as
a = arg max (Q(s , a) + U(s , a)),
t a t t
where
N(s)
def
U(s, a) = C(s)P(s, a) ,
1 + N(s, a)
with C(s) = log 1+N(s)+c base + c being slightly time-increasing exploration rate.
( c
base
) init
The paper uses c = 1.25 , c = 19652 without any supporting experiments.
init base
Also, the reason for the modification of the UCB formula was never discussed in any AlphaZero
paper and is not obvious.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 11/27
--- PAGE 12 ---
AlphaZero – Monte Carlo Tree Search
Additionally, exploration in the root state s is supported by including a random sample from
root
Dirichlet distribution,
P(s , a) = (1 − ε)p + ε Dir(α),
root a
with ε = 0.25 and α = 0.3, 0.15, 0.03 for chess, shogi and go, respectively.
Note that using α < 1 makes the Dirichlet noise non-uniform, with a smaller number of actions
with high probability.
The Dirichlet distribution can be seen as a limit of the Pólya’s urn scheme, where in each step
we sample from a bowl of balls (with the initial counts α) and return an additional ball of the
same color to the bowl.
To sample from a symmetric Dirichlet distribution, we can:
sample x from a Gamma distribution x ∼ Gamma(α) ,
i i
normalize the sampled values to sum to one, p = x i .
i
∑
j
x
j
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 12/27
--- PAGE 13 ---
AlphaZero – Monte Carlo Tree Search
When reaching a leaf node s , we:
L
evaluate it by the network, generating (p, v) ,
add all its children with N = W = 0 and the prior probability p,
in the backward pass for all t ≤ L, we update the statistics in nodes by performing
N(s , a ) ← N(s , a ) + 1 , and
t t t t
W(s , a ) ← W(s , a ) ± v, depending on the player on turn.
t t t t
Figure 2 of the paper "Mastering the game of Go without human knowledge" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 13/27
--- PAGE 14 ---
AlphaZero – Monte Carlo Tree Search
The Monte Carlo Tree Search runs usually several hundreds simulations in a single tree. The
result is a distribution proportional to exponentiated visit counts N(s , a) 1 using a
root τ
temperature τ (τ = 1 is mostly used), together with the predicted value function.
The next move is chosen as either:
proportional to visit counts N(s , ⋅) 1 :
root
τ
1
π (a) ∝ N(s , a) ,
τ
root root
deterministically as the most visited action
π = arg max N(s , a).
root root
a
During self-play, the stochastic policy is used for the first 30 moves of the game, while the
deterministic is used for the rest of the moves. (This does not affect the internal MCTS search,
there we always sample according to PUCT rule.)
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 14/27
--- PAGE 15 ---
AlphaZero – Monte Carlo Tree Search Example
Visualization of the
10 most visited
states in a MCTS
with a given number
of simulations. The
displayed numbers
are predicted value
functions from the
white's perspective,
scaled to
[0, 100]
range. The border
thickness is
proportional to a
node visit count.
Figure 4 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 15/27
--- PAGE 16 ---
AlphaZero – Network Architecture
The network processes game-specific input, which consists of a history of 8 board positions
encoded by several N × N planes, and some number of constant-valued inputs.
Output is considered to be a categorical distribution of possible moves. For chess and shogi, for
each piece we consider all possible moves (56 queen moves, 8 knight moves and 9
underpromotions for chess).
The input is processed by:
initial convolution block with CNN with 256 kernels with stride 1, batch
3 × 3
normalization and ReLU activation,
19 residual blocks, each consisting of two CNN with 256 kernels with stride 1, batch
3 × 3
normalization and ReLU activation, and a residual connection around them,
policy head, which applies another CNN with batch normalization, followed by a convolution
with 73/139 filters for chess/shogi, or a linear layer of size 362 for go,
value head, which applies another CNN with one kernel with stride 1, followed by a
1 × 1
ReLU layer of size 256 and a final layer of size 1.
tanh
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 16/27
--- PAGE 17 ---
AlphaZero – Network Inputs
Table S1 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 17/27
--- PAGE 18 ---
AlphaZero – Network Outputs
Table S2 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 18/27
--- PAGE 19 ---
AlphaZero – Training
Training is performed by running self-play games of the network with itself. Each MCTS uses
800 simulations. A replay buffer of one million most recent games is kept.
During training, 5000 first-generation TPUs are used to generate self-play games.
Simultaneously, network is trained using SGD with momentum of 0.9 on batches of size 4096,
utilizing 16 second-generation TPUs. Training takes approximately 9 hours for chess, 12 hours
for shogi and 13 days for go.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 19/27
--- PAGE 20 ---
AlphaZero – Training
Figure 1 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
Table S3 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 20/27
--- PAGE 21 ---
AlphaZero – Training
According to the authors, training is highly repeatable.
Figure S3 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 21/27
--- PAGE 22 ---
AlphaZero – Symmetries
In the original AlphaGo Zero, symmetries (8 in total, using rotations and reflections) were
explicitly utilized, by
randomly sampling a symmetry during training,
randomly sampling a symmetry during MCTS evaluation.
However, AlphaZero does not utilize symmetries in any way (because chess and shogi do not
have them).
Figure S1 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 22/27
--- PAGE 23 ---
AlphaZero – Inference
During inference, AlphaZero utilizes much less evaluations than classical game playing programs.
Table S4 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 23/27
--- PAGE 24 ---
AlphaZero – Ablations
Table S8 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
Table S9 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 24/27
--- PAGE 25 ---
AlphaZero – Ablations
Figure 2 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 25/27
--- PAGE 26 ---
AlphaZero – Ablations
Figure 4 of the paper "Mastering the game of Go without human knowledge" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 26/27
--- PAGE 27 ---
AlphaZero – Preferred Chess Openings
Figure S2 of the paper "A general reinforcement learning algorithm that masters chess, shogi, and Go through self-play" by David Silver et al.
NPFL122, Lecture 10 UCB AlphaZero A0-MCTS A0-Network A0-Training A0-Evaluation 27/27