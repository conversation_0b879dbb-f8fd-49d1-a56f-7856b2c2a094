#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test de l'Optimisation 8 Séquences - Rollout 2

Ce script teste la nouvelle approche qui génère les 8 meilleures séquences
parmi les 16 possibilités (2^4) triées par probabilité décroissante.
"""

import sys
import os
import itertools
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import AZRCluster, AZRConfig

def test_8_sequences_optimization():
    """Test de l'optimisation 8 séquences du Rollout 2"""
    
    print("🎯 TEST OPTIMISATION 8 SÉQUENCES - ROLLOUT 2")
    print("=" * 50)
    
    # Initialisation
    config = AZRConfig()
    cluster = AZRCluster(cluster_id=1, config=config)
    
    print("✅ Configuration et cluster initialisés")
    print(f"   • Longueur fixe : {config.rollout2_fixed_length} P/B")
    print(f"   • Nombre de séquences : {config.rollout2_sequences_count}")
    print(f"   • Total possibilités : {config.rollout2_total_possibilities}")
    print(f"   • Pourcentage sélectionné : {(config.rollout2_sequences_count/config.rollout2_total_possibilities)*100:.1f}%")
    
    # ========================================================================
    # TEST 1 : GÉNÉRATION DES 16 POSSIBILITÉS THÉORIQUES
    # ========================================================================
    
    print("\n📊 TEST 1 : Toutes les possibilités théoriques")
    print("-" * 45)
    
    # Générer toutes les 16 possibilités
    all_16_possibilities = []
    for sequence_tuple in itertools.product(['P', 'B'], repeat=4):
        sequence = list(sequence_tuple)
        pattern = ''.join(sequence)
        all_16_possibilities.append({
            'sequence': sequence,
            'pattern': pattern
        })
    
    print(f"✅ Possibilités générées : {len(all_16_possibilities)}")
    print("📋 Liste complète :")
    for i, poss in enumerate(all_16_possibilities, 1):
        print(f"   {i:2d}. {poss['pattern']} → {poss['sequence']}")
    
    # ========================================================================
    # TEST 2 : GÉNÉRATION OPTIMISÉE DU ROLLOUT 2
    # ========================================================================
    
    print("\n🎲 TEST 2 : Génération optimisée Rollout 2")
    print("-" * 40)
    
    # Données simulées pour le test
    analyzer_report = {
        'signals_summary': {
            'top_signals': [
                {'signal_name': 'IMPAIR_TO_PLAYER', 'signal_type': 'pb_prediction', 'strength': 0.8, 'confidence': 0.85, 'strategy': 'player_focus'},
                {'signal_name': 'PAIR_TO_BANKER', 'signal_type': 'pb_prediction', 'strength': 0.75, 'confidence': 0.8, 'strategy': 'banker_focus'},
                {'signal_name': 'SO_SAME_PREDICTION', 'signal_type': 'so_prediction', 'strength': 0.7, 'confidence': 0.75, 'target_outcome': 'S', 'strategy': 'same_focus'},
                {'signal_name': 'PAIR_SYNC_PATTERN', 'signal_type': 'pattern', 'strength': 0.65, 'confidence': 0.7, 'strategy': 'pair_sync_exploitation'}
            ],
            'exploitation_ready': True,
            'overall_confidence': 0.8
        },
        'generation_guidance': {
            'primary_focus': 'IMPAIR_patterns',
            'secondary_focus': 'so_patterns',
            'avoid_patterns': [],
            'optimal_sequence_length': 4,
            'confidence_thresholds': {'high': 0.7, 'medium': 0.6, 'low': 0.5},
            'exploitation_strategy': 'moderate',
            'risk_level': 'low'
        },
        'quick_access': {
            'current_state': 'IMPAIR_SYNC',
            'next_prediction_pb': 'P',
            'next_prediction_so': 'S',
            'prediction_confidence': 0.85,
            'alert_level': 'HIGH',
            'exploitation_ready': True
        },
        'indices_analysis': {
            'pbt': {'pbt_sequence': ['P', 'B', 'P', 'B', 'P']},
            'impair_pair': {'position_types': ['IMPAIR', 'PAIR', 'IMPAIR', 'PAIR', 'IMPAIR']},
            'desync_sync': {'sync_sequence': ['SYNC', 'DESYNC', 'SYNC', 'DESYNC', 'SYNC']},
            'combined': {'combined_sequence': ['IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC']}
        },
        'synthesis': {'analysis_quality': 0.8},
        'sequence_metadata': {'total_hands_analyzed': 50}
    }
    
    # Génération des séquences optimisées
    sequences = cluster._rollout_generator(analyzer_report)
    
    # Extraire les vraies séquences
    actual_sequences = sequences.get('sequences', []) if isinstance(sequences, dict) else sequences
    
    print(f"✅ Séquences générées : {len(actual_sequences)}")
    
    # Validation du nombre de séquences
    if len(actual_sequences) == config.rollout2_sequences_count:
        print(f"✅ Nombre correct : {len(actual_sequences)} séquences")
    else:
        print(f"❌ Nombre incorrect : {len(actual_sequences)} au lieu de {config.rollout2_sequences_count}")
    
    # ========================================================================
    # TEST 3 : ANALYSE DES SÉQUENCES GÉNÉRÉES
    # ========================================================================
    
    print("\n📊 TEST 3 : Analyse des séquences générées")
    print("-" * 40)
    
    sequence_patterns = []
    sequence_probabilities = []
    
    for i, sequence in enumerate(actual_sequences):
        # Extraire les valeurs P/B du format enrichi
        if isinstance(sequence, list):
            # Format enrichi : extraire predicted_pbt
            pb_values = []
            for item in sequence:
                if isinstance(item, dict) and 'predicted_pbt' in item:
                    pb_values.append(item['predicted_pbt'])
            # Filtrer le summary (dernier élément)
            pb_values = [v for v in pb_values if v in ['P', 'B']]
            sequence_data = pb_values
        elif isinstance(sequence, dict):
            sequence_data = sequence.get('sequence_data', [])
            probability = sequence.get('estimated_probability', 0.0)
            sequence_probabilities.append(probability)
        else:
            sequence_data = sequence
        
        if len(sequence_data) == 4:
            pattern = ''.join(sequence_data)
            sequence_patterns.append(pattern)
            print(f"   Séquence {i+1} : {sequence_data} → {pattern}")
            if len(sequence_probabilities) > i:
                print(f"              Probabilité : {sequence_probabilities[i]:.3f}")
        else:
            print(f"   Séquence {i+1} : {sequence_data} ❌ (longueur incorrecte)")
    
    # ========================================================================
    # TEST 4 : VÉRIFICATION DE LA DIVERSITÉ
    # ========================================================================
    
    print("\n🔍 TEST 4 : Vérification de la diversité")
    print("-" * 35)
    
    unique_patterns = set(sequence_patterns)
    diversity_score = len(unique_patterns) / len(sequence_patterns) if sequence_patterns else 0
    
    print(f"📊 Patterns uniques : {len(unique_patterns)}/{len(sequence_patterns)}")
    print(f"📊 Score de diversité : {diversity_score:.1%}")
    print(f"📊 Patterns générés :")
    for pattern in sorted(unique_patterns):
        count = sequence_patterns.count(pattern)
        print(f"   • {pattern} : {count} fois")
    
    # ========================================================================
    # TEST 5 : VÉRIFICATION DU TRI PAR PROBABILITÉ
    # ========================================================================
    
    print("\n📈 TEST 5 : Vérification du tri par probabilité")
    print("-" * 45)
    
    if sequence_probabilities:
        is_sorted_desc = all(sequence_probabilities[i] >= sequence_probabilities[i+1] 
                           for i in range(len(sequence_probabilities)-1))
        
        print(f"📊 Probabilités triées par ordre décroissant : {'✅' if is_sorted_desc else '❌'}")
        print(f"📊 Probabilités :")
        for i, prob in enumerate(sequence_probabilities):
            print(f"   Rang {i+1} : {prob:.3f}")
        
        if sequence_probabilities:
            print(f"📊 Probabilité max : {max(sequence_probabilities):.3f}")
            print(f"📊 Probabilité min : {min(sequence_probabilities):.3f}")
            print(f"📊 Écart : {max(sequence_probabilities) - min(sequence_probabilities):.3f}")
    else:
        print("⚠️ Pas de données de probabilité disponibles")
    
    # ========================================================================
    # TEST 6 : COUVERTURE DES POSSIBILITÉS
    # ========================================================================
    
    print("\n🎯 TEST 6 : Couverture des possibilités")
    print("-" * 35)
    
    all_patterns = set(poss['pattern'] for poss in all_16_possibilities)
    generated_patterns = set(sequence_patterns)
    
    coverage = len(generated_patterns) / len(all_patterns) * 100
    missing_patterns = all_patterns - generated_patterns
    
    print(f"📊 Couverture : {len(generated_patterns)}/{len(all_patterns)} = {coverage:.1f}%")
    print(f"📊 Patterns manqués : {len(missing_patterns)}")
    if missing_patterns:
        print(f"📋 Patterns non générés : {sorted(missing_patterns)}")
    
    # ========================================================================
    # VALIDATION FINALE
    # ========================================================================
    
    print("\n✅ VALIDATION FINALE")
    print("-" * 20)
    
    # Critères de validation
    correct_count = len(actual_sequences) == config.rollout2_sequences_count
    good_diversity = diversity_score >= 0.5  # Au moins 50% de diversité
    good_coverage = coverage >= 40  # Au moins 40% des possibilités couvertes
    
    print(f"📊 Nombre correct de séquences : {'✅' if correct_count else '❌'}")
    print(f"📊 Diversité suffisante (≥50%) : {'✅' if good_diversity else '❌'}")
    print(f"📊 Couverture suffisante (≥40%) : {'✅' if good_coverage else '❌'}")
    
    if sequence_probabilities:
        sorted_probs = all(sequence_probabilities[i] >= sequence_probabilities[i+1] 
                          for i in range(len(sequence_probabilities)-1))
        print(f"📊 Tri par probabilité : {'✅' if sorted_probs else '❌'}")
    
    # ========================================================================
    # CONCLUSION
    # ========================================================================
    
    print("\n🎉 CONCLUSION")
    print("-" * 15)
    
    if correct_count and good_diversity and good_coverage:
        print("✅ OPTIMISATION 8 SÉQUENCES RÉUSSIE !")
        print("✅ Le Rollout 2 génère les 8 meilleures séquences")
        print("✅ Couverture optimale des 16 possibilités")
        print("✅ Diversité et qualité préservées")
        print("✅ Tri par probabilité décroissante")
    else:
        print("⚠️ Optimisation partiellement réussie")
        print("🔧 Ajustements nécessaires")
    
    print(f"\n🎯 AVANTAGES DE L'APPROCHE 8 SÉQUENCES :")
    print(f"• Couverture de 50% des possibilités (vs 25% avec 4)")
    print(f"• Sélection intelligente basée sur la probabilité")
    print(f"• Équilibre optimal entre diversité et qualité")
    print(f"• Tri garantissant les meilleures options en premier")

if __name__ == "__main__":
    test_8_sequences_optimization()
