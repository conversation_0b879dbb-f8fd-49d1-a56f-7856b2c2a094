#!/usr/bin/env python3
"""
Analyse spécifique des méthodes potentiellement inutilisées

Vérifie manuellement certaines méthodes suspectes
"""

import re
from typing import List, Dict

def check_method_usage(file_content: str, method_name: str) -> Dict:
    """Vérifie l'utilisation d'une méthode spécifique"""
    
    # Trouver la définition
    def_pattern = rf'def\s+{re.escape(method_name)}\s*\('
    def_matches = list(re.finditer(def_pattern, file_content))
    
    # Trouver les appels
    call_patterns = [
        rf'self\.{re.escape(method_name)}\s*\(',  # self.method()
        rf'[a-zA-Z_][a-zA-Z0-9_]*\.{re.escape(method_name)}\s*\(',  # obj.method()
        rf'(?<![a-zA-Z0-9_\.]){re.escape(method_name)}\s*\(',  # method() direct
    ]
    
    all_calls = []
    for pattern in call_patterns:
        calls = list(re.finditer(pattern, file_content))
        all_calls.extend(calls)
    
    # Calculer les numéros de ligne
    lines = file_content.split('\n')
    
    def get_line_number(match):
        return file_content[:match.start()].count('\n') + 1
    
    def_lines = [get_line_number(match) for match in def_matches]
    call_lines = [get_line_number(match) for match in all_calls]
    
    # Filtrer les appels qui ne sont pas dans la définition elle-même
    filtered_calls = []
    for call_line in call_lines:
        is_in_definition = False
        for def_line in def_lines:
            # Vérifier si l'appel est dans la même méthode (approximation)
            if abs(call_line - def_line) < 50:  # Dans les 50 lignes de la définition
                # Vérifier le contexte plus précisément
                context_start = max(0, def_line - 1)
                context_end = min(len(lines), def_line + 49)
                context = '\n'.join(lines[context_start:context_end])
                
                # Si l'appel est dans le même contexte de méthode, l'ignorer
                if f'def {method_name}(' in context:
                    is_in_definition = True
                    break
        
        if not is_in_definition:
            filtered_calls.append(call_line)
    
    return {
        'method_name': method_name,
        'definitions': def_lines,
        'all_calls': call_lines,
        'external_calls': filtered_calls,
        'is_used': len(filtered_calls) > 0
    }

def analyze_specific_methods():
    """Analyse des méthodes spécifiques potentiellement inutilisées"""
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return
    
    print("🔍 ANALYSE SPÉCIFIQUE DES MÉTHODES SUSPECTES")
    print("=" * 55)
    
    # Méthodes suspectes à vérifier manuellement
    suspect_methods = [
        # Méthodes d'analyse avancées
        '_analyze_variations_impact_on_outcomes',
        '_analyze_consecutive_length_impact',
        '_analyze_transition_moments_impact',
        '_analyze_desync_periods_impact',
        '_analyze_combined_state_changes_impact',
        '_analyze_temporal_correlation_evolution',
        '_calculate_variation_strength_analysis',
        
        # Méthodes de calcul spécialisées
        '_calculate_asymmetric_impair_alert_level',
        '_calculate_asymmetric_pair_alert_level',
        '_calculate_impair_rarity_score',
        '_calculate_pair_commonality_score',
        '_calculate_asymmetric_significance',
        
        # Méthodes d'extraction
        '_extract_consecutive_length_strength',
        '_extract_transition_moments_strength',
        '_extract_desync_periods_strength',
        '_extract_combined_state_changes_strength',
        '_extract_temporal_evolution_strength',
        
        # Méthodes utilitaires
        '_calculate_variance',
        '_calculate_distribution',
        '_classify_combined_transition_type',
        
        # Méthodes d'interface
        '_create_interface',
        '_create_burn_section',
        '_create_counter_section',
        '_create_columns_section',
        '_create_reset_section',
        
        # Méthodes de génération de données
        'generate_training_data',
        'generate_massive_data_optimized',
        'generate_and_save_formatted_games',
        
        # Méthodes de test et benchmark
        'test_on_generated_data',
        'benchmark_cpu_performance',
        'train_on_generated_data',
        'train_on_file',
        
        # Méthodes d'analyse statistique
        'analyze_maximum_sequences',
        'analyze_desync_impact',
        'analyze_sequence_patterns',
        'analyze_exploitable_sequence_patterns',
        
        # Méthodes de sauvegarde/chargement
        'save_model_state',
        'load_model_state',
        '_save_azr_intelligence',
        '_load_azr_intelligence',
        
        # Fonctions globales
        'demo_training_and_testing',
        'demo_training_and_testing_with_analysis',
        'create_azr_interface',
        'generate_analysis_report',
        'display_key_statistics'
    ]
    
    unused_methods = []
    used_methods = []
    
    for method in suspect_methods:
        result = check_method_usage(file_content, method)
        
        if result['is_used']:
            used_methods.append(result)
        else:
            unused_methods.append(result)
    
    print(f"\n⚠️  MÉTHODES INUTILISÉES CONFIRMÉES: {len(unused_methods)}")
    print("-" * 45)
    
    for i, method_info in enumerate(unused_methods, 1):
        method_name = method_info['method_name']
        def_lines = method_info['definitions']
        line_info = f"ligne {def_lines[0]}" if def_lines else "ligne inconnue"
        print(f"{i:2d}. {method_name:<45} ({line_info})")
    
    print(f"\n✅ MÉTHODES UTILISÉES CONFIRMÉES: {len(used_methods)}")
    print("-" * 40)
    
    for method_info in used_methods[:10]:  # Afficher les 10 premières
        method_name = method_info['method_name']
        external_calls = len(method_info['external_calls'])
        print(f"   {method_name:<40} ({external_calls} appels)")
    
    if len(used_methods) > 10:
        print(f"   ... et {len(used_methods) - 10} autres méthodes utilisées")
    
    # Analyse détaillée des méthodes inutilisées
    if unused_methods:
        print(f"\n📊 ANALYSE DÉTAILLÉE DES MÉTHODES INUTILISÉES")
        print("-" * 50)
        
        categories = {
            'Analyse avancée': [],
            'Calculs spécialisés': [],
            'Interface graphique': [],
            'Génération de données': [],
            'Tests et benchmarks': [],
            'Sauvegarde/Chargement': [],
            'Fonctions globales': [],
            'Autres': []
        }
        
        for method_info in unused_methods:
            method = method_info['method_name']
            
            if any(x in method for x in ['analyze_variations', 'analyze_consecutive', 'analyze_transition', 'analyze_desync_periods', 'analyze_combined_state', 'analyze_temporal']):
                categories['Analyse avancée'].append(method)
            elif any(x in method for x in ['calculate_asymmetric', 'calculate_impair', 'calculate_pair', 'extract_']):
                categories['Calculs spécialisés'].append(method)
            elif any(x in method for x in ['_create_', 'interface']):
                categories['Interface graphique'].append(method)
            elif any(x in method for x in ['generate_training', 'generate_massive', 'generate_and_save']):
                categories['Génération de données'].append(method)
            elif any(x in method for x in ['test_on_', 'benchmark_', 'train_on_']):
                categories['Tests et benchmarks'].append(method)
            elif any(x in method for x in ['save_model', 'load_model', '_save_azr', '_load_azr']):
                categories['Sauvegarde/Chargement'].append(method)
            elif any(x in method for x in ['demo_', 'create_azr', 'generate_analysis', 'display_']):
                categories['Fonctions globales'].append(method)
            else:
                categories['Autres'].append(method)
        
        for category, methods in categories.items():
            if methods:
                print(f"\n{category}: {len(methods)} méthodes")
                for method in methods:
                    print(f"  - {method}")
    
    print(f"\n📈 RÉSUMÉ FINAL")
    print("-" * 20)
    print(f"Méthodes analysées: {len(suspect_methods)}")
    print(f"Méthodes inutilisées: {len(unused_methods)} ({len(unused_methods)/len(suspect_methods)*100:.1f}%)")
    print(f"Méthodes utilisées: {len(used_methods)} ({len(used_methods)/len(suspect_methods)*100:.1f}%)")
    
    return unused_methods, used_methods

if __name__ == "__main__":
    analyze_specific_methods()
