#!/usr/bin/env python3
"""
Test end-to-end complet des Rollouts 1, 2 et 3

Ce script teste le fonctionnement complet et intégré des 3 rollouts
pour valider qu'ils fonctionnent parfaitement ensemble.
"""

import sys
import traceback
from typing import Dict, List

class MockHand:
    """Classe mock pour simuler un objet Hand compatible avec les rollouts"""
    
    def __init__(self, hand_number: int, result: str):
        self.hand_number = hand_number
        self.result = result
    
    @property
    def pbt_result(self) -> str:
        """Alias pour result - compatibilité avec les rollouts"""
        return self.result

def create_realistic_sequence_data() -> Dict:
    """Crée des données de séquence réalistes pour tester les rollouts"""
    
    # Séquence réaliste de résultats avec patterns intéressants
    results = ['P', 'B', 'P', 'B', 'P', 'T', 'B', 'P', 'P', 'B', 
               'P', 'B', 'P', 'B', 'P', 'B', 'P', 'B', 'P', 'B',
               'P', 'B', 'P', 'B', 'P']
    
    # C<PERSON>er les objets Hand mock
    hands_history = []
    for i, result in enumerate(results, 1):
        hand = MockHand(hand_number=i, result=result)
        hands_history.append(hand)
    
    return {
        'hands_history': hands_history,
        'metadata': {
            'total_hands': len(hands_history),
            'player_wins': results.count('P'),
            'banker_wins': results.count('B'),
            'ties': results.count('T'),
            'session_id': 'test_session_complete'
        }
    }

def test_complete_rollouts_pipeline():
    """Test principal du pipeline complet des 3 rollouts"""
    
    print("🔄 TEST PIPELINE COMPLET ROLLOUTS 1-2-3")
    print("=" * 45)
    
    try:
        # Importer les classes
        from azr_baccarat_predictor import AZRCluster, AZRConfig
        
        print("✅ Import réussi")
        
        # Créer une instance de cluster avec configuration
        config = AZRConfig()
        cluster = AZRCluster(cluster_id=1, config=config)
        print("✅ Instance cluster créée")
        
        # Créer des données de séquence réalistes
        sequence_data = create_realistic_sequence_data()
        print("✅ Données de séquence créées")
        
        # ÉTAPE 1 : ROLLOUT 1 - ANALYSE COMPLÈTE
        print("\n📊 ÉTAPE 1 : ROLLOUT 1 - ANALYSE COMPLÈTE")
        print("-" * 45)
        
        analyzer_report = cluster._rollout_analyzer(sequence_data)
        
        if 'error' in analyzer_report:
            print(f"❌ Erreur Rollout 1 : {analyzer_report['error']}")
            return False
        
        print("✅ Rollout 1 exécuté avec succès")
        
        # Vérifier les sections optimisées
        sections_optimized = ['signals_summary', 'generation_guidance', 'quick_access']
        for section in sections_optimized:
            if section in analyzer_report:
                print(f"   ✅ Section {section} générée")
            else:
                print(f"   ❌ Section {section} manquante")
                return False
        
        # Afficher les signaux détectés
        signals_summary = analyzer_report.get('signals_summary', {})
        top_signals = signals_summary.get('top_signals', [])
        print(f"   📊 Signaux détectés : {len(top_signals)}")
        
        for i, signal in enumerate(top_signals[:3]):
            print(f"      Signal {i+1}: {signal.get('signal_name', 'N/A')} (confiance: {signal.get('confidence', 0):.3f})")
        
        # ÉTAPE 2 : ROLLOUT 2 - GÉNÉRATION BASÉE SUR ROLLOUT 1
        print("\n🎲 ÉTAPE 2 : ROLLOUT 2 - GÉNÉRATION OPTIMISÉE")
        print("-" * 45)
        
        generator_result = cluster._rollout_generator(analyzer_report)

        if 'error' in generator_result:
            print(f"❌ Erreur Rollout 2 : {generator_result['error']}")
            return False

        generated_sequences = generator_result.get('sequences', [])
        generation_metadata = generator_result.get('generation_metadata', {})

        if not generated_sequences:
            print("❌ Aucune séquence générée par le Rollout 2")
            return False

        print(f"✅ Rollout 2 exécuté avec succès : {len(generated_sequences)} séquences")
        print(f"   📊 Stratégie: {generation_metadata.get('generation_strategy', 'N/A')}")

        # Analyser les séquences générées
        for i, seq in enumerate(generated_sequences[:2]):  # Afficher les 2 premières
            print(f"   Séquence {i+1}:")
            if isinstance(seq, list):
                # Séquence enrichie
                print(f"     • Type: Séquence enrichie")
                print(f"     • Longueur: {len(seq)}")
                if seq and isinstance(seq[-1], dict) and seq[-1].get('type') == 'sequence_enrichment_summary':
                    summary = seq[-1]
                    print(f"     • Stratégie: {summary.get('strategy_source', 'N/A')}")
                    print(f"     • Confiance: {summary.get('avg_global_confidence', 0):.3f}")
            else:
                # Séquence standard (dictionnaire)
                if isinstance(seq, dict):
                    print(f"     • Stratégie: {seq.get('strategy', 'N/A')}")
                    print(f"     • Probabilité: {seq.get('estimated_probability', 0):.3f}")
                else:
                    print(f"     • Type inattendu: {type(seq)}")
        
        # ÉTAPE 3 : ROLLOUT 3 - SÉLECTION INTELLIGENTE
        print("\n🎯 ÉTAPE 3 : ROLLOUT 3 - SÉLECTION INTELLIGENTE")
        print("-" * 50)
        
        final_prediction = cluster._rollout_predictor(generator_result, analyzer_report)
        
        if 'error' in final_prediction:
            print(f"❌ Erreur Rollout 3 : {final_prediction['error']}")
            return False
        
        print("✅ Rollout 3 exécuté avec succès")
        
        # Analyser la prédiction finale
        print(f"   🎯 Prédiction finale :")
        print(f"      • Séquence: {final_prediction.get('sequence', [])}")
        print(f"      • Stratégie: {final_prediction.get('strategy', 'N/A')}")
        print(f"      • Probabilité: {final_prediction.get('estimated_probability', 0):.3f}")
        print(f"      • Score évaluation: {final_prediction.get('evaluation_score', 0):.3f}")
        print(f"      • Confiance cluster: {final_prediction.get('cluster_confidence', 0):.3f}")
        print(f"      • Prochaine main: {final_prediction.get('next_hand_prediction', 'N/A')}")
        
        # ÉTAPE 4 : VALIDATION DE LA COMMUNICATION INTER-ROLLOUTS
        print("\n🔗 ÉTAPE 4 : VALIDATION COMMUNICATION")
        print("-" * 35)
        
        # Vérifier que le Rollout 3 a bien utilisé le rapport du Rollout 1
        if 'justification' in final_prediction:
            justification = final_prediction['justification']
            print(f"✅ Justification générée : {justification[:50]}...")
        
        # Vérifier que la sélection est cohérente avec les signaux
        selected_strategy = final_prediction.get('strategy', '')
        signal_strategies = [s.get('strategy', '') for s in top_signals]
        
        alignment_found = False
        for signal_strategy in signal_strategies:
            if signal_strategy and signal_strategy in selected_strategy:
                alignment_found = True
                break
        
        if alignment_found:
            print("✅ Alignement détecté entre sélection et signaux")
        else:
            print("⚠️  Pas d'alignement direct détecté (peut être normal)")
        
        # ÉTAPE 5 : MÉTRIQUES DE PERFORMANCE
        print("\n📊 ÉTAPE 5 : MÉTRIQUES DE PERFORMANCE")
        print("-" * 40)
        
        # Qualité d'analyse
        synthesis = analyzer_report.get('synthesis', {})
        analysis_quality = synthesis.get('analysis_quality', 0)
        print(f"✅ Qualité d'analyse Rollout 1 : {analysis_quality:.3f}")
        
        # Nombre de signaux exploitables
        exploitation_ready = signals_summary.get('exploitation_ready', False)
        print(f"✅ Exploitation prête : {exploitation_ready}")
        
        # Confiance finale
        cluster_confidence = final_prediction.get('cluster_confidence', 0)
        print(f"✅ Confiance finale cluster : {cluster_confidence:.3f}")
        
        # Score d'évaluation
        evaluation_score = final_prediction.get('evaluation_score', 0)
        print(f"✅ Score d'évaluation : {evaluation_score:.3f}")
        
        # ÉTAPE 6 : VALIDATION DE COHÉRENCE
        print("\n🔍 ÉTAPE 6 : VALIDATION DE COHÉRENCE")
        print("-" * 40)
        
        # Vérifier que la prédiction est cohérente
        next_hand = final_prediction.get('next_hand_prediction', '')
        sequence = final_prediction.get('sequence', [])
        
        if sequence and next_hand:
            if next_hand == sequence[0]:
                print("✅ Cohérence prédiction immédiate")
            else:
                print("⚠️  Incohérence prédiction immédiate")
        
        # Vérifier que les probabilités sont dans les bonnes plages
        probability = final_prediction.get('estimated_probability', 0)
        if 0.0 <= probability <= 1.0:
            print("✅ Probabilité dans plage valide")
        else:
            print("❌ Probabilité hors plage")
            return False
        
        # RÉSULTAT FINAL
        print("\n🏆 RÉSULTAT FINAL")
        print("-" * 20)
        
        # Score global de performance
        global_score = (
            analysis_quality * 0.3 +
            cluster_confidence * 0.4 +
            evaluation_score * 0.3
        )
        
        print(f"📊 Score global pipeline : {global_score:.3f}")
        
        if global_score > 0.7:
            print("🏆 PIPELINE EXCELLENT !")
        elif global_score > 0.5:
            print("👍 PIPELINE TRÈS BON !")
        elif global_score > 0.3:
            print("✅ PIPELINE ACCEPTABLE")
        else:
            print("⚠️  PIPELINE À AMÉLIORER")
        
        print("✅ TOUS LES ROLLOUTS FONCTIONNENT PARFAITEMENT !")
        print("✅ Communication inter-rollouts optimale")
        print("✅ Sélection intelligente validée")
        print("✅ Pipeline complet opérationnel")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import : {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur lors du test : {type(e).__name__}: {e}")
        print(f"Détails : {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 LANCEMENT TEST PIPELINE COMPLET ROLLOUTS 1-2-3")
    print("=" * 55)
    
    success = test_complete_rollouts_pipeline()
    
    if success:
        print("\n🎉 VALIDATION PIPELINE COMPLÈTE RÉUSSIE !")
        print("🏆 Les Rollouts 1, 2 et 3 fonctionnent parfaitement ensemble")
        print("🎯 Logique complète validée : Analyse → Génération → Sélection")
        sys.exit(0)
    else:
        print("\n❌ ÉCHEC DU TEST PIPELINE")
        sys.exit(1)
