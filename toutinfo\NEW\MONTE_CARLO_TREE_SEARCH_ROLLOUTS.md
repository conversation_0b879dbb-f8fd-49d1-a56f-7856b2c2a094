# 🎯 Monte Carlo Tree Search (MCTS) et Rollouts - Analyse Technique

## 📋 **Informations de Base**

**Source :** Wikipedia - Monte Carlo Tree Search  
**URL :** https://en.wikipedia.org/wiki/<PERSON>_<PERSON>_tree_search  
**Domaine :** Algorithmes de recherche heuristique  
**Applications :** Jeux de plateau, IA, planification, optimisation  

## 🧠 **Principe Fondamental de MCTS**

### **Définition**
Monte Carlo Tree Search (MCTS) est un algorithme de recherche heuristique pour certains types de processus de décision, notamment ceux employés dans les logiciels qui jouent aux jeux de plateau.

### **Concept Central : Les Rollouts**
Les **rollouts** (aussi appelés **playouts**) sont au cœur de MCTS :
- **Simulation complète :** Chaque rollout simule une partie complète jusqu'à la fin
- **Sélection aléatoire :** Les coups sont choisis aléatoirement pendant la simulation
- **Évaluation finale :** Le résultat final de chaque rollout sert à pondérer les nœuds de l'arbre

## 🔄 **Les 4 Étapes de MCTS**

### **1. Sélection (Selection)**
- **Point de départ :** Racine R (état actuel du jeu)
- **Navigation :** Sélection successive de nœuds enfants jusqu'à atteindre une feuille L
- **Critère :** Utilisation de formules de balance exploration/exploitation (UCT)

### **2. Expansion**
- **Condition :** Si L ne termine pas le jeu de manière décisive
- **Action :** Création d'un ou plusieurs nœuds enfants
- **Sélection :** Choix d'un nœud C parmi les nouveaux enfants

### **3. Simulation (Rollout)**
- **Exécution :** Completion d'un rollout aléatoire complet depuis le nœud C
- **Méthode :** Sélection de coups uniformément aléatoires jusqu'à la fin du jeu
- **Résultat :** Obtention d'un résultat final (victoire/défaite/égalité)

### **4. Rétropropagation (Backpropagation)**
- **Mise à jour :** Propagation du résultat vers tous les nœuds ancêtres
- **Statistiques :** Mise à jour des compteurs de victoires et de simulations
- **Amélioration :** Amélioration progressive de l'évaluation des nœuds

## 📊 **Formule UCT (Upper Confidence Bound for Trees)**

### **Équation Fondamentale**
```
UCT = (wi/ni) + c * sqrt(ln(Ni)/ni)
```

**Où :**
- **wi :** Nombre de victoires pour le nœud i
- **ni :** Nombre de simulations pour le nœud i  
- **Ni :** Nombre total de simulations du nœud parent
- **c :** Paramètre d'exploration (théoriquement √2, souvent ajusté empiriquement)

### **Balance Exploration/Exploitation**
- **Premier terme (wi/ni) :** **Exploitation** - Favorise les coups avec un taux de victoire élevé
- **Second terme :** **Exploration** - Favorise les coups peu explorés

## 🎮 **Applications Historiques**

### **Évolution dans le Go**
- **1992 :** Première application par B. Brügmann dans un programme de Go
- **2006 :** Rémi Coulom développe et nomme MCTS
- **2008 :** MoGo atteint le niveau dan (maître) en Go 9×9
- **2016 :** AlphaGo bat Lee Sedol en utilisant MCTS + réseaux de neurones

### **Autres Jeux**
- **Échecs, Shogi, Dames**
- **Hex, Havannah, Amazons**
- **Jeux vidéo :** Ms. Pac-Man, Total War: Rome II
- **Jeux non-déterministes :** Poker, Magic: The Gathering

## 🔬 **Types de Rollouts**

### **Rollouts Légers (Light Playouts)**
- **Méthode :** Coups purement aléatoires
- **Avantages :** Rapides, simples à implémenter
- **Inconvénients :** Peuvent manquer de réalisme

### **Rollouts Lourds (Heavy Playouts)**
- **Méthode :** Application d'heuristiques pour influencer le choix des coups
- **Exemples :** Patterns de pierres au Go, heuristiques de domaine
- **Paradoxe :** Jouer de manière sous-optimale en simulation peut améliorer la force globale

## ⚡ **Améliorations de MCTS**

### **RAVE (Rapid Action Value Estimation)**
- **Principe :** Utilise les statistiques de tous les rollouts contenant un coup donné
- **Application :** Jeux où les permutations de coups mènent à la même position
- **Formule modifiée :** Combine statistiques directes et RAVE avec un facteur β

### **Progressive Bias**
- **Méthode :** Ajout d'un terme heuristique bi/ni à la formule UCB1
- **Objectif :** Incorporation de connaissances expertes du domaine

### **Priors Non-Zéro**
- **Technique :** Attribution de valeurs initiales aux nouveaux nœuds
- **Effet :** Influence artificielle des taux de victoire initiaux

## 🚀 **Parallélisation de MCTS**

### **Parallélisation des Feuilles**
- **Méthode :** Exécution parallèle de multiples rollouts depuis une feuille
- **Avantage :** Simple à implémenter

### **Parallélisation de la Racine**
- **Méthode :** Construction d'arbres indépendants en parallèle
- **Décision :** Basée sur les branches au niveau racine de tous les arbres

### **Parallélisation de l'Arbre**
- **Méthode :** Construction parallèle du même arbre
- **Défis :** Protection contre les écritures simultanées (mutex, synchronisation non-bloquante)

## 💪 **Avantages de MCTS**

### **Pas de Fonction d'Évaluation Explicite**
- **Simplicité :** Seule l'implémentation des mécaniques du jeu est nécessaire
- **Universalité :** Applicable aux jeux sans théorie développée

### **Croissance Asymétrique de l'Arbre**
- **Efficacité :** Concentration sur les sous-arbres les plus prometteurs
- **Performance :** Meilleurs résultats sur les jeux à fort facteur de branchement

### **Convergence Théorique**
- **Garantie :** Convergence vers minimax avec UCT (sous certaines conditions)

## ⚠️ **Limitations de MCTS**

### **États Pièges**
- **Problème :** Coups superficiellement forts mais menant à une défaite
- **Cause :** Politique d'expansion sélective des nœuds
- **Exemple :** Défaite d'AlphaGo dans la 4ème partie contre Lee Sedol

### **Phase Exploratoire**
- **Début aléatoire :** Les premiers coups sont essentiellement aléatoires
- **Solution :** Techniques comme RAVE pour réduire cette phase

## 🔗 **Relation avec AZR (Absolute Zero Reasoning)**

### **Points Communs**
- **Auto-amélioration :** Les deux utilisent des mécanismes d'auto-amélioration
- **Simulation :** Utilisation de simulations pour l'évaluation
- **Exploration :** Balance entre exploration et exploitation

### **Différences Clés**
- **MCTS :** Focalisé sur la recherche dans l'arbre de jeu
- **AZR :** Génération autonome de tâches d'apprentissage
- **MCTS :** Rollouts pour évaluer les positions
- **AZR :** Rollouts pour générer et résoudre des problèmes

## 📈 **Impact sur l'IA Moderne**

### **Révolution dans les Jeux**
- **Go :** Passage de programmes faibles à super-humains
- **Échecs :** Intégration dans les moteurs modernes (Leela Chess Zero)
- **Jeux Généraux :** Applicable sans connaissances spécifiques du domaine

### **Applications au-delà des Jeux**
- **Planification :** Problèmes de planification automatisée
- **Optimisation :** Problèmes d'optimisation combinatoire
- **Robotique :** Planification de trajectoires

## 🧮 **Aspects Mathématiques**

### **Théorie des Bandits Multi-Bras**
- **Base théorique :** UCB1 dérivé de la théorie des bandits
- **Convergence :** Garanties théoriques sous certaines conditions

### **Méthodes Monte Carlo**
- **Échantillonnage :** Utilisation de l'échantillonnage aléatoire
- **Convergence :** Amélioration avec le nombre de simulations

## 🎯 **Conclusion**

MCTS représente une révolution dans les algorithmes de recherche pour les jeux et la prise de décision. Les **rollouts** sont l'élément central qui permet :

1. **Évaluation sans fonction heuristique**
2. **Apprentissage par simulation**
3. **Amélioration progressive des décisions**
4. **Applicabilité universelle**

Cette approche a ouvert la voie à des systèmes comme AZR qui étendent le concept de simulation et d'auto-amélioration au-delà des jeux traditionnels vers l'apprentissage autonome et la génération de tâches.
