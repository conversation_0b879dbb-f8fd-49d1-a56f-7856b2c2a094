# EXTRACTION TEXTUELLE - AI_German_Book.pdf
# Généré automatiquement le 2025-05-28 10:23:08
# Source: AI_German_Book.pdf
# ================================================================


--- PAGE 2 ---
Künstliche Intelligenz
--- PAGE 4 ---
Künstliche Intelligenz
Ein moderner Ansatz
4., aktualisierte Aufl age
Stuart <PERSON>g
--- PAGE 5 ---
BibliografischeInformationderDeutschenNationalbibliothek
DieDeutscheNationalbibliothekverzeichnetdiesePublikationinderDeutschenNationalbibliografie;
detailliertebibliografischeDatensindimInternetüber<http://dnb.dnb.de>abrufbar.
AlleRechtevorbehalten,auchdiederfotomechanischenWiedergabeundderSpeicherungin
elektronischenMedien.DiegewerblicheNutzungderindiesemProduktgezeigtenModelleund
Arbeitenistnichtzulässig.FastalleProduktbezeichnungenundweitereStichworteundsonstige
Angaben,dieindiesemBuchverwendetwerden,sindalseingetrageneMarkengeschützt.
AuthorizedtranslationfromtheEnglishlanguageedition,entitledArtificialIntelligence:AModern
Approach4thEditionbyStuartRussell,publishedbyPearsonEducation,Inc,publishingasPearson,
Copyright©2021Pearson.
Allrightsreserved.Nopartofthisbookmaybereproducedortransmittedinanyformorbyanymeans,
electronicormechanical,includingphotocopying,recordingorbyanyinformationstorageretrieval
system,withoutpermissionfromPearsonEducation,Inc.
GermanlanguageeditionpublishedbyPearsonDeutschlandGmbH,Copyright©2023.
BildnachweiseCover:
AlanTuring–ScienceHistoryImages/AlamyStockPhoto
AdaLovelace–PictorialPressLtd/AlamyStockPhoto
AutonomesAuto–AndreySuslov/Shutterstock
RoboterAtlas–BostonDynamics,Inc.
Berkeley-CampusundGoldenGateBridge–BenChu/Shutterstock
SchachbrettmitSchachfiguren–Titania/Shutterstock
G.Kasparow–KATHYWILLENS/APImages
10 9 8 7 6 5 4 3 2 1
27 26 25 24 23
ISBN978-3-86894-430-3(Buch)
ISBN978-3-86326-326-3(E-Book)
©2023byPearsonDeutschlandGmbH
St.-Martin-Straße82,D-81541München
AlleRechtevorbehalten
www.pearson.de
ApartofPearsonplcworldwide
Programmleitung:BirgerPeil,<EMAIL>
Fachlektorat:Prof.Dr.GabrieleKern-Isberner,TUDortmund
Übersetzung:PetraAlm;KatharinaPieper
Sprachkorrektorat:KatharinaPieper,Berlin
Bildbearbeitung:ElisabethPrümm,<EMAIL>
Satz:le-texpublishingservicesGmbH,Leipzig
DruckundVerarbeitung:Neografiaa.s.,Martin-Priekopa
PrintedinSlovakia
--- PAGE 6 ---
Für Loy,Gordon, Lucy, Georgeund Isaac – S.J.R.
Für Kris,Isabellaund Juliet – P.N.
--- PAGE 8 ---
Inhaltsverzeichnis
Vorwort 15
ÜberdieAutoren 20
I Künstliche Intelligenz
Kapitel1 Einleitung 21
1.1 WasistKI?.............................................................................. 22
1.2 DieGrundlagenderKünstlichenIntelligenz ............................................ 27
1.3 DieGeschichtederKünstlichenIntelligenz ............................................. 39
1.4 StateoftheArt.......................................................................... 50
1.5 RisikenundNutzenderKI.............................................................. 54
BibliografischeundhistorischeAnmerkungen ................................................. 59
Kapitel2 Intelligente Agenten 61
2.1 AgentenundUmgebungen.............................................................. 62
2.2 GutesVerhalten:dasKonzeptderRationalität .......................................... 64
2.3 ArtenvonUmgebungen................................................................. 68
2.4 DieStrukturvonAgenten............................................................... 73
BibliografischeundhistorischeAnmerkungen ................................................. 88
II Problemlösen
Kapitel3 Problemlösen durchSuchen 91
3.1 ProblemlösendeAgenten................................................................ 92
3.2 Beispielprobleme ....................................................................... 95
3.3 Suchalgorithmen........................................................................ 100
3.4 UninformierteSuchstrategien........................................................... 105
3.5 Informierte(heuristische)Suchstrategien ............................................... 114
3.6 HeuristischeFunktionen................................................................ 128
BibliografischeundhistorischeAnmerkungen ................................................. 137
--- PAGE 9 ---
Inhaltsverzeichnis
Kapitel4 Sucheinkomplexen Umgebungen 141
4.1 LokaleSucheundOptimierungsprobleme .............................................. 142
4.2 LokaleSucheinstetigenRäumen ....................................................... 151
4.3 SuchemitnichtdeterministischenAktionen ............................................ 154
4.4 SucheinteilweisebeobachtbarenUmgebungen......................................... 158
4.5 OnlinesuchagentenundunbekannteUmgebungen...................................... 167
BibliografischeundhistorischeAnmerkungen................................................. 175
Kapitel5 Adversariale SucheundSpiele 179
5.1 Spieltheorie............................................................................. 180
5.2 OptimaleEntscheidungeninSpielen ................................................... 182
5.3 HeuristischeAlpha-Beta-Baumsuche ................................................... 189
5.4 Monte-Carlo-Baumsuche................................................................ 195
5.5 StochastischeSpiele .................................................................... 199
5.6 TeilweisebeobachtbareSpiele .......................................................... 203
5.7 EinschränkungenvonSpiel-Suchalgorithmen .......................................... 208
BibliografischeundhistorischeAnmerkungen................................................. 211
Kapitel6 Constraint-Satisfaction-Probleme 217
6.1 DefinierenvonConstraint-Satisfaction-Problemen ...................................... 218
6.2 Constraint-Propagation:InferenzinCSPs ............................................... 223
6.3 Backtracking-SuchefürCSPs ........................................................... 229
6.4 LokaleSuchefürCSPs.................................................................. 236
6.5 DieStrukturvonProblemen ............................................................ 238
BibliografischeundhistorischeAnmerkungen................................................. 243
III Wissen, Schlussfolgerungen und Planen
Kapitel7 LogischeAgenten 247
7.1 WissensbasierteAgenten................................................................ 249
7.2 DieWumpus-Welt....................................................................... 250
7.3 Logik.................................................................................... 254
7.4 Aussagenlogik:EinesehreinfacheLogik................................................ 257
7.5 TheorembeweiseinderAussagenlogik.................................................. 262
7.6 EffektivesModelCheckinginderAussagenlogik ....................................... 273
7.7 AgentenaufderBasisvonAussagenlogik............................................... 278
BibliografischeundhistorischeAnmerkungen................................................. 289
8
--- PAGE 10 ---
Inhaltsverzeichnis
Kapitel8 Prädikatenlogik 293
8.1 Repräsentation.......................................................................... 294
8.2 SyntaxundSemantikderPrädikatenlogik .............................................. 299
8.3 AnwendenderPrädikatenlogik ......................................................... 309
8.4 WissensmodellierunginderPrädikatenlogik............................................ 316
BibliografischeundhistorischeAnmerkungen ................................................. 323
Kapitel9 Inferenz inderPrädikatenlogik 325
9.1 AussagenlogischeundprädikatenlogischeInferenz..................................... 326
9.2 UnifikationundprädikatenlogischeInferenz............................................ 328
9.3 Vorwärtsverkettung ..................................................................... 332
9.4 Rückwärtsverkettung.................................................................... 339
9.5 Resolution .............................................................................. 345
BibliografischeundhistorischeAnmerkungen ................................................. 358
Kapitel10 Wissensrepräsentation 363
10.1 Ontologie-Engineering .................................................................. 364
10.2 KategorienundObjekte................................................................. 367
10.3 Ereignisse............................................................................... 373
10.4 MentaleObjekteundModallogik ....................................................... 377
10.5 SchlussfolgerungssystemefürKategorien ............................................... 380
10.6 SchlussfolgernmitDefault-Informationen .............................................. 384
BibliografischeundhistorischeAnmerkungen ................................................. 390
Kapitel11 Automatisches Planen 397
11.1 KlassischesPlanen...................................................................... 398
11.2 AlgorithmenfürklassischesPlanen..................................................... 402
11.3 HeuristikenfürPlanungsprobleme...................................................... 407
11.4 HierarchischesPlanen .................................................................. 411
11.5 PlanenundAgiereninnichtdeterministischenDomänen............................... 420
11.6 ZeitabläufeundRessourcenplanen..................................................... 430
11.7 AnalysevonPlanungsansätzen ......................................................... 434
BibliografischeundhistorischeAnmerkungen ................................................. 436
9
--- PAGE 11 ---
Inhaltsverzeichnis
IV Unsicheres Wissen und Schlussfolgern
Kapitel12 Quantifizieren vonUnsicherheit 441
12.1 HandelnunterUnsicherheit ............................................................ 442
12.2 GrundlegendeNotationderProbabilistik ............................................... 446
12.3 InferenzmithilfevollständigergemeinsamerVerteilungen.............................. 453
12.4 Unabhängigkeit ......................................................................... 455
12.5 DieBayes’scheRegelundihreAnwendung............................................. 457
12.6 NaiveBayes-Modelle.................................................................... 461
12.7 EineerneuteBetrachtungderWumpus-Welt............................................ 462
BibliografischeundhistorischeAnmerkungen................................................. 468
Kapitel13 Probabilistisches Schlussfolgern 471
13.1 WissensrepräsentationineinerunsicherenDomäne .................................... 472
13.2 DieSemantikBayes’scherNetze ........................................................ 474
13.3 ExakteInferenzinBayes’schenNetzen.................................................. 488
13.4 ApproximativeInferenzfürBayes’scheNetze........................................... 496
13.5 KausaleNetze........................................................................... 511
BibliografischeundhistorischeAnmerkungen................................................. 516
Kapitel14 Probabilistisches SchlussfolgernüberdieZeit 523
14.1 ZeitundUnsicherheit................................................................... 524
14.2 InferenzinZeitmodellen................................................................ 528
14.3 Hidden-Markov-Modelle................................................................ 536
14.4 Kalman-Filter........................................................................... 542
14.5 DynamischeBayes’scheNetze .......................................................... 549
BibliografischeundhistorischeAnmerkungen................................................. 562
Kapitel15 Probabilistische Programmierung 565
15.1 RelationaleWahrscheinlichkeitsmodelle................................................ 567
15.2 WahrscheinlichkeitsmodellemitoffenemUniversum................................... 573
15.3 EinekomplexeWeltverfolgen .......................................................... 581
15.4 ProgrammealsWahrscheinlichkeitsmodelle ............................................ 585
BibliografischeundhistorischeAnmerkungen................................................. 590
10
--- PAGE 12 ---
Inhaltsverzeichnis
Kapitel16 Einfache Entscheidungen 595
16.1 ÜberzeugungenundWünscheunterUnsicherheit ...................................... 596
16.2 GrundlagederNutzentheorie ........................................................... 597
16.3 Nutzenfunktionen....................................................................... 601
16.4 NutzenfunktionenmitMehrfachattributen.............................................. 609
16.5 Entscheidungsnetze..................................................................... 614
16.6 DerWertvonInformationen ............................................................ 616
16.7 UnbekanntePräferenzen ................................................................ 623
BibliografischeundhistorischeAnmerkungen ................................................. 628
Kapitel17 KomplexeEntscheidungen 633
17.1 SequenzielleEntscheidungsprobleme................................................... 634
17.2 AlgorithmenfürMDPs.................................................................. 645
17.3 Bandit-Probleme........................................................................ 653
17.4 TeilweisebeobachtbareMDPs........................................................... 661
17.5 AlgorithmenzumLösenvonPOMDPs.................................................. 663
BibliografischeundhistorischeAnmerkungen ................................................. 669
Kapitel18 Entscheidungen inMultiagentenumgebungen 673
18.1 EigenschaftenvonMultiagentenumgebungen........................................... 674
18.2 NichtkooperativeSpieltheorie.......................................................... 680
18.3 DiekooperativeSpieltheorie............................................................ 702
18.4 Kollektiventscheidungentreffen ........................................................ 709
BibliografischeundhistorischeAnmerkungen ................................................. 724
V Maschinelles Lernen
Kapitel19 LernenausBeispielen 729
19.1 Lernformen ............................................................................. 730
19.2 ÜberwachtesLernen .................................................................... 732
19.3 LernenvonEntscheidungsbäumen...................................................... 736
19.4 ModellauswahlundOptimierung....................................................... 745
19.5 DieTheoriedesLernens ................................................................ 752
19.6 LineareRegressionundKlassifikation .................................................. 756
19.7 NichtparametrischeModelle............................................................ 767
19.8 Ensemble-Lernen ....................................................................... 777
19.9 EntwicklungvonSystemenfürmaschinellesLernen.................................... 785
BibliografischeundhistorischeAnmerkungen ................................................. 797
11
--- PAGE 13 ---
Inhaltsverzeichnis
Kapitel20 Lernenprobabilistischer Modelle 803
20.1 StatistischesLernen..................................................................... 804
20.2 LernenmitvollständigenDaten......................................................... 807
20.3 LernenmitverborgenenVariablen:derEM-Algorithmus................................ 821
BibliografischeundhistorischeAnmerkungen................................................. 830
Kapitel21 DeepLearning 833
21.1 EinfacheFeedforward-Netze ............................................................ 835
21.2 BerechnungsgraphenfürDeepLearning ................................................ 840
21.3 ConvolutionalNeuralNetworks......................................................... 844
21.4 Algorithmenlernen ..................................................................... 849
21.5 Generalisierung ......................................................................... 853
21.6 RekurrenteneuronaleNetze............................................................. 857
21.7 UnüberwachtesLernenundTransferLearning.......................................... 860
21.8 Anwendungen .......................................................................... 867
BibliografischeundhistorischeAnmerkungen................................................. 870
Kapitel22 Reinforcement Learning 875
22.1 AusBelohnungenlernen................................................................ 876
22.2 PassivesReinforcementLearning ....................................................... 878
22.3 AktivesReinforcementLearning ........................................................ 884
22.4 GeneralisierungbeimReinforcementLearning.......................................... 890
22.5 Strategiesuche .......................................................................... 897
22.6 ApprenticeshipLearningundInverseReinforcementLearning ......................... 900
22.7 AnwendungenvonReinforcementLearning ............................................ 903
BibliografischeundhistorischeAnmerkungen................................................. 907
VI Kommunikation, Wahrnehmung und Aktion
Kapitel23 Natürliche Sprachverarbeitung 911
23.1 Sprachmodelle.......................................................................... 912
23.2 Grammatik.............................................................................. 923
23.3 Parsen................................................................................... 925
23.4 ErweiterteGrammatiken ................................................................ 931
23.5 KomplikationenderrealennatürlichenSprache ........................................ 936
23.6 AufgabeninnatürlicherSprache........................................................ 939
BibliografischeundhistorischeAnmerkungen................................................. 942
12
--- PAGE 14 ---
Inhaltsverzeichnis
Kapitel24 Deep LearningfürdieVerarbeitung natürlicher Sprache 947
24.1 Worteinbettungen....................................................................... 948
24.2 RekurrenteNeuronaleNetzefürNLP ................................................... 952
24.3 Sequenz-zu-Sequenz-Modelle........................................................... 956
24.4 DieTransformerarchitektur ............................................................. 961
24.5 VortrainingundTransferLearning ...................................................... 963
24.6 StandderTechnik....................................................................... 968
BibliografischeundhistorischeAnmerkungen ................................................. 971
Kapitel25 ComputerVision 975
25.1 Einleitung............................................................................... 976
25.2 Bildaufbau.............................................................................. 977
25.3 EinfacheBildeigenschaften ............................................................. 983
25.4 Bilderklassifizieren..................................................................... 991
25.5 ErkennenvonObjekten ................................................................. 994
25.6 Die3-D-Welt ............................................................................ 997
25.7 AnwendungenderComputerVision .................................................... 1002
BibliografischeundhistorischeAnmerkungen ................................................. 1016
Kapitel26 Robotik 1021
26.1 Roboter ................................................................................. 1022
26.2 Roboterhardware........................................................................ 1023
26.3 WelcheArtvonProblemlöstdieRobotik? .............................................. 1027
26.4 Roboterwahrnehmung .................................................................. 1028
26.5 PlanungundKontrolle.................................................................. 1036
26.6 PlanungunsichererBewegungen........................................................ 1054
26.7 ReinforcementLearninginderRobotik ................................................. 1057
26.8 MenschenundRoboter ................................................................. 1059
26.9 AlternativeRobotik-Frameworks........................................................ 1067
26.10 Anwendungsbereiche................................................................... 1070
BibliografischeundhistorischeAnmerkungen ................................................. 1075
VII Fazit
Kapitel27 Philosophie,EthikundSicherheit derKI 1081
27.1 DieGrenzenderKI...................................................................... 1082
27.2 KönnenMaschinenwirklichdenken?................................................... 1085
27.3 EthikderKI............................................................................. 1087
BibliografischeundhistorischeAnmerkungen ................................................. 1109
13
--- PAGE 15 ---
Inhaltsverzeichnis
Kapitel28 DieZukunftderKI 1115
28.1 KI-Komponenten........................................................................ 1116
28.2 KI-Architekturen........................................................................ 1123
AnhangA Mathematischer Hintergrund 1127
A.1 KomplexitätsanalyseundO()-Notation.................................................. 1128
A.2 Vektoren,MatrizenundlineareAlgebra................................................. 1130
A.3 Wahrscheinlichkeitsverteilungen ....................................................... 1132
BibliografischeundhistorischeAnmerkungen................................................. 1134
AnhangB HinweisezuSprachen undAlgorithmen 1135
B.1 SprachenmitBackus-Naur-Form(BNF)definieren...................................... 1136
B.2 AlgorithmenmitPseudocodebeschreiben .............................................. 1137
B.3 ErgänzendesOnlinematerial ............................................................ 1138
Literaturverzeichnis 1139
Index 1209
Namensregister 1238
14
--- PAGE 16 ---
Vorwort
Künstliche Intelligenz (KI) ist ein weites Feld und dieses Buch entsprechend umfangreich.
Wir haben versucht, der ganzen Bandbreite des Themas gerecht zu werden, die von Logik,
WahrscheinlichkeitundstetigerMathematiküberWahrnehmung,Schlussfolgern,Lernenund
HandelnbishinzuFairness,Vertrauen,GemeinwohlundSicherheitreicht.Begleitetwerden
unsere Ausführungen von anschaulichen Anwendungsbeispielen, sei es zu mikroelektroni-
schenGeräten,RobotermobilenzurErkundungfernerPlanetenbishinzuOnlinedienstenmit
MilliardenvonBenutzern.
DerUntertiteldiesesBuchslautet„EinmodernerAnsatz“,wasausdrückensoll,dasswirdie
GeschichteausaktuellerPerspektiveerzählenwollen.WirfassenallebisherigenErkenntnisse
in einem Rahmenwerk zusammen, indem wir frühere Arbeiten auf der Basis der heutigen
Vorstellungen und Terminologie neu formulieren. Wir entschuldigen uns bei allen, deren
Teilgebietedadurchwenigereindeutigzuerkennensind.
Neu in dieser Auflage
DieseAuflagespiegeltdieÄnderungeninderKIseitderletztenenglischenAusgabeimJahr
2010wider:
(cid:2) AufgrundderzunehmendenVerfügbarkeitvonDaten,RechenressourcenundneuenAlgo-
rithmen konzentrieren wirunsmehr auf maschinelles Lernen als auf diehändische Wis-
sensmodellierung.
(cid:2) DeepLearning, probabilistischeProgrammierungundMultiagentensysteme werden inje-
weilseinemeigenenKapitelausführlichbehandelt.
(cid:2) DieThemen natürlicheSprachverarbeitung, RobotikundComputerVisionwurdeüberar-
beitet,umdemEinflussvonDeepLearningRechnungzutragen.
(cid:2) DasRobotik-KapitelumfasstnunauchRoboter,diemitMenscheninteragieren,sowieden
EinsatzvonReinforcementLearninginderRobotik.
(cid:2) FrüherbestanddasZielderKIdarin,Systemezuentwickeln,mitdenensichdererwartete
Nutzen maximieren lässt, wobei die spezifischen Nutzeninformationen – das Ziel – von
den menschlichen Entwicklern des Systems vorgegeben werden. Inzwischen gehen wir
nichtmehrdavonaus,dassdasZielfeststehtunddemKI-Systembekanntist,sonderndass
dasSystemdurchausunsicherhinsichtlichderwahren ZielederMenschenseinkann,in
deren Auftrag es arbeitet. Es muss lernen, was zu maximieren ist, und muss auch dann
angemessenfunktionieren,wennessichüberdasZielimUnklarenist.
(cid:2) WirgehenstärkeraufdieAuswirkungenvonKIaufdieGesellschaftein,einschließlichso
wichtigerFragenwieEthik,Fairness,VertrauenundSicherheit.
(cid:2) WirhabendieÜbungenvomEndejedesKapitels aufeineWebsiteverschoben.Aufdiese
WeisekönnenwirdieÜbungenkontinuierlichergänzen,aktualisierenundverbessern,um
denAnforderungenderDozentinnenundDozentengerechtzuwerdenunddieFortschritte
aufdemGebietundbeidenKI-bezogenenSoftware-Toolszuberücksichtigen.
(cid:2) Insgesamtsindetwa25%desMaterialsindiesemBuchkomplettneu.Dierestlichen75%
wurdenweitgehendneugeschrieben,umdasThemamöglichsteinheitlichzupräsentieren.
22% der Literaturangaben in dieser Ausgabe beziehen sich auf Arbeiten, die nach 2010
veröffentlichtwurden.
15
--- PAGE 17 ---
Vorwort
Überblick über das Buch
Das wichtigste verbindende Element ist das Konzept eines intelligenten Agenten. Wir defi-
nieren KI als das Studium von Agenten, die Perzepte aus der Umgebung empfangen und
Aktionen ausführen. Jeder dieser Agenten implementiert eine Funktion, die Perzeptfolgen
auf Aktionen abbildet, und wir beschreiben verschiedene Möglichkeiten, diese Funktionen
zurepräsentieren,z.B.reaktiveAgenten,Echtzeitplaner,entscheidungstheoretischeSysteme
und Deep-Learning-Systeme. Wir legen den Schwerpunkt auf das Lernen sowohl als Kon-
struktionsmethodefürkompetenteSystemealsauchalsMöglichkeit,dieReichweitedesEnt-
wicklersinunbekannteUmgebungenzuerweitern.WirbehandelnRobotikundVisionnicht
als unabhängig definierte Probleme, sondern als Probleme, die beim Erreichen von Zielen
auftreten.WirlegendasAugenmerkaufdieBedeutungderAufgabenumgebungbeiderFest-
legungdesgeeignetenAgentenentwurfs.
UnserprimäresZielistes,dieKonzepteundIdeenzuvermitteln,diesichindenletztensieb-
zigJahrenderKI-ForschungunddenletztenzweiJahrtausendenverwandterArbeitenheraus-
gebildet haben.Wir haben versucht, diese Ideen nicht zu formal zu präsentieren, ohnedass
diesjedochzuLastenderGenauigkeitgeht.WirhabenmathematischeFormelnundPseudo-
code-Algorithmenverwendet,umdiewichtigstenIdeenzukonkretisieren.EineBeschreibung
dermathematischenBegriffeundNotationfindenSieinAnhangAundunserenPseudocode
beschreibenwirinAnhangB.
DiesesBuchistinersterLiniefürdenEinsatzineinemGrundkursgedacht.DasBuchbesteht
aus28Kapiteln,vondenenjedesetwaeineWocheVorlesungszeiterfordert,sodassdasDurch-
arbeitendesgesamtenBuchssichübereinenzweisemestrigen Grundkurserstreckt.Ineinem
einsemestrigen Kurs können, orientiert an den Interessen der Dozentinnen und Dozenten
sowie der Studentinnen und Studenten, ausgewählte Kapitel durchgearbeitet werden. Das
Buch kann auch im Masterstudium eingesetzt werden (vielleicht indem einige der Primär-
quellenhinzugezogenwerden,dieindenbibliografischenAnmerkungenvorgeschlagenwur-
I
den).EbensoeignetessichzumSelbststudiumoderalsNachschlagewerk.
ImgesamtenBuchsindwichtigePunktemiteinemDreieckssymbolamRandgekennzeichnet.
Wichtige Begriffe werden fett gedruckt, einige sind zusätzlich noch farbig – hier wird der
BegriffinderRegeleingeführtbzw.definiert.DasBuchbeinhalteteinenumfassendenIndex,
einNamensregisterundeinumfangreichesLiteraturverzeichnis.
DieeinzigeVoraussetzung isteinegewisse Kenntnisdergrundlegenden KonzeptederInfor-
matik(Algorithmen,Datenstrukturen,Komplexität)aufAnfängerniveau. FüreinigederThe-
mensindgrundlegendeKenntnissederAnalysisundderlinearenAlgebravonVorteil.
Online-Materialien
Online-Materialien sindüberpearsonhighered.com/cs-resourcesoderaufderWebsiteder
AutorenzumBuch,aima.cs.berkeley.edu,verfügbar.DortfindenSie:
(cid:2) Übungen, Programmierprojekte und Forschungsprojekte: Diese befinden sich nicht mehr
am Ende eines jeden Kapitels, sondern sind nur noch online verfügbar. Im Buch selber
verweisenwiraufeineOnline-ÜbungüberihreNummerierungwie„Übung6.6“.Mitden
AnweisungenaufderWebsitekönnenSiedieÜbungennachThemaoderNummersuchen.
(cid:2) ImplementierungenderAlgorithmenimBuchsindinPython,JavaundanderenProgram-
miersprachen im Online-Code-Repository erhältlich (derzeit gehostet auf github.com/
aimacode).
16
--- PAGE 18 ---
Vorwort
(cid:2) eine Liste vonüber 1.500Lehrinstituten, die das Buch verwendet haben, viele mit Links
zuonlineverfügbarenKursmaterialienundLehrplänen;
(cid:2) ergänzendes Materialinenglisch undLinksfürStudentinnenundStudentensowieLehr-
kräfte.
Cover
Das Cover zeigt einen Teil der Endstellung aus der entscheidenden sechsten Partie des
Schachspiels von 1997, in der das Programm DeepBlue Garry Kasparow (mit Schwarz)
besiegte, wo zum ersten Mal ein Weltmeister von einem Computer in einem Schachspiel
geschlagen wurde. Kasparow ist oben abgebildet. Rechts vonihm ist eine Schlüsselstellung
aus der zweiten Partie des historischen Go-Spiels zwischen dem ehemaligen Weltmeister
Lee Sedol und dem Programm ALPHAGO von DeepMind zu sehen. Zug 37 von ALPHAGO
verstieß gegen die jahrhundertealte Go-Tradition und wurde von menschlichen Experten
sofort als peinlicher Fehler angesehen, der sich jedoch als Gewinnzug herausstellte. Oben
linksistdervonBostonDynamicsentwickeltehumanoideRoboterAtlaszusehen.Zwischen
Ada Lovelace, der ersten Computerprogrammiererin der Welt, und Alan Turing, der mit
seinengrundlegendenArbeitendiekünstlicheIntelligenzdefinierte,istdieDarstellungeines
selbstfahrenden Autos zu sehen, das seine Umgebung wahrnimmt. Hinter dem Schachbrett
befindet sich ein probabilistisches Programmiermodell, das von der UN-Organisation zum
Vertrag überdas „umfassende Verbot vonNuklearversuchen zurErkennung vonNuklearex-
plosionen aus seismischen Signalen“ verwendet wird. (In der englischen Originalausgabe
gibtesnochweitere Bilder,diewirhierleidernichtaufnehmenkonnten:AmunterenRand
des Schachbretts ist ein Roboter der Mars-Exploration-Rover-Mission abgebildet sowie eine
Statue von Aristoteles, dem Pionier der Logik; seinen Planungsalgorithmus aus De Motu
AnimaliumkannmanhinterdenNamenderAutorensehen.)
Danksagungen
Es braucht ein globales Dorf, um ein Buch zu machen. Über 600 Personen haben Teile des
Buchs gelesen und Verbesserungsvorschläge gemacht. Die vollständige Liste finden Sie auf
aima.cs.berkeley.edu/ack.html – ihnen allen gilt unser Dank. Wir haben hier nur Platz,
um einige besonders wichtige Mitwirkende zu erwähnen. Zunächst seien die beitragenden
Verfassergenannt:
(cid:2) JudeaPearl(Abschnitt13.5,KausaleNetze);
(cid:2) VikashMansinghka(Abschnitt15.4,ProgrammealsWahrscheinlichkeitsmodelle);
(cid:2) MichaelWooldridge(Kapitel18,EntscheidungeninMultiagentenumgebungen);
(cid:2) IanGoodfellow(Kapitel21,DeepLearning);
(cid:2) JacobDevlinundMei-WingChang(Kapitel24,DeepLearningfürdieVerarbeitungnatür-
licherSprache);
(cid:2) JitendraMalikundDavidForsyth(Kapitel25,ComputerVision);
(cid:2) AncaDragan(Kapitel26,Robotik).
DanneinigeSchlüsselrollen:
(cid:2) CynthiaYeungundMalikaCantor(Projektleitung);
(cid:2) JulieSussmanundTomGalloway(KorrektoratundredaktionelleBearbeitung);
17
--- PAGE 19 ---
Vorwort
(cid:2) OmariStephens(Illustrationen);
(cid:2) TracyJohnson(Lektorat);
(cid:2) ErinAultundRoseKernan(CoverundFarbumsetzung);
(cid:2) NalinChhibber,SamGoto,RaymonddeLacaze, RaviMohan,CiaranO’Reilly,AmitPatel,
DragomirRadivundSamagraSharma(EntwicklungdesOnlinecodesundMentoring);
(cid:2) StudentinnenundStudentendesGoogleSummerofCode(EntwicklungdesOnlinecodes).
StuartsDankgiltseinerFrauLoySheflottfürihreendloseGeduldundgrenzenloseWeisheit.
Erhofft, dass Gordon,Lucy, George und Isaac dieses Buch bald lesen werden, nachdem sie
ihmverziehen haben,dassersolangedaran gearbeitet hat.RUGS(Russell’sUnusualGroup
ofStudents)warenwieimmeraußergewöhnlichhilfreich.
PetersDankgiltseinenEltern(TorstenundGerda),dieihnzudiesemProjektermutigthaben,
sowieseinerFrau(Kris),seinenKindern(BellaundJuliet),seinenKollegen,seinemChefund
seinen Freunden, die ihn während der langen Stunden des Schreibens und Neuschreibens
ermutigtunderduldethaben.
Vorwort zur deutschen Auflage
DieKünstlicheIntelligenz(KI)hatseitihremGeburtsjahr1956schonvieleHöhenundTiefen
erlebt.EinerenthusiastischenBegeisterungswelle überneueTechnikenundMethodenfolgte
in der Regel eine herbe Ernüchterungsphase – zu groß waren die Erwartungen, zu tief die
EnttäuschungüberFehlschläge.AktuellbefindetsichdieKIwiederaufeinemHöhenflugund
vieles spricht dafür, dass sich dieser Trend dieses Mal zumindest auf einem hohen Plateau
stabilisiert: DieErfolgedesDeep LearningbeiderVerarbeitung vonBildernundnatürlicher
Sprache sind mehr als überzeugend, ChatGPT begeistert sogar Experten und smarte intelli-
genteGeräteprägenlängstunserenAlltagundunsergesellschaftlichesZusammenleben.
Es scheint also gar nicht mehr ohne KI zu gehen, aber was genau bedeutet das für den ein-
zelnenunddieGesellschaft?WielassensichFehlschläge, dieauchdiesesMalunweigerlich
beiderAnwendungvonneuenKI-Technikenauftreten,perspektivischeinschätzen?Wiekön-
nenwirdieVorteileund„Segnungen“derKIsoeinsetzen undnutzen,dasssiewirklichdie
Lebensqualität der Menschen verbessern? Es wird viel von „explainable AI“ (erklärbare KI)
und „human-centred AI“ (auf den Menschen zentrierte KI) gesprochen, um den Menschen
explizitinKI-Vorgängeeinzubinden.DassindganzzentraleAspekte,damitKI-Innovationen
auchalsFortschrittvonallenBeteiligtenwahrgenommenwerden.
AufderSeitedermenschlichenAkteuregiltesaberauch,einbesseresVerständnisfürgrund-
legendeKI-Technikenaufzubauen,umderenNutzenundGrenzen besserbeurteilenzukön-
nen. Eine solche Beurteilungskompetenz ist unverzichtbar, wenn KI tatsächlich ein wertge-
schätzterTeilunseresberuflichenundprivatenLebenswerdensoll.FürvieleNicht-Experten
ist die KI immer noch so etwas wie eine magische Komponente, die auf wunderbare Weise
erstaunlicheDingeleistet.„DasmachtdieKI“hörtmandannoft,wobeidassowohlbewun-
dernd wie auch vorwurfsvoll klingen kann. Es soll hier gar nicht bezweifelt werden, dass
die Wissenschaften allgemein schon viele wunderbare Erkenntnisse zutage gefördert haben
und dass die Ergebnisse des Deep Learning auch eine Herausforderung an das Verständnis-
vermögen der Experten darstellen. Aber vieles lässt sich doch in seinen Grundzügen recht
anschaulich auf einem Niveau erklären, das KI auch für Nicht-Experten zugänglich macht.
Zum Beispiel ließe sich das Grundprinzip des Lernens von Entscheidungsbäumen aus Bei-
spielen(hierinKapitel19behandelt)miteinemeinfachenZählalgorithmusschonSchulkin-
dernnahebringen.
18
--- PAGE 20 ---
Vorwort
Und genau hier setzt dieses Buch an. Im Vorwort findet man den Hinweis, dass lediglich
„gewisseKenntnissedergrundlegendenKonzeptederInformatikaufAnfängerniveau“erfor-
derlich sind, um dieses Buch lesen zu können. Und das darf man getrost wörtlich nehmen.
DasBuchführtauchinschwierigeThemenüberauslesbarundanschaulichein,vertieftzen-
trale Details bis auf die mathematische Ebene in verständlicher Weise und gibt einen her-
vorragendenEinblickinundÜberblicküberdenStandderKI-Forschung.Sofindetauchdie
KI-Expertin oder der KI-Expertehier ergiebiges Material fürden Einstieg in Gebiete, dieihr
oderihmwenigervertrautsind.
EswarmireinbesonderesVergnügen,diesesBuchimRahmendesFachlektoratszulesenund
mitdemhervorragenden Übersetzerinnen-Team KatharinaPieperundPetraAlmsowiedem
SeniorPublishervonPearsonDeutschland,BirgerPeil,zusammenzuarbeiten.Nunbleibtmir
noch, auch dem Leser viel Freude und vertiefte Einsichten in die KI bei der Lektüre dieses
Bucheszuwünschen.
Dortmund GabrieleKern-Isberner
19
--- PAGE 21 ---
Über die Autoren
StuartRussellwurde1962inPortsmouth,England,geboren.ErerhieltseinenB.A.mitAus-
zeichnung in PhysikvonderUniversität Oxford imJahr1982undseinen Ph.D.in Informa-
tik von Stanford im Jahr 1986. Danach wechselte er an die Universität von Kalifornien in
Berkeley, wo er als Professor und ehemaliger Lehrstuhlinhaber für Informatik, Direktor des
CenterforHuman-CompatibleAIundInhaberdesSmith-Zadeh-LehrstuhlsfürIngenieurwis-
senschaften arbeitet. Im Jahr 1990 erhielt er den Presidential Young Investigator Award der
NationalScienceFoundationundwurde1995mitdemComputersandThoughtAwardausge-
zeichnet.EristMitgliedderAmericanAssociationforArtificialIntelligence,derAssociation
for Computing Machinery und der American Association for the Advancement of Science,
Foschungsstipendiat desWadham College, Oxford, undAndrewCarnegie Fellow. Von 2012
bis 2014war er Preisträger des Chaire Blaise Pascal in Paris. Er hat über 300 Publikationen
zu einem breiten Spektrum von Themen der künstlichen Intelligenz veröffentlicht. Zu sei-
nen Büchern gehören unter anderem The Use of Knowledge in Analogy and Induction, Do
the Right Thing: Studies in Limited Rationality (mit Eric Wefald) und Human Compatible:
ArtificialIntelligenceandtheProblemofControl.
Peter Norvig ist derzeit Forschungsleiter bei Google, Inc. und war zuvor als Leiter für die
wichtigsten Algorithmen der Websuche verantwortlich. Er war Co-Dozent eines KI-Online-
kurses, für den sich 160.000Studenten anmeldeten, und gab damit den Startschuss für die
aktuelleRundedervielzähligenoffenzugänglichenOnlinekurse.ErwarLeiterderComputa-
tionalSciencesDivisionamNASAAmesResearch Center,woerfürdieForschungundEnt-
wicklungindenBereichenkünstlicheIntelligenzundRobotikverantwortlichwar.Ererhielt
einenB.S.inangewandter MathematikvonderBrown-UniversitätundeinenPh.D.inInfor-
matik von Berkeley. Er war Professor an der Universität von Südkalifornien und Fakultäts-
mitgliedinBerkeleyundStanford.EristeinMitgliedderAmericanAssociationforArtificial
Intelligence, derAssociation forComputingMachinery, derAmerican Academy ofArtsand
SciencesundderCaliforniaAcademyofScience.SeineweiterenBücherlautenParadigmsof
AI Programming:CaseStudiesinCommonLisp,Verbmobil:A TranslationSystem forFace-
to-FaceDialogundIntelligentHelpSystemsforUNIX.
BeideAutorenteiltensichimJahr2016denerstenAAAI/EAAIOutstandingEducatorAward.
20
--- PAGE 22 ---
1
Einleitung
1.1 WasistKI?...................................................... 22
1.1.1 MenschlichesHandeln:derAnsatzmitdemTuring-Test ......... 23
1.1.2 MenschlichesDenken:derAnsatzderkognitivenModellierung.. 23
1.1.3 RationalesDenken:derAnsatzder„Denkgesetze“................ 24
1.1.4 RationalesHandeln:derAnsatzderrationalenAgenten.......... 25
1.1.5 NutzbringendeMaschinen........................................ 26
1.2 Die Grundlagen derKünstlichen Intelligenz.............. 27
1.2.1 Philosophie....................................................... 27
1.2.2 Mathematik....................................................... 29
1.2.3 Wirtschaftswissenschaft .......................................... 31
1.2.4 Neurowissenschaft ............................................... 32
1.2.5 Psychologie....................................................... 35
1.2.6 Computertechnik................................................. 36
1.2.7 KontrolltheorieundKybernetik .................................. 38
1.2.8 Linguistik ........................................................ 39
1.3 Die GeschichtederKünstlichen Intelligenz ............... 39
1.3.1 DieAnfängederKünstlichenIntelligenz(1943–1956)............ 39
1.3.2 FrüherEnthusiasmus,großeErwartungen(1952–1969)........... 41
1.3.3 EinePortionRealität(1966–1973)................................ 43
1.3.4 Expertensysteme(1969–1986).................................... 44
1.3.5 DieRückkehrderneuronalenNetze(1986–heute)................ 46
1.3.6 ProbabilistischesSchlussfolgernundmaschinellesLernen
(1987–heute) ..................................................... 47
1.3.7 BigData(2001–heute) ............................................ 48
1.3.8 DeepLearning(2011–heute)...................................... 49
1.4 StateoftheArt................................................ 50
1.5 Risikenund NutzenderKI................................... 54
--- PAGE 23 ---
1
Einleitung
In diesem Kapitel wollen wir erklären, warum wir die Künstliche Intelligenz für ein
absolut lohnenswertes Forschungsgebiet halten. Wir wollen versuchen zu definieren,
worum es sich dabei genau handelt – eine sinnvolle Maßnahme, bevor wir wirklich
anfangen.
Wirbezeichnen unsselbst alsHomosapiens–denweisen Menschen –,weil unsereIntelli-
genz so wichtig ist füruns. Seit Tausenden vonJahren versuchen wir zu verstehen, wiewir
denkenundhandeln–dasheißt,wieunserGehirn,eineHandvollMaterie, eineWeltwahr-
nehmen, verstehen, vorhersagen und manipulieren kann, die viel größer und komplizierter
ist als es selbst. Das Gebiet der Künstlichen Intelligenz beschäftigt sich nicht nur mit dem
Verstehen, sondernauch mitdemErstellenintelligenterEinheiten–Maschinen,dieberech-
nenkönnen,wiesieineinerVielzahlneuartigerSituationeneffektivundzuverlässighandeln
können.
InUmfragenwirddieKIregelmäßigalseinesderinteressantestenundamschnellstenwach-
senden Felder eingestuft, und sie generiert bereits über eine Billion US-Dollar Umsatz pro
Jahr.DerKI-ExperteKai-FuLeeprognostiziert,dassdieAuswirkungenderKI„größeralsalles
andere in der Geschichte der Menschheit“ sein werden. Außerdem sind die intellektuellen
Grenzen der KI weit offen. Während ein Student einer älteren Wissenschaft wie der Physik
dasGefühlhabenkönnte,dassdiebestenIdeenbereitsvonGalileo,Newton,Curie,Einstein
undanderen großenDenkern entdecktwurden,bietet dieKIeinemVollzeit-Superhirn noch
vieleMöglichkeiten.
Die KI umfasst momentan eine Vielzahl von Teilgebieten, die von allgemeinen Bereichen
(Lernen,Schlussfolgerungen,Wahrnehmungusw.)biszuspezifischenBereichenwieSchach
spielen,mathematischeTheoremebeweisen,Poesieverfassen,AutofahrenoderKrankheiten
diagnostizieren reichen. Die KI ist fürjede intellektuelle Aufgabe relevant – sie ist wirklich
einuniversellesGebiet.
1.1 Was ist KI?
Wirhabenbehauptet,dieKIseiinteressant,aberwirhabennichtgesagt,wasKIist.InderVer-
gangenheithabenForschermehrereverschiedeneAnsätzederKIverfolgt.EinigehabenIntel-
ligenz in Bezug auf die Wiedergabetreue menschlicher Leistung definiert, während andere
eine abstrakte, formale Definition von Intelligenz in der Form von Rationalität bevorzugen
– grob gesagt, das „Richtige“ zu tun. Auch der Gegenstand selbst variiert: Einige betrach-
tenIntelligenzalseineEigenschaftinternerDenkprozesseundSchlussfolgerungen,während
anderesichaufintelligentesVerhaltenkonzentrieren,eineexterneCharakterisierung.1
Aus diesen beiden Dimensionen – intuitiv versus rational und Denken versus Verhalten –
ergebensichviermöglicheKombinationenundfüralleviergabesAnhängerundForschungs-
programme.DieverwendetenMethodensindnotwendigerweiseunterschiedlich:DasStreben
nach menschenähnlicher Intelligenz muss teilweise eine empirische Wissenschaft sein, die
mitderPsychologieverwandtistundBeobachtungenundHypothesenüberdastatsächliche
menschlicheVerhalten unddieDenkprozesse beinhaltet;einrationalistischer Ansatzhinge-
gen beinhaltet eine KombinationausMathematik undIngenieurwesen undist mit Statistik,
KontrolltheorieundWirtschaftverbunden.DieverschiedenenGruppenhabensichgegensei-
tigsowohldiffamiertalsauchunterstützt.SchauenwirunsdievierAnsätzegenaueran.
1 In der Öffentlichkeit werden manchmal die Begriffe „künstliche Intelligenz“ und „maschinelles Lernen“
verwechselt.MaschinellesLernenisteinTeilgebietderKI,dassichmitderFähigkeitbeschäftigt,dieLeistung
aufBasisvonErfahrungenzuverbessern.EinigeKI-SystemenutzenMethodendesmaschinellenLernens,um
Kompetenzzuerlangen,mancheaberauchnicht.
22
--- PAGE 24 ---
1.1 WasistKI?
1.1.1 Menschliches Handeln: der Ansatzmit demTuring-Test
DervonAlanTuring(1950)vorgeschlageneTuring-TestwurdealsGedankenexperimentkon-
zipiert, das die philosophische Unbestimmtheit der Frage „Kann eine Maschine denken?“
umgehen sollte. Ein Computer besteht den Test, wenn ein menschlicher Fragesteller, der
einige schriftlichen Fragen stellt, nicht erkennen kann, ob die schriftlichen Antworten von
einem Menschen oder voneinem Computerstammen. In Kapitel 27werden dieDetails des
Testsbesprochen undwirgehenderFragenach,obeinComputerwirklichintelligent wäre,
wennerdenTestbestünde.FürdenMomenthaltenwirfest,dassdieProgrammierungeines
Computers,derdenTuring-TestimstrengenSinnebestehensoll,eineMengeArbeitmitsich
bringt.DerComputermüsstediefolgendenFähigkeitenbesitzen:
(cid:2) VerarbeitungnatürlicherSprache(NLP,NaturalLanguageProcessing), umerfolgreich in
einermenschlichenSprachezukommunizieren;
(cid:2) Wissensrepräsentation,umzuspeichern,waserweißoderhört;
(cid:2) automatisches Schlussfolgern, um Fragen anhand gespeicherter Informationen zu beant-
wortenundneueSchlussfolgerungenzuziehen;
(cid:2) maschinellesLernen,umsichanneueGegebenheitenanzupassensowieMusterzuerken-
nenundzuextrapolieren.
Turing hielt die physische Simulation einer Person für unnötig, um Intelligenz zu demons-
trieren.AndereForscherhabenjedocheinentotalenTuring-Testvorgeschlagen,derdieInter-
aktionmitObjektenundMenscheninderrealenWelterfordert.UmdentotalenTuring-Test
zubestehen,brauchteinRechneraußerdem
(cid:2) ComputerVisionundSpracherkennung,umdieWeltwahrzunehmen;
(cid:2) Robotik,umObjektezumanipulierenundsichzubewegen.
DiesesechsDisziplinenbildendengrößtenTeilderKI.DennochhabensichKI-Forschernur
selten am Turing-Test orientiert, weil sie glauben, es sei wichtiger, die zugrunde liegenden
PrinzipienderIntelligenzzuuntersuchen.SchließlichwarauchdieSuchenachdem„künst-
lichen Fliegen“ erst erfolgreich, als Ingenieure und Erfinder aufhörten, Vögel zu imitieren,
und stattdessen anfingen, Windkanäle einzusetzen undsich mit Aerodynamik zu beschäfti-
gen.InLehrbüchernderLuftfahrttechnikwirddasZielihresFachgebietsebennichtdefiniert
als„Bau vonMaschinen, diegenau wieTaubenfliegen, sodasssiesogarandere Taubentäu-
schenkönnen“.
1.1.2 Menschliches Denken: der Ansatz der kognitiven Modellierung
Wennwirsagen,einProgrammdenktwieeinMensch,somüssenwirwissen,wieMenschen
denken.WirhabendreiMöglichkeiten,etwasüberdasmenschlicheDenkenzulernen:
(cid:2) Introspektion–derVersuch,unsereeigenenGedankenzuerfassen,währendsievorbeizie-
hen;
(cid:2) psychologischeExperimente–dasBeobachteneinerhandelndenPerson;
(cid:2) Neuroimaging–dasBeobachtendesaktivenGehirns.
Sobald wir eine hinreichend präzise Theorie des Verstands haben, wird es möglich, diese
Theorie als Computerprogramm auszudrücken. Stimmen die Eingaben und Ausgaben des
Programms mit dem entsprechenden menschlichen Verhalten überein, dann ist dies ein
Beleg dafür, dasseinige derMechanismen desProgrammsauch beim Menschen funktionie-
renkönnten.
23
--- PAGE 25 ---
1
Einleitung
AllenNewellundHerbertSimon,diedenGPS–den„GeneralProblemSolver“(Newellund
Simon, 1961) entwickelten, begnügten sich zum Beispiel nicht damit, dass ihr Programm
Probleme korrekt löste. Es ging ihnen vielmehr darum, die Abfolge und das Timing der
Schlussfolgerungsschritte mitdenen menschlicher Probandenzu vergleichen, diedieselben
Problemelösten.DasinterdisziplinäreFeldderKognitionswissenschaftbringtComputermo-
delle aus der KI und experimentelle Techniken aus der Psychologie zusammen, um exakte
undüberprüfbareTheoriendesmenschlichenVerstandszukonstruieren.
Die Kognitionswissenschaft ist ein an sich faszinierendes Gebiet, das mehrerer Lehrbücher
undmindestenseinerEnzyklopädiewürdigist(WilsonundKeil,1999).Wirwerdengelegent-
lichauf Ähnlichkeiten oderUnterschiede zwischen KI-Techniken undmenschlicher Kogni-
tionhinweisen.DieeigentlicheKognitionswissenschaftbasiertjedochnotwendigerweiseauf
experimentellen Untersuchungen an „echten“ Menschen oder Tieren. Dies überlassen wir
aberanderenBüchern,dawirdavonausgehen,dassderLeserfürseineExperimentenurüber
einenRechnerverfügt.
IndenAnfängen derKIgabeshäufigVerwechselungen zwischen KI-undkognitivenAnsät-
zen. Ein Autor konnte behaupten, dass ein Algorithmus für eine Aufgabe gut geeignet und
deshalbeingutesModellfürdiemenschlicheLeistungsei,oderumgekehrt.ModerneAutoren
unterscheidenzwischen denbeidenAnsätzen–dieseUnterscheidunghatsowohlderKIals
auchderKognitionswissenschaft eineschnellereEntwicklungermöglicht.DiebeidenBerei-
chebefruchtensichgegenseitig, vorallemimGebietderComputerVision,dasneurophysio-
logische Erkenntnissein Berechnungsmodelle einbezieht. Injüngster Zeit hat dieKombina-
tion von Methoden des Neuroimagings mit Techniken des maschinellen Lernens zur Ana-
lyse dieser Daten zu den ersten Anfängen eines „Gedankenlesens“ geführt – das heißt, den
semantischenInhaltvonGehirntätigkeiteneinerPersonzuergründen.DieseFähigkeitkönnte
wiederumdieFunktionsweisedermenschlichenKognitionweitererhellen.
1.1.3 Rationales Denken: der Ansatz der „Denkgesetze“
DergriechischePhilosophAristoteleswareinerderersten,derversuchte,„richtigesDenken“
–alsounwiderlegbareProzessefürlogischeSchlussfolgerungen–zuformalisieren.SeineSyl-
logismenliefertenMusterfürArgumentationsstrukturen,diebeikorrektenPrämissenimmer
zu richtigen Schlussfolgerungen führten. Das kanonische Beispiel beginnt mit Sokrates ist
einMensch undAlleMenschen sindsterblich,daraus folgert er: Sokratesist sterblich.(Die-
sesBeispielgehtwahrscheinlicheheraufSextusEmpiricusalsaufAristoteleszurück.)Diese
Denkgesetze sollten die Arbeitsweise des Verstands abbilden; ihr Studium begründete das
GebietderLogik.
Die Logiker des 19. Jahrhunderts entwickelten eine präzise Notation für Aussagen über
Objekte in der Welt sowie den Beziehungen zwischen ihnen (im Gegensatz zur gewöhn-
lichen arithmetischen Notation, die nur Aussagen über Zahlen zulässt). Bis 1965 konnten
Programme im Prinzip jedes lösbare Problem lösen, das in logischer Notation beschrieben
war. Die sogenannte logizistische Tradition innerhalb der Künstlichen Intelligenz hofft, auf
solchenProgrammenaufbauenzukönnen,umintelligenteSystemezuschaffen.
Logik, wie sie konventionell verstanden wird, setzt ein Wissen über die Welt voraus, das
sicher ist – eine Bedingung, die in der Realität nur selten erreicht wird. Wir kennen die
Regeln etwa der PolitikoderderKriegsführung einfach nicht in dergleichen Weise wie die
Regeln des Schachs oder der Arithmetik. Die Wahrscheinlichkeitstheorie füllt diese Lücke,
indem sie stringentes Schlussfolgern mit unsicheren Informationen ermöglicht. Im Prinzip
erlaubt sie die Konstruktion eines umfassenden Modells des rationalen Denkens, das von
bloßenWahrnehmungsinformationenzueinemVerständnisderFunktionsweisederWeltund
zu Vorhersagen über die Zukunft führt. Was es nicht kann, ist, intelligentes Verhalten zu
erzeugen.DafürbrauchenwireineTheoriedesrationalenHandelns.RationalesDenkenallein
reichtnichtaus.
24
--- PAGE 26 ---
1.1 WasistKI?
1.1.4 Rationales Handeln: der Ansatzder rationalenAgenten
Ein Agent ist einfach etwas, das handelt (Agent kommt vom lateinischen agere – agieren,
tun, handeln). Natürlich tun alle Computerprogramme etwas, doch von Computeragenten
erwartet man,dasssiemehrtun:autonomagieren,ihreUmgebungwahrnehmen,übereinen
längerenZeitraumBestandhaben,sichanVeränderungenanpassensowieZieleerstellenund
verfolgen. EinrationalerAgenthandelt so,dasserdasbesteErgebnisbzw. beiUnsicherheit
dasbesteerwarteteErgebniserzielt.
BeimKI-Ansatz der„Denkgesetze“ lagdieBetonungaufkorrektenSchlussfolgerungen. Kor-
rekte Schlüsse zu ziehen, ist manchmal Teil des rationalen Agenten, denn eine Möglich-
keit,rationalzuhandeln,bestehtgeradedarinzufolgern,dasseinebestimmteHandlungdie
besteist,unddanndanachzuhandeln.AndererseitsgibtesWege,rationalzuhandeln,ohne
Schlussfolgerungen zu ziehen. Zum Beispiel ist das Zurückweichen voreiner heißen Herd-
platte eine Reflexhandlung, die normalerweise erfolgreicher ist als eine langsamere Hand-
lung,dienachsorgfältigerÜberlegungausgeführtwird.
AlleFertigkeiten,diefürdenTuring-Testbenötigtwerden,erlaubeneseinemAgentenauch,
rational zu handeln. Mithilfe von Wissensrepräsentation und Schlussfolgerungen können
Agenten gute Entscheidungen treffen. Wir müssen in der Lage sein, verständliche Sätze in
natürlicher Sprache zu bilden, um in einer komplexen Gesellschaft zurechtzukommen. Wir
müssennichtlernen,umWeisheitzuerlangen, sondernweilesunsereFähigkeitverbessert,
effektivesVerhaltenzugenerieren,besondersunterneuenUmständen.
Der KI-Ansatz des rationalen Agenten hat zwei Vorteile gegenüber den anderen Konzepten.
Erstens ist er allgemeiner als der Ansatz der „Denkgesetze“, weil korrektes Schlussfolgern
nureinervonmehrerenmöglichenMechanismenist,umRationalitätzuerreichen.Zweitens
ist er zugänglicher für die wissenschaftliche Entwicklung. Der Standard der Rationalität ist
mathematischwohldefiniertundvollständigallgemein.Wirkönnenoftausgehendvondieser
Spezifikation Agentenentwürfe ableiten, die den Standard nachweislich erreichen – etwas,
das weitgehend unmöglich ist, wenn das Ziel darin besteht, menschliches Verhalten oder
Denkprozessezuimitieren.
AusdiesenGründenhatsichdasKonzeptderrationalenAgenteninderKI-Geschichteallge-
meindurchgesetzt.IndenerstenJahrzehntenwurdenrationaleAgentenauflogischenGrund-
lagen aufgebaut und besaßen eindeutige Pläne, um bestimmte Ziele zu erreichen. Später
ermöglichtenMethoden,dieaufderWahrscheinlichkeitstheorie unddemmaschinellenLer- J
nenbasierten,dieEntwicklungvonAgenten,dieunterUnsicherheit Entscheidungentreffen
konnten, um das beste erwartete Ergebnis zu erzielen. Kurz gesagt, hat sich die KI auf die
Untersuchung undKonstruktionvonAgenten fokussiert,diedasRichtigetun.Wasals„das
Richtige“ zählt, wird durch das Ziel definiert, das wir dem Agenten vorgeben. Dieses allge-
meineParadigmaistsoallgegenwärtig, dasswiresalsStandardmodellbezeichnenkönnten.
EsistnichtnurinderKIvorherrschend,sondernauchinderKontrolltheorie,woeineKon-
trollinstanz(Controller)eineKostenfunktionminimiert;imBereichOperationsResearch,wo
eine Strategie eine Gesamtbelohnung maximiert; in der Statistik, wo eine Entscheidungsre-
gel eineVerlustfunktionminimiert;undindenWirtschaftswissenschaften, woeinEntschei-
dungsträgerdenNutzenodereinMaßfürdasAllgemeinwohlmaximiert.
Wir müssen eine wichtige Präzisierung des Standardmodells vornehmen, um der Tatsache
Rechnung zu tragen, dass perfekte Rationalität – immer genau die optimale Handlung zu
wählen–inkomplexenUmgebungennichterreichbarist.DieAnforderungenandieCompu-
terleistung sind einfach zu hoch. Die Kapitel 5 und 17 befassen sich mit dem Problem der
begrenzten Rationalität–das heißt, angemessenes Handeln, wenn nicht genug Zeit füralle
Berechnungen zurVerfügung steht,diemangerne durchführenwürde. Perfekte Rationalität
bleibtjedochofteinguterAusgangspunktfürdietheoretischeAnalyse.
25
--- PAGE 27 ---
1
Einleitung
1.1.5 Nutzbringende Maschinen
DasStandardmodellistseit seinen Anfängen einnützlicherLeitfaden fürdieKI-Forschung,
aberesistauflangeSichtwahrscheinlichnichtdasrichtigeModell.DerGrunddafürist,dass
dasStandardmodelldavonausgeht,dasswirderMaschineeinvollständigspezifiziertesZiel
liefern.
Bei einer künstlich definierten Aufgabe, wie z.B. Schach oder Berechnung des kürzesten
Wegs, istdasZieldirektmitderAufgabeverbunden–dasStandardmodellistalsoanwend-
bar.WennwirunsjedochindierealeWeltbegeben,wirdesimmerschwieriger,dasZielvoll-
ständig und korrekt zu spezifizieren. Zum Beispiel könnte man bei der Entwicklung eines
selbstfahrenden Autos denken, dass das Ziel darin besteht, den Bestimmungsort sicher zu
erreichen. Aber jegliches Fahren auf einer Straße birgt ein Verletzungsrisiko, sei es durch
Fehler anderer Fahrer, durch Geräteversagen usw. – ein striktes Sicherheitsziel müsste also
dazu führen, inderGarage zu bleiben. Esgibtjedoch eine Abwägung zwischen dem Voran-
kommeninRichtungBestimmungsortunddemRisikoeinerVerletzung.Wiesolltemandiese
Abwägung treffen?Undinwieweit kannmandemAutoAktionenerlauben, dieandere Fah-
rer beeinträchtigen würden? Wie stark sollte das Auto seine Beschleunigung, Lenkung und
Bremsendrosseln,umdieInsassennichtdurchzuschütteln?DieseArtvonFragensindapri-
orinurschwerzubeantworten.BesondersproblematischsindsieimallgemeinenBereichder
Mensch-Roboter-Interaktion,wofürdasselbstfahrendeAutoeinBeispielist.
DasProblem,eineÜbereinstimmungzwischenunserenwahrenPräferenzenunddemZielzu
erreichen, daswirindieMaschineeingeben, istdasProblemderWerteangleichung (Value-
Alignment-Problem):diederMaschineübergebenenWerteoderZielemüssenmitdenendes
Menschenübereinstimmen.WennwireinKI-SystemimLaboroderineinemSimulatorent-
wickeln – wie es im Laufe der KI-Geschichte meistens der Fall war –, gibt es eine einfache
Lösungfür ein falsch spezifiziertes Ziel: das System zurücksetzen, das Ziel korrigieren und
einen neuen Versuch starten. Mit der Entwicklung hin zu immer leistungsfähigeren intel-
ligenten Systemen, die in der realen Welt eingesetzt werden, ist dieser Ansatz nicht mehr
praktikabel.EinSystem,dasmiteinemfalschenZieleingesetzt wird,wirdnegativeAuswir-
kungenhaben.UndjeintelligenterdasSystemist,destonegativersinddieFolgen.
Kommen wir noch einmal auf das scheinbar unproblematische Beispiel des Schachspiels
zurück:ÜberlegenSie,waspassiert,wenndieMaschineintelligentgenugist,überdieGren-
zendesSchachbrettshinauszudenkenundzuhandeln.SiekönnteindiesemFallversuchen,
ihreGewinnchancendurchTrickswieHypnoseoderErpressungdesGegnerszuerhöhenoder
I dasPublikumzubestechen,damitesraschelndeGeräuscheinderZeitmacht,inderderGeg-
nernachdenkt.2 Siekönnteaußerdem versuchen, zusätzliche Rechenleistung fürsich selbst
zukapern.DieseVerhaltensweisen sindnicht„unintelligent“oder„verrückt“–siesindeine
logischeKonsequenzdaraus,wennmanGewinnenalseinzigesZielderMaschinedefiniert.
Esistunmöglichvorherzusehen,aufwelcheWeisensicheineMaschine,dieeinbestimmtes
Ziel verfolgt, falsch verhalten könnte. Das heißt, wir haben allen Grund anzunehmen, dass
dasStandardmodellunzureichendist.WirwollenkeineMaschinen,dieindemSinneintelli-
gentsind,dasssieihreZieleverfolgen–wirmöchten,dasssieunsereZieleverfolgen.Wenn
wir unsere Ziele nicht absolut korrekt auf dieMaschine übertragen können,dann brauchen
wireineneueZielformulierung–dieMaschinesollunsereZieleverfolgen, istaberzwangs-
läufig unsicher, wie diese genau lauten. Weiß eine Maschine, dass sie das Gesamtziel nicht
kennt,dannhatsieeinenAnreiz,vorsichtigzuhandeln,umErlaubniszufragen,durchBeob-
achtungmehrüberunserePräferenzen zuerfahrenundsichdermenschlichenSteuerungzu
unterstellen.LetztendlichwollenwirAgenten,dienachweislichnutzbringendfürdenMen-
schensind.WirwerdenaufdiesesThemainAbschnitt1.5zurückkommen.
2 IneinemdererstenBücherüberSchachschriebRuyLopez(1561):„LegedasBrettimmerso,dassdieSonne
indenAugendeinesGegnerssteht.“
26
--- PAGE 28 ---
1.2 DieGrundlagenderKünstlichenIntelligenz
1.2 Die Grundlagen der Künstlichen Intelligenz
IndiesemAbschnittgebenwireinenkurzengeschichtlichen AbrissderDisziplinen,diemit
Ideen, Sichtweisen undTechniken zurKI beigetragen haben.Wie jedeChronikkonzentriert
sich auch diese auf eine kleine Anzahl von Personen, Ereignisse und Ideen und ignoriert
andere, die ebenfalls wichtig waren. Wir arrangieren unseren historischen Abriss um eine
Reihe von Fragen herum. Keinesfalls möchten wir den Eindruck erwecken, die Disziplinen
würden sich ausschließlich mit diesen Fragen befassen oder hätten alle auf die KI als ihre
ultimativeErfüllunghingearbeitet.
1.2.1 Philosophie
(cid:2) KannmanformaleRegelneinsetzen,umgültigeSchlüssezuziehen?
(cid:2) WieentstehtderVerstandauseinemphysischenGehirn?
(cid:2) WoherstammtWissen?
(cid:2) WieführtWissenzumHandeln?
Aristoteles(384–322v.Chr.)formuliertealsErstereinepräziseMengevonGesetzen,dieden
rationalenTeildesGeisteslenken.ErentwickelteeininformellesSystemvonSyllogismenfür
korrektes Schlussfolgern, dases imPrinzip erlaubte, ausgehend vonPrämissen mechanisch
Schlüssezuziehen.
Ramon Llull (ca. 1232–1315) entwickelte ein System des logischen Denkens, das als Ars
Magna (zu deutsch Große Kunst; 1305) veröffentlicht wurde. Llull versuchte, sein System
mithilfeeinestatsächlichenmechanischenGerätsumzusetzen:einerReihevonPapierrädern,
dieinverschiedeneKombinationengedrehtwerdenkonnten.
Um das Jahr 1500 entwarf Leonardo da Vinci (1452–1519) eine mechanische Rechenma-
schine,bautesieabernie;neuereRekonstruktionenzeigen,dassderEntwurffunktionstüchtig
war.DieerstebekannteRechenmaschinewurdeum1623vondemdeutschenGelehrtenWil-
helmSchickard(1592–1635)konstruiert.BlaisePascal(1623–1662)baute1642diePascaline
undschrieb, dasssie„Wirkungen zeigt, diedemDenkennäherkommenalsalles, was Tiere
vollbringen“.GottfriedWilhelmLeibniz(1646–1716)bauteunteranderemeinmechanisches
Gerät, das Operationen mit Begriffen anstelle von Zahlen durchführen sollte, aber dessen
Anwendungsbereich wareherbegrenzt.ThomasHobbes(1588–1679)schluginseinem1651
erschienen BuchLeviathandasKonzept einerdenkenden Maschine vor,eines „künstlichen
Tiers“,wieeresnannte,undargumentierte: „DennwasistdasHerz,wennnichteineFeder,
wassinddieNerven,wennnichtdievielenStränge,undwasnichtdieGelenke,wennnicht
dievielen Räder.“Erschlugauch vor,dasDenkeneinernumerischen Berechnunggleichzu-
setzen: „Denn ‚Vernunft‘...istnichtsanderes als‚Rechnen‘, dasheißtAddieren undSubtra-
hieren.”
Es ist eine Sache zu sagen, dass der Verstand – zumindest teilweise – nach logischen oder
numerischen Regeln arbeitet,undrealeSystemezubauen,dieeinigedieserRegeln nachbil-
den.EineandereSacheisteszubehaupten,dassderVerstandselbsteinsolchesphysisches
Systemist.RenéDescartes(1596–1650)liefertedieersteklareDiskussionzurUnterscheidung
zwischen Geist undMaterie. Erstellte fest,dasseinerein physischeAuffassung desGeistes
wenigRaumfürdenfreienWillenzulassenschien.WennderGeistausschließlichvonphysi-
schenGesetzenbeherrschtwird,dannhatergenausowenigeinenfreienWillenwieeinStein,
der„beschließt“,nachuntenzufallen.DescarteswareinVerfechterdesDualismus.Ervertrat
die Ansicht, dass es einen Teil des menschlichen Verstands (oder der Seele oder des Geis-
tes) gibt, der außerhalb der Natur steht und nicht den Gesetzen der Physik unterliegt. Tiere
hingegenbesäßendiesedualeQualitätnicht;siekönntenwieMaschinenbehandeltwerden.
27
--- PAGE 29 ---
1
Einleitung
EineAlternative zumDualismusistderMaterialismus,derdavonausgeht, dassdieFunkti-
onsweisedesGehirnsdenGeistnachdenGesetzen derPhysikbildet.DerfreieWilleistein-
fachdieArtundWeise, wiediewählendeEntitätdieverfügbaren Alternativen wahrnimmt.
DieBegriffePhysikalismusundNaturalismuswerdenebenfallsverwendet,umdieseAnsicht
zubeschreiben,dieimGegensatzzumÜbernatürlichensteht.
Angesichts eines physischen Geistes, der Wissen manipuliert, besteht das nächste Problem
darin,dieQuelledesWissenszubestimmen.DieBewegungdesEmpirismus,beginnendmit
FrancisBacons(1561–1626)NovumOrganum3,istdurcheinDiktumvonJohnLocke(1632–
1704)gekennzeichnet:„NichtsistimVerstand,wasnichtvorherindenSinnenwar.“
David Hume (1711–1776)schlug in Ein Traktat über die menschliche Natur (Hume, 1739)4
dasvor,washeutealsPrinzip derInduktionbekannt ist:Allgemeine Regeln werden erwor-
ben,indemmansichdenAssoziationenzwischenihrenElementenwiederholtaussetzt.
Aufbauend auf den Arbeiten von Ludwig Wittgenstein (1889–1951) und Bertrand Russell
(1872–1970)entwickeltederberühmteWienerKreis(Sigmund,2017),eineGruppevonPhi-
losophenundMathematikern,diesich inden1920erund1930erJahren inWientrafen, die
Doktrin des logischen Positivismus. Diese Lehre besagt, dass alles Wissen durch logische
Theoriencharakterisiertwerdenkann,dieletztlichmitBeobachtungssätzenverbundensind,
diedenSinneseindrückenentsprechen–derlogischePositivismusverbindetsomitRationa-
lismusundEmpirismus.
DieBestätigungstheorie vonRudolfCarnap (1891–1970)und Carl Hempel (1905–1997)ver-
suchte,denErwerb vonWissen ausErfahrungzu analysieren, indemderGradderÜberzeu-
gungquantifiziertwurde,derlogischenSätzenzugewiesenwerdensollte.DieserGradbasiert
auf derVerbindungderSätze zu den Beobachtungen, diedieSätze bestätigen oderwiderle-
gen. Carnaps Buch Der logische Aufbau der Welt5 (1928) war vermutlich die erste Theorie,
diedenVerstandalsRechenprozessbetrachtete.
DasletzteElementinderphilosophischenBetrachtungdesVerstandsistdieVerbindungzwi-
schen Wissen und Handeln. Diese Frage ist für die KI von entscheidender Bedeutung, weil
IntelligenzsowohlHandelnalsauchDenkenerfordert.Außerdemkönnenwirnurdurchdas
Verständnis,wieHandlungengerechtfertigtwerden,verstehen,wiemaneinenAgentenbaut,
dessenHandlungengerechtfertigt(oderrational)sind.
Aristoteles argumentierte in De MotuAnimalium(Über die Bewegung der Lebewesen), dass
Handlungengerechtfertigtsind,wenneseinelogischeVerbindungzwischenZielenunddem
WissenüberdasErgebnisderHandlunggibt:
Aberwiekannessein,dassdasDenkenmanchmalvonHandelnbegleitetwirdundmanchmal
nicht,manchmalvonBewegungundmanchmalnicht?Esscheint,alspassierefastdasselbebeim
DenkenundbeimSchlussfolgernübersichunveränderlicheObjekte.DochinjenemFallistdas
Ziel eine spekulative Aussage ... während hier die Schlussfolgerung, die sich aus den beiden
Prämissenergibt,eineHandlungist.... IchbraucheBekleidung;einMantelisteineBekleidung.
IchbraucheeinenMantel.Wasichbrauche,mussichanfertigen;ichbraucheeinenMantel.Ich
musseinenMantelanfertigen.UnddieSchlussfolgerung„IchmusseinenMantelanfertigen“ist
eineHandlung.
In der Nikomachischen Ethik (Buch III. 3, 1112b) geht Aristoteles weiter auf dieses Thema
einundschlägteinenAlgorithmusvor:
UnsereÜberlegungbetrifftnichtdasZiel,sonderndieMittel,eszuerreichen.DerArztüberlegt
nicht,oberheilensoll,derRednernicht,oberüberzeugensoll,... sondernnachdemmansich
3 DasNovumOrganumisteineAktualisierungvonAristoteles’Organon.
4 Originaltitel:ATreatiseofHumanNature.
5 Originaltitel:TheLogicalStructureoftheWorld.
28
--- PAGE 30 ---
1.2 DieGrundlagenderKünstlichenIntelligenz
einZielgestellthat,siehtmansichum,wieunddurchwelcheMitteleszuerreichenist,wennes
durchverschiedeneMittelmöglichscheint,siehtmanzu,durchwelchesesamleichtestenund
bestenerreichtwird;undwennesdurcheinesregelrechtverwirklichtwird,fragtmanwieder,wie
esdurchdasselbeverwirklichtwird,undwodurchwiederumjenes,bismanzudererstenUrsache
gelangt,... das,wasbeiderAnalysealsLetztesherauskommt,istbeiderVerwirklichungdurch
dieHandlungdasErste.StößtmanaufeineUnmöglichkeit,sostehtmanvonderSacheab,z.B.
wenn sich Geldmittel erforderlich zeigen, die man nicht aufbringen kann. Erscheint die Sache
abermöglich,sonimmtmansieindieHand.6
Der Algorithmus von Aristoteles wurde 2300 Jahre später von Newell und Simon in ihrem
General Problem Solver implementiert. Wir würden es heute ein „gieriges Regressionspla-
nungssystem“nennen(sieheKapitel11).Methoden,dieauflogischerPlanungzumErreichen
bestimmterZieleberuhen,dominiertendieerstenJahrzehntedertheoretischenForschungin
derKI.
Sichnurdaraufzukonzentrieren,ZieledurchHandlungenzuerreichen,istoftnützlich,aber
manchmalungeeignet.Gibtesz.B.verschiedeneWege,einZielzuerreichen,somusseseine
Möglichkeit geben, zwischen diesen Wegen zu wählen. Noch entscheidender ist der Fall,
wenn es vielleicht nicht möglich ist, ein Ziel mit Sicherheit zu erreichen, aber irgendeine
Handlung trotzdem ausgeführt werden muss. Wie sollte man dann eine Entscheidung tref-
fen? Antoine Arnauld (1662) analysierte das Konzept der rationalen Entscheidungen beim
GlücksspielundschlugeinequantitativeFormelvor,mitderenHilfedererwarteteGeldwert
des Ergebnisses maximiert werden konnte. Später führte Daniel Bernoulli (1738) den allge-
meineren Begriff des Nutzens ein, um den internen, subjektiven Wert eines Ergebnisses zu
erfassen. Die moderne Vorstellung von rationaler Entscheidungsfindung unter Unsicherheit
beinhaltetdieMaximierungdeserwartetenNutzens,diesesThemawirdinKapitel16behan-
delt.
InFragenderEthikundderöffentlichenOrdnungmusseinEntscheidungsträgerdieInteres-
senmehrererIndividuenberücksichtigen.JeremyBentham(1823)undJohnStuartMill(1863)
unterstützten die Idee des Utilitarismus: das Konzept, dass die rationale Entscheidungsfin-
dung,dieaufderMaximierungdesNutzensbasiert,füralleBereichemenschlicherAktivitä-
ten gelten sollte,einschließlich deröffentlichenpolitischen Entscheidungen, dieimNamen
vielerIndividuengetroffenwerden.DerUtilitarismusisteinespezifischeArtdesKonsequen-
zialismus: was richtig und falsch ist, wird durch die erwarteten Ergebnisse einer Handlung
bestimmt.
ImGegensatzdazuschlugImmanuelKantimJahr1785eineTheoriederregelbasierten oder
deontologischen Ethik vor, in der „das Richtige tun“ nicht von den Ergebnissen bestimmt
wird,sondernvonuniversellensozialenGesetzen,dieerlaubteHandlungenregeln,wiez.B.
„Du sollst nicht lügen“ oder „Du sollst nicht töten“. So könnte ein Utilitarist eine Notlüge
benutzen, wenn das erwartete Gutedas Schlechte überwiegt, aber ein Kantianer dürfte dies
nichttun,weilLügenvonNaturausfalschsind.MillerkanntedenWertvonRegelnan,ver-
standsieaberalseffizienteEntscheidungsprozeduren,dieausgrundsätzlichenÜberlegungen
zuKonsequenzenzusammengestelltwerden.VielemoderneKI-Systemeverfolgengenaudie-
senAnsatz.
1.2.2 Mathematik
(cid:2) WassinddieformalenRegeln,umgültigeSchlussfolgerungenzuziehen?
(cid:2) Waskannberechnetwerden?
(cid:2) WieschlussfolgernwirmitunsicherenInformationen?
6 ÜbersetzungnachE.Rolfes(1910).
29
--- PAGE 31 ---
1
Einleitung
DiePhilosophenumrisseneinigegrundlegendenIdeenderKI,dochderSprungzueinerfor-
malenWissenschaft erfordertediemathematischeAufbereitungvonLogikundWahrschein-
lichkeitsowiedieEinführungeinesneuenZweigsderMathematik:dieBerechenbarkeit.
Das Konzept einer formalen Logik lässt sich bis zu den Philosophen des antiken Griechen-
lands, Indiens und Chinas zurückverfolgen, doch ihre mathematische Entwicklung begann
eigentlich mit der Arbeit von George Boole (1815–1864),der die Details der Aussagenlogik
(oder booleschen Logik) ausarbeitete (Boole, 1847). Im Jahr 1879 erweiterte Gottlob Frege
(1848–1925)dieboolescheLogikumObjekteundRelationenundschufdamitdieheutever-
wendete Prädikatenlogik (oder Logik erster Ordnung).7 Zusätzlich zu ihrer zentralen Rolle
inderfrühenPhasederKI-ForschungmotiviertediePrädikatenlogikdieArbeitenvonGödel
und Turing, die die Berechenbarkeit selbst zum Thema hatten, wie wir weiter unten noch
ausführenwerden.
Die Wahrscheinlichkeitstheorie kann als Verallgemeinerung der Logik auf Situationen mit
unsicherer Informationslage gesehen werden – eine Überlegung, die für die KI von großer
Bedeutungist.GerolamoCardano(1501–1576)formuliertealsErsterdieIdeederWahrschein-
lichkeitundbeschrieb sieinBezugaufdiemöglichenErgebnissebeieinemGlücksspiel.Im
Jahr1654zeigteBlaisePascal(1623–1662)ineinemBriefanPierreFermat(1601–1665),wie
man den Ausgang eines abgebrochenen Glücksspiels vorhersagen und den Spielern durch-
schnittlicheAuszahlungenzuweisenkann.DieWahrscheinlichkeitsrechnungwurdeschnell
zueinemunschätzbarenTeilderquantitativenWissenschaftenundhalf,mitunsicherenMes-
sungen und unvollständigen Theorien umzugehen. Jakob Bernoulli (1654–1705, der Onkel
vonDanielBernoulli),PierreLaplace(1749–1827)undandereentwickeltendieTheoriewei-
terundführtenneuestatistischeMethodenein.ThomasBayes(1702–1761)schlugeineRegel
zum Aktualisieren von Wahrscheinlichkeiten angesichts neuer Informationen vor – diese
Bayes’scheRegelisteinwichtigesWerkzeugfürKI-Systeme.
Die Formalisierung der Wahrscheinlichkeit, kombiniert mit der Verfügbarkeit von Daten,
führtezurEntstehungderStatistikalsneuemFachgebiet.EinedererstenAnwendungenwar
die Analyse von Daten der Londoner Sterbeverzeichnisse durch John Graunt im Jahr 1662.
Ronald Fisher gilt als der erste moderne Statistiker (Fisher, 1922). Er brachte die Konzepte
vonWahrscheinlichkeit, statistischer Versuchsplanung, Datenanalyse und Durchführen von
Berechnungenzusammen–imJahr1919bestanderdarauf,dasserfürseineArbeitunbedingt
eine mechanische Rechenmaschine namens MILLIONAIRE (die erste Rechenmaschine, die
multiplizieren konnte) benötigte, obwohl die Anschaffungskosten für die Rechenmaschine
seinJahresgehaltüberstiegen(Ross,2012).
Die Geschichte des Rechnens ist so alt wie die Geschichte der Zahlen, aber als erster nicht
trivialer Algorithmus gilt Euklids Algorithmus zur Berechnung des größten gemeinsamen
Teilerszweier natürlicherZahlen. DasWortAlgorithmusstammtvonMuhammadibnMusa
al-Khwarizmi,einemMathematikerdes9.Jahrhunderts,dessenSchriftenauchdiearabischen
Ziffern und die Algebra nach Europa brachten. Booleund andere diskutierten Algorithmen
für die logische Deduktion und im späten 19. Jahrhundert gab es Bestrebungen, allgemeine
mathematischeBeweisealslogischeDeduktionzuformalisieren.
KurtGödel(1906–1978)zeigte,dasseseineffektivesVerfahrengibt,umjedewahreAussage
in der Prädikatenlogik von Frege und Russell zu beweisen, dass die Prädikatenlogik jedoch
nicht das Prinzip der mathematischen Induktion erfassen kann, das zur Charakterisierung
der natürlichen Zahlen notwendig ist. Im Jahr 1931 bewies Gödel, dass es Grenzen für die
Deduktiongibt.SeinUnvollständigkeitssatzzeigte,dassesinjederformalenTheorie,dieso
starkistwiediePeano-Arithmetik(dieelementareTheoriedernatürlichenZahlen),notwen-
digerweisewahreAussagengibt,dienichtinnerhalbderTheoriebewiesenwerdenkönnen.
7 DievonFregevorgeschlageneNotationfürdiePrädikatenlogik–einegeheimnisvolleKombinationaustex-
tuellenundgeometrischenMerkmalen–hatsichniedurchgesetzt.
30
--- PAGE 32 ---
1.2 DieGrundlagenderKünstlichenIntelligenz
Dieses fundamentale Ergebnis kann auch dahingehend interpretiert werden, dass einige
Funktionen auf den ganzen Zahlen nicht durch einen Algorithmus dargestellt werden kön-
nen–dasheißt,nichtberechnetwerdenkönnen.DiesmotivierteAlanTuring(1912–1954)zu
dem Versuch, genau zucharakterisieren, welche Funktionenberechenbarsind –alsodurch
ein effektives Verfahren berechnet werden können. Die Church-Turing-These schlägt vor,
allgemeineineFunktionalsberechenbarzudefinieren,wennsievoneinerTuring-Maschine
berechnet werden kann (Turing, 1936). Turing zeigte außerdem, dass es einige Funktionen
gibt, die von keiner Turing-Maschine berechnet werden können. Zum Beispiel kann keine
Maschine imAllgemeinen sagen, obein bestimmtesProgramm bei einer gegebenen Eingabe
eineAntwortzurückgibtoderunendlichläuft.
ObwohldieBerechenbarkeitwichtigfürdasVerständnisvonRechenoperationenist,hatteder
BegriffdereffizientenMachbarkeit(tractability)einennochgrößerenEinflussaufdieKI.Ver-
einfacht ausgedrückt wird ein Problem als nicht effizient machbar (intractable) bezeichnet,
wenndieZeit,diefürdieLösungvonInstanzendesProblemsbenötigtwird,exponentiellmit
derGrößederInstanzenwächst.DieUnterscheidungzwischenpolynomiellemundexponen-
tiellem Komplexitätswachstum wurde erstmals Mitte der 1960er(Cobham, 1964; Edmonds,
1965)Jahrethematisiert.Sieistwichtig,weilexponentiellesWachstumbedeutet,dassselbst
mäßiggroßeInstanzennichtineinerangemessenenZeitgelöstwerdenkönnen.
DieTheoriederNP-Vollständigkeit, dieauf Cook(1971)undKarp(1972)zurückgeht, bietet
eine Grundlage für die Analyse der effizienten Machbarkeit von Problemen: Jede Problem-
klasse, auf die die Klasse der NP-vollständigen Probleme reduziert werden kann, ist wahr-
scheinlichnichteffizientmachbar.(Obwohlesnichtbewiesenist,dassNP-vollständigePro-
blemezwangsläufignichteffizientmachbarsind,gehendiemeistenTheoretikerdavonaus.)
Diese Ergebnisse stehen im Gegensatz zu dem Optimismus, mit dem die Boulevardpresse
dieerstenComputerbegrüßthatte–„elektronischeSuperhirne“,die„schnelleralsEinstein“
wären!TrotzdersteigendenGeschwindigkeitvonComputernzeichnensichintelligenteSys-
temedurchsorgfältigeNutzungvonRessourcenunddienotwendigeUnvollkommenheitaus.
DieWeltist,saloppgesagt,eineextremgroßeProbleminstanz!
1.2.3 Wirtschaftswissenschaft
(cid:2) WiesolltenwirEntscheidungentreffen,diemitunserenPräferenzenübereinstimmen?
(cid:2) Wiesolltenwirdiestun,wennanderenichtmitmachen?
(cid:2) Wiesolltenwirdiestun,wennderGewinnweitinderZukunftliegt?
DieWirtschaftswissenschaftentstand1776,alsAdamSmith(1723–1790)seinWerkDerWohl-
standderNationen8veröffentlichte.Smithschlugvor,Wirtschaftssystemezuanalysieren,die
aus vielen einzelnen Akteuren bestehen, die jeweils ihre eigenen Interessen wahrnehmen.
SmithwarjedochkeinVerfechterderfinanziellenGieralsmoralischePosition:Seinfrüheres
BuchTheoriederethischenGefühle9(1759)beginntmitdemHinweis,dassdieSorgeumdas
Wohlergehen anderer ein wesentlicher Bestandteil der Interessen eines jeden Individuums
ist.
Die meisten Menschen denken, dass es in der Wirtschaftswissenschaft um Geld geht, und
in der Tat befasste sich die erste mathematische Analyse von Entscheidungen unter Unsi-
cherheit,dieFormelfürdenmaximalenErwartungswertvonArnauldundNicole(1662),mit
dem Geldwert vonWetten. Daniel Bernoulli(1738)bemerkte, dass diese Formelfürgrößere
Geldbeträge, wie z.B. Investitionen in maritime Handelsexpeditionen, nicht gut zu funktio-
nierenschien.ErschlugstattdesseneinPrinzipvor,dasaufderMaximierungdeserwarteten
8 Originaltitel:AnInquiryintotheNatureandCausesoftheWealthofNations.
9 Originaltitel:TheTheoryofMoralSentiments.
31
--- PAGE 33 ---
1
Einleitung
Nutzens basiert, und erklärte die menschlichen Investitionsentscheidungen damit, dass der
GrenznutzeneinerzusätzlichenGeldmengeabnimmt,wennmanmehrGelderwirbt.
LéonWalras(1834–1910)gabderNutzentheorieeineallgemeinereGrundlage,indemerPrä-
ferenzen zwischen Spielen mitbeliebigen Ergebnissen (nicht nurmonetäre Ergebnisse) ein-
führte.DieTheoriewurdevonRamsey(1931)undspätervonJohnvonNeumannundOskar
MorgensterninihremBuchTheTheoryofGamesandEconomicBehavior(1944)verbessert.
In der Wirtschaftswissenschaft geht es nicht mehr nurum Geld, sondern um Wünsche und
Präferenzen.
Die Entscheidungstheorie, die die Wahrscheinlichkeitstheorie mit der Nutzentheorie kom-
biniert, bietet einen einfachen und vollständigen Rahmen für individuelle (wirtschaftliche
oderanderweitige) Entscheidungen, dieunterUnsicherheit getroffen werden –dasheißt,in
Fällen, in denen probabilistische Beschreibungen die Umgebung des Entscheidungsträgers
angemessen erfassen. Dies ist geeignet für„große“ Wirtschaftssysteme, in denen kein Agent
die Handlungen anderer Agenten als Individuen berücksichtigen muss. Für „kleine“ Wirt-
schaftssystemeähneltdieSituationehereinemSpiel:DieHandlungeneinesSpielerskönnen
den Nutzen eines anderen signifikant beeinflussen (entweder positiv oder negativ). Die von
MorgensternundvonNeumannentwickelteSpieltheorie(sieheauchLuceundRaiffa,1957)
beinhaltete das überraschende Ergebnis, dass ein rationaler Agent bei einigen Spielen eine
Zufallsstrategie (oder die zumindest als zufällig erscheint) verfolgen sollte. Anders als die
EntscheidungstheoriebietetdieSpieltheoriekeineeindeutigeVorschriftdafür,wieAktionen
auszuwählen sind. In der KI werden Entscheidungen, an denen mehrere Agenten beteiligt
sind,unterdemBegriffMultiagentensystemebehandelt(Kapitel18).
Wirtschaftswissenschaftlerhabensich,voneinigenAusnahmenabgesehen,nichtmitderdrit-
ten der oben genannten Fragen befasst: wie man rationale Entscheidungen trifft, wenn die
Auszahlung (der Nutzen) aus den Aktionen nicht unmittelbarstattfindet, sondern erst nach
mehreren Aktionen erfolgt, die nacheinander ausgeführt werden. Dieses Thema wurde im
Bereich des Operations Research verfolgt, der im Zweiten Weltkrieg aus Bemühungen im
Vereinigten Königreich zur Optimierung von Radaranlagen entstand und später unzählige
zivileAnwendungen fand. Die Arbeit vonRichard Bellman (1957)formalisierte eineKlasse
vonsequenziellenEntscheidungsproblemen,dieMarkov-Entscheidungsprobleme,diewirin
Kapitel 17 und, unter der Überschrift des Reinforcement Learning (Verstärkungslernen), in
Kapitel22untersuchen.
Die Arbeiten aus den Bereichen Wirtschaftswissenschaften und Operations Research haben
viel zu unserem Konzept der rationalen Agenten beigetragen, dennoch entwickelte sich die
KI-Forschung viele Jahre lang auf völlig davon getrennten Wegen. Ein Grund dafür war die
offensichtlicheKomplexität,diemitdemFindenrationalerEntscheidungen einhergeht. Der
Pionier der KI-Forschung Herbert Simon (1916–2001) erhielt 1978 den Nobelpreis in Wirt-
schaftswissenschaften für sein frühes Werk. Darin zeigte er, dass Modelle, die auf Satisfi-
cing10 basieren–dasheißt,Entscheidungenzutreffen,die„gutgenug“sind,anstattmühsam
eine optimale Entscheidung zu berechnen –, das tatsächliche menschliche Verhalten besser
beschreiben(Simon,1947).Seitden1990erJahrenistdasInteresseanentscheidungstheore-
tischenTechnikenfürdieKIwiedergestiegen.
1.2.4 Neurowissenschaft
(cid:2) WieverarbeitetdasGehirnInformationen?
Neurowissenschaft ist das Studium des Nervensystems, insbesondere des Gehirns. Obwohl
diegenaueArtundWeise,wiedasGehirndasDenkenermöglicht,einesdergroßenGeheim-
10ZuDeutsch:Zufriedenstellung.
32
--- PAGE 34 ---
1.2 DieGrundlagenderKünstlichenIntelligenz
Verzweigung des Axons
Axon von einer anderen Zelle
Synapse
Dendrit
Axon
Nucleus
Synapsen
Zellkörper oder Soma
Abbildung1.1:BestandteileeinerNervenzelle(Neuron).JedesNeuronbestehtauseinemZellkörper,Soma,daseinenZellkern
enthält.VomZellkörperverzweigensicheineReihevonFasern,dieDendritengenanntwerden,sowieeineeinzelnelangeFaser,
dasAxon.DasAxonerstrecktsichübereinelangeDistanz,diegrößerist,alsderMaßstabindieserAbbildungandeutet.Inder
RegelisteinAxon1cmlang(das100-FachedesDurchmessersdesZellkörpers),eskannaberauchbiszu1Meterlangsein.
EinNeuronknüpftVerbindungenmit10bis100.000anderenNeuronenanVerbindungsstellen,denSynapsen.Signalewerden
vonNeuronzuNeuronmittelseinerkompliziertenelektrochemischenReaktionweitergeleitet.DieSignalesteuernkurzfristig
dieHirnaktivitätundermöglichenauchlangfristigeVeränderungeninderVerschaltungderNeuronen.Mannimmtan,dass
dieseMechanismendieGrundlagefürdasLernenimGehirnbilden.DergrößteTeilderInformationsverarbeitungfindetin
derGroßhirnrinde(Kortex)statt,deräußerenSchichtdesGehirns.DiegrundlegendeorganisatorischeEinheitscheinteineArt
GewebesäulemiteinemDurchmesservonetwa0,5mmzusein,deretwa20.000Neuronenenthältundsichüberdiegesamte
TiefedesKortex(beimMenschenetwa4mm)erstreckt.
nisse der Wissenschaft ist, wird die Tatsache, dass es das Denken ermöglicht, seit Tausen-
den von Jahren anerkannt, denn es ist bewiesen, dass schwere Kopfverletzungen zu geisti-
gerBeeinträchtigungführenkönnen.EsistauchseitLangembekannt,dassdasmenschliche
Gehirn irgendwie anders ist; etwa 335v.Chr. schrieb Aristoteles: „Von allen Tieren hat der
MenschdasgrößteGehirnimVerhältniszuseinerGröße.“11 DennochwurdedasGehirnerst
Mittedes18.JahrhundertsallgemeinalsSitzdesBewusstseinsanerkannt.Davorkamenunter
anderemdasHerzunddieMilzalsKandidateninfrage.
Mit den Forschungsarbeiten von Paul Broca (1824–1880) zur Aphasie (Sprachstörung) bei
hirngeschädigtenPatientenimJahr1861beganndieUntersuchungderfunktionellenOrgani-
sation des Gehirns. Broca konnte ein abgegrenztes Areal in der linken Hemisphäre identifi-
zieren –heuteBroca-Areal genannt–,dasfürdieSprachproduktionverantwortlichist.12 Zu
dieser Zeit war bereits bekannt, dass das Gehirn größtenteils aus Nervenzellen oder Neuro-
nenbesteht,abererst1873entwickelteCamilloGolgi(1843–1926)eineFärbemethode,diedie
Beobachtung einzelner Neuronen ermöglichte (IAbbildung 1.1). Diese Technik wurde von
SantiagoRamónyCajal(1852–1934)inseinenbahnbrechendenStudienzurneuronalenOrga- J
nisation verwendet.13 Es ist heute allgemein anerkannt, dass kognitive Funktionen aus der
elektrochemischen Funktionsweise dieser Strukturen resultieren. Das heißt, eine Ansamm-
11Inzwischenhatmanherausgefunden, dassdasSpitzhörnchenundeinigeVogelarteneingrößeres Gehirn-
Körper-VerhältnisalsderMenschhaben.
12VielfachwirdauchAlexanderHood(1824)alsmöglichefrühereQuellezitiert.
13GolgibeharrteaufseinerÜberzeugung,dassdieFunktionendesGehirnsinersterLinieineinemkontinuierli-
chenMediumausgeführtwerden,indasNeuroneneingebettetsind,währendRamónyCajaldie„Neuronendok-
trin“vertrat.Diebeidenteiltensich1906denNobelpreis,hieltenabergegenseitigantagonistischeDankesreden.
33
--- PAGE 35 ---
1
Einleitung
lung von einfachen Zellen kann zu Denken, Handeln und Bewusstsein führen. In den prä-
gnantenWortenvonJohnSearle(1992),GehirnverursachtGeist.
HeutehabenwireinigeDatenüberdieZuordnungenzwischenHirnarealenunddenKörper-
teilen, diesiesteuern odervondenen siesensorische Eingaben erhalten. SolcheZuordnun-
genkönnensichimLaufeeinigerWochenradikaländernundeinigeTierescheinenmehrere
Zuordnungen zu besitzen. Außerdem verstehen wir nicht vollständig, wie andere Bereiche
Funktionenübernehmenkönnen,wenneinBereichgeschädigtist.EsgibtfastkeineTheorie
darüber,wiedasGedächtniseinesMenschengespeichertwirdoderwiekognitiveFunktionen
aufhöhererEbenearbeiten.
DieMessungderintaktenGehirnaktivitätbegann1929mitderErfindungderElektroenzepha-
lografie (EEG)durch Hans Berger. Die EntwicklungderfunktionellenMagnetresonanztomo-
grafie(fMRT;Ogawaetal.,1990;CabezaundNyberg,2001)liefertdenNeurowissenschaftlern
beispiellosdetaillierteBilderderHirnaktivitätundermöglichtMessungen,dieaufspannende
Weise mit aktuell ablaufenden kognitiven Prozessen korrespondieren. Hinzu kommen Fort-
schritteinderelektrischenEinzelzellaufzeichnungderNeuronenaktivitätunddieMethoden
der Optogenetik (Crick, 1999; Zemelman et al., 2002; Han und Boyden, 2007), die sowohl
die Messung als auch die Steuerung einzelner Neuronen ermöglichen, die lichtempfindlich
gemachtwurden.
Die Entwicklung von Gehirn-Maschine-Schnittstellen (Brain-Machine-Interface; Lebedev
und Nicolelis, 2006) sowohl für die Sensorik als auch für die motorische Steuerung ver-
sprichtnichtnurdieWiederherstellung derFunktionalitätvonGliedmaßenbeibehinderten
Menschen, sondernwirft aucheinLicht aufvieleAspekteneuronalerSysteme.Einebemer-
kenswerte Erkenntnis aus dieser Arbeit ist, dass das Gehirn in der Lage ist, sich selbst so
einzustellen,dasseserfolgreichmiteinemexternenGerätinteragierenkann,indemesdieses
praktischwieeinweiteresSinnesorganodereinKörperteilbehandelt.
GehirneunddigitaleComputerhabenrechtunterschiedlicheEigenschaften.IAbbildung1.2
zeigt,dassdieZykluszeitvonComputerneineMillionMalschnelleristalsdieeinesGehirns.
DasGehirn gleicht diesdurchweitaus mehrSpeicherundVerbindungenaus, selbst imVer-
gleich mit einem High-End-PC, obwohl die größten Supercomputer in einigen Punkten mit
dem Gehirn gleichziehen. Futuristen geben viel auf diese Zahlen und verweisen auf eine
nahende Singularität, bei der Computer ein übermenschliches Leistungsniveau erreichen
(Vinge, 1993; Kurzweil, 2005; Doctorow und Stross, 2012) und sich dann rapide noch wei-
Supercomputer PC MenschlichesGehirn
Recheneinheiten 106GPUs+CPUs 8CPUKerne 106Säulen
1015Transistoren 1010Transistoren 1011Neuronen
Speichereinheiten 1016ByteRAM 1010ByteRAM 1011Neuronen
1017ByteFestplatte 1012ByteFestplatte 1014Synapsen
Zykluszeit 10(cid:2)9s 10(cid:2)9s 10(cid:2)3s
Operationen/s 1018 1010 1017
Abbildung1.2:EingroberVergleichzwischeneinemführendenSupercomputer,Summit(Feldman,2017),einemtypischen
PCimJahr2019unddemmenschlichenGehirn.MenschlicheHirnleistunghatsichinTausendenvonJahrenkaumverändert,
Supercomputerhabensichdagegenstetigverbessert:angefangenbeiMegaFLOPsinden1960erJahren,überGigaFLOPsinden
1980erJahren,TeraFLOPsinden1990erJahren,hinzuPetaFLOPsimJahr2008undExaFLOPsimJahr2018(1ExaFLOPD1018
FloatingPointOperationsperSecond/GleitkommaoperationenproSekunde).
34
--- PAGE 36 ---
1.2 DieGrundlagenderKünstlichenIntelligenz
terverbessern.AberdiereinenZahlenvergleichesindnichtbesondersinformativ.Selbstmit
einemComputervonnahezuunbegrenzterKapazitätsindwirnochweitdavonentfernt,Intel-
ligenz vollständig zu begreifen (siehe Kapitel 28). Grob gesagt: Ohne die richtige Theorie
gebenschnellereMaschineneinfachschnellerdiefalscheAntwort.
1.2.5 Psychologie
(cid:2) WiedenkenundhandelnMenschenundTiere?
Die Ursprünge der wissenschaftlichen Psychologie werden gewöhnlich auf die Arbeit des
deutschen PhysikersHermann vonHelmholtz(1821–1894)undseinesAssistenten Wilhelm
Wundt(1832–1920)zurückgeführt.HelmholtzwandtediewissenschaftlicheMethodeaufdie
UntersuchungdermenschlichenSehkraftan,undseinHandbuchderPhysiologischenOptik
wurde als „die wichtigste Abhandlung über die Physik und Physiologie des menschlichen
Sehens“ bezeichnet (Nalwa, 1993, S.15). Im Jahr 1879 eröffnete Wundt das erste Laborato-
riumfürexperimentellePsychologieanderUniversitätLeipzig.Wundtbestandaufsorgfältig
kontrollierten Experimenten, bei denen seine Mitarbeiter eine Wahrnehmungs- oder Asso-
ziationsaufgabeausführtenunddabeiihreGedankenprozesseselbstbeobachteten(Introspek-
tion).DiesorgfältigenKontrollentrugenvieldazubei,diePsychologiezueinerWissenschaft
zuerheben,dochdiesubjektiveNaturderDatenmachteesunwahrscheinlich,dassdieExpe-
rimentatorenihreeigenenTheorienjemalswiderlegenwürden.
Biologen, die das Verhalten von Tieren untersuchten, verfügten dagegen nicht über intro-
spektiveDatenundentwickelten eineobjektiveMethodik,wiesievonH.S.Jennings(1906)
in seinem einflussreichen Werk Behavior of the Lower Organisms beschrieben wurde. Die
Behaviorismus-Bewegung, angeführt von John Watson (1878–1958), übertrug diese Sicht-
weiseaufdenMenschenundlehntejedeTheorieab,diementaleProzesseeinbezog,mitder
Begründung, dass Introspektion keine zuverlässigen Beweise liefern könne. Die Behavioris-
tenbeharrtendarauf,nurobjektiveMessungenderReize(auchStimulusgenannt),dieaufein
Tier einwirken, sowie die daraus resultierenden Handlungen (Reaktionen) zu untersuchen.
Der Behaviorismus fand zwar viel über Ratten und Tauben heraus, mit dem Verstehen von
MenschenhatteerallerdingswenigerErfolg.
Die kognitive Psychologie, die das Gehirn als einen informationsverarbeitenden Apparat
betrachtet,lässtsichmindestensbiszudenArbeitenvonWilliamJames(1842–1910)zurück-
führen. Auch Helmholtz vertrat die Ansicht, dass die Wahrnehmung eine Form der unbe-
wussten logischen Schlussfolgerung beinhaltete. DiekognitiveSichtweise stand in denVer-
einigten Staaten weitgehend im Schatten des Behaviorismus, doch am Lehrstuhl für Ange-
wandte Psychologie in Cambridge, der von Frederic Bartlett (1886–1969) geleitet wurde,
konnte sich die kognitive Modellierung entfalten. Bartletts Schüler und Nachfolger Ken-
nethCraik(1943)konntemitseinemWerkTheNatureofExplanationdieLegitimitätsolcher
„mentalen“ Begriffe wie Überzeugungen und Ziele mit Nachdruck wiederherstellen, indem
erargumentierte,dasssiegenausowissenschaftlichseienwiebeispielsweisedieVerwendung
vonDruckundTemperaturfürdieBeschreibungvonGas,obwohlGasausMolekülenbesteht,
diewederdieeinenochdieandereEigenschaftbesitzen.
Craikspezifiziertediedreiwichtigsten Schritteeineswissensbasierten Agenten:(1)derReiz
muss in eine interne Repräsentation übersetzt werden, (2) die Repräsentation wird durch
kognitiveProzessemanipuliert,umneueinterneRepräsentationen abzuleiten,und(3)diese
werden wieder zurück in eine Aktion übersetzt. Er erklärte deutlich, warum dies ein gutes
DesignfüreinenAgentenist:
WennderOrganismusein„kleineresModell“deräußerenRealitätundseinereigenenHandlungs-
möglichkeitenimKopfträgt,isterinderLage,verschiedeneAlternativenauszuprobieren,daraus
zuschließen,welchediebestevonihnenist,aufzukünftigeSituationenzureagieren,bevorsie
35
--- PAGE 37 ---
1
Einleitung
entstehen,dasWissenübervergangeneEreignisseimUmgangmitderGegenwartundderZukunft
zunutzenundinjederHinsichtaufeinevielumfassendere,sicherereundkompetentereWeise
aufalleSituationenzureagieren,mitdenenerkonfrontiertist.(Craik,1943)
Nach Craiks Tod durch einen Fahrradunfall im Jahr 1945 wurde seine Arbeit von Donald
Broadbent fortgesetzt, dessen Buch Perception and Communication (1958) eines der ersten
Werkewar,daspsychologischePhänomenealsInformationsverarbeitungmodellierte.Inder
Zwischenzeit führteindenVereinigten Staaten dieEntwicklungderComputermodellierung
zur Entstehungdes Fachbereichs der Kognitionswissenschaft. Man sagt, dieKognitionswis-
senschaft begannaufeinemWorkshopimSeptember1956amMIT–nurzwei Monatenach
derKonferenz,aufderdieKIselbst„geboren“wurde(sieheS.40inAbschnitt1.3.1).
AufdemWorkshopstellteGeorgeMillerTheMagicNumberSevenvor,NoamChomskyprä-
sentierteThreeModelsofLanguageundAllenNewellundHerbertSimonstelltenTheLogic
TheoryMachinevor.Diesedreieinflussreichen Arbeitenzeigten,wieComputermodellever-
wendet werden können, um die Psychologie des Gedächtnisses, der Sprache und des logi-
schen Denkens zu untersuchen. Es ist heute eine verbreitete (wenn auch bei Weitem nicht
universelle) AnsichtunterPsychologen,dass„einekognitiveTheoriewieeinComputerpro-
grammseinsollte“(Anderson,1980)–dasheißt,siesolltedieFunktionsweiseeinerkogniti-
venFunktioninBezugaufdieVerarbeitungvonInformationenbeschreiben.
Für diesen Rückblick werden wir das Gebiet der Mensch-Computer-Interaktion (Human-
ComputerInteraction,HCI)derPsychologiezuordnen.DougEngelbart,einerderPioniereder
HCI,vertrat dieIdeedererweiterten Intelligenz (IntelligenceAugmentation,IA).Erwarder
Meinung,dass Computerdiemenschlichen Fähigkeiten erweitern sollten,anstatt menschli-
cheAufgaben zu automatisieren. ImJahr1968führteEngelbart ineinem Vortrag (derspäter
als „The Mother of All Demos“ bezeichnet wurde) zum ersten Mal die Computermaus, ein
Fenstersystem, Hypertext und Videokonferenzen vor – alles in dem Bemühen zu demons-
trieren,wasmenschlicheWissensträger miteinerPortionerweiterten Intelligenzgemeinsam
erreichenkönnten.
HeutesehenwirIAundKIeheralszweiSeitenderselbenMedaille,wobeiersterediemensch-
licheKontrolleundletztere dasintelligente Verhalten derMaschine betont.Beide sindnot-
wendig,damitMaschinenfürdenMenschennutzbringendsind.
1.2.6 Computertechnik
(cid:2) WiekönnenwireineneffizientenComputerbauen?
Der moderne digitale elektronische Computer wurde unabhängig und fast zeitgleich von
Wissenschaftlern in drei Ländern erfunden, die am Zweiten Weltkrieg beteiligt waren. Der
erstefunktionsfähigeComputerwarderelektromechanischeHeathRobinson14,der1943von
Alan Turings Team für einen einzigen Zweck gebaut wurde: die Entschlüsselung deutscher
Nachrichten.DiegleicheGruppeentwickelte1943denColossus,eineleistungsfähigeUniver-
salmaschine aufderBasisvonVakuumröhren.15 Dererste funktionsfähigeprogrammierbare
Computerwar derZ-3,eineErfindungvonKonrad ZuseinDeutschland imJahr1941.Zuse
erfandaußerdemdieGleitkommazahlenunddieerstehöhereProgrammiersprache, Plankal-
kül.DerersteelektronischeComputer,derABC,wurdevonJohnAtanasoffundseinemDokto-
randenCliffordBerryzwischen1940und1942anderUniversitätvonIowagebaut.Atanasoffs
Forschungen erfuhren wenig Unterstützung oder Anerkennung; es war ENIAC, der im Rah-
14EinekomplexeMaschine,benanntnacheinembritischenCartoonisten,derskurrileundabsurdkomplizierte
LösungenfüralltäglicheAufgabenwiezumBeispieldasBestreicheneinesButterbrotszeichnete.
15Inder Zeit nach dem Weltkrieg wollteTuring dieseComputer fürdie KI-Forschung nutzen – er erstellte
beispielsweiseeinenEntwurfdeserstenSchachprogramms(Turingetal.,1953)–,dochdiebritischeRegierung
blockiertedieseForschung.
36
--- PAGE 38 ---
1.2 DieGrundlagenderKünstlichenIntelligenz
meneinesgeheimenMilitärprojektsanderUniversitätvonPennsylvaniavoneinemTeamum
John Mauchly und J. Presper Eckert entwickelt wurde und der sich als der einflussreichste
VorläuferdermodernenComputererwies.
SeitdemhatjedeGenerationvonComputerhardwareeineSteigerunghinsichtlichGeschwin-
digkeitundKapazitätsowieeinenRückganghinsichtlichdesPreisesmitsichgebracht–ein
Trend, der im Moore’schen Gesetz festgehalten ist. Bis etwa ins Jahr 2005 verdoppelte sich
dieLeistungetwaalle18Monate.AbdannbegannendieHerstelleraufgrundvonProblemen
mit derVerlustleistung dieAnzahl der CPU-Kerne anstatt dieTaktrate zu erhöhen. ZurZeit
gehtmandavonaus,dasskünftigeSteigerungenderFunktionalitätdurchmassiveParallelität
erreicht werden – eine merkwürdige Übereinstimmung mit den Eigenschaften des Gehirns.
WirerlebenauchneueHardwaredesigns, dieaufderIdeebasieren,dasswirimUmgangmit
einer unsicheren Welt keine 64-Bit-Präzision in unseren Zahlen benötigen; 16 Bit (wie im
bfloat16-Format)16 oder sogar 8 Bit reichen aus und ermöglichen eine schnellere Verarbei-
tung.
Wir stehen gerade am Anfang einer Hardwareentwicklung, die auf KI-Anwendungen abge-
stimmt ist, wie z.B. Grafikprozessoren (Graphics Processing Unit, GPU), Tensorprozessoren
(Tensor Processing Unit, TPU) oder die Wafer Scale Engine (WSE). Von den 1960er Jahren
bis etwa ins Jahr 2012 folgte die Höhe der Rechenleistung, die zum Trainieren der Top-
Anwendungen für das maschinelle Lernen verwendet wurde, dem Moore’schen Gesetz. Ab
2012ändertesichdieLage:Von2012bis2018gabeseinen300.000-fachenAnstieg,wasunge-
fähreinerVerdoppelungalle100Tageentspricht(AmodeiundHernandez,2018).EinModell
des maschinellen Lernens, das 2014 einen ganzen Tag zum Trainieren benötigte, brauchte
2018 nurnoch zwei Minuten (Ying et al.,2018).Obwohl aktuell noch nicht praxistauglich,
versprechen Quantencomputer noch weitaus größere Beschleunigungen für einige wichtige
UnterklassenvonKI-Algorithmen.
Natürlich gab esauch vordem elektronischen Computerschon Rechengeräte. Diefrühesten
automatisiertenMaschinenausdem17.JahrhundertwurdenbereitsaufS.27angesprochen.
DieersteprogrammierbareMaschinewareinWebstuhl,der1805vonJosephMarieJacquard
(1752–1834) entwickelt wurde und der Anweisungen für das zu webende Muster mithilfe
vonLochkartenspeicherte.
Mitte des 19. Jahrhunderts entwarf Charles Babbage (1792–1871) zwei Rechenmaschinen,
die er aber beide nicht fertigstellte. Seine Differenzmaschine („Difference Engine“) sollte
mathematischeTabellenfürtechnischeundwissenschaftlicheProjekteberechnen.Siewurde
schließlichzwischen1989und1991nachgebautunderwiessichalsfunktionstüchtig(Swade,
2000). Babbages „Analytical Engine“ war weitaus ambitionierter: Sie verfügte über einen
adressierbaren Speicher, gespeicherte Programme, die auf Jacquards Lochkarten basierten,
sowie über bedingte Sprünge. Sie war die erste Maschine, die universelle Berechnungen
durchführenkonnte.
Babbages Kollegin Ada Lovelace, Tochter des Dichters Lord Byron, verstand das Potenzial
der Analytical Engine und beschrieb sie als „eine denkende oder ... eine schlussfolgernde
Maschine“, diein derLage sei, über„alle Themen imUniversum“ zu räsonieren (Lovelace,
1843).SienahmauchdenHype-ZyklusderKIvorweg,indemsieschrieb:„Esistwünschens-
wert, sich vor der Möglichkeit übertriebener Ideen zu schützen, die hinsichtlich der Leis-
tungen derAnalytical Engineentstehen könnten.“Leidergerieten Babbages Maschinen und
LovelacesIdeenweitgehendinVergessenheit.
DieKIistauchderSoftwareseitederInformatiksehrzuDankverpflichtet,vonderBetriebs-
systeme, Programmiersprachen und Werkzeuge bereitgestellt wurden, die zur Entwicklung
modernerProgramme(undderentsprechendenVeröffentlichungdazu)nötigsind.Dochhier
16Brainfloatingpointwith16bits.
37
--- PAGE 39 ---
1
Einleitung
wurde dieSchuld beglichen: Die Tätigkeiten im Bereich der KI haben viele Ideen hervorge-
bracht,dieihrenWegzurückindieMainstream-Informatikgefundenhaben,darunterTime-
Sharing,interaktiveInterpreter,PCsmitFensternundMäusen,schnelleEntwicklungsumge-
bungen, derDatentyp derverknüpften Liste, automatische Speicherverwaltung und Schlüs-
selkonzepte der symbolischen, funktionalen, deklarativen und objektorientierten Program-
mierung.
1.2.7 Kontrolltheorie undKybernetik
(cid:2) WiekönnenArtefakteuntereigenerSteuerungarbeiten?
KtesibiosvonAlexandria(ca.250v.Chr.)bautedieersteselbststeuerndeMaschine:eineWas-
seruhr mit einem Regler, der eine konstante Durchflussrate aufrechterhielt. Diese Erfindung
verändertedieDefinitiondessen,waseinArtefakttunkonnte.BisdahinkonntennurLebewe-
senihrVerhaltenalsReaktionaufVeränderungeninderUmweltanpassen.AndereBeispiele
fürselbstregulierenderückgekoppelteSteuerungssystemesindu.a.derDampfmaschinenreg-
ler vonJames Watt (1736–1819)undder Thermostat vonCornelisDrebbel (1572–1633),der
auchdasU-Booterfand.JamesClerkMaxwell(1868)begründetediemathematischeTheorie
derRegelsysteme.
EinezentraleFigurderNachkriegszeit beiderEntwicklungderKontrolltheoriewarNorbert
Wiener (1894–1964).Wiener war ein brillanter Mathematiker, der u.a. mit Bertrand Russell
zusammenarbeitete, bevorer anfing, sich fürbiologische undmechanische Kontrollsysteme
undderen VerbindungzurKognitionzu interessieren. WieCraik (derebenfalls Kontrollsys-
teme als psychologische Modelle verwendete) stellten Wiener und seine Kollegen Arturo
RosenbluethundJulianBigelowdiebehavioristischeOrthodoxieinfrage(Rosenbluethetal.,
1943).Siebetrachteten zweckgerichtetes Handeln alsFolgeeines Regulationsmechanismus,
durchdenversuchtwird,„Fehler“–dieDifferenzzwischendemaktuellenZustandunddem
Zielzustand–zuminimieren.Endeder1940erJahreorganisierteWienerzusammenmitWar-
renMcCulloch,WalterPittsundJohnvonNeumanneineReiheeinflussreicherKonferenzen,
diedieneuenmathematischenundcomputergestütztenModellederKognitionuntersuchten.
WienersBuchCybernetics(1948)wurdeeinBestsellerundmachtedieÖffentlichkeitaufdie
Möglichkeitaufmerksam,MaschinenmitkünstlicherIntelligenzauszustatten.
InderZwischenzeitbereiteteW.RossAshbyimVereinigtenKönigreichdenWegfürähnliche
Ideen(Ashby,1940).Ashby,AlanTuring,GreyWalterundanderegründetendenRatioClub
für„diejenigen,dieWienersIdeenhatten,bevorWienersBucherschien“.InDesignforaBrain
(1948, 1952) führte Ashby seine Idee weiter aus, dass Intelligenz mithilfe homöostatischer
Geräte erzeugt werden kann, in denen mit geeigneten Rückkopplungsschleifen ein stabiles
adaptivesVerhaltenerreichtwird.
DiemoderneKontrolltheorie,insbesonderederalsstochastischeoptimaleSteuerungbekann-
te Zweig, hat das Ziel, Systeme zu entwerfen, die eine Kostenfunktion über die Zeit mini-
mieren.DiesentsprichtinetwademStandardmodellderKI:Systemezuentwerfen,diesich
optimal verhalten. Warum also sind KI und Kontrolltheorie zwei unterschiedliche Gebiete,
trotz der engen Verbindungen zwischen ihren Begründern? Die Antwort liegt in der engen
KopplungzwischendenmathematischenTechniken,diedenBeteiligtenvertrautwaren,und
denentsprechendenProblemstellungen,dieinjederWeltsichtenthaltenwaren.Analysisund
Matrizenalgebra,dieWerkzeugederKontrolltheorie,eignensichfürSysteme,diedurchfeste
Mengen kontinuierlicher Variablen beschreibbar sind, während KI zum Teil als eine Mög-
lichkeit gegründet wurde, diesen wahrgenommenen Einschränkungen zu entkommen. Die
Werkzeuge der logischen Inferenz und der Berechnung erlaubten es den KI-Forschern, Pro-
blemewieSprache,SehenundsymbolischePlanungzubetrachten,dievölligaußerhalbdes
AufgabenbereichsderKontrolltheoretikerlagen.
38
--- PAGE 40 ---
1.3 DieGeschichtederKünstlichenIntelligenz
1.2.8 Linguistik
(cid:2) WiehängenSpracheundDenkenzusammen?
1957veröffentlichteB.F.SkinnerseinBuchVerbalBehavior –eineumfassende,detaillierte
DarstellungdesbehavioristischenAnsatzeszumSpracherwerb,geschriebenvondemführen-
den Experten auf diesem Gebiet. Doch seltsamerweise wurde eine Rezension des Buchs so
bekanntwiedasBuchselbstundbrachtedasInteresseamBehaviorismusfastzumErliegen.
DerAutorderKritikwarderLinguistNoamChomsky,dergeradeeinBuchüberseineeigene
Theorie, SyntacticStructures, veröffentlichthatte. Chomskywiesdarauf hin,dassdiebeha-
vioristischeTheorienichtaufdenBegriffderKreativitätinderSpracheeinging–sieerklärte
nicht,wieKinderSätzeverstehenundsichausdenkenkonnten,diesienochniezuvorgehört
hatten. Chomskys Theorie – basierend auf syntaktischen Modellen, die auf den indischen
Linguisten Panini(ca. 350v.Chr.)zurückgehen –konntedieserklären undimGegensatz zu
früherenTheorienwarsieformalgenug,dasssieimPrinzipprogrammiertwerdenkonnte.
DiemoderneLinguistikunddieKIwurdenalsoetwazurgleichenZeit„geboren“undwuch-
sen zusammen auf,miteinerSchnittmengeineinemhybridenBereich, der Computerlingu-
istik oder natürlichen Sprachverarbeitung. Das Problem, Sprache zu verstehen, stellte sich
alswesentlichkomplexerheraus,alses1957denAnscheinhatte.DasVerstehenvonSprache
erforderteinBegreifendesThemasunddesKontexts,nichtnurderStrukturvonSätzen.Dies
mag offensichtlich erscheinen, war aber bis in die 1960er Jahre nicht allgemein anerkannt.
Ein Großteil der frühen Arbeiten im Bereich der Wissensrepräsentation (das Studium dar-
über, wieWissen ineineFormgebracht wird, mitderein Computerumgehen kann)war an
Sprache gebunden undwurdedurchdielinguistische Forschungunterstützt, diewiederum
durchjahrzehntelangeArbeitmitderphilosophischenAnalysevonSpracheverbundenwar.
1.3 Die Geschichte der Künstlichen Intelligenz
Eine schnelle Möglichkeit, einen Überblick über die Meilensteine der KI-Geschichte zu
bekommen, ist die Auflistung der Preisträger des Turing Awards: Marvin Minsky (1969)
und John McCarthy (1971) für die Definition der Grundlagen des Forschungsgebiets auf
der Basis von Repräsentation und Schlussfolgerungen; Allen Newell und Herbert Simon
(1975)fürsymbolischeModellederProblemlösungundmenschlicherKognition;EdFeigen-
baum und Raj Reddy (1994) für die Entwicklung von Expertensystemen, die menschliches
Wissen codieren, um reale Probleme zu lösen; Judea Pearl (2011) für die Entwicklung pro-
babilistischer Schlussfolgerungstechniken, die mit Unsicherheiten auf grundlegende Weise
umgehen; und schließlich Yoshua Bengio, Geoffrey Hinton und Yann LeCun (2019), die
„Deep Learning“ (mehrschichtige neuronale Netze) zu einem wichtigen Bestandteil der
modernen Datenverarbeitung gemacht haben. Im weiteren Verlauf dieses Abschnitts gehen
wirnäheraufdieeinzelnenPhasenderKI-Geschichteein.
1.3.1 Die Anfänge der KünstlichenIntelligenz (1943–1956)
Die erste Arbeit, dieheuteallgemein als KIanerkannt wird,stammt vonWarren McCulloch
und Walter Pitts (1943). Inspiriert von der mathematischen Modellierungsarbeit von Pitts’
Berater Nicolas Rashevsky (1936, 1938), basiert sie auf drei Quellen: dem Wissen über die
grundlegende Physiologie und die Funktionvon Neuronen im Gehirn; einer formalen Ana-
lyse der Aussagenlogik, die auf Russell und Whitehead zurückgeht; sowie Turings Theorie
der Berechenbarkeit. Sie schlugen ein Modell künstlicherNeuronen vor, in dem jedes Neu-
ron als „an“ oder „aus“ charakterisiert wird, wobei ein Wechsel auf „an“ als Reaktion auf
dieAktivierungdurcheineausreichendeAnzahlbenachbarterNeuronenerfolgt.DerZustand
einesNeuronswurdeaufgefasstals„faktischäquivalentzueinerlogischenAussage,dieihren
39
--- PAGE 41 ---
1
Einleitung
adäquaten Reiz darstellt“. Siezeigten zumBeispiel, dass jedeberechenbare Funktiondurch
ein Netzwerk verbundener Neuronen berechnet werden kann und dass alle logischen Ver-
knüpfungen(UND, ODER, NICHTusw.) durch einfache Netzstrukturen implementiert werden
können.McCullochundPittsbehauptetenaußerdem,dassgeeignetdefinierteNetzelernfähig
seien.DonaldHebb(1949)demonstrierteeineeinfacheAktualisierungsregel, mitderenHilfe
dieVerbindungsstärkenzwischenNeuronenverändertwerdenkönnen.SeineRegel,heuteals
Hebb’schesLernenbezeichnet,istimmernocheineinflussreichesModell.
Zwei Studenten in Harvard, Marvin Minsky (1927–2016)und Dean Edmonds, bauten 1950
den ersten neuronalen Netzcomputer – SNARC. SNARC verwendete 3.000 Vakuumröhren
und einen nicht mehr benötigten Autopilotmechanismus aus einem B-24-Bomber, um ein
Netzwerk aus 40 Neuronen zu simulieren. Minsky studierte später in Princeton universelle
BerechnungeninneuronalenNetzwerken.SeinPromotionsausschusswarskeptisch,obdiese
Art von Arbeit als Mathematik angesehen werden konnte, doch von Neumann soll gesagt
haben:„Wennesheutenochnichtsoist,dannwirdeseinesTagessosein.“
Es gab eine Reihe weiterer Beispiele für frühe Arbeiten, die als KI charakterisiert werden
können,darunterzweiDameprogramme,die1952unabhängigvoneinandervonChristopher
StracheyanderUniversitätvonManchesterundvonArthurSamuelbeiIBMentwickeltwur-
den. Am einflussreichsten war jedoch die Vision von Alan Turing. Bereits 1947 hielt er bei
derLondonMathematicalSocietyVorträgezudiesemThemaundformulierte1950inseinem
Artikel„ComputingMachineryandIntelligence“eineüberzeugendeAgenda.Darinstellteer
denTuring-Test, maschinelles Lernen,genetische AlgorithmenundReinforcement Learning
vor.Erging auf vieleder Einwände ein, die gegen dieMöglichkeit von KI erhoben wurden,
waswirinKapitel27nochausführenwerden.Außerdemvermuteteer,eineKIaufmensch-
lichem Niveau zu schaffen wäre einfacher, wenn man Lernalgorithmen entwickelt und mit
diesen dann dieMaschine trainiert, anstatt ihreIntelligenz vonHand zu programmieren. In
späteren Vorträgen warnte er, dass dasErreichen dieses Ziels vielleicht nicht vorteilhaft für
dieMenschheitwäre.
ImJahr1955überzeugteJohnMcCarthyvomDartmouthCollegeseineKollegenMarvinMin-
sky,Claude ShannonundNathaniel Rochester, ihmdabei zu helfen, Forscheraus denUSA
zusammenzubringen, dieanderAutomatentheorie, anneuronalenNetzen undderUntersu-
chungvonIntelligenz interessiert waren. Sieorganisierten im Sommer1956einen zweimo-
natigen Workshop in Dartmouth. Insgesamt nahmen 10 Personen daran teil, darunter Allen
Newell undHerbert Simonvonder Carnegie Tech17, Trenchard Moreaus Princeton, Arthur
SamuelvonIBMsowieRaySolomonoffundOliverSelfridgevomMIT.ImAntragheißtes:18
Wirschlagenvor,imSommer1956amDartmouthCollegeinHanover,NewHampshire,
einezweimonatigeStudiemit10PersonenzurkünstlichenIntelligenzdurchzuführen.
DieStudiesollaufgrundderVermutungstattfinden,dassjederAspektdesLernensoder
jedes andere Merkmal der Intelligenz im Prinzip so genau beschrieben werden kann,
dass eine Maschine gebaut werden kann, die dies simuliert. Es soll versucht werden
herauszufinden,wiemanMaschinendazubringt,Sprachezubenutzen,Abstraktionen
undKonzeptezubilden,Problemezulösen,dieheutedemMenschenvorbehaltensind,
undsichselbstzuverbessern. Wirglauben,dasseinsignifikanterFortschrittineinem
odermehrerendieserProblemeerzieltwerdenkann,wenneinesorgfältigausgewählte
GruppevonWissenschaftlerneinenSommerlanggemeinsamdaranarbeitet.
17HeuteCarnegieMellonUniversity(CMU).
18DieswardieersteoffizielleVerwendungvonMcCarthysBegriffderkünstlichenIntelligenz.Vielleichtwäre
„berechenbareRationalität“präziserundwenigerbedrohlichgewesen,doch„KI“istgeblieben.Anlässlichdes
50.JahrestagsderDartmouth-KonferenzerklärteMcCarthy,dasserdieBegriffe„Computer“oder„berechenbar“
ausRücksichtaufNorbertWienervermiedenhatte,dersichmitanalogenkybernetischenGerätenundnichtmit
digitalenComputernbeschäftigte.
40
--- PAGE 42 ---
1.3 DieGeschichtederKünstlichenIntelligenz
Dieser optimistischen Vorhersage zum Trotz führte der Workshop in Dartmouth zu keinem
Durchbruch.NewellundSimonpräsentiertendievielleichtamweitestenausgereifteArbeit,
einen mathematischen Theorembeweiser namens Logic Theorist (LT). Simon behauptete:
„Wir haben ein Computerprogramm entworfen, dasin derLage ist, nichtnumerisch zu den-
ken, und haben damit das altehrwürdige Körper-Geist-Problem gelöst.“19 Kurz nach dem
WorkshopwardasProgramminderLage,diemeistenderTheoremeinKapitel2derPrinci-
piaMathematicavonRussellundWhiteheadzubeweisen.Russellsollerfreutgewesensein,
alsererfuhr,dassderLTeinenBeweisfüreinTheoremgefundenhatte,derkürzeralsderin
derPrincipiawar.DieRedakteuredesJournalofSymbolicLogicwarenwenigerbeeindruckt–
sielehnteneinenAufsatzab,dervonNewell,SimonunddemLogicTheoristverfasstwurde.
1.3.2 Früher Enthusiasmus,große Erwartungen(1952–1969)
DasintellektuelleEstablishmentder1950erJahrezogesimGroßenundGanzenvorzuglau-
ben,dass„eineMaschineniemalsX tunkann“.(InKapitel27findenSieeinelangeListeder
vonTuringzusammengetragenen Xe.)DieAntwort derKI-Forscherbestand natürlichdarin,
einX nachdemanderenvorzuführen.DabeikonzentriertensiesichinsbesondereaufAufga-
ben, die als Indikator für die Intelligenz von Menschen gelten, zum Beispiel Spiele, Rätsel,
Mathematik und IQ-Tests. John McCarthy bezeichnete diese Periode als die „Look, Ma, no
hands!“-Ära(„Mama,guckmal,freihändig!“).
Newell undSimonknüpftenanihrenLT-ErfolgmitdemGeneral ProblemSolver,kurzGPS,
an. Anders als der LT war dieses Programm von Anfang an darauf ausgelegt, menschliche
Protokolle zur Problemlösung zu imitieren. Innerhalb der begrenzten Klasse logischer Rät-
sel, dieerbearbeiten konnte,stelltesich heraus, dass dieReihenfolge, inderdasProgramm
Teilziele undmöglicheAktioneninBetracht zog, derReihenfolge ähnelte, in derMenschen
dieselbeArtProblemeangehen.DamitwarderGPSwahrscheinlichdasersteProgramm,das
denAnsatzdes„menschlichenDenkens“verkörperte.DerErfolgvonGPSundnachfolgenden
ProgrammenalsModellederKognitionveranlassteNewellundSimon(1976),ihreberühmte
Hypothese des physischen Symbolsystemszu formulieren, die besagt, dass „ein physisches
Symbolsystem die notwendigen und hinreichenden Mittel für allgemein intelligentes Han-
delnbesitzt“.Siemeintendamit,dassjedesSystem(MenschoderMaschine),dasIntelligenz
zeigt, durchdieManipulationvonDatenstrukturen,dieausSymbolenbestehen,funktionie-
renmuss.Wirwerdenspäternochsehen,dassdieseHypotheseausvielenRichtungeninfrage
gestelltwurde.
Bei IBM erstellten Nathaniel Rochesterundseine Kollegen einige derersten KI-Programme.
Herbert Gelernter (1959) konstruierte den Geometry Theorem Prover, der in der Lage war,
Theoreme zu beweisen, die viele Mathematikstudenten als zu knifflig empfunden hätten.
DieseArbeitwareineVorstufedermodernenmathematischenTheorembeweiser.
Von allen Forschungsarbeiten dieser Zeit war die von Arthur Samuel zum Damespiel ver-
mutlich langfristig die einflussreichste. Unter Verwendung von Methoden, die wir heute
als Reinforcement Learning bezeichnen (siehe Kapitel 22), lernten SamuelsProgramme, auf
hohem Amateurniveau zu spielen. Damit widerlegte er die Vorstellung, dass Computer nur
das tun können, was man ihnen sagt: Sein Programm lernte schnell, besser zu spielen als
seinSchöpfer.DasProgrammwurde1956imFernsehenvorgeführtundhinterließeinenstar-
kenEindruck.WieTuringhatteauchSamuelSchwierigkeiten, Rechenzeit zubekommen.Er
arbeitete nachtsundbenutzteMaschinen, diesichbeiIBMnochinderTestphasebefanden.
SamuelsProgrammwarderVorläuferspätererSystemewieTD-GAMMON(Tesauro,1992),das
19UmdenLTzuschreiben,erfandenNewellundSimonaußerdemeinelistenverarbeitendeSprache,IPL.Sie
hattenkeinenCompilerundübersetztensievonHandinMaschinencode.UmFehlerzuvermeiden,arbeiteten
sieparallelundriefensichbeimSchreibenjederAnweisunggegenseitigBinärzahlenzu,umsicherzustellen,
dasssieübereinstimmten.
41
--- PAGE 43 ---
1
Einleitung
zudenweltbestenBackgammon-Spielerngehörte,undALPHAGO(Silveretal.,2016),dasdie
Weltschockierte,alsesdenmenschlichenWeltmeisterimGobesiegte(sieheKapitel5).
Im Jahr 1958 leistete John McCarthy zwei wichtige Beiträge zur KI. Im „MIT AI ab Memo
No. 1“ definierte er die höhere Programmiersprache Lisp, die für die nächsten 30 Jahre die
dominierende KI-Programmiersprache werden sollte. In einem Aufsatz mit dem Titel Pro-
grams with Common Sense („Programme mit gesundem Menschenverstand“) unterbreitete
ereinen konzeptionellen Vorschlag fürKI-Systeme, dieauf Wissen undSchlussfolgerungen
basieren. McCarthy beschreibt in dieser Arbeit den Advice Taker, ein hypothetisches Pro-
gramm, das Allgemeinwissen über die Welt verkörpern würde und daraus Handlungspläne
ableiten könnte.DasKonzept wurdemiteinfachen logischen Axiomenveranschaulicht, die
ausreichen, um einen Plan für die Fahrt zum Flughafen zu generieren. Das Programm war
außerdemsokonzipiert,dassesimnormalenBetriebneueAxiomeakzeptierenundsoKom-
petenzinneuenBereichen erlangen konnte,ohneneuprogrammiertzuwerden.DerAdvice
TakerverkörpertedamitdiezentralenPrinzipienderWissensrepräsentationunddesSchluss-
folgerns:dassessinnvollist,eineformale,expliziteRepräsentationderWeltundihrerFunkti-
onsweisezuhabensowieinderLagezusein,dieseRepräsentationmitdeduktivenVerfahren
zuverändern.DerArtikelbeeinflusstedenVerlaufderKIundistbisheuterelevant.
1958warauchdasJahr,indemMarvinMinskyansMITwechselte.SeineanfänglicheZusam-
menarbeitmitMcCarthywarjedochnichtvonDauer.McCarthykonzentriertesichaufReprä-
sentation und Schlussfolgern in formaler Logik, Minsky hingegen war mehr daran inter-
essiert, Programme zum Laufen zu bringen – er entwickelte schließlich eine antilogische
Anschauung.ImJahr1963gründeteMcCarthydasAILabinStanford.SeinPlan,mithilfevon
LogikdenultimativenAdviceTakerzubauen,wurdedurchJ.A.RobinsonsEntdeckungdes
Resolutionsverfahrens(einvollständigerAlgorithmuszumBeweisenvonTheoremenderPrä-
dikatenlogik,sieheKapitel9)imJahr1965vorangetrieben.DieArbeiteninStanfordbetonten
allgemeineMethodenfürlogischesSchlussfolgern.ZudenAnwendungenderLogikgehörten
CordellGreensFrage-Antwort-SystemeundPlanungssysteme(Green,1969b)unddasShakey-
RobotikprojektamStanfordResearchInstitute(SRI).DasShakey-Projekt,aufdaswirinKapi-
tel26nähereingehen,wardaserste,dasdievollständigeIntegrationvonlogischemSchluss-
folgernundphysischerAktivitätdemonstrierte.
Am MIT betreute Minsky eine Reihe von Studenten, die Aufgaben aus einem begrenzten
Problembereichauswählten, derenLösungIntelligenzzuerfordernschien.Diesebegrenzten
DomänenwurdenunterdemNamenMikroweltenbekannt.DasProgrammSAINTvonJames
Slagle(1963)warinderLage,geschlosseneIntegrationsproblemezulösen,dietypischerweise
in Kursen des ersten Grundstudiumsjahrs vorkommen. Das Programm ANALOGY von Tom
Evans (1968) löste geometrische Analogieprobleme, die in IQ-Tests vorkommen. STUDENT
vonDanielBobrow(1967)lösteAlgebra-Textaufgaben,wiez.B.diefolgende:
WenndieAnzahlderKunden,dieTomhat,doppeltsogroßistwiedasQuadratvon20Prozentder
Anzeigen,dieTomaufgegebenhat,unddieAnzahlderAnzeigen45beträgt–wievieleKunden
hatTomdann?
DiebekanntesteMikroweltistdieBlockwelt,dieauseinerReihevonfestenBlöckenbesteht,
die auf einer Tischplatte (oder häufiger auf der Simulation einer Tischplatte) platziert sind,
wieinIAbbildung1.3gezeigt.EinetypischeAufgabeindieserWeltbestehtdarin,dieBlöcke
ineinerbestimmtenWeiseumzuordnen,wozueineRoboterhandverwendetwird,diejeweils
einenBlockaufhebenkann.DieBlockweltwarAusgangspunktdesVision-ProjektsvonDavid
Huffman(1971),derVisions-undConstraint-Propagation-ArbeitvonDavidWaltz(1975),der
TheoriedesLernensvonPatrickWinston(1970),desProgrammszumVerstehen natürlicher
SprachevonTerryWinograd(1972)unddesPlanersvonScottFahlman(1974).
Frühe Arbeiten, die auf den neuronalen Netzen von McCulloch und Pitts aufbauten, erleb-
tenebenfallseineBlütezeit.DieArbeitvonShmuelWinogradundJackCowan(1963)zeigte,
42
--- PAGE 44 ---
1.3 DieGeschichtederKünstlichenIntelligenz
Abbildung1.3:EineSzeneausderBlockwelt.SHRDLU(Winograd,1972)hatsoebendenBefehl„FindeeinenBlock,der
größeristalsderjenige,dendugeradehältst,undlegeihnindenKasten“verarbeitet.
wie eine große Anzahl von Elementen kollektiv ein individuelles Konzept repräsentieren
konnten,miteinerentsprechendenSteigerunganRobustheitundParallelität.HebbsLernme-
thodenwurdenvonBernieWidrow(WidrowundHoff,1960;Widrow,1962),derseineNetze
als Adalinesbezeichnete, undvonFrank Rosenblatt (1962)mitseinem Perzeptron-Konzept
weiterentwickelt. Das Perzeptron-Konvergenz-Theorem (Block et al.,1962)besagt, dass der
LernalgorithmusdieVerbindungsstärkeneinesPerzeptronsanbeliebigeEingabedatenanpas-
senkann,soferneinesolcheAnpassungexistiert.
1.3.3 Eine PortionRealität (1966–1973)
DieKI-ForscherscheutensichvonAnfangannicht,ihrekommendenErfolgevorherzusagen.
DiefolgendeAussagevonHerbertSimonausdemJahr1957wirdoftzitiert:
Esliegtmirnichtdaran,Siezuüberraschenoderzuschockieren–dochumesmöglichsteinfach
und kurz auszudrücken: Es gibt heute in der Welt Maschinen, die denken, die lernen und die
kreativsind.DarüberhinauswirdihreFähigkeit,dieszutun,schnellzunehmen,bis–inabseh-
barerZukunft–dieBandbreitederProbleme,diesiebewältigenkönnen,genausogroßseinwird
wiedieBereiche,diedermenschlicheVerstandbewältigenkann.
DerAusdruck„absehbareZukunft“istzwarvage,aberSimonmachteauchkonkretereVorher-
sagen:dassinnerhalbvon10JahreneinComputerSchachweltmeisterseinundeinbedeuten-
des mathematisches Theorem maschinell bewiesen sein würde. Diese Vorhersagen bewahr-
heiteten sich tatsächlich (zumindest annähernd), allerdings innerhalb von 40 Jahren und
nicht von 10 Jahren. Simons übersteigerte Zuversicht war auf die vielversprechende Leis-
tungfrüherKI-SystemefüreinfacheBeispielezurückzuführen.InfastallenFällenversagten
diesefrühenSystemejedochbeischwierigerenProblemen.
FürdiesesScheiterngabeszweiHauptgründe.Dererstewar,dassvielefrüheKI-Systemein
erster Linieauf „informierter Introspektion“ basierten, wie Menschen eine Aufgabe ausfüh-
ren,anstattdarauf,dieAufgabesorgfältigzuanalysieren; herauszufinden,wiedieLösungen
43
--- PAGE 45 ---
1
Einleitung
beschaffenseinmuss;undwaseinAlgorithmustunmüsste,umsolcheLösungenzuverlässig
zuproduzieren.
Der zweite Grund für das Scheitern war das mangelnde Bewusstsein für das Komplexitäts-
verhalten der Probleme, die die KI zu lösen versuchte, das heißt, wie sich die Laufzeit mit
der Größe der Eingabemenge ändert (die effiziente Machbarkeit). In den meisten der frühen
SystemezumautomatischenProblemlösenwurdenverschiedeneSchrittkombinationenaus-
probiert, bis die Lösung gefunden war. Diese Strategie funktionierte anfangs, weil Mikro-
welten nur sehr wenige Objekte und damit nur wenige mögliche Aktionen und sehr kurze
Lösungsfolgenenthielten.BevordieKomplexitätstheorieentwickeltwurde,warmanweithin
derMeinung,dassdie„Skalierung“aufgrößereProblemeeinfacheineFragevonschnellerer
I HardwareundgrößeremSpeichersei.DerOptimismus,derzumBeispieldieEntwicklungdes
TheorembeweisensmittelsResolutionbegleitete,wurdebaldgedämpft,alsesdenForschern
nicht gelang, Theoreme mit mehr als ein paar Dutzend Fakten zu beweisen. Die Tatsache,
dasseinProgrammprinzipielleineLösungfindenkann,bedeutetnicht,dassdasProgramm
irgendeinen Mechanismusenthält,dernötigist, umdieseLösungauch inderPraxiszu fin-
den.
Die Illusion unbegrenzter Rechenleistung war nicht auf Problemlösungsprogramme be-
schränkt. FrüheExperimenteinderMaschinenevolution(heutealsgenetische Algorithmen
bezeichnet; Friedberg, 1958; Friedberg et al., 1959) basierten auf der zweifellos richtigen
Überzeugung, dass man durch eine geeignete Reihe kleiner Mutationen im Maschinencode
ein effizientes Programm für jede konkrete Aufgabe erzeugen kann. Die Idee war, zufällige
Mutationen auszuprobieren und dabei einen Selektionsprozess zu verwenden, der nützlich
erscheinendeMutationenbeibehielt.TrotzTausendervonStundenCPU-Zeitkonntefastkein
Fortschrittnachgewiesenwerden.
DasVersäumnis,die„kombinatorischeExplosion“indenGriffzubekommen,wareinerder
HauptkritikpunkteanderKIimLighthill-Bericht(Lighthill,1973),aufdessenGrundlagedie
britischeRegierung entschied, dieUnterstützungderKI-Forschunginallenaußerzwei Uni-
versitäten einzustellen. (Die mündliche Überlieferung zeichnet ein etwas anderes und bun-
teresBild,mitpolitischenAmbitionenundpersönlichenAnimositäten,diehiernichtweiter
ausgeführtwerdensollen.)
EinedritteSchwierigkeitergabsichauseinigenfundamentalenEinschränkungendergrund-
legenden Strukturen, die zur Erzeugung intelligenten Verhaltens verwendet werden. Zum
Beispiel bewiesen MinskyundPapertinihremBuch Perceptrons(1969),dassPerzeptronen
(eine einfache Form eines neuronalen Netzes) zwar nachweislich alles lernen können, was
sieauchdarstellenkönnen,abersiekönnennurrechtwenigdarstellen.Insbesonderekonnte
ein Perzeptron mit zwei Eingängen nicht darauf trainiert werden zu erkennen, wann die
Werte an den Eingängen unterschiedlich waren. Obwohl ihre Ergebnisse nicht auf komple-
xere,mehrschichtigeNetzwerkezutrafen,wurdendieForschungsgelderfürneuronaleNetze
baldfastaufnullzurückgefahren.IronischerweisewarenausgerechnetdieneuenRückwärts-
propagation-Lernalgorithmen,dieindenspäten1980erJahrenunddannnocheinmalinden
2010erJahren füreineenormeWiederbelebung derNeuronale-Netze-Forschung sorgen soll-
ten, bereits in den frühen 1960er Jahren in anderen Zusammenhängen entwickelt worden
(Kelley,1960;Bryson,1962).
1.3.4 Expertensysteme(1969–1986)
Das Bild des Problemlösens, das sich im ersten Jahrzehnt der KI-Forschung herausgebildet
hatte,wardaseinesuniversellenSuchmechanismus,derversucht,elementareSchlussfolge-
rungsschritteaneinanderzureihen,umvollständigeLösungenzufinden.SolcheAnsätzewur-
den als schwache Methoden bezeichnet, weil sie zwar allgemein sind, aber nicht auf große
oder schwierige Probleminstanzen skaliert werden können. Die Alternative zu schwachen
44
--- PAGE 46 ---
1.3 DieGeschichtederKünstlichenIntelligenz
Methoden besteht darin, leistungsfähigeres, fachbereichspezifisches Wissen zu verwenden,
dasgrößereSchlussfolgerungsschritteerlaubtundFälle,dietypischerweiseineingeschränk-
terenFachgebietenauftreten,leichterbehandelnkann.Mankönntesagen,dassman,umein
schweresProblemzulösen,dieAntwortfastschonkennenmuss.
Das Programm DENDRAL (Buchanan et al., 1969) war ein frühes Beispiel fürdiesen Ansatz.
Es wurde in Stanford entwickelt, wo sich Ed Feigenbaum (ein ehemaliger Student von
Herbert Simon), Bruce Buchanan (ein Philosoph, der zum Informatiker wurde) und Joshua
Lederberg (ein Genetiker und Nobelpreisträger) zusammenschlossen, um das Problem der
Ableitung molekularer Strukturen aus den Informationen eines Massenspektrometers zu
lösen.DieEingabefürdasProgrammbestehtausderelementarenFormeldesMoleküls(z.B.
C H NO ) und dem Massenspektrum, das die Massen der verschiedenen Fragmente des
6 13 2
Molekülsangibt,diebeimBeschussmiteinemElektronenstrahlentstehen.DasMassenspek-
trum könntez.B. einen Peak beim D 15 enthalten, was derMasse eines Methyl-Fragments
(CH )entspricht.
3
Die naive Version des Programms generierte alle möglichen Strukturen, die mit der Formel
übereinstimmten, sagte dann voraus,welches Massenspektrum fürjedeStrukturbeobachtet
werden würde, und verglich dies mit dem tatsächlichen Spektrum. Wie zu erwarten war,
ist dies selbst für Moleküle mittlerer Größe nicht effizient machbar. Die DENDRAL-Forscher
zogen analytische Chemiker zu Rate und und fanden heraus, dass diese nach bekannten
Peak-Mustern im Spektrum suchten, die auf gemeinsame Unterstrukturen im Molekül hin-
deuten.ZumBeispielwirddiefolgendeRegelverwendet,umeineKeton-Untergruppe(C=O)
zuerkennen(derenGewicht28beträgt):
fallsMdieMassedesgesamtenMolekülsistundeszweiPeaksbeix1undx2gibt,sodass
(a)x1 Cx2 DMC28;(b)x1 (cid:2)28isteinhoherPeak;(c)x2 (cid:2)28isteinhoherPeak;und
(d)mindestenseinesvonx1undx2isthoch
dannhandeltessichumeineKeton-Untergruppe.
DieErkenntnis,dassdasMoleküleinebestimmteUnterstrukturenthält,reduziertdieAnzahl
derinfragekommendenKandidatenenorm.DENDRALwarnachAnsichtseinerAutorendes-
halb so mächtig, weil es das relevante Wissen der Massenspektroskopie nicht in Form von
Grundbegriffen,sonderninFormvoneffizienten„Kochbuchrezepten“abbildete(Feigenbaum
etal.,1971).DieBedeutungvonDENDRALlagdarin,dassesdasersteerfolgreichewissensin-
tensiveSystemwar:seineExpertisestammteauseinergroßenAnzahlvonSpezialregeln.1971
starteten Feigenbaum undanderein StanforddasHeuristic ProgrammingProject (HPP),um
zuuntersuchen,inwieweitsichdieneueMethodikderExpertensystemeaufandereBereiche
anwendenlässt.
DienächstegroßeAnstrengungwardasMYCIN-SystemzurDiagnosevonInfektionskrankhei-
ten,diemitAntibiotikabehandeltwerdenkönnen.Mitetwa450RegelnkonnteMYCINsogut
wie einige Experten und deutlich besser als Assistenzärzte arbeiten. Außerdem unterschied
es sich von DENDRAL in zwei wesentlichen Punkten: Erstens gab es im Gegensatz zu den
DENDRAL-RegelnkeinallgemeinestheoretischesModell,ausdemdieMYCIN-Regelnabgelei-
tet werden konnten. Sie mussten aus umfangreichen Befragungen von Experten gewonnen
werden.ZweitensmusstendieRegelndiemitdemmedizinischenWissenverbundeneUnsi-
cherheit widerspiegeln. MYCIN enthielt ein Unsicherheitskalkül, die sogenannten Sicher-
heitsfaktoren (siehe Kapitel 13), die (damals) gut die Art und Weise abzubilden schienen,
wieÄrztedieAuswirkungenvonBefundenaufdieDiagnosebeurteilten.
Das erste kommerziell erfolgreiche Expertensystem, R1, wurde bei Digital Equipment Cor-
poration (DEC; McDermott, 1982) in Betrieb genommen. Das Programm half bei der Konfi-
gurationvonBestellungenfürneueComputersysteme;bis1986sparteesdemUnternehmen
geschätzte40MillionenUS-DollarproJahr.Bis1988hattedieKI-GruppevonDEC40Exper-
tensystemeimEinsatz,weiterewareninVorbereitung.DuPonthatte100imEinsatzund500
45
--- PAGE 47 ---
1
Einleitung
inderEntwicklung.NahezujedesgrößereUS-UnternehmenhatteseineeigeneKI-Gruppeund
nutzteodererforschteExpertensysteme.
DieBedeutungvonFachbereichswissen zeigtesichauchimBereichdesnatürlichenSprach-
verständnisses (Natural Language Understanding, NLU). Trotz des Erfolgs von Winograds
SHRDLU-System reichten seine Methoden nicht an allgemeinere Aufgaben heran: Für Pro-
blemewiedieAuflösungvonMehrdeutigkeitenverwendeteeseinfacheRegeln,diesichauf
denwinzigenAnwendungsbereichderBlockweltstützten.
Mehrere Forscher, darunter Eugene Charniak am MIT und Roger Schank in Yale, nahmen
an, dass für ein robustes Sprachverständnis allgemeines Wissen über die Welt sowie eine
allgemeine Methode zur Nutzung dieses Wissens nötig sei. (Schank ging noch weiter und
behauptete: „So etwas wie Syntax gibt es nicht“, was viele Linguisten aufbrachte, doch es
dientedazu, einehilfreicheDiskussionanzuregen.) SchankundseineStudentenentwickel-
ten eine Reihe von Programmen (Schank und Abelson, 1977; Wilensky, 1978; Schank und
Riesbeck,1981),diealledieAufgabehatten,natürlicheSprachezuverstehen.DieBetonung
lagjedochwenigeraufderSprachepersealsvielmehraufdenProblemenderRepräsentation
unddesSchlussfolgernsmitdemfürdasSprachverständniserforderlichenWissen.
Die breite Zunahme von Anwendungen auf reale Probleme führte zur Entwicklung einer
breiten Palette von Repräsentations- und Schlussfolgerungswerkzeugen. Einige basierten
auf Logik – zum Beispiel wurde die Sprache Prolog in Europa und Japan populär und
die PLANNER-Familie in den USA. Andere, die Minskys Idee der Frames (1975) folgten,
übernahmen einen strukturierteren Ansatz, indem sie Fakten über bestimmte Objekt- und
Ereignistypen zusammenstellten und die Typen in einer großen taxonomischen Hierarchie
anordneten,analogeinerbiologischenTaxonomie.
1981kündigte die japanische Regierung das Projekt „Fifth Generation“ an, einen 10-Jahres-
Plan für den Bau massiv paralleler, intelligenter Computer unter Prolog. Das Budget sollte
1,3Milliarden US-Dollar übersteigen (auf heutigen Wert umgerechnet). Als Reaktion darauf
gründeten dieUSA dieMicroelectronics and ComputerTechnology Corporation (MCC), ein
Konsortium,dasdienationaleWettbewerbsfähigkeitsicherstellensollte.InbeidenFällenwar
dieKITeileinerbreitangelegtenForschungsbemühung,unteranderemzumChipentwurfund
zuBenutzerschnittstellen.ImVereinigtenKönigreichsorgtederAlvey-Berichtdafür,dassdie
durch den Lighthill-Bericht gestrichene Finanzierung wieder aufgenommen wurde. Keines
dieser Projekte hat jedoch jemals seine ehrgeizigen Ziele in Bezug auf neue KI-Fähigkeiten
oderwirtschaftlicheAuswirkungenerreicht.
Insgesamt erlebte die KI-Industrie einen Anstieg von ein paar Millionen US-Dollar im Jahr
1980 auf einige Milliarden US-Dollar im Jahr 1988, außerdem gab es Hunderte von Unter-
nehmen, die Expertensysteme, Bildverarbeitungssysteme, Roboter und darauf spezialisierte
Soft-undHardwareentwickelten.
BalddarauffolgteeinePeriode,dieals„KI-Winter“bezeichnetwurde,indervieleUnterneh-
menaufderStreckeblieben,dasieihreextravagantenVersprechennichteinhaltenkonnten.
Esstellte sich heraus, dass esschwierig war, Expertensysteme fürkomplexeFachgebiete zu
entwickeln und zu warten –zum Teil, weil dievonden Systemen verwendeten Schlussfol-
gerungsmethoden angesichts von Ungewissheit versagten, und zum Teil, weil die Systeme
nichtausErfahrungenlernenkonnten.
1.3.5 Die Rückkehrder neuronalenNetze (1986–heute)
Mitte der 1980er Jahre entdeckten mindestens vier verschiedene Gruppen den Rückwärts-
propagation-Lernalgorithmus neu, der in den frühen 1960er Jahren entwickelt wurde. Der
Algorithmus wurde auf viele Lernprobleme in der Informatik und Psychologie angewendet
46
--- PAGE 48 ---
1.3 DieGeschichtederKünstlichenIntelligenz
und die weite Verbreitung der Ergebnisse im Sammelband Parallel Distributed Processing
(RumelhartundMcClelland,1986)sorgtefürgroßesAufsehen.
DiesesogenanntenkonnektionistischenModellewurdenvoneinigenalsdirekteKonkurrenz
sowohl zu den symbolischen Modellen von Newell und Simon als auch zu dem logizisti-
schenAnsatzvonMcCarthyundanderengesehen.Esscheintoffensichtlich,dassMenschen
auf irgendeiner Ebene Symbole manipulieren – tatsächlich legt der Anthropologe Terrence
DeaconinseinemBuchTheSymbolicSpecies(1997)nahe,dassdiesdasdefinierendeMerk-
mal desMenschen sei. ImGegensatz dazu hat GeoffHinton,eineführendeFigurbeimWie-
derauflebenneuronalerNetzwerkeinden1980erund2010erJahren,Symbolealsden„leuch-
tendenÄtherderKI“beschrieben–eineAnspielungaufdasnichtexistierendeMedium,von
demvielePhysikerdes19.Jahrhundertsglaubten,dasssichdarinelektromagnetischeWellen
ausbreiteten. Sicherlich erfüllen viele Konzepte, die wir sprachlich benennen, bei näherer
Betrachtung nicht die Art von logisch definierten notwendigen und hinreichenden Bedin-
gungen, von denen die frühen KI-Forscher hofften, sie in axiomatischer Form erfassen zu
können.Esmagsein,dasskonnektionistischeModelleinterneKonzepteaufeinefließendere
undungenauereArtundWeisebilden,diebesserandieUnordnungderrealenWeltangepasst
ist.SiehabenaußerdemdieFähigkeit,ausBeispielenzulernen–siekönnenihrenvorherge-
sagten Ausgabewert mitdemtatsächlichen WerteinerProblemstellungvergleichen undihre
Parameter ändern, um die Differenz zu verringern, was die Wahrscheinlichkeit erhöht, bei
zukünftigenBeispielengutabzuschneiden.
1.3.6 Probabilistisches Schlussfolgernund maschinelles Lernen
(1987–heute)
Die Fragilität von Expertensystemen führte zu einem neuen wissenschaftlicheren Ansatz,
der den Fokus mehr auf Wahrscheinlichkeit als auf boolesche Logik, mehr auf maschinel-
lesLernenalsaufmanuelleProgrammierungundmehraufexperimentelleErgebnissealsauf
philosophische Behauptungen legte.20 Es wurde üblicher, auf bestehenden Theorien aufzu-
bauenanstattvölligneueTheorienvorzuschlagen,BehauptungenaufstrengeTheoremeoder
solideexperimentelleMethoden(Cohen,1995)stattaufIntuitionzustützenunddieRelevanz
anhandrealerAnwendungenstattaufSpielzeugbeispielenaufzuzeigen.
Gemeinsame Benchmark-Problemsätze wurden zur Norm, um den Fortschritt zu demons-
trieren, darunter das UC Irvine Repository für Datensätze zum maschinellen Lernen, die
International Planning Competition für Planungsalgorithmen, das LibriSpeech-Korpus für
die Spracherkennung, der MNIST-Datensatz für die Erkennung handschriftlicher Ziffern,
ImageNetundCOCOfürdieErkennungvonBildobjekten,SQUADfürdieBeantwortungvon
Fragen in natürlicher Sprache, der WMT-Wettbewerb für maschinelle Übersetzung und die
InternationalSATCompetitionfürSAT-Solver.
Die KI wurde zum Teil als Rebellion gegen die Beschränkungen bestehender Fachgebiete
wie Kontrolltheorie und Statistik gegründet, doch in dieser Periode machte sich die KI die
positivenErgebnissedieserGebietezueigen.DavidMcAllester(1998)drückteessoaus:
InderAnfangszeitderKIschienesplausibel,dassneueFormendersymbolischenBerechnung,
z.B.FramesundsemantischeNetzwerke,einenGroßteilderklassischenTheorieobsoletmachten.
DiesführtezueinerFormvonIsolationismus,inderdieKIweitgehendvomRestderInformatik
getrenntwurde.DieserIsolationismuswirdderzeitaufgegeben.Manhaterkannt,dassmaschinel-
20VoneinigenwurdedieserWandelimStreit„NeatsversusScruffies“interpretiertalsSiegderNeats–diejeni-
gen,diedenken,dassKI-TheorienaufmathematischerStrengeberuhensollten–überdieScruffies–diejenigen,
die lieber vieleIdeen ausprobieren, ein paar Programme schreiben und dann beurteilen, was zu funktionie-
ren scheint. Beide Ansätzesindwichtig. Eine Verschiebung in RichtungNeat bedeutet, dass das Fachgebiet
eingewissesMaßanStabilitätundReifeerreichthat.DiederzeitigeBetonungvonDeepLearningkönnteein
WiederauflebendesScruffy-Paradigmasdarstellen.
47
--- PAGE 49 ---
1
Einleitung
lesLernennichtvonderInformationstheorieisoliertwerdensollte,dassunsicheresSchlussfolgern
nicht von stochastischer Modellierung isoliert werden sollte, dass Suche nicht von klassischer
OptimierungundSteuerungisoliertwerdensollteunddassautomatisiertesSchlussfolgernnicht
vonformalenMethodenundstatischerAnalyseisoliertwerdensollte.
Der Bereich der Spracherkennung veranschaulicht das Muster. In den 1970erJahren wurde
eineVielzahlunterschiedlicherArchitekturenundAnsätzeausprobiert.Vieledavonentstan-
deneheradhoc,waren fragil undfunktioniertennurfürwenigesorgfältig ausgewählte Bei-
spiele.Inden1980erJahrendominiertenAnsätzemitHidden-Markov-Modellen(HMMs)das
Gebiet.ZweiAspektevonHMMssindrelevant.Erstensbasierensieaufeinerstrengenmathe-
matischen Theorie. Dies ermöglichte es den Sprachforschern, auf mathematischen Ergeb-
nissen aufzubauen, die sich in anderen Bereichen über mehrere Jahrzehnte entwickelt hat-
ten.ZweitenswerdensiedurcheinenTrainingsprozessaufeinemgroßenKorpusvonrealen
Sprachdaten erzeugt. Dies stellt sicher, dass die Leistung robust ist, und in strengen Blind-
tests verbesserten dieHMMs ihreErgebnisse stetig. Infolgedessen schafften dieSprachtech-
nologieunddasverwandteGebietderHandschrifterkennungdenÜbergangzuweitverbrei-
tetenindustriellenundprivatenAnwendungen.BeachtenSie,dasseskeinenwissenschaftli-
chenAnspruchgab,dassMenschenHMMszurSpracherkennungverwenden;vielmehrboten
HMMseinenmathematischenRahmen,umdasProblemzuverstehenundzulösen.Wirwer-
den jedoch inAbschnitt 1.3.8sehen, dass Deep Learning dieses bequemeNarrativ ziemlich
aufgemischthat.
1988 war ein wichtiges Jahr für die Verbindung zwischen KI und anderen Gebieten, ein-
schließlich Statistik,Operations Research, Entscheidungstheorie und Kontrolltheorie.Judea
Pearls (1988) Probabilistic Reasoning in Intelligent Systems führte zu einer neuen Akzep-
tanz der Wahrscheinlichkeits- und Entscheidungstheorie in der KI. Pearls Entwicklung der
Bayes’schenNetzebrachteeinenrigorosenundeffizientenFormalismuszurDarstellungvon
unsicherem Wissen sowie praktische Algorithmen für probabilistisches Schlussfolgern her-
vor. Die Kapitel 12 bis 16 behandeln diesen Bereich, zusätzlich zu neueren Entwicklun-
gen, die die Ausdruckskraft probabilistischer Formalismen stark erhöht haben; Kapitel 20
beschreibt Methoden zum Lernen von Bayes’schen Netzen und verwandten Modellen aus
Daten.
EinzweiterwichtigerBeitragimJahr1988wardieArbeitvonRichSutton,derdasReinforce-
mentLearning–dasinArthursDameprogramminden1950erJahrenverwendetwurde–mit
der Theorie der Markov-Entscheidungsprozesse (Markov Decision Process, MDP) verband,
die im Bereich von Operations Research entwickelt wurde. Es folgte eine Flut von Arbei-
ten, diedie KI-Planungsforschung mit MDPs verbanden, und das Gebiet des Reinforcement
LearningfandeinerseitsAnwendungeninderRobotikundderProzesssteuerungundgewann
andererseitstiefetheoretischeGrundlagen.
Eine Folge der neu entdeckten Wertschätzung der KI für Daten, statistische Modellierung,
OptimierungundmaschinellesLernenwardieallmählicheWiedervereinigungvonTeilberei-
chen wie Computer Vision, Robotik, Spracherkennung, Multiagentensysteme und Verarbei-
tungnatürlicherSprache,diebisdahinetwasvomKernbereichderKIgetrenntwordenwaren.
Der Prozess der Reintegration hat sowohl in Bezug auf Anwendungen – der Einsatz prakti-
scherRoboterwurdezumBeispielindieserZeitstarkausgeweitet–alsauchinBezugaufein
besserestheoretischesVerständnisderKernproblemederKIbedeutendeVorteilegebracht.
1.3.7 Big Data (2001–heute)
Bemerkenswerte Fortschritte bei der Rechenleistung sowie die Schaffung des World Wide
WebhabendieErstellungsehrgroßerDatensätzeermöglicht–einPhänomen,dasmanchmal
alsBigDatabezeichnet wird.DieseDatensätze umfassen BillionenvonWörtern,Milliarden
vonBildern undMilliarden vonStundenan Sprach- undVideodaten sowie riesige Mengen
48
--- PAGE 50 ---
1.3 DieGeschichtederKünstlichenIntelligenz
an genomischen Daten, Fahrzeugortungsdaten, Clickstream-Daten, Daten aus sozialen Netz-
werkenusw.
DieshatzurEntwicklungvonLernalgorithmengeführt,diespeziellfürdieNutzungsehrgro-
ßerDatensätzeentwickeltwurden.OftistdieüberwiegendeMehrheitderBeispieleinsolchen
Datensätzenunbeschriftet(d.h.ohneLabel);inYarowskys(1995)einflussreichemArtikelzur
Mehrdeutigkeit des Wortsinns sind beispielsweise die Vorkommen eines Worts wie „Bank“
imDatensatz nichtbeschriftet, sodassnichtklarist,obsich dieWörteraufeinFinanzunter-
nehmen oder auf eine Sitzgelegenheit beziehen. Bei ausreichend großen Datensätzen errei-
chen geeignete Lernalgorithmen jedochbeiderAufgabezuerkennen, welche Bedeutungim
jeweiligen Satz gemeint war, eine Genauigkeit vonüber 96%. Darüber hinaushaben Banko
undBrill(2001)argumentiert, dassdieLeistungsverbesserung, diedurch eineVergrößerung
des Datensatzes um zwei oder drei Größenordnungen erzielt wird, jede Verbesserung über-
wiegt,diedurchdieOptimierungdesAlgorithmuserreichtwerdenkann.
EinähnlichesPhänomenscheintbeiAufgabenderComputerVisionwiedemAuffüllenvon
Leerstellen inFotosaufzutreten–Leerstellen,dieentwederdurchBeschädigungoderdurch
das Entfernen vonEx-Freunden entstanden sind. Hays und Efros (2007)entwickelten dafür
eineclevereMethode,indemsiePixelausähnlichenBilderneinblendeten;siefandenheraus,
dassdieTechnikbeieinerDatenbankmitnurTausendenvonBildernschlechtfunktionierte,
aberbeiMillionenvonBilderneineQualitätsschwelle überschritt.BalddarauflöstedieVer-
fügbarkeit von mehreren zehn Millionen Bildern in der ImageNet-Datenbank (Deng et al.,
2009)eineRevolutionimBereichderComputerVisionaus.
DieVerfügbarkeitvonBigDataunddieHinwendungzummaschinellenLernenverhalfender
KIzuneuerkommerziellerAttraktivität(Havenstein,2005;Halevyetal.,2009).BigDatawar
ein entscheidender Faktorfür den Sieg von IBMs Watson-System über menschliche Gegner
imQuizspiel Jeopardy!imJahr2011,ein Ereignis, dasdieöffentlicheWahrnehmungderKI
starkbeeinflussthat.
1.3.8 Deep Learning(2011–heute)
Der Begriff Deep Learning bezieht sich auf maschinelles Lernen mit mehreren Ebenen ein-
facher, anpassbarer Rechenelemente. Bereits in den 1970er Jahren wurde mit solchen Net-
zen experimentiert, und in Form von Convolutional Neural Networks (CNN) hatten sie in
den1990erJahreneinigeErfolgebeiderErkennunghandgeschriebenerZiffern(LeCunetal.,
1995).Docherst2011kamenDeep-Learning-Methoden sorichtiginSchwung.Diesgeschah
zunächstinderSpracherkennungunddannindervisuellenObjekterkennung.
Beim ImageNet-Wettbewerb 2012, bei dem Bilder in eine von tausend Kategorien (Gürtel-
tier,Bücherregal,Korkenzieherusw.)klassifiziertwerdenmussten,zeigteeinDeep-Learning-
System, das in der Gruppe von Geoffrey Hinton an der Universität von Toronto entwickelt
wurde (Krizhevsky et al., 2013), eine dramatische Verbesserung gegenüber früheren, größ-
tenteils handindizierten Systemen. Seitdem haben Deep-Learning-Systeme diemenschliche
Leistung bei einigen visuellen Aufgaben übertroffen (und liegen bei einigen anderen Auf-
gaben zurück). ÄhnlicheFortschritte wurden bei derSpracherkennung, maschinellen Über-
setzung, medizinischen Diagnose und bei Spielen vermeldet. Die Verwendung eines tiefen
Netzwerks zur Darstellung der Bewertungsfunktion trug zu den Siegen von ALPHAGO über
führendemenschlicheGo-Spielerbei(Silveretal.,2016,2017,2018).
Diesebemerkenswerten ErfolgehabenzueinemWiederauflebendesInteressesanderKIbei
Studenten,Unternehmen,Investoren,Regierungen,denMedienundderallgemeinenÖffent-
lichkeit geführt. Es scheint, als gäbe es jede Woche Neuigkeiten über eine neue KI-Anwen-
dung, die sich der menschlichen Leistung annähert oder diese übertrifft, oft begleitet von
SpekulationenüberentwederbeschleunigtenErfolgodereinenneuenKI-Winter.
49
--- PAGE 51 ---
1
Einleitung
Deep Learning ist stark auf leistungsfähige Hardware angewiesen. Während eine Standard-
Computer-CPU 109 oder 1010 Operationen pro Sekunde ausführen kann, kann ein Deep-
Learning-Algorithmus, der auf spezialisierter Hardware (z.B. GPU, TPU oder FPGA) läuft,
zwischen 1014 und 1017 Operationen pro Sekunde verbrauchen, meist in Form von hoch-
parallelisiertenMatrix-undVektoroperationen.NatürlichhängtDeepLearningauchvonder
VerfügbarkeitgroßerMengenvonTrainingsdaten undvoneinigenalgorithmischenTricksab
(sieheKapitel21).
1.4 State of the Art
Das Projekt „One Hundred Year Study on AI“ der Stanford-Universität (auch bekannt als
AI100) beruft Expertengremien ein, um Berichte über die aktuelle Situation in der KI zu
erstellen. Ihr Bericht aus dem Jahr 2016(Stone et al., 2016;Grosz und Stone, 2018) kommt
zudemSchluss,dass„einerheblicherAnstiegderzukünftigenNutzungvonKI-Anwendun-
genzuerwartenist,daruntermehrselbstfahrendeAutos,Gesundheitsdiagnostikundgezielte
Behandlungen sowie körperliche Unterstützung bei der Pflege älterer Menschen“ und dass
„dieGesellschaft jetztaneinementscheidenden Punktsteht,andemesdarumgeht,wieKI-
basierte Technologien soeingesetzt werden können,dasssiedemokratische WertewieFrei-
heit, Gleichheit und Transparenz fördern und nicht behindern“. AI100 erstellt auch einen
KI-Indexaufaiindex.org,mitdessenHilfederFortschrittnachverfolgtwerdenkann.Einige
Höhepunkte aus den Berichten 2018 und 2019 (im Vergleich zur Situation im Jahr 2000,
sofernnichtandersangegeben):
(cid:2) Veröffentlichungen: DieZahlderKI-Publikationenstiegzwischen 2010und2019umdas
20-Fache auf etwa 20.000 pro Jahr. Die beliebteste Kategorie war maschinelles Lernen.
(Die Zahl der Veröffentlichungen zum maschinellen Lernen auf arXiv.org hat sich von
2009bis2017jedesJahrverdoppelt.)ComputerVisionundComputerlinguistikwarendie
nächstbeliebtenKategorien.
(cid:2) Tonalität der Berichterstattung: Etwas 70% der Nachrichtenartikel über KI sind neutral,
aberArtikelmitpositivemTonsindvon12%imJahr2016auf30%imJahr2018gestie-
gen.DiehäufigstenThemensindethischerNatur:DatenschutzundalgorithmischeVorein-
genommenheit.
(cid:2) Studenten:DieZahlderStudienanfängeristindenUSAumdas5-Facheundinternational
umdas16-Fachegegenüber2010gestiegen.KIistdiebeliebtesteSpezialisierunginnerhalb
derInformatik.
(cid:2) Diversität: KI-Professoren weltweit sind zu etwa 80% männlich und zu 20% weiblich.
ÄhnlicheZahlengeltenfürDoktorandenundMitarbeiterinderIndustrie.
(cid:2) Konferenzen: Die Teilnehmerzahl der NeurIPS stieg seit 2012 um 800% auf 13.500 Teil-
nehmer.AndereKonferenzenverzeichneneinjährlichesWachstumvonetwa30%.
(cid:2) Industrie:KI-StartupssindindenUSAumdas20-Facheaufüber800gestiegen.
(cid:2) Internationalisierung: China veröffentlicht mehr Publikationen pro Jahr als die USA und
etwasovielewieganzEuropa.HinsichtlichderZitationshäufigkeitliegendieUS-Autoren
jedoch 50% vor den chinesischen Autoren. Singapur, Brasilien, Australien, Kanada und
IndiensinddieLänder,diehinsichtlichderNeueinstellungenimKI-Bereichamschnells-
tenwachsen.
(cid:2) Vision: Die Fehlerraten bei der Objekterkennung (wie sie in der LSVRC, der Large-Scale
Visual Recognition Challenge, erreicht werden) verringerten sich von 28% im Jahr 2010
auf2%imJahr2017undübertreffendamitdiemenschlicheLeistung.DieGenauigkeitbei
derBeantwortungoffenervisuellerFragen(VisualQuestionAnswering,VQA)hatsichseit
50
--- PAGE 52 ---
1.4 StateoftheArt
2015von55%auf68%verbessert, bleibtaberhinterdermenschlichen Leistungzurück,
diebei83%liegt.
(cid:2) Geschwindigkeit:DieTrainingszeitfürdieBilderkennungsaufgabeistalleinindenletzten
zwei Jahren um den Faktor100 gesunken. Die Rechenleistung derTop-KI-Anwendungen
verdoppeltsichalle3,4Monate.
(cid:2) Sprache:DieGenauigkeitbeiderBeantwortungvonFragen,angegebendurchdasF1-Maß
vomStanford Question Answering Dataset (SQUAD), stieg von 2015bis 2019von 60 auf
95; bei der SQUAD2-Variante war der Fortschritt noch schneller: von 62 auf 90 in nur
einemJahr.BeideWerteübertreffendiemenschlicheLeistung.
(cid:2) Menschliche Benchmarks: Bis 2019 haben KI-Systeme Berichten zufolge die Leistung
von Menschen in folgenden Bereichen erreicht oder übertroffen: Schach, Go, Poker,
Pac-Man, Jeopardy!, ImageNet-Objekterkennung, Spracherkennung in einem begrenzten
Bereich,Chinesisch-Englisch-ÜbersetzungineinembegrenztenBereich,QuakeIII,Dota2,
StarCraft II, verschiedenen Atari-Spiele, Hautkrebserkennung, Prostatakrebserkennung,
ProteinfaltungundDiagnosevondiabetischerRetinopathie.
Wann (wenn überhaupt) werden KI-Systeme das Niveau erreichen, die menschlichen Leis-
tungenbeieinerbreitenFülleanAufgabenzuübertreffen?Ford(2018)befragteKI-Experten
undbekameine großeSpannbreitean Zieljahren zurAntwort –von2029bis2200–,wobei
derMittelwert dasJahr2099bildet.LauteinerähnlichenUmfrage (Graceet al.,2017)dach-
ten 50%der Befragten, dass diesbis2066so weit sein könnte,obwohl10% glaubten, dass
esbereits2025geschehenkönnte,undeinigewenigeantworteten„nie“.DieExpertenwaren
auch geteilter Meinung darüber, ob wir grundlegende neue Durchbrüche oder nur Verfei-
nerungen aktueller Ansätze benötigen. Aber nehmen Sie ihre Vorhersagen nicht zu ernst –
wiePhilipTetlock(2017)imBereichderVorhersagevonWeltereignissen demonstriert,sind
ExpertendarinnichtbesseralsAmateure.
Wie werden zukünftige KI-Systeme arbeiten? Wir können es noch nicht sagen. Wie in die-
sem Abschnitt beschrieben, hat das Fachgebiet im Laufe seiner Geschichte schon mehrere
Perspektiveneingenommen–zuerstdiekühneIdee,dassmaschinelleIntelligenzüberhaupt
möglich ist, dann, dass sie durch die Codierung von Expertenwissen innerhalb der Logik
erreichtwerdenkann,dann,dassprobabilistischeModellederWeltdasHauptwerkzeugsein
werden, und zuletzt, dass maschinelles Lernen Modelle hervorbringen wird, die vielleicht
überhauptnichtaufeinerdergutverstandenenTheorienberuhen.DieZukunftwirdzeigen,
welchesModellalsNächsteskommt.
WaskanndieKIheuteschonleisten?Vielleichtnichtsoviel,wieeinigederoptimistischeren
Medienartikelvermutenlassen,aberdocheineganzeMenge.HiersindeinigeBeispiele:
Roboterfahrzeuge: Die Geschichte der Roboterfahrzeuge reicht bis zu den funkgesteuerten
Autosder1920erJahrezurück,dochdieerstenDemonstrationendesautonomenFahrensauf
der Straße ohne spezielle Führung fanden in den 1980er Jahren statt (Kanade et al., 1986;
Dickmanns und Zapp, 1987). Nachdem das Fahren auf unbefestigten Straßen im Rahmen
der fast 213km langen DARPA Grand Challenge im Jahr 2005 (Thrun, 2006) und auf befes-
tigten Straßen mit Verkehr bei der DARPA Urban Challenge 2007 erfolgreich demonstriert
war, begannderWettlaufumdieEntwicklungselbstfahrender Autosernsthaft. ImJahr2018
erreichtendieTestfahrzeugevonWaymodieMarkevon16MillionengefahrenenKilometern
auföffentlichenStraßenohneeinenschwerenUnfall,wobeidermenschlicheFahrernurein-
mal circa alle 9.000 Kilometer die Kontrolle übernehmen musste. Bald darauf begann das
Unternehmen,einenkommerziellenRobotertaxidienstanzubieten.
InderLuftsorgenautonomeStarrflügler-Drohnenseit2016fürlandesweiteBlutlieferungenin
Ruanda.DieQuadcoptervollführenbemerkenswerte Kunstflugmanöver,erkundenGebäude,
erstellen3-D-KartenundfügensichzuautonomenFormationenzusammen.
51
--- PAGE 53 ---
1
Einleitung
Laufroboter:BigDog,einvierbeinigerRobotervonRaibertetal.(2008),hatunsereVorstellun-
gendarüber,wieRobotersichbewegen,aufdenKopfgestellt–keinlangsamer,steifbeiniger,
seitwärtsgerichteterGangderHollywood-Filmrobotermehr,sonderneineBewegung,diesehr
der eines Tieres ähnelt und auch ein Wiederaufstehen erlaubt, wenn der Robotergeschubst
wirdoderaufeinerEispfützeausrutscht. Atlas, einhumanoiderRoboter,läuftnichtnurauf
unebenemTerrain,sondernspringtauchaufKistenundschlägtRückwärtssaltos (Ackerman
undGuizzo,2016).
Autonomes Planen und Scheduling: Hundert Millionen Kilometer von der Erde entfernt
wurde das Remote-Agent-Programm der NASA zum ersten autonomen Planungsprogramm
an BordeinesRaumfahrzeugs, dasdenzeitlichen Ablauf vonOperationen steuerte (Jonsson
et al., 2000). RemoteAgent generierte Pläne anhand komplexer Ziele, die vom Boden aus
spezifiziertwurden,undüberwachtedieAusführungdieserPläne–RemoteAgenterkannte,
diagnostizierteundbehobProbleme,sobaldsieauftraten.HeutewirddasEUROPA-Planungs-
toolkit(Barreiro etal.,2012)fürdentäglichenBetrieb derMars-RoverderNASAverwendet
und das SEXTANT-System (Winternitz, 2017)ermöglicht die autonomeNavigation im tiefen
Weltraum,jenseitsdesglobalenGPS-Systems.
WährendderKriseamPersischenGolfimJahr1991setztendieUS-StreitkräftedasProgramm
DART (DynamicAnalysisandReplanningTool; Crossund Walker, 1994)ein, um eine auto-
matisiertelogistischePlanungdurchzuführenundZeitplänefürTransportaufgabenzuerstel-
len.Diesbetrafbiszu50.000Fahrzeuge, LadungundPersonengleichzeitig, undesmussten
Startpunkte,Zielorte, Routen, Transportkapazitäten, Hafen- undFlugplatzkapazitäten sowie
Konfliktlösungen zwischen allen Parametern berücksichtigt werden. Die DARPA (Defense
AdvancedResearch ProjectAgency)stelltefest,dasssichdurchdieseeineAnwendungihre
30-jährigenInvestitionenindieKImehralsausgezahlthatten.
JedenTagstellenMitfahrunternehmenwieUberundKartendienstewieGoogleMapsFahran-
weisungen fürHunderteMillionenvonNutzernbereitundberechnenschnelleineoptimale
RouteunterBerücksichtigungderaktuellenundprognostiziertenzukünftigenVerkehrsbedin-
gungen.
Maschinelles Übersetzen: Maschinelle Online-Übersetzungssysteme ermöglichen heute das
Lesen von Dokumenten in über 100 Sprachen, darunter sind die Muttersprachen von über
99%derMenschen,undübersetzen täglich HundertevonMilliardenvonWörternfürHun-
dertevonMillionenvonBenutzern.Siesindzwarnichtperfekt,aberimAllgemeinenfürdas
Verständnisausreichend.BeiengverwandtenSprachenmiteinergroßenMengeanTrainings-
daten (wie Französisch und Englisch) liegen die Übersetzungen innerhalb eines begrenzten
FachbereichsnaheamNiveaueinesMenschen(Wuetal.,2016b).
Spracherkennung:ImJahr2017zeigteMicrosoft,dassseinSystemzurKonversationssprach-
erkennungeineWortfehlerratevon5,1%erreichthat,wasdermenschlichenLeistungbeider
sogenanntenSwitchboard-Aufgabeentspricht,beiderTelefongesprächetranskribiertwerden
(Xiongetal.,2017).EtwaeinDrittelderComputerinteraktionweltweiterfolgtinzwischenper
Sprache statt über die Tastatur; Skype stellt eine Echtzeit-Sprachübersetzung in zehn Spra-
chenzurVerfügung.Alexa,Siri,CortanaundGooglebietenAssistentenan,dieFragenbeant-
worten und Aufgaben für den Benutzer ausführen können; zum Beispiel nutzt der Dienst
GoogleDuplexSpracherkennungundSprachsynthese,umRestaurantreservierungen fürden
BenutzervorzunehmenundeinefließendeKonversationinseinemNamendurchzuführen.
Empfehlungen: Unternehmen wie Amazon, Facebook, Netflix, Spotify, YouTube, Walmart
undanderenutzenmaschinellesLernen,umihrenKundenzuempfehlen,wasdiesengefallen
könnte,basierend aufihrenbisherigenErfahrungenunddenenandererGleichgesinnter. Der
BereichderEmpfehlungsdienstehateinelangeGeschichte(ResnickundVarian,1997),verän-
dertsichaberschnelldurchneueDeep-Learning-Methoden,diesowohlInhalte(Text,Musik,
Video) als auch die Historie und Metadaten analysieren (van den Oord et al., 2014; Zhang
52
--- PAGE 54 ---
1.4 StateoftheArt
etal.,2017).AuchdieSpam-FilterungkannalseineFormderEmpfehlung(bzw.Nichtemp-
fehlung)betrachtetwerden–aktuelleKI-Technikenfilternüber99,9%desSpamsherausund
E-Mail-DienstekönnenauchpotenzielleEmpfängersowiemöglicheAntworttexteempfehlen.
Spiele:AlsDeepBlue1997denSchachweltmeisterGarriKasparowbesiegte,setztendieVer-
fechterdermenschlichenÜberlegenheitihreHoffnungenaufGo.PietHut,einAstrophysiker
undGo-Enthusiast,sagtevoraus,dasses„hundertJahredauernwürde,biseinComputerden
Menschen in Go schlägt – vielleicht sogar noch länger“. Doch nur 20 Jahre später übertraf
ALPHAGOallemenschlichenSpieler(Silveretal.,2017).DerWeltmeisterKeJiesagte:„Letz-
tesJahrwarseinSpielnochrechtmenschenähnlich.AberdiesesJahrspielteerwieeinGott
desGo.“ALPHAGOprofitiertevomStudiumHunderttausendervergangenerPartienmensch-
licherGo-SpielerundvomdestilliertenWissenderGo-Experten,dieimTeammitarbeiteten.
Ein Nachfolgeprogramm, ALPHAZERO, benötigte keine Eingaben von Menschen (außer den
Spielregeln) undwarinderLage,allein durchSelbstspiel zulernen,alleGegner, Menschen
undMaschinen,beiGo,SchachundShogizubesiegen(Silveretal.,2018).Inzwischenwur-
denmenschlicheWeltmeistervonKI-SystemenbeisounterschiedlichenSpielenbesiegtwie
Jeopardy!(Ferruccietal.,2010),Poker(Bowlingetal.,2015;Moravcˇíketal.,2017;Brownund
Sandholm,2019)unddenVideospielenDota2(FernandezundMahlmann,2018),StarCraftII
(Vinyalsetal.,2019)undQuakeIII(Jaderbergetal.,2019).
Bildverstehen: Nicht zufrieden damit, die menschlichen Genauigkeit bei der anspruchsvol-
lenImageNet-Objekterkennungsaufgabezuübertreffen,habensichForscherausdemBereich
ComputerVisiondemschwierigeren ProblemderBildbeschriftungangenommen. Beeindru-
ckende Beispiele sind „Eine Person auf einem Motorrad auf einer unbefestigten Straße“,
„Zwei Pizzen auf einer Herdplatte“ und „Eine Gruppe junger Leute, die spielen“ (Vinyals
et al.,2017b).Dieaktuellen Systemesindjedochalles andere alsperfekt:Ein„Kühlschrank
gefüllt mit vielen Lebensmitteln und Getränken“ entpuppt sich als Parkverbotsschild, das
durchvielekleineAufkleberteilweiseverdecktwird.
Medizin:KI-AlgorithmensindinzwischenbeiderDiagnosevielerErkrankungengleichwertig
oderbesseralsExperten,insbesonderewenndieDiagnoseaufBildernbasiert.Beispielesind
dieAlzheimer-Krankheit (Dingetal.,2018),metastasierender Krebs(Liuetal.,2017;Esteva
etal.,2017),Augenkrankheiten(Gulshanetal.,2016)undHautkrankheiten(Liuetal.,2019c).
EinesystematischeÜbersichtsarbeitundMeta-Analyse(Liuetal.,2019a)ergab,dassdieLeis-
tung von KI-Programmen im Durchschnitt der vonmedizinischem Fachpersonal gleichwer-
tig ist. Ein aktueller Schwerpunkt in der medizinischen KI liegt in der Ermöglichung von
Mensch-Maschine-Partnerschaften.SoerreichtdasLYNA-SystemeineGesamtgenauigkeitvon
99,6%beiderDiagnosevonmetastasierendemBrustkrebs–besseralseinalleinigermensch-
licherExperte, dochdieKombinationderbeiden schneidet nochbesserab (Liuet al.,2018;
Steineretal.,2018).
Die weit verbreitete Einführung dieser Techniken wird nun nicht durch die diagnostische
Genauigkeit begrenzt, sondern durch die Notwendigkeit, eine Verbesserung der klinischen
Ergebnisse nachzuweisen und Transparenz, Unvoreingenommenheit und Datenschutz zu
gewährleisten (Topol,2019).ImJahr2017wurdennurzweimedizinischeKI-Anwendungen
vonderFDA21 zugelassen, dochbereits2018wardieseZahlauf12gestiegen undsienimmt
weiterzu.
Klimawissenschaft: EinTeamvonWissenschaftlern gewann denGordon-Bell-Preis2018für
einDeep-Learning-Modell,dasdetaillierteInformationenüberextremeWetterereignisseent-
deckt,diezuvorinKlimadaten vergraben waren.SienutzteneinenSupercomputermitspe-
zialisierter GPU-Hardware, um die ExaOp-Ebene (1018 Operationen pro Sekunde) zu über-
schreiten – das erste maschinelle Lernprogramm, dem dies gelang (Kurth et al., 2018). Rol-
21FoodandDrugAdministration,US-amerikanischeBehördefürLebensmittel-undArzneimittelsicherheit.
53
--- PAGE 55 ---
1
Einleitung
nicketal.(2019)präsentiereneinen60-seitigenKatalogvonMöglichkeiten,wiemaschinelles
LernenzurBewältigungdesKlimawandelseingesetztwerdenkann.
Dies sind nur einige Beispiele für KI-Systeme, die es heute gibt: nicht Magie oder Science-
Fiction–sondernWissenschaft,TechnikundMathematik,indiediesesBucheinführt.
1.5 Risiken und Nutzen der KI
Francis Bacon, ein Philosoph, dem die Erfindung der wissenschaftlichen Methode zuge-
schrieben wird, bemerkte in The Wisdom of the Ancients (1609), dass die „mechanischen
Künstevonzweideutigem Nutzen sindundsowohl zumSchaden alsauch zurHeilung die-
nen“.DadieKIeineimmerwichtigere Rolleinden Bereichen Wirtschaft, Soziales, Wissen-
schaft,Medizin,FinanzenundMilitärspielt,tunwirgutdaran,dieSchädenundHeilmittel
–modernausgedrückt,dieRisikenundNutzen–zubedenken,diesiemitsichbringenkann.
DiehierzusammengefasstenThemenwerdenindenKapiteln27und28ausführlicherbehan-
delt.
Um mit dem Nutzen zu beginnen: Einfach ausgedrückt ist unsere gesamte Zivilisation das
Produkt unserer menschlichen Intelligenz. Wenn wir Zugang zu einer wesentlich höheren
maschinellenIntelligenzhaben,wirddieObergrenzefürunsereAmbitionenerheblichange-
hoben.DasPotenzialvonKIundRobotik,dieMenschheitvonniederen,sichwiederholenden
ArbeitenzubefreienunddieProduktionvonGüternundDienstleistungen drastischzustei-
gern,könnteeineÄradesFriedensunddesÜberflusseseinläuten.DieFähigkeit,diewissen-
schaftlicheForschungzubeschleunigen, könntezuHeilmittelnfürKrankheitenundLösun-
gen für den Klimawandel und die Ressourcenknappheit führen. Wie Demis Hassabis, CEO
vonGoogleDeepMind,vorgeschlagenhat:„LösenSiezuerstKI,dannverwendenSiedieKI,
umallesanderezulösen.“
LangebevorwirdieMöglichkeithaben,„KIzulösen“,werdenwirjedochaufRisikendurch
den Missbrauch von KI eingehen, ob unbeabsichtigt oder nicht. Einige davon sind bereits
erkennbar,anderescheinenaufgrundderaktuellenTrendswahrscheinlich:
(cid:2) TödlicheautonomeWaffen:DiesewerdenvondenVereintenNationenalsWaffendefiniert,
die menschliche Ziele ohne menschliches Eingreifen lokalisieren, auswählen und elimi-
nierenkönnen.EinHauptsorgebeisolchenWaffenistihreSkalierbarkeit:DasFehlender
Notwendigkeit menschlicher Überwachung bedeutet, dass eine kleineGruppeeinebelie-
big große Anzahl von Waffen gegen menschliche Ziele einsetzen kann, die durch jedes
machbareErkennungskriterium definiertsind.DiefürautonomeWaffenbenötigten Tech-
nologienähnelndenen,diebeiselbstfahrenden AutoszumEinsatzkommen.2014began-
nen bei den Vereinten Nationen die ersten informellen Expertendiskussionen, die dann
2017 formal in den Status der Vorbereitung eines Abkommens durch eine Gruppe von
Regierungsexpertenmündeten.
(cid:2) Überwachung und Einflussnahme: Während es für Institutionen der inneren Sicherheit
teuer, langwierig und manchmal rechtlich grenzwertig ist, Telefonleitungen, Videokame-
ras, E-Mails und andere Nachrichtenkanäle zu überwachen, kann die KI (Spracherken-
nung, Computer Vision und natürliches Sprachverständnis) in skalierbarer Weise einge-
setzt werden, um eine auf Einzelpersonen abzielende Massenüberwachung durchzufüh-
renundbestimmteinteressanteAktivitätenauszumachen.Undumgekehrt:Indem–basie-
rend aufTechniken desmaschinellen Lernens–derInformationsflussdurchdiesozialen
Medien auf Einzelpersonen zugeschnitten wird, kann politisches Verhalten bis zu einem
gewissen Grad verändert undgesteuert werden –eine Sorge, diebeiWahlen ab demJahr
2016deutlichwurde.
54
--- PAGE 56 ---
1.5 RisikenundNutzenderKI
(cid:2) Befangene Entscheidungsfindung: Der fahrlässige oder absichtliche Missbrauch von ma-
schinellenLernalgorithmenfürAufgabenwiedieBewertungvonBewährungs-undKredit-
anträgen kannzuEntscheidungen führen,dieaufgrundvonRasse, Geschlecht oderande-
ren diskriminierenden Kategorien voreingenommen sind. Oft spiegeln die Daten selbst
eineweitverbreiteteVoreingenommenheitinderGesellschaftwider.
(cid:2) AuswirkungenaufdieBeschäftigung:DieSorge,dassMaschinenArbeitsplätzevernichten,
ist Jahrhundertealt. Die Sache ist nieeinfach: Maschinen erledigen einige derAufgaben,
diesonstMenschenerledigenwürden,dochsiemachenMenschenauchproduktiverund
festigen so ihr Beschäftigungsverhältnis. Außerdem sind diese Unternehmen dann pro-
fitablerundkönnenhöhereLöhnezahlen. EinigeTätigkeiten, diesich sonstnichtlohnen
würden,könnendadurchwirtschaftlichrentabelwerden.IhrEinsatzführtimAllgemeinen
zusteigendemWohlstand,hatabertendenzielldenEffekt,dasssichderWohlstandvonder
ArbeitzumKapital verlagert, wasdieUngleichheit weiterverschärft. Früheretechnologi-
scheFortschritte–wiedieErfindungdesmechanischenWebstuhls–habenzuschwerwie-
gendenEinbrüchenbeiderBeschäftigung geführt,aberletztendlich findendieMenschen
neueArtenvonArbeit.Andererseitsistesmöglich,dassdieKIauchdieseneuenArtenvon
Arbeit übernehmen wird. Dieses Thema rückt immermehr in den Fokusvon Ökonomen
undRegierungenaufderganzenWelt.
(cid:2) Sicherheitskritische Anwendungen: Mit dem Fortschritt der KI-Techniken werden diese
zunehmendinsicherheitskritischenAnwendungenmithohemRisikoeingesetzt,z.B.beim
FahrenvonAutosoderbeiderVerwaltungderWasserversorgunginStädten.Eskambereits
zutödlichenUnfällen,wasdieSchwierigkeitderformalenVerifizierungundstatistischen
Risikoanalyse für Systeme hervorhebt, die mit Techniken des maschinellen Lernens ent-
wickelt wurden. Das Fachgebiet der KI wird technische und ethische Standards entwi-
ckeln müssen, die mindestens mit denen vergleichbar sind, die in anderen Ingenieurs-
und Gesundheitsdisziplinen vorherrschen, in denen das Leben von Menschen auf dem
Spielsteht.
(cid:2) Cybersicherheit: KI-Techniken sind nützlich bei der Abwehr von Cyberangriffen, z.B.
durch die Erkennung ungewöhnlicher Verhaltensmuster, aber sie werden auch dazu
beitragen, dass Malware stärker, überlebens- und verbreitungsfähiger wird. So wurden
beispielsweisemitMethodendesReinforcementLearninghocheffektiveToolsfürautoma-
tisierte,personalisierteErpressungs-undPhishing-Angriffeentwickelt.
WirwerdendieseThemeninAbschnitt27.3nocheinmalgenauerbetrachten. WennKI-Sys-
teme immer leistungsfähiger werden, werden sie einige der gesellschaftlichen Rollen über-
nehmen, diezuvorvonMenschen besetzt waren. SowieMenschen dieseRollenin derVer-
gangenheit genutzt haben, um Unheil zu stiften, ist zu erwarten, dass Menschen auch KI-
SystemeindiesenRollenmissbrauchenwerden,umnochmehrUnheilzustiften.Alleoben
genannten Beispiele betonen die Bedeutung von Lenkung und schlussendlich Regulierung.
Derzeit habendieForschungsgemeinschaft unddiegroßenUnternehmen,dieanderKI-For-
schung beteiligt sind, freiwillige Selbstregulierungsprinzipien für KI-bezogene Aktivitäten
entwickelt (siehe Abschnitt 27.3). Regierungen und internationale Organisationen richten
Beratungsgremien ein, um für jeden spezifischen Anwendungsfall geeignete Regelungen zu
erarbeiten, sich auf diewirtschaftlichen undsozialen Auswirkungen vorzubereiten und die
FähigkeitenderKIzurLösungwichtigergesellschaftlicherProblemezunutzen.
WiesiehtesauflangeSichtaus?WerdenwirdasseitLangemangestrebteZielerreichen:die
SchaffungvonIntelligenz,diemitdermenschlichenIntelligenzvergleichbaristodersiesogar
übertrifft?Undwennwirdasgeschaffthaben,wasdann?
WährendeinesGroßteilsderKI-GeschichtewurdendieseFragenvomalltäglichenTrottüber-
schattet, der darin bestand, KI-Systeme dazu zu bewegen, irgendetwas auch nur annähernd
Intelligentes zu tun. Wie bei jeder breit angelegten Disziplin hat sich die große Mehrheit
55
--- PAGE 57 ---
1
Einleitung
derKI-ForscheraufeinbestimmtesTeilgebietspezialisiert,beispielsweiseSpiele,Wissensre-
präsentation, Vision oder Verständnis natürlicher Sprache – oft in der Annahme, dass Fort-
schritteimjeweiligenTeilgebietzudenallgemeinenZielenderKIbeitragenwürde.NilsNils-
son (1995), einer der leitenden Wissenschaftler des Shakey-Projekts am SRI, erinnerte das
FachgebietandieseumfassenderenZieleundwarnte,dassdieTeilgebieteGefahrliefen,zum
Selbstzweck zu werden. Später schlossen sich einige einflussreiche Begründer der KI, dar-
unterJohnMcCarthy(2007),MarvinMinsky(2007)undPatrickWinston(BealundWinston,
2009),den Warnungen Nilssonsan undschlugen vor,dassdieKI, anstatt sich auf messbare
LeistungeninspezifischenAnwendungenzukonzentrieren,zuihrenWurzelnzurückkehren
sollte, um in Herb SimonsWorten „Maschinen, die denken, die lernen und die erschaffen“
anzustreben.SienanntendiesesBestrebenHuman-LevelAIoderHLAI–eineMaschinesollte
in der Lage sein, alles zu lernen, was ein Mensch tun kann. Ihr erstes Symposium fand im
Jahr2004statt(Minskyetal.,2004).EinweiteresProjektmitähnlichenZielen,dieArtificial
GeneralIntelligence-Bewegung(AGI;GoertzelundPennachin,2007),hieltimJahr2008ihre
ersteKonferenzabundorganisiertedasJournalofArtificialGeneralIntelligence.
EtwazurgleichenZeitwurdenBedenkenlaut,dassdieErschaffungeinerkünstlichenSuper-
intelligenz oder ASI – einer Intelligenz, die die menschlichen Fähigkeiten weit übertrifft –
eineschlechte Ideesein könnte(Yudkowsky,2008;Omohundro,2008).Turing(1996)selbst
äußerte sich in einer Vorlesung, die er 1951 in Manchester hielt, in Anlehnung an frühere
IdeenvonSamuelButler(1863):22
Esistwahrscheinlich,dasssobalddieMethodedesmaschinellenDenkensbegonnenhat,esnicht
langedauernwird,bissieunsereschwachenKräfteübertrifft....AnirgendeinemPunktsolltenwir
alsodamitrechnen,dassdieMaschinendieKontrolleübernehmen,sowieesinSamuelButlers
Erewhonerwähntwird.
DieseBedenkenhabensichebenerstmitdenjüngstenFortschrittenimBereichDeepLearn-
ing,derVeröffentlichungvonBüchernwieSuperintelligencevonNickBostrom(2014)sowie
öffentlichenÄußerungenvonStephenHawking,BillGates,MartinReesundElonMuskver-
stärkt.
Ein allgemeines Unbehagen bei der Idee von superintelligenten Maschinen zu verspüren,
ist nurnatürlich. Man könntedies das Gorilla-Problem nennen: Vor etwa sieben Millionen
Jahren entwickelte sich ein heute ausgestorbener Primat, von dem ein Zweig zu den Goril-
las und einer zum Menschen führte. Heute sind die Gorillas nicht allzu glücklich über den
menschlichenZweig;siehabenimGrundekeineKontrolleüberihreZukunft.Wenndiesdas
Ergebnis der erfolgreichen Entwicklung von übermenschlicher KI ist – dass die Menschen
die Kontrolle über ihre Zukunft abgeben –, dann sollten wir vielleicht die Arbeit an der KI
einstellenundfolglichaufdenNutzenverzichten,densiebringenkönnte.DiesistdieEssenz
vonTuringsWarnung:Esistnichtoffensichtlich,dasswirMaschinenkontrollierenkönnen,
dieintelligentersindalswir.
WäredieübermenschlicheKIeineBlackbox,dieausdemWeltallkommt,dannwäreesinder
Tatklug,beimÖffnenderBoxVorsicht waltenzulassen. DochdiesistjanichtderFall:Wir
entwerfen die KI-Systeme, wenn sie also am Ende„die Kontrolleübernehmen“, wie Turing
andeutet,wäredasdasErgebniseinesEntwurfsfehlers.
22Nochfrüher,imJahr1847,wetterteRichardThornton,HerausgeberdesPrimitiveExpounder,gegenmecha-
nische Rechenmaschinen: „Der Geist ...überholt sich selbst und beseitigt die Notwendigkeit seiner eigenen
Existenz, indem er Maschinen erfindet, die sein eigenes Denken übernehmen. ...Aber wer weiß, ob solche
Maschinen,wennsiezugrößererVollkommenheitgebrachtwerden,nichteinenPlanaushecken,umalleihre
eigenenMängelzubeheben,unddannIdeenausarbeiten,diejenseitsdesWissensdessterblichenVerstands
liegen!“
56
--- PAGE 58 ---
1.5 RisikenundNutzenderKI
UmeinsolchesErgebniszuvermeiden,müssenwirdieQuelleeinesmöglichenFehlersver-
stehen. Norbert Wiener (1960), der sich angeregt sah, über die langfristige Zukunft der KI
nachzudenken,nachdemergesehenhatte,dassdasDameprogrammvonArthurSamuellernt,
seinenSchöpferzuschlagen,sagte:
Wennwir,umunsereZielezuerreichen,einemechanischeApparaturbenutzen,inderenBetrieb
wirnichtwirksameingreifenkönnen...dannsolltenwirunsganzsichersein,dassdasZiel,das
wirderMaschineeingeben,dasselbeZielist,daswirtatsächlichhabenmöchten.
InvielenKulturengibtesMythenvonMenschen,dieGötter,Geister,MagieroderTeufelum
etwas bitten. Unweigerlich bekommen sie in diesen Geschichten das, worum sie wörtlich
bitten, und bereuen es dann. Der dritte Wunsch, wenn es denn einen gibt, besteht in der
Regel darin, die ersten beiden wieder rückgängig zu machen. Wir nennen dies das König-
Midas-Problem:Midas,einlegendärerKönigindergriechischenMythologie,batdarum,dass
sichalles,waserberührte,inGoldverwandelnsollte,bereuteesdannaber,nachdemerseine
Speisen,GetränkeundFamilienmitgliederberührthatte.23
Wir haben dieses Thema in Abschnitt 1.1.5 schon angeschnitten, als wir auf die Notwen-
digkeit einer signifikanten Modifikation des Standardmodells, feste Ziele in die Maschine
einzubauen, hingewiesen haben.DieLösungfürWienersDilemmabesteht darin,überhaupt
keinfestes„ZielindieMaschineeinzugeben“.StattdessenwollenwirMaschinen,diedanach
streben, menschliche Ziele zu erreichen, aber wissen, dass sie nicht mit Sicherheit wissen,
wasgenaudieseZielesind.
Es ist vielleicht ein unglücklicher Zustand, dass fast die gesamte bisherige KI-Forschung
innerhalbdesStandardmodellsdurchgeführtwurde,wasbedeutet,dassfastdasgesamtetech-
nischeMaterialindiesemBuchdiesenintellektuellenRahmenwiderspiegelt. Esgibtjedoch
einige frühe Ergebnisse innerhalb des neuen Rahmens. In Kapitel 16 zeigen wir, dass eine
Maschine genau dann einen positiven Anreiz hat, sich abschalten zu lassen, wenn sie sich
überdasmenschlicheZielunsicherist.InKapitel18formulierenunduntersuchenwirAssis-
tenzspiele, diemathematisch dieSituationbeschreiben, indereinMensch ein Zielhat und
eineMaschineversucht,diesesZielzuerreichen,aberzunächstunsicherist,wiedasZiellau-
tet.InKapitel22erläuternwirdieMethodendesInverseReinforcementLearning(IRL),die
es Maschinen ermöglichen, mehr übermenschliche Präferenzen zu lernen, indem sie beob-
achten, welche Entscheidungen dieMenschen treffen. In Kapitel 27gehen wirauf zwei der
Hauptschwierigkeitenein:dieersteist,dassunsereEntscheidungenvonunserenPräferenzen,
d.h.voneinersehrkomplexenkognitivenArchitekturabhängen,dienurschwerumkehrbar
ist; die zweite, dass wir Menschen möglicherweise nicht von vornherein konsistente Präfe-
renzenhaben–wederindividuellnochalsGruppe–,sodassesmöglicherweisenichtklarist,
wasKI-Systemefürunstunsollten.
23Midashättebesserdarangetan,wennersichandiegrundlegendenPrinzipienderSicherheitgehaltenhätte
undeine„Rückgängig“-undeine„Pause“-TasteinseinenWunscheingebauthätte.
57
--- PAGE 59 ---
1
Einleitung
ZUSAMMENFASSUNG
In diesem Kapitel wird die KI definiert und der kulturelle Hintergrund dargelegt, vor
demsiesichentwickelthat.DiewichtigstenPunktesind:
(cid:2) Unterschiedliche Menschen gehen mit unterschiedlichen Zielen an die KI heran.
ZweiwichtigeFragen,diegestelltwerdensollten,sind:GehtesIhnenumdasDenken
oderumdasVerhalten?WollenSiedenMenschenmodellieren,oderversuchen Sie,
optimaleErgebnissezuerzielen?
(cid:2) Legt man das sogenannte Standardmodell zugrunde, dann geht es in der KI haupt-
sächlichumrationalesHandeln.EinidealerintelligenterAgentergreiftdiebestmög-
licheHandlungineinerSituation.WiruntersuchendasProblem,Agentenzubauen,
dieindiesemSinneintelligentsind.
(cid:2) Diese einfache Idee muss in zwei Punkten verfeinert werden: Erstens ist die Fähig-
keit eines jeden Agenten, ob menschlich oder nicht, rationale Handlungen zu wäh-
len, durch die effiziente Machbarkeit der Berechnung begrenzt; zweitens muss das
KonzepteinerMaschine,dieeinbestimmtesZielverfolgt,durchdaseinerMaschine
ersetzt werden, die Ziele verfolgt, die den Menschen zugute kommen, wobei die
Maschineabernichtsicherweiß,wiedieseZielegenaulauten.
(cid:2) Philosophen (deren diesbezügliche Arbeiten bis 400 v.Chr. zurückgehen) schufen
den Nährboden fürdie KI, indem sie anregten, den Verstand in gewisser Weise wie
eine Maschine zu betrachten, der mit Wissen arbeitet, das in einer internen Spra-
checodiertist,undGedanken dazuverwendet werden können,umzuentscheiden,
welcheHandlungenausgeführtwerdensollen.
(cid:2) Mathematiker lieferten die Werkzeuge, um sowohl Aussagen mit logischer Gewiss-
heit als auch unsichere, probabilistische Aussagen zu handhaben. Sie legten auch
den Grundstein für das Verständnis von Berechenbarkeit und Schlussfolgern über
Algorithmen.
(cid:2) Wirtschaftswissenschaftler formalisierten das Problem, Entscheidungen zu treffen,
diedenerwartetenNutzenfürdenEntscheidungsträgermaximieren.
(cid:2) NeurowissenschaftlerentdeckteneinigeFaktendarüber,wiedasGehirnfunktioniert
undaufwelcheWeiseesComputernähnlichistbzw.sichvonihnenunterscheidet.
(cid:2) Psychologen übernahmen dieIdee, dass Menschen undTiere alsinformationsverar-
beitendeMaschinenbetrachtetwerdenkönnen.Linguistenzeigten,dassderSprach-
gebrauchindiesesModellpasst.
(cid:2) Computerspezialisten lieferten auf der Hardwareseite immer leistungsfähigere Ma-
schinen, dieKI-Anwendungen möglich machen, undmachten sie auf der Software-
seitebessernutzbar.
(cid:2) DieKontrolltheoriebefasstsichmitdemEntwurfvonGeräten,dieaufderGrundlage
von Feedback aus der Umgebung optimal agieren. Anfangs unterschieden sich die
mathematischen Werkzeuge der Kontrolltheorie völlig von denen der KI, doch die
Fachgebietenähernsichnuneinanderan.
(cid:2) In der Geschichte der KI gab es Zyklen von Erfolg, unangebrachtem Optimismus
unddarausresultierendenRückschrittensowohlinderBegeisterungalsauchinder
Finanzierung. Dochesgabauch Zyklen,indenen neue,kreativeAnsätze eingeführt
unddiebestendavonsystematischverfeinertwurden.
58
--- PAGE 60 ---
BibliografischeundhistorischeAnmerkungen
(cid:2) Die KI hat sich im Vergleich zu den ersten Jahrzehnten erheblich weiterentwickelt,
sowohl theoretisch als auch methodisch. Da die Probleme, mit denen sich die KI
befasst,immerkomplexerwurden,hatsichdasFachgebietvonderbooleschenLogik
zur probabilistischen Schlussfolgerung und vom selbst erarbeiteten Wissen zum
maschinellen Lernen aus Daten entwickelt. Dies hat zu Verbesserungen der Fähig-
keiten realer Systeme und zu einer stärkeren Integration mit anderen Disziplinen
geführt.
(cid:2) Da KI-Systeme in der realen Welt Anwendung finden, ist es notwendig geworden,
einegroßeBandbreitevonRisikenundethischenKonsequenzenzuberücksichtigen.
(cid:2) LängerfristigstehenwirvordemschwierigenProblem,superintelligenteKI-Systeme
zu kontrollieren, die sich möglicherweise auf unvorhersehbare Weise weiterentwi-
ckeln.DieLösungdiesesProblemsscheinteineÄnderungunsererVorstellungvonKI
zuerfordern.
Bibliografische und historische Anmerkungen
Eine umfassende Geschichte der KI wird von Nils Nilsson (2009) gegeben, einem der frü-
hen Pioniere des Fachgebiets. Pedro Domingos (2015) und Melanie Mitchell (2019) geben
Übersichten übermaschinelles Lernen füreinallgemeines PublikumundKai-FuLee(2018)
beschreibt das Rennen um die internationale Führung in der KI. Martin Ford (2018) inter-
viewt23führendeKI-Forscher.
Die wichtigsten KI-Fachgesellschaften sind die Association for the Advancement of Artifi-
cial Intelligence (AAAI), die ACM Special Interest Group in Artificial Intelligence (SIGAI,
früher SIGART), die European Association for AI und die Society for Artificial Intelligence
and Simulationof Behaviour (AISB). Die Partnership on AI bringt viele kommerzielle und
gemeinnützige Organisationen zusammen, diesich mitdenethischen undsozialen Auswir-
kungen der KI beschäftigen. Das AI Magazine der AAAI enthält viele aktuelle Artikel und
TutorialsundaufihrerWebsiteaaai.orgfindensichNachrichten,TutorialsundHintergrund-
informationen.
Die neuesten Arbeiten erscheinen in den Tagungsbändern der großen KI-Konferenzen: der
International Joint Conference on AI (IJCAI), der jährlichen European Conference on AI
(ECAI) und der AAAI-Konferenz. Maschinelles Lernen wird durch die International Con-
ference on Machine Learning (ICML, jetzt ICMLA) und die Tagung Neural Information
Processing Systems (NeurIPS) abgedeckt. Die wichtigsten Fachzeitschriften für allgemeine
KI sind Artificial Intelligence, Computational Intelligence, IEEE Transactions on Pattern
Analysis and Machine Intelligence, IEEE Intelligent Systems und das Journal of Artifi-
cial Intelligence Research. Es gibt auch viele Konferenzen und Zeitschriften, die sich mit
speziellenBereichenbefassen,diesegebenwirindenentsprechendenKapitelnan.
59
--- PAGE 62 ---
2
Intelligente Agenten
2.1 Agenten undUmgebungen ................................. 62
2.2 Gutes Verhalten:das KonzeptderRationalität .......... 64
2.2.1 Leistungsmessung ................................................ 65
2.2.2 Rationalität....................................................... 66
2.2.3 Allwissenheit,LernenundAutonomie........................... 66
2.3 ArtenvonUmgebungen..................................... 68
2.3.1 SpezifizierenderAufgabenumgebung ............................ 68
2.3.2 EigenschaftenvonAufgabenumgebungen ........................ 69
2.4 Die StrukturvonAgenten ................................... 73
2.4.1 Agentenprogramme............................................... 74
2.4.2 EinfacheReflexagenten........................................... 75
2.4.3 ModellbasierteReflexagenten..................................... 78
2.4.4 ZielbasierteAgenten.............................................. 79
2.4.5 NutzenbasierteAgenten .......................................... 80
2.4.6 LernendeAgenten................................................ 82
2.4.7 WiedieKomponentenvonAgentenprogrammenfunktionieren .. 84
--- PAGE 63 ---
2
IntelligenteAgenten
In diesem Kapiteldiskutieren wirdasWesen vonAgenten, ob perfekt odernicht, die
Vielfalt von Umgebungen sowie die daraus resultierende Menagerie von Agententy-
pen.
InKapitel1habenwirdasKonzeptderrationalenAgentenalszentralfürunserenAnsatzzur
künstlichenIntelligenzidentifiziert.IndiesemKapitelwollenwirdiesenBegriffnunkonkre-
tisieren.Wirwerdensehen,dassdasKonzeptderRationalitätaufeineVielzahlvonAgenten
angewendetwerdenkann,dieinjedererdenklichenUmgebungagieren.Wirmöchtenindie-
sem Buch anhand dieses Konzepts einige Entwurfsprinzipien für den Aufbau erfolgreicher
Agentenentwickeln–Systeme,diemitFugundRechtalsintelligentbezeichnetwerdenkön-
nen.
Wir beginnen mit der Untersuchung von Agenten, Umgebungen und der Verknüpfung zwi-
schen ihnen. Die Beobachtung, dass sich einige Agenten besser verhalten als andere, führt
zwangsläufigzuderIdeeeinesrationalenAgenten–einesAgenten,dersichsogutwiemög-
lich verhält. Wie gut sich ein Agent verhalten kann, hängt von der Art der Umgebung ab;
mancheUmgebungensindschwierigeralsandere.WirwerdenUmgebungengrobinKatego-
rieneinordnenundzeigen,wiedieEigenschafteneinerUmgebungdenEntwurfvonAgenten
beeinflussen, diefürdieseUmgebunggeeignet sind.Wirbeschreiben eineReihevongrund-
legenden „Gerüsten“ für den Entwurf von Agenten, die wir im weiteren Verlauf des Buchs
weiterausbauen.
2.1 Agenten und Umgebungen
Als Agent kann im Prinzip alles angesehen werden, was seine Umgebung mithilfe von
Sensoren wahrnimmt und durch Aktuatoren auf diese Umgebung einwirkt. Dieses einfa-
che Konzept ist in IAbbildung 2.1 dargestellt. Ein menschlicher Agent hat Augen, Ohren
und andere Organe als Sensoren sowie Hände, Beine, Vokaltrakt usw. als Aktuatoren.
Ein Roboter-Agent könnte Kameras und Infrarotentfernungsmesser als Sensoren und ver-
schiedene Motoren als Aktuatoren besitzen. Ein Software-Agent empfängt Dateiinhalte,
NetzwerkpaketeundmenschlicheEingaben(Tastatur/Maus/Touchscreen/Stimme) alssenso-
rischeEingabenundwirktaufdieUmgebungein,indemerDateienschreibt,Netzwerkpakete
Agent
Sensoren
Aktuatoren
Umgebung
Perzepte
?
Aktionen
Abbildung2.1:AgenteninteragierenmitderUmgebungdurchSensorenundAktuatoren.
62
--- PAGE 64 ---
2.1 AgentenundUmgebungen
sendet,InformationenaufdemBildschirmanzeigtoderTöneerzeugt. DieUmgebungkönnte
alles sein – das gesamte Universum! In der Praxis ist eine Umgebung allerdings nur der
TeildesUniversums,dessenZustandfürdenEntwurfdiesesAgentenrelevant ist–alsoder
Teil, der beeinflusst, was der Agent wahrnimmt, und der umgekehrt von den Aktionen des
Agentenbeeinflusstwird.
Wir verwenden den Begriff Perzept, um den Inhalt zu bezeichnen, den die Sensoren eines J
Agentenwahrnehmen.DiePerzeptfolgeeinesAgentenistdievollständigeHistorievonallem,
was der Agent jemals wahrgenommen hat. Im Allgemeinen kanndie Auswahleiner Aktion
durch den Agenten zu jedem beliebigen Zeitpunkt von seinem integrierten Wissen und von
der gesamten bisherigen Perzeptfolge abhängen, aber nicht von etwas, das der Agent nicht
wahrgenommen hat. Indem wir die Auswahl der Aktion eines Agenten für jede mögliche
Perzeptfolgespezifizieren,habenwirmehroderwenigerallesgesagt,wasesüberdenAgenten
zusagengibt.Mathematischbetrachtetsagenwir,dassdasVerhalteneinesAgentendurchdie
Agentenfunktionbeschriebenwird,diejedebeliebigePerzeptfolgeaufeineAktionabbildet.
Wir können uns vorstellen, die Agentenfunktion, die einen beliebigen Agenten beschreibt,
tabellarisch wiederzugeben. Für die meisten Agenten wäre dies allerdings eine sehr große
Tabelle – eigentlich unendlich groß, es sei denn, wir begrenzen die Länge der Perzeptfol-
gen,diewirberücksichtigenwollen.WirkönnenimPrinzipfüreinenAgenten,mitdemwir
experimentieren wollen,dieseTabelledadurcherstellen,indemwirallemöglichenPerzept-
folgen ausprobieren unddannaufzeichnen, welche AktionenderAgent alsReaktion darauf
ausführt.1 Die Tabelle ist natürlich eine externe Charakterisierung desAgenten. Intern wird
dieAgentenfunktionfüreinenkünstlichenAgentendurcheinAgentenprogrammimplemen-
tiert. Es ist wichtig, diese beiden Konzepte auseinanderzuhalten: Die Agentenfunktion ist
eineabstraktemathematischeBeschreibung–dasAgentenprogrammisteinekonkreteImple-
mentierung,dieineinemphysischenSystemausgeführtwird.
ZurVeranschaulichungdieserIdeenverwendenwireineinfachesBeispiel–dieStaubsauger-
welt,dieauseinemstaubsaugendenRoboter-Agentenbesteht,dersichineinerWeltausFel-
dern befindet, die jeweils entweder schmutzig oder sauber sein können. IAbbildung 2.2
zeigteineKonfigurationmitnurzweiFeldern,AundB.DerStaubsauger-Agentnimmtwahr,
inwelchemFeldersichbefindetundobesSchmutzindiesemFeldgibt.DerAgentstartetim
FeldA.DiemöglichenAktionensind:nachrechtsgehen,nachlinksgehen,denSchmutzauf-
1 WähltderAgentseineAktionennachdemZufallsprinzipaus,dannmüsstenwirjedeFolgemehrfachaus-
probieren,umdieWahrscheinlichkeitjederAktionzuermitteln.Mankönntemeinen,zufälligAktionenauszu-
wählenseiziemlichdumm,dochwirwerdenspäterindiesemKapitelsehen,dassesdurchaussehrintelligent
seinkann.
A B
Abbildung2.2:EineStaubsaugerweltmitnurzweiFeldern.JedesFeldkannsauberoderschmutzigseinundderAgentkann
sichnachlinksoderrechtsbewegenundkanndasFeldsäubern,indemersichbefindet.InverschiedenenVersionenderStaub-
saugerweltkannmanunterschiedlicheRegelndarüberimplementieren,wasderAgentwahrnehmenkann,obseineAktionen
immererfolgreichsindusw.
63
--- PAGE 65 ---
2
IntelligenteAgenten
Perzeptfolge Aktion
ŒA;Sauber(cid:2) Rechts
ŒA;Schmutzig(cid:2) Saugen
ŒB;Sauber(cid:2) Links
ŒB;Schmutzig(cid:2) Saugen
ŒA;Sauber(cid:2),ŒA;Sauber(cid:2) Rechts
ŒA;Sauber(cid:2),ŒA;Schmutzig(cid:2) Saugen
: :
: :
: :
ŒA;Sauber(cid:2),ŒA;Sauber(cid:2),ŒA;Sauber(cid:2) Rechts
ŒA;Sauber(cid:2),ŒA;Sauber(cid:2),ŒA;Schmutzig(cid:2) Saugen
: :
: :
: :
Abbildung2.3:AusschnittdertabellarischenDarstellungeinereinfachenAgentenfunktionfürdieinAbbildung2.2gezeigte
Staubsaugerwelt.DerAgentsäubertdasaktuelleFeld,wennesschmutzigist,ansonstengehterzumanderenFeld.Beachten
Sie,dassdieGrößederTabelleunbegrenztist,solangeeskeineBeschränkungderLängemöglicherPerzeptfolgengibt.
saugenodernichtstun.2EinesehreinfacheAgentenfunktionistdiefolgende:Wenndasaktu-
elleFeldschmutzigist,dannsaugedort;andernfallsbewegedichaufdasandereFeld.Einen
TeildertabellarischenDarstellungdieserAgentenfunktionsehenSieinIAbbildung2.3und
ein Agentenprogramm, dasdieFunktionimplementiert, istin IAbbildung2.8auf S.75 zu
sehen.
I Man kann anhand der Tabelle in IAbbildung 2.3 sehen, dass verschiedene Agenten der
Staubsaugerwelteinfachdadurchdefiniertwerdenkönnen,dassmandierechteSpalteunter-
schiedlichausfüllt.DieoffensichtlicheFrageistalso:WiekanndieTabellerichtigausgefüllt
werden? Mit anderen Worten: Was macht einen Agenten gut oder schlecht, intelligent oder
dumm?ImnächstenAbschnittwerdenwirdieseFragenbeantworten.
BevorwirdiesenAbschnittabschließen,solltenwirbetonen,dassdasKonzeptdesAgenten
alsWerkzeugzurAnalysevonSystemengedachtistundnichtalsabsoluteCharakterisierung,
diedieWelt inAgenten undNichtagenten einteilt.Mankönntesogareinen Taschenrechner
alsAgentenbetrachten,derwählt,beiderPerzeptfolge„2+2=“dieAktion„4“anzuzeigen,
doch eine solcheAnalyse würdeunskaum dabei helfen, den Taschenrechner zu verstehen.
IngewissemSinnelassensichsämtlicheBereichederTechnikalsEntwerfenvonArtefakten
betrachten,diemitderWeltinteragieren;dieKIarbeitet(nachAnsichtderAutoren)aminter-
essantesten EndedesSpektrums,andemdieArtefakteüberbeträchtlicheRechenressourcen
verfügenunddieAufgabenumgebungnichttrivialeEntscheidungsfindungvoraussetzt.
2.2 Gutes Verhalten: das Konzept der Rationalität
EinrationalerAgentisteinAgent,derdasRichtigetut.Offensichtlichistesbesser,dasRich-
tigezutun,alsdasFalschezutun,aberwasbedeutetes,„dasRichtige“zutun?
2 BeieinemechtenRobotergäbeesAktionenwie„nachrechtsgehen“und„nachlinksgehen“wahrscheinlich
nicht.StattdessenwürdendieAktionenlauten:„Rädervorwärtsdrehen“und„Räderrückwärtsdrehen“.Wir
habenunsjedochfürdieseBenennungentschieden,umdieAktionenleichteraufderSeitenachvollziehenzu
können,nichtfürdieeinfachereImplementierungeinesechtenRoboters.
64
--- PAGE 66 ---
2.2 GutesVerhalten:dasKonzeptderRationalität
2.2.1 Leistungsmessung
Innerhalb der Ethik wurden verschiedene Vorstellungen vom „Richtigen“ entwickelt, aber
die KI hat sich im Allgemeinen an ein Konzept gehalten, das man als Konsequenzialismus
bezeichnet: Wir bewerten das Verhalten eines Agenten anhand seiner Konsequenzen. Wird
einAgent ineineUmgebunghineingestellt, danngeneriert eranhandderempfangenen Per-
zepte eineFolgevonHandlungen. Diese Handlungssequenz führt dazu, dassdieUmgebung
eineAbfolgevonZuständendurchläuft.IstdieFolgewünschenswert, sohatderAgent eine
guteLeistungerbracht.DieserBegriffderErwünschtheitwirddurcheinLeistungsmaßerfasst,
dasjedebeliebigeFolgevonUmgebungszuständenbewertet.
MenschenhabeneigeneWünscheundPräferenzen,dahergehtesbeidemKonzeptderRatio-
nalität, angewandt auf Menschen, darum, wie erfolgreich Handlungen gewählt werden, aus
denendannSequenzenvonUmgebungszuständenentstehen,dieausihrerSichtwünschens-
wert sind. Maschinen hingegen haben keine eigenen Wünsche und Präferenzen; die Leis-
tungsbewertung findet, zumindest anfangs, im Kopf des Entwicklers der Maschine oder im
Kopf der Benutzer statt, für die die Maschine konzipiert ist. Wir werden sehen, dass einige
Agentenentwürfe eineexpliziteRepräsentation(einerVersion)desLeistungsmaßesbesitzen,
während in anderen Entwürfen das Leistungsmaß vollständig implizit ist – der Agent mag
vielleicht„dasRichtige“tun,docherweißnicht,warum.
ErinnernwirunsanNorbertWienersWarnung,dieunsmahntsicherzustellen,dass„dasZiel,
daswirderMaschineeingeben,dasselbeZielist,daswirtatsächlichhabenmöchten“(S.57),
so stellen wir fest, dass es ziemlich schwierig sein kann, ein Leistungsmaß korrekt zu for-
mulieren.BetrachtenwirzumBeispieldenStaubsauger-AgentenausdemvorigenAbschnitt.
Wirkönntenempfehlen,dieLeistunganhandderMengeanSchmutzzumessen,dieineiner
Acht-Stunden-Schichtaufgesaugtwird.BeieinemrationalenAgentenistdas,wasSieverlan-
gen,natürlichauchdas,wasSiebekommen.EinrationalerAgentkanndiesesLeistungsmaß
maximieren, indem erdenSchmutzaufsaugt, dannwieder alles auf denBoden kippt,dann
wiedersaugtundsoweiter.Eingeeigneteres LeistungsmaßwürdedenAgentendafürbeloh-
nen, einen sauberen Boden zu haben. Zum Beispiel könntefürjedes saubere Feld pro Zeit- J
schritt ein Punkt vergeben werden (vielleicht mit einem Abzug für verbrauchte Elektrizität
underzeugtenLärm).Generellistesbesser,dieLeistungsmaßedanachzugestalten,wasman
inderUmgebungtatsächlicherreichenmöchte,undnichtdanach,wiemanmeint,dasssich
derAgentverhaltensoll.
SelbstwenndieoffensichtlichenFallstrickeumgangenwerden, bleibeneinigekniffligePro-
blemebestehen.ZumBeispielbasiertderBegriff„saubererBoden“imvorigenAbsatzaufder
durchschnittlichenSauberkeitimLaufederZeit.DochdieselbedurchschnittlicheSauberkeit
kann von zwei unterschiedlichen Agenten erreicht werden, von denen einer die ganze Zeit
übereinemittelmäßigeArbeitleistet,währendderandereenergischsaugt,aberlangePausen
macht. Was vorzuziehen ist, erscheint zwar wie eine Spitzfindigkeit aus der Hausmeister-
wissenschaft,istabertatsächlicheinetiefgreifendephilosophischeFragemitweitreichenden
Implikationen. Was ist besser – ein unbekümmertes Leben mit Höhen und Tiefen oder eine
sichere,abereintönigeExistenz?Wasistbesser–eineWirtschaft,inderalleinmäßigerArmut
leben,odereine,indereinigeimÜberflussleben,währendanderesehrarmsind?Wirüber-
lassendemeifrigenLeserdieseFragenalsÜbung.
WirwerdenindiesemBuchweitestgehendvoraussetzen,dassdasLeistungsmaßkorrektange-
geben werden kann. Aus den oben genannten Gründen müssen wir jedoch die Möglichkeit
inKaufnehmen,dasswirderMaschineeinfalschenZielangeben–dasistgenaudasKönig-
Midas-Problem, das wir auf S. 57 beschrieben haben. Außerdem können wir bei der Ent-
wicklungeinerSoftware, deren Kopienanverschiedene Benutzerverteilt werden, nichtdie
genauenPräferenzenjedeseinzelnenBenutzersvorhersehen.AusdiesemGrundmüssenwir
möglicherweise Agenten erstellen, bei denen das richtige Leistungsmaß anfangs noch unsi-
cheristundimLaufederZeitverfeinertwird;solcheAgentenwerdenindenKapiteln 16,18
und22beschrieben.
65
--- PAGE 67 ---
2
IntelligenteAgenten
2.2.2 Rationalität
WaszueinembestimmtenZeitpunktrationalist,hängtvonvierDingenab:
(cid:2) demLeistungsmaß,dasdasErfolgskriteriumdefiniert,
(cid:2) demVorwissendesAgentenüberdieUmgebung,
(cid:2) denAktionen,diederAgentausführenkann,
(cid:2) derbisherigenPerzeptfolgedesAgenten.
I
DiesePunkteführenunszueinerDefinitioneinesrationalenAgenten:
Ein rationaler Agent soll für jede mögliche Perzeptfolge eine Aktion wählen, von der erwartet
wird,dasssieseinLeistungsmaßmaximiert,wenndieErgebnissederPerzeptfolgesowiejegliches
VorwissendesAgentenberücksichtigtwerden.
BetrachtenwirdeneinfachenStaubsauger-Agenten,dereinFeldsäubert,wennesschmutzig
ist,undsichandernfallszumnächstenFeldbewegt–diesistdieinIAbbildung2.3tabella-
rischdargestellteAgentenfunktion.HandeltessichdabeiumeinenrationalenAgenten?Das
kommtdaraufan!Zuerstmüssenwirangeben,wasdasLeistungsmaßist,wasüberdieUmge-
bungbekanntistundwelcheSensorenundAktuatorenderAgenthat.WirwollenFolgendes
annehmen:
(cid:2) DasLeistungsmaßvergibteinenPunktfürjedessaubereFeldproZeitschritt,undzwarüber
eine„Lebensdauer“von1.000Zeitschritten.
(cid:2) Die„Geografie“derUmgebungistaprioribekannt(IAbbildung2.2),aberdieSchmutzver-
teilungunddieAnfangspositiondesAgentensindesnicht.SaubereFelderbleibensauber
unddurchSaugenwirddasaktuelleFeldgereinigt.DieAktionenRechtsundLinksbewe-
gen den Agenten um ein Feld in der entsprechenden Richtung weiter, es sei denn, dies
würdedenAgentenausderUmgebungherausführen;indiesemFallbleibtderAgent,wo
erist.
(cid:2) DieeinzigenverfügbarenAktionensindRechts,LinksundSaugen.
(cid:2) DerAgentnimmtseinePositionkorrektwahrunderkennt,obseinStandortschmutzigist.
UnterdiesenUmständenistderAgenttatsächlichrational;seineerwartete Leistungistmin-
destenssogutwiediejedesanderenAgenten.
Man kann leicht nachvollziehen, dass derselbe Agent unter anderen Umständen irrational
wäre. Zum Beispiel wird der Agent, sobald der gesamte Schmutz beseitigt ist, unnötig hin-
und herpendeln – beinhaltet das Leistungsmaß eine Strafe voneinem Punkt fürjede Bewe-
gung, so wird der Agent schlecht abschneiden. Ein für diesen Fall besserer Agent würde
nichts tun, sobald er sicher ist, dass alle Felder sauber sind. Falls saubere Felder wieder
schmutzigwerdenkönnen,solltederAgentsiegelegentlichüberprüfenundbeiBedarferneut
reinigen. IstdieGeografie derUmgebungunbekannt,dannmussderAgent sieerkunden.In
Übung2.2aufderWebsitesollenSieAgentenfürsolcheFälleentwerfen.
2.2.3 Allwissenheit, LernenundAutonomie
WirmüssensorgfältigzwischenRationalitätundAllwissenheitunterscheiden.Einallwissen-
derAgentkenntdastatsächlicheErgebnisseinerAktionenundkannentsprechendhandeln–
inderRealität istAllwissenheit allerdingsunmöglich.Betrachten SiedasfolgendeBeispiel:
Ich gehe eines Tages auf den Champs Elysées spazieren und sehe einen alten Freund auf
der anderen Straßenseite. Es gibt um mich herum keinen Straßenverkehr und ich bin nicht
anderweitigbeschäftigt,alsoistesrationalvonmir,dieStraßezuüberqueren.Währenddessen
66
--- PAGE 68 ---
2.2 GutesVerhalten:dasKonzeptderRationalität
fällt in 10.000Meter Höheeine Frachttür aus einem über mirfliegenden Passagierflugzeug3
undbevorichesaufdieandereStraßenseiteschaffe,binichplatt.Waresirrationalvonmir,
dieStraßezuüberqueren?Esistunwahrscheinlich, dassinmeinerTodesanzeige steht:„Der
DummkopfstarbbeimVersuch,dieStraßezuüberqueren.“
Dieses Beispiel zeigt, dass Rationalität nicht mit Perfektion gleichzusetzen ist. Rationalität
maximiert die erwartete Leistung, während Perfektion die tatsächliche Leistung maximiert.
WennwirvonderForderungnachPerfektionabrücken,istdasnichtnureineFragederFair-
ness gegenüber Agenten. Der springende Punktist: Wennwir voneinem Agenten erwarten,
dasserdastut,wassichimNachhineinalsdiebesteAktionherausstellt,dannistesunmög-
lich,einenAgentenzuentwerfen,derdieseVorgabeerfüllt–esseidenn,wirverbesserndie
LeistungvonKristallkugelnoderZeitmaschinen.
FürunsereDefinitionvonRationalitätbenötigenwiralsokeineAllwissenheit,denndieratio-
naleWahlhängtnurvonderbisherigenPerzeptfolgeab.Wirmüssenaußerdemsicherstellen,
dasswirdemAgentennichtversehentlicherlaubthaben,ausgesprochendummeAktivitäten
auszuführen.SchauteinAgentz.B.nichtnachrechtsundnachlinks,bevorereinevielbefah-
rene Straßeüberquert, dannwird erseiner Perzeptfolge nicht entnehmen können,dass sich
eingroßerLkwmithoherGeschwindigkeitnähert.BesagtunsereDefinitionvonRationalität,
dassesjetztinOrdnungist,dieStraßezuüberqueren?Weitgefehlt!
Erstens wäre es angesichts dieser nicht informativen Perzeptfolge nicht rational, die Straße
zu überqueren: Das Unfallrisiko beim Überqueren, ohne nach rechts und links zu schauen,
ist zu groß. Zweitens sollteeinrationaler Agent dieAktion„Schauen“ wählen, bevorerauf
die Straße tritt, weil dies hilft, die erwartete Leistung zu maximieren. Das Ausführen von
Aktionen,umzukünftigePerzeptezuverändern–manchmalauchalsInformationsbeschaf-
fungbezeichnet –,ist ein wichtiger Teil derRationalität undwird in Kapitel16ausführlich
behandelt.EinzweitesBeispielfürdasSammelnvonInformationenistdieExploration,die
einStaubsauger-AgentineinerzunächstunbekanntenUmgebungdurchführenmuss.
Unsere Definition verlangt von einem rationalen Agenten nicht nur, dass er Informationen
sammelt, sondern er soll auch so viel wie möglich lernen aus dem,was er wahrnimmt. Die
anfängliche Konfiguration des Agenten könnte ein gewisses Vorwissen über die Umgebung
widerspiegeln, doch sobald der Agent an Erfahrung gewinnt, kann dieses Wissen modifi-
ziert underweitert werden. EsgibtExtremfälle,indenendieUmgebung apriorivollständig
bekanntundvollständigvorhersagbarist.InsolchenFällenmussderAgentnichtswahrneh-
menoderlernen;erhandelteinfachkorrekt,wennerentsprechendprogrammiertwird.
Agenten, die weder wahrnehmen noch lernen können,sind natürlich anfällig. Nehmen wir
den einfachen Mistkäfer: Nachdem er sein Nest gegraben und seine Eier abgelegt hat, holt
er eine Mistkugel von einem nahe gelegenen Haufen, um den Eingang zu verstopfen. Wird
ihm die Mistkugel auf dem Weg dorthin abgenommen, dann setzt der Käfer seine Aufgabe
trotzdemfort,erverstopftdasNestpantomimischmitdernichtvorhandenenMistkugelund
bemerkt nicht einmal, dass sie fehlt. Die Evolution hat eine Annahme in das Verhalten des
Käferseingebautundwenndieseverletztwird,resultiertdarauseinerfolglosesVerhalten.
EinwenigintelligenteristdieSphex,eineGattungderGrabwespe.DasSphex-Weibchengräbt
eineHöhle,kommtheraus,stichteineRaupeundschlepptsievordieHöhle,betrittdieHöhle
erneut, umzu prüfen,oballes in Ordnungist,zieht dieRaupehinein undlegt ihreEierab.
DieRaupedientalsNahrungsquellefürdieLarven,dieausdenEiernschlüpfen.Soweit,so
gut.Dochwenn einEntomologedieRaupeumeinpaarZentimeterverschiebt, während die
Sphex ihren Kontrollgang durchführt,dann kehrt sie zumSchritt „die Raupe vordieHöhle
schleppen“ihresPlanszurückundwirddiesenunverändertfortsetzen,kontrolliertdieHöhle
erneut,anstattdieRaupedirektindieHöhlezuziehen–unddasauch,nachdemdieRaupe
3 SieheN.Henderson,„NewdoorlatchesurgedforBoeing747jumbojets“,WashingtonPost,24.August1989.
67
--- PAGE 69 ---
2
IntelligenteAgenten
mehrfachverschobenwurde.DieSphexistnichtinderLagezulernen,dassihrangeborener
Planfehlschlägt,undwirdihndahernichtändern.
VerlässtsicheinAgentaufdasVorwissenseinesEntwicklersanstattaufseineeigenenWahr-
nehmungenundLernprozesse,dannsprechenwirdavon,dassesdemAgentenanAutonomie
mangelt.EinrationalerAgentsollteautonomsein–ersolltesovielwiemöglichlernen,um
unvollständigesoderfalschesVorwissen zukompensieren.EinStaubsauger-Agent, derlernt
vorherzusehen, wo und wann zusätzlicher Schmutz auftaucht, wird beispielsweise besser
abschneidenalseinAgent,derdasnichtkann.
InderPraxisistesseltenerforderlich,dasseinAgentvonAnfanganvölligautonomist:Wenn
derAgentwenigodergarkeineErfahrunghat,müssteerzufällighandeln,fallsihmseinEnt-
wicklerkeineHilfestellungmitgegebenhat.GenausowiedieEvolutionTieremitausreichend
angeborenenReflexenausstattet,damitsielangegenugüberleben,bissieselbstlernen,wäre
es vernünftig, einen künstlichen intelligenten Agenten mit einem gewissen Anfangswissen
sowie einer Fähigkeit zum Lernen auszustatten. Nach ausreichender Erfahrung mit seiner
Umgebung kann das Verhalten eines rationalen Agenten praktisch unabhängig von seinem
Vorwissenwerden.DieEinbeziehungdesLernensermöglichtesalso,eineneinzigenrationa-
lenAgentenzuentwerfen,derineinerVielzahlvonUmgebungenerfolgreichist.
2.3 Arten von Umgebungen
DawirnuneineDefinitionderRationalitäthaben,sindwirfastsoweit,überdieEntwicklung
rationaler Agenten nachzudenken. Zunächst müssen wir uns jedoch Gedanken über Aufga-
benumgebungenmachen,dieimWesentlichendie„Probleme“darstellen,deren„Lösungen“
dierationalen Agenten sind.AlsErsteszeigen wir,wiemaneineAufgabenumgebung spezi-
fiziert, undillustrieren diesen Prozess anhand einerReihevonBeispielen. Dann stellen wir
Aufgabenumgebungen in unterschiedlichen Varianten vor. Die Art der Aufgabenumgebung
wirktsichdirektdaraufaus,welcherEntwurffürdasAgentenprogrammgeeignetist.
2.3.1 Spezifizieren der Aufgabenumgebung
In unserer Diskussion zur Rationalität des einfachen Staubsauger-Agenten mussten wir das
Leistungsmaß,dieUmgebungsowiedieAktuatorenundSensorendesAgentenangeben.Dies
allesfassenwirnununterdemOberbegriffderAufgabenumgebungzusammen.Dawirgerne
Akronyme benutzen, nennen wir dies die PEAS-Beschreibung (von Performance, Environ-
ment,Actuators,Sensors–Leistung,Umgebung,Aktuatoren,Sensoren).BeimEntwurfeines
Agenten muss es immer der erste Schritt sein, die Aufgabenumgebung so vollständig wie
möglichzuspezifizieren.
Die Staubsaugerwelt war ein einfaches Beispiel – betrachten wir nun ein komplexeres Pro-
blem:einenautomatisiertenTaxifahrer.IAbbildung2.4fasstdiePEAS-Beschreibungfürdie
AufgabenumgebungdesTaxiszusammen.IndenfolgendenAbschnittenbesprechenwirjedes
ElementimDetail.
AlsErstes fragen wir, welches Leistungsmaß wirfürunseren automatisierten Fahrer anstre-
benmöchten.Zudenwünschenswerten Eigenschaften gehören:daskorrekteZielerreichen,
denKraftstoffverbrauch unddenVerschleiß minimieren,dieFahrzeitoder-kostenminimie-
ren, Verstöße gegen Verkehrsregeln sowie die Behinderungen anderer Fahrer minimieren,
SicherheitundFahrgastkomfortsowiedenGewinnmaximieren.Offensichtlichsteheneinige
dieserZieleimWiderspruchzueinander,sodassKompromisseeingegangenwerdenmüssen.
DienächsteFrageist:WiesiehtdieFahrumgebungdesTaxisaus?JederTaxifahrermussmit
einer Vielzahl von Straßen zurechtkommen, von ländlichen Wegen und städtischen Gassen
68
--- PAGE 70 ---
2.3 ArtenvonUmgebungen
Agententyp Leistungsmaß Umgebung Aktuatoren Sensoren
Taxifahrer Sicher,schnell,legaleFahrweise, Straßen, Lenkrad,Gaspedal, Kameras,Radar,Tachometer,
komfortableFahrt, andererVerkehr, Bremspedal, GPS,Motorsensoren,
maximiertGewinne, Polizei,Fußgänger, Blinker,Hupe, Beschleunigungsmesser,
minimiertAuswirkungauf Fahrgäste,Wetter Bildschirmanzeige, Mikrofone,
andereVerkehrsteilnehmer Sprache Touchscreenbildschirm
Abbildung2.4:PEAS-BeschreibungderAufgabenumgebungfüreinenautomatisiertenTaxifahrer.
bis zu vielspurigen Autobahnen. Auf den Straßen gibt es andere Verkehrsteilnehmer, Fuß-
gänger, kreuzende Wildtiere, Baustellen, Polizeiautos, Pfützen undSchlaglöcher. Außerdem
muss das Taxi mit potenziellen und tatsächlichen Fahrgästen kommunizieren. Und es gibt
einigeWahlmöglichkeiten.DasTaxikönnteinSüdkalifornieneingesetzt werden,woSchnee
selteneinThemaist–oderinAlaska,woesseltenkeinsist.Eskönnteimmeraufderrechten
Seitefahren–odermöchtenwir,dassesflexibelgenugist,umaufderlinkenSeitezufahren,
wenn es sich in Großbritannien oder Japan befindet? Je eingeschränkter die Umgebung ist,
destoeinfacheristnatürlichdasEntwurfsproblem.
DieAktuatorendesautomatisiertenTaxissindimPrinzipdieselben,dieaucheinemmensch-
lichen Fahrer zur Verfügung stehen: Bedienung des Motorsdurch das Gaspedal und Steue-
rung über Lenkungund Bremsen. Darüber hinaus wird eine Ausgabe auf einem Bildschirm
oder über einen Sprachsynthesizer benötigt, damit das Taxi mit den Fahrgästen sprechen
kann,undvielleichteineMöglichkeit,ummitanderenFahrzeugen–höflichoderanderwei-
tig–zukommunizieren.
ZudengrundlegendenSensorenfürdasTaxiwerdeneineodermehrereVideokamerasgehö-
ren,damitesetwassehenkann,sowieLidar-undUltraschallsensoren,umAbständezuande-
renAutosundHindernissenzuerkennen.UmGeschwindigkeitsüberschreitungenzuvermei-
den,solltedasTaxieinenTachometerhaben,undumdasFahrzeugvoralleminKurvenrich-
tigzusteuern,sollteeseinenBeschleunigungsmesser besitzen. ZurFeststellungdesmecha-
nischenZustandsdesFahrzeugs,benötigtesdieüblichenSensorenfürMotor,Kraftstoffund
Elektrik. Wie viele menschliche Fahrer wird es möglicherweise auf GPS-Signale zugreifen
wollen, damit es sich nicht verirrt. Schließlich benötigt es Touchscreen- oder Spracheinga-
ben,damitderFahrgastseinZielmitteilenkann.
InIAbbildung2.5skizzierenwirdiegrundlegendenPEAS-ElementefüreineReihezusätzli-
cherAgententypen.WeitereBeispielefindenSieinÜbung2.5aufderWebsite.DieBeispiele
umfassensowohlphysischealsauchvirtuelleUmgebungen.BeachtenSie,dassvirtuelleAuf-
gabenumgebungen genauso komplex sein können wie die „reale“ Welt: Ein Software-Agent
(auchalsSoftware-RoboteroderSoftbotbezeichnet), deraufAuktions-undWiederverkaufs-
plattformenhandelt,hatbeispielsweisemitMillionenandererBenutzerundMilliardenvon
Objektenzutun,daruntervielemitrealenBildern.
2.3.2 EigenschaftenvonAufgabenumgebungen
Die Bandbreite der Aufgabenumgebungen, diein der KI auftreten können,ist offensichtlich
riesig. WirkönnenjedocheinerelativkleineAnzahl anDimensionenidentifizieren,entlang
dererAufgabenumgebungenkategorisiertwerdenkönnen.DieseDimensionenlegenzueinem
großenTeildengeeignetenEntwurffüreinenAgentensowiedieAnwendbarkeitdergrundle-
gendenFamilienvonVerfahrenfürdieImplementierungfest.ZunächstlistenwirdieDimen-
sionen auf, dann analysieren wir mehrere Aufgabenumgebungen, um die Konzepte zu ver-
deutlichen. Die Definitionen hier sind informell; in späteren Kapiteln finden Sie genauere
AussagenundBeispielefürdieeinzelnenUmgebungen.
69
--- PAGE 71 ---
2
IntelligenteAgenten
Agententyp Leistungsmaß Umgebung Aktuatoren Sensoren
Medizinisches GesunderPatient, Patient,Krankenhaus, AnzeigevonFragen, Touchscreen/
Diagnosesystem verringerteKosten Mitarbeiter Untersuchungen, Spracheingabevon
Diagnosen, Symptomenund
Behandlungen Befunden
Satellitenbild- KorrekteObjekt-und Satellitinder Anzeigeder Hochauflösende
Analysesystem Geländekategorisie- Umlaufbahn, Szenenkategorisierung Digitalkamera
rung Downlink,Wetter
Pack-Roboterfür Prozentsatzder Förderbandmit Gelenkarmund-hand Kamera,taktileund
Werkstücke Werkstückein Werkstücken;Behälter Gelenkwinkelsensoren
korrektenBehältern
Raffinerie-Controller Reinheit,Ausbeute, Raffinerie, Ventile,Pumpen, Temperatur-,Druck-,
Sicherheit Rohmaterialien, Heizungen,Rührwerke, Durchfluss-und
Arbeiter Anzeigen chemischeSensoren
Interaktiver Punktzahl/Notedes Schülergruppe, AnzeigevonÜbungen, Tastatureingabe,
Englischlehrer SchülersimTest Prüfungskommission Feedback,Sprache Stimme
Abbildung2.5:BeispielefürAgententypenundihrePEAS-Beschreibungen.
Vollständigbeobachtbar–teilweisebeobachtbar:WenndieSensoreneinesAgentenihmzu
jedemZeitpunktZugriffaufdenvollständigenZustandderUmgebunggestatten,dannsagen
wir, dass die Aufgabenumgebung vollständig beobachtbar ist. Eine Aufgabenumgebung ist
faktischvollständigbeobachtbar, wenndieSensoren alleAspekteerfassen, diefürdieWahl
der Aktion relevant sind; die Relevanz hängt wiederum vom Leistungsmaß ab. Vollständig
beobachtbare Umgebungen sind praktisch, weil der Agent keinen internen Zustand verwal-
tenmuss,umdieWeltimAugezubehalten.EineUmgebungistmöglicherweisenurteilweise
beobachtbar, weil die Sensoren verrauscht und ungenau sind oder weil Teile des Zustands
einfachnichtindenSensordatenenthaltensind–zumBeispielkanneinStaubsauger-Agent,
der mit einem lokalen Schmutzsensor ausgestattet ist, nicht erkennen, ob sich in anderen
FeldernSchmutzbefindet,undeinautomatisiertesTaxikannnichtsehen,wasandereFahrer
denkenoderplanen.WennderAgentüberhauptkeineSensorenhat,dannistdieUmgebung
unbeobachtbar.Mankönntemeinen,dassdieLagedesAgenteninsolchenFällenhoffnungs-
losist,dochwiewirinKapitel4besprechenwerden,könnendieZieledesAgentendennoch
–manchmalsogarabsolut–erreichbarsein.
Einzelagent – Multiagent: Die Unterscheidung zwischen Einzelagenten- und Multiagenten-
umgebungen scheint einfach zu sein. Zum Beispiel befindet sich ein Agent, der alleine ein
Kreuzworträtsel löst, offensichtlich in einer Einzelagentenumgebung, während ein Agent,
der Schach spielt, in einer Zwei-Agenten-Umgebung ist. Es gibt jedoch einige subtile Pro-
bleme.Wirhabenzwarbeschrieben,wieeineEntitätalsAgentbetrachtetwerdenkann,aber
wir haben nicht geklärt, welche Entitäten als Agenten betrachtet werden müssen. Muss ein
Agent A(z.B.derTaxifahrer) einObjektB(einanderes Fahrzeug) alsAgentbehandeln oder
kannereseinfachalseinObjektansehen, dassichgemäßdenphysikalischenGesetzen ver-
hält, wie Wellen am Strand oder Blätter, die im Wind wehen? Das wesentliche Unterschei-
dungsmerkmalist,obdasVerhaltenvonBambestenalsMaximierungeinesLeistungsmaßes
beschriebenwird,dessenWertvomVerhaltenvonAgentAabhängt.
BeimSchachspielzumBeispielversuchtdiegegnerischeEntitätBihrLeistungsmaßzumaxi-
mieren, was nach den Regeln des Schachspiels das Leistungsmaß von Agent A minimiert.
SchachistalsoeinekonkurrierendeMultiagentenumgebung.InderTaxi-Umgebungdagegen
verbessert dieVermeidungvonKollisionendasLeistungsmaßallerAgenten, eshandeltsich
70
--- PAGE 72 ---
2.3 ArtenvonUmgebungen
also um eine partiell kooperative Multiagentenumgebung. Sie besitzt aber auch konkurrie-
rendeAspekte,z.B.weiljeweilsnureinAutoeinenParkplatzbelegenkann.
Die Probleme beim Entwurf von Agenten in Multiagentenumgebungen sind häufig etwas
anderealsinEinzelagentenumgebungen; zumBeispielerweistsichKommunikationinMul-
tiagentenumgebungen oftals rationales Verhalten; in einigen konkurrierenden Umgebungen
istzufälligesVerhaltenrational,weilesdieFallstrickederVorhersagbarkeitvermeidet.
Deterministisch –nichtdeterministisch. WenndernächsteZustandderUmgebungvollstän-
dig durch den aktuellen Zustand sowie durch die von dem oder den Agenten ausgeführte
Aktionfestgelegtwird,dannsagenwir,dassdieUmgebungdeterministischist;andernfallsist
sienichtdeterministisch.ImPrinzipmusssicheinAgentineinervollständigbeobachtbaren,
deterministischen UmgebungkeineGedanken überUnsicherheit machen.IstdieUmgebung
jedochnurteilweisebeobachtbarist,könntesiealsnichtdeterministischerscheinen.
Die meisten realen Situationen sind so komplex,dass es unmöglich ist, den Überblick über
alle unbeobachteten Aspekte zu behalten; für praktische Zwecke müssen sie als nichtdeter-
ministischangesehen werden.DieTaxi-UmgebungistindiesemSinneeindeutignichtdeter-
ministisch,weilmandasVerkehrsgeschehenniegenauvorhersagenkann;außerdemkannes
passieren, dassdieReifenunerwartetplatzenoderderMotorohneVorwarnungausfällt.Die
Staubsaugerwelt, so wie wirsie beschrieben haben, ist deterministisch, aberVarianten kön-
nen nichtdeterministische Elementeenthalten, z.B.Schmutz,derzufällig dazukommt,oder
einunzuverlässigerSaugmechanismus(Übung2.16aufderWebsite).
EineletzteAnmerkung:DerBegriffstochastischwirdvonmanchenalsSynonymfür„nicht-
deterministisch“ verwendet, doch wir unterscheiden zwischen den beiden Begriffen – wir
sagen,dasseinModellderUmweltstochastischist,wennesexplizitmitWahrscheinlichkei-
tenarbeitet(z.B.„esbestehteine25%igeChance,dassesmorgenregnet“),und„nichtdeter-
ministisch“, wenn dieMöglichkeiten aufgelistet werden, ohnedasssiequantifiziert werden
(z.B.„esbestehtdieChance,dassesmorgenregnet“).
Episodisch – sequenziell: In einer episodischen Aufgabenumgebung ist die Erfahrung des
Agenten in atomare Episoden unterteilt. In jeder Episode empfängt der Agent ein Perzept
und führt dann eine einzelne Aktion aus. Entscheidend ist, dass die nächste Episode nicht
vondenAktionendervorhergehendenEpisodenabhängt.VieleKlassifikationsaufgabensind
episodisch. Beispielsweise basiert jede Entscheidung eines Agenten, der defekte Teile auf
einem Fließband erkennen soll, nur auf dem aktuellen Teil und ist unabhängig von frühe-
renEntscheidungen;außerdemhatdieaktuelleEntscheidungkeinenEinflussdarauf,obdas
nächsteTeildefektist.InsequenziellenUmgebungenhingegenkönntedieaktuelleEntschei-
dungallezukünftigenEntscheidungenbeeinflussen.4 SchachundTaxifahrensindsequenzi-
ell: In beiden Fällen können kurzfristige Aktionen langfristige Konsequenzen haben. Episo-
discheUmgebungen sindvieleinfacher alssequenzielle Umgebungen, weil derAgent nicht
vorausdenkenmuss.
Statisch–dynamisch:WennsichdieUmgebungändernkann,während einAgenteineEnt-
scheidung trifft, dann sprechen wir davon, dass die Umgebung für diesen Agenten dyna-
misch ist;andernfalls istsiestatisch. StatischeUmgebungen sindeinfach zuhandhaben,da
der Agent die Welt nicht ständig beobachten muss, während er sich für eine Aktion ent-
scheidet, und er muss sich auch keine Gedanken über den Zeitablauf machen. Dynamische
UmgebungenhingegenfragendenAgentenständig,wasertunmöchte;hatersichnochnicht
entschieden,sowirddasalsEntscheidunggewertet,nichtszutun.ÄndertsichimLaufeder
Zeit nicht dieUmgebung, sondern dasLeistungsmaß desAgenten, dann sagen wir, dassdie
Umgebung semidynamisch ist. Taxifahren ist offensichtlich dynamisch: Die anderen Autos
4 Das Wort „sequenziell“ wird in der Informatik auch als Antonym von „parallel“ verwendet. Die beiden
Bedeutungensindaberweitgehendunabhängigvoneinander.
71
--- PAGE 73 ---
2
IntelligenteAgenten
unddasTaxiselbstbewegensich,währendderFahralgorithmusberechnet,wasalsNächstes
zutunist.Schachist,wennesmiteinerUhrgespieltwird,semidynamisch.Kreuzworträtsel-
löseniststatisch.
Diskret – stetig: Die Unterscheidung diskret/stetig bezieht sich auf den Zustand der Umge-
bung,aufdieArtundWeise,wiedieZeitgehandhabtwird,sowieaufdiePerzepteundAktio-
nendesAgenten.ZumBeispielhatdieSchachumgebungeineendlicheAnzahlunterschied-
licherZustände(vonderUhrabgesehen).AußerdembesitztSchacheinediskreteMengevon
PerzeptenundAktionen.TaxifahrenisteineProblemstellungmitstetigenZuständenundste-
tigerZeit:DieGeschwindigkeitundderStandortdesTaxisundderanderenFahrzeugedurch-
laufeneinenBereichstetigerWerte,undzwargleichmäßigüberderZeit.AuchdieAktionen
desTaxifahrens sindstetig (Lenkwinkelusw.). DieEingaben vonDigitalkameras sind streng
genommen diskret, werden aber typischerweise so behandelt, als würden sie stetig variie-
rendeIntensitätenundPositionendarstellen.
Bekannt – unbekannt: Streng genommen bezieht sich diese Unterscheidung nicht auf die
Umgebungselbst,sondernaufdenWissensstanddesAgenten(oderseinesEntwicklers)über
die„physikalischen Gesetze“ derUmgebung. Ineinerbekannten UmgebungsinddieErgeb-
nisse(oderErgebniswahrscheinlichkeiten, wenndieUmgebungnichtdeterministischist)für
alleAktionengegeben. IstdieUmgebungunbekannt,dannmussderAgentnatürlichlernen,
wiesiefunktioniert,umguteEntscheidungentreffenzukönnen.
Die Unterscheidung zwischen bekannten und unbekannten Umgebungen ist nicht dieselbe
wie die zwischen vollständig und teilweise beobachtbaren Umgebungen. Es ist durchaus
möglich,dasseinebekannteUmgebungnurteilweisebeobachtbarist–beieinemSolitär-Kar-
tenspielzumBeispielkenneichzwardieRegeln,kannabernichtdieKartensehen,dienoch
nichtumgedrehtwurden.UmgekehrtkanneineunbekannteUmgebungvollständigbeobacht-
barsein–beieinemneuenVideospielzeigtderBildschirmvielleichtdengesamtenSpielzu-
standan,aberichweißerst,wasdieTastenbedeuten,wennichsieausprobiere.
WieaufS.65erwähnt,kanndasLeistungsmaßselbstunbekanntsein,entwederweilderEnt-
wickler nicht sicher ist, wie er es korrekt angeben soll, oder weil der Endanwender – auf
dessen Präferenzen esankommt–nochnicht bekanntist. EinTaxifahrerweiß zum Beispiel
normalerweisenicht,obeinneuerFahrgasteinegemächlicheoderschnelleFahrt,einenvor-
sichtigen oder aggressiven Fahrstil bevorzugt. Ein virtueller persönlicher Assistent weiß zu
BeginnnichtsüberdiepersönlichenVorliebenseinesneuenBesitzers.InsolchenFällenkann
derAgentdurchweitereInteraktionenmitdemEntwickleroderBenutzermehrüberdasLeis-
tungsmaßerfahren.Dieswiederumlegtnahe,dassdieAufgabenumgebungnotwendigerweise
alsMultiagentenumgebungbetrachtetwird.
Der schwierigste Fall ist eine teilweise beobachtbare, nichtdeterministische, sequenzielle,
dynamische, stetige und unbekannte Multiagentenumgebung. Taxifahren ist in all diesen
Aspektenschwierig,außerdassdieUmgebungdesFahrersgrößtenteilsbekanntist.DasFah-
reneinesMietwagensineinemneuenLandmitnichtvertrauterGeografie,anderenVerkehrs-
regelnundnervösenFahrgästenistvielaufregender.
InIAbbildung2.6sinddieEigenschafteneinigervertrauterUmgebungenaufgeführt.Beach-
tenSie,dassdieEigenschaftennichtimmerganzeindeutigsind.ZumBeispielhabenwirdie
medizinischeDiagnosealsAufgabefüreineneinzelnenAgentenaufgeführt,weilderKrank-
heitsprozessbeieinemPatientennichtnutzbringendalsAgentmodelliertwerdenkann;aber
ein medizinisches Diagnosesystem musseventuell auch mitwiderspenstigen Patienten und
skeptischem Personal umgehen, sodass die Umgebung durchaus auch Aspekte einer Multi-
agentenumgebunghabenkönnte.AußerdemistdiemedizinischeDiagnoseepisodisch,wenn
mandieAufgabesoversteht,dasseineDiagnoseanhandeinerListevonSymptomenausge-
wähltwird;dasProblemistabersequenziell,wenndieAufgabedasVorschlageneinerReihe
72
--- PAGE 74 ---
2.4 DieStrukturvonAgenten
Aufgabenumgebung Beobachtbar Agenten Deterministisch Episodisch Statisch Diskret
Kreuzworträtsel Vollständig Einzel Deterministisch Sequenziell Statisch Diskret
SchachmitUhr Vollständig Multi Deterministisch Sequenziell Semidyn. Diskret
Poker Teilweise Multi Nichtdeterministisch Sequenziell Statisch Diskret
Backgammon Vollständig Multi Nichtdeterministisch Sequenziell Statisch Diskret
Taxifahren Teilweise Multi Nichtdeterministisch Sequenziell Dynamisch Stetig
MedizinischeDiagnose Teilweise Einzel Nichtdeterministisch Sequenziell Dynamisch Stetig
Bildanalyse Vollständig Einzel Deterministisch Episodisch Semidyn. Stetig
Pack-RoboterfürWerkstücke Teilweise Einzel Nichtdeterministisch Episodisch Dynamisch Stetig
Raffinerie-Controller Teilweise Einzel Nichtdeterministisch Sequenziell Dynamisch Stetig
InteraktiverEnglischlehrer Teilweise Multi Nichtdeterministisch Sequenziell Dynamisch Diskret
Abbildung2.6:BeispielefürAufgabenumgebungenundihreEigenschaften.
vonTests, dieBewertungdesFortschrittsimVerlauf derBehandlung, dieBehandlungmeh-
rererPatientenusw.beinhaltenkann.
WirhabenkeineSpalte„bekannt/unbekannt“aufgenommen,weildies,wiebereitserwähnt,
streng genommen keine Eigenschaft der Umgebung ist. Bei einigen Umgebungen, wie z.B.
Schach und Poker, ist es recht einfach, den Agenten mit vollständigem Regelwissen auszu-
statten,dochesisttrotzdeminteressantzuüberlegen,wieeinAgentlernenkönnte,dieSpiele
auchohnediesesWissenzumeistern.
DerzudiesemBuchbereitgestelltCode(aima.cs.berkeley.edu)enthältImplementierungen
fürmehrereUmgebungen sowieeinenallgemeinen UmgebungssimulatorzurBewertungder
Leistung eines Agenten. Experimente werden oft nicht für eine einzelne Umgebung durch-
geführt, sondern für viele Umgebungen, die aus einer Umgebungsklasse stammen. Um bei-
spielsweise einen Taxifahrer im simulierten Verkehr zu bewerten, möchten wir gerne viele
Simulationenmitunterschiedlichen Verkehrs-,Licht-undWetterbedingungen durchführen.
WirsinddannanderdurchschnittlichenLeistungdesAgenteninderUmgebungsklasseinter-
essiert.
2.4 Die Struktur von Agenten
Bisher haben wir uns bei unseren Ausführungen über Agenten auf die Beschreibung ihres
Verhaltens konzentriert – die Aktion, die nach einer bestimmten Folge von Perzepten aus-
geführt wird. Jetzt müssen wir in den sauren Apfel beißen und darüber sprechen, wie das
InnenlebeneinesAgentenaussieht.DieAufgabederKIistes,einAgentenprogrammzuent-
werfen,dasdieAgentenfunktionimplementiert–dieAbbildungvonPerzeptenaufAktionen.
Wirgehendavonaus,dassdiesesProgrammaufirgendeinerArtRechengerätmitphysischen
SensorenundAktuatorenläuft–wirnennendiesdieAgentenarchitektur:
AgentDArchitekturCProgramm
Natürlich muss das Programm, das wir auswählen, für die Architektur geeignet sein. Wenn
dasProgrammAktionenwieGehenempfehlensoll,dannsolltedieArchitekturauchirgend-
eine Form von Beinen haben. Bei der Architektur könnte es sich um einen gewöhnlichen
PC handeln oder um ein Roboterauto mit mehreren integrierten Computern, Kameras und
anderen Sensoren. Im Allgemeinen stellt die Architektur dem Programm die Perzepte der
73
--- PAGE 75 ---
2
IntelligenteAgenten
SensorenzurVerfügung, führtdasProgrammausundgibtdievomProgrammvorgeschlage-
nen Aktionen an die Aktuatoren weiter, sobald sie generiert wurden. Der größte Teil dieses
Buchsbefasst sich mitdemEntwurf vonAgentenprogrammen, während sich dieKapitel25
und26direktmitdenSensorenundAktuatorenbefassen.
2.4.1 Agentenprogramme
DieAgentenprogramme,diewirindiesemBuchentwerfen,habenalledasgleicheGrundge-
rüst:SienehmendasaktuellePerzeptalsEingabevondenSensorenentgegenundgebeneine
Aktion an die Aktuatoren zurück.5 Beachten Sie den Unterschied zwischen dem Agenten-
programm, das das aktuelle Perzept als Eingabe entgegennimmt, und der Agentenfunktion,
die von der gesamten Perzepthistorie abhängen kann. Dem Agentenprogramm bleibt nichts
anderesübrig,alslediglichdasaktuellePerzeptalsEingabezuübernehmen,weilsonstnichts
von der Umgebung zur Verfügung gestellt wird – sollen die Aktionen des Agenten von der
gesamtenPerzeptfolgeabhängen,dannmusssichderAgentdiePerzeptemerken.
Wir beschreiben die Agentenprogramme in der einfachen Pseudocode-Sprache, die in
AnhangBdefiniertist.(DeronlinebereitgestellteCodeenthältImplementierungeninechten
Programmiersprachen.) IAbbildung 2.7 zeigt zum Beispiel ein eher triviales Agentenpro-
gramm, das die Perzeptfolge verfolgt und sie dann verwendet, um eine passende Aktion
anhand des entsprechenden Indexes aus der Tabelle herauszusuchen und zu entscheiden,
waszutunist.DieTabelle–einBeispieldafüristinIAbbildung2.3fürdieStaubsaugerwelt
dargestellt–repräsentiertexplizitdieAgentenfunktion,diewiederumdasAgentenprogramm
verkörpert. Um auf diese Weise einen rationalen Agenten zu erstellen, müssen wir als Ent-
wicklereineTabelleaufbauen,diefürjedemöglichePerzeptfolge dieentsprechendeAktion
enthält.
functionTABLE-DRIVEN-AGENT(percept)returnseineAktion
persistent:percepts,eineFolge,anfangsleer
table,eineTabellemitAktionen,durchPerzeptfolgenindiziert,
anfangsvollständigspezifiziert
perceptamEndevonperceptsanhängen
action LOOKUP(percepts,table)
returnaction
Abbildung2.7:DasProgrammTABLE-DRIVEN-AGENTwirdfürjedesneuePerzeptaufgerufenundgibtjeweilseineAktionzurück.
EsverwaltetdievollständigePerzeptfolgeimSpeicher.
Esistsehraufschlussreich, sichzuüberlegen,warumdertabellengesteuerte AnsatzzurEnt-
wicklungvonAgentenzumScheiternverurteiltist.SeiP dieMengedermöglichenPerzepte
und T die Lebensdauer des Agenten (die GesaPmtzahl der Perzepte, die er entgegennimmt).
Die Nachschlagetabelle (LOOKUP) wird dann T
tD1
jPjt Einträge enthalten. Betrachten wir
das automatisierte Taxi: Die visuelle Eingabe einer einzelnen Kamera (acht Kameras sind
üblich)trifftmiteinerRatevonetwa 70MegabyteproSekundeein(30BilderproSekunde,
1:080(cid:2)720 Pixel mit 24 Bit Farbinformationen). Damit kommen wir auf eine Nachschlage-
tabellemitüber10600:000:000:000 EinträgenfüreineStundeFahrzeit.SogardieNachschlageta-
bellefürSchach–einwinziges,bravesFragmentderrealenWelt–hat(wiesichherausstellt)
mindestens 10150 Einträge. Im Vergleich dazu beträgt die Anzahl der Atome im beobacht-
5 EsgibtnochandereMöglichkeitenfürdasGrundgerüsteinesAgentenprogramms;zumBeispielkönntenwir
dieAgentenprogrammealsCoroutinenausführen,dieasynchronzuderUmgebunglaufen.JedesolcheCoroutine
hateinenEingabe-undeinenAusgabeportundbestehtauseinerSchleife,diePerzeptevomEingabeportliest
undAktionenaufdenAusgabeportschreibt.
74
--- PAGE 76 ---
2.4 DieStrukturvonAgenten
baren Universum weniger als 1080. Die erschreckende Größe dieser Tabellen bedeutet, dass
(a)keinphysischerAgentindiesemUniversumgenügendPlatzhat,dieTabellezuspeichern;
(b) kein Entwickler die Zeit hätte, die Tabelle zu erstellen; und (c) kein Agent jemals alle
richtigenTabelleneinträgeausseinerErfahrunglernenkönnte.
J
Trotz alledem erledigt TABLE-DRIVEN-AGENTgenau das, was wir wollen, vorausgesetzt, die
Tabelle ist korrekt ausgefüllt: Es implementiert die gewünschte Agentenfunktion. Die zen-
trale Herausforderung der KI besteht darin herauszufinden, wie man Programme schreibt,
die rationales Verhalten so weit wie möglich aus einem recht kleinen Programm statt aus
einerriesigenTabelleerzeugen.
Esgibt viele Beispiele, diezeigen, dassdies auch in anderen Bereichen erfolgreich möglich
ist:SowurdendieriesigenQuadratwurzeltabellen,dievorden1970erJahrenvonIngenieuren
undSchulkindernverwendetwurden,durcheinfünfzeiligesProgrammersetzt,welchesdas
Newton-Verfahren implementiert und das auf Taschenrechnern ausgeführt wird. Die Frage
lautet: Kann die KI für allgemeines intelligentes Verhalten das leisten, was Newton für die
Quadratwurzelgetanhat?Wirglauben,dieAntwortist„ja“.
Imweiteren Verlauf diesesAbschnittsskizzieren wirviergrundlegende ArtenvonAgenten-
programmen, die die Prinzipien verkörpern, auf denen fast alle intelligenten Systeme auf-
bauen:
(cid:2) EinfacheReflexagenten
(cid:2) ModellbasierteReflexagenten
(cid:2) ZielbasierteAgenten
(cid:2) NutzenbasierteAgenten
JedeArtvonAgentenprogrammkombiniertspezielleKomponentenaufbestimmteWeise,um
Aktionenzuerzeugen.Abschnitt2.4.6erklärtinallgemeinerForm,wiemanalldieseAgenten
inlernendeAgentenumwandelt,diedieLeistungihrerKomponentenverbessernkönnen,um
bessereAktionenzugenerieren.SchließlichwirdinAbschnitt2.4.7dieVielfaltderMöglich-
keiten beschrieben, wie die Komponenten selbst innerhalb des Agenten dargestellt werden
können. Diese Vielfalt stellt ein wichtiges Ordnungsprinzip sowohl für das Fachgebiet als
auchfürdiesesBuchdar.
2.4.2 Einfache Reflexagenten
Die simpelste Art von Agenten sind die einfachen Reflexagenten. Diese Agenten wählen
Aktionen auf der Basis des aktuellen Perzepts aus und ignorieren den Rest der Perzepthis-
torie. Zum Beispiel ist der Staubsauger-Agent, dessen Agentenfunktion in IAbbildung 2.3
tabellarisch dargestellt ist, ein einfacher Reflexagent, weil seine Entscheidung nur auf der
aktuellenPositionunddaraufbasiert,obdiesesFeldSchmutzenthält.EinAgentenprogramm
fürdiesenAgentenistinIAbbildung2.8dargestellt.
functionREFLEX-VACUUM-AGENT([location,status])returnseineAktion
ifstatus=SchmutzigthenreturnSaugen
elseiflocation=AthenreturnRechts
elseiflocation=BthenreturnLinks
Abbildung2.8:DasAgentenprogrammfüreineneinfachenReflexagenteninderStaubsauger-UmgebungmitzweiFeldern.
DiesesProgrammimplementiertdieAgentenfunktionausAbbildung2.3.
75
--- PAGE 77 ---
2
IntelligenteAgenten
BeachtenSie,dassdasProgrammdesStaubsauger-AgentenimVergleichzurentsprechenden
Tabelle tatsächlich sehr klein ist. Die offensichtlichste Reduzierung ergibt sich daraus, dass
diePerzepthistorieignoriertwird,wodurchsichdieAnzahlderrelevantenPerzeptfolgenvon
4T aufnur4verringert.Eineweitere,kleineReduktionergibtsichausderTatsache,dass,falls
das aktuelle Feld schmutzig ist, die zu wählende Aktion nicht von der Position des Agen-
ten abhängt. Obwohl wir das Agentenprogramm mit if-then-else-Anweisungen geschrieben
haben,istesderartigeinfach,dassesauchalsboolescherSchaltkreisimplementiertwerden
kann.
EinfachereflexartigeVerhaltensweisentretenauchinkomplexerenUmgebungenauf.Stellen
Siesichvor,SiewärenderFahrerdesautomatisiertenTaxis.WenndasvorausfahrendeAuto
bremstundseineBremslichteraufleuchten,dannsolltenSiedieserkennenundebenfallsden
Bremsvorgang einleiten. Mitanderen Worten: Esfindet eineVerarbeitung dervisuellen Ein-
gabe statt, um die Bedingung zu schaffen, die wir „das Auto vor uns bremst“ nennen. Dies
löstdanneinebestehendeVerbindungimAgentenprogrammzurAktion„Bremsvorgangein-
leiten“aus.WirnenneneinesolcheVerbindungeineBedingung-Aktion-Regel6,geschrieben
als:
ifAuto-vor-uns-bremstthenBremsvorgang-einleiten
Auch Menschen haben viele solcher Verbindungen, von denen einige erlernte Reaktionen
sind(wiebeimAutofahren)undeinigeangeboreneReflexe(wiedasBlinzeln,wennsichetwas
demAugenähert).ImRahmendiesesBuchszeigenwirverschiedeneMöglichkeitenauf,wie
solcheVerbindungenerlerntundimplementiertwerdenkönnen.
DasProgramminIAbbildung2.8istspezifisch füreinebestimmteStaubsauger-Umgebung.
EinallgemeinererundflexiblererAnsatzbestehtdarin,zunächsteinenuniversellenInterpre-
terfürBedingung-Aktion-RegelnzuerstellenunddannRegelsätzefürspezifischeAufgaben-
umgebungen festzulegen. IAbbildung 2.9 gibt die Struktur dieses allgemeinen Programms
in schematischer Form wieder und zeigt, wie es die Bedingung-Aktion-Regeln dem Agen-
6 AuchSituation-Aktion-Regel,Produktions-oderif-then-Regel.DasBesondereandiesenRegelnist,dasssie
immereineAktion„produzieren“.DerBegriffif-then-Regelwirdauchallgemeinerverwendet.
Agent
Umgebung
Sensoren
Wie die Welt
jetzt aussieht
Welche Aktion ich
Bedingung-Aktion-Regeln
jetzt ausführen sollte
Aktuatoren
Abbildung2.9:SchematischeDarstellungeineseinfachenReflexagenten.WieverwendenRechtecke,umdenaktuelleninternen
ZustanddesEntscheidungsprozessesimAgentenzukennzeichnen,undOvale,umdieHintergrundinformationendarzustellen,
dieindemProzessbenutztwerden.
76
--- PAGE 78 ---
2.4 DieStrukturvonAgenten
functionSIMPLE-REFLEX-AGENT(percept)returnseineAktion
persistent:rules,eineMengevonBedingung-Aktion-Regeln
state INTERPRET-INPUT(percept)
rule RULE-MATCH(state,rules)
action rule.ACTION
returnaction
Abbildung2.10:EineinfacherReflexagent.ErhandeltnacheinerRegel,derenBedingungmitdemdurchdasPerzeptdefinierten
aktuellenZustandübereinstimmt.
ten erlauben, die Verbindung vom Perzept zur Aktion herzustellen. Machen Sie sich keine
Sorgen,wenndiestrivialerscheint;eswirdinKürzeinteressanter.
Ein Agentenprogramm für IAbbildung 2.9 ist in IAbbildung 2.10 dargestellt. Die Funk-
tionINTERPRET-INPUTerzeugtausdemPerzepteineabstrahierteBeschreibungdesaktuellen
ZustandsunddieFunktionRULE-MATCHgibtdieersteRegel ausdemRegelsatz zurück,die
mit der gegebenen Zustandsbeschreibung übereinstimmt. Beachten Sie, dass die Beschrei-
bunghinsichtlich„Regeln“und„Übereinstimmung“reinkonzeptuellist;wieobenerwähnt,
könnendietatsächlichen Implementierungen soeinfach sein wieeine MengevonLogikgat-
tern, dieeinen booleschen Schaltkreis implementieren. Alternativ kannauch ein „neurona-
ler“ Schaltkreis verwendet werden, bei dem die logischen Gatter durch die nichtlinearen
EinheitenkünstlicherneuronalerNetzeersetztwerden(sieheKapitel21).
J
Einfache Reflexagenten haben die großartige Eigenschaft, dass sie einfach sind. Allerdings
sindsieauchnurvonbegrenzterIntelligenz.DerAgentinIAbbildung2.10funktioniertnur
dann,wenndierichtigeEntscheidungalleinaufGrundlagedesaktuellenPerzeptsgetroffen
werdenkann–dasheißtnur,wenndieUmgebungvollständigbeobachtbarist.
Selbst das kleinste bisschen Unbeobachtbarkeit kann zu ernsthaften Problemen führen. Die
oben beschriebene Bremsregel geht zum Beispiel davon aus, dass die Bedingung Auto-vor-
uns-bremst ausdemaktuellenPerzept–einemeinzigenVideobild–bestimmtwerdenkann.
Diesfunktioniert,wenndasvorausfahrendeFahrzeugeinmittigangebrachtes(unddamitein-
deutigidentifizierbares)Bremslichthat.LeiderhabenältereModelleunterschiedlicheAnord-
nungenvonRücklicht,BremslichtundBlinkerundesistnichtimmermöglich,anhandeines
einzigen Bilds zu erkennen, ob das Auto bremst odernurdie Rücklichter an hat. Ein einfa-
cherReflexagent, derhintereinemsolchenAutofährt,würdeentwederständigundunnötig
bremsenoder–nochschlimmer–überhauptnichtbremsen.
EinähnlichesProblemkannunsinderStaubsaugerwelt begegnen. Nehmenwiran,einein-
facher Staubsauger-Reflexagent verliert seinen Positionssensor und besitzt nur noch einen
Schmutzsensor. Ein solcher Agent hat lediglich zwei mögliche Perzepte: ŒSchmutzig(cid:2) und
ŒSauber(cid:2). Er kann Saugen als Reaktion auf ŒSchmutzig(cid:2) ausführen – doch was sollte er als
Reaktion auf ŒSauber(cid:2) tun? Die Links-Bewegung scheitert (immer), falls er in Feld A startet,
und die Rechts-Bewegung scheitert (immer), falls er in Feld B startet. Endlosschleifen sind
füreinfacheReflexagenteninteilweisebeobachtbarenUmgebungenoftunvermeidbar.
Ein Austritt aus Endlosschleifen ist möglich, wenn der Agent seine Aktionen zufällig aus-
wählen(randomisieren)kann.EmpfängtderStaubsauger-AgentzumBeispielŒSauber(cid:2),dann
könnte er eine Münze werfen, um zwischen Rechts und Links zu wählen. Man kann leicht
zeigen, dass der Agent das andere Feld in durchschnittlich zwei Schritten erreicht. Wenn
dieses Felddannschmutzigist,wirdderAgent essäubernunddieAufgabeisterledigt. Ein
einfacher Reflexagent mit Zufallsauswahl könnte somit einem deterministischen einfachen
Reflexagentenüberlegensein.
77
--- PAGE 79 ---
2
IntelligenteAgenten
In Abschnitt 2.3 haben wir erwähnt, dass zufälliges Verhalten der richtigen Art in einigen
Multiagentenumgebungenrationalseinkann.InEinzelagentenumgebungenistdieRandomi-
sierungnormalerweisenichtrational.EsisteinnützlicherTrick,dereinemeinfachenReflex-
agenteninmanchenSituationenhilft,aberindenmeistenFällenkönnenwirmitausgefeilte-
rendeterministischenAgentenvielmehrerreichen.
2.4.3 Modellbasierte Reflexagenten
Der effektivste Weg für einen Agenten, mit teilweiser Beobachtbarkeit umzugehen, ist, über
denaktuellnichtsichtbaren TeilderWeltquasiBuchzu führen.Das heißt,derAgent sollte
eine Art internen Zustand verwalten, der von der Perzepthistorie abhängt und dadurch
zumindest einige der unbeobachteten Aspekte des aktuellen Zustands widerspiegelt. Für
das Bremsproblem ist der interne Zustand nicht allzu umfangreich – es ist nur das vor-
hergehende Kamerabild, sodass der Agent erkennen kann, wenn zwei rote Lichter an den
SeitendesFahrzeugsgleichzeitigan-oderausgehen.FürandereFahraufgaben,wiez. B.den
Spurwechsel, musssich derAgent merken, wosich dieanderen Autosbefinden,fallsersie
nicht alle gleichzeitig sehen kann. Und damit überhaupt eine Fahrt möglich ist, muss sich
derAgentmerken,woseineSchlüsselsind.
UmdieseinternenZustandsinformationenüberdieZeitzuaktualisieren,müssenzweiArten
vonWisseninirgendeinerFormimAgentenprogrammcodiertwerden.Erstensbenötigenwir
Informationendarüber,wiesichdieWeltimLaufederZeitverändert,wobeimanimWesent-
lichenzweiTeileunterscheidenkann:dieAuswirkungenderAktionendesAgentenunddie
EntwicklungderWeltunabhängigvomAgenten. WennderAgent zumBeispieldasLenkrad
imUhrzeigersinnbewegt,danndrehtsichdasAutonachrechts,undwennesregnet,können
die Kameras des Autos nass werden. Dieses Wissen darüber, „wie die Welt funktioniert“ –
obineinfachenbooleschenSchaltkreisenoderinvollständigenwissenschaftlichenTheorien
implementiert–,wirdalsTransitionsmodellderWeltbezeichnet.
Zweitens benötigen wir Informationen darüber, wie sich der Zustand der Welt in den Per-
zeptendesAgentenwiderspiegelt. Wennz.B.dasvorausfahrendeAutoeinenBremsvorgang
einleitet,soerscheineneinodermehrererotbeleuchteteBereicheimnachvornegerichteten
Kamerabild,undwenndieKameranasswird,erscheinentropfenförmigeObjekteimBild,die
dieStraßeteilweiseverdecken.DieseArtvonWissenwirdalsSensormodellbezeichnet.
Zusammen ermöglichen das Transitionsmodell und das Sensormodell einem Agenten, den
ZustandderWeltzuverfolgen–soweitdiesangesichtsderEinschränkungendurchdieSen-
sorendesAgentenmöglichist.EinAgent,dersolcheModelleverwendet,wirdalsmodellba-
sierterAgentbezeichnet.
IAbbildung2.11zeigtdieStrukturdesmodellbasiertenReflexagentenmitinternemZustand
und verdeutlicht, wie das aktuelle Perzept mit dem alten internen Zustand verknüpft wird,
umdieaktualisierte Beschreibungdesaktuellen Zustandszu generieren, basierend aufdem
Modell des Agenten über Funktionsweise der Welt. Das Agentenprogramm ist in IAbbil-
dung 2.12. dargestellt. Der interessante Teil ist die Funktion UPDATE-STATE, die für die
Erstellung der neuen internen Zustandsbeschreibung verantwortlich ist. Wie Modelle und
Zustände im Detail dargestellt werden, variiert stark und hängt von der Art der Umgebung
undderspeziellenTechnologieab,diebeimEntwurfdesAgentenverwendetwird.
UnabhängigvonderArtderDarstellungistesfürdenAgentenseltenmöglich,denaktuellen
Zustand einer teilweise beobachtbaren Umgebung genau zu bestimmen. Stattdessen reprä-
sentiert das Kästchen mit der Aufschrift „Wie die Welt jetzt aussieht“ (IAbbildung 2.11)
die „beste Vermutung“ des Agenten (oder manchmal beste Vermutungen, wenn der Agent
mehrere Möglichkeiten in Betracht zieht). ZumBeispiel kann ein automatisiertes Taxi mög-
licherweise nicht um den großen Lkw herumsehen, der vor ihm angehalten hat, und kann
78
--- PAGE 80 ---
2.4 DieStrukturvonAgenten
Agent
Umgebung
Sensoren
Zustand
Wie sich die Welt entwickelt Wie die Welt
jetzt aussieht
Was meine Aktionen bewirken
Welche Aktion ich
Bedingung-Aktion-Regeln
jetzt ausführen sollte
Aktuatoren
Abbildung2.11:EinmodellbasierterReflexagent.
functionMODEL-BASED-REFLEX-AGENT(percept)returnseineAktion
persistent:state,dieaktuelleAuffassungdesAgentenvomZustandderWelt
transition_model,eineBeschreibung,wiedernächsteZustandvom
aktuellenZustandundderAktionabhängt
sensor_model,eineBeschreibung,wiesichderaktuelleZustandderWelt
indenPerzeptendesAgentenwiderspiegelt
rules,eineMengevonBedingung-Aktion-Regeln
action,dievorherigeAktion,anfangskeine
state UPDATE-STATE(state,action,percept,transition_model,sensor_model)
rule RULE-MATCH(state,rules)
action rule.ACTION
returnaction
Abbildung2.12:EinmodellbasierterReflexagent.ErverwaltetdenaktuellenZustandderWeltmithilfeeinesinternenModells.
DannwähltereineAktionaufdiegleicheWeisewiederReflexagentaus.
nur raten, was die Ursache für den Stau sein könnte. Die Unsicherheit über den aktuellen
Zustand ist also eventuell unvermeidlich,aber derAgent musstrotzdemeine Entscheidung
treffen.
2.4.4 Zielbasierte Agenten
Es reicht nicht immer aus, etwas über den aktuellen Zustand der Umgebung zu wissen,
um zu entscheiden, was zu tun ist. Zum Beispiel kann das Taxi an einer Straßenkreuzung
linksabbiegen,rechtsabbiegenodergeradeausweiterfahren.DierichtigeEntscheidunghängt
davon ab, wohin das Taxi fahren will. Mit anderen Worten: Der Agent benötigt neben einer
Beschreibung des aktuellen Zustands auch gewisse Informationen über das Ziel, das wün-
schenswerte Situationen beschreibt – z.B. an einem bestimmten Ort angekommen zu sein.
DasAgentenprogrammkanndiesmitdemModellkombinieren(dieselbenInformationen,die
im modellbasierten Reflexagenten verwendet wurden), um Aktionen auszuwählen, die das
Zielerreichen.IAbbildung2.13zeigtdieStrukturdeszielbasiertenAgenten.
79
--- PAGE 81 ---
2
IntelligenteAgenten
Agent
Umgebung
Sensoren
Zustand
Wie die Welt
Wie sich die Welt entwickelt jetzt aussieht
Was passiert, wenn ich
Was meine Aktionen bewirken Aktion A ausführe
Welche Aktion ich
Ziele jetzt ausführen sollte
Aktuatoren
Abbildung2.13:Einmodellbasierter,zielbasierterAgent.ErverwaltetdenZustandderWeltsowieeineReihevonZielen,die
erzuerreichenversucht,undwählteineAktionaus,die(letztendlich)zumErreichenseinerZieleführt.
ManchmalistdieAuswahlvonzielbasiertenAktioneneinfach–zumBeispiel,wenndasZiel
direkt mit einer einzigen Aktion erreicht wird. Manchmal ist es etwas schwieriger – zum
Beispiel,wennderAgentlangeFolgenkomplizierterAktioneninBetrachtziehenmuss,um
dasZiel zu erreichen. Suchen (Kapitel 3bis5)und Planen(Kapitel 11)sind dieTeilgebiete
derKI,diesichdamitbeschäftigen,Aktionsfolgenzufinden,diedieZieledesAgentenerrei-
chen.
Beachten Sie, dass sich diese Art der Entscheidungsfindung grundlegend von den zuvor
beschriebenenBedingung-Aktion-Regelnunterscheidet,dahierauchdieZukunftberücksich-
tigtwird–sowohl„Waswirdpassieren,wennichdiesundjenestue?“alsauch„Wirdmich
das glücklich machen?“. In den Entwürfen der Reflexagenten wird diese Information nicht
explizit dargestellt, da die eingebauten Regeln direkt von Perzepten auf Aktionen abbilden.
Der Reflexagent bremst, wenn er Bremslichter sieht, Punkt. Er hat keine Ahnung, warum.
Einzielbasierter Agent bremst,wenn erBremslichter sieht,weil ervorhersieht, dasdiesdie
einzige Aktionist, dieihm hilft, sein Ziel, nicht mit anderen Autoszusammenzustoßen, zu
erreichen.
Obwohl der zielbasierte Agent weniger effizient erscheint, ist er flexibler, weil das Wissen,
dasseineEntscheidungen unterstützt, explizit dargestellt wird undverändert werden kann.
Zum Beispiel kann das Verhalten eines zielbasierten Agenten leicht dahingehend geändert
werden,einanderesFahrtzielanzusteuern,indemeinfachdieseneuePositionalsZielange-
geben wird. DieRegeln desReflexagenten, wann erabbiegen undwann ergeradeaus fahren
soll, funktionieren nur für einen einzigen Zielort; sie müssen alle ersetzt werden, wenn er
irgendwoandershinfahrensoll.
2.4.5 NutzenbasierteAgenten
Zieleallein reichen indenmeistenUmgebungen nichtaus,umqualitativhochwertiges Ver-
haltenzuerzeugen.ZumBeispielwerdenvieleAktionsfolgendasTaxianseinenZielortbrin-
gen (und damit das Ziel des Agenten erreichen), aber einige sind schneller, sicherer, zuver-
lässigeroderbilligeralsandere.ZielebietennureinegrobebinäreUnterscheidungzwischen
„glücklichen“und„unglücklichen“Zuständen.EinallgemeineresLeistungsmaßsollteeinen
Vergleich verschiedener Zustände derWelt danach erlauben, wieglücklich sie denAgenten
80
--- PAGE 82 ---
2.4 DieStrukturvonAgenten
machen würden. Da „glücklich” nicht sehr wissenschaftlich klingt, verwenden Wirtschafts-
wissenschaftlerundInformatikerstattdessendenBegriffNutzen(utility).
Wirhabenbereitsgesehen,dasseinLeistungsmaßjedergegebenenFolgevonUmgebungszu-
ständeneinePunktzahlzuweist,sodassmanleichtzwischenmehrundwenigerwünschens-
wertenWegenzumZielortdesTaxisunterscheidenkann.DieNutzenfunktioneinesAgenten
istimWesentlicheneineInternalisierungdesLeistungsmaßes.UnterderVoraussetzung,dass
dieinterneNutzenfunktionunddasexterneLeistungsmaßübereinstimmen,isteinAgent,der
Aktionen auswählt, umseinen Nutzen zumaximieren, hinsichtlich desexternen Leistungs-
maßesrational.
Es sei noch einmal betont, dass dies nicht die einzige Möglichkeit ist, rational zu sein –
wir haben bereits ein rationales Agentenprogramm für die Staubsaugerwelt kennengelernt
(IAbbildung2.8),dasnichtsvonseinerNutzenfunktionweiß–,dochwiezielbasierteAgen-
tenbieteteinnutzenbasierter AgentvieleVorteileinBezugaufFlexibilitätundLernen.Dar-
überhinaussindZieleinzwei Arten vonFällenunzureichend, woabereinnutzenbasierter
Agent trotzdem rationale Entscheidungen treffen kann. Der erste Fall ist, wenn es wider-
sprüchliche Ziele gibt, vondenen nureinige erreicht werden können(z.B.hoheGeschwin-
digkeitundvolleSicherheit).HiergibtdieNutzenfunktiondieangemessene Abwägungvor.
Der zweite Fall ist, wenn es mehrere Ziele gibt, die der Agent anstreben kann, doch von
denenkeinesmitSicherheiterreichtwerdenkann.DannbietetderNutzeneineMöglichkeit,
dieErfolgswahrscheinlichkeitgegendieWichtigkeitderZieleabzuwägen.
TeilweiseBeobachtbarkeitundNichtdeterminismussindinderrealenWeltallgegenwärtig –
unddamitauchdieEntscheidungsfindungunterUnsicherheit.Technischgesehen wähltein
rationaler nutzenbasierter Agent die Aktion aus, die den erwarteten Nutzen der Aktionser-
gebnissemaximiert–d.h.denNutzen,denderAgentimDurchschnitterwartet,wennerdie
Wahrscheinlichkeiten undNutzenwertedereinzelnenErgebnisseberücksichtigt.(AnhangA
definiert den Erwartungswert genauer.) In Kapitel 16 zeigen wir, dass sich jeder rationale
Agent soverhaltenmuss,alsobereineNutzenfunktionbesäße,derenErwartungswert erzu
maximieren versucht. Ein Agent, der eine explizite Nutzenfunktion besitzt, kann rationale
Entscheidungen mit einem allgemeinen Algorithmus treffen, dernicht vonder spezifischen
Nutzenfunktionabhängt,dieermaximiert.AufdieseWeisewirddie„globale“Definitionvon
Rationalität–inderdieAgentenfunktionenmitderhöchstenLeistungalsrationalbezeichnet
werden –ineine„lokale“EinschränkungfürdenEntwurfrationalerAgenten umgewandelt,
dieineinemeinfachenProgrammausgedrücktwerdenkann.
Die Struktur eines nutzenbasierten Agenten ist in IAbbildung 2.14 dargestellt. Nutzenba-
sierteAgentenprogrammetaucheninKapitel16und17wiederauf,wowirentscheidungsfä-
higeAgenten entwerfen,diemitderUnsicherheit umgehenmüssen,dieinnichtdeterminis-
tischenoderteilweisebeobachtbarenUmgebungenherrscht.EntscheidungsfindunginMulti-
agentenumgebungenwirdinKapitel18,ebenfallsimRahmenderNutzentheorie,untersucht.
AndieserStellefragtsichderLeservielleicht:„Isteswirklichsosimpel?Wirerstelleneinfach
Agenten,diedenerwartetenNutzenmaximieren,undschonsindwirfertig?“Esstimmt,sol-
cheAgentenwärenintelligent,abereinfachistesnicht.EinnutzenbasierterAgentmussseine
Umgebung modellieren und verfolgen – Aufgaben, für die umfangreiche Forschung in den
Bereichen Wahrnehmung, Repräsentation, Schlussfolgerung und Lernen betrieben wurde.
Die Ergebnisse dieser Forschung füllen viele Kapitel dieses Buchs. Die Wahl der nutzen-
maximierenden Aktionskette ist ebenfalls eine schwierige Aufgabe, die ausgeklügelte Algo-
rithmenerfordert,womitwirmehrereweitere Kapitelfüllen.SelbstmitdiesenAlgorithmen
ist perfekte Rationalität normalerweise in der Praxis aufgrund der Komplexität der Berech-
nungenunerreichbar,wiewirinKapitel1festgestellthaben.Wirwollennichtverschweigen,
dassnichtallenutzenbasiertenAgentenmodellbasiertsind;wirwerdeninKapitel22und26
sehen,dasseinmodellfreierAgentlernenkann,welcheAktionineinerbestimmtenSituation
ambestenist,ohnejemalsgenauzulernen,wiedieseAktiondieUmgebungverändert.
81
--- PAGE 83 ---
2
IntelligenteAgenten
Agent
Umgebung
Sensoren
Zustand
Wie sich die Welt
Wie sich die Welt entwickelt entwickelt
Was passiert, wenn ich
Was meine Aktionen bewirken Aktion A ausführe
Wie glücklich ich in
Nutzen
diesem Zustand bin
Welche Aktion ich
jetzt ausführen sollte
Aktuatoren
Abbildung2.14:Einmodellbasierter,nutzenbasierterAgent.ErverwendeteinModellderWeltzusammenmiteinerNutzen-
funktion,dieseinePräferenzenhinsichtlichderZuständederWeltbewertet.DannwählterdieAktion,diezumbestenerwarteten
Nutzenführt,wobeidererwarteteNutzenberechnetwird,indemderMittelwertüberallemöglichenErgebniszustände,gewich-
tetnachderWahrscheinlichkeitdesErgebnisses,gebildetwird.
Schließlich wird bei all dem davon ausgegangen, dass der Entwickler die Nutzenfunktion
korrektspezifizieren kann;indenKapiteln17,*********************************
Nutzenfunktionenausführlicherbehandelt.
2.4.6 LernendeAgenten
WirhabenAgentenprogrammebeschrieben,dieAktionennachverschiedenenMethodenaus-
wählen.Dochwirhabenbishernichterklärt,wiedieAgentenprogrammeentstehen.Inseiner
berühmten frühen Arbeit erwägt Turing (1950) die Idee, seine intelligenten Maschinen tat-
sächlich vonHandzuprogrammieren. Erschätzt, wievielArbeitdiesbedeutenwürde,und
kommt zu dem Schluss: „Eine schnellere Methode scheint wünschenswert.“ Turing schlägt
als Methode vor, lernende Maschinen zu erstellen und diese dann zu trainieren. In vielen
BereichenderKünstlichenIntelligenzistdiesauchmittlerweilediebevorzugteMethode,um
moderneSystemezuentwickeln.JederAgententyp(modellbasiert,zielbasiert,nutzenbasiert
usw.)kannalslernenderAgentgebautwerden(oderebennicht).
Wiebereitserwähnt,hatLernennocheinenweiterenVorteil:LernenermöglichtesdemAgen-
ten, in anfänglich unbekannten Umgebungen zu arbeiten und mit der Zeit kompetenter zu
werden, als es sein anfängliches Wissen allein erlauben würde. Indiesem Abschnitt stellen
wir kurz die wichtigsten Ideen von lernenden Agenten vor. Im weiteren Verlauf des Buchs
gehenwiraufdieMöglichkeitenundMethodendesLernensfürbestimmteArtenvonAgen-
tenein.IndenKapiteln 19–22werdendieLernalgorithmenselbstnäherbeleuchtet.
Ein lernender Agent kann in vier konzeptionelle Komponenten unterteilt werden, wie in
IAbbildung 2.15 gezeigt. Die wichtigste Unterscheidung ist die zwischen dem Lernele-
ment, das für Verbesserungen zuständig ist, und dem Leistungselement, das für die Aus-
wahl externer Aktionen verantwortlich ist. Das Leistungselement ist das, was wir zuvor als
den ganzen Agent betrachtet haben: Es empfängt Perzepte und entscheidet über Aktionen.
Das Lernelement nutzt dasFeedback derKritik-KomponentezurLeistung desAgenten und
bestimmt, wie das Leistungselement modifiziert werden sollte, um in Zukunft besser abzu-
schneiden.
82
--- PAGE 84 ---
2.4 DieStrukturvonAgenten
Leistungsstandard
Agent
Umgebung
Kritik Sensoren
Feed-
back
Änderungen
Lern- Leistungs-
element element
Wissen
Lern-
ziele
Problem-
generator
Aktuatoren
Abbildung2.15:EinallgemeinerlernenderAgent.DerKasten„Leistungselement“repräsentietdas,waswirbisheralsdas
gesamteAgentenprogrammbetrachtethaben.JetztwirdderKasten„Lernelement“dazuverwendet,diesesProgrammzumodi-
fizieren,umseineLeistungzuverbessern.
DerEntwurfdesLernelementshängtsehrstarkvomEntwurfdesLeistungselementsab.Wenn
man versucht, einen Agenten zu entwerfen, der eine bestimmteFähigkeit erlernt, lautet die
ersteFragenicht:„Wiebringeichihndazu,dieszulernen?“,sondern:„WelcheArtvonLeis-
tungselement wird mein Agent verwenden, um dies zu tun, sobald er gelernt hat, wie es
geht?“ Füreinen gegebenen EntwurfdesLeistungselements könnenLernmechanismen kon-
struiertwerden,umjedenTeildesAgentenzuverbessern.
DieKritikteiltdemLernelementmit,wiegutderAgentinBezugaufeinenfestgelegtenLeis-
tungsstandardabschneidet.DieKritikistnotwendig,weildiePerzepteselbstkeinenHinweis
auf den Erfolg des Agenten liefern. Zum Beispiel könnte ein Schachprogramm ein Perzept
erhalten,dasanzeigt,dassdasProgrammseinenGegnerschachmattgesetzthat,aberesbenö-
tigteinenLeistungsstandard, umzuwissen,dassdieseineguteSacheist;dasPerzeptselbst
sagt das nicht. Es ist wichtig, dass der Leistungsstandard festgelegt ist. Konzeptionell sollte
manihnsichalsaußerhalbdesAgenten befindlichvorstellen,weil derAgent ihnnichtver-
änderndarf,umdenStandardanseineigenesVerhaltenanzupassen.
DieletzteKomponentedeslernendenAgentenistderProblemgenerator.Eristdafürverant-
wortlich,Aktionenvorzuschlagen,diezuneuenundinformativenErfahrungenführen.Wenn
esnurnachdemLeistungselementginge,sowürdeesimmerweiterdieAktionenausführen,
dieseinesWissensnachambestensind,dochwennderAgentbereitist,sicheinwenigaus-
zuprobierenundkurzfristigeinigemöglicherweisesuboptimaleAktionenauszuführen,dann
könnteerauflangeSichtwesentlichbessereAktionenentdecken.DieAufgabedesProblem-
generators istes, dieseErkundungsaktionenvorzuschlagen. Soverhalten sichWissenschaft-
ler,wennsieExperimentedurchführen.Galileodachtenicht,dassderAkt,Steinevoneinem
Turm in Pisa fallen zu lassen, an sich wertvoll sei. Es ging ihm nicht darum, die Steine zu
zertrümmern oder die Gehirne der unglücklichen Passanten zu modifizieren. Sein Ziel war
es,seineigenesGehirnzumodifizieren,indemereinebessereTheoriefürdieBewegungvon
Objektenausfindigmachte.
Das Lernelement kann Änderungen an jeder der in den Agentendiagrammen (IAbbildun-
gen 2.9,2.11,2.13und2.14)dargestellten „Wissens“-Komponenten vornehmen.Indenein-
83
--- PAGE 85 ---
2
IntelligenteAgenten
fachstenFällenwirddirektausderPerzeptfolgegelernt.DurchdieBeobachtungvonPaaren
aufeinanderfolgender Zustände der Umgebung kann der Agent lernen, „was meine Aktio-
nen bewirken“ und „wie sich die Welt entwickelt“ in Reaktion auf seine Aktion. Wenn das
automatisierteTaxiz.B.beieinerFahrtaufnasserStraßeeinenbestimmtenBremsdruckaus-
übt,wirdesbaldherausfinden,wievielBremsleistungdadurchtatsächlicherreichtwirdund
ob es von der Straße abrutscht. Der Problemgenerator könnte bestimmte Teile des Modells
identifizieren, die verbesserungsbedürftig sind, und Experimente vorschlagen, wie z.B. das
AusprobierenderBremsenaufverschiedenenStraßenbelägenunterunterschiedlichenBedin-
gungen.
DieModellkomponenteneinesmodellbasiertenAgentensozuverbessern,dasssiemehrmit
der Realität übereinstimmen, ist fast immer eine gute Idee, unabhängig vom externen Leis-
tungsstandard. (In manchen Fällen ist es aus rechnerischer Sicht besser, ein einfaches, aber
leichtungenauesModellzuhaben,alseinperfektes,aberhöllischkomplexesModell.)Infor-
mationenausdemexternenStandardwerdenbenötigt,wennmanversucht,eineReflexkom-
ponenteodereineNutzenfunktionzulernen.
Nehmen wir zum Beispiel an, der Taxifahrer-Agent erhält kein Trinkgeld von Fahrgästen,
die während der Fahrt gründlich durchgeschüttelt wurden. Der externe Leistungsstandard
mussdenAgentendarüberinformieren,dasssichderVerlustvonTrinkgeldnegativaufseine
Gesamtleistungauswirkt;sokönntederAgentlernen,dassaggressive Manövernichtzusei-
nemeigenen Nutzen beitragen. Ingewissem Sinneidentifiziert derLeistungsstandard einen
TeildeseingehendenPerzeptsalseineBelohnung(bwz.Strafe),wodurchderAgenteindirek-
tesFeedbackzurQualitätseinesVerhaltensbekommt.Fest„verdrahtete“Leistungsstandards
wieSchmerzundHungerbeiTierenkönnenaufdieseWeiseverstandenwerden.
Allgemeiner gesagt, können menschliche Entscheidungen Informationen über menschliche
Vorliebenliefern.NehmenwirzumBeispielan,dasTaxiweißnicht,dassMenschengewöhn-
lich keine lauten Geräusche mögen, und kommtauf die Idee, ständig zu hupen, um sicher-
zustellen,dassalleFußgängermitbekommen,dasssichdasTaxinähert.Dasdarauffolgende
VerhaltenderMenschen–sichdieOhrenzuhalten,Schimpfwörterbenutzenundmöglicher-
weisedieKabelzurHupedurchschneiden–würdedemAgentenHinweisebieten,mitdenen
er seine Nutzenfunktion aktualisieren kann. Dieses Themawird in Kapitel 22weiter disku-
tiert.
Zusammenfassendhaltenwirfest:AgentenbesitzeneineVielzahlvonKomponenten,dieauf
vieleArtenimAgentenprogrammdargestelltwerdenkönnen,sodasseseinegroßeBandbreite
anLernmethodenzugebenscheint.EsgibtjedochaucheinenverbindendenAspekt:Lernen
inintelligentenAgentenlässtsichalseinProzesszusammenfassen,derjedeKomponentedes
Agentenverändert,umdieKomponentenengermitdenverfügbarenFeedback-Informationen
abzustimmenunddadurchdieGesamtleistungdesAgentenzuverbessern.
2.4.7 Wie die Komponenten vonAgentenprogrammen funktionieren
LautunsererbisherigenBeschreibung(aufsehrhohemNiveau)bestehenAgentenprogramme
aus verschiedenen Komponenten, deren Aufgabe es ist, Fragen zu beantworten wie: „Wie
sieht die Welt jetzt aus?“, „Welche Aktion sollte ich jetzt ausführen?“ oder „Was bewirken
meine Aktionen?“. Und für einen KI-Studenten lautet die nächste Frage: „Wie in aller Welt
funktionierendieseKomponenten?“Manbräuchteungefährtausend Seiten,umdieseFrage
ordentlich zu beantworten, doch wir wollen hier die Aufmerksamkeit des Lesers auf einige
grundlegendeUnterscheidungenzwischendenverschiedenenMöglichkeitenlenken,wiedie
KomponentendieUmgebung,inderderAgentlebt,darstellenkönnen.
Grob gesagt können wir die Darstellungen entlang einer Achse zunehmender Komplexität
undAusdrucksstärkeeinordnen–atomar,faktorisiertundstrukturiert.DieseKonzeptelas-
84
--- PAGE 86 ---
2.4 DieStrukturvonAgenten
B C
B C
(a) Atomar (b) Faktorisiert (c) Strukturiert
Abbildung2.16:DreiMöglichkeiten,ZuständeunddieÜbergängezwischenihnendarzustellen.(a)AtomareDarstellung:ein
Zustand(z.B.BoderC)isteineBlackboxohneinterneStruktur.(b)FaktorisierteDarstellung:einZustandbestehtauseinem
VektorvonAttributwerten–boolescheWerte,GleitkommazahlenodereinfesterSatzvonSymbolen.(c)StrukturierteDarstellung:
EinZustandumfasstObjekte,vondenenjedessowohleigeneAttributealsauchBeziehungenzuanderenObjektenhabenkann.
sen sich am besten anhand einer bestimmten Agentenkomponente veranschaulichen, z.B.
an der, die sich mit „Was meineAktionen bewirken“ befasst. Diese Komponentebeschreibt
dieÄnderungen, dieinderUmgebungalsErgebniseinerAktionauftreten können.IAbbil-
dung2.16zeigtschematischeDarstellungendieserÜbergänge.
In einer atomaren Darstellung ist jeder Zustand der Welt unteilbar – er hat keine interne
Struktur.BetrachtenSiedieAufgabe,eineFahrtroutevoneinemEndeeinesLandszumande-
renübereineReihevonStädtenzufinden(wirbehandelndiesesProbleminIAbbildung3.1
aufS.93).FürdieLösungdiesesProblemskannesausreichen,denZustandderWeltaufden
Namen der Stadt zu reduzieren, in der wir unsbefinden – ein einzelnes Wissensatom, eine
„Blackbox“mitnureinereinzigenerkennbarenEigenschaft:Sieistentwedermiteinerande-
ren Blackbox identisch oder von ihr verschieden. Die Standardalgorithmen, die der Suche
unddemSpielen(Kapitel3–5),denHidden-Markov-Modellen(Kapitel14)unddenMarkov-
Entscheidungsprozessen (Kapitel 17) zugrundeliegen, arbeiten alle mitatomaren Repräsen-
tationen.
Eine faktorisierte Darstellung unterteilt jeden Zustand in eine feste Menge von Variablen
oderAttributen,vondenenjedeseinenWertannehmenkann.Sehenwirunsdasobenange-
gebene Fahrtproblem noch einmal unter einem realitätsnäheren Blickwinkel an, wenn wir
mehralsunserenStandortineinerStadtalsatomarenZustandberücksichtigenmüssen:Wir
müssen vielleicht darauf achten, wie der Benzinstand in unserem Tank ist, welches unsere
aktuellen GPS-Koordinaten sind, ob die Ölwarnleuchte funktioniert, wie viel Geld wir für
die Maut haben, welcher Sender im Radio läuft und so weiter. Während zwei verschiedene
atomareZuständenichtsgemeinsamhaben–siesindeinfachnurunterschiedlicheBlackbo-
xes –, könnenzwei verschiedene faktorisierte Zustände einige gemeinsame Attribute haben
(z.B. an einer bestimmten GPS-Position zu sein), andere wiederum nicht (z.B. viel Benzin
oderkeinBenzinzuhaben);aufdieseWeiselässtessichvieleinfacherherausfinden,wieman
einen Zustand in einen anderen überführen kann. Viele wichtige Bereiche der Künstlichen
Intelligenz basieren auf faktorisierten Darstellungen, darunter Algorithmen für Constraint-
Satisfaction-Probleme(ProblemeunterRand-/Nebenbedingungen, Kapitel6),Aussagenlogik
(Kapitel7),Planung(Kapitel11),Bayes’scheNetze(Kapitel12–16)sowieverschiedeneAlgo-
rithmenzummaschinellenLernen.
FürvieleAufgabenisteswichtig,dieWeltindemSinnezubegreifen,dasssienichtnuraus
VariablenmitWertenbesteht,sonderndassesinihrDingegibt,diemiteinanderinBeziehung
85
--- PAGE 87 ---
2
IntelligenteAgenten
stehen. ZumBeispiel könntenwirmitbekommen,dasseingroßerLkwvorunsrückwärtsin
dieEinfahrteinesMilchbauernhofsfährt,docheinefreilaufendeKuhblockiertdenWegdes
Lkws. Es ist recht unwahrscheinlich, dass in einer faktorisierten Repräsentation bereits ein
Attribut LkwVorausFährtRückwärtsInMilchbauernhofeinfahrtBlockiertVonFreilaufenderKuh
existiert, das den Wert wahr oder falsch annehmen kann. Stattdessen benötigen wir
eine strukturierte Repräsentation, in der Objekte wie Kühe und Lkws sowie ihre unter-
schiedlichen und variierenden Beziehungen explizit beschrieben werden können (IAbbil-
dung 2.16c). Strukturierte Darstellungen bilden die Basis für relationale Datenbanken und
diePrädikatenlogik(Kapitel8,9und10),fürprädikatenlogischeWahrscheinlichkeitsmodelle
(Kapitel 15) sowie einen großen Teil der Computerlinguistik (Kapitel 23 und 24). Tatsäch-
lich betrifft fast alles, was Menschen in natürlicher Sprache ausdrücken, Objekte und ihre
Beziehungen.
Wiewirbereitserwähnthaben,liegenatomare, faktorisierteundstrukturierteDarstellungen
aufderAchsederzunehmendenAusdrucksstärke.Grobgesagt kanneineausdrucksstärkere
Darstellung alles, was eine weniger ausdrucksstarke Darstellung erfassen kann, mindestens
genausoprägnantwiedergeben,undnocheinigesmehr.HäufigistdieausdrucksstärkereSpra-
chewesentlichprägnanter;zumBeispielkannmandieRegelndesSchachspielsineinerstruk-
turiertenRepräsentationssprachewiederPrädikatenlogikaufeinoderzweiSeitenaufschrei-
ben,manbenötigtaberTausendevonSeiten,wennmansieineinerfaktorisiertenRepräsen-
tationssprache wieder Aussagenlogik verfasst, undetwa 1038 Seiten, wenn siein einer ato-
marenSprachewiederderendlichenAutomatengeschrieben werden.Andererseits werden
SchlussfolgernundLernenkomplexer,wenndieAusdrucksstärkederDarstellungzunimmt.
Um die Vorteile von ausdrucksstarken Repräsentationen auszunutzen und gleichzeitig ihre
Nachteile zu vermeiden, müssen intelligente Systeme für die reale Welt möglicherweise an
allenPunktenentlangderAchsegleichzeitigarbeiten.
EineandereAchsefürdieDarstellungbeziehtsichaufdasAbbildenvonKonzeptenaufOrte
imphysischenSpeicher,seiesineinemComputeroderineinemGehirn.GibteseineEins-zu-
eins-AbbildungzwischenKonzeptenundSpeicherplätzen,sonennenwirdaseinelokalisti-
scheRepräsentation.WennhingegendieDarstellungeinesKonzeptsaufvieleSpeicherplätze
verteiltistundjederSpeicherortalsTeilderRepräsentationmehrererverschiedenerKonzepte
verwendetwird,dannistdieseineverteilteRepräsentation.VerteilteRepräsentationensind
robustergegenRauschenundInformationsverlust.BeieinerlokalistischenRepräsentationist
die Zuordnung vom Konzept zum Speicherort willkürlich und wenn ein Übertragungsfeh-
lereinpaarBitsverstümmelt,könntenwirMilchmitdemnichtverwandtenKonzeptMulch
verwechseln. Eineverteilte Repräsentation kann man sich dagegen so vorstellen, dass jedes
Konzept einen Punkt im mehrdimensionalen Raum darstellt, und wenn ein paar Bits ver-
stümmeltwerden,bewegtmansichzueinemnahegelegenenPunktinnerhalbdiesesRaums,
dereineähnlicheBedeutunghat.
86
--- PAGE 88 ---
2.4 DieStrukturvonAgenten
ZUSAMMENFASSUNG
DiesesKapitelwarsoetwaswieeinSchnelldurchlaufdurchdieKI,diewirhieralsdie
Wissenschaft des Agentenentwurfs verstanden haben. Die wichtigsten Punkte, an die
Siesicherinnernsollten,sind:
(cid:2) EinAgentistetwas,dasineinerUmgebungPerzepteempfängtunddorthandelt.Die
AgentenfunktionspezifiziertdieAktion,diederAgentalsReaktionaufeinebeliebige
Perzeptfolgeausführt.
(cid:2) DasLeistungsmaßbewertetdasVerhaltendesAgentenineinerUmgebung.Einratio-
nalerAgenthandeltso,dasseraufGrundlagederbisherigenPerzeptfolgedenErwar-
tungswertdesLeistungsmaßesmaximiert.
(cid:2) Zu einer Aufgabenumgebunggehören das Leistungsmaß, dieäußere Umgebung, die
Aktuatoren und die Sensoren. Beim Entwurf eines Agenten muss immer der erste
Schrittsein,dieAufgabenumgebungsovollständigwiemöglichzuspezifizieren.
(cid:2) AufgabenumgebungenvariierenentlangmehrererwichtigerDimensionen.DieUmge-
bung kann vollständig oder teilweise beobachtbar sein, eine Einzel- oder Multi-
agentenumgebung sein, deterministisch oder nichtdeterministisch, episodisch oder
sequenziell,statischoderdynamisch,diskretoderstetigundbekanntoderunbekannt
sein.
(cid:2) In den Fällen, in denen das Leistungsmaß unbekannt oder nur schwer korrekt zu
spezifizierenist,bestehteinerheblichesRisiko,dassderAgentdasfalscheZielopti-
miert.InsolchenFällensolltederAgentenentwurf dieUnsicherheit überdaswahre
Zielwiderspiegeln.
(cid:2) Das Agentenprogramm implementiert die Agentenfunktion. Es gibt eine Vielzahl
grundlegender EntwürfefürAgentenprogramme, die jeweils die Art der Informatio-
nenwiderspiegeln, dieexplizit angegeben undimEntscheidungsprozess verwendet
werden.DieEntwürfeunterscheidensichinEffizienz,KompaktheitundFlexibilität.
Der für ein Agentenprogramm geeignete Entwurf hängt von der Art der Umgebung
ab.
(cid:2) Einfache Reflexagenten reagieren direkt auf Perzepte, während modellbasierte Re-
flexagenten eineninternen Zustand verwalten, umAspektederWeltnachzuvollzie-
hen, die im aktuellen Perzept nicht offensichtlich sind. Zielbasierte Agenten han-
deln,umihreZielezuerreichen,undnutzenbasierteAgentenversuchen,ihreigenes
erwartetes„Glück“zumaximieren.
(cid:2) AlleAgentenkönnenihreLeistungdurchLernenverbessern.
87
--- PAGE 89 ---
2
IntelligenteAgenten
Bibliografische und historische Anmerkungen
DiezentraleRollevonAktioneninderIntelligenz–dasKonzeptdespraktischenSchlussfol-
gerns – geht mindestens bis zur Nikomachischen Ethik von Aristoteles zurück. Praktisches
SchlussfolgernwarauchdasThemavonMcCarthyseinflussreichemArtikel„Programswith
CommonSense“(1958).DieBereicheRobotikundKontrolltheoriebefassensichnaturgemäß
vorallemmitphysischenAgenten.DasKonzepteinesControllersinderKontrolltheorieent-
spricht dem eines Agenten in der KI. Es mag vielleicht überraschen, dass sich die KI den
größtenTeilihrerGeschichtemehraufisolierteKomponentenvonAgenten–Frage-Antwort-
Systeme,Theorembeweiser,visuelleSystemeundsoweiter–alsaufganzeAgentenkonzen-
trierthat.DieDiskussionvonAgentenimBuchvonGeneserethundNilsson(1987)wareine
bemerkenswerte Ausnahme. Dieganzheitliche Sichtauf Agenten ist heuteallgemein akzep-
tiert und zentrales Thema in neueren Büchern (Padgham und Winikoff, 2004; Jones, 2007;
PooleundMackworth,2017).
InKapitel1wurdendieWurzelndesRationalitätskonzeptsinderPhilosophieundderWirt-
schaftswissenschaft nachgezeichnet. InderKIwardasKonzeptanfangsnurvonperipherem
Interesse, bis es Mitte der 1980er Jahre in vielen Diskussionen über die passenden techni-
schen Grundlagen des Gebiets auftauchte. Jon Doyle sagte in einem Aufsatz (1983) voraus,
dass der Entwurf rationaler Agenten zu einer Kernaufgabe der KI werden würde, während
anderepopuläreThemensichabspaltenundneueDisziplinenbildenwürden.
Die sorgfältige Beachtung der Eigenschaften einer Umgebung und ihrer Konsequenzen für
denEntwurfvonrationalenAgentenistamdeutlichsteninderTraditionderKontrolltheorie
zuerkennen–beispielsweisebehandelnklassischeKontrollsysteme(DorfundBishop,2004;
Kirk,2004)vollständigbeobachtbare,deterministischeUmgebungen;stochastischeoptimale
Steuerung (KumarundVaraiya, 1986;Bertsekas und Shreve, 2007)beschäftigt sich mitteil-
weise beobachtbaren, stochastischen Umgebungen; und hybride Steuerung (Henzinger und
Sastry,1998;CassandrasundLygeros,2006)hatmitUmgebungenzutun,diesowohldiskrete
alsauchstetigeElementeenthalten.DieUnterscheidungzwischenvollständigundteilweise
beobachtbarenUmgebungenistebenfallszentralinderLiteraturzurdynamischenProgram-
mierung, die im Bereich des Operations Research entwickelt wurde (Puterman, 1994) und
diewirinKapitel17besprechen.
ObwohleinfacheReflexagentenfürdiebehavioristischePsychologievonzentralerBedeutung
waren(sieheKapitel1),werdensievondenmeistenKI-Forschernalszueinfachangesehen,
um einen großen Nutzen zu bringen. (Rosenschein (1985) und Brooks (1986) stellten diese
Annahme infrage; siehe Kapitel 26.) Es wurde viel Aufwand für die Suche nach effizien-
tenAlgorithmenbetrieben,umdenÜberblicküberkomplexeUmgebungenzubehalten(Bar-
Shalometal.,2001;Chosetetal.,2005;Simon,2006),dasmeistedavonimprobabilistischen
Umfeld.
ZielbasierteAgentenwerdenüberallvorausgesetzt, angefangen beideraristotelischen Sicht-
weise des praktischen Schlussfolgerns bis hin zu McCarthys frühen Arbeiten zur logischen
KI. Shakey (Fikes und Nilsson, 1971; Nilsson, 1984) war die erste robotische Verkörperung
eineslogischen, zielbasierten Agenten. EinevollständigelogischeAnalysevonzielbasierten
Agenten erschien in Genesereth und Nilsson (1987) und eine zielbasierte Programmierme-
thodik, genannt agentenorientierte Programmierung, wurde von Shoham (1993) entwickelt.
DeragentenbasierteAnsatzistheuteinderSoftwareentwicklungsehrbeliebt(Ciancariniund
Wooldridge, 2001). Er ist auch in den Bereich der Betriebssysteme eingedrungen, wo sich
AutonomicComputingaufComputersystemeundNetzwerkebezieht,diesichselbstmiteiner
Wahrnehmungs- und Handlungsschleife und maschinellen Lernmethoden überwachen und
steuern (Kephart und Chess, 2003). Eine Sammlung von Agentenprogrammen, die so ent-
worfenwurde,dassdieProgrammeineinerechtenMultiagentenumgebunggutzusammenar-
beiten,weistbekanntlichnotwendigerweiseModularitätauf–-dieProgrammeteilenkeinen
88
--- PAGE 90 ---
BibliografischeundhistorischeAnmerkungen
internen Zustand und kommunizieren miteinander nur über die Umgebung. Deshalb ist es
imBereichderMultiagentensystemeüblich,dasAgentenprogrammeineseinzelnenAgenten
als eine Sammlung von autonomen Subagenten zu entwerfen. In einigen Fällen kann man
sogar beweisen, dass das resultierende System die gleichen optimalen Lösungen liefert wie
einmonolithischerEntwurf.
Die zielbasierte Sichtweise von Agenten dominiert auch die Tradition der kognitiven Psy-
chologieaufdemGebietdesProblemlösens,beginnendmitdemenormeinflussreichenBuch
HumanProblemSolving(NewellundSimon,1972)ziehtsiesichdurchallespäterenArbeiten
vonNewell(Newell,1990).Ziele,dieweiteralsWünsche(allgemein)undAbsichten(aktuell
verfolgt) analysiert werden, sindzentral fürdieeinflussreicheTheoriederAgenten, dievon
MichaelBratman(1987)entwickeltwurde.
WieinKapitel1erwähnt,reichtdieEntwicklungderNutzentheoriealsGrundlagefürratio-
nales Verhalten HundertevonJahren zurück. InderKI hatdiefrüheForschungdenNutzen
zugunsten vonZielengemieden, miteinigenAusnahmen (FeldmanundSproull,1977).Das
Wiederaufleben des Interesses an probabilistischen Methoden in den 1980er Jahren führte
zur Akzeptanz der Maximierung des erwarteten Nutzens als allgemeinsten Rahmen für die
Entscheidungsfindung (Horvitz et al., 1988). Der Text von Pearl (1988) war der erste in der
KI,derdieWahrscheinlichkeits-undNutzentheorieeingehendbehandelte;seineDarstellung
praktischerMethodenzumSchlussfolgernundEntscheidungsfindungunterUnsicherheitwar
wahrscheinlich dergrößteEinzelfaktorfürdenschnellenWechsel zunutzenbasiertenAgen-
teninden1990erJahren(sieheKapitel16).DieFormalisierungvonReinforcementLearning
innerhalb eines entscheidungstheoretischen Rahmenstrugebenfalls zu dieser Verschiebung
bei(Sutton,1988).Esistbemerkenswert,dassfastdiegesamteKI-ForschungbisvorKurzem
davonausging, dassdasLeistungsmaßexaktundkorrektinFormeinerNutzen-oderBeloh-
nungsfunktionspezifiziertwerdenkann(Hadfield-Menelletal.,2017a;Russell,2019).
Der in IAbbildung2.15dargestellte allgemeine Entwurf fürlernende Agenten ist ein Klas-
sikerinderLiteraturzummaschinellenLernen(Buchananetal.,1978;Mitchell,1997).Bei-
spielefürdenEntwurf,wieerinProgrammenumgesetztist,reichenmindestensbiszuArthur
Samuels(1959,1967)LernprogrammfürdasDamespielzurück.LernendeAgentenwerdenin
denKapiteln19–22ausführlichbehandelt.
Einige frühe Arbeiten zu agentenbasierten Ansätzen werden von Huhns und Singh (1998)
undWooldridgeundRao(1999)gesammelt.TexteüberMultiagentensystemebieteneinegute
Einführungin vieleAspektedesAgentenentwurfs (Weiss, 2000a;Wooldridge,2009).Inden
1990er Jahren starteten mehrere Konferenzreihen, die sich mit Agenten beschäftigen, dar-
unterderInternationalWorkshoponAgent Theories,Architectures, andLanguages(ATAL),
die InternationalConference on AutonomousAgents (AGENTS) und dieInternationalCon-
ference onMulti-Agent Systems (ICMAS). Im Jahr 2002fusionierten diese drei zu der Inter-
nationalJoint Conference on AutonomousAgents and Multi-Agent Systems (AAMAS). Von
2000bis2012gabesjährlicheWorkshopszumThemaAgent-OrientedSoftwareEngineering
(AOSE).DieZeitschriftAutonomousAgentsandMulti-AgentSystemswurde1998gegründet.
SchließlichbietetDungBeetleEcology (HanskiundCambefort,1991)eineFüllevoninteres-
santen InformationenüberdasVerhalten vonMistkäfern.AufYouTubegibtesinspirierende
VideoaufnahmenvonihrenAktivitäten.
89
--- PAGE 92 ---
3
Problemlösen durch Suchen
3.1 Problemlösende Agenten.................................... 92
3.1.1 SuchproblemeundLösungen..................................... 94
3.1.2 Problemeformulieren ............................................ 95
3.2 Beispielprobleme.............................................. 95
3.2.1 StandardisierteProbleme......................................... 96
3.2.2 ProblemederrealenWelt......................................... 98
3.3 Suchalgorithmen.............................................. 100
3.3.1 Bestensuche...................................................... 102
3.3.2 DatenstrukturenfürdieSuche.................................... 103
3.3.3 RedundantePfade................................................ 103
3.3.4 LeistungsmessungderProblemlösung............................ 104
3.4 Uninformierte Suchstrategien .............................. 105
3.4.1 Breitensuche ..................................................... 106
3.4.2 DerAlgorithmusvonDijkstraoderuniformeKostensuche ....... 107
3.4.3 TiefensucheunddasSpeicherproblem ........................... 108
3.4.4 BeschränkteunditerativeTiefensuche ........................... 110
3.4.5 BidirektionaleSuche ............................................. 112
3.4.6 VergleichvonuninformiertenSuchalgorithmen.................. 114
3.5 Informierte (heuristische)Suchstrategien................. 114
3.5.1 GierigeBestensuche.............................................. 114
3.5.2 A(cid:3)-Suche ......................................................... 115
3.5.3 Suchkonturen .................................................... 119
3.5.4 Satisficing-Suche:UnzulässigeHeuristiken
undgewichteteA(cid:3)-Algorithmen.................................. 120
3.5.5 SpeicherbeschränkteSuche ...................................... 122
3.5.6 BidirektionaleheuristischeSuche................................ 126
3.6 Heuristische Funktionen ..................................... 128
3.6.1 DerEinflussderheuristischenGenauigkeitaufdieLeistung...... 128
3.6.2 HeuristikenausrelaxiertenProblemengenerieren................ 130
3.6.3 HeuristikenausTeilproblemengenerieren:Musterdatenbanken.. 131
3.6.4 HeuristikenmitLandmarkengenerieren.......................... 132
3.6.5 Lernen,besserzusuchen......................................... 134
3.6.6 HeuristikenausErfahrunglernen................................. 135
--- PAGE 93 ---
3
ProblemlösendurchSuchen
IndiesemKapitelerfahrenwir,wieeinAgentmittelsvorausschauenderEntscheidun-
geneineFolgevonAktionenfindet,mitdererseinZielerreichenkann.
Wenn die richtige Aktion nicht sofort ersichtlich ist, muss ein Agent möglicherweise vor-
ausplanen: Er muss eine Folge von Aktionen in Betracht ziehen, die einen Pfad zu einem
Zielzustand bilden. Ein solcher Agent wird als problemlösender Agent bezeichnet und der
Rechenprozess,denerausführt,heißtSuche.
Problemlösende Agenten verwenden atomare Repräsentationen, wie in Abschnitt 2.4.7 be-
schrieben–d.h.,dieZuständederWeltwerdenalsGanzesbetrachtet,ohnedassdieinterne
Struktur für die problemlösenden Algorithmen sichtbar ist. Agenten, die faktorisierte oder
strukturierte Repräsentationen von Zuständen verwenden, werden als planende Agenten
bezeichnet,wirbehandelnsieindenKapiteln7und11.
WirwerdenverschiedeneSuchalgorithmenbehandeln.IndiesemKapitelbetrachtenwirnur
dieeinfachstenUmgebungsarten:episodisch,einzelnerAgent,vollständigbeobachtbar,deter-
ministisch,statisch,diskretundbekannt.WirunterscheidenzwischeninformiertenAlgorith-
men, bei denen der Agent abschätzen kann, wie weit er vom Ziel entfernt ist, und uninfor-
miertenAlgorithmen,beidenenkeinesolcheAbschätzungverfügbarist.InKapitel4werden
dieEinschränkungen derUmgebungen gelockert undin Kapitel 5werden mehrere Agenten
betrachtet.
Dieses Kapitel verwendet die Konzepte der asymptotischen Komplexität (d.h. die O.n/-
Notation). Leser, die mit diesen Konzepten nicht vertraut sind, sollten Anhang A zurate
ziehen.
3.1 Problemlösende Agenten
Stellen Sie sich einen Agenten vor, der in seinem Urlaub durch Rumänien reist. Der Agent
möchte sich die Sehenswürdigkeiten ansehen, sein Rumänisch verbessern, das Nachtleben
genießen,einenKatervermeidenundsoweiter.DasEntscheidungsproblemistalsosehrkom-
plex.Nehmenwirnunan,derAgentbefindetsichgeradeinderStadtAradundhateinnicht
erstattungsfähiges Flugticket ab Bukarest für den nächsten Tag. Der Agent studiert die Stra-
ßenschilderundstelltfest,dassesdreiStraßengibt,dieausAradherausführen:eineinRich-
tungSibiu,einenach Timisoaraundeinenach Zerind.Keinerdieser OrteistdasZiel,d.h.,
fallsderAgentnichtmitderGeografieRumäniensvertrautist,weißernicht,welcherStraße
erfolgensoll.1
Hat der Agent keine zusätzlichen Informationen – d.h., die Umgebung ist unbekannt –, so
kannernichtsBesseres tun,alseinederAktionenzufälligauszuführen. Diese unglückliche
Situation wird in Kapitel 4 behandelt. In diesem Kapitel gehen wir davon aus, dass unsere
AgentenimmerZugangzuInformationenüberdieWelthaben,wiez.B.dieKarteinIAbbil-
dung3.1.MitdiesenInformationenkannderAgentdiesemVier-Phasen-Problemlösungspro-
zessfolgen:
(cid:2) Zielformulierung:DerAgentwähltdasZiel,Bukarestzuerreichen.Zielestrukturierendas
Verhalten, indem sie die Menge der Zielvorgaben und damit die zu berücksichtigenden
Aktioneneinschränken.
(cid:2) Problemformulierung:DerAgententwirfteineBeschreibungderZuständeundAktionen,
dienotwendigsind,umdasZielzuerreichen–einabstraktesModelldesrelevantenTeils
1 Wir gehen davon aus, dass es den meisten Lesern genauso geht und sie sich leicht in die Lage unseres
ahnungslosenAgentenversetzenkönnen.WirentschuldigenunsbeidenrumänischenLesern,beidenendieser
didaktischeTricknatürlichnichtgreift.
92
--- PAGE 94 ---
3.1 ProblemlösendeAgenten
Oradea
71
Neamt
Zerind 87
151
75
Iasi
Arad
140
92
Sibiu Fagaras
99
118
Vaslui
80
Rimnicu Vilcea
Timisoara
142
111 Lugoj 97 Pitesti 211
70 98
Hirsova
Mehadia 146 101 85 Urziceni
86
75 138
Bukarest
Drobeta 120
90
Craiova Giurgiu Eforie
Abbildung3.1:EinevereinfachteStraßenkarteeinesTeilsvonRumänien(EntfernungensindinMeilenangegeben).
der Welt. Für unseren Agenten ist ein gutes Modell, die Aktionen des Reisens von einer
Stadt zu einer anderen Stadt zu betrachten, und daher ist die einzige Tatsache über den
ZustandderWelt,diesichaufgrundeinerAktionändert,dieaktuelleStadt.
(cid:2) Suche:BevorderAgenteineAktioninderrealenWeltausführt,simulierterAktionsfolgen
inseinemModellundsuchtsolange,bisereineFolgefindet,diedasZielerreicht–diese
wirdalsLösungbezeichnet.DerAgentwirdmöglicherweiseerstmehrereFolgensimulie-
ren, die das Ziel nicht erreichen, aber schließlich wird er eine Lösung finden (z.B. von
Arad nach Sibiunach Fagaras nach Bukarest) odererwird feststellen, dasskeineLösung
möglichist.
(cid:2) Ausführung: Der Agent kann nun die Aktionen in der Lösung ausführen, eine nach der
anderen.
J
Einewichtige Eigenschaft ineinervollständigbeobachtbaren, deterministischen, bekannten
Umgebungist,dassdieLösungjedesProblemseinefesteAbfolgevonAktionenist:Fahrnach
Sibiu, dann nach Fagaras, dann nach Bukarest. Ist das Modell korrekt, so kann der Agent,
sobaldereineLösunggefundenhat,seinePerzepteignorieren,währenderdieAktionenaus-
führt–sozusagen dieAugen schließen –,dadieLösunggarantiert zumZiel führt.Kontroll-
theoretikernennen dieseinOpen-Loop-System:DasIgnorieren derPerzepte unterbrichtdie
Schleifezwischen Agent undUmwelt.Besteht dieMöglichkeit,dassdasModellfalschoder
dieUmgebungnichtdeterministischist,dannwärederAgentsicherer,wennereinenClosed-
Loop-Ansatzverfolgenwürde,derdiePerzepteüberwacht(sieheAbschnitt4.4).
InteilweisebeobachtbarenodernichtdeterministischenUmgebungenwäreeineLösungeine
Verzweigungsstrategie, dieverschiedenezukünftigeAktionenempfiehlt,jenachdem,welche
Perzepte eintreffen. ZumBeispiel könntederAgent planen,vonArad nachSibiuzufahren,
aber erkönnteeinen Notfallplan brauchen, fallserzufällig inZerind ankommtoderauf ein
SchildmitderAufschrift„DrumÎnchis“(Straßegesperrt)trifft.
93
--- PAGE 95 ---
3
ProblemlösendurchSuchen
3.1.1 Suchprobleme undLösungen
EinSuchproblemkannformaldurchfolgendeElementedefiniertwerden:
(cid:2) EineMengemöglicherZustände,indenensichdieUmgebungbefindenkann.Wirnennen
diesdenZustandsraum.
(cid:2) DerAnfangszustand,indemderAgentbeginnt.ZumBeispiel:Arad.
(cid:2) Eine Menge von einem oder mehreren Zielzuständen. Manchmal gibt es nur einen Ziel-
zustand (z.B. Bukarest), manchmal gibt es eine kleine Menge alternativer Zielzustände
und manchmal ist das Ziel durch eine Eigenschaft definiert, die für viele Zustände gilt
(möglicherweise eine unendliche Anzahl). In einer Staubsaugerwelt könnte das Ziel bei-
spielsweisesein,dassankeinemOrtSchmutzvorhandenist,unabhängigvonallenande-
renFaktenüberdenZustand.WirkönnenalledreidieserMöglichkeitenberücksichtigen,
indem wir eine IS-GOAL-Methodefür ein Problem angeben. In diesem Kapitel sagen wir
der Einfachheit halber manchmal „das Ziel“, meinen damit aber „jeden der möglichen
Zielzustände“.
(cid:2) DieAktionen,diedemAgentenzurVerfügungstehen.FüreinengegebenenZustandsgibt
dieFunktion ACTIONS .s/eineendliche2 MengevonAktionenzurück,dieinsausgeführt
werdenkönnen.Wirsagen,dassjededieserAktioneninsanwendbarist.EinBeispiel:
ACTIONS .Arad/DfNachSibiu;NachTimisoara;NachZerindg
(cid:2) Ein Transitionsmodell, das beschreibt, was jede Aktion bewirkt. RESULT(s,a) gibt den
Zustand zurück, deraus der Ausführungvon Aktiona im Zustand sresultiert. ZumBei-
spiel:
RESULT .Arad;NachZerind/DZerind
(cid:2) Eine Aktionskostenfunktion, ACTION-COST .s;a;s0/ (in Programmierschreibweise) oder
c.s;a;s0/(inmathematischer Schreibweise), diedienumerischenKosten derAnwendung
vonAktionaimZustandsangibt,umZustands0zuerreichen.EinproblemlösenderAgent
sollte eine Kostenfunktion verwenden, die sein eigenes Leistungsmaß widerspiegelt; für
Agenten,dieeineRoutefinden,könntendieKosteneinerAktionbeispielsweisedieLänge
inKilometernoderMeilensein(wieinIAbbildung3.1zusehen)odereskönntedieZeit
sein,diebenötigtwird,umdieAktiondurchzuführen.
Eine Aktionsfolge bildet einen Pfad, und eine Lösung ist ein Pfad vom Anfangszustand zu
einem Zielzustand. Wir nehmen an, dass die Aktionskosten additiv sind, d.h., die Gesamt-
kosten eines Pfads setzen sich aus den einzelnen Aktionskosten zusammen. Eine optimale
Lösunghatdieniedrigsten Pfadkostenunterallen Lösungen.Indiesem Kapitelnehmenwir
an,dassalleAktionskostenpositivsind,umgewisseKomplikationenzuvermeiden.3
2 FürProblememiteinerunendlichenAnzahlvonAktionenwürdenwirTechnikenbenötigen,dieüberden
InhaltdiesesKapitelshinausgehen.
3 BeijedemProblemmiteinemZyklusmitnegativenNettokostenbestehtdiekostenoptimaleLösungdarin,
diesen Zyklusunendlich oftauszuführen. DieBellman-Ford- und Floyd-Warshall-Algorithmen(werden hier
nicht besprochen) behandeln Aktionenmit negativen Kosten, solange es keine negativen Zyklengibt. Es ist
einfach,Null-Kosten-Aktionenzuberücksichtigen,solangedieAnzahlderaufeinanderfolgendenNull-Kosten-
Aktionenbegrenztist.ZumBeispielkönntenwireinenRoboterhaben,beidemzwardieBewegungenetwas
kosten,nichtjedochdieDrehungum90o;dieAlgorithmenindiesemKapitelkönnendamitumgehen,solange
nichtmehralsdreiaufeinanderfolgende90o-Drehungenerlaubtsind.EsgibtaucheineKomplikationbeiPro-
blemen,dieeineunendlicheAnzahlvonbeliebigkleinenAktionskostenhaben.BetrachtenSieeineVersiondes
Zeno-Paradoxons,beidemeseineAktiongibt,mitdermansichaufhalbemWegzumZielbewegt,undzwar
zudenhalbenKostendervorherigenBewegung.DiesesProblemhatkeineLösungmiteinerendlichenAnzahl
vonAktionen,aberumzuverhindern,dasseineSucheeineunbegrenzteAnzahlvonAktionenausführt,ohne
dasZieljemalsganzzuerreichen,könnenwirfordern,dassalleAktionskostenmindestens(cid:3)sind,wobei(cid:3)ein
kleinerpositiverWertist.
94
--- PAGE 96 ---
3.2 Beispielprobleme
DerZustandsraumkannalsGraphdargestellt werden,indemdieKnotenZuständeunddie
gerichtetenKantenzwischenihnenAktionensind.DieinIAbbildung3.1gezeigteKartevon
Rumänien ist ein solcher Graph, wobei jede Straße zwei Aktionen bezeichnet, eine in jeder
Richtung.
3.1.2 Probleme formulieren
Unsere Formulierung des Problems, nach Bukarest zu gelangen, ist ein Modell – eine abs-
traktemathematischeBeschreibung–undnichtsReales.VergleichenSiedieeinfacheatomare
Zustandsbeschreibung vonArad miteinertatsächlichen Überlandfahrt, bei derderZustand
der Welt so viele Dinge umfasst: die Mitreisenden, das aktuelle Radioprogramm, die Land-
schaft vor dem Fenster, die Nähe von Ordnungshütern, die Entfernung zur nächsten Rast-
stätte,derZustandderStraße,dasWetter,derVerkehrundsoweiter.AlldieseÜberlegungen
lassenwirinunseremModellaußenvor,weilsieirrelevantsindfürdasProblem,eineRoute
nachBukarestzufinden.
DerProzess,DetailsauseinerDarstellungzuentfernen,wirdalsAbstraktionbezeichnet.Eine
guteProblemformulierunghatdenrichtigen GradanDetailgenauigkeit. Wären dieAktionen
auf derEbenevon„bewege den rechten Fußeinen Zentimeternach vorne“oder„drehe das
Lenkrad um ein Grad nach links“, dann würde der Agent wahrscheinlich nie den Weg aus
demParkplatzfinden,geschweigedennnachBukarest.
Können wir den angemessenen Abstraktionsgrad genauer bestimmen? Stellen Sie sich vor,
dass die abstrakten Zustände und Aktionen, die wir gewählt haben, großen Mengen von
detailliertenWeltzuständenunddetailliertenAktionsfolgenentsprechen.Betrachtenwirnun
eineLösungdesabstraktenProblems:zumBeispielderWegvonAradnachSibiunachRim-
nicuVilceanachPitestinachBukarest.DieseabstrakteLösungentsprichteinergroßenAnzahl
vonausführlicherenPfaden.ZumBeispielkönntenwiraufderFahrtvonSibiunachRimnicu
VilceadasRadioeingeschaltethabenundesdannfürdenRestderStreckeausschalten.
DieAbstraktionistgültig,wennwirjedeabstrakteLösungzueinerLösunginderdetaillierte-
renWeltausarbeitenkönnen;einehinreichendeBedingungist,dassesfürjedendetaillierten
Zustand, z.B. „in Arad“, einen detaillierten Pfad zu einem Zustand gibt, der beispielsweise
„inSibiu“heißt,undsoweiter.4DieAbstraktionistnützlich,wenndieAusführungjederder
Aktionen in der Lösung einfacher ist als das ursprüngliche Problem; in unserem Fall kann
dieAktion„fahrevonAradnachSibiu“ohneweitereSucheoderPlanungvoneinemdurch-
schnittlichen Fahrer ausgeführt werden. Bei der Wahl einer guten Abstraktion geht es also
darum, so viele Einzelheiten wie möglich zu entfernen, dabei aber die Validität zu erhalten
undsicherzustellen, dass dieabstrakten Aktionenleicht auszuführen sind.OhnedieFähig-
keit,sinnvolleAbstraktionenzukonstruieren,wärenintelligenteAgentenmitderrealenWelt
völligüberfordert.
3.2 Beispielprobleme
Der Problemlösungsansatz wurde auf einen umfangreichen Bereich vonAufgabenumgebun-
gen angewendet. Wir listen hier einige der bekanntesten auf, wobei wir zwischen standar-
disierten und realen Problemen unterscheiden. Ein standardisiertes Problem dient derVer-
anschaulichung oder Übung verschiedener Problemlösungsmethoden. Es kann knapp und
genau beschrieben werden undeignet sich daheralsMaßstabfürForscher, umdieLeistung
vonAlgorithmenzuvergleichen.EinrealesProblem,wiez.B.dieRoboternavigation,istein
Problem, dessen Lösungen tatsächlich verwendet werden und dessen Formulierung extrem
spezifischundnichtstandardisiertist,dabeispielsweisejederRoboterunterschiedlicheSen-
sorenhat,dieunterschiedlicheDatenliefern.
4 SieheAbschnitt11.4.
95
--- PAGE 97 ---
3
ProblemlösendurchSuchen
3.2.1 Standardisierte Probleme
EinGitterwelt-ProblemisteinezweidimensionalerechteckigeAnordnungvonquadratischen
Zellen,indenensichAgentenvonZellezuZellebewegenkönnen.Typischerweisekannsich
derAgentzujederhindernisfreienbenachbartenZellebewegen–horizontalodervertikalund
ineinigenProblemenauchdiagonal.ZellenkönnenObjekteenthalten,diederAgentaufneh-
men, verschieben oder auf die er anderweitig einwirken kann; eine Wand oder ein anderes
unüberwindbaresHindernisineinerZellehinderneinenAgenten daran,sichindieseZelle
zu begeben. Die Staubsaugerwelt aus Abschnitt 2.1 kann als Gitterwelt-Problem wie folgt
formuliertwerden:
(cid:3) Zustände: Ein Zustand dieser Welt gibt Auskunft darüber, welche Objekte sich in wel-
chenZellenbefinden.FürdieStaubsaugerweltsinddieObjektederAgentundeventueller
Schmutz. In der einfachen Zwei-Zellen-Version kann sich der Agent in einer der beiden
Zellen befindenundjedeZellekannentwederSchmutzenthalten odernicht,esgibtalso
2(cid:4)2(cid:4)2 D 8Zustände (IAbbildung3.2).Im Allgemeinen hat eine Staubsaugerumgebung
mitnZellenn(cid:4)2nZustände.
R
L R
L
S S
R R
L R L R
L L
S S
S S
R
L R
L
S S
Abbildung3.2:DerGraphdesZustandsraumsfürdiezweizelligeStaubsaugerwelt.Esgibt8ZuständeunddreiAktionenfürjedenZustand:L=
Links,R=Rechts,S=Saugen.
(cid:3) Anfangszustand:AlsAnfangszustandkanneinbeliebigerZustandfestgelegtwerden.
(cid:3) Aktionen:InderZwei-Zellen-WelthabenwirdreiAktionendefiniert:Saugen,Links-Bewe-
gungundRechts-Bewegung.IneinerzweidimensionalenmehrzelligenWeltbenötigenwir
mehrBewegungsaktionen. Wirkönnten AufwärtsundAbwärtshinzufügen, wodurch wir
vier absolute Bewegungsaktionen hätten, oder wir könnten zu egozentrischen Aktionen
übergehen,dierelativzumStandpunktdesAgentendefiniertsind–zumBeispielVorwärts,
Rückwärts,DrehenRechtsundDrehenLinks.
(cid:3) Transitionsmodell:SaugenentferntjeglichenSchmutzausderZelledesAgenten;Vorwärts
bewegtdenAgentenumeineZelleindieRichtung,indieerblickt,esseidenn,ertrifftauf
eineWand,indiesemFallhatdieAktionkeineWirkung.Rückwärtsbewegt denAgenten
in die entgegengesetzte Richtung, während DrehenRechts und DrehenLinks seine Blick-
richtungum90°ändern.
96
--- PAGE 98 ---
3.2 Beispielprobleme
(cid:3) Zielzustände:DieZustände,indenenjedeZellesauberist.
(cid:3) Aktionskosten:JedeAktionkostet1.
Eineweitere ArtderGitterwelt istdasSokoban-Puzzle,beidemdasZieldesAgenten darin
besteht, eine Anzahl von Kisten, die über das Gitter verstreut sind, an bestimmte Lagerorte
zuschieben.EskannmaximaleineKisteproZellegeben.WennsicheinAgentineineZelle
mit einer Kiste vorwärts bewegt und sich auf der anderen Seite der Kiste eine leere Zelle
befindet, dannbewegen sich sowohl dieKiste alsauch derAgent vorwärts. DerAgent kann
eine Kiste nicht in eine andere Kiste oder eine Wand schieben. Für eine Welt mit n Zellen
ohneHindernisseundbKistengibtesn(cid:2)nŠ=.bŠ.n(cid:5)b/Š/Zustände;zumBeispielgibtesauf
einem8(cid:2)8-GittermiteinemDutzendKistenüber200BillionenZustände.
BeieinemSchiebepuzzlewirdeineReihevonSpielsteinen(manchmalauchBlöckeoderTeile
genannt)ineinemGittermiteinemodermehrerenLeerräumenangeordnet,sodasseinigeder
Spielsteine in den Leerraum gleiten können. Eine Variante ist das Puzzle „RushHour“, bei
dem Autosund Lkws auf einem 6(cid:2)6-Gitter verschoben werden, umein Auto aus dem Stau
zubefreien.DiewohlbekanntesteVarianteistdas8-Puzzle(IAbbildung3.3),dasauseinem
3(cid:2)3-GittermitachtnummeriertenSpielsteinenundeinemleerenFeldbesteht,sowiedas15-
Puzzleaufeinem4(cid:2)4-Gitter.DasZielistes,einenbestimmtenZielzustandzuerreichen,wie
beispielsweisedenZustandrechtsinderAbbildung.DieStandardformulierungdes8-Puzzles
lautet:
(cid:3) Zustände:EineZustandsbeschreibungspezifiziertdieLagedereinzelnenSpielsteine.
(cid:3) Anfangszustand: Jeder beliebige Zustand kann als Anfangszustand festgelegt werden.
Beachten Sie,dasseineParitätseigenschaft denZustandsraumunterteilt–jedesgegebene
Ziel kann von genau der Hälfte der möglichen Anfangszustände erreicht werden (siehe
Übung3.6aufderWebsite).
(cid:3) Aktionen:InderphysischenWelthandeltessichzwarumeinenSpielstein,derverschoben
wird,dochdieeinfachste Art,eineAktionzubeschreiben, istsich vorzustellen, dassdas
leereFeldeineLinks-,Rechts-,Oben-oderUnten-Bewegung ausführt.WenndasLeerfeld
amRandoderineinerEckeliegt,sindnichtalleAktionenanwendbar.
(cid:3) Transitionsmodell:BildeteinenZustandundeineAktionaufeinenresultierendenZustand
ab;wennwirz.B.LinksaufdenStartzustandinIAbbildung3.3anwenden,sosindbeim
resultierendenZustanddie5unddasLeerfeldvertauscht.
(cid:3) Zielzustand: Obwohl jederbeliebige Zustand dasZiel sein könnte,nehmenwir normaler-
weiseeinenZustand,beidemdieZahleninderrichtigenReihenfolgesindwieinIAbbil-
dung3.3.
(cid:3) Aktionskosten:JedeAktionkostet1.
7 2 4 1 2
5 6 3 4 5
8 3 1 6 7 8
Startzustand Zielzustand
Abbildung3.3:EintypischesBeispielfürein8-Puzzle.
97
--- PAGE 99 ---
3
ProblemlösendurchSuchen
Beachten Sie, dass jede ProblemformulierungAbstraktionen beinhaltet. Die8-Puzzle-Aktio-
nenwerdenaufihreAnfangs- undEndzuständeabstrahiert, wobeidieZwischenpositionen,
bei denen der Spielstein verschoben wird, ignoriert werden. Wir haben Aktionen wie das
SchüttelndesBretts, wenndieSpielsteinesichverklemmthaben,„wegabstrahiert“ unddas
AusbauenderSteinemiteinemMessermitanschließendemWiedereinsetzenausgeschlossen.
ÜbrigbleibteineBeschreibung derRegeln, diealleDetails derphysikalischen Manipulatio-
nenvermeidet.
Unserletztes standardisiertes ProblemwurdevonDonaldKnuth(1964)entwickelt undver-
anschaulicht,wieunendlicheZustandsräumeentstehenkönnen.Knuthvermutete,dassman
von der Zahl 4 aus durch eine Folge von Quadratwurzel-, Fakultät- und Abrundungsopera-
tionen(Gaußklammer)jedebeliebigepositiveganzeZahlerreichenkann.ZumBeispielkann
manfolgendermaßenvonder4zur5kommen:
v
j u u t s r q p k
.4Š/Š D5
DieProblemstellungisteinfach:
(cid:3) Zustände:positivereelleZahlen
(cid:3) Anfangszustand:4
(cid:3) Aktionen:AnwendenvonQuadratwurzel-,Fakultät-oderAbrundungsoperationen(Fakul-
tätnurfürganzeZahlen)
(cid:3) Transitionsmodell:wiedurchdiemathematischenDefinitionenderOperationengegeben
(cid:3) Zielzustand:diegewünschtepositiveganzeZahl
(cid:3) Aktionskosten:JedeAktionkostet1.
Der Zustandsraum für dieses Problem ist unendlich: Für jede ganze Zahl größer als 2
wird der Fakultätsoperator immer eine größere ganze Zahl liefern. Das Problem ist inter-
essant, weil es sehr große Zahlen untersucht: Der kürzeste Weg zu 5 führt über (4!)! =
620.448.401.733.239.439.360.000. Unendliche Zustandsräume treten häufig bei Aufgaben
auf, die mathematische Ausdrücke, Schaltkreise, Beweise, Programme und andere rekursiv
definierteObjektegenerieren.
3.2.2 Probleme der realen Welt
Wir haben bereits gesehen, dass das Problem, eine Route zu finden (Wegfindungsproblem),
durch die vorgegebenen Orte und die Übergänge entlang der Kanten zwischen ihnen defi-
niertist.AlgorithmenzurWegfindungwerdeninvielenAnwendungeneingesetzt.Einige,wie
z.B.WebsitesundimAutoeingebauteSysteme,dieRichtungsanweisungengeben,sindrela-
tiv einfache Erweiterungen des Rumänien-Beispiels. (Die Hauptkomplikationen sind unter-
schiedlicheKostenaufgrundvonverkehrsbedingtenVerzögerungen sowieUmleitungenauf-
grund von Straßensperrungen.) Andere, wie beispielsweise das Routing von Videostreams
in Computernetzen, militärische Operationsplanung und die Reiseplanung von Fluggesell-
schaften,beinhaltenerheblich komplexereSpezifikationen.Betrachten SiedieProblemebei
Flugreisen,dievoneinerWebsitezurReiseplanunggelöstwerdenmüssen:
(cid:3) Zustände: Jeder Zustand umfasst natürlich eine Position (z.B. einen Flughafen) und die
aktuelle Zeit. Da die Kosten einer Aktion (eines Flugabschnitts) von früheren Abschnit-
ten,ihrenFlugtarifenundihremStatusalsnationaleroderinternationalerFlugabhängen
können,mussderZustandaußerdemzusätzlicheInformationenüberdiese„historischen“
Aspekteerfassen.
98
--- PAGE 100 ---
3.2 Beispielprobleme
(cid:3) Anfangszustand:DerHeimatflughafendesBenutzers.
(cid:3) Aktionen:EinenbeliebigenFlugvomaktuellenStandortineinerbeliebigenSitzklasseneh-
men,derspäteralsdieaktuelleUhrzeitstartetundgenügendZeitfüreinenTransferinner-
halbdesFlughafenslässt,fallserforderlich.
(cid:3) Transitionsmodell:DerZustand,dersichnachAusführeneinesFlugsergibt,hatdasFlug-
zielalsneuePositionunddieAnkunftszeitdesFlugsalsneueZeit.
(cid:3) Zielzustand:EineStadtalsBestimmungsort.ManchmalkanndasZielauchkomplexersein,
z.B.„miteinemNonstop-FlugamZielortankommen“.
(cid:3) Aktionskosten:EineKombinationausmonetärenKosten,Wartezeit,Flugzeit,Zoll-undEin-
wanderungsprozeduren,Sitzqualität,Tageszeit,Flugzeugtyp,BonuspunktenfürVielflieger
undsoweiter.
KommerzielleReiseberatungssystemeverwendeneineProblemformulierungdieserArt–mit
vielen zusätzlichen Komplikationen,dieentstehen, umdieverworrenen Tarifstrukturen der
Fluggesellschaftenzuhandhaben.JedererfahreneReisendeweißjedoch,dassnichtalleFlug-
reisen nach Plan verlaufen. Ein wirklich gutes System sollte Notfallpläne enthalten – was
passiert,wenndieserFlugVerspätunghatundderAnschlussverpasstwird?
Tourenplanungsprobleme beschreiben nicht ein einzelnes Ziel, sondern eine Reihe von
Orten, diebesucht werden müssen. Das Problem desHandlungsreisenden (TravelingSales-
person Problem, TSP) ist ein Tourenplanungproblem, bei dem jede Stadt auf einer Karte
besuchtwerdenmuss.DasZielistes,eineTourmitKosten<C zufinden(oderinderOpti-
mierungsversion,eineTourmitdengeringstmöglichenKostenzufinden).Eswurdenenorme
Anstrengungen unternommen,um dieFähigkeiten derTSP-Algorithmen zu verbessern. Die
Algorithmen können auch auf Fahrzeugflotten erweitert werden. Ein Such- und Optimie-
rungsalgorithmus für die Routenplanung von Schulbussen in Boston sparte beispielsweise
5MillionenUS-Dollar,reduzierte dasVerkehrsaufkommen unddieLuftverschmutzungund
spartesowohlFahrernalsauchdenSchülernZeit(Bertsimasetal.,2019).NebenderPlanung
von Fahrten wurden Suchalgorithmen auch für Aufgaben wie die Planung der Bewegun-
gen von automatischen Leiterplattenbohrern und von Maschinen zur Regalbestückung in
Werkshalleneingesetzt.
EinVLSI-LayoutproblemerfordertdiePositionierungvonMillionenvonKomponentenund
Verbindungen auf einem Chip, um Fläche, Schaltverzögerungen und Streukapazitäten zu
minimierenunddieProduktionsausbeutezu maximieren.DasLayout-Problemkommtnach
derlogischenEntwurfsphaseundwirdnormalerweiseinzweiTeileaufgeteilt:Zellen-Layout
und Kanal-Routing. Beim Zellen-Layout werden die elementaren Komponenten der Schal-
tung in Zellen gruppiert, von denen jede eine bestimmte Funktion ausführt. Jede Zelle hat
eine feste Gehäusegeometrie (Größe und Umriss) und benötigt eine bestimmte Anzahl von
Verbindungen zu den anderen Zellen. Ziel ist es, die Zellen so auf dem Chip zu platzieren,
dasssiesichnichtüberlappenunddasszwischendenZellenPlatzfürdieVerbindungsdrähte
ist.BeimKanal-RoutingwirdfürjedenDrahteinbestimmterWegdurchdieLückenzwischen
denZellengefunden.DieseSuchproblemesindextremkomplex,dochesistabsolutsinnvoll,
siezulösen.
Die Roboternavigation ist eine Verallgemeinerung des zuvor beschriebenen Problems der
Wegfindung. Ein Roboter muss nicht bestimmten Pfaden folgen (wie z.B. den Straßen in
Rumänien), sondern kann umherwandern und so seine eigenen Pfade erstellen. Für einen
kreisförmigen Roboter, der sich auf einer ebenen Fläche bewegt, ist der Raum im Wesentli-
chenzweidimensional.HatderRoboterArmeundBeine,dieebenfallsgesteuertwerdenmüs-
sen,dannwirdderSuchraummehrdimensional–eineDimensionfürjedenGelenkwinkel.Es
sindfortschrittlicheTechnikenerforderlich,umdenimWesentlichenstetigenSuchraumend-
lichzumachen(sieheKapitel26).ZusätzlichzuderKomplexitätdesProblemsmüssenreale
99
--- PAGE 101 ---
3
ProblemlösendurchSuchen
RoboterauchaufFehlerinihrenSensormessungenunddenMotorsteuerungen,mitteilweiser
BeobachtbarkeitundmitanderenAgenten,diedieUmgebungverändernkönnten,reagieren.
Die automatische Montageablaufsteuerung komplexer Objekte (wie z.B. Elektromotoren)
durcheinenRoboteristseit den1970erJahren gängige Branchenpraxis. Algorithmenfinden
zunächsteinedurchführbareMontagereihenfolgeundarbeitendannanderOptimierungdes
Prozesses. Die Minimierung des Anteils an manueller menschlicher Arbeit am Fließband
kann zu erheblichen Zeit- und Kosteneinsparungen führen. Bei Montageablaufproblemen
bestehtdasZieldarin,eineReihenfolgezufinden,inderdieTeileeinesObjektszusammen-
gebaut werden. Wenn die falsche Reihenfolge gewählt wird, gibt es keine Möglichkeit, ein
Teil später im Ablauf hinzuzufügen, ohne bereits geleistete Arbeit rückgängig zu machen.
DasÜberprüfeneinerAktioninderAbfolgeaufDurchführbarkeitisteinschwierigesgeome-
trisches Suchproblem, das eng mit der Roboternavigation verwandt ist. Daher ist die Gene-
rierungvonzulässigenAktionenderteureTeilderMontageablaufsteuerung.Jederpraktische
Algorithmus darf nicht den gesamten, sondern nureinen winzigen Teil des Zustandsraums
untersuchen. Einwichtiges Montageproblem ist der Proteinentwurf, bei dem dasZiel darin
besteht, eine Sequenz von Aminosäuren zu finden, die sich zu einem dreidimensionalen
ProteinmitdenpassendenEigenschaftenfaltet,umeineKrankheitzuheilen.
3.3 Suchalgorithmen
EinSuchalgorithmusbekommteinSuchproblemalsEingabeundgibtentwedereineLösung
oder eine Fehlermeldung zurück. In diesem Kapitel betrachten wir Algorithmen, die einen
Suchbaumüber den Zustandsraumgraphen legen, verschiedene Pfade vom Anfangszustand
ausbildenundversuchen,einenPfadzufinden,dereinenZielzustanderreicht.JederKnoten
im Suchbaum entspricht einem Zustand im Zustandsraum und die Kanten im Suchbaum
entsprechenAktionen.DieWurzeldesBaumsstelltdenAnfangszustanddesProblemsdar.
Esistwichtig,denUnterschiedzwischendemZustandsraumunddemSuchbaumzuverste-
hen.DerZustandsraum beschreibt die(möglicherweise unendliche) MengederZuständein
derWeltsowiedieAktionen,dieÜbergänge voneinem Zustandzueinemanderen ermögli-
chen.DerSuchbaumbeschreibtPfadezwischendiesenZuständen,diezumZielführen.Der
Suchbaum kann mehrere Pfade zu jedem (und damit mehrere Knoten für jeden) beliebigen
Zustand haben, aber jeder Knoten im Baum hat einen eindeutigen Pfad zurück zur Wurzel
(wieinallenBäumen).
IAbbildung3.4zeigtdieerstenSchrittebeiderSuchenacheinemPfadvonAradnachBuka-
rest.DerWurzelknotendesSuchbaumsbefindetsichimAnfangszustand, Arad.Wirkönnen
denKnotenexpandieren,indemwirdieverfügbarenAKTIONENfürdiesenZustandbetrach-
ten unddieRESULT-Funktionverwenden, umzu sehen, wohindieseAktionen führen.Und
wirkönnenfürjedenderresultierendenZuständeeinenneuenKnotenerzeugen(Kindknoten
oderNachfolgerknotengenannt).JederKindknotenhatAradalsseinenElternknoten.
Nunmüssenwirauswählen,welchen dieserdreiKindknotenwiralsNächstesuntersuchen.
Dies ist das Wesen der Suche – erst einmal eine Option zu verfolgen und sich die anderen
für später vorzubehalten. Angenommen wir wählen, zuerst Sibiu zu expandieren. IAbbil-
dung3.4(unten)zeigtdasErgebnis:eineMengevon6nichtexpandiertenKnoten(grünund
fettumrandet).WirnennendiesdieGrenze desSuchbaums.Wirsagen, dassjederZustand,
für den ein Knoten erzeugt wurde, erreicht (reached) wurde (unabhängig davon, ob dieser
Knoten expandiert wurde oder nicht).5 IAbbildung 3.5 zeigt den Suchbaum, der auf den
Zustandsraumgraphengelegtwurde.
5 EinigeAutorenbezeichnendieGrenzealsoffeneListe,wassowohlgeografischwenigeraussagekräftigals
auchrechnerischwenigergeeignetist,daeineWarteschlangehiereffizienteristalseineListe.DieseAutoren
verwendendenBegriffgeschlosseneListe,umsichaufdieMengederzuvorexpandiertenKnotenzubeziehen,
wasinunsererTerminologiedenerreichtenKnotenabzüglichderGrenzeentspricht.
100
--- PAGE 102 ---
3.3 Suchalgorithmen
Abbildung3.4:DreipartielleSuchbäume,umeineRoutevonAradnachBukarestzufinden.Knoten,dieexpandiertwurden,
sindviolettmitfettgedrucktenNamen;KnotenaufderGrenze,dieerzeugt,abernochnichtexpandiertwurden,sindgrün;die
MengederZustände,diediesenbeidenKnotentypenentsprechen,giltalserreicht.Knoten,diealsNächstesgeneriertwerden
könnten,sindmithellgestricheltenLiniendargestellt.BeachtenSie,dassesimunterenBaumeinenZyklusvonAradnachSibiu
nachAradgibt;daskannkeinoptimalerPfadsein,alsosolltedieSuchevondortausnichtfortgesetztwerden.
Abbildung3.5:EineFolgevonSuchbäumen,diedurcheineGraphensuchefürdasRumänien-ProblemausAbbildung3.1gene-
riertwurde.InjederStufehabenwirjedenKnotenaufderGrenzeexpandiert,indemwirjedenPfadmitallenanwendbaren
Aktionenerweiterthaben,dienichtzueinemZustandführen,derbereitserreichtwurde.BeachtenSie,dassinderdrittenStufe
dieobersteStadt(Oradea)zweiNachfolgerhat,diebeidebereitsdurchanderePfadeerreichtwurden,sodasskeinePfadevon
Oradeaauserweitertwerden.
Beachten Sie,dassdieGrenze zwei Regionen desZustandsraumgraphen trennt:eine innere
Region, in derjederZustand expandiert wurde, undeineäußere Region mitZuständen, die
nochnichterreichtwurden.DieseEigenschaftistinIAbbildung3.6dargestellt.
101
--- PAGE 103 ---
3
ProblemlösendurchSuchen
Abbildung3.6:DieTrennungseigenschaftderGraphensuche,dargestelltaneinemProblemmitrechteckigemGitter.DieGrenze
(grün)trenntdasInnere(violett)vomÄußeren(schwachgestrichelt).DieGrenzeistdieMengederKnoten(undderentsprechen-
denZustände),dieerreicht,abernochnichtexpandiertwurden;dasInnereistdieMengederKnoten(undderentsprechenden
Zustände),dieexpandiertwurden;unddasÄußereistdieMengederZustände,dienochnichterreichtwurden.In(a)wurde
nurdieWurzelexpandiert.In(b)istderobersteGrenzknotenexpandiert.In(c)wurdendieübrigenNachfolgerderWurzelim
Uhrzeigersinnexpandiert.
3.3.1 Bestensuche
Wie wird entschieden, welcher Knoten der Grenze als Nächstes erweitert werden soll? Ein
sehr allgemeiner Ansatz wird als Bestensuche (Best-First Search, BFS) bezeichnet, bei der
wir einen Knoten n mit dem minimalen Wert einer Evaluierungsfunktion f.n/ auswählen.
IAbbildung3.7zeigtdenAlgorithmus.BeijederIterationwählenwireinenKnotenaufder
Grenzemitminimalemf.n/-Wert,gebenihnzurück,wennseinZustandeinZielzustand ist,
und wenden andernfalls EXPAND an, um Kindknoten zu erzeugen. Jeder Kindknoten wird
derGrenzehinzugefügt,fallservorhernichterreichtwurde,odererwirderneuthinzugefügt,
wennerjetztmiteinemPfaderreichtwird,dergeringerePfadkostenalsjedervorherigePfad
hat.DerAlgorithmusgibtentwedereineFehlermeldungodereinenKnotenzurück,dereinen
PfadzueinemZieldarstellt.DurchdieVerwendungverschiedenerf.n/-Funktionenerhalten
wirunterschiedlicheAlgorithmen,dieindiesemKapitelbehandeltwerden.
functionBEST-FIRST-SEARCH(problem,f)returnseinenKnotenalsLösungoderfailure
node NODE(STATE=problem.INITIAL)
frontier einePrioritätswarteschlange,sortiertdurchf,mitnodealseinElement
reached eineNachschlagtabelle,miteinemEintragfürdenSchlüsselproblem.INITIAL
undWertnode
whilenotIS-EMPTY(frontier)do
node POP(frontier)
ifproblem.IS-GOAL(node.STATE)thenreturnnode
foreachchildinEXPAND(problem,node)do
s child.STATE
ifsistnichtinreachedorchild.PATH-COST <reached[s].PATH-COSTthen
reached[s] child
fügechildzufrontierhinzu
returnfailure
functionEXPAND(problem,node)yieldsnodes
s node.STATE
foreachactioninproblem.ACTIONS(s)do
s’ problem.RESULT(s,action)
cost node.PATH-COST+problem.ACTION-COST(s,action,s’)
yieldNODE(STATE=s’,PARENT=node,ACTION=action,PATH-COST=cost)
Abbildung3.7:DerAlgorithmuszurBestensucheunddieFunktionzurExpansioneinesKnotens.DiehierverwendetenDaten-
strukturensindinAbschnitt3.3.2beschrieben.FüryieldsieheAnhangB.
102
--- PAGE 104 ---
3.3 Suchalgorithmen
3.3.2 Datenstrukturenfür die Suche
Suchalgorithmen benötigen eine Datenstruktur, um den Überblick über den Suchbaum zu
behalten.EinKnotenimBaumwirddurcheineDatenstrukturmitvierKomponentendarge-
stellt:
(cid:2) node.STATE:derZustand,demderKnotenentspricht;
(cid:2) node.PARENT:derKnotenimBaum,derdiesenKnotenerzeugthat;
(cid:2) node.ACTION:dieAktion,dieaufdenElternzustandangewendetwurde,umdiesenKnoten
zuerzeugen;
(cid:2) node.PATH-COST:dieGesamtkostendesPfadsvomAnfangszustand zudiesemKnoten.In
mathematischenFormelnverwendenwirg.node/alsSynonymfürPATH-COST.
Wenn wir diePARENT-Zeigervoneinem Knoten zurückverfolgen, können wir die Zustände
und Aktionen entlang des Pfads zu diesem Knoten wiederherstellen. Wenn Sie dies von
einemZielknotenaustun,erhaltenSiedieLösung.
Wir brauchen eine Datenstruktur, um die Grenze zu speichern. Die geeignete Wahl ist eine
ArtWarteschlange,denndieOperationenaufeinerGrenzesind:
(cid:2) IS-EMPTY(frontier) – gibt true (wahr) genau dann zurück, wenn es keine Knoten in der
Grenzegibt.
(cid:2) POP(frontier)–entferntdenoberstenKnotenvonderGrenzeundgibtihnzurück.
(cid:2) TOP(frontier)–gibtdenoberstenKnotenderGrenzezurück(entferntihnabernicht).
(cid:2) ADD(node,frontier)–fügtdenKnotenanseinemrichtigenPlatzinderWarteschlangeein.
InSuchalgorithmenwerdendreiArtenvonWarteschlangenverwendet:
(cid:2) In einer Prioritätswarteschlange wird die Funktion POP zuerst auf den Knoten mit den
geringsten Kosten gemäß einer Evaluierungsfunktion f angewandt. Sie wird bei der Bes-
tensucheverwendet.
(cid:2) In einer FIFO-Warteschlange oder First-In-First-Out-Warteschlange wird POP zuerst auf
denKnotenangewandt,deralsErsterindieWarteschlangeaufgenommenwurde;wirwer-
densehen,dassdiesbeiderBreitensucheverwendetwird.
(cid:2) Eine LIFO-Warteschlange oder Last-In-First-Out-Warteschlange (auch als Stapel oder
Stack bezeichnet) entfernt zuerst den zuletzt hinzugefügten Knoten; wir werden sehen,
dasssiebeiderTiefensucheeingesetztwird.
Die erreichten Zustände können als Nachschlagetabelle (z.B. als Hash-Tabelle) gespeichert
werden,wobeijederSchlüsseleinZustandundjederWertderKnotenfürdiesenZustandist.
3.3.3 Redundante Pfade
Der in IAbbildung 3.4 (unten) dargestellte Suchbaum enthält einen Pfad von Arad nach
Sibiu und wieder zurück nach Arad. Wir sagen, dass Arad ein wiederholter Zustand im
Suchbaumist,derindiesemFalldurcheinenZyklus(auchschleifenförmigerPfadgenannt)
erzeugt wird. So hat der Zustandsraum zwar nur20 Zustände, doch der vollständige Such-
baum ist unendlich, weil es keine Begrenzung gibt, wie oft man eine Schleife durchlaufen
kann.
EinZyklusisteinSpezialfalleinesredundantenPfads.ZumBeispielkönnenwirnachSibiu
überdenPfadArad–Sibiu(140Meilen)oderdenPfadArad–Zerind–Oradea–Sibiu(297Mei-
len)kommen.DieserzweitePfadistredundant–eristnureineschlechtereMöglichkeit,um
zum gleichen Zustand zu gelangen – und muss bei unserer Suche nach optimalen Pfaden
nichtberücksichtigtwerden.
103
--- PAGE 105 ---
3
ProblemlösendurchSuchen
BetrachtenSieeinenAgentenineiner10(cid:2)10-Gitterwelt,dersichaufjedesvon8benachbar-
ten Feldern bewegen kann. Wenn es keine Hindernisse gibt, kann der Agent jedes der 100
Quadratein9Zügenoderwenigererreichen.DochdieAnzahlderPfademitLänge9istfast
89 (wegen der Kanten des Gitters ein bisschen weniger) bzw. mehr als 100 Millionen. Mit
I anderen Worten, im Durchschnitt kann eine Zelle über mehr als eine Million redundanter
PfadederLänge9erreichtwerdenundwennwirredundantePfadeeliminieren,könnenwir
eineSucheetwaeineMillionMalschnellerbeenden.Wieheißtessoschön:Algorithmen,die
sich nichtandieVergangenheit erinnern können,sinddazuverdammt,siezu wiederholen.
EsgibtdreiWege,diesesProblemanzugehen.
ErstenskönnenwirunsallezuvorerreichtenZuständemerken(wiebeiderBestensuche),was
unserlaubt,alleredundantenPfadezuerkennenundnurdenbestenPfadzujedemZustand
zubehalten.DiesistfürZustandsräumegeeignet,indenenesvieleredundantePfadegibt,und
istdiebevorzugteWahl,wenndieTabelledererreichtenZuständeindenSpeicherpasst.
Zweitens: Wir müssen uns nicht darum sorgen, die Vergangenheit zu wiederholen. Es gibt
einigeProblemstellungen,beidenenesseltenoderunmöglichist,dasszweiPfadedenselben
Zustand erreichen. Ein Beispiel wäre ein Montageproblem, bei dem durch jede Aktion ein
Teil zu einer sich entwickelnden Baugrupppe hinzufügt wird und bei dem es eine Reihen-
folge der Teile gibt, sodass es möglich ist, erst A und dann B hinzuzufügen, aber nicht erst
BunddannA.BeisolchenProblemenkönnenwirSpeicherplatzsparen,wennwirerreichte
Zustände nicht verfolgen und nicht auf redundante Pfade prüfen. Wir nennen einen Such-
algorithmuseine Graphensuche, wenn erauf redundante Pfade prüft,und eine baumartige
Suche6,wennernichtprüft.DerAlgorithmusBEST-FIRST-SEARCHinIAbbildung3.7istein
AlgorithmuszurGraphensuche;wennwiralleVerweiseaufreached entfernen,erhaltenwir
einebaumartigeSuche,dieweniger Speicherbenötigt,aberredundantePfadezumgleichen
Zustanduntersuchtunddaherlangsamerläuft.
Drittens können wir einen Kompromiss eingehen und zwar auf Zyklen, aber nicht auf red-
undante Pfade im Allgemeinen prüfen. Da jeder Knoten eine Kette von Elternzeigern hat,
können wir ohne zusätzlichen Speicherbedarf auf Zyklen prüfen, indem wir die Kette der
Eltern nach oben verfolgen, um festzustellen, ob der Zustand am Ende des Pfads früher im
Pfadschoneinmalaufgetauchtist.EinigeImplementierungenfolgendieserKettedenganzen
Weg nach oben und eliminieren so alle Zyklen; andere Implementierungen folgen nur ein
paar Gliedern (z.B. zum Elternteil, zu Großeltern und Urgroßeltern) und benötigen so nur
eine konstante Zeit, um alle kurzen Zyklen zu eliminieren (und verlassen sich auf andere
Mechanismen,umlangeZyklenzubehandeln).
3.3.4 Leistungsmessungder Problemlösung
BevorwirunsmitdemEntwurfverschiedenerSuchalgorithmenbefassen,betrachtenwirdie
Kriterien,dieverwendetwerden,umeinenAlgorithmusauszuwählen.WirkönnendieLeis-
tungeinesAlgorithmusaufvierArtenbewerten:
(cid:2) Vollständigkeit:FindetderAlgorithmusgarantierteineLösung,fallseseinegibt,undgibt
erkorrekteineFehlermeldungzurück,fallseskeinegibt?
(cid:2) Kostenoptimalität:FindetereineLösungmitdengeringstenPfadkostenallerLösungen?7
6 Wirsagen „baumartigeSuche“, weil der Zustandsraum immernochderselbe Graph ist,egal wiewir ihn
durchsuchen;wirentscheidenunsnurdafür,ihnwieeinenBaumzubehandeln,alsomitnureinemPfadvon
jedemKnotenzurückzurWurzel.
7 EinigeAutorenverwendendenBegriff„Zulässigkeit“fürdieEigenschaft,diekostengünstigsteLösungzufin-
den,undeinigebenutzeneinfach„Optimalität“,dochdieskannmitanderenArtenvonOptimalitätverwechselt
werden.
104
--- PAGE 106 ---
3.4 UninformierteSuchstrategien
(cid:2) Zeitkomplexität: Wie lange dauert es, eine Lösung zu finden? Dies kann in Sekunden
gemessenwerdenoderabstrakterdurchdieAnzahlderbetrachtetenZuständeundAktio-
nen.
(cid:2) Speicherplatzkomplexität:WievielSpeicherwirdfürdieDurchführungderSuchebenö-
tigt?
UmdasKonzeptderVollständigkeitzuverstehen,betrachtenSieeinSuchproblemmiteinem
einzigenZiel.DiesesZielkönnteüberallimZustandsraumliegen;dahermusseinvollständi-
gerAlgorithmusinderLagesein,systematischjedenZustandzuerkunden,dervomAnfangs-
zustandauserreichbarist.InendlichenZustandsräumenistdaseinfachzuerreichen:Solange
wirdiePfadeverfolgenundsolcheabschneiden,dieZyklensind(z.B.vonAradnachSibiu
nachArad),werdenwirirgendwannjedenerreichbarenZustanderreichen.
InunendlichenZustandsräumenistmehrVorsichtgeboten.ZumBeispielwürdeeinAlgorith-
mus,derwiederholtdenFakultätsoperatorinKnuths„4“-Problemanwendet,einemunendli-
chenPfadvon4zu4!zu(4!)!undsoweiterfolgen.InähnlicherWeisefolgtaufeinemunendli-
chenGitterohneHindernissedaswiederholteVorwärtsbewegenineinergeradenLinieeben-
falls einem unendlichen Pfad neuer Zustände. In beiden Fällen kehrt der Algorithmus nie
zu einemZustandzurück,denerschoneinmalerreicht hat,sondernistunvollständig,weil
weiteTeiledesZustandsraumsnieerreichtwerden.
DamiteinSuchalgorithmusvollständigist,mussereinenunendlichenZustandsraumsyste-
matischerforschen undsicherstellen, dasserschließlich jedenZustanderreichen kann,der
mit dem Anfangszustand verbunden ist. Auf dem unendlichen Gitter ist eine Art der syste-
matischenSuchezumBeispieleinspiralförmigerPfad,deralleZellenabdeckt,diesSchritte
vomUrsprungentferntsind,bevorersichzuZellenbewegt,diesC1Schritteentferntsind.
Leider mussein guter Algorithmusin einem unendlichen Zustandsraum ohneLösungewig
weitersuchen; er kann nicht abbrechen, weil er nicht wissen kann, ob der nächste Zustand
einZielseinwird.
Zeit- und Speicherplatzkomplexität werden im Hinblick auf ein Maß für die Schwierigkeit
desProblemsbetrachtet. Indertheoretischen InformatikistdastypischeMaßdieGrößedes
Zustandsraumgraphen, jVjCjEj, wobei jVj die Anzahl derEcken (Zustandsknoten, vertice)
desGraphenistundjEjdieAnzahlderKanten(eindeutigeZustand-Aktion-Paare,edge).Dies
ist passend, wenn derGraph eine explizite Datenstruktur ist, wie z.B. die Karte vonRumä-
nien.Doch invielenKI-Problemenwird derGraph nurimplizitdurchden Anfangszustand,
die Aktionen und das Transitionsmodell beschrieben. Für einen impliziten Zustandsraum
kann die Komplexität mithilfe der folgenden drei Größen gemessen werden: d, die Tiefe
(depth) bzw. Anzahl derAktionen ineiner optimalenLösung; m,diemaximaleAnzahl von
AktionenineinembeliebigenPfad;undb,derVerzweigungsfaktor(branching)bzw.Anzahl
vonNachfolgerneinesKnotens,diebetrachtetwerdenmüssen.
3.4 Uninformierte Suchstrategien
Ein uninformierter Suchalgorithmus weiß nicht, wie nah ein Zustand am Ziel bzw. an den
Zielen ist.Betrachten wirzumBeispiel unserenAgenten inAradmitdemZiel,Bukarest zu
erreichen. Ein uninformierter Agent ohne Kenntnisse der rumänischen Geografie hat keine
Ahnung, obes besser ist, zuerst nach Zerind oder nach Sibiu zu fahren. Im Gegensatz dazu
weiß ein informierter Agent (Abschnitt 3.5), der die Lage der einzelnen Städte kennt, dass
SibiuvielnäheranBukarestliegtundsomitwahrscheinlichaufdemkürzestenPfadliegt.
105
--- PAGE 107 ---
3
ProblemlösendurchSuchen
3.4.1 Breitensuche
Wenn alle Aktionen die gleichen Kosten haben, ist eine geeignete Strategie die Breitensu-
che(Breadth-FirstSearch,BFS),beiderzuerstderWurzelknotenexpandiertwird,dannalle
Nachfolger desWurzelknotens, dannderen Nachfolger undsoweiter. Diesisteinesystema-
tische Suchstrategie, die daher auch auf unendlichen Zustandsräumen vollständig ist. Wir
könnten die Breitensuche als einen Aufruf von BEST-FIRST-SEARCHimplementieren, wobei
dieEvaluierungsfunktionf.n/dieTiefedesKnotensist–d.h.dieAnzahlderAktionen,die
erforderlichsind,umdenKnotenzuerreichen.
Wir können jedoch mit ein paar Tricks zusätzliche Effizienz erreichen. Eine First-In-First-
Out-Warteschlange ist schneller als eine Prioritätswarteschlange und gibt uns die korrekte
ReihenfolgederKnoten:NeueKnoten(dieimmertieferalsihreElternsind)wandernandas
EndederWarteschlangeundalteKnoten,dieflacheralsdieneuenKnotensind,werdenzuerst
expandiert. Außerdem kann reached eine Menge von Zuständen sein und keine Abbildung
von Zuständen auf Knoten, denn wenn wir einmal einen Zustand erreicht haben, können
wir niemals einen besseren Pfad zu diesem Zustand finden. Das bedeutet auch, dass wir
einenfrühenZieltestdurchführenkönnen,derprüft,obeinKnoteneineLösungist,sobalder
erzeugt wird,anstattdesspätenZieltests,dendieBestensucheverwendet,indemsiewartet,
bis ein Knoten aus der Warteschlange entfernt wird. IAbbildung 3.8 zeigt den Fortschritt
einerBreitensucheineinemBinärbaumundIAbbildung3.9zeigtdenAlgorithmusmitden
EffizienzverbesserungendurchdiefrühenZieltests.
Abbildung3.8:BreitensucheineinemeinfachenBinärbaum.InjederPhasewirddernächstezuerweiterndeKnotendurchdie
dreieckigeMarkierungangezeigt.
functionBREADTH-FIRST-SEARCH(problem)returnseinenKnotenalsLösungoderfailure
node NODE(problem.INITIAL)
ifproblem.IS-GOAL(node.STATE)thenreturnnode
frontier eineFIFO-WarteschlagnemitnodealseinemElement
reached {problem.INITIAL}
whilenotIS-EMPTY(frontier)do
node POP(frontier)
foreachchildinEXPAND(problem,node)do
s child.STATE
ifproblem.IS-GOAL(s)thenreturnchild
ifsistnichtinreachedthen
fügeszureachedhinzu
fügechildzufrontierhinzu
returnfailure
functionUNIFORM-COST-SEARCH(problem)returnseinenKnotenalsLösungoderfailure
returnBEST-FIRST-SEARCH(problem,PATH-COST)
Abbildung3.9:AlgorithmenfürBreitensucheunduniformeKostensuche.
106
--- PAGE 108 ---
3.4 UninformierteSuchstrategien
DieBreitensuchefindetimmereineLösungmiteinerminimalenAnzahlvonAktionen,denn
wennsieKnoteninderTiefederzeugt,hatsiebereitsalleKnoteninderTiefed(cid:5)1generiert–
fallseinervondieseneineLösungist,wärediesealsogefundenworden.Dasheißt,dieSuche
ist kostenoptimal für Probleme, bei denen alle Aktionen die gleichen Kosten haben, jedoch
nicht für Probleme, die diese Eigenschaft nicht haben. In beiden Fällen ist sie vollständig.
Zum Zeit- und Platzbedarf: Stellen Sie sich vor, Sie durchsuchen einen uniformen Baum,
bei dem jeder Zustand b Nachfolger hat. Die Wurzel des Suchbaums erzeugt b Knoten, von
denenjederbweitereKnotenerzeugt,insgesamtgibtesalsob2KnotenaufderzweitenEbene.
JedervondiesenerzeugtbweitereKnoten,wasb3KnotenaufderdrittenEbeneergibt,undso
weiter.Nehmenwirnunan,dieLösungbefindetsichinderTiefed.DannistdieGesamtzahl
dererzeugtenKnoten:
1CbCb2Cb3C(cid:4)(cid:4)(cid:4)CbdDO.bd/
AlleKnotenbleibenimSpeicher,sodasssowohldieZeit-alsauchdieSpeicherplatzkomple-
xität O.bd/ ist. Solche exponentiellen Schranken sind erschreckend. Als typisches Beispiel
aus der Praxis betrachten wir ein Problem mit dem Verzweigungsfaktor b D 10, einer Ver-
arbeitungsgeschwindigkeit von 1 Million Knoten/Sekunde und einem Speicherbedarf von J
1 KByte/Knoten. Eine Suche bis zur Tiefe d D 10 würde weniger als 3 Stunden dauern,
aber 10 Terabyte Speicher benötigen. Der Speicherbedarf ist fürdieBreitensuche ein größe-
resProblemalsdieAusführungszeit.DochdieZeitistimmernocheinwichtigerFaktor.Bei J
Tiefe d D 14 würde die Suche selbst bei unendlichem Speicher 3,5 Jahre dauern. Generell
gilt:SuchproblememitexponentiellerKomplexitätkönnenaußerfürdiekleinstenInstanzen
nichtdurchuninformierteSuchegelöstwerden.
3.4.2 Der Algorithmus vonDijkstra oder uniforme Kostensuche
Wenn Aktionenunterschiedliche Kosten haben, ist esnaheliegend, dieBestensuche zu ver-
wenden,beiderdieEvaluierungsfunktiondieKostendesPfadsvonderWurzelzumaktuel-
lenKnotenist.DieseSuchewirdindertheoretischenInformatikalsAlgorithmusvonDijkstra
bezeichnet, inder KI-Gemeinschaft als uniformeKostensuche. Während sich dieBreitensu-
cheinWellenmiteinheitlicherTiefeausbreitet–zuerstTiefe1,dannTiefe2undsoweiter–,
ist hierderGrundgedanke, dasssich dieuniformeKostensuche inWellenmiteinheitlichen
Pfadkosten ausbreitet. Der Algorithmus kann als Aufruf von BEST-FIRST-SEARCHmit PATH-
COSTalsEvaluierungsfunktionimplementiertwerden,wieinIAbbildung3.9gezeigt.
Betrachten Sie IAbbildung 3.10, wo das Problem darin besteht, von Sibiu nach Bukarest
zu kommen.DieNachfolger vonSibiusindRimnicuVilcea undFagaras, mitKosten von80
Sibiu Fagaras
99
80
Rimnicu Vilcea
211
Pitesti
97
101
Bukarest
Abbildung3.10:AusgewählterTeildesRumänien-Zustandsraums,umdieuniformeKostensuchezuveranschaulichen.
107
--- PAGE 109 ---
3
ProblemlösendurchSuchen
bzw. 99. Der Knoten mit den geringsten Kosten, Rimnicu Vilcea, wird als Nächstes expan-
diert, indem Pitesti mit Kosten von 80C97D177 hinzugefügt wird. Der Knoten mit den
geringsten Kosten ist nun Fagaras, also wird dieser expandiert und Bukarest wird mit Kos-
ten99C211D310hinzugefügt.Bukarest istdasZiel,dochderAlgorithmustestet nurbeim
Expandieren eines Knotens auf Ziele, nicht beim Erzeugen eines Knotens, also hat er noch
nichterkannt,dassdieseinPfadzumZielist.
DerAlgorithmusarbeitetweiter,wähltalsNächstesPitestifürdieExpansionundfügteinen
zweitenPfadnachBukarestmitdenKosten80C97C101D278hinzu.DieserPfadhatnied-
rigereKosten,alsoersetzt erdenvorhergehenden Pfadinreached undwirdzufrontier hin-
zugefügt. Esstelltsichheraus, dassdieserKnotennundieniedrigsten Kostenhat,alsowird
eralsNächsterbetrachtet,alsZielerkanntundzurückgegeben. BeachtenSie,dasswireinen
Pfad mit höheren Kosten (den durch Fagaras) zurückgegeben hätten, wenn wir beim Erzeu-
geneinesKnotensundnichtbeimExpandierendesKnotensmitdenniedrigsten Kostenauf
ErreichendesZielsgeprüfthätten.
Die Komplexität der uniformen Kostensuche wird charakterisiert durch C(cid:3), die Kosten der
optimalenLösung,8 und(cid:3),einerunteren SchrankefürdieKostenjederAktion,wobei(cid:3) > 0
ist. Die Zeit- und Speicherplatzkomplexität des Algorithmus beträgt also im schlechtesten
Fall O.b1CbC(cid:3)=(cid:2)c/, was viel größer als bd sein kann. Das liegt daran, dass die uniformeKos-
tensuchegroßeBäumevonAktionenmitniedrigenKostenuntersuchenkann,bevorsiePfade
erkundet,dieeineteureundvielleichtnützlicheAktionbeinhalten.SindalleAktionskosten
gleich,dannistb1CbC(cid:3)=(cid:2)c einfachbdC1 unddieuniformeKostensucheistmitderBreitensu-
chevergleichbar.
Die uniforme Kostensuche ist vollständig und kostenoptimal, weil die erste gefundene
LösungKostenhat,diemindestenssoniedrigsindwiedieKostenjedesanderenKnotensder
Grenze. Die Suche betrachtet alle Pfade systematisch in der Reihenfolge steigender Kosten
und läuft nie Gefahr, sich auf einen einzelnen unendlichen Pfad zu begeben (unter der
Annahme,dassalleAktionskosten>(cid:3)>0sind).
3.4.3 Tiefensucheund das Speicherproblem
Bei der Tiefensuche (Depth-First Search, DFS) wird immer zuerst der tiefste Knoten der
Grenze expandiert. Sie könnte als Aufruf von BEST-FIRST-SEARCH implementiert werden,
wobei die Evaluierungsfunktion f das Negativ der Tiefe ist. Sie wird jedoch normalerweise
nicht als Graphensuche, sondern als baumartige Suche implementiert, bei der keine Tabel-
len der erreichten Zustände geführt werden. Der Verlauf der Suche ist in IAbbildung 3.11
dargestellt;dieSuchegehtsofortbiszurtiefstenEbenedesSuchbaums,wodieKnotenkeine
Nachfolger haben. Die Suche geht dann „rückwärts“ zum nächsttieferen Knoten, der noch
nicht expandierteNachfolger hat. DieTiefensuche ist nichtkostenoptimal;sie gibt dieerste
gefundeneLösungzurück,auchwenndiesenichtdiegünstigsteist.
FürendlicheZustandsräumeinFormvonBäumenistdieTiefensucheeffizientundvollstän-
dig; für azyklische Zustandsräume kann es passieren, dass am Ende derselbe Zustand viele
MaleüberverschiedenePfadeexpandiertwurde,aber(irgendwann)wirddergesamteRaum
systematischerkundetsein.
InzyklischenZustandsräumenkannsichdieSucheineinerEndlosschleifeverfangen;daher
überprüfen einige Implementierungen der Tiefensuche jeden neuen Knoten auf Zyklen. In
unendlichen Zustandsräumen schließlich ist die Tiefensuche nicht systematisch: Sie kann
auf einem unendlichen Pfad hängenbleiben, auch wenn es keine Zyklen gibt. Daher ist die
Tiefensucheunvollständig.
8 HierundimgesamtenBuchbedeutetdasSterncheninC(cid:3)einenoptimalenWertfürC.
108
--- PAGE 110 ---
3.4 UninformierteSuchstrategien
Abbildung3.11:DutzendSchritte(vonlinksnachrechts,vonobennachunten)imVerlaufeinerTiefensucheineinemBinär-
baumvomStartzustandAzuZielM.DieGrenzeistgrüngezeichnet,wobeieinedreieckigeMarkierungdenalsNächstenzu
expandierendenKnotenkennzeichnet.ZuvorexpandierteKnotensindlavendelfarbenundpotenziellezukünftigeKnotenhaben
schwachgestrichelteLinien.ExpandierteKnoten,diekeineNachkommenimGrenzbereichhaben(sehrschwacheLinien),kön-
nenverworfenwerden.
Warum sollte jemand bei all diesen schlechten Nachrichten eine Tiefensuche anstelle einer
BreitensucheoderBestensucheinBetrachtziehen?DieAntwortist,dassbeiProblemen,bei
deneneinebaumartigeSuchemöglichist,dieTiefensucheeinenvielgeringerenSpeicherbe-
darfhat.Wirführenerstgarkeinereached-TabelleunddieGrenzeistsehrklein:StellenSie
sichdieGrenzebeiderBreitensuchealsdieOberflächeeinerimmergrößerwerdendenKugel
vor,währenddieGrenzebeiderTiefensuchenurderRadiusderKugelist.
Für einen endlichen baumförmigen Zustandsraum wie in IAbbildung 3.11 benötigt eine
baumartige Tiefensuche Zeit, die proportional zur Anzahl der Zustände ist, und hat eine
Speicherplatzkomplexität vonnurO.bm/,wobeibderVerzweigungsfaktor undmdiemaxi-
male Tiefe des Baums ist. Einige Probleme, die bei der Breitensuche Exabytes an Speicher
benötigenwürden,kommenbeiderTiefensuchemitnureinigenKilobytesaus.Aufgrundder
sparsamen Speichernutzung wurde die baumartige Tiefensuche als grundlegendes Arbeits-
pferdfürvieleKI-Bereicheübernommen,einschließlichderConstraint-Satisfaction-Probleme
(Kapitel6),deaussagenlogischenErfüllbarkeit(Kapitel7)undderlogischenProgrammierung
(Kapitel9).
109
--- PAGE 111 ---
3
ProblemlösendurchSuchen
Eine Variante der Tiefensuche, die Backtracking-Suche, benötigt sogar noch weniger Spei-
cher (siehe Kapitel 6 für weitere Details). Beim Backtracking wird jeweils nur ein Nachfol-
ger erzeugt und nicht alle Nachfolger; jeder partiell expandierte Knoten merkt sich, wel-
cher Nachfolger als Nächstes erzeugt werden soll. Außerdem werden Nachfolger generiert,
indem die aktuelle Zustandsbeschreibung direkt geändert wird, anstatt Speicher für einen
ganz neuen Zustand vorzuhalten. Diesreduziert den Speicherbedarf auf nureineZustands-
beschreibung und einen Pfad von O.m/ Aktionen – eine signifikante Einsparung gegenüber
O.bm/ Zuständen bei der Tiefensuche. Mit Backtracking haben wir auch die Möglichkeit,
eine effiziente Mengendatenstruktur fürdie Zustände auf dem aktuellen Pfad zu verwalten,
wodurchwirinO.1/ZeitstattO.m/nacheinemzyklischenPfadsuchenkönnen.DamitBack-
trackingfunktioniert,müssenwirinderLagesein,jedeAktionrückgängigzumachen,wenn
wir zurückgehen. Backtracking ist entscheidend für den Erfolg vieler Probleme mit großen
Zustandsbeschreibungen,wiez.B.beiderRobotermontage.
3.4.4 Beschränkteunditerative Tiefensuche
Um zu verhindern, dass sich die Tiefensuche auf einem unendlichen Pfad verliert, können
wirdiebeschränkteTiefensuche(Depth-LimitedSearch)verwenden,eineVersionderTiefen-
suche,beiderwireineTiefengrenze`angebenundalleKnotenderTiefe`sobehandeln,als
hättensiekeineNachfolger(IAbbildung3.12).DieZeitkomplexitätistO.b`/unddieSpei-
cherplatzkomplexitätistO.b`/.TreffenwireineschlechteWahlfür`,sowirdderAlgorithmus
dieLösungleidernichterreichen,wodurcherwiederunvollständigwird.
DadieTiefensucheeinebaumartigeSucheist,könnenwirnichtverhindern,dasssiegenerell
Zeit auf redundanten Pfaden verschwendet, doch wir könnenZyklen auf Kosten von etwas
Rechenzeiteliminieren.WennwirnureinpaarGliederinderElternkettenachobenschauen,
könnenwirdiemeistenZyklenabfangen;längereZyklenwerdendurchdieTiefenbegrenzung
behandelt.
functionITERATIVE-DEEPENING-SEARCH(problem)returnseineLösungoderfailure
fordepth=0to1do
result DEPTH-LIMITED-SEARCH(problem,depth)
ifresult¤cutoff thenreturnresult
functionDEPTH-LIMITED-SEARCH(problem,`)returnseinKnotenoderfailureodercutoff
frontier eineLIFO-Warteschlange(Stapel)mitNODE(problem.INITIAL)alseinElement
result failure
whilenotIS-EMPTY(frontier)do
node POP(frontier)
ifproblem.IS-GOAL(node.STATE)thenreturnnode
ifDEPTH(node)> `then
result cutoff
elseifnotIS-CYCLE(node)do
foreachchildinEXPAND(problem,node)do
fügechildzufrontierhinzu
returnresult
Abbildung3.12:DieiterativeTiefensuchewendetwiederholteinebeschränkteTiefensuchemitwachsendenGrenzenan.Sie
gibteinenvondreiunterschiedlichenWerttypenzurück:entwedereinenLösungsknoten;oderfailure,wennalleKnotenerschöp-
fenduntersuchtsindundesnachweislichinkeinerTiefeeineLösunggibt;odercutoff,washeißt,dasseseineLösungineiner
Tiefeunterhalbvon`gebenkönnte.DiesisteinbaumartigerSuchalgorithmus,derdieerreichtenZustände(reached)nichtauf-
zeichnetunddahervielwenigerSpeicheralsdieBestensuchebenötigt,aberdasRisikoeingeht,denselbenZustandmehrmalsauf
verschiedenenPfadenzubesuchen.UndwennderIS-CYCLE-TestnichtalleZyklenprüft,kannderAlgorithmusineinerSchleife
hängenbleiben.
110
--- PAGE 112 ---
3.4 UninformierteSuchstrategien
Manchmal kann eine gute Tiefengrenze aus der Kenntnis des Problems abgeleitet werden.
Zum Beispiel gibt es auf der Karte von Rumänien 20 Städte. Daher ist `D19 eine gültige
Grenze. Doch wenn wir die Karte sorgfältig studierten, würden wir herausfinden, dass jede
Stadt von jeder anderen Stadt mit höchstens 9 Aktionen erreicht werden kann. Diese Zahl,
bekanntalsderDurchmesserdesZustandsraumgraphen,gibtunseinebessereTiefengrenze,
diezueinereffizienteren beschränktenTiefensucheführt.FürdiemeistenProblemekennen
wirjedochersteineguteTiefengrenze,wennwirdasProblemgelösthaben.
Die iterative Tiefensuche (Iterative Deepening Search, IDS) löst das Problem der Auswahl
eines guten Werts für`, indem siealle Werte ausprobiert –zuerst 0, dann1, dann 2usw. –,
bisentwedereineLösunggefundenwirdoderdiebeschränkteTiefensuchedenfailure-Wert
anstelledescutoff-Wertsliefert.DerAlgorithmusistinIAbbildung3.12dargestellt.Dieite-
rative Tiefensuche kombiniert viele der Vorteile von Tiefen- und Breitensuche. Wie bei der
Tiefensuche sind die Speicheranforderungen bescheiden: O.bd/, falls es eine Lösung gibt,
oderO.bm/aufendlichenZustandsräumenohneLösung.WiedieBreitensucheistdieitera-
tive Tiefensuche optimal für Probleme, bei denen alle Aktionen die gleichen Kosten haben,
und ist vollständig auf endlichen azyklischen Zustandsräumen oder auf jedem endlichen
Zustandsraum,wennwirdieKnotenaufdemgesamtenPfadnachobenaufZyklenüberprü-
fen.
DieZeitkomplexitätistO.bd/,fallseseineLösunggibt,oderO.bm/,wenneskeinegibt.Jede
Iteration der iterativen Tiefensuche erzeugt ebenso wie die Breitensuche eine neue Ebene,
doch bei der Breitensuche werden dazu alle Knoten im Speicher gespeichert, während bei
deriterativenTiefensuchedievorherigenEbenenwiederholtwerden,wodurchSpeicherauf
Kosten von mehr Zeit gespart wird. IAbbildung 3.13 zeigt vier Iterationen der iterativen
TiefensucheineinembinärenSuchbaum,wobeidieLösungbeiderviertenIterationgefunden
wird.
Die iterative Tiefensuche mag verschwenderisch erscheinen, weil Zustände oben im Such-
baummehrfachneuerzeugtwerden.DochbeivielenZustandsräumenbefindensichdiemeis-
ten Knoten in der untersten Ebene, sodass es nicht viel ausmacht, dass die oberen Ebenen
wiederholt werden. Bei einer iterativen Tiefensuche werden die Knoten auf der untersten
Ebene(Tiefed)einmalerzeugt,dieaufderzweitenEbenevonuntenzweimalundsoweiter,
biszudenKindernderWurzel, died-malerzeugt werden.Imschlechtesten Fallistalsodie
GesamtzahldererzeugtenKnoten
N.IDS/D.d/b1C.d(cid:5)1/b2C.d(cid:5)2/b3C(cid:4)(cid:4)(cid:4)Cbd;
was eine Zeitkomplexität von O.bd/ ergibt – asymptotisch die gleiche Komplexität wie bei
derBreitensuche.WennzumBeispielbD10unddD5ist,dannerhaltenwir:
N.IDS/D50C400C3:000C20:000C100:000D123:450
N.BFS/D10C100C1:000C10:000C100:000D111:110
FallsSiesichwirklichwegenderWiederholungSorgenmachen,könnenSieeinenhybriden
Ansatzverwenden, dereineBreitensuche ausführt,bisfastdergesamteverfügbareSpeicher J
aufgebraucht ist, und dann mit einer iterativen Tiefensuche von allen Knoten der Grenze
ausweiterarbeitet.ImAllgemeinenistdieiterativeTiefensuchediebevorzugteuninformierte
Suchmethode,wennderSuchzustandsraumzugroßist,umindenSpeicherzupassen,und
dieTiefederLösungnichtbekanntist.
111
--- PAGE 113 ---
3
ProblemlösendurchSuchen
Tiefe: 0
Tiefe: 1
Tiefe: 2
Tiefe: 3
Abbildung3.13:VierIterationenderiterativenTiefensuchefürdasZielMineinembinärenBaum,wobeidieTiefengrenzevon0bis3variiert.
BeachtenSie,dassdieinnerenKnoteneineneinzelnenPfadbilden.DasDreieckmarkiertdenalsNächsteszuexpandierendenKnoten;grüne
KnotenmitdunklenUmrissengehörenzurGrenze;diesehrdünngezeichnetenKnotenkönnennachweislichnichtTeileinerLösungmitdieser
Tiefengrenzesein.
3.4.5 Bidirektionale Suche
Die Algorithmen, die wir bisher behandelt haben, beginnen bei einem Anfangszustand und
könneneinenvonmehrerenmöglichenZielzuständenerreichen.EinalternativerAnsatz,der
als bidirektionale Suche bezeichnet wird, sucht gleichzeitig vom Anfangszustand aus vor-
wärts und vom Zielzustand bzw. den Zielzuständen aus rückwärts – in der Hoffnung, dass
sich die beiden Suchprozesse treffen. Die Grundidee dabei ist, dass bd=2Cbd=2 viel kleiner
istalsbd (z.B.50.000-malkleiner,wennbDdD10).
Damitdiesfunktioniert,müssenwirzweiGrenzenundzweiTabellenmiterreichtenZustän-
den im Auge behalten und wir müssen in der Lage sein, rückwärts zu denken: Wenn der
Zustands0 einNachfolgervonsinVorwärtsrichtungist,dannmüssenwirwissen,dasssein
112
--- PAGE 114 ---
3.4 UninformierteSuchstrategien
Nachfolgervons0inRückwärtsrichtungist.WirhabeneineLösung,wenndiebeidenGrenzen
aufeinanderstoßen.9
Es gibt viele verschiedene Versionen der bidirektionalen Suche, so wie es auch viele ver-
schiedene unidirektionale Suchalgorithmen gibt. In diesem Abschnitt beschreiben wir die
bidirektionale Bestensuche. Obwohl es zwei getrennte Grenzen gibt, ist der als Nächstes zu
expandierendeKnotenimmereinKnotenmiteinemminimalenWertderAuswertungsfunk-
tion,wasfürbeideGrenzengilt.WenndieEvaluierungsfunktiondiePfadkostensind,erhalten
wireinebidirektionaleuniformeKostensuche,undwenndieKostendesoptimalenPfadsC(cid:3)
sind, so wird kein Knoten mit Kosten > C(cid:3) expandiert. Dies kann zu einer beträchtlichen
2
Beschleunigungführen.
Derallgemeine bidirektionaleAlgorithmusfürdieBestensuche istinIAbbildung3.14dar-
gestellt. WirübergebendemAlgorithmusjeweilszweiVersionendesProblemsundderEva-
luierungsfunktion, eine in Vorwärtsrichtung (tiefgestelltes F fürforward)und eine in Rück-
wärtsrichtung(tiefgestelltesBfürbackward).WenndieEvaluierungsfunktiondiePfadkosten
sind, so wissen wir, dass dieerste gefundene LösungeineoptimaleLösungsein wird, doch
9 InunsererImplementierungunterstütztdiereached-DatenstruktureineAbfrage,obeingegebenerZustand
einElementist,unddiefrontier-Datenstruktur(einePrioritätswarteschlange)tutdiesnicht,sodasswirmithilfe
von reached auf eine Kollision prüfen, allerdings fragen wir konzeptionell ab, ob sich die beiden Grenzen
getroffenhaben.DieImplementierungkannerweitertwerden,ummehrereZielzuständezubehandeln,indem
derKnotenfürjedenZielzustandindieTabellenreachedBundfrontierBgeladenwird.
functionBIBF-SEARCH(problemF,fF,problemB,fB)returnseineLösungoder failure
nodeF NODE(problemF.INITIAL) //KnotenfüreinenStartzustand
nodeB NODE(problemB.INITIAL) //KnotenfüreinenZielzustand
frontierF einePrioritätswarteschlange,sortiertdurchfF,mitnodeF alseinemElement
frontierB einePrioritätswarteschlange,sortiertdurchfB,mitnodeBalseinemElement
reachedF eineNachschlagetabellemiteinemSchlüsselnodeF.STATEundWertnodeF
reachedB eineNachschlagetabellemiteinemSchlüsselnodeB.STATEundWertnodeB
solution failure
whilenotTERMINATED(solution,frontierF,frontierB)do
iffF(TOP(frontierF))<fB(TOP(frontierB))then
solution PROCEED(F,problemF,frontierF,reachedF,reachedB,solution)
elsesolution PROCEED(B,problemB,frontierB,reachedB,reachedF,solution)
returnsolution
functionPROCEED(dir,problem,frontier,reached,reached2,solution)returnseineLösung
//ExpandiertKnotenanderGrenze;vergleichtmitandererGrenzeinreached2.
//DieVariable„dir“istdieRichtung:entwederFfür„forward“oderBfür„backward“.
node POP(frontier)
foreachchildinEXPAND(problem,node)do
s child.STATE
ifsistnichtinreachedorPATH-COST(child)<PATH-COST(reached[s])then
reached[s] child
fügechildzufrontierhinzu
ifsistinreached2then
solution2 JOIN-NODES(dir,child,reached2[s])
ifPATH-COST(solution2)<PATH-COST(solution)then
solution solution2
returnsolution
Abbildung3.14:DiebidirektionaleBestensucheverwaltetzweiGrenzenundzweiTabellenmiterreichtenZuständen.Wenn
einPfadineinerGrenzeeinenZustanderreicht,derauchinderanderenHälftederSucheerreichtwurde,werdendiebeiden
Pfadevereiningt(durchdieFunktionJOIN-NODES)undbildensoeineLösung.Esistnichtgarantiert,dassdieersteerhaltene
Lösungdiebesteist–dieFunktionTERMINATEDbestimmt,wanndieSuchenachneuenLösungenbeendetwird.
113
--- PAGE 115 ---
3
ProblemlösendurchSuchen
beianderenEvaluierungsfunktionenistdasnichtunbedingtderFall.Daherspeichernwirdie
bestebishergefundeneLösungundmüssendieseeventuellmehrmalsaktualisieren,bevorder
TERMINATED-Testbeweist,dasskeinebessereLösungmehrmöglichist.
3.4.6 Vergleichvon uninformiertenSuchalgorithmen
IAbbildung 3.15 vergleicht uninformierte Suchalgorithmen in Bezug auf die vier in
Abschnitt3.3.4genanntenBewertungskriterien.DieserVergleichbeziehtsichaufbaumartige
Suchversionen, die nicht auf wiederholte Zustände prüfen. Bei Graphen-Suchalgorithmen,
die diese Prüfung vornehmen, bestehen die Hauptunterschiede darin, dass die Tiefensuche
für endliche Zustandsräume vollständig ist und die Platz- und Zeitkomplexität durch die
GrößedesZustandsraums(dieAnzahlderEckpunkteundKanten,jVjCjEj)begrenztist.
Kriterium Breitensuche Uniforme Tiefensuche Beschränkte Iterative Bidirektional
Kostensuche Tiefensuche Tiefensuche (fallsmöglich)
Vollständig? Ja1 Ja1;2 Nein Nein Ja1 Ja1;4
OptimaleKosten? Ja3 Ja Nein Nein Ja3 Ja3;4
Zeit O.bd/ O.b1CbC(cid:3)=(cid:2)c/ O.bm/ O.b`/ O.bd/ O.bd=2/
Platz O.bd/ O.b1CbC(cid:3)=(cid:2)c/ O.bm/ O.b`/ O.bd/ O.bd=2/
Abbildung3.15:BewertungvonSuchalgorithmen.bistderVerzweigungsfaktor;mistdiemaximaleTiefedesSuchbaums;
distdieTiefederflachstenLösungbzw.m,wenneskeineLösunggibt;`istdieTiefenbegrenzung.DiehochgestelltenSymbole
bedeuten:1–vollständig,fallsbendlichistundderZustandsraumentwedereineLösunghatoderendlichist;2–vollständig,
wennalleAktionskosten(cid:3)(cid:3)>0sind;3–kostenoptimal,fallsalleAktionskostenidentischsind;4–wennbeideRichtungen
BreitensuchenoderuniformeKostensuchensind.
3.5 Informierte (heuristische) Suchstrategien
In diesem Abschnitt wird gezeigt, wie eine informierte Suchstrategie – die Wissen aus der
Problemdomänenutzt,umetwasüberdieLagevonZielenzuerfahren–Lösungeneffizienter
finden kann als eine uninformierte Strategie. Das Wissen wird in Form einer heuristischen
Funktionh.n/zurVerfügunggestellt:10
h.n/D geschätzteKostendesgünstigstenPfadsvomZustandamKnotenn
zueinemZielzustand
BeiWegfindungsproblemenkönnenwirz.B.dieEntfernungvomaktuellenZustandzueinem
Zielschätzen,indemwirdieLuftlinieaufderKartezwischendenbeidenPunktenberechnen.
WiruntersuchenHeuristikenundderenHerkunftinAbschnitt3.6genauer.
3.5.1 Gierige Bestensuche
Die gierige Bestensuche (Greedy Best-First Search) ist eine Form der Bestensuche, bei der
zuerstderKnotenmitdemniedrigstenh.n/-Wertexpandiertwird–derKnoten,derdemZiel
10Es magseltsamerscheinen, dass dieheuristischeFunktioneinen Knotenals Eingabe verarbeitet, obwohl
sieeigentlichnurdenZustanddesKnotensbenötigt.Esistüblich,h.n/statth.s/zuverwenden,ummitder
Evaluierungsfunktionf.n/unddenPfadkosteng.n/konsistentzubleiben.
114
--- PAGE 116 ---
3.5 Informierte(heuristische)Suchstrategien
Arad 366 Mehadia 241
Bukarest 0 Neamt 234
Craiova 160 Oradea 380
Drobeta 242 Pitesti 100
Eforie 161 Rimnicu Vilcea 193
Fagaras 176 Sibiu 253
Giurgiu 77 Timisoara 329
Hirsova 151 Urziceni 80
Iasi 226 Vaslui 199
Lugoj 244 Zerind 374
Abbildung3.16:Wertefürh –LuftliniennachBukarest.
LL
am nächsten zu sein scheint – mit dem Hintergrund, dass dies wahrscheinlich schnell zu
einerLösungführt.DieEvaluierungsfunktionistdamitf.n/Dh.n/.
Schauen wir unsan, wie dies fürWegfindungsprobleme in Rumänien funktioniert; wir ver-
wendendieHeuristikderLuftlinie,diewirh nennenwerden.WenndasZielBukarestist,
LL
benötigenwirdieLuftliniennachBukarest,dieinIAbbildung3.16angegebenwerden.Zum
Beispiel: h .Arad/D366. Beachten Sie, dass die Werte von h nicht aus der Problembe-
LL LL
schreibungselbstberechnetwerdenkönnen(d.h.ausdenFunktionenACTIONSundRESULT).
AußerdembenötigtmaneingewissesMaßanWeltwissen,umzuwissen,dassh mittatsäch-
LL
lichenStraßenentfernungenkorreliertunddahereinesinnvolleHeuristikist.
IAbbildung3.17zeigtdenFortschritteinergierigenBestensuchemith ,umeinenPfadvon
LL
AradnachBukarestzufinden.DerersteKnoten,dervonAradausexpandiertwird,istSibiu,
dadieHeuristikbesagt,dassesnäheranBukarestliegtalsZerindoderTimisoara.Dernächste
Knoten,derexpandiertwird,istFagaras,weildieserOrtjetztlautHeuristikamnächstenliegt.
FagaraswiederumerzeugtBukarest,welchesdasZielist.FürdiesesspezielleProblemfindet
diegierigeBestensuchemith eineLösung,ohnejeeinenKnotenzuexpandieren,dernicht
LL
auf dem Lösungspfad liegt. Die gefundene Lösung ist jedoch nicht kostenoptimal: Der Weg
überSibiuundFagaras nachBukarestist32MeilenlängeralsderWegüberRimnicuVilcea
und Pitesti. Aus diesem Grund wird der Algorithmus „gierig“ genannt – bei jeder Iteration
versuchter,demZielsonahewiemöglichzukommen,aberGierkannzuschlechterenErgeb-
nissenführenalsvorsichtigesVorgehen.
DiegierigeBestensucheinGraphenistinendlichenZustandsräumenvollständig,abernicht
inunendlichen.DieZeit-undSpeicherplatzkomplexitätistimschlechtestenFallO.jVj/.Mit
einergutenheuristischenFunktionkanndieKomplexitätjedocherheblichreduziertwerden
underreichtbeibestimmtenProblemenO.bm/.
(cid:2)
3.5.2 A -Suche
Der gebräuchlichste informierte Suchalgorithmus ist die A(cid:3)-Suche (gesprochen „A-Stern“),
eineBestensuche,diedieEvaluierungsfunktion
f.n/Dg.n/Ch.n/
verwendet, wobei g.n/ die Pfadkosten vom Anfangszustand zum Knoten n und h.n/ die
geschätztenKostendeskürzestenPfadsvonnzueinemZielzustandsind,wirhabenalso:
f.n/D geschätzteKostendesbestenPfads,dervonnzueinemZielführt
InIAbbildung3.18zeigen wirdenVerlaufeinerA(cid:3)-SuchemitdemZiel,Bukarestzuerrei-
chen.DieWertevongwerdenausdenAktionskosteninIAbbildung3.1berechnet,unddie
115
--- PAGE 117 ---
3
ProblemlösendurchSuchen
(a) Anfangszustand
(b) Nach Expansion von Arad
(c) Nach Expansion von Sibiu
(d) Nach Expansion von Fagaras
Bukarest
Abbildung3.17:PhaseneinergierigenbaumartigenBestensuchenachBukarestmitderLuftlinienheuristikh .DieKnotensind
LL
mitihrenh-Wertenbeschriftet.
Wertevonh sindinIAbbildung3.16angegeben. Beachten Sie,dassBukarest zumersten
LL
Mal im Schritt (e) auf der Grenze erscheint, aber nicht für die Expansion ausgewählt (und
somit nicht als Lösung erkannt) wird, weil Bukarest mit fD450 nicht der Knoten mit den
niedrigstenKostenaufderGrenzeist–daswärePitestimitfD417.Mankönnteauchsagen,
dass es eine Lösungüber Pitesti geben könnte, dessen Pfadkosten nurbei 417liegen, daher
wirdsichderAlgorithmusnichtmiteinerLösungvon450zufrieden geben.InSchritt(f)ist
nuneinandererPfadnachBukarestderKnotenmitdenniedrigstenKostenmitfD418,also
wirddieserausgewähltundalsoptimaleLösungerkannt.
DieA(cid:3)-Sucheistvollständig.11ObA(cid:3)kostenoptimalist,hängtvonbestimmtenEigenschaften
derHeuristik ab. EineSchlüsseleigenschaft ist dieZulässigkeit: einezulässige Heuristik ist
eineHeuristik,diedieKostenzumErreicheneinesZielsniemalsüberschätzt.(Einezulässige
Heuristikistalsooptimistisch.)MiteinerzulässigenHeuristikistA(cid:3) kostenoptimal,waswir
11WiederumunterderAnnahme,dassalleAktionskosten>(cid:3)>0sindundderZustandsraumentwedereine
Lösunghatoderendlichist.
116
--- PAGE 118 ---
3.5 Informierte(heuristische)Suchstrategien
(a) Anfangszustand
(b) Nach Expansion von Arad
(c) Nach Expansion von Sibiu
(d) Nach Expansion von Rimnicu Vilcea
(e) Nach Expansion von Fagaras
Bukarest
(f) Nach Expansion von Pitesti
Bukarest
Bukarest
Abbildung3.18:PhaseneinerA(cid:3)-SuchenachBukarest.KnotensindmitfDgChbeschriftet.Dieh-WertesinddieLuftlinien
nachBukarestausAbbildung3.16.
117
--- PAGE 119 ---
3
ProblemlösendurchSuchen
miteinemWiderspruchsbeweiszeigenkönnen.Angenommen,deroptimalePfadhatKosten
C(cid:3), doch der Algorithmus gibt einen Pfad mit Kosten C > C(cid:3) zurück. Dann muss es einen
Knotenngeben,deraufdemoptimalenPfadliegtunddernichtexpandiertist(dennfallsalle
Knoten auf dem optimalen Pfad expandiert wären, dann hätten wir bereits diese optimale
Lösung zurückgegeben). Wenn wir also die Notation g(cid:3).n/ verwenden, um die Kosten des
optimalen Pfads vom Start bis n zu bezeichnen, und h(cid:3).n/, um die Kosten des optimalen
PfadsvonnzumnächstgelegenenZielzubezeichnen,dannerhaltenwir:
f.n/>C(cid:3) (andernfallswärenexpandiert)
f.n/Dg.n/Ch.n/ (perDefinition)
f.n/Dg(cid:3).n/Ch.n/ (weilnaufeinemoptimalenPfadist)
f.n/(cid:6)g(cid:3).n/Ch(cid:3).n/ (aufgrundderZulässigkeit,h.n/(cid:6)h(cid:3).n/)
f.n/(cid:6)C(cid:3) (nachDefinition,C(cid:3)Dg(cid:3).n/Ch(cid:3).n/)
Die erste und die letzte Zeile bilden einen Widerspruch, also muss die Annahme, dass der
Algorithmus einen suboptimalen Pfad zurückgeben könnte, falsch sein – A(cid:3) gibt daher nur
kostenoptimalePfadezurück.
Eineetwasstärkere Eigenschaft wirdalsKonsistenzbezeichnet. EineHeuristik h.n/istkon-
sistent, falls für jeden Knoten n und jeden Nachfolger n0 von n, der durch eine Aktion a
erzeugtwird,gilt:
h.n/(cid:6)c.n;a;n0/Ch.n0/
DiesisteineFormderDreiecksungleichung, diebesagt, dasseineSeiteeinesDreiecksnicht
längerseinkannalsdieSummederbeidenanderenSeiten(IAbbildung3.19).EinBeispiel
für eine konsistente Heuristik ist die Luftlinie h , die wir bei der Reise nach Bukarest ver-
LL
wendethaben.
n′
c(n, a, n′) h(n′)
n G
n′
h(n)
Abbildung3.19:Dreiecksungleichung:IstdieHeuristikhkonsistent,dannistdereinzelneWerth.n/kleineralsdieSumme
derKostenc.n;a;a0/derAktionvonnbisn0plusderheuristischenSchätzungh.n0/.
JedekonsistenteHeuristikistzulässig(abernichtumgekehrt),alsoistA(cid:3)miteinerkonsisten-
tenHeuristikkostenoptimal.AußerdemgiltbeieinerkonsistentenHeuristikFolgendes:Wenn
wireinenZustanddasersteMalerreichen,befindetersichaufeinemoptimalenPfad,sodass
wirnieeinenZustandneuzurGrenzehinzufügenmüssenundnieeinenEintraginreached
ändernmüssen.DochmiteinerinkonsistentenHeuristikkönntenwiramEndemehrerePfade
haben,diedenselbenZustanderreichen,undwennjederneuePfadniedrigerePfadkostenhat
alsdervorherige, dannhaben wiram Endemehrere Knotenfürdiesen Zustand derGrenze,
was uns sowohl Zeit als auch Platz kostet. Aus diesem Grund achten einige Implementie-
rungen vonA(cid:3) darauf, einen Zustand nureinmal in dieGrenze aufzunehmen, und falls ein
bessererPfadzumZustandgefundenwird,werdenalleNachfolgerdesZustandsaktualisiert
(was erfordert, dass Knoten sowohl Kind-Zeiger als auch Eltern-Zeiger haben). Diese Kom-
plikationenhabendazugeführt,dassvieleImplementiererinkonsistenteHeuristikenvermei-
den, doch Felner et al. (2011) führen an, dass die schlimmsten Effekte in der Praxis selten
auftretenundmankeineAngstvorinkonsistentenHeuristikenhabensollte.
118
--- PAGE 120 ---
3.5 Informierte(heuristische)Suchstrategien
MiteinerunzulässigenHeuristikkannA(cid:3)kostenoptimalseinoderauchnicht.Hiersindzwei
Fälle, in denen A(cid:3) optimal ist: Erstens, wenn es auch nur einen kostenoptimalen Pfad gibt,
auf dem h.n/ für alle Knoten n auf dem Pfad zulässig ist, dann wird dieser Pfad gefunden,
unabhängig davon, wie die Heuristik die Zustände außerhalb des Pfads bewertet. Zweiter
Fall: Wenn die optimale Lösung die Kosten C(cid:3) und die zweitbeste die Kosten C hat und
2
h.n/einige Kostenüberschätzt, abernieummehralsC (cid:5)C(cid:3),dannliefert A(cid:3) garantiert die
2
kostenoptimalenLösungen.
3.5.3 Suchkonturen
EineguteArt,eineSuchezuvisualisieren,istdasZeichnenvonKonturenimZustandsraum,
ähnlich den Konturen in einer topografischen Karte. IAbbildung 3.20 zeigt ein Beispiel
dafür.InnerhalbderKontur,diemit400beschriftetist,habenalleKnotenf.n/Dg.n/Ch.n/(cid:6)
400undsoweiter.DaA(cid:3)denGrenzknotenmitdenniedrigstenf-Kostenexpandiert,kannman
sehen, dass sich eine A(cid:3)-Suche vomStartknoten aus fächerförmig ausbreitet und Knoten in
konzentrischenBändernmitsteigendenf-Kostenhinzufügt.
O
N
Z
A I
380 S
F
V
400
T R
P
L
H
M U
B
420
D
E
C
G
Abbildung3.20:KartevonRumänienmitKonturenfürfD380,fD400undfD420mitAradalsStartzustand.Knoten
innerhalbeinerbestimmtenKonturhabenfDgCh-KostenkleinerodergleichdemKonturwert.
BeideruniformenKostensuchesehenwirebenfallsKonturen,dochmitg-KostenanstattgCh.
DieKonturenbeideruniformenKostensuchesind„kreisförmig“umdenStartzustandherum
und breiten sich gleichmäßig in alle Richtungen aus, ohne Präferenz in Richtung des Ziels.
Bei der A(cid:3)-Suche mit einer guten Heuristik dehnen sich die gCh-Bänder in Richtung eines
Zielzustandsaus(wieinIAbbildung3.20)undkonzentrierensichengerumeinenoptimalen
Pfad.
Es sollte klar sein, dass die g-Kosten monoton sind, wenn Sie einen Pfad verlängern: Die
Pfadkostensteigenimmer,wennSieeinenPfadentlanggehen,weildieAktionskostenimmer
positiv sind.12 Daher erhalten Sie konzentrische Konturlinien, die sich nicht kreuzen, und
wenn Sie sich entscheiden, die Linien fein genug zu zeichnen, können Sie eine Linie zwi-
schenzweibeliebigenKnotenaufjedemPfadziehen.
12Technischgesehensagenwir„strengmonoton“beiKosten,dieimmersteigen,und„monoton“beiKosten,
dieniesinken,abergleichbleibenkönnen.
119
--- PAGE 121 ---
3
ProblemlösendurchSuchen
Doch es ist nicht offensichtlich, ob die Kosten für f D g Ch monoton ansteigen werden.
WennSieeinen Pfadvonnnach n0 verlängern, gehen dieKosten vong.n/Ch.n/zu g.n/C
c.n;a;n0/Ch.n0/.StreichenwirdenTermg.n/,sosehenwir,dassdieKostendesPfadsgenau
dann monoton ansteigen, wenn h.n/ (cid:6) c.n;a;n0/Ch.n0/ ist – mit anderen Worten, genau
dann,wenn dieHeuristik konsistent ist.13 Beachten Sieaber, dass einPfad mehrere Knoten
hintereinandermitdemgleicheng.n/Ch.n/-Wertbeitragenkann;diesgeschiehtimmerdann,
wenn die Verringerung von h genau den soeben durchgeführten Aktionskosten entspricht
(z.B.ineinemGitterproblem,wennnindergleichenReihewiedasZielliegtundSieeinen
Schritt in Richtungdes Ziels machen, wird g um 1 erhöht und h um 1 verringert). Sind C(cid:3)
dieKostendesoptimalenLösungswegs,dannkönnenwirFolgendesfesthalten:
(cid:2) A(cid:3) expandiert alle Knoten, die vom Anfangszustand aus auf einem Pfad erreicht werden
können,wobei fürjeden Knotenauf demPfadf.n/ < C(cid:3) gilt.Wirsagen, dassdiessicher
expandierteKnotensind.
(cid:2) A(cid:3)könntedanneinigederKnotendirektaufder„Zielkontur“(mitf.n/DC(cid:3))expandieren,
bevoreinZielknotenausgewähltwird.
(cid:2) A(cid:3) expandiertkeineKnotenmitf.n/>C(cid:3).
Wirsagen, dassA(cid:3) miteinerkonsistenten Heuristik optimaleffizient indemSinneist,dass
jederAlgorithmus,derSuchpfadevomAnfangszustandauserweitertunddieselbenheuristi-
schenInformationenverwendet,alleKnotenexpandierenmuss,dievonA(cid:3)sicherexpandiert
werden (weil jedervonihnenTeileineroptimalenLösunggewesen seinkönnte).Unterden
Knotenmitf.n/DC(cid:3)könnteeinAlgorithmusGlückhabenunddenoptimalenKnotenzuerst
auswählen,währendeinandererAlgorithmusPechhat–diesenUnterschiedberücksichtigen
wirbeiderDefinitionderoptimalenEffizienznicht.
A(cid:3)isteffizient,weilesSuchbaumknotenkürzt(Pruning),diefürdasFindeneineroptimalen
Lösungnicht notwendig sind. In IAbbildung3.18b sehen wir, dass fürTimisoara f D 447
und für Zerind f D 449 gilt. Obwohl beides Kindknoten der Wurzel sind und zu den ers-
ten Knoten gehören würden, die bei der uniformen Kosten- oder Breitensuche expandiert
werden, werden sie bei der A(cid:3)-Suche nie expandiert, weil die Lösung mit f D 418 zuerst
gefundenwird.DasKonzeptdesKürzens–dasEliminierenvonMöglichkeitenaufBasisdes
vorhandenenWissen,ohnediesnäheruntersuchenzumüssen–istfürvieleBereichederKI
wichtig.
Die A(cid:3)-Suche ist also unter all diesen Algorithmen vollständig, kostenoptimal und optimal
effizient –was recht zufriedenstellend ist,dochleider heißtesnochnicht,dassA(cid:3) dieAnt-
wort auf alle unsere Suchanforderungen ist. Der Haken dabei ist, dass bei vielen Proble-
mendieAnzahlderexpandiertenKnotenexponentiellzurLängederLösungwachsenkann.
BetrachtenwirzumBeispieleineVersionderStaubsaugerweltmiteinemsuperstarkenStaub-
sauger, der jedes beliebige Feld mit einem Aufwand von 1 Einheit säubern kann, ohne das
Feld überhaupt besuchen zu müssen – in diesem Szenario können die Felder in beliebiger
Reihenfolgegereinigt werden.BeiN anfänglich verschmutztenFelderngibtes2N Zustände,
in denen eine Teilmenge gereinigt wurde; alle diese Zustände liegen auf einem optimalen
Lösungswegunderfüllendaherf.n/<C(cid:3),sodassallevonA(cid:3)besuchtwürden.
3.5.4 Satisficing-Suche:Unzulässige Heuristiken
(cid:2)
undgewichtete A -Algorithmen
Die A(cid:3)-Suche hat viele gute Eigenschaften, doch sie expandiert viele Knoten. Wir können
wenigerKnotenerforschen(unddamitwenigerZeitundPlatzverbrauchen),wennwirbereit
sind, Lösungen zu akzeptieren, die zwar suboptimal, aber „gut genug“ sind – was wir als
13InderTatistderBegriff„monotoneHeuristik“einSynonymfür„konsistenteHeuristik“.DiebeidenKonzepte
wurdenunabhängigvoneinanderentwickelt,dannwurdebewiesen,dasssieäquivalentsind(Pearl,1984).
120
--- PAGE 122 ---
3.5 Informierte(heuristische)Suchstrategien
zufriedenstellende(satisficing)Lösungenbezeichnen.WennwirderA(cid:3)-Sucheerlauben,eine
unzulässige Heuristik zu verwenden – eine, die eventuell überschätzt – dann riskieren wir,
die optimale Lösung zu verpassen, aber die Heuristik ist möglicherweise genauer, was die
Anzahl der expandierten Knoten reduziert. Straßenbauingenieure kennen zum Beispiel das
Konzept eines Umwegindexes, ein Wert, der mit der Luftlinie multipliziert wird, um die
typische Krümmung von Straßen zu berücksichtigen. Ein Umwegindex von 1;3 bedeutet,
dass fürzwei Städte, die10km Luftlinevoneinanderentfernt sind, eine gute Schätzung für
den besten Pfad zwischen ihnen 13kmbeträgt. Fürdie meisten Orte liegt der Umwegindex
zwischen1,2und1,6.
WirkönnendiesesKonzeptaufjedesbeliebigeProblemanwenden,nichtnuraufsolche,bei
denen es um Straßen geht, und zwar mit einem Ansatz, der gewichtete A(cid:3)-Suche genannt
wird,beiderwirdenheuristischenWertstärkergewichten,sodasswirdieEvaluierungsfunk-
tionf.n/Dg.n/CW(cid:2)h.n/erhalten,fürirgendeinW >1.
IAbbildung 3.21 zeigt ein Suchproblem in einer Gitterwelt. In IAbbildung 3.21a findet
eine A(cid:3)-Suche die optimale Lösung, muss dafür aber einen großen Teil des Zustandsraums
erkunden.InIAbbildung3.21bfindeteinegewichteteA(cid:3)-SucheeineLösung,dieetwasteu-
rer, doch viel schneller ist. Wir sehen, dass diegewichtete Suche die Konturdererreichten
Zustände auf ein Ziel konzentriert. Das bedeutet, dass weniger Zustände erforscht werden,
dochwennderoptimalePfadaußerhalbderKonturdergewichteten Sucheliegt(wieindie-
semFall),dannwirdernichtgefunden.KostetdieoptimaleLösungC(cid:3),sowirdeinegewich-
tete A(cid:3)-Suche im Allgemeinen eine Lösung finden, die irgendwo zwischen C(cid:3) und W (cid:2)C(cid:3)
liegt;inderPraxiserhaltenwirjedochnormalerweiseErgebnisse,dievielnäheranC(cid:3)alsan
W(cid:2)C(cid:3)liegen.
(a) (b)
Abbildung3.21:ZweiSuchenaufdemselbenGitter:(a)eineA(cid:3)-Sucheund(b)einegewichteteA(cid:3)-SuchemitGewichtWD2.
DiegrauenBalkensindHindernisse,dievioletteLinieistderWegvomgrünenStartzumrotenZielunddiekleinenPunktesind
Zustände,dievonjederSucheerreichtwurden.BeidiesemspeziellenProblemerkundetdiegewichteteA(cid:3)-Suche7-malweniger
ZuständeundfindeteinenPfad,der5%teurerist.
Wir haben Suchen betrachtet, die Zustände durch Kombination von g und h auf verschie-
deneWeisebewerten;diegewichteteA(cid:3)-SuchekannalseineVerallgemeinerungderanderen
Suchmethodenbetrachtetwerden:
A(cid:3)-Suche: g.n/Ch.n/.W D1/
uniformeKostensuche: g.n/.W D0/
gierigeBestensuche: h.n/.W D1/
gewichteteA(cid:3)-Suche: g.n/CW(cid:2)h.n/.1<W <1/
121
--- PAGE 123 ---
3
ProblemlösendurchSuchen
Man könnte die gewichtete A(cid:3)-Suche als „ein bisschen gierig“ bezeichnen: wie die gierige
BestensuchekonzentriertsiesichaufeinZiel;andererseitsignoriertsiediePfadkostennicht
vollständigundstellteinenPfadzurück,derwenigFortschrittzugroßenKostenbringt.
EsgibteineVielzahl vonsuboptimalenSuchalgorithmen,diedurchdieKriteriendafür,was
als „gut genug“ gilt, charakterisiert werden können. Bei der beschränkten suboptimalen
Suche suchen wir nach einer Lösung, die garantiert innerhalb eines konstanten Faktors W
der optimalen Kosten liegt. Die gewichtete A(cid:3)-Suche bietet diese Garantie. Bei der Suche
mitbeschränkten Kosten suchen wirnach einer Lösung,deren Kosten kleiner sindals eine
Konstante C. Und bei derSuche ohnebeschränkte Kosten akzeptieren wireine Lösungmit
beliebigenKosten,solangewirsieschnellfindenkönnen.
EinBeispielfüreinenSuchalgorithmusmitunbeschränktenKostenistdieSpeedy-Suche,die
eine Version der gierigen Bestensuche ist und als Heuristik die geschätzte Anzahl der zum
ErreicheneinesZielserforderlichenAktionenverwendet,unabhängigvondenKostendieser
Aktionen.FürProbleme,beidenenalleAktionendiegleichenKostenhaben,istdieSpeedy-
Suchealso dieselbe wiediegierige Bestensuche, doch wenn dieAktionen unterschiedliche
Kosten haben, findet sie in der Regel schnell eine Lösung, auch wenn diese vielleicht hohe
Kostenhat.
3.5.5 SpeicherbeschränkteSuche
DasHauptproblemvonA(cid:3)istderSpeicherverbrauch.IndiesemAbschnittwerdenwireinige
Implementierungstricks behandeln, die Platz sparen, sowie einige völligneue Algorithmen,
diedenverfügbarenPlatzbesserausnutzen.
DerSpeicherwirdzwischendenZuständenderGrenze(frontier)unddenerreichtenZustän-
den(reached)aufgeteilt.InunsererImplementierungderBestensuchewirdeinZustand,der
sich auf der Grenze befindet, an zwei Stellen gespeichert: als Knoten in der Grenze (damit
wir entscheiden können, was als Nächstes expandiert werden soll) und als Eintrag in der
TabelledererreichtenZustände(damitwirwissen,obwirdenZustandschoneinmalbesucht
haben).FürvieleProbleme(z.B.dieErkundungeinesGitters)istdieseDuplizierungkeinPro-
blem,dadieGrößevonfrontier vielkleineristalsdievonreached,sodassdieDuplizierung
derZuständeinderGrenzeeinevergleichsweise trivialeMengeanSpeichererfordert.Doch
einige Implementierungen speichern einen Zustand nur an einem der beiden Orte, was ein
wenigSpeicherplatzeinspart,dochdenAlgorithmuskomplizierter(undvielleichtlangsamer)
macht.
EineandereMöglichkeitist,Zuständeausreachedzuentfernen,wennwirbeweisenkönnen,
dass sie nicht mehr benötigt werden. Für einige Probleme können wir die Trennungseigen-
schaft (IAbbildung3.6auf S.102)zusammen mitdem Verbot vonKehrtwenden benutzen,
um sicherzustellen, dass sich alle Aktionen entweder von der Grenze weg- oder auf einen
anderenGrenzzustandzubewegen.IndiesemFallmüssenwirnurdieGrenzeaufredundante
Pfadeüberprüfenundbrauchendiereached-Tabellenichtmehr.
BeianderenProblemenkönnenwirReferenzzählerverwenden,dieaufzeichnen,wieoftein
Zustand erreicht wurde, und diesen aus der reached-Tabelle entfernen, wenn es keine wei-
terenMöglichkeitengibt,denZustandzuerreichen.IneinerGitterwelt,inderjederZustand
nur von seinen vier Nachbarn aus erreicht werden kann, können wir beispielsweise einen
ZustandausderTabelleentfernen,sobalderviermalerreichtwurde.
BetrachtenwirnunneueAlgorithmen,dieaufeinesparsameSpeichernutzungausgelegtsind.
DieStrahlsuche(BeamSearch)begrenztdieGrößederGrenze.DereinfachsteAnsatzist,nur
diekKnotenmitdenbestenf-WertenzubehaltenundalleanderenexpandiertenKnotenzu
verwerfen.DadurchwirddieSuchenatürlichunvollständigundsuboptimal,aberwirkönnen
122
--- PAGE 124 ---
3.5 Informierte(heuristische)Suchstrategien
ksowählen,dassderverfügbareSpeichergutgenutztwird,außerdemwirdderAlgorithmus
schnellausgeführt,daerwenigerKnotenexpandiert.FürvieleProblemekannergute,nahezu
optimaleLösungenfinden.MankannsichdieuniformeKosten-oderA(cid:3)-Suchesovorstellen,
dass sie sich überall in konzentrischen Konturen ausbreitet, und man kann sich die Strahl-
suchesovorstellen,dasssienureinenfokussiertenTeildieserKonturenerforscht–denTeil,
derdiekbestenKandidatenenthält.
EinealternativeVersionderStrahlsuchebeschränktdieGrößederGrenzewenigerstark,son-
dern speichert stattdessen jeden Knoten, dessen f-Wert innerhalb von ı des besten f-Werts
liegt. AufdieseWeise werden, wenn eseinige Knotenmitstarken Werten gibt, nureinpaar
behalten, dochwenneskeinestarken Knotengibt,werden mehrgespeichert, biseinstarker
Knotenauftaucht.
Die iterative A(cid:3)-Tiefensuche (Iterative Deepening A(cid:3), IDA(cid:3)) ist für A(cid:3) das, was die iterative
TiefensuchefürdieallgemeineTiefensucheist:IDA(cid:3)bietetunsdieVorteilevonA(cid:3),ohnedass
alleerreichtenZuständeimSpeichergehaltenwerdenmüssen,allerdingszudemPreis,dass
einigeZuständemehrfachbesuchtwerden.Esisteinsehrwichtigerundhäufigverwendeter
AlgorithmusfürProbleme,dienichtindenSpeicherpassen.
Bei dernormalen iterativen Tiefensuche istder Cutoff-WertdieTiefe, diebei jeder Iteration
umeinserhöhtwird.BeiIDA(cid:3)sinddief-Kosten(gCh)derCutoff-Wert;beijederIterationist
derCutoff-Wertderkleinstef-KostenwerteinesKnotens,derdenCutoff-Wertbeidervorhe-
rigen Iteration überschritten hat. Mitanderen Worten, jedeIteration sucht erschöpfend eine
f-Kontur,findeteinenKnotendirekthinterdieserKonturundverwendetdief-Kostendieses
KnotensalsnächsteKontur.BeiProblemenwiedem8-Puzzle,beidemderf-Kostenwertjedes
PfadseineganzeZahlist,funktioniertdiessehrgutundführtbeijederIterationzueinemste-
tigen Fortschritt in Richtung des Ziels. Hat die optimale Lösung die Kosten C(cid:3), so kann es
nichtmehralsC(cid:3) Iterationengeben(zumBeispielnichtmehrals31Iterationenbeidenhär-
testen 8-Puzzle-Problemen). Aber für ein Problem, bei dem jeder Knoten unterschiedliche
f-Kostenhat,könntejedeneueKonturnureinenneuenKnotenenthaltenunddieAnzahlder
IterationenkönntesogroßwiedieAnzahlderZuständesein.
Die rekursive Bestensuche (Recursive Best-First Search, RBFS,IAbbildung3.22)versucht,
dieFunktionsweisederstandardmäßigen Bestensuche zu imitieren,benötigtabernurlinea-
renPlatz.RBFSähnelteinerrekursivenTiefensuche,dochanstattdenaktuellenPfadunend-
functionRECURSIVE-BEST-FIRST-SEARCH(problem)returnseineLösungoderfailure
solution,fvalue RBFS(problem,NODE(problem.INITIAL),1)
returnsolution
functionRBFS(problem,node,f_limit)returnseineLösungoderfailureundeineneue
f-Kostengrenze
ifproblem.IS-GOAL(node.STATE)thenreturnnode
successors LIST(EXPAND(node))
ifsuccessorsistleerthenreturnfailure,1
foreachsin successorsdo //fmitWertausvorigerSucheaktualisieren
s.f max.s:PATH-COST C h.s/; node:f/)
whiletruedo
best derKnoteninsuccessorsmitkleinstemf-Wert
ifbest:f > f_limitthenreturnfailure,best.f
alternative derzweitkleinstef-Wertaussuccessors
result,best.f RBFS(problem,best,min.f_limit;alternative/)
ifresult¤failurethenreturnresult,best.f
Abbildung3.22:DerAlgorithmusfürdierekursiveBestensuche
123
--- PAGE 125 ---
3
ProblemlösendurchSuchen
lichnachuntenfortzusetzen,verwendetRBFSdief_limit-Variable,umdenf-Wertdesbesten
alternativenPfadszuverfolgen,dervonjedemVorgängerdesaktuellenKnotensausverfügbar
ist.WennderaktuelleKnotendiesen Grenzwert überschreitet, wirddieRekursionaufgelöst
unddieSuchegehtzumalternativenPfadzurück.BeiderRekursionsauflösungersetztRBFS
den f-Wert jedes Knotens entlang des Pfads durch einen gesicherten Wert – den besten f-
Wert seiner Kindknoten. Auf diese Weise merkt sich RBFS den f-Wert des besten Blatts im
vergessenen Unterbaum und kann daher entscheiden, ob es sich lohnt, den Unterbaum zu
einem späteren Zeitpunkt erneut zu expandieren. IAbbildung 3.23 zeigt, wie RBFS Buka-
resterreicht.
(a) Nach Expansion von Arad, Sibiu ∞
und Rimnicu Vilcea Arad 366
447
Sibiu Timisoara Zerind
393
447 449
415
Arad Fagaras Oradea Rimnicu Vilcea
413
646 415 671
Craiova Pitesti Sibiu
526 417 553
(b) Nach Rückschritt zu Sibiu und ∞
Expansion von Fagaras Arad 366
447
Sibiu Timisoara Zerind
393 447 449
417
Arad Fagaras Oradea Rimnicu Vilcea
646 415 671 413 417
Sibiu Bukarest
591 450
(c) Nach Wechsel zu Rimnicu Vilcea ∞
und Expansion von Pitesti Arad 366
447
Sibiu Timisoara Zerind
393 447 449
447
Arad Fagaras Oradea Rimnicu Vilcea
646 415 450 671 417
447
Craiova Pitesti Sibiu
526 417 553
Bukarest Craiova Rimnicu Vilcea
418 615 607
Abbildung3.23:PhasenineinerRBFS-SuchenachderkürzestenRoutenachBukarest.Derf-Grenzwertfürjedenrekursiven
AufrufwirdüberdemjeweilsaktuellenKnotenangezeigtundjederKnotenistmitseinenf-Kostenbeschriftet.(a)DerPfad
überRimnicuVilceawirdsolangeverfolgt,bisdasaktuellbesteBlatt(Pitesti)einenWerthat,derschlechteristalsderbeste
alternativePfad(Fagaras).(b)DieRekursionwirdaufgelöstundderbesteBlattwertdesvergessenenUnterbaums(417)wird
RimnicuVilceaalsgesicherterWertzugeordnet;dannwirdFagarasexpandiert,waseinenbestenBlattwertvon450ergibt.
(c)DieRekursionwirdaufgelöstundderbesteBlattwertdesvergessenenUnterbaums(450)wirdFagarasalsgesicherterWert
zugeordnet;dannwirdRimnicuVilceaexpandiert.DaderbestealternativePfad(überTimisoara)mindestens447kostet,wird
dieExpansiondiesmalbisBukarestfortgesetzt.
124