# EXTRACTION TEXTUELLE - RL_Spanish_Thesis.pdf
# Généré automatiquement le 2025-05-28 10:27:51
# Source: RL_Spanish_Thesis.pdf
# ================================================================


--- PAGE 1 ---
Universidad Politécnica
de Madrid
Escuela Técnica Superior de
Ingenieros Informáticos
Grado en Ingeniería Informática
Trabajo Fin de Grado
Aprendizaje de funciones de evaluación
con métodos deep learning en ajedrez
por computadora
Autor: <PERSON><PERSON><PERSON>(a): Javier <PERSON>ín
Madrid, 06 22
--- PAGE 2 ---
Este Trabajo Fin de Grado se ha depositado en la ETSI Informáticos de la
Universidad Politécnica de Madrid para su defensa.
Trabajo Fin de Grado
Grado en Ingeniería Informática
Título: Aprendizaje de funciones de evaluación con métodos deep learning
en ajedrez por computadora
06 22
Autor: <PERSON><PERSON><PERSON>: Javier <PERSON> Asiaín
Inteligencia Artificial
ETSI Informáticos
Universidad Politécnica de Madrid
--- PAGE 3 ---
Resumen
El ajedrez es uno de los juegos más importantes de la historia de la humani-
dad, con siglos de influencia alrededor del mundo, siendo jugado por una gran
cantidad de personas incluso en la actualidad.
Esta gran influencia ha hecho que desde los inicios de la computación, fuera
objeto de estudio y aprendizaje. Fruto de ello, han nacido diferentes soluciones
para hacer que un computador sea capaz de jugar al juego de manera indepen-
diente y optimizada, pudiendo incluso superar el nivel de los mejores jugadores
humanos.
En los últimos años, el Deep Learning ha sufrido una gran evolución hasta
situarse como uno de los pilares fundamentales de la informática, extendiendo
dicha influencia también al ajedrez. Con el reciente auge del Deep Learning,
y su aplicación en el campo del ajedrez por computadora, se han llevado las
capacidades de las máquinas a niveles nunca antes vistos.
i
--- PAGE 5 ---
Abstract
Chess is one of the most important games in the history of mankind, with cen-
turies of influence around the world, being played by a great number of people
even nowadays.
This great influence has made that since the beginnings of computing, it has
been object of study and learning. As a result, different solutions have been
born to make a computer capable of playing the game in an independent and
optimized way, being even able to surpass the level of the best human players.
In recent years, Deep Learning has undergone a great evolution to become one
of the fundamental pillars of computer science, extending this influence also to
chess. With the recent rise of Deep Learning, and its application in the field of
computer chess, the capabilities of machines have been taken to levels never
seen before.
iii
--- PAGE 7 ---
Tabla de contenidos
1. Introducción 1
1.1. Motivación . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 1
1.2. Objetivos . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 2
1.3. Herramientas Utilizadas . . . . . . . . . . . . . . . . . . . . . . . . . 3
1.3.1. PyTorch . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 3
2. Fundamentos del Ajedrez 5
2.1. El ajedrez . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 5
2.2. Origen del Ajedrez: Chaturanga . . . . . . . . . . . . . . . . . . . . . 5
2.3. Origen del Ajedrez: Otras Hipótesis . . . . . . . . . . . . . . . . . . . 6
2.4. Expansión y evolución del Ajedrez . . . . . . . . . . . . . . . . . . . . 6
2.5. Teoría de Juegos en el ámbito de la Inteligencia Artificial . . . . . . 7
2.5.1. Definición . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 7
2.5.2. Juegos de suma cero . . . . . . . . . . . . . . . . . . . . . . . 7
3. Fundamentos del Deep Learning 9
3.1. Inteligencia Artificial: Deep Learning . . . . . . . . . . . . . . . . . . 9
3.2. Red Neuronal . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 9
3.2.1. Objetivo principal de una red neuronal . . . . . . . . . . . . . 10
3.2.2. Tipos de problemas . . . . . . . . . . . . . . . . . . . . . . . . 10
3.2.3. Funciones de activación . . . . . . . . . . . . . . . . . . . . . . 10
3.2.4. RelU . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 11
3.2.5. Tangente Hiperbólica . . . . . . . . . . . . . . . . . . . . . . . 11
3.2.6. Optimizadores . . . . . . . . . . . . . . . . . . . . . . . . . . . 12
3.3. Datasets . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 13
3.4. Entrenamiento . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 13
3.5. Problemas a resolver . . . . . . . . . . . . . . . . . . . . . . . . . . . 14
3.5.1. Overfitting . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 14
3.5.2. Underfitting . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 15
3.5.3. Problema del Vanishing Gradient . . . . . . . . . . . . . . . . 16
4. Fundamentos del Ajedrez por Computadora 17
4.1. Estado del Arte . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 17
4.1.1. Stockfish . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 17
*******. Origen . . . . . . . . . . . . . . . . . . . . . . . . . . . 17
*******. Características . . . . . . . . . . . . . . . . . . . . . . 18
v
--- PAGE 8 ---
TABLA DE CONTENIDOS
*******. Actualidad . . . . . . . . . . . . . . . . . . . . . . . . . 18
4.1.2. AlphaZero . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 18
4.1.2.1. Origen . . . . . . . . . . . . . . . . . . . . . . . . . . . 18
*******. Características . . . . . . . . . . . . . . . . . . . . . . 19
*******. Actualidad . . . . . . . . . . . . . . . . . . . . . . . . . 19
4.2. Árbol de Juego . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 19
4.3. Algoritmo de Búsqueda . . . . . . . . . . . . . . . . . . . . . . . . . . 19
4.3.1. Minimax . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 20
4.3.2. Optimización Alpha-Beta . . . . . . . . . . . . . . . . . . . . . 21
4.3.3. Stockfish: Principal Variation Search (PVS). . . . . . . . . . . 23
4.3.4. AlphaZero: Monte Carlo Tree Search (MCTS) . . . . . . . . . . 24
4.3.5. Diferencias entre PVS y MCTS . . . . . . . . . . . . . . . . . . 25
4.4. Función de evaluación . . . . . . . . . . . . . . . . . . . . . . . . . . 26
4.4.1. Enfoque clásico . . . . . . . . . . . . . . . . . . . . . . . . . . . 26
4.4.2. Redes neuronales como función de evaluación . . . . . . . . 28
4.4.3. Stockfish: Redes NNUE . . . . . . . . . . . . . . . . . . . . . . 28
4.4.4. AlphaZero: Redes Neuronales Convolucionales (CNN) . . . . 30
5. Desarrollo 35
5.1. Datasets . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 35
5.1.1. Elección de Datasets . . . . . . . . . . . . . . . . . . . . . . . . 35
5.1.2. Análisis de datos . . . . . . . . . . . . . . . . . . . . . . . . . . 35
5.1.3. Transformación de datos . . . . . . . . . . . . . . . . . . . . . 38
5.2. Creación de la Red Neuronal . . . . . . . . . . . . . . . . . . . . . . . 41
5.2.1. Número de capas . . . . . . . . . . . . . . . . . . . . . . . . . . 41
5.2.2. Funciones de activación . . . . . . . . . . . . . . . . . . . . . . 42
6. Resultados y conclusiones 43
6.1. Alternativas a la red propuesta . . . . . . . . . . . . . . . . . . . . . 43
6.2. Resultados de los distintos modelos . . . . . . . . . . . . . . . . . . . 43
6.2.1. Modelo 1 . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 44
6.2.2. Modelo 2 . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 44
6.2.3. Modelo 3 . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 44
6.2.4. Modelo Final . . . . . . . . . . . . . . . . . . . . . . . . . . . . 45
6.3. Partidas contra Stockfish . . . . . . . . . . . . . . . . . . . . . . . . . 45
6.4. Conclusiones . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 46
6.5. Trabajos Futuros. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 46
7. Análisis de impacto 49
Bibliografía 51
vi
--- PAGE 9 ---
Capítulo 1
Introducción
DesdequeelconceptodeInteligenciaArtificialhaexistido,sehanbuscadoejem-
plos prácticos en los que una máquina pudiera superar la habilidad de un hu-
mano en la resolución de un problema determinado. El ajedrez, siendo un juego
con una influencia muy grande en la historia y con un carácter estratégico muy
pronunciado, parecía el candidato perfecto para este reto.
Así nació en el año 1950 la idea del Ajedrez por Computadora, y desde entonces
se ha convertido en un clásico de la Inteligencia Artificial, habiendo cada vez
más y más soluciones e ideas que intentan llevar la capacidad del computador
al extremo dentro del juego.
Gracias a su gran desarrollo y popularidad, el Ajedrez por Computadora ha ido
escalando hasta poco a poco ir convirtiéndose en todo un referente en la disci-
plina.
1.1. Motivación
Una de las aplicaciones de la teoría de juegos en el ámbito de la informática es
la de crear algoritmos capaces de jugar a diferentes juegos, de tal manera que
la habilidad de la computadora sea superior a la del humano. Con esta idea
en mente, en 1950 Claude Elwood Shannon propuso la idea de programar un
ordenador para jugar al ajedrez en su árticulo Programming a Computer for
Playing Chess [1]. Desde entonces este concepto fue avanzando y el ajedrez se
convirtió en uno de los juegos por excelencia en cuanto al desarrollo de diferen-
tes soluciones capaces de jugar de manera autónoma.
El año 1996 supuso un antes y un después en el desarrollo de supercomputado-
res capaces de jugar al ajedrez. En este año, el supercomputador Deep Blue [2]
de IBM se convirtió en el primer computador en vencer a un campeón mundial
de ajedrez, por entonces Garri Kaspárov. En este encuentro se disputaron 6
partidas, cuyos resultados fueron:
1
--- PAGE 10 ---
1.2. Objetivos
Partida Blancas Negras Resultado Situación Global
1 Deep Blue Kaspárov 1-0 Deep Blue 1 - 0 Kaspárov
2 Kaspárov Deep Blue 1–0 Deep Blue 1 - 1 Kaspárov
3 Deep Blue Kaspárov ½–½ Deep Blue 1½ - 1½ Kaspárov
4 Kaspárov Deep Blue ½–½ Deep Blue 2 - 2 Kaspárov
5 Deep Blue Kaspárov 0–1 Deep Blue 2 - 3 Kaspárov
6 Kaspárov Deep Blue 1-0 Deep Blue 2 - 4 Kaspárov
Cuadro 1.1: Enfrentamientos entre Garri Kaspárov y Deep Blue en 1996
Al año siguiente, en 1997, el equipo de IBM presentó una nueva version de Deep
Blue, con la cual consiguieron vencer definitivamente a Kaspárov, de nuevo al
mejor de 6:
Partida Blancas Negras Resultado Situación Global
1 Kaspárov Deep Blue 1-0 Deep Blue 0 - 1 Kaspárov
2 Deep Blue Kaspárov 1–0 Deep Blue 1 - 1 Kaspárov
3 Kaspárov Deep Blue ½–½ Deep Blue 1½ - 1½ Kaspárov
4 Deep Blue Kaspárov ½–½ Deep Blue 2 - 2 Kaspárov
5 Kaspárov Deep Blue ½–½ Deep Blue 2½ - 2½ Kaspárov
6 Deep Blue Kaspárov 1-0 Deep Blue 3½ - 2½ Kaspárov
Cuadro 1.2: Enfrentamientos entre Garri Kaspárov y Deep Blue en 1997
A lo largo de los siguientes años se fueron desarrollando diferentes ideas y en-
foques sobre el desarrollo de computadoras capaces de jugar al ajedrez, entre
los cuales destacan Stockfish y AlphaZero. Estos algoritmos, denominados mo-
tores de ajedrez tienen una optimización tan elevada, que su habilidad actual
supera con creces a la de los mejores jugadores del mundo.
1.2. Objetivos
El objetivo principal de este Trabajo de Fin de Grado es el estudio y aplicación
de técnicas de Deep Learning en el contexto del ajedrez por computadora.
Además, otros objetivos que derivan de este son:
Revisión del estado del arte del ajedrez y ajedrez por computadora, desde
su historia y origen hasta su situación actual.
Entendimiento de la Teoría de Juegos en el ámbito de la informática.
Estudio de los diferentes enfoques para crear un motor de ajedrez (algorit-
mica clásica vs inteligencia artificial).
Comprensión de los conceptos y técnicas que caracterizan al Deep Lear-
ning.
Creación de un pequeño motor de ajedrez combinando el enfoque más clá-
sico con la optimizacion de la Inteligencia Artificial, mediante el uso de una
2
--- PAGE 11 ---
Introducción
red neuronal.
1.3. Herramientas Utilizadas
1.3.1. PyTorch
PyTorch [3] es una librería de código abierto para el lenguaje de programación
Python desarrollada por el el Facebook’s AI Research lab (FAIR) que fue lanzada
en el año 2016 y permite el desarrollo de redes neuronales.
La creación de esta librería tenía como objetivos fundamentales:
Ofrecer una buena implementación de conceptos Deep Learning con un
código al estilo Pythonic.
Ser una librería que diera alta flexibilidad a los desarrolladores.
Conseguir la sencillez del código al estilo Pythonic, sin perder demasiado
rendimiento a cambio.
Gracias a su mayor simpleza en términos de código, comparada con otras li-
brerías o frameworks del ámbito, PyTorch se ha consolidado como una de las
mejores librerías de Deep Learning, siendo utilizada por muchos desarrollado-
res en todo el mundo. Una de sus principales características es la existencia
del Tensor, una estructura de datos que se asemeja a un array multidimensio-
nal el cual esta altamente optimizado y que puede ser utilizado por las GPUs
para la realización de operaciones en paralelismo masivo, lo que consigue un
rendimiento muy superior al de la CPU.
Por lo tanto, las principales ventajas de PyTorch se podrían resumir en:
Sencillez de código.
Mayor rendimiento mediante el uso de GPUs.
Versatilidad para la resolución de problemas Deep Learning.
3
--- PAGE 13 ---
Capítulo 2
Fundamentos del Ajedrez
2.1. El ajedrez
El ajedrez es un juego de estrategia que enfrenta a dos personas las cuales dis-
ponen de manera inicial de 16 piezas cada una, que se mueven por un tablero
de 64 casillas llamadas escaques. Cada jugador es asignado un color (blanco o
negro), el cual se utiliza para diferenciarse de su rival. El objetivo final del juego
es conseguir capturar al rey enemigo, obteniendo así la victoria sobre el opo-
nente. El ajedrez es considerado deporte por el Comité Olímpico Internacional,
y hay competiciones a nivel mundial reguladas por la Federación Internacional
de Ajedrez (FIDE). Estas competiciones suelen ser de carácter individual aunque
hay algunas competiciones en las que se juega en equipos, como por ejemplo en
las Olimpíadas de Ajedrez.
2.2. Origen del Ajedrez: Chaturanga
Gracias a la investigación de Harold James Ruthven Murray con su obra A his-
tory of chess [4] podemos relacionar el origen del Ajedrez con el juego indio
Chaturanga. Pese a que el origen de este juego no es conocido, se cree que era
utilizado para idear estrategias en el campo de batalla. El nombre puede hacer
referencia a las cuatro partes en las que se dividía el ejército dentro del juego.
Algunoshistoriadorescreenquelasreglasdeljuegosonlasmismas,omuypare-
cidas, que las del juego persa shatranj. Algo que sí que se sabe es que se jugaba
en un tablero de 8x8 casillas del mismo color, llamado ashta¯pada.
También Alex Kraaijeveld, mediante un estudio en el año 2000 en el que analizó
40 variantes modernas y antiguas del juego, llegó a la conclusión que el origen
más probable del ajedrez era el Chaturanga indio [5].
5
--- PAGE 14 ---
2.3. Origen del Ajedrez: Otras Hipótesis
2.3. Origen del Ajedrez: Otras Hipótesis
Enelaño2001GerhardJosten propusounaideaalternativaalChaturanga
como origen del ajedrez en su estudio Chess. A living fossil [6]. En este
estudio se plantea una hipótesis por la cual el ajedrez fue la consecuencia
de la combinación de juegos más antiguos utilizados en regiones conecta-
das con el comercio de mercancías a lo largo de la antigua ruta de la seda.
Su lugar de origen sería el Imperio Kushan (50 a.C – 200 d.C),una región
al noroeste de la actual India.
Otro de los posibles orígenes del ajedrez es en China, donde un coman-
dante llamado Hán Xin creó el juego para representar una batalla. Tras
desaparecer durante varios siglos, reapareció en el siglo VIId.C con reglas
distintas. Este nuevo juego se hizo popular bajo el nombre Xiang Qi. Se-
gún esta teoría, el Xiang Qi viajaría a la India, y de ahí a Persia donde fue
modificado hasta convertirse en algo parecido al ajedrez tal y como se le
conoce hoy.
2.4. Expansión y evolución del Ajedrez
Se dice que el ajedrez fue introducido en Occidente por los musulmanes, debido
a la conquista de la Península Ibérica. Esta versión antigua del ajedrez contaba
con reglas distintas al ajedrez más contemporáneo:
La dama y el alfil sólo podían avanzar dos casillas.
El alfil podía saltar.
En vez de existir el enroque, existía el salto del rey,que le permitía saltar
por encima de una casilla.
Los peones sólo se movían una casilla
Estas reglas hacían que las partidas fueran muy lentas, por lo que se inventa-
ron las tabiyas (posiciones simétricas con las que por acuerdo de los jugadores
comenzaba la partida) para darle más dinamismo al juego.
Las primeras piezas de ajedrez en Europa datan del año 900d.C y fueron encon-
tradas en Peñalba de Santiago. Reciben el nombre de las piezas o bolos de San
Genadio. En esa época la dama seguía sin existir y era la alferza la que ocupaba
esa casilla, con un movimiento mucho más limitado y que figura descrito en un
códice de Alfonso X el Sabio.
El ajedrez iría evolucionando en Europa hasta ser el juego que hoy conocemos.
Las piezas se transformaron en:
1. Rey
2. Dama
3. Caballo
4. Torre
6
--- PAGE 15 ---
Fundamentos del Ajedrez
5. Alfil
Estas piezas tienen una gran influencia de la sociedad medieval, concretamente
la Cristiana, que se había convertido en la religión más popular en Europa.
Sin embargo, no es hasta siglo XVd.C que el sacerdote español Ruy López de
Segura deja reflejadas en su obra Libro de la invención liberal y arte del
juego del ajedrez [7] las reglas del ajedrez tal y como se le conoce hoy en día,
salvo pequeños ajustes posteriores.
En 1924 se funda en París la Fédération Internationale des Échecs (FIDE) que
cuenta con alrededor de 175 miembros, siendo la segunda mayor federación
deportiva del mundo tras la FIFA.
2.5. Teoría de Juegos en el ámbito de la Inteligencia Ar-
tificial
2.5.1. Definición
La teoría de juegos es un área de las matemáticas aplicadas, introducida y desa-
rrollada en su mayoria por el matemático John von Neumann [8], que se dedica
al estudio de la toma de decisiones de una persona o grupos de personas en
un ámbito de interdependencia. Esta interdependencia hace que la toma de una
decisión se vea influenciada por diferentes factores, como por ejemplo tener en
cuenta las posibles decisiones del oponente a la hora de tomar la suya propia.
La teoría de juegos tiene aplicaciones en varias áreas, como pueden ser la infor-
mática o la economía.
2.5.2. Juegos de suma cero
Los juegos de suma cero son un tipo específico de problema dentro de la Teoría
de juegos en el cual los oponentes tienen intereses contrarios de tal forma que la
suma de dicho interés es 0 [9]. Esto implica que las partes contrarias no pueden
cooperar, y la decisión de cada parte ha de tomarse en desconocimiento de la
decisión del contrario.
Elajedrezentradentrodeestacategoríadejuegos,yaqueelbalanceentrepiezas
ganadas y perdidas por parte de ambos jugadores tiene como resultado final 0.
7
--- PAGE 17 ---
Capítulo 3
Fundamentos del Deep Learning
3.1. Inteligencia Artificial: Deep Learning
La Inteligencia Artificial es una disciplina dentro de la informática que se dedica
al desarrollo de la habilidad que un ordenador tiene para realizar tareas de
manera automática [10].
De manera histórica el enfoque de la Inteligencia Artificial ha sido la creación
de algoritmos capaces de realizar tareas que un humano puede hacer, pero de
manera más rapida y optimizada.
El Deep Learning es una ténica de aprendizaje automático, perteneciente al
campo de la Inteligencia Artificial, que se encarga de la creación de modelos
computacionales capaces de aprender en base a una serie de ejemplos, buscan-
do patrones en los mismos. Estos modelos computacionales reciben el nombre
de redes neuronales.
3.2. Red Neuronal
Una red neuronal es un modelo computacional formado por una serie de capas
de neuronas, conectadas entre sí, que para un determinado dato de entrada
generan una salida [11].
Figura 3.1: Figura que representa una red neuronal.
9
--- PAGE 18 ---
3.2. Red Neuronal
3.2.1. Objetivo principal de una red neuronal
A la hora de la creación de una red neuronal, el objetivo principal es el poder
lograr que la red sea capaz de generalizar, es decir, ser capaz de producir datos
de salida de manera eficiente ante entradas anteriormente no vistas. Para ello,
se busca hallar el punto de convergencia [12].
3.2.2. Tipos de problemas
Dentro del Deep Learning, hay diferentes tipos de enfoques a la hora de la crea-
ción de una red neuronal, en función del tipo de problema que queremos resol-
ver. Algunas categorías que engloban diferentes tipos de problemas son:
Regresión: Se busca encontrar pautas entre los datos de entrada y los
datos objetivo, produciendo valores de salida contínuos, dentro de un in-
tervalo.
Clasificación: Mediante los datos de entrada, se produce una categoriza-
ción como salida. Un ejemplo claro es la clasificación de imágenes.
Debido a la naturalez de nuestro dataset, la red neuronal desarrollada en este
Trabajo de Fin de Grado entraría dentro de la categoría de los problemas de
Regresión.
3.2.3. Funciones de activación
Una red neuronal está compuesta por, al menos, una capa de neuronas de en-
trada y otra de salida, además de posibles capas intermedias, también deno-
minadas ocultas. Estas capas neuronales tienen que ser capaces de determinar
quécaminotomarenfuncióndelosdatosdeentrada,esdecir,calcularlospesos
de cada neurona dentro de cada capa para así producir dicho resultado.
Para que la red neuronal sea capaz de calcular estos pesos, existen las funcio-
nes de activación.
Una función de activación [13] es una función matemática que, como el pro-
pio nombre indica, se encarga de activar las neuronas necesarias para que la
red neuronal sea capaz de calcular los pesos de cada capa y así producir un
resultado final, en relación con los datos de entrada de la red.
Existen varios tipos de funciones de activación, como pueden ser:
Linear
Sigmoidal
Tangente Hiperbólica
RelU
Cada una de estas funciones de evaluación tiene sus propias características, las
cuales son interesantes en función del problema que se esté intentando resolver.
Sin embargo, todas tienen el mismo concepto como base fundamental.
10
--- PAGE 19 ---
Fundamentos del Deep Learning
Para la red neuronal de este Trabajo de Fin de Grado, se va a hacer un mayor
hincapié en las funciones de activación RelU y Tangente Hiperbólica, debido a
que han sido utilizadas en el modelo desarrollado.
3.2.4. RelU
La función de activación RelU (Rectified Linear Unit) [14] viene definida por la
siguiente fórmula matemática:
(cid:2)
0 si x < 0,
RelU(x) = (3.1)
x si x >= 0.
Como se puede ver en la fórmula, la RelU se puede definir como máx(0,x), es
decir, no admite valores negativos.
La función RelU tiene las siguientes ventajas con respecto a otras funciones de
activación:
Menor tiempo computacional, debido a su sencillez.
Mejor rendimiento para alcanzar la convergencia [12].
Mayor estabilidad de los gradientes, lo que aumenta la efectividad del en-
trenamiento de la red.
Figura 3.2: Figura que representa la función de activación RelU.
3.2.5. Tangente Hiperbólica
La función de activación de la Tangente Hiperbólica (Tanh) [15] viene definida
por la siguiente fórmula matemática:
ex−e−x
Tanh(x) = (3.2)
ex+e−x
11
--- PAGE 20 ---
3.2. Red Neuronal
La función de activación Tanh trabaja con valores en el intervalo [−1,1], por lo
que es ideal para usarla como función de activación de la capa de salida, debido
a que nuestra red producirá datos de salida contenidos en este intervalo.
Figura 3.3: Figura que representa la función de activación Tangente Hiperbólica
(tanh).
3.2.6. Optimizadores
Las redes neuronales tradicionales son entrenadas mediante el cálculo del peso
de cada neurona, que llevará los datos de entrada por un camino concreto hasta
una salida, buscando minimizar una función de coste.
Normalmente, estas redes neuronales utilizan técnicas de ajuste de pesos, como
lo es el backpropagation [16].
El backpropagation consiste en ajustar los pesos de cada neurona dentro de
las capas de la red neuronal para el siguiente paso, recorriendo un camino que
empieza en la capa de salida y llega hasta la capa de entrada.
Sin embargo, el backpropagation únicamente se encarga de propagar los valores
quehayqueajustar,peronodecalcularlos.Paracalcularcuántohayqueajustar
los pesos de cada neurona en cada paso existen los optimizadores [17].
Losoptimizadoressonunconjuntodeoperacionesmatemáticasqueseencargan
de calcular los nuevos pesos de cada neurona, con el objetivo de alcanzar una
mayor precisión en el funcionamiento de la red neuronal. Estas operaciones
tienen en cuenta valores como el learning rate [18], el cual indica la escala por
12
--- PAGE 21 ---
Fundamentos del Deep Learning
la cual se deben modificar los pesos, además de otros valores como la función
de coste, la cual indica la diferencia entre el valor salida de la red y el valor real
que se estaba intentando obtener.
Existen multitud de optimizadores, como pueden ser:
Stochastic Gradient Descent.
Adaptive Gradient Descent.
Root Mean Square.
Adam.
En el caso del modelo que se va a crear, se ha optado por la utilización del
optimizador Adam, debido a que es un optimizador bastante genérico, que tiene
un buen funcionamiento en el Deep Learning.
Figura 3.4: Figura que representa la función del optimizador Adam.
3.3. Datasets
Un dataset [19] es una colección de datos utilizados para el entrenamiento de
una red neuronal. Estos datos sirven para que la red sea capaz de aprender,
mediante la comparación de los resultados producidos como salida con los re-
sultados que deberían de producirse, contenidos en dicho dataset.
Si bien es cierto que hay distintas técnicas de entrenamiento que no necesitan
recopilar datos para poder entrenar una red neuronal, el enfoque más clásico,
así como el implementado en este Trabajo de Fin de Grado, hace uso de datasets
para poder llevar a cabo el desarrollo de la red neuronal.
La calidad de los datos recopilados puede tener gran influencia en el resultado
final de la red neuronal. Tener una cantidad muy pequeña de datos puede cau-
sar una falta de aprendizaje, y tener una cantidad muy grande puede causar la
necesidad de mucho tiempo para poder completar el entrenamiento.
3.4. Entrenamiento
El entrenamiento es el proceso mediante el cual una red neuronal es capaz
de aprender sobre unos datos de entrada a generar una salida concreta. La
13
--- PAGE 22 ---
3.5. Problemas a resolver
gran cantidad de datos contenidos en nuestro dataset hace que la red neuronal
necesite de un mayor tiempo de entrenamiento para poder aprender.
Losentrenamientosdeunaredneuronalsedividenenepochs (iteraciones)que
consisten en una serie de pasos que se realizan sobre el conjunto de datos de
entrenamiento, con el objetivo de que la red aprenda a reconocer los patrones
de entrada.
En cada una de estas iteraciones, la red recalcula los pesos de cada neurona,
y luego se actualiza con los nuevos pesos, para que en la próxima iteración se
puedan reconocer de manera más efectiva los patrones en los datos de entrada.
3.5. Problemas a resolver
Cuando se trata de la creación de redes neuronales, hay una serie de problemas
que necesitamos tratar para poder conseguir una red capaz de aprender. Estos
problemas, aunque no son los únicos existentes, son los que suelen estar más
presentes a la hora del desarrollo del modelo.
3.5.1. Overfitting
Eloverfitting[20]esunodelosproblemasprincipalesalahoradelentrenamien-
to de una red neuronal.
Seproducecuandolaredseacostumbrademasiadoalosdatosdeentrenamien-
to, obteniendo unos resultados muy buenos para ellos, pero no aprendiendo a
generalizar de manera correcta y produciendo datos de salida poco eficientes
ante datos no vistos durante el entrenamiento.
Para evitar el overfitting existen diferentes métodos y técnicas a la hora de en-
trenar, como por ejemplo:
Dropout: Permite a la red eliminar o desactivar de manera aleatoria una
cantidaddeterminadadeneuronasencadapaso,paraevitarelsobreapren-
dizaje de unas neuronas sobre otras.
Early Stopping: Es una técnica que detecta cuando se puede empezar a
producir overfitting, y detiene el entrenamiento antes de que se produzca.
Aumentar la cantidad de datos de entrenamiento:Enocasiones,elover-
fitting es producido por la falta de datos que se le aportan a la red para
entrenar, por lo que aumentar esta cantidad puede evitarlo.
14
--- PAGE 23 ---
Fundamentos del Deep Learning
Figura 3.5: Figura que representa el overfitting.
3.5.2. Underfitting
Este problema, menos común que el overfitting, tiene un comportamiento com-
pletamente contrario al mismo.
El underfitting se produce cuando la red no es capaz de producir datos de sa-
lida adecuados en fase de entrenamiento, mostrando una incapacidad para el
aprendizaje.
Para evitar el underfitting existen diferentes métodos y técnicas a la hora de
entrenar, como por ejemplo:
Disminuir técnicas de regularización: Técnicas como el Dropout pueden
tener un impacto negativo en la habilidad de la red para identificar los
patrones dentro de los datos.
Añadir capas extras: En ocasiones, si disponemos de una red demasiado
simple,esposiblequenoseacapazdeaprender.Añadirmáscapassignifica
añadir más neuronas, por lo que la red puede aprender más.
Aumentar el tiempo de entrenamiento: Muchas veces disponemos de un
modeloderedneuronalsuficientementebuenoparalatareaqueleestamos
encargando. Sin embargo, si no le damos tiempo suficiente para entrenar,
es posible que observemos underfitting. Aumentar el tiempo de entrena-
miento suele ser la técnica más efectiva para combatir este problema
Figura 3.6: Figura que representa el Underfitting.
15
--- PAGE 24 ---
3.5. Problemas a resolver
3.5.3. Problema del Vanishing Gradient
El gradiente [21] es un valor numérico resultado de la evolución de la función
de coste en el tiempo. Generalmente, es utilizado por diferentes optimizadores
para calcular los nuevos pesos en la siguiente iteración de la red neuronal.
Cuando el gradiente tiende a un valor muy pequeño (fenómeno conocido como
vanishing gradient), la técnica de backpropagation pierde eficacia, puesto que
no se puede calcular de manera efectiva el valor por el cual los pesos deben de
ser ajustados en el siguiente paso. Este problema es bastante común cuando
se utilizan funciones de activación como la Sigmoidal o la Tangente Hiperbólica.
Sin embargo, otras funciones como la RelU minimizan estos casos.
Unmalcálculodepesostieneunimpactodirectoenlascapacidadesdeaprendi-
zaje y, por tanto, generalización de una red neuronal, por lo que es fundamental
minimizarlo.
16
--- PAGE 25 ---
Capítulo 4
Fundamentos del Ajedrez por
Computadora
4.1. Estado del Arte
Hoy en día existen muchos ejemplos de motores de ajedrez capaces de jugar
de manera muy eficiente, sin embargo, cuando se menciona el término ajedrez
por computadora, inmediatamente vienen a la cabeza los nombres Stockfish y
AlphaZero.
Estos motores destacan sobre los otros por su capacidad superior a la hora de
la toma de decisiones dentro del juego, lo que los ha situado como los mejores
dentro de la disciplina.
Diversos motores de ajedrez, entre ellos Stockfish, compiten contra muchos
otros motores en el torneo anual Top Chess Engine Championship (TCEC),
una competición en la cuál se determina el mejor en ese año en concreto [22].
4.1.1. Stockfish
*******. Origen
Stockfish es un motor de ajedrez de código abierto originalmente creado por
Tord Romstad, Marco Costalba y Joona Kiiski enel año 2008. Enla actualidad
cuenta con una comunidad entera que realiza aportaciones para la creación de
nuevas versiones mejoradas del motor.
El origen de Stockfish se remonta al proyecto Glaurung, desarrollado en el año
2004 por Tord Romstad, y que posteriormente fue ampliado por Marco Costalba,
dando origen a Stockfish 1.0.
Eventualmente el proyecto Glaurung se deshechó, centrando todos los esfuer-
zos en la mejora contínua de Stockfish, el cual ya por aquel entonces recibía
actualizaciones mensuales.
17
--- PAGE 26 ---
4.1. Estado del Arte
*******. Características
La principal característica de Stockfish, y la razón por la que tiene la potencia
necesaria para ser el mejor motor de ajedrez, es su algoritmo de búsqueda de
movimientos, que recorre el árbol de juego con una rápidez que no tiene pa-
rangón. A su vez, su púnto debil (comparado con AlphaZero) es su función de
evaluación.
El algoritmo utilizado por Stockfish es una versión altamente optimizada
de Minimax, que recibe el nombre de Principal Variation Search (PVS) o
Negascout. Sin entrar en demasiado detalle, el algoritmo es una versión de
Minimax que utiliza una estrategia de búsqueda que consiste en explorar
todas las posibles variaciones como consecuencia de un movimiento, pero
descartando aquellas que se tiene la certeza de que no van a dar un buen
resultado.
Antes del año 2020, Stockfish utilizaba una función de evaluación clásica,
generada a partir de la recopliación de conceptos avanzados de ajedrez,
desarrollados por expertos y probada en fishtest (plataforma de testing de
Stockfish). Sin embargo, tras la aparición de AlphaZero y la revolución que
supuso el uso de redes neuronales como función de evaluación, se lanzó
Stockfish 12 en el año 2020, el cual implementaba una red neuronal basa-
da en NNUE (Funciones de Evaluación basadas en Redes Neuronales de
forma eficiente).
*******. Actualidad
En el momento de la realización de este Trabajo de Fin de Grado, la versión
estable más actualizada de Stockfish es Stockfish 15 (lanzada el 18−04−2022),
la cual sigue utilizando la red neuronal como función de evaluación, puesto
que ha resultado ser sustancialmente más eficiente que el enfoque más clásico,
según los propios desarrolladores del motor.
4.1.2. AlphaZero
4.1.2.1. Origen
Enelaño2017,laempresabritánicaDeepMind(pertenecienteaGoogle)lanzósu
primera versión del motor AlphaZero. El motor, compuesto por una compleja red
neuronal, fue capaz de ganar de manera notable a Stockfish, que por entonces
era ya el rey del ajedrez por computadora.
En ese primer enfrentamiento entre el novato motor y Stockfish, AlphaZero fue
capaz de ganar 28 partidas y empatar otras 72, dando un duro golpe en la mesa
y posicionándose como claro aspirante a convertirse en el motor más fuerte de
todos [23].
18
--- PAGE 27 ---
Fundamentos del Ajedrez por Computadora
*******. Características
La mayor virtud de AlphaZero, justamente al contrario que Stockfish, es su fun-
ción de evaluación, mientras que su púnto debil (comparado con Stockfish) es su
alrogitmo de búsqueda. Desde que fue presentado al mundo en el año 2014, la
red neuronal que está detrás del éxito del motor [24] ha sido lo que le ha llevado
a ser elegido como el mejor en diferentes años.
La red neuronal pertenece a un tipo específico de redes neuronales, deno-
minadas redes convolucionales, que utiliza una metodología de aprendi-
zaje muy novedosa en el campo, llamada aprendizaje por refuerzo. Este
tipo de aprendizaje se caracteriza por tener un funcionamiento en el cual
se le da una recompensa a la red cuando toma una buena decisión, de tal
manera que vaya mejorando su toma de decisiones. Debido al uso de es-
te tipo de aprendizaje, AlphaZero es capaz de mejora de manera contínua
jugando contra sí mismo.
Encuantoalalgoritmodebúsqueda,AlphaZeroutilizaelMonteCarloTree
Search [25], el cual es mucho menos efectivo que el algoritmo de búsqueda
de Stockfish en cuanto a posiciones analizadas por segundo se refiere. A
pesardeello,sufuncióndeevaluaciónestátandesarrolladaqueesamenor
efectividad no supone un problema demasiado grande para el motor.
*******. Actualidad
En la actualidad, AlphaZero sigue recibiendo actualizaciones por parte del equi-
po de DeepMind, pero puesto que no es un proyecto de código abierto, no se
tiene acceso a esas mejoras en forma de lanzamiento de versiones públicas.
Una alternativa a AlphaZero, con un funcionamiento similar, es el proyecto de
código abierto desarrollado por la comunidad: Leela Chess Zero.
4.2. Árbol de Juego
Elárboldejuegoesunarepresentacióndelosposiblesestadosfuturosdeljuego,
partiendo de la posición actual, la cual es el nodo raíz del árbol.
Una vez se ha generado el árbol, se recorre el mismo mediante el algoritmo de
búsqueda, aplicando la misma función de evaluación en cada estado, pudiendo
así determinar qué jugada llevará a un mayor beneficio futuro para el jugador.
4.3. Algoritmo de Búsqueda
Los Algoritmos de Búsqueda son una de las dos partes fundamentales del Aje-
drez por computadora, pues sin ellos no seríamos capaces de ver qué jugadas
resultan de nuestros posibles movimientos, y evaluar dichas jugadas en conse-
cuencia.
19
--- PAGE 28 ---
4.3. Algoritmo de Búsqueda
Figura 4.1: Árbol de juego de ajedrez.
La mayoría (si no todos) los motores de ajedrez emplean algún algoritmo de bús-
queda para encontrar la mejor jugada posible. Aunque las técnicas a la hora de
la realización de estos algoritmos difiere, todos siguen un mismo concepto: obte-
ner todas las posibles variaciones a partir de una posición y elegir el movimiento
que lleve a la posición futura más ventajosa.
4.3.1. Minimax
Como ya hemos visto antes, el ajedrez se puede categorizar como un juego en
el que los jugadores tienen intereses opuestos. Esto hace que la ventaja de uno
pueda verse como la desventaja del otro de tal forma que si el jugador A tiene
una ventaja hipotética de +50, el jugador B tiene una desventaja de −50.
El algoritmo de búsqueda Minimax [26][27] utiliza este principio para llevar a
cabo la búsqueda de la posición más favorable para un jugador en un momento
determinado, mediante la minimización (mini) de la ventaja del oponente y ma-
ximización (max) de la ventaja del jugador, obtenida de la función de evaluación.
Hay que tener en cuenta que mediante la minimización de la ventaja del opo-
nente, estamos maximizando nuestra ventaja de manera directa, según los con-
ceptos anteriormente explicados.
Para ello, se evalúan tantas jugadas futuras como se le indique, mediante el
parámetro profundidad. Un valor de profundidad X significa que se evalúan las
posiciones derivadas del tablero actual, hasta aquellas X turnos en el futuro.
El algoritmo empieza maximizando la ventaja del jugador, y luego minimizando
la ventaja del oponente, y va realizando esta operación de manera intercala-
da hasta que se alcanza la profundidad máxima establecida. Por lo tanto, si
tenemos una profundidad de 5 capas, habrá 3 fases de maximización y 2 de
minimización, como se observa en la siguiente imagen:
20
--- PAGE 29 ---
Fundamentos del Ajedrez por Computadora
Figura 4.2: Figura que representa el funcionamiento de Minimax a profundidad
5
El pseudo-código del algoritmo Minimax se muestra a continuación:
function MINIMAX(N) is
begin
if N is deep enough then
return the estimated score of this leaf
else
Let N1, N2, .., Nm be the successors of N;
if N is a Min node then
return min{MINIMAX(N1), .., MINIMAX(Nm)}
else
return max{MINIMAX(N1), .., MINIMAX(Nm)}
end MINIMAX;
4.3.2. Optimización Alpha-Beta
La optimización Alpha-Beta [28] es una técnica utilizada en el algoritmo de bús-
quedaMinimaxquepermitereducirelnúmerodeposicionesevaluadas,median-
te el sesgo de aquellas que se sabe que no van a mejorar la posición actual.
Parapoderdeterminarsiuncaminoenelárboldejuegovaasuponerunamejora
en la ventaja del jugador o no, es necesario tener unos límites superiores (alpha)
e inferiores (beta) con los que comparar. Por ello, cada vez que se encuentra un
máximo, se guarda ese valor en el límite superior, y cada vez que se encuentra
un mínimo, se guarda el valor en el límite inferior. Así, cuando el algoritmo esté
recorriendo el árbol, podrá evitar la evaluación de posiciones que previamente
se sabía que no iban a suponer una mejora.
El resultado finaldel algoritmo es el mismo quecon Minimax clásico, pero conla
optimizaciónenrendimientoquesuponeelnotenerquemirartodaslasposibles
jugadas, sino sólo aquellas que están dentro del límite inferior y superior.
21
--- PAGE 30 ---
4.3. Algoritmo de Búsqueda
Figura 4.3: Figura que representa el funcionamiento de Minimax con la optimi-
zación Alpha-Beta
Elpseudo-códigodelalgoritmoMinimaxconlaoptimizaciónAlpha-Betasemues-
tra a continuación:
function minimax(node, depth, alpha, beta, maximizingPlayer) is
if depth ==0 or node is a terminal node then
return evaluation of node
if MaximizingPlayer then
maxEval = -infinity
for each child of node do
eval = minimax(child, depth-1, alpha, beta, False)
maxEval = max(maxEval, eval)
alpha = max(alpha, maxEval)
if beta<=alpha
break
return maxEval
else
minEval = +infinity
for each child of node do
eval = minimax(child, depth-1, alpha, beta, true)
minEval = min(minEval, eval)
beta = min(beta, eval)
if beta<=alpha
break
return minEval
Como se puede observar, se está guardando un máximo (maxEval) en la fase de
maximización y un mínimo (minEval) en la fase de minimización. Estos valores
representan el valor más alto alcanzado en la fase de maximización (mayor ven-
22
--- PAGE 31 ---
Fundamentos del Ajedrez por Computadora
taja)yelvalormásbajoalcanzadoenlafasedeminimización(menordesventaja)
hasta el momento. Cuando una posición es evaluada se compara con el máximo
o mínimo asegurado y en el caso de no mejorar ese valor, se deja de recorrer ese
camino en el árbol de juego. En el caso en el que la posición actual sea mejor
que los valores guardados hasta entonces, se establecería esa posición como el
nuevo mejor valor asegurado.
4.3.3. Stockfish: Principal Variation Search (PVS)
ElalgoritmodebúsquedaPVS[29]esunaoptimizacióndelalgoritmoalpha-beta,
por lo que al final del día sigue siendo una implementación Minimax basada en
la definición de juego de suma cero.
El funcionamiento de PVS sigue la idea fundamental de que no es necesario
evaluar todas las posiciones derivadas del nodo raíz del árbol de juego, si no solo
analizar tantas posiciones como sea necesario para determinar que ese camino
era peor que el que ya se había establecido como mejor.
Para ello, previo al propio PVS, se calcula el camino que se cree que va a ser el
mejor mediante un algoritmo conocido como Iterative deepening.
Iterative deepening (ID) [30] es un algoritmo ejecutado antes de PVS que sirve
para determinar qué camino se va a establecer como el mejor de manera inicial
cuando se de comienzo a la búsqueda del mejor movimiento posible. Para ello,
ID realiza una búsqueda por el árbol de juego hasta que se agote el tiempo
establecido para esa búsqueda (normalmente definido por la profundidad de
la propia búsqueda), dando como resultado uno de todos los posibles caminos
iniciales, que será considerado como el mejor en la primera iteración de PVS.
Una vez se ha establecido el mejor camino con ID, PVS hace una búsqueda pro-
funda en dicho camino para encontrar el mejor movimiento, y una búsqueda
muy superficial en todos los demás, buscando confirmar que eran caminos me-
nos ventajosos. En el caso en el que se determine que uno de los caminos en
los cuales se realizó una búsqueda superficial resultó ser un mejor camino, se
vuelve a recorrer ese camino de manera más exhaustiva y se establece como
mejor camino actual para la siguiente iteración del algoritmo.
A la búsqueda de confirmación de que un camino era realmente peor que el ya
establecido por Iterative Deepening, utilizada en otros algoritmos de búsqueda
como Scout, NegaScout o MTD(f), es conocida como Null Window [31].
El pseudo-código del algoritmo PVS se muestra a continuación:
function pvSearch( int alpha, int beta, int depth ) {
if(depth == 0) return quiesce(alpha, beta);
bool bSearchPv = true;
for (all moves){
make
if (bSearchPv){
score = -pvSearch(-beta, -alpha, depth - 1);
} else {
23
--- PAGE 32 ---
4.3. Algoritmo de Búsqueda
score = -pvSearch(-alpha-1, -alpha, depth - 1);
if ( score > alpha ) // in fail-soft ... && score < beta ) is common
score = -pvSearch(-beta, -alpha, depth - 1); // re-search
}
unmake
if(score >= beta)
return beta; // fail-hard beta-cutoff
if(score > alpha) {
alpha = score; // alpha acts like max in MiniMax
bSearchPv = false; // *1)
}
}
return alpha; // fail-hard
}
Figura 4.4: Figura que representa el funcionamiento de Iterative Deepening.
4.3.4. AlphaZero: Monte Carlo Tree Search (MCTS)
El algoritmo de búsqueda MCTS [25] se basa en la simulación de posibles parti-
das derivadas de un movimiento elegido arbitrariamente en el árbol de juego, de
tal manera que al final se elija seguir el camino simulado que nos lleve al mejor
resultado final. Para ello, el algoritmo se divide en 4 fases:
1. Selection: En esta fase, el algoritmo viaja desde el nodo raíz del árbol de
juego hasta un nodo hoja que aún no se ha añadido al árbol, de manera
completamente arbitraria.
2. Expansion: Una vez se ha elegido el nodo hoja, se añade al árbol de juego.
3. Simulation: En esta fase, se juega hasta el final de la partida, obteniendo
24
--- PAGE 33 ---
Fundamentos del Ajedrez por Computadora
un resultado de victoria, derrota o empate.
4. Backpropagation: Según los resultados de la fase anterior, se toma la de-
cisión que nos llevará a una posición más ventajosa (preferiblemente la
victoria).
Figura 4.5: Figura que representa el funcionamiento de MCTS.
Elpseudo-codigodeMCTS,paraunaúnicaiteración,semuestraacontinuación:
function MCTS(node, num_rollout):
path = select(node)
leaf = path[-1]
expand(leaf)
reward = 0
for i in range(num_rollout):
reward += simulate(leaf)
backup(path, reward)
4.3.5. Diferencias entre PVS y MCTS
Como podemos observar viendo ambos algoritmos de búsqueda, se trata de dos
enfoques completamente diferentes. Mientras que PVS se basa en intentar eva-
luar el mayor número de posiciones posibles, MCTS necesita una función de
evaluación muy especializada, puesto que por su funcionamiento el rendimien-
to va a ser bastamente inferior al de PVS, en cuanto a posiciones evaluadas se
refiere, y es necesario asegurarse de que las posiciones que se evalúen lo sean
de manera muy precisa.
Ambosalgoritmostienensuusoendistintoscontextos,yporlotanto,tienensus
ventajas y desventajas. En el caso de la elección del algoritmo de búsqueda para
este Trabajo de Fin de Grado, se ha optado por la elección de Alpha-Beta, muy
cercano a PVS, debido a su sencilla implementación y a que es un algoritmo que
representa de manera excelente el problema que se quiere resolver con este tipo
de juegos de suma cero.
25
--- PAGE 34 ---
4.4. Función de evaluación
4.4. Función de evaluación
La función de evaluación es una heurística utilizada por las computadoras que
juegan a juegos de estrategia y que determina la calidad de la posición actual
del jugador, para así averiguar como proceder en el siguiente turno. Es gracias
a esta heurística que las computadoras pueden decidir qué decisión tomar. Para
poder puntuar diferentes posiciones a partir de la actual, se evalúan diferentes
futuras posibles posiciones mediante la generación del árbol de juego. La otra
parte que complementa a los algoritmos de búsqueda en el ajedrez por compu-
tadora y permite la creación de programas que jueguen de manera autónoma al
juego, es la función de evaluación.
La función de evaluación es la que nos permite entender cómo de ventajosa,
o desventajosa, es la posición en la que nos encontramos en un punto deter-
minado de la partida, para así poder tomar decisiones de manera consecuente.
Históricamente hablando, ha habido diferentes formas de evaluar una posición
de ajedrez, desde evaluar en función del material presente en el tablero, a eva-
luar según los estudios de diferentes momentos de la partida (inicios, finales...)
hasta la más novedosa, la evaluación mediante el uso de redes neuronales.
Generalmente hablando, cuanto mejor seamos capaz de evaluar una posición,
mejoresdecisionestomaremos,porloquepodríamosdecirquelafuncióndeeva-
luación es la parte más importante de las dos fundamentales ya mencionadas.
Esta afirmación se puede comprobar con el caso específico del motor AlphaZe-
ro, el cual a pesar de tener un algoritmo de búsqueda severamente más lento
que otros motores de ajedrez, la superioridad de su función de evaluación lo ha
llevado a ser todo un pilar dentro del ámbito del ajedrez por computadora.
4.4.1. Enfoque clásico
Dentro de los enfoques clásicos, hay diferentes estrategias utilizadas para deter-
minar el valor de una posición:
Evaluación a partir del estudio de partidas por parte de profesionales
del ajedrez:
Siendo el ajedrez un juego tan antiguo, hay muchas situaciones dentro de
una partida que han sido objeto de estudio minucioso por parte de nume-
rosos profesionales del juego. Cuando nos encontramos en una de estas
situaciones, existen los llamados movimientos de libro: movimientos que se
han demostrado como los mejores en esas situaciones.
Esto es algo sumamente importante dentro del juego, hasta tal punto que
los jugadores profesionales suelen conocer muchos movimientos de memo-
ria, como es el caso de las diferentes aperturas o finales de partidas.
En el caso de los motores de ajedrez, el poder contar con unos movimientos
que se sabe con certeza que van a ser los mejores supone el poder omitir
la evaluación de miles de combinaciones posibles dadas por el algoritmo
de búsqueda, y realizar un movimiento de manera inmediata cada vez que
26
--- PAGE 35 ---
Fundamentos del Ajedrez por Computadora
se detecta una de estas situaciones con la certeza de que se ha tomado la
mejor decisión.
Evaluación en función del material presente:
En cuanto a la evaluación en base al material presente en el tablero en un
momento determinado, se podría establecer como la forma más sencilla de
determinar la ventaja de un jugador en ajedrez. Para determinar la situa-
ción actual, bastaría con contabilizar el número de piezas en el tablero, y
realizar una suma del valor de estas piezas para saber qué jugador tiene la
ventaja.
Sobre la manera más tradicional de obtener esta evaluación, se realiza me-
diante la asignación de un valor estático a cada pieza, que varía en función
de la importancia de dicha pieza, para poder determinar la suma ya men-
cionada.
Sin embargo, y gracias a la primera técnica del estudio de partidas de aje-
drez, hay una forma un poco más avanzada de contabilizar el material
presente en el tablero, mediante la asignación dinámica de valores a cada
pieza:
Figura 4.6: Figura que representa el valor de cada pieza en función de su posi-
ción.
27
--- PAGE 36 ---
4.4. Función de evaluación
4.4.2. Redes neuronales como función de evaluación
Como ya hemos visto antes, una red neuronal es un sistema de procesamiento
computacional altamente inspirado en el funcionamiento del cerebro humano.
Las redes neuronales están compuestas por un gran número de nodos compu-
tacionales interconectados entre sí, normalmente llamados neuronas, los cuales
trabajan de manera conjunta para, ante un dato de entrada, generar un dato de
salida. Estas neuronas están divididas en diferentes capas:
Capa de entrada: Son las neuronas que reciben los datos de entrada.
Capa(s) oculta(s): Son las neuronas que se encargan de procesar los datos
de entrada.
Capa de salida: Son las neuronas que generan los datos de salida.
Las redes neuronales son capaces de generar datos de salida gracias a una serie
de funciones matemáticas las cuales son las encargadas de ajustar la impor-
tancia de cada neurona (peso) para generar dicha salida. Sin embargo, para que
este proceso sea fiable, es necesario que la red sea capaz de entrenar, con el
objetivo de obtener los patrones necesarios para que los pesos de cada neurona
sean calculados de manera correcta.
Elsurgimientodelasredesneuronalescomofuncionesdeevaluaciónhadividido
a los actuales motores de ajedrez en 2 categorías:
Motores de ajedrez Alpha-Beta (A/B): Motores que buscan evaluar el ma-
yor número de posiciones posibles.
Motores de ajedrez enfocados en la heurística: Motores que evalúan me-
nos posiciones, pero de manera más precisa.
Sin embargo, hay casos de motores híbridos, que utilizan algoritmos de búsque-
da de fuerza bruta, como Alpha-Beta, pero que utilizan redes neuronales como
funciones de evaluación, siendo el caso más conocido el del motor Stockfish.
4.4.3. Stockfish: Redes NNUE
Previoallanzamientodelaversión12,elmotordeajedrezStockfishutilizabauna
función de evaluación basada en la recopilación de movimientos derivados del
estudio de una gran cantidad de partidas de ajedrez por parte de profesionales,
además de otros conceptos avanzados del juego, como:
Balance de material: Número de piezas de cada jugador.
Amenazas entrantes: Piezas que son atacadas, piezas que se quedan col-
gadas, etc.
Ventaja de posición: Piezas en buenas posiciones, como una dama con-
traria expuesta, ataques a piezas enemigas, etc.
Seguridad del rey: Si hay posible jaque, si está seguro tras otras piezas,
etc.
28
--- PAGE 37 ---
Fundamentos del Ajedrez por Computadora
Sin embargo, tras el nacimiento y victoria de AlphaZero ante Stockfish, la comu-
nidad se dio cuenta del potencial de utilizar una red neuronal para evaluar las
posiciones generadas por el algoritmo de búsqueda.
Por ello, se comenzó a trabajar en una red neuronal propia capaz de evaluar es-
tos movimientos, específicamente una red que implementara el concepto NNUE
[32]. Las Funciones de Evaluación basadas en Redes Neuronales de forma efi-
ciente (NNUE), son un nuevo tipo de función de evaluación no lineal, creadas
originalmente para el juego de estrategia japonés Sho¯gi.
Estetipodefuncionesdeevaluaciónestánpensadasparatenerunagraneficien-
ciaenlaCPUdelordenador,medianteelusodedistintastécnicasdeaceleración,
como el cálculo incremental.
En cuanto a la red neuronal creada para Stockfish, se trata de una red neuronal
muy sencilla, compuesta por 1 capa de entrada, 2 capas intermedias u ocultas
y 1 capa de salida. La red neuronal tiene opción de ser entrenada mediante el
uso de aprendizaje supervisado o no supervisado. En el caso concreto del en-
trenamiento de la red de Stockfish, se optó por utilizar datasets de movimientos
aleatorios generados por la antigua función de evaluación, por lo que se trata de
un entrenamiento utilizando la ténica de aprendizaje supervisado.
Figura 4.7: Figura que representa la red neuronal de Stockfish.
Sobre el rendimiento de la red con respecto a la función de evaluación previa,
fue capaz de superarla por 100 puntos de ELO, lo que supone un gran aumento
en el nivel, ya que la función de evaluación anterior ya se encontraba en un ELO
150 puntos superiores a la versión que fue derrotada ante AlphaZero.
29
--- PAGE 38 ---
4.4. Función de evaluación
Figura 4.8: Figura que representa la diferencia entre Stockfish 12 y Stockfish
11.
4.4.4. AlphaZero: Redes Neuronales Convolucionales (CNN)
Las redes neuronales convolucionales (CNN) [33] son un tipo específico de red
neuronal generalmente usada en el ámbito del reconocimiento de imágenes. De-
bido a su inclinación a este tipo de problemas, las CNN tienen una estructura
ligeramente diferente a la de una red neuronal tradicional, aunque su funciona-
miento final sigue siendo el mismo.
Figura 4.9: Figura que representa una CNN.
Las redes convolucionales tienen un proceso especial llamado convolución que
se encarga de, para cada entrada de la red, realizar una serie de operaciones
30
--- PAGE 39 ---
Fundamentos del Ajedrez por Computadora
vectoriales contra una matriz denominada Kernel. Cada imagen es dividida en
una diferente cantidad de píxeles, por lo que realmente este proceso de convo-
lución aplicará una serie de operaciones vectoriales a cada uno de los píxeles,
denominadas filtro.
Figura 4.10: Figura que representa un filtro sobre una imagen.
Otra de las características de las CNN, es la operación conocida como subsam-
pling.Estaoperación,llevadaacaboporlacapapooling,seencargadereducirel
número de parámetros de activación de la red, mediante una de las 2 siguientes
técnicas:
Max Pooling
Average Pooling
Ambas ténicas se encargan de reducir el número de parámetros de activación
de la red para la siguiente convolución, pero eligiendo diferentes valores de la
matriz resultado de la operación. El max pooling elige el valor más alto dentro
de la submatriz resultado, mientras que el average pooling hace una media
aritmética de todos los valores de dicha submatriz, como se puede apreciar en
la siguiente imagen:
Figura 4.11: Figura que representa el subsampling de una CNN.
31
--- PAGE 40 ---
4.4. Función de evaluación
Una vez el proceso de convolución ha finalizado, los resultados de la operación
son aplanados y envíados a las capas ocultas, que funcionan exactamente igual
que en una red tradicional. Estas capas serán las encargadas de calcular los
pesos que darán lugar al resultado final de la red.
AlphaZero utiliza una red neuronal convolucional basada en ResNet 50 [34], la
cual se divide en dos ramas:
Posibles movimientos derivados del algoritmo de búsqueda Monte-Carlo
Tree Search (MCTS).
Valor que representa el resultado de las partidas simuladas con esos movi-
mientos.
Figura 4.12: Figura que representa la red neuronal de AlphaZero.
Como ya se ha mencionado anteriormente, la red neuronal fue entrenada utili-
zando la ténica de aprendizaje no supervisado. Para ello, AlphaZero jugó contra
32
--- PAGE 41 ---
Fundamentos del Ajedrez por Computadora
sí misma de manera reiterada, con las reglas del ajedrez como única informa-
ción. Según el equipo de DeepMind, la red fue capaz de superar a Stockfish en
tan solo 4 horas de juego, y alcanzar un nivel superior al humano tras 24 horas
[23].
La comunidad del ajedrez por computadora recibió este nuevo enfoque como
un soplo de aire fresco, puesto que se distancia enormemente del enfoque de
fuerza bruta que tanto caracteriza a la mayoría de algoritmos, como Alpha-Beta,
y en cambio ofrece una solución mucho más enfocada en el apartado del Deep
Learning.
33
--- PAGE 43 ---
Capítulo 5
Desarrollo
5.1. Datasets
5.1.1. Elección de Datasets
En el caso concreto de la red neuronal desarrollada en este Trabajo de Fin de
Grado, necesitamos un conjunto de datos capaz de relacionar de alguna forma
una situación de juego de ajedrez con una valoración de la misma.
Para ello, contamos con un dataset que contiene un conjunto de datos muy
similar al utilizado por Stockfish para entrenar su red neuronal NNUE, más
concretamente, se trata de valoraciones de partidas por parte de Stockfish a
una profundidad 22 en el árbol de juego. Este dataset es un archivo csv (comma
separated values) que contiene 2 columnas, la primera representa el tablero de
juego actual en una notación especial (FEN) [35], y la segunda representa la
evaluación que Stockfish concede a dicho tablero.
Este dataset cuenta con un total de 12958035 datos, lo cual tiene tanto ventajas
como desventajas a la hora de crear la red neuronal:
La parte positiva de contar con esta gran cantidad de datos es que, con
un entrenamiento suficiente, la red tendrá un muy buen funcionamiento
puesto que ha sido capaz de aprender con una cantidad de datos suficien-
temente grande como para poder haber abarcado muchas posibilidades, lo
que se traduciría en menos incertidumbre ante un tablero inusual al cual
la red no esté acostumbrado.
La parte negativa es la necesidad de una gran capacidad computacional
parapoderentrenarredesneuronalescondatasetsdetamañostangrandes
como este, lo cual supone un problema debido a la escala del trabajo que
se está realizando.
5.1.2. Análisis de datos
La notación de Forsyth-Edwards (FEN) [35] es un tipo de notación de ajedrez,
utilizado para representar una posición, que sigue las siguientes reglas:
35
--- PAGE 44 ---
5.1. Datasets
1. El tablero se lee de izquierda a derecha y de arriba abajo.
2. Las piezas blancas se nombran en mayúsculas y las piezas negras se nom-
bran en minúsculas (con las iniciales de su nombre en inglés).
3. La posición se indica a partir de la primera fila de las negras, escribiendo
cada elemento de izquierda a derecha e incluyendo el número de casillas
vacías.
4. Cada fila se separa de las demás mediante una barra (/).
Además de la representación del tablero, la notación FEN cuenta con informa-
ción extra, como:
Turno de juego (blancas: w o negras: b).
Disponibilidad de enroque: K si blancas puederealizar enroque en elflanco
del rey, Q si blancas puede realizar enroque en el flanco de dama, e igual
para negras, pero representado con k y q, respectivamente
Posible captura al paso por parte de algún peon, o - si no hay captura al
paso.
El número de medios movimientos desde la última captura o avance de un
peón, para la regla de los 50 movimientos.
El número de movimientos completos, empezando en 1 y avanzando desde
el primer movimiento de negras (movimiento 2 de la partida).
Figura 5.1: Figura que representa el tablero inicial en notación FEN.
En la imágen superior podemos apreciar:
El tablero: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR.
El turno actual es w, por lo que es el turno de blancas.
36
--- PAGE 45 ---
Desarrollo
Ambos jugadores tienen ambos flancos de enroque disponibles.
El número de medios movimientos es 0.
El número de movimientos completos es 1.
En cuanto a las evaluaciones contenidas en el dataset, están representadas en
Centipawns. Este tipo de forma de representar una evaluación es común en
el ajedrez por computadora, y supone el 1/100 valor de un peón, es decir, 100
centipawns equivalen a 1 peón.
Estas evaluaciones pueden ser tanto positivas, lo cual indica ventaja por parte
del jugador sobre su oponente, como negativas, lo cual indica justo lo contrario.
Además, algunas evaluaciones tienen anexionadas una almohadilla (#), lo cual
representa un jaque mate forzado. Estas evaluaciones están agrupadas de la
siguiente forma:
Figura 5.2: Figura que representa la distribución de las evaluaciones del Data-
set.
Comopodemosapreciar,apesardequelosdatosestanbastanteagrupadosenel
centro, hay bastantes datos dispersos en los intervalos [5000,15000] y [-5000,-
15000], lo cual influye de manera muy directa en la media de las evaluaciones,
la cual cae hasta 44.85 centipawns.
Esta disparidad entre valores puede suponer que la red neuronal no sea capaz
de aprender a evaluar un tablero, si no que se limite a generalizar en valores
muy pequeños, cercanos al 0, por lo cual es necesario tratar estos datos para
evitar que eso suceda.
37
--- PAGE 46 ---
5.1. Datasets
Los datos acabamos de visualizar suponen una representación del dataset origi-
nal, sin embargo, como ya se ha comentado y debido a la escala de este trabajo,
se ha optado por descartar todos aquellos tableros que contengan más de 8 pie-
zas, por lo cual tanto el tamaño final del dataset utilizado como la agrupación
de los datos varía ligeramente.
El nuevo número de evaluaciones totales es de 615366, lo cual supone una re-
ducción significante con respecto al dataset original. Aún así, estamos ante un
dataset inusualmente grande, en cuanto a tamaño estándar de dataset utilizado
para el entrenamiento de una red neuronal se refiere.
Al igual que pasaba con el dataset original, tenemos el mismo problema de dis-
persión de datos que puede llevar a una red neuronal que se limite a generar
valores muy bajos, aunque la media se ha incrementado ligeramente hasta al-
canzar un valor de 61.36.
La nueva distribución viene representada en el siguiente histograma:
Figura 5.3: Figura que representa la distribución de las evaluaciones del Data-
set.
5.1.3. Transformación de datos
Para poder desarrollar un modelo de aprendizaje que sea capaz de llegar a gene-
ralizar una valoración de un tablero, hemos de solucionar el problema anterior-
mente comentado. Para ello, limitaremos el valor de las evaluaciones del dataset
original a, como valor máximo (independientemente del signo) , el percentil 95
de todos los datos. El percentil 95 representa el valor por el cual debajo están el
95% de todos los datos. En nuestro dataset, el valor percentil 95 es 5373.
38
--- PAGE 47 ---
Desarrollo
Es necesario resaltar que, debido a que el dataset contiene datos de tableros
cuyavaloraciónpertenecetantoaljugadordenegrascomoaljugadordeblancas,
es necesario escoger uno de los dos casos, puesto que tenemos que ser capaces
de saber sobre qué jugador será la valoracion que nos llegue como salida de la
red neuronal. Además, gracias a la teoría de Minimax, podemos establecer la
evaluación de un jugador como la contraria a la evaluación del oponente.
Además de limitar los valores que superen el percentil 95, también establece-
remos los tableros que vienen marcados como jaque mate (#) como el máximo
valor disponible. De esta manera, las evaluaciones que tengan un #, serán sus-
tituidas por el percentil 95 con el signo correspondiente. Esta transformación es
debido a que queremos que nuestra red detecte el jaque mate positivo como un
tablero muy positivo para nosotros, mientras que el jaque mate negativo repre-
sentará un tablero muy positivo para el enemigo, y por tanto muy negativo para
nosotros.
Figura 5.4: Figura que representa los límites de las evaluaciones del Dataset.
Una vez hemos tratado el problema que supone la dispersión de datos, vamos
a transformar los datos de entrada de la red neuronal de tal manera que sean
datos con los que se pueda entrenar.
Primero transformaremos la representacion FEN del tablero, que originalmente
es un String, a un Tensor que represente ese mismo tablero. El tensor es la es-
tructura de datos especial de PyTorch, la cual está optimizada para poder ser
utilizada por GPUs, aumentando así enormemente el rendimiento del entrena-
miento de la red.
Existe una forma especial de representar los datos de entrada de una red neu-
39
--- PAGE 48 ---
5.1. Datasets
ronal, llamada One-hot Encoding [36]. Esta representación consiste en trans-
formar los datos de entrada en vectores, de tal manera que nuestros datos del
dataset sean todo ceros, excepto el valor que se quiere representar. En el caso
del ajedrez, al existir 12 piezas diferentes, 6 blancas y 6 negras, cada posición en
el tablero será un vector de 12 posiciones, siguiendo la siguiente forma:
Peón de negras (p): [1.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
Caballo de negras (n): [0, 1.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
Alfil de negras (b): [0, 0, 1.0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
Torre de negras (r): [0, 0, 0, 1.0, 0, 0, 0, 0, 0, 0, 0, 0]
Dama/Reina de negras (q): [0, 0, 0, 0, 1.0, 0, 0, 0, 0, 0, 0, 0]
Rey de negras (k): [0, 0, 0, 0, 0, 1.0, 0, 0, 0, 0, 0, 0]
Peón de blancas (P): [0, 0, 0, 0, 0, 0, 1.0, 0, 0, 0, 0, 0]
Caballo de blancas (N): [0, 0, 0, 0, 0, 0, 0, 1.0, 0, 0, 0, 0]
Alfil de blancas (B): [0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0, 0, 0]
Torre de blancas (R): [0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0, 0]
Dama/Reina de blancas (Q): [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0]
Rey de blancas (K): [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0]
Porlotanto,debidoaqueeltablerodeajedreztieneunadimensiónde8x8ycada
posición está representada por un vector de 12 posiciones, nuestra red tendrá
una entrada de 768 datos, que representarán la posición de cada pieza en el
tablero correspondiente del dataset.
Después de haber definido cómo se van a consumir los tableros por parte de la
red, es necesario explicar qué tipo de transformación es necesario hacer a las
evaluaciones.
Al igual que sucedía con la técnica de one-hot encoding, existen otro tipo de
técnicas de transformación de datos que mejoran el funcionamiento final de
una red neuronal, como sucede con la normalización [37].
La normalización consiste en limitar los valores de los datos a valores en el
intervalo [0,1]. Para conseguir que todos los valores del dataset cumplan la per-
tenencia, se dividen los datos entre el valor máximo.
En nuestro caso, debido a la existencia de valores negativos, vamos a tener un
intervalodepertenenciade[-1,1],paraquelaredpuedaaprenderareconocerlos
tableros negativos, que suponen una desventaja, y no únicamente los positivos.
40
--- PAGE 49 ---
Desarrollo
5.2. Creación de la Red Neuronal
5.2.1. Número de capas
El desarrollo de un modelo de red neuronal es una tarea complicada, debido a
quenoexisteunamaneraperfectaparasucreación.Sinembargo,síqueexisten
diferentes nociones generales sobre cómo actuar en función de qué queremos
conseguir.
Haciendo un resumen de lo visto hasta ahora, y previo a la creación del modelo,
sabemos lo siguiente:
1. Una red neuronal tiene, al menos, 1 capa de entrada y 1 capa de salida.
2. Nuestro tablero está representado por 64 vectores de 12 posiciones, por lo
que tenemos 768 neuronas de entrada.
3. Nos encontramos ante un problema de regresión: Tenemos una única neu-
rona como salida que representa la evaluación, contenida en el intervalo
[−1,1].
Por lo tanto, antes de la creación del modelo, ya sabemos que nuestra capa de
entrada va a contar con 768 neuronas de entrada que representan el tablero, y
nuestra capa de salida va a contar con una única neurona que representa la
evaluación que nuestra red concede a dicho tablero.
A continuación, hemos de llevar a cabo la tarea más complicada a la hora de la
creación de una red neuronal: añadir las capas ocultas.
Debido a problemas como pueden ser el underfitting, hemos de tener mucho
cuidado a la hora de elegir el número de capas ocultas que queremos añadirle a
nuestrared,puestoquetieneimpactodirectoensucapacidaddegeneralización.
A la hora de la adición de capas ocultas, hay diferentes técnicas que se pueden
utilizar:
Pocas capas ocultas, pero que tengan un número muy elevado de neu-
ronas. Este tipo de redes reciben el nombre de Shallow Neural Networks
[38].
Muchas capas ocultas, pero con un número de neuronas más reducido.
Una combinación de ambas técnicas.
En el caso de este modelo, se ha optado por llevar a cabo la adición de un
númeromoderadodecapasocultas,asícomodelnúmerodeneuronasqueestas
contienen. Concretamente, se han introducido 3 capas ocultas, cuyo número
de neuronas es el 50% de la capa anterior, por lo que el modelo final tiene la
siguiente estructura:
Capa de Entrada: 768 neuronas.
Capa Oculta 1: 384 neuronas.
Capa Oculta 2: 192 neuronas.
41
--- PAGE 50 ---
5.2. Creación de la Red Neuronal
Capa Oculta 3: 96 neuronas.
Capa de Salida 1 neurona.
A cada capa del modelo de la red neuronal se le ha añadido un Dropout de 0.8,
es decir, el 80% de las neuronas de cada capa serán eliminadas aleatoriamente
en cada iteración de aprendizaje, para evitar el overfitting del modelo.
5.2.2. Funciones de activación
Como se ha explicado anteriormente, las redes neuronales necesitan de unas
funciones de activación que determinen qué neurona tiene que activarse, con el
objetivo de poder producir la salida más adecuada para los datos de entrada.
Cada capa dentro de una red neuronal puede estar siguiendo una diferente fun-
ción de activación, por lo que es posible la combinación de distintas funciones.
En nuestro caso, se han utilizado 2 funciones de evaluación diferentes: RelU y
Tangente Hiperbólica (Tanh):
Tanh: Debido a que nuestra capa de salida produce datos contenidos en
el intervalo [−1,1], parece bastante evidente el uso de la función de la Tan-
gente Hiperbólica, la cual tiene unos límites superiores e inferiores que
coinciden de manera exacta con los de nuestras evaluaciones.
RelU: Utilizada en el resto de capas, tanto la de entrada como las ocul-
tas, principalmente por su funcionamiento superior en el entrenamiento
de redes neuronales comparado con otras funciones.
42
--- PAGE 51 ---
Capítulo 6
Resultados y conclusiones
6.1. Alternativas a la red propuesta
Además de la red neuronal final que se ha entrenado para este Trabajo de Fin
de Grado, se ha experimentado con otros modelos capaces de evaluar un tablero
de entrada. Inicialmente, la idea era crear una red neuronal capaz de jugar
partidas de ajedrez completas. Sin embargo, debido a la reducida capacidad
computacional, así como el tiempo limitado para la creación de una red que
presentar, finalmente se optó por limitar los tableros a tener,como máximo, 8
piezas presentes en el tablero.
Los modelos alternativos que se intentaron crear previos a la red neuronal final
son los siguientes:
Modelo Capas Error medio
Modelo 1 [768,256,128,1] 0.17
Modelo 2 [768,512,256,1] 0.15
Modelo 3 (Shallow Network) [768,1024,1] 0.21
Cuadro 6.1: Resultados de los modelos alternativos a la red neuronal final
Todas estos modelos alternativos utilizaron las mismas funciones de activación
que el modelo final; RelU en las capas de entrada y ocultas, y Tangente Hiper-
bólica en la capa de salida.
Elmodelo3fueelmodeloqueseintentóutilizarparasercapazdejugarpartidas
enteras, sin embargo, debido a la gran cantidad de datos con los que debía
entrenar, tras 200 epochs alcanzó un error de 0.21, lo cual era bastante alto y no
tenía un desempeño tan bueno como la red final.
6.2. Resultados de los distintos modelos
A continuación se presentan imágenes sobre el entrenamiento de los distintos
modelos de red neuronal que se intentaron desarrollar.
43
--- PAGE 52 ---
6.2. Resultados de los distintos modelos
6.2.1. Modelo 1
Figura 6.1: Figura que representa el aprendizaje del modelo 1.
La figura anterior muestra la curva de aprendizaje del modelo 1, con un error
medio de 0.17.
6.2.2. Modelo 2
Figura 6.2: Figura que representa el aprendizaje del modelo 2.
La figura anterior muestra la curva de aprendizaje del modelo 2, con un error
medio de 0.15.
6.2.3. Modelo 3
Figura 6.3: Figura que representa el aprendizaje del modelo 3.
La figura anterior muestra la curva de aprendizaje del modelo 3, con un error
medio de 0.21.
44
--- PAGE 53 ---
Resultados y conclusiones
6.2.4. Modelo Final
La red neuronal de este Trabajo de Fin de Grado ha sido entrenada durante 200
epochs:
Figura 6.4: Figura que representa el aprendizaje de la red neuronal final.
Como se puede apreciar en la imagen anterior, no hay indicios de que la red
neuronal haya sufrido overfitting, ni underfitting, ya que la curva de aprendi-
zaje muestra un comportamiento casi idéntico sobre los datos de prueba y de
entrenamiento. El error medio final de este modelo es de 0.14.
6.3. Partidas contra Stockfish
Con el objetivo de evaluar la efectividad de la red neuronal creada, se han reali-
zado partidas contra la versión más reciente de Stockfish, Stockfish 15.
Las condiciones de juego fueron las siguientes:
Tableros de máximo 8 piezas.
Algoritmo Minimax a un niveld de profundidad 3.
Tiempo máximo de Stockfish para la evaluación de un tablero de 0.3 segun-
dos.
Pese a limitar el tiempo de Stockfish para la evaluación del árbol de juego, su
algoritmo de búsqueda está optimizado para correr de manera paralela en las
CPUs, además de estar realizado en C++, un lenguaje de programación bastante
más rápido que Python.
El número total de partidas en las que se enfrentaron es de 500, y los resultados
son:
Partidas Tablas Porcentaje
500 177 35.4%
Cuadro 6.2: Resultados de las partidas contra Stockfish 15.
Como se puede observar en la tabla superior, la red ha sido capaz de empatar
el 35.4% de las partidas que se jugaron contra la versión más reciente de Stock-
45
--- PAGE 54 ---
6.4. Conclusiones
fish, la cual tiene un ELO de 3600 puntos, 800 puntos por encima del campeón
mundial Magnus Carlsen.
Para poner en perspectiva este resultado, podemos comparar con el enfrenta-
miento entre AlphaZero y Stockfish comentado al inicio de este documento. En
este enfrentamiento a 100 partidas entre ambos motores, el porcentaje de tablas
fue del 72%.
6.4. Conclusiones
COncluyendo este Trabajo de Fin de Grado, se ha realizado un pequeño motor
de ajedrez capaz de jugar de manera autónoma al juego. Para que esto
haya sido posible, se ha tenido que desarrollar un algoritmo de búsqueda capaz
de recorrer el árbol de juego a partir de una posición inicial, y una función de
evaluacióncapazdedarunvaloracadatablero.Paralacreacióndelafunciónde
evaluación se ha decidido optar por el uso de técnicas Deep Learning, mediante
la creación de una red neuronal, las cuales son muy punteras en el ámbito del
ajedrez por computadora y muestran un rendimiento superior a otras formas de
evaluación.
UNa vez realizado el proyecto, puede asegurarse el cumplimiento de los ob-
jetivos inicialmente planteados, valorándolo en función de los resultados
obtenidos. Los primeros objetivos tenían un enfoque menos práctico, y se cen-
traban en la investigación del ajedrez por computadora, desde su origen hasta
su evolución y estado actual. La valoración para estos primeros objetivos es bas-
tante positiva, puesto que para la realización de este Trabajo de Fin de Grado,
se han tenido que entender los conceptos básicos de la disciplina para poder
aplicarlos en la parte más práctica.
Además de la investigación sobre el ajedrez por computadora, también se ha
tenido que entender el funcionamiento y características del Deep Learning. El
entendimiento de los conceptos Deep Learning han sido fundamentales para la
creación de una red neuronal capaz de evaluar el árbol de juego, la cual ha sido
utilizada como función de evaluación.
MEdiante la evaluación de los resultados obtenidos, se infiere que la creación
de este pequeño motor de ajedrez es bastante positiva, ya que los resul-
tados en los enfrentamientos contra Stockfish muestran que, con una mayor
cantidad de tiempo de entrenamiento, la red podría haber llegado a unos re-
sultados aún mejores que los obtenidos con la versión actual. Sin embargo, un
35.4% de tablas contra el mejor motor de ajedrez por computadora del mundo es
algo a tener en cuenta, sobre todo debido a la escala de este Trabajo de Fin de
Grado.
6.5. Trabajos Futuros
Existen dos ramas definidas que podrían suponer una mejora sustancial res-
pecto a este Trabajo de Fin de Grado:
46
--- PAGE 55 ---
Resultados y conclusiones
1. Mejora del Algoritmo de Búsqueda: Debido al hincapié realizado en es-
te trabajo en las técnicas Deep Learning, mediante la creación de una red
neuronal utilizada como función de evaluación, el algoritmo de búsque-
da ha quedado relegado a un segundo puesto en cuanto a importancia.
Aunque es verdad que su desarrollo era necesario para un funcionamiento
completo, su aplicación tiene margen de mejora.
Existen diferentes posibilidades que hagan el algoritmo de búsqueda más
eficiente, como puede ser el añadir tablas de transposición [39].
Debido a que es posible llegar a un mismo tablero mediante diferentes mo-
vimientos, estas tablas son muy útiles para reducir el tiempo de búsqueda.
El funcionamiento básico es el de actuar como caché de posiciones ya ana-
lizadas, con el objetivo de no tener que evaluar de nuevo el mismo tablero.
Además de este tipo de técnicas, existe la posibilidad de añadir paralelismo
aniveldelenguaje,locualaumentademaneraconsiderableelrendimiento
del algoritmo [40].
2. Mejora de la Red Neuronal: En cuanto a la red neuronal, la mejora más
evidente es la de la creación de una red neuronal capaz de evaluar cual-
quier tablero (como se tenía pensado en un principio), y no únicamente
aquellos tableros con un máximo de 8 piezas. Aunque una mayor capa-
cidad computacional ayudaría, el principal factor limitante sería la gran
cantidad de tiempo necesario para entrenarla.
47
--- PAGE 57 ---
Capítulo 7
Análisis de impacto
Para finalizar, se comentará el impacto de este Trabajo de Fin de Grado en dife-
rentes contextos:
• Personal: Gracias a la experiencia proporcionada en este trabajo, se ha po-
dido aprender más sobre la historia y estado actual del ajedrez por compu-
tadora, así como de su importancia dentro del ámbito de la informática.
Además, he podido comprender las capacidades de la Inteligencia Artifi-
cial, más concretamente del Deep Learning, pudiendo comprender así su
reciente expansión en la disciplina.
• Empresarial: El uso del Deep Learning para la creación de software es un
atractivo para diferentes empresas, ya que pueden utilizar estas caracterís-
ticas para optimizar sus procesos de desarrollo, reducir costes y mejorar la
productividad. Hay numerosos ejemplos de la aplicación de Deep Learning
en diferentes ámbitos de la informática, siendo uno de los campos más
prominentes en la actualidad.
• Económico:Comosehacomentadoanteriormente,elusodelaInteligencia
Artificial puede suponer un incentivo a las empresas, ya que es posible la
reducción de costes y mejora de la productividad mediante su aplicación.
• Social: Debido a su naturaleza estratégica, el ajedrez tiene numerosos be-
neficios, como por ejemplo, aumentar la capacidad de análisis de un indi-
viduo o entrenar la memoria. Esto hace que sea posible tomar el juego no
solamente como un pasatiempo, sino como un entreno de las capacidades
personales.
• Cultural: El ajedrez es uno de los juegos de estrategia más importantes de
la historia de la humanidad, con varios siglos de historia. Llevar al límite
este juego mediante sistemas inteligentes capaces de tomar decisiones de
manera precisa ayudará a que en un futuro la gente siga teniendo interés
en este juego.
• Medioambiental: Las redes neuronales necesitan de grandes cantidades
de energía para entrenar, lo cual puede suponer un impacto negativo en el
medio ambiente. Además, los componentes de los sistemas utilizados para
49
--- PAGE 58 ---
entrenar estas redes, como las GPUs, contienen materiales que pueden
llegar a ser dañinos para el medio ambiente.
50
--- PAGE 59 ---
Bibliografía
[1] E. S. Claude, “Programming a computer for playing chess,” Philosophical
Magazine, Ser, vol. 7, no. 41, p. 314, 1950.
[2] M. Campbell, A. J. Hoane Jr, and F.-h. Hsu, “Deep blue,” Artificial intelli-
gence, vol. 134, no. 1-2, pp. 57–83, 2002.
[3] A. Paszke, S. Gross, F. Massa, A. Lerer, J. Bradbury, G. Chanan, T. Killeen,
Z. Lin, N. Gimelshein, L. Antiga, A. Desmaison, A. Köpf, E. Yang, Z. DeVito,
M. Raison, A. Tejani, S. Chilamkurthy, B. Steiner, L. Fang, J. Bai, and
S. Chintala, “Pytorch: An imperative style, high-performance deep learning
library,” 2019. [Online]. Available: https://arxiv.org/abs/1912.01703
[4] H. J. R. Murray, A history of chess. Clarendon Press, 1913.
[5] A.R.Kraaijeveld,“Originofchess-aphylogeneticperspective,”BoardGames
Studies, vol. 3, pp. 39–50, 2000.
[6] G.Josten,“Chess–alivingfossil,”Cologne:InitiativeGroupKönigstein,2001.
[7] R. L. de Segura, Libro de la invención liberal y arte del juego del axedrez,
1993.
[8] O. Morgenstern and J. Von Neumann, Theory of games and economic beha-
vior. Princeton university press, 1953.
[9] M. Bacharach, “Zero-sum games,” in Game theory. Springer, 1989, pp.
253–257.
[10] J. N. Kok, E. J. Boers, W. A. Kosters, P. Van der Putten, and M. Poel, “Ar-
tificial intelligence: definition, trends, techniques, and cases,” Artificial inte-
lligence, vol. 1, pp. 270–299, 2009.
[11] S.-C.Wang,“Artificialneuralnetwork,”inInterdisciplinarycomputinginjava
programming. Springer, 2003, pp. 81–100.
[12] K.-C. Jim, C. L. Giles, and B. G. Horne, “An analysis of noise in recurrent
neural networks: convergence and generalization,” IEEE Transactions on
neural networks, vol. 7, no. 6, pp. 1424–1438, 1996.
[13] F. Agostinelli, M. Hoffman, P. Sadowski, and P. Baldi, “Learning acti-
vation functions to improve deep neural networks,” arXiv preprint ar-
Xiv:1412.6830, 2014.
51
--- PAGE 60 ---
BIBLIOGRAFÍA
[14] J. Schmidt-Hieber, “Nonparametric regression using deep neural networks
with relu activation function,” The Annals of Statistics, vol. 48, no. 4, pp.
1875–1897, 2020.
[15] A. Kundu, A. Heinecke, D. Kalamkar, S. Srinivasan, E. C. Qin, N. K. Me-
llempudi, D. Das, K. Banerjee, B. Kaul, and P. Dubey, “K-tanh: Efficient
tanh for deep learning,” arXiv preprint arXiv:1909.07729, 2019.
[16] Y. LeCun, Y. Bengio, and G. Hinton, “Deep learning,” nature, vol. 521, no.
7553, pp. 436–444, 2015.
[17] D. Choi, C. J. Shallue, Z. Nado, J. Lee, C. J. Maddison, and G. E. Dahl,
“On empirical comparisons of optimizers for deep learning,” arXiv preprint
arXiv:1910.05446, 2019.
[18] A. Gotmare, N. S. Keskar, C. Xiong, and R. Socher, “A closer look at deep
learning heuristics: Learning rate restarts, warmup and distillation,” arXiv
preprint arXiv:1810.13243, 2018.
[19] A. H. Renear, S. Sacchi, and K. M. Wickett, “Definitions of dataset in the
scientific and technical literature,” Proceedings of the American Society for
Information Science and Technology, vol. 47, no. 1, pp. 1–4, 2010.
[20] H. Li, J. Li, X. Guan, B. Liang, Y. Lai, and X. Luo, “Research on overfitting
of deep learning,” in 2019 15th International Conference on Computational
Intelligence and Security (CIS). IEEE, 2019, pp. 78–81.
[21] S. Shalev-Shwartz, O. Shamir, and S. Shammah, “Failures of gradient-
based deep learning,” in International Conference on Machine Learning.
PMLR, 2017, pp. 3067–3075.
[22] G. Haworth and N. Hernandez, “The 20 th top chess engine championship,
tcec20,” ICGA Journal, vol. 43, no. 1, pp. 62–73, 2021.
[23] D.Silver,T.Hubert,J.Schrittwieser,I.Antonoglou,M.Lai,A.Guez,M.Lan-
ctot, L. Sifre, D. Kumaran, T. Graepel et al., “Mastering chess and shogi by
self-play with a general reinforcement learning algorithm,” arXiv preprint
arXiv:1712.01815, 2017.
[24] T. McGrath, A. Kapishnikov, N. Tomasev, A. Pearce, D. Hassabis,
B. Kim, U. Paquet, and V. Kramnik, “Acquisition of chess knowledge
in alphazero,” CoRR, vol. abs/2111.09259, 2021. [Online]. Available:
https://arxiv.org/abs/2111.09259
[25] G. M. J. Chaslot, M. H. Winands, H. J. v. d. Herik, J. W. Uiterwijk, and
B. Bouzy, “Progressive strategies for monte-carlo tree search,” New Mathe-
matics and Natural Computation, vol. 4, no. 03, pp. 343–357, 2008.
[26] G. Strong, “The minimax algorithm,” Trinity College Dublin, 2011.
[27] M. S. Campbell and T. A. Marsland, “A comparison of minimax tree search
algorithms,” Artificial Intelligence, vol. 20, no. 4, pp. 347–367, 1983.
52
--- PAGE 61 ---
BIBLIOGRAFÍA
[28] S. H. Fuller, J. G. Gaschnig, J. Gillogly et al., Analysis of the alpha-beta
pruningalgorithm. DepartmentofComputerScience,Carnegie-MellonUni-
versity, 1973.
[29] M. H. Winands, H. J. Van den Herik, J. W. Uiterwijk, and E. C. Van der
Werf, “Enhanced forward pruning,” Information Sciences, vol. 175, no. 4,
pp. 315–329, 2005.
[30] A. De Groot, “Thought and choice in chess _, mouton, the hague, 1965,”
Also, 1978.
[31] J. Pearl, “Scout: A simple game-searching algorithm with proven optimal
properties.” in AAAI, 1980, pp. 143–145.
[32] Y. Nasu, “Efficiently updatable neural-network-based evaluation functions
for computer shogi,” The 28th World Computer Shogi Championship Appeal
Document, 2018.
[33] K. O’Shea and R. Nash, “An introduction to convolutional neural
networks,” CoRR, vol. abs/1511.08458, 2015. [Online]. Available:
http://arxiv.org/abs/1511.08458
[34] B. Koonce, “Resnet 50,” in Convolutional Neural Networks with Swift for
Tensorflow. Springer, 2021, pp. 63–72.
[35] S. Edwards, “Forsyth-edwards notation,” Portable Game Notation Specifica-
tion and Implementation Guide, vol. 3, 1994.
[36] C. Seger, “An investigation of categorical variable encoding techniques in
machine learning: binary versus one-hot and feature hashing,” 2018.
[37] D. Singh and B. Singh, “Investigating the impact of data normalization on
classification performance,” Applied Soft Computing, vol. 97, p. 105524,
2020.
[38] B. Illing, W. Gerstner, and J. Brea, “Biologically plausible deep lear-
ning—but how far can we go with shallow networks?” Neural Networks,
vol. 118, pp. 90–101, 2019.
[39] T. A. Marsland, “Computer chess methods,” Encyclopedia of Artificial Intelli-
gence, vol. 1, pp. 159–171, 1987.
[40] J. Gonzalez, J. Taylor, S. Castro, J. Kern, J. Knudstrup, S. Zampieri,
A. Manning, S. Bhatnagar, L. Davis, K. Golap et al., “Python code paralle-
lization, challenges and alternatives,” Astronomical Data Analysis Software
and Systems XXVI, vol. 521, p. 515, 2019.
53
--- PAGE 62 ---
Este documento esta firmado por
Firmante CN=tfgm.fi.upm.es, OU=CCFI, O=ETS Ingenieros Informaticos -
UPM, C=ES
Fecha/Hora Wed Jun 01 21:01:09 CEST 2022
Emisor del EMAILADDRESS=<EMAIL>, CN=CA ETS Ingenieros
Certificado Informaticos, O=ETS Ingenieros Informaticos - UPM, C=ES
Numero de Serie 561
Metodo urn:adobe.com:Adobe.PPKLite:adbe.pkcs7.sha1 (Adobe
Signature)