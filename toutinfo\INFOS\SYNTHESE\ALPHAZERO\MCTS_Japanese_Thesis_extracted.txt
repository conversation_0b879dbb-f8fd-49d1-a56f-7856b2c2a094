# EXTRACTION TEXTUELLE - MCTS_Japanese_Thesis.pdf
# Généré automatiquement le 2025-05-28 10:25:57
# Source: MCTS_Japanese_Thesis.pdf
# ================================================================


--- PAGE 1 ---
博士論文
モンテカルロ木探索の改善に関する研究
今川 孝久
--- PAGE 2 ---
目 次
第1章 序論 1
1.1 本論文の構成 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 3
第2章 モンテカルロ木探索とその関連手法 5
2.1 グラフ : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 5
2.2 ゲームとゲーム木 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 6
2.2.1 ゲーム木モデル : : : : : : : : : : : : : : : : : : : : : : : : : : : 6
2.2.2 Finnsonnの仮想ゲーム: : : : : : : : : : : : : : : : : : : : : : : 9
2.2.3 General Video Game Playing : : : : : : : : : : : : : : : : : : : 9
2.2.4 マルコフ決定過程（MDP） : : : : : : : : : : : : : : : : : : : : 10
2.3 強化学習と探索 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 11
2.4 バンディット問題とそのアルゴリズム : : : : : : : : : : : : : : : : : : : 11
2.4.1 多腕バンディット問題（MAB） : : : : : : : : : : : : : : : : : : 11
2.4.2 UCB1 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 12
2.4.3 KL-UCB : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 13
2.4.4 Thompson Sampling : : : : : : : : : : : : : : : : : : : : : : : : 13
2.4.5 Epsilon-greedy : : : : : : : : : : : : : : : : : : : : : : : : : : : 14
2.4.6 Softmax : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 14
2.5 期待値の最大値の推定量 : : : : : : : : : : : : : : : : : : : : : : : : : : 14
2.5.1 Average Estimator (AVE) : : : : : : : : : : : : : : : : : : : : : 15
2.5.2 Maximum Estimator (ME) : : : : : : : : : : : : : : : : : : : : 15
2.5.3 Double Estimator (DE) : : : : : : : : : : : : : : : : : : : : : : 16
2.5.4 Weighted Estimator (WE) : : : : : : : : : : : : : : : : : : : : : 16
2.5.5 Mixmax Estimator (MM) : : : : : : : : : : : : : : : : : : : : : 17
2.6 モンテカルロ木探索（MCTS） : : : : : : : : : : : : : : : : : : : : : : 17
2.6.1 UCT : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 18
2.6.2 MCTS-Solver : : : : : : : : : : : : : : : : : : : : : : : : : : : : 21
2.6.3 Accerated UCT : : : : : : : : : : : : : : : : : : : : : : : : : : : 22
2.7 その他のMCTSアルゴリズム : : : : : : : : : : : : : : : : : : : : : : : 23
2.7.1 Sparse Sampling (SS) : : : : : : : : : : : : : : : : : : : : : : : 23
2.7.2 BRUE : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 23
2.7.3 単純リグレットの最小化に向けた研究 : : : : : : : : : : : : : : 24
1
--- PAGE 3 ---
2.8 探索ヒューリスティックの動的な調整 : : : : : : : : : : : : : : : : : : : 25
2.8.1 Dynamic Komi : : : : : : : : : : : : : : : : : : : : : : : : : : : 25
2.8.2 シミュレーションからの学習 : : : : : : : : : : : : : : : : : : : 26
2.9 ハイブリッドな探索手法 : : : : : : : : : : : : : : : : : : : : : : : : : : 27
2.9.1 利得分布のミニマックス的な更新 : : : : : : : : : : : : : : : : : 28
第3章 モンテカルロ木探索と有利不利が偏った局面 30
3.1 背景 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 30
3.2 Incremental Random Gameモデルでの次善手の偏り : : : : : : : : : : 32
3.3 利得の差の最大化 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 32
3.3.1 Constantモデルの一手読み探索での分析 : : : : : : : : : : : : : 33
3.3.2 最大頻度法とUCB : : : : : : : : : : : : : : : : : : : : : : : : : 35
3.4 実験 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 36
3.4.1 実験設定: : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 36
3.4.2 Constant Trees : : : : : : : : : : : : : : : : : : : : : : : : : : : 37
3.4.3 Random Trees : : : : : : : : : : : : : : : : : : : : : : : : : : : 40
3.5 まとめ : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 40
第4章 分布を使ったハイブリッドMCTS 47
4.1 背景 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 47
4.2 信頼度合いの修正に基づく探索 : : : : : : : : : : : : : : : : : : : : : : 48
4.2.1 利得の分布の更新 : : : : : : : : : : : : : : : : : : : : : : : : : : 48
4.2.2 探索の手法 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 49
4.2.3 実装の詳細 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 52
4.3 実験 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 53
4.3.1 誤答率の比較 : : : : : : : : : : : : : : : : : : : : : : : : : : : : 53
4.3.2 Budgetを増やすことでの計算の効率化 : : : : : : : : : : : : : : 54
4.4 まとめ : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 55
第5章 子孫の勝敗確定時のシミュレーション結果の修正 58
5.1 背景 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 58
5.2 提案手法 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 59
5.2.1 利得の推定値の修正：Replacement MCTS-Solver : : : : : : : : 59
5.2.2 訪問数の修正：All Removal MCTS-Solver : : : : : : : : : : : : 59
5.2.3 利得の推定値の変化に応じた訪問数の修正：Inconsistency Re-
moval MCTS-Solver : : : : : : : : : : : : : : : : : : : : : : : : 60
5.3 実験 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 62
5.3.1 実験設定: : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 62
5.3.2 実験結果: : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 62
2
--- PAGE 4 ---
5.4 まとめ : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 66
第6章 期待値の最大値の推定量 72
6.1 背景 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 72
6.2 Weighted Estimator Based on Upper Con(cid:12)dence Bound : : : : : : : : 73
6.3 実験 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 78
6.4 まとめ : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 80
第7章 MCTSにおける期待値の最大値の推定量の改善 87
7.1 背景 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 87
7.2 UCTSWE : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 88
7.3 確率的な利得が得られるゲームのモデル : : : : : : : : : : : : : : : : : 89
7.4 実験 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 90
7.4.1 確率的な利得が得られる木 : : : : : : : : : : : : : : : : : : : : : 90
7.4.2 確定的な利得が得られる木 : : : : : : : : : : : : : : : : : : : : : 91
7.5 まとめ : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 100
第8章 結論 105
3
--- PAGE 5 ---
図 目 次
2.1 Kocsisのゲーム木モデルの例 : : : : : : : : : : : : : : : : : : : : : : : 8
2.2 トラップの図 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 9
2.3 最良優先探索の概要 : : : : : : : : : : : : : : : : : : : : : : : : : : : : 18
2.4 プレイアウトとシミュレーション : : : : : : : : : : : : : : : : : : : : : 18
2.5 ハイブリッドなMCTSの探索木 : : : : : : : : : : : : : : : : : : : : : : 28
3.1 分枝数と深さが2のゲーム木モデルの例 : : : : : : : : : : : : : : : : : 33
3.2 Constant’(1,13)で，終端節点でのゲームのスコアのヒストグラム : : : 33
3.3 Constantでの比較 (UCB1) : : : : : : : : : : : : : : : : : : : : : : : : 37
3.4 Constantでの比較 (Thompson sampling) : : : : : : : : : : : : : : : : 38
3.5 Constantでの比較 (KL-UCB) : : : : : : : : : : : : : : : : : : : : : : : 39
3.6 Constant(1,5)での誤答率の比較（拡張手法あり，なし） : : : : : : : : 42
3.7 UCT, Constant’(1,13)での誤答率 : : : : : : : : : : : : : : : : : : : : : 43
3.8 Randomでの誤答率の比較（UCB1） : : : : : : : : : : : : : : : : : : : 43
3.9 Randomでの誤答率の比較（Thompson Sampling） : : : : : : : : : : : 44
3.10 Randomでの誤答率の比較（KL-UCB） : : : : : : : : : : : : : : : : : 45
3.11 Random(4;3)での誤答率の比較 : : : : : : : : : : : : : : : : : : : : : : 46
4.1 提案手法で扱う探索木の二つの部分 : : : : : : : : : : : : : : : : : : : : 48
4.2 提案手法の概要 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 49
4.3 誤答率の比較 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 56
4.4 計算の効率化と誤答率 : : : : : : : : : : : : : : : : : : : : : : : : : : : 57
5.1 Replacementの問題点 : : : : : : : : : : : : : : : : : : : : : : : : : : : 60
5.2 提案手法の更新の例 : : : : : : : : : : : : : : : : : : : : : : : : : : : : 61
5.3 （分枝数，最大深さ）＝ (4;12) での誤答率の推移 : : : : : : : : : : : : 63
5.4 （分枝数，最大深さ）＝ (8;8) での誤答率の推移 : : : : : : : : : : : : 64
5.5 （分枝数，最大深さ）＝ (16;16) での誤答率の推移 : : : : : : : : : : : 65
5.6 手の値の比率2，（分枝数，最大深さ）＝ (4;12) での誤答率の推移 : : : 66
5.7 手の値の比率2，（分枝数，最大深さ）＝ (8;8) での誤答率の推移 : : : : 67
5.8 手の値の比率2，（分枝数，最大深さ）＝ (16;16) での誤答率の推移 : : : 68
5.9 終端確率 0:1，手の値の比率2，（分枝数，最大深さ）＝ (4;12) での最善
手の推定値の平均と分散の推移 : : : : : : : : : : : : : : : : : : : : : : 68
4
--- PAGE 6 ---
5.10 終端確率 0:2，手の値の比率2，（分枝数，最大深さ）＝ (4;12) での最善
手の推定値の平均と分散の推移 : : : : : : : : : : : : : : : : : : : : : : 69
5.11 終端確率 0:1，手の値の比率2，（分枝数，最大深さ）＝ (8;8) での推定
値の平均と分散の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : 69
5.12 終端確率 0:2，手の値の比率2，（分枝数，最大深さ）＝ (8;8) での推定
値の平均と分散の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : 70
5.13 終端確率 0:1，手の値の比率2，（分枝数，最大深さ）＝ (16;16) での最
善手の推定値の平均と分散の推移 : : : : : : : : : : : : : : : : : : : : : 70
5.14 終端確率 0:2，手の値の比率2，（分枝数，最大深さ）＝ (16;16) での最
善手の推定値の平均と分散の推移 : : : : : : : : : : : : : : : : : : : : : 71
6.1 Softmaxに従って，サンプルサイズを増やした時の推定量のバイアス，
分散，MSE : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 81
6.2 UCB1に従ってサンプルサイズを増やした時の推定量のバイアス，分散，
MSE : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 82
6.3 epsilon-greedyに従ってサンプルサイズを増やした時の推定量のバイア
ス，分散，MSE : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 83
6.4 uniformに従ってサンプルサイズを増やした時の推定量のバイアス，分
散，MSE : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 84
6.5 アームの設定に対する，各値の依存 : : : : : : : : : : : : : : : : : : : : 85
6.6 アームの数に対するバイアス，分散，MSEの依存度合い : : : : : : : : 86
p
7.1 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (8;2)の誤答
p
率の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 90
p
7.2 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (8;4)の誤答
p
率の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 91
p
7.3 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (16;2)の誤
p
答率の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 91
p
7.4 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (16;4)の誤
p
答率の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 92
p
7.5 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (32;2)の誤
p
答率の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 92
p
7.6 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (8;2) での利
p
得の推定値の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 93
p
7.7 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (8;4) での利
p
得の推定値の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 94
p
7.8 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (16;4) での
p
利得の推定値の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : 95
5
--- PAGE 7 ---
p
7.9 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (16;2) での
p
利得の推定値の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : 96
p
7.10 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (32;2) での
p
利得の推定値の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : 97
p
7.11 C = 2，（分枝数，深さ）＝ (8;6) での利得の推定値の推移 : : : : : : 98
p
7.12 incremental random treeで，c=0:5とした時の誤答率の推移 : : : : : 99
p
7.13 UCTSWE（c=0:5，C = 2），（分枝数，深さ）＝ (8;6) での利得の
p
推定値の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 100
p
7.14 C = 2（分枝数，深さ）＝ (8;12) での利得の推定値の推移 : : : : : : 101
p
7.15 特異節点を含む木（分枝数，深さ）＝ (8;12) でC を調節した場合の誤
p
答率の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 103
p
7.16 特異節点を含む木，C = 2（分枝数，深さ）＝ (8;12) での利得の推
p
定値の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 104
7.17 特異節点を含む木（分枝数，深さ）＝ (16;12) でC を調節した場合の
p
誤答率の推移 : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 104
6
--- PAGE 8 ---
表 目 次
2.1 類似の手法と4章での提案手法との比較 : : : : : : : : : : : : : : : : : 29
4.1 4000プレイアウト時の誤答率 : : : : : : : : : : : : : : : : : : : : : : : 54
4.2 プレイアウト一回当たりにかかる平均時間（ミリ秒） : : : : : : : : : : 54
p
7.1 C = 2，（分枝数，深さ）＝ (8;6)，各プレイアウト数での利得の推定値 98
p
p
7.2 C = 2，（分枝数，深さ）＝ (8;12)，各プレイアウト数での利得の推定値100
p
p
7.3 特異節点を含む木（分枝数，深さ）＝ (8;12)，C = 2各プレイアウト
p
数での利得の推定値 : : : : : : : : : : : : : : : : : : : : : : : : : : : : 100
p
7.4 C = 2kとした時の8000プレイアウト時の誤答率 : : : : : : : : : : : 101
p
7
--- PAGE 9 ---
概 要
本論文は二人零和完全情報ゲームを主な対象とした，モンテカルロ木探索（MCTS）の
研究について記したものである．ゲームにおいては，全ての局面を読み切ることは出来
ないことが一般的である．それは，一般に，ゲームは探索空間が広く，かつ手を打つま
での時間が限られているためである．従って，探索では，効率的に最善手を見つけるこ
とが要請される．MCTSは，モンテカルロシミュレーションを繰り返し，その結果が
良い選択肢を優先的に探索するアルゴリズムである．MCTSでは，シミュレーション
結果に対し，利得（良さ）を与え，各候補手を利得の平均値で評価し，評価の高い手を
優先して，先読みとシミュレーションを行う．最善手の判別のためには，各候補手を選
んだ場合の真の利得の期待値を推定する必要がある．代表的なMCTSでは，多腕バン
ディット問題（MAB）における，累積リグレット（累積的な損失）の最小化のためのア
ルゴリズムを，シミュレーションを開始する節点の決定に応用している．MCTSに累
積リグレットの最小化のためのアルゴリズムを応用することで，MABに同様に，徐々
に最善手を多く選ぶようになり，シミュレーションの平均的な利得が，最善を尽くした
場合の利得に近づくと期待される．
MCTS は汎用なアルゴリズムであり，General Game Playing や，General Video
Game Playingという初見のゲームを総合的に上手くプレイすることが求められるドメ
インで成果を上げてきた．また，囲碁やHexといった個々のゲームでも主流な探索手
法である．特に囲碁では，初めてプロ棋士に勝ったプログラムAlphaGoでの探索に採
用され，その勝利に貢献した．
しかしながら，MCTSは常に優れた性能を発揮出来る訳では無く，比較的苦手なド
メインがあることが知られている．そして，MCTSの性能とドメインの特徴の関係は
まだ不明な点も多く，その関係性を明らかにする必要がある．また，代表的なMCTS
ではシミュレーションを開始する節点を選ぶことをMABとみなして，MABのアルゴ
リズムを用いているが，MABと木探索では異なる点があり，MABの性質は成り立た
ない．そのため，MABより木探索の性質に近い仮定とその仮定に基づくアルゴリズム
について調べる価値がある．
本研究では，MCTSに関して，ドメインの種類とそこでの性能，理論的な仮定といっ
た観点から議論し，既存手法の問題点を指摘し，その改善策を提示した．具体的には，
四つの点に着目して研究を行った．
第一点目として，MCTSが比較的苦手とされるドメインの特徴の一つとして，最善
手を選び損ねた場合の不利益の偏りという特徴に新たに着目する．そして，その特徴と
MCTSの性能の関係性について分析する．MCTSが成果を上げてきたドメインの一つ
である，囲碁の中でも，攻め合いの局面は比較的苦手であると知られている．本研究で
扱う特徴を持つ局面は，攻め合いの局面と共通の難しさを有している．本研究では，シ
ミュレーションの結果，囲碁での目数の差といったゲーム上のスコアが手に入るという
--- PAGE 10 ---
仮定の下で，理論的な分析に基づく利得の与え方を提案する．そして，既存手法が性能
を発揮しづらい特徴を持つ局面で，提案手法が効果的であることを示した．
第二点目として，MCTSにおける探索資源の割り振りに着目し，直接的に単純リグ
レット（最終的な手の選択での損失）の最小化を目指したMCTSを提案する．探索の
目的は最善手を判別することであり，これは単純リグレットを最小化することである．
しかしながら，探索において，MABのアルゴリズムを使い，単純リグレットを直接最
小化することは難しいため，累積リグレットの最小化のための手法がMCTSに応用さ
れてきた．本研究では，探索での単純リグレットの直接的な最小化に向け，分布を使っ
たMCTSを提案する．提案手法は，扱う分布を真の利得の主観確率の分布と仮定する
と，最善手の判別のために，探索資源の割り振りが効果的になされると期待できる手法
である．実験の結果，提案手法の性能は，深さが一様な木では，既存手法を超えないも
のの，非一様な木では上回った．
第三点目として，MCTSにおける勝敗の確定に着目する．探索木中のある節点が終
端（ゲームの終わり）の場合，その節点での勝敗が確定する．これは，シミュレーショ
ンによる確率的な評価しか得られないMCTSにおいて，例外的に確実な評価が得られ
るという意味で重要である．しかしながら，既存手法では，その情報を枝刈りにしか利
用していないという問題点がある．本研究では，勝敗確定の情報を枝刈りだけでなく，
推定値の修正とそれに応じた探索の優先度度合いの調整に利用する方法を提案する．計
算機実験により，提案手法は既存手法よりも効果的であることを示した．
第四点目として，MCTSにおける候補手の利得の推定値に着目する．最善手の判別
のためには，最善を尽くした時の利得を正確に推定する必要がある．特に，最善手は以
後最善を尽くすと仮定した場合での最も良い利得の手であるので，期待利得の最大値の
推定が重要である．まず，MABの設定下で，利得の期待値の最大値に対する，新しい
推定量 Simpli(cid:12)ed Weighted Estimator (SWE) を提案する．各選択肢の試行回数が予
め決まっているという仮定のもとで，SWEについて理論的な分析を行い，試行回数が
増えるにつれて，推定値が正しい値に収束することを示した．さらに，累積リグレット
最小化のためのアルゴリズムに基づいて試行回数が決まる場合での，推定値の正確さに
ついて，実験を行い検証した．その結果．設定次第では，既存手法の性能がSWEを超
える場合もあるが，設定に対する推定量の性能の安定性という観点でSWEが優れてい
ることを示した．特に，MCTSで行われている，平均による推定値は，正しい値と比
べて過度に低くなることを示した．加えて，SWEをMCTSに応用し，実験で性能を
確かめる．実験は，終端節点で利得が確率的に得られる木と確定的に得られる木の２種
類で行った．提案手法は後者の木では，既存手法を超えないものの，前者の木では上回
る性能を示した．
以上のように本研究は，複合的なアルゴリズムであるMCTSについて，利得の与え
方，候補手の評価の仕方，評価に対する探索資源の割り振り方という三つの要素技術の
それぞれの観点から議論し，改善策を提示したと言える．
2
--- PAGE 11 ---
第1章 序論
1956年ダートマスにて，マッカーシーらによって会議が行われた．いわゆるダート
マス会議である．そこで Arti(cid:12)cial Intelligence （人工知能）という言葉が初めて使わ
れ，人工知能という分野が生まれたと言える．その３年後にはサミュエルによってチェッ
カーのプログラムに関する論文 [60]が発表されたことに代表されるように，人工知能
研究の初期からゲームを題材に研究が行われてきた．ゲームを上手くプレイ出来ると
いうことは，相手の行動を予測し，その行動に対して自分の効用を大きくするという戦
略的な行動を効率的に見つけられるということであり，それは，人間の持つ知性の一側
面である．ゲームを上手くプレイする方法（探索アルゴリズム）について研究すること
は，間接的に人間の知性を理解することに繋がるという意義がある．また，ゲームは
ルールが明確なため，研究対象として扱い易いという良い性質もある．
ゲームの内でも特にチェスは，人間のプレイヤーも多く，長い歴史があり，人間のプ
レイが洗練されているということもあり，コンピュータプログラムがチェスで人間の
トッププロに勝つことを目標に効率的な探索についての研究がなされてきた．1997年
にプログラムが，初めて人間の世界チャンピオンであるカスパロフに６戦中２勝１敗３
引き分けで勝ち越した際には，ミニマックス探索を基本とする技術が用いられた [12]．
チェスでトッププロに勝った後は，より探索空間が広く，なおかつプロのプレイヤーが
存在するゲームである，囲碁でトッププロに勝つことが新たな目標の一つとなった．
ミニマックス探索は，ゲームの探索木（節点が局面，辺が指し手に対応する）を展開
し，葉の局面の良し悪しを判定し，そして，互いに最善を尽くすという前提で指し手の
優劣を判定する方法である．もし葉がゲームの終局であったなら，ゲームの勝敗を局面
の良し悪しの評価に使える．しかし，例えば，囲碁やチェスの状態空間の大きさはそれ
ぞれ10172;1047 [31]であると推定されているように，一般にゲーム木は大きく，終局
まで探索木の葉が到達することは現実的では無い．そのような状況下で，効率的にミニ
マックス探索するために，終局でない局面の良し悪しを正しく評価する関数，評価関数
が必要である．チェスや将棋では良い評価関数が作られて，プログラムの強さも人間の
トップレベル達したが，囲碁で信頼に足る評価関数が作るのは難しいと考えられてい
た1．実際に，囲碁でミニマックス探索を用いたプログラムの強さは，弱いアマチュア
の段階にとどまっていた．
しかし，2006年にモンテカルロ木探索（MCTS）が提案され，ミニマックス探索に
1その後，2016年に囲碁プログラムAlphaGoに関する論文[61]で示されたように，深層学習で作った
評価関数だけを用いてアマチュア４，５段の程度のレーティングであることから，十分に信頼に足る評価関
数は作れていると言える．
1
--- PAGE 12 ---
代えて用いられるようになって以来，CrazyStoneをはじめとした囲碁プログラムの強
さは急激に向上した[17]．また，近年ではMCTSを深層学習と組み合わせAlphaGoが
囲碁のトッププロに勝ったことでもMCTSは注目されている [61]．MCTSはモンテカ
ルロシミュレーションを行い，その結果で各手を評価し，その評価が良い手を優先して
先読みする手法である．これは，シミュレーション結果をもとにオンラインで良い手を
学習しながら探索する手法と解釈が出来る．このため，予め学習等で評価関数を用意し
なくても，それなりに上手く探索出来，囲碁以外にも幅広い応用先を持つ．
MCTSの応用先として，例えば，GeneralGamePlaying（GGP）が挙げられる．GGP
では，複数の初見のゲームを万遍なく上手にプレイするAIを目指している．GGPの
大会において，MCTSを用いたプレイヤーが優勝したことから，MCTSの有効性が明
らかになった[8]．また，類似のものとして，テレビゲームを題材としたGeneralVideo
Game Playing（GVGP）が挙げられる．GVGPでもMCTSアルゴリズムが好成績を
収めている [55]．さらに，GVGPでは，MCTSを使ったエージェントがサンプルとし
て提供されているように [24,55]，標準的なアルゴリズムとなっている．MCTSは他に
も，プランニング [20]や 2.2.4節で紹介するマルコフ決定過程だけでなく，状態が部分
的にしか観測出来ない部分観測マルコフ決定過程 [65]にも応用されている．
MCTSの一種UCT[42]は，多腕バンディット問題（MAB）のアルゴリズムUCB1[3]
をMCTSに応用したものであり，代表的なアルゴリズムである．MCTSが囲碁プログ
ラムCrazyStoneに採用された2006年にUCTは提案されたという意味で，初期から
存在し，また，UCB1に基づく理論的な解析がなされたことや，UCB1の簡便さや高
い実用性から広く用いられている．しかし，UCTをはじめとしてMCTSはチェスや
将棋で性能が限定的であり2，囲碁でも置き碁や攻め合いなどが苦手であると経験的に
知られているように，MCTSが有効に働くためのゲームの性質は明らかでない．また，
代表的なMCTSではシミュレーションを開始する節点を選ぶことをMABとみなして，
MABのアルゴリズムを用いているが，MABと木探索では異なる点があり，MABの
性質は成り立たない．そのため，MABより木探索の性質に近い仮定とその仮定に基づ
くアルゴリズムについて調べる価値がある．また，後に記すように改善の余地があるこ
とが分かってきた．
本研究では，二人零和有限確定完全情報ゲームを主な対象とし，MCTSの内，特に
UCTに関して，その欠点を明らかにするとともに，改善策について議論した．大きく分
けて２つの観点から研究を行った．１つ目は主流のMCTSアルゴリズムの性能とゲー
ムの特徴の関係を分析することである．主流のMCTSが上手く性能を発揮出来ない局
面の特徴が分かれば，より効果的なMCTS，あるいは新たな探索手法の考案の手がか
りになる．また，そのような効果的な探索手法が考案出来ない場合でも，その特徴を持
つ局面では，旧来のミニマックス探索を用いる等という対策が可能である．つまり，そ
2囲碁プログラム AlphaGoZero [63] を一般化し，囲碁以外のゲームでも学習出来るようにした Alp-
haZero[62]では，自己対戦により学習し，囲碁だけでなく，チェス，将棋でも手法の有効性を示した．そし
て，AlphaZeroで使われている探索はMCTSと言及されている．しかしながら，局面を評価する際に，モ
ンテカルロシミュレーションを行う代わりに評価関数を使う点で，従来の一般的なMCTSとは異なる．
2
--- PAGE 13 ---
の特徴を見極める手法を考案出来れば，旧来の手法を合わせて用いることで全体として
性能が改善するという期待が持てる．２つ目はMCTS自体の改善である．２つ目に関
して，主に３つの観点から研究を行った．１つ目は最善手を見分けるという観点，２つ
目は終局時の情報の扱いという観点，３つ目はシミュレーション結果からの手の評価と
いう観点である．MCTSの全般的な性能の改善には大きな意義がある．しかし，特定
の局面でしか改善しなかったとしても，前述の通り，その特徴を持つ局面を見つけ出す
ことが可能な手法と組み合わせることで，全体として性能を改善できると期待される．
また，特定の局面で何故改善したのかを突き詰めることで，理解を深め，より優れた
MCTSに向けた知見が得られるという意義もある．
尚，全般的な性能を改善することについての反論として，No Free Lunch (NFL) 定
理が有名である [75]が，MCTSの全般的な性能の改善は可能だと思われる．大雑把に
は，NFL定理は，あらゆる問題に対しての平均的な性能を比べると，どのアルゴリズ
ムも同じになるという定理である．つまり，全般的な性能の改善することは不可能とい
うことである．しかしながら，NFL定理は繰り返しのある問題（例えば多腕バンディッ
ト問題）には適用出来ないため [76]，MCTSについても同様であると予想される．ま
た，Blackbox Optimizationの分野で信じられているように，現実の問題に限って考え
た場合，問題の分布は偏ったものになり，たとえ繰り返しの無い問題でも，NFL定理
での前提を満たさないと予想される．
1.1 本論文の構成
2章には，本研究の背景について記す．本研究での探索対象のゲームや，モンテカル
ロ木探索（MCTS），それからMCTSに関連が深い多腕バンディット問題（MAB）と
そのアルゴリズム，また，MCTSと組み合わせて使われる拡張手法について記す．
3章では，主にMCTSの分析の観点から有利不利が偏っている局面でのMCTSの改
善について記す．より具体的には，「最善手を互いに選び続ければ引き分けになるが，次
善手を選んだ場合の不利益の度合いが手番によって異なる」という状況を表すゲームを
提案し，分析する．このゲームはUCTでは性能が出にくく，また，MABにおける他
のアルゴリズムKL-UCB，Thompson Sampling [1,40,69]をMCTSに応用しても改善
はほとんど無いという意味でMCTSとは相性が悪いということを示す．加えて，この
ゲームでは性能を改善する鍵が，終局時のスコアを利得に変換する過程にあることを
示す．
4章には，多腕バンディットの知見に基づき，最善手を見分けるアルゴリズムと最善を
より多く選ぶことで評価を正確にするアルゴリズムとを適切に使い分けることでMCTS
の性能の改善を目指した手法について記す．実験の結果，提案手法は近くに終局がある
場合に効果的であった．
5章では，終局が近くにあると判明した場合に，その情報をより積極的に用いる手法
を提案する．4章の内容から，既存手法は終局の情報を上手く活かせていないと予想さ
3
--- PAGE 14 ---
れるためである．提案手法は4章の手法と比べて，計算コストが少ない手法である．
6章には，期待値の最大値の推定量の研究について記す．5章の手法は効果的ではあっ
たが，探索局面の近くに終局が無いと効果が無い．推定量を改善することにより一般的
な状況でのMCTSの改善が期待される．6章にはその端緒として，多腕バンディット
問題での推定量の改善について記す．
7章には，6章の推定量を実際にMCTSに応用する研究について記す．
8章には本論文の結論を記す．
4
--- PAGE 15 ---
第2章 モンテカルロ木探索とその関連
手法
本章では本研究で扱う，モンテカルロ木探索（MCTS）を中心に，MCTSの改善の
ために考案された手法やMCTSと関連が深い多腕バンディット問題，探索の対象とし
てのゲーム等についての研究を紹介する．
2.1 グラフ
本研究では，モンテカルロ木探索という木探索アルゴリズムの一種について扱う．木
はグラフの一種であり，木探索アルゴリズムは木を調べるアルゴリズムである．本章で
は，本研究で用いる，グラフ理論の用語について，簡単に説明する．
まずは基本的な定義を紹介する．グラフG（特に無向グラフ）とは形式的には，集
合V と集合E (cid:26) [V]2 に対する，組(V;E)のことである．ここでの[V]2 は元を２つ
含むV の部分集合族である．グラフ理論では，V とE の元をそれぞれ節点，辺と呼
ぶ．Eの元はV の元を２つ含んでいるので，V の２つの元を結んでいるという解釈が
可能である．例えば，a;b 2 V かつfa;bg 2 E なら，節点aと節点bの間に「道」が
存在するという解釈である．グラフ理論では，これを一般化し，道や閉路を定義する．
道P とはP =(V′;E′)となるグラフである．ここでのV′;E′はV′ =fv ;v ;:::;v g，
0 1 k
E′ =ffv 0 ;v 1 g;fv 1 ;v 2 g;:::;fv k(cid:0)1 ;v k ggであり，v 0 ;v 1 ;:::v k は全て異なる節点である．
特にv ;v を道P の端点と呼ぶ．kをv とv の距離と呼ぶ．閉路とは，端点2つを含む
0 k 0 k
辺を道に加えたグラフである．より正確には，上記のV′;E′に対し，(V′;E′[ffv ;v gg)
k 0
となるグラフである．
次に，上記の定義を使って，本研究での使うグラフ理論の用語を紹介する．木はどの
２節点にも道が存在するグラフで閉路が存在しないグラフである．木の内，根付き木
は，木の節点の内の１つを根としたグラフである．根という特別な節点を導入すること
で，根との距離の大小から，「親」，「子」等の概念を導入することが出来る．r 2V を根
とした根付き木T = (V;E)を考える．ある節点n;m (fn;mg 2 E) に対し，nとrの
距離の方がmとrの距離よりも小さい時，節点nを節点mの親と呼び，逆に節点m
を節点nの子と呼ぶ．ある節点n;mが同じ親を持つ時，節点nとmは兄弟と呼ぶ．あ
る節点n;mに対し，nとrを端点とした道(V′;E′)を考える．V′ ∋ mの時，節点m
をnの先祖と呼び，節点nをmの子孫と呼ぶ．子を持たない節点のことを葉と呼ぶ．
5
--- PAGE 16 ---
2.2 ゲームとゲーム木
本研究では，二人零和有限確定完全情報ゲームを主な対象として探索アルゴリズムを
評価する．これは囲碁やチェス等のゲームが属するゲームの種類である．この用語の意
味について簡単に説明する．二人とは，エージェント（プレイヤー）が二人（Maxと
Minとする）であることを意味する．零和とは，プレイヤーの利得の総和が０になるこ
とを意味し，特に，二人ゲームの場合，相手の利得を下げ（上げ）た分だけ，自分の利
得が上がる（下がる）ことを意味する．有限とは，ゲームで可能な行動（手）の列が有
限であることを意味する1．確定とは，確定的なゲームを意味する．非確定的なゲーム
では，ある状態（局面）である手を選ぶとゲームのルールから次の局面の確率分布が定
まり，その確率分布に従い次の局面が決まる．確定的なゲームはその特別な場合で，手
を選ぶと次の局面が一意に決まるゲームである．完全情報とは，各プレイヤーがゲーム
の局面についての情報を全て観測可能であることを意味する．例えば多くのカードゲー
ムに存在する手札は所有するプレイヤーだけが観測可能な情報である．このような情報
が無いゲームが完全情報ゲームである．
ゲーム木とは，ゲームの局面等の状態を節点に，そこから可能な行動（手）を辺に対
応させた木2であり，特に，ゲームの初期局面を根とした根付き木である．非確定的な
ゲーム木では，状態を対応させた節点の他にチャンスノードと呼ばれる節点を追加する
ことが多い．チャンスノードは確率的な状態遷移に対応した節点で，状態と行動の対に
対応する．その対から遷移確率が正の状態へと辺を結ぶことで，非確定的な遷移をゲー
ム木で表現出来る．
本節では，MCTSやその他の探索アルゴリズムの評価に用いられてきた二人零和完
全情報ゲームのゲーム木モデルやgeneralvideogameplaying等について紹介する．尚，
本研究では，確定的なゲームを主な対象としているため，行動・手・辺を選ぶことと次
の状態・次の局面・子節点を同一視している．
2.2.1 ゲーム木モデル
二人零和有限確定完全情報ゲームのゲーム木についての研究として，古くはPearlの
研究 [52]がある．そこでは，確率Pで終端節点にランダムに勝敗を割り当てるという
ゲーム木のモデルを考え，Pの大小と木の深さを深くした場合のゲームの理論値（根か
ら最善を尽くした場合の勝敗）との関係について調べられている．このようなゲーム木
モデルは一般的なゲームの性質の解析だけでなく，探索アルゴリズムの性能の評価にも
用いられてきた．通常のゲームでは手に入らない値，例えば，最善手がどれか，現局面
プレイヤーの優勢度合い等の情報が解析者には手に入るという利点があるためである．
1チェスは３回同じ局面になった場合，プレイヤーの指摘により，引き分けとなるため，実質的に有限で
ある．また，囲碁でも，３コウ等の同一局面になり得る局面が存在するが，ルールにより，プレイヤー合意
のもとで引き分け（正式には無勝負）となるため，実質的には有限である．
2多くのゲームでは，同一局面になるような異なる手順が存在するため，厳密には木でない場合も多いが，
異なる手順で至る局面は異なるとすれば木になる．
6
--- PAGE 17 ---
ゲーム木モデルを使った他の研究として，木探索で深く読むほど手の評価が不確かに
なる病的な状況について調べた研究[49,50]がある．そこでは，P-game（Pearl’sgame）
モデルとN-game（incrementalgame）モデルが用いられた．このN-gameモデルでは，
各節点での各行動に対し(cid:0)1か1の値をそれぞれ確率1(cid:0)p，pで割り当てられる．根で
はMaxプレイヤーの手番とする．ゲーム木の分枝数（合法手の数）はどの節点でも一
定とし，規定深さの節点を終端節点とする．初期局面（根節点）から終端局面（終端節
点）に至るまでの道の辺の値の総和（ゲームのスコア）が正だとその終端局面はMax
プレイヤーの勝ち，負だと負け，0だと引き分けとして勝敗を決める．N-gameモデル
ではこのように勝敗を決めるため，兄弟間の勝ち負けは同じになりやすいという特徴
がある．また，同じN-gameモデルでも，ランダムに辺に値を割り当てることで様々な
ゲーム木ができるという特長がある．このようなモデルを使うことで，個々のゲーム木
に着目して性能を調べることや，個々のゲーム木での性能を平均することで，全体的な
性能について評価することも出来る．SmithはN-gameモデルに基づき生成したゲーム
木等を使って，前向き枝刈りの性能を確かめた [66]．
Kocsis は N-game モデルを一般化したゲーム木モデルを MCTS の評価に利用し
た [42]3．Kocsisのゲーム木モデルについて図 2.1に例を示した．このゲーム木モデ
ルでのMax（Min）の手には[0;127]（[(cid:0)127;0]）を一様な確率でランダムに割り当て
る．この辺の値はその辺を辿る（手を選ぶ）ことでMaxプレイヤーがどれだけ勝ちに
近づくかを表す値であり，根から各節点までの道の辺の値の総和はその節点でのプレイ
ヤーの優勢度合いに相当する．KocsisはこのモデルでMCTSとアルファベータ探索を
行い，最善手を選べたかの割合を調べ，MCTSが優れていることを示した．この木の
内部節点のゲームのスコアの理論値（互いに最善を尽くした場合のゲームのスコア）は
木全体に渡ってミニマックス探索することで判明する．
しかしながら，木全体に渡って探索するのは時間がかかる．FurtakはKocsisのモデ
ルの変種として，より簡便にゲームのスコアの理論値が分かるゲーム木モデルを提案し
た [23]．この変種では，Max（Min）節点から子節点への辺の内，一つだけ値を0とし
て他は負（正）の値をランダムに割り当てる．そのため，値を0にした辺が最善手に
対応し，各節点のゲームのスコアの理論値は，根からその節点までの道の辺の値の和に
なり，根節点の理論値は引き分けとなる．また，一方のプレイヤーが常に最善をとる場
合，もう一方のプレイヤーが一度でも次善手をとるとそのプレイヤーの負けになる．そ
のため，最善手を選ぶ必要性が高いゲーム木モデルといえる．それに対して，N-game
モデルやKocsisのゲーム木モデルに従ってゲーム木を作る場合，一方のプレイヤーが
どんな手を選んでも勝ちになるゲーム木が出来うる．
Furtakのモデルは，前述のとおり，終端まで読むことなく，最善手が予め分かる．そ
のため，最善手を選べたかという絶対的な評価を探索アルゴリズムの評価に使えるとい
う長所がある．一般にチェスや囲碁等の局面数が膨大なゲームでは，最善手は不明で，
3文献[42]では\P-game"と言及されているが，ここで扱われているのはむしろN-gameモデルの変種
である．
7
--- PAGE 18 ---
s s
1 2
s s s s
11 12 21 22
A B C D
図 2.1: Kocsisのゲーム木モデルの例（分枝数と深さは2）．四角と丸はそれぞれMax，
Minプレイヤーの節点を意味する．この例では，終端節点がA，B，C，Dの４つ存在
する．終端節点のゲームのスコアは根からその節点までの辺の値の和である．例えば，
終端節点Aのゲームのスコアはs +s である．節点AでMaxの勝ちか負けかはこ
1 11
のゲームのスコアが正か負かによって決まる．Maxプレイヤーの手に相当する辺の値
はs ;s (cid:24)U([0;127]) かつMinプレイヤーの手に相当する辺の値はs ;s ;s ;s (cid:24)
1 2 11 12 21 22
U([(cid:0)127;0])である．U((cid:1))は一様分布を意味する．N-gameモデルはKocsisのゲーム木
モデルの内，[0;127]（[(cid:0)127;0]）の範囲の値を一様な確率で割り当てる代わりに(cid:0)1か
1の値をそれぞれ確率1(cid:0)p，pで割り当てたモデルである．
現実的な時間では見つけることが出来ない．加えて，局面の優勢度合いについても，エ
キスパートがいる，もしくは，それに準じる評価ができる評価関数がないと判断できな
い．そのため，それらのゲームで探索アルゴリズムの性能を評価する場合，対戦実験を
繰り返し行い，勝率をもとに評価をするのが一般的である．しかしながら，勝率は，対
戦相手と比べた場合の相対的な評価である．つまり，対戦相手がそれなりに妥当な手を
打つという期待がなければ，評価が難しい．その一方で，Furtakのモデルでは，最善
手を選べたかどうかによる絶対的な評価が出来る．特にこのモデルで生成されるゲーム
の局面は根で最善手を選べないと理論値が引き分けから負けに変わるため，最善手を選
べるかが重要な局面である．
他にも長所として，ゲーム木モデル全般に言えることであるが，分析の際に探索空
間の大きさの変更が容易であることが挙げられる．囲碁では9路盤，13路盤，19路盤
等，盤面の大きさを変えることである程度可能だが，多くのゲーム，例えばチェスでは
難しい．
本研究では，Furtak のゲームを特に incremental random game（そのゲーム木を
incremental random tree）と呼び，探索の主な対象とし，探索アルゴリズムの評価に
用いた．
ゲーム木モデルを使った研究が他にも存在する．RamanujanはFurtakのモデルで
節点の種類として，トラップを定義し，解析した．トラップである節点は，その節点か
ら数手で終局する一方で兄弟節点ではまだ終局しない，かつ，ゲームの理論値が親手
番の負け（つまりその節点を選ぶと数手で負けてしまう）という節点である．トラップ
の図を図 2.2に示した．Ramanujanはゲーム木モデルの優勢度合いにノイズを加えた
値が評価関数として手に入る場合のミニマックス探索とMCTSを比較し，木に多くの
トラップがある場合ではMCTSの性能はミニマックス探索と比べて下がることを示し
8
--- PAGE 19 ---
図 2.2: 文献 [58]からのトラップの図（(cid:12)gure 1）の抜粋．この例では，白と黒のプレイ
ヤーが存在し，白が根で手番となっている．点線で囲まれた節点の内，四角の節点は終
端節点であり，黒の勝ちの節点である．根で白が３つ手の内で真ん中の手を選び，黒が
最善を尽くした場合，その後の白の手によらず，白の負けになる．真ん中の手がトラッ
プである．
た [58,59]．
2.2.2 Finnsonnの仮想ゲーム
Finnssonの研究 [22]もまた，ゲーム木モデルを使った研究の一つと位置づけられる．
Finnssonの研究では二人零和有限確定完全情報ゲームに属するいくつかのゲームを提案
し，そのゲームの種類と探索空間の大きさとMCTSの性能の関係を示した．Finnsson
のゲームの一つは 2.2.1節でのゲーム木モデルで，ランダムに割り当てる辺の値を予め
決めたルールで決定的に割り当てる場合に相当する．実験を通じて，MCTSの性能に
とって，古典的なミニマックス探索で重要と知られている探索空間の大きさよりも，次
善手の値の割り当て方法の方が重要であると示した．
2.2.3 General Video Game Playing
General Video Game Playing AI（GVG-AI）は，初見の様々なゲームをプレイし，
上手くプレイするエージェントを作るということを目標としたドメインである．この研
究には，汎用人工知能に向けた知見を得るという意義がある．
GVG-AIコンペティションでは，複数のゲームで，複数エージェントが競い合う．ゲー
ムには勝利（敗北）条件と，スコアとタイムリミットがある．タイムリミットが来たら
ゲームは終了し，勝利（敗北）条件を満たしているかが判定され，勝利か敗北かのどち
らかに決まる．タイムリミットが来ない場合でも，勝利（敗北）条件を満たすとゲーム
9
--- PAGE 20 ---
が終了し，その結果が勝利（敗北）となる．各エージェントがプレイしたゲームの結果
は，勝利したか否か，ゲーム上のスコア，かかった時間で順で優劣をつける，そして，
各ゲーム毎に順位に応じてポイントがエージェントに与えられ，得たポイントの総和が
最も高いエージェントが優勝となる．
コンペティションの内，single player planning trackでは，一人ゲームをそれぞれ
のエージェントがプレイする．エージェントは，ゲーム中の行動を決める際にforward
modelと呼ばれる，状態と行動に対し，次の状態（確率的な遷移をする場合は，確率的
にその遷移先の一つを）返す関数を利用出来る．他にも，NPC，壁等のオブジェクト
の情報（位置，オブジェクトの種類のID等）を利用出来る．エージェントはこれらを
もとに良い行動を見つけることが求めれられる．
2017年現在では，single player planning trackの他にも，2-player planning track
（協力するゲームも含む二人ゲーム部門），levelgenerationtrack（ゲームの生成部門），
single player learning track（forward model無しの部門）がある．
2.2.4 マルコフ決定過程（MDP）
本研究では扱わない対象の内，代表的な探索対象としてはマルコフ決定過程が挙げら
れる．マルコフ決定過程（MDP）は一人ゲームで，非確定性完全情報のゲームに対応す
るドメインに相当し，強化学習での主要な対象である．MDPは4つ組(S;A;T;R)で表
せる．Sは取りうる状態の集合，Aは可能な行動の集合である．T :S(cid:2)A(cid:2)S ![0;1]
は状態遷移確率で，状態sと行動aと状態s′に対し，状態sで行動aをとった時に状
態s′に至る確率を返す．R:S(cid:2)A(cid:2)S ![0;1]は利得関数で，状態sで行動aをとり
状態s′に至った時の利得，あるいは利得の期待値を返す．利得は，[0;1]に限らない実
数をとる場合もある．
MDPには終端状態が存在するものとしないものがあり，特に前者についてより詳し
く記す．終端状態はゲームでの終局に相当するものであり，そこに至るとMDPは終了
し，それ以上利得は得られないという状態である．終端状態が存在するMDPでの状態
行動価値関数Q:S(cid:2)A!Rを
8
<
0 (sは終端節点);
Q(s;a):= ∑
: s′2S T(s;a;s′)(R(s;a;s′)+max a′2A Q(s′;a′)) (sは終端節点でない)
と定義する．状態行動価値関数を使って，このMDPにおける状態sでの最善の行動を
式で表すとargmax a2A Q(s;a)である．
KocsisはMDPに属するsailingdomainで，既存手法に対するUCTの状態空間の大
きさに対する性能の良さを示した [42]．
10
--- PAGE 21 ---
2.3 強化学習と探索
強化学習と探索には密接な関わりがある．あるドメイン（例えばMDP(S;A;T;R)）
を考えて，強化学習する際，その目的は現在の状態から可能な行動をとり，それに対す
る報酬（利得）を得ることを繰り返し，最も未来の期待利得の総和が高いという意味で
最善の方策（状態と行動に対しその確率を返す関数(cid:25) : S(cid:2)A ! [0;1]）を学習するこ
とである．その一方で探索の目的は，探索対象の局面の最善手を見つけることである．
最善手は対象の局面に対し最善の方策が返す行動（手）である．
強化学習のアルゴリズムはドメインのモデルの存在の有無の観点で分類するとmodel-
free，model-basedの２種類の学習が存在する．本研究の主題であるモンテカルロ木探
索はmodel-basedの学習アルゴリズムの一種として位置づけられる．model-freeでは
エージェントはドメインのモデル（遷移確率と利得関数）を持たない仮定のもとで，最
善の行動を学習する [67]4．model-freeのアルゴリズムはモデルが手に入りにくい状況
でも使えるという長所がある．一方，model-basedではモデルが手に入るという仮定の
もとでそのモデルを利用し，先読みして方策を学習する．こちらは，モデルを使う分だ
け観測したデータを効率的に扱えるという長所がある．尚，モンテカルロ木探索の代表
的なアルゴリズムUCTは遷移確率が不明でもgenerative model（2.7に記す）があれ
ば実行可能である．
探索と学習について別の位置づけも可能である．文献 [64,67]でのDynaアーキテク
チャに従って位置づけると，探索は学習したモデルをもとに，実際の行動を決める方法
で，学習は方策の更新方法ではなく，その実際の行動とその結果に基づきモデルを更新
する方法である．
2.4 バンディット問題とそのアルゴリズム
本節では，MCTSと密接な関わりのある多腕バンディット問題とそのアルゴリズムに
ついて紹介する．また， 6章で議論する，期待値の最大値の推定量について紹介する．
2.4.1 多腕バンディット問題（MAB）
多腕バンディット問題（MAB）[44]は強化学習の最も単純なドメインの一つである．
MABを 2.2.4節のマルコフ決定過程（MDP）の文脈で解釈すると，根から一つの行
動を選ぶと終端状態に至るようなMDPであり，MABでのアームの選択はMDPで
の行動の選択に対応する．典型的なMABの設定では，アームはK 本あり，各アーム
1(cid:20)i(cid:20)K（iは整数）には利得の分布が割り当てられる．エージェント（プレイヤー）
は繰り返しアームを引く．アームを引くとそのアームに割り当てられた分布に従って
独立に利得が得られる．利得は[0;1]の間の値をとる．しかしながら，エージェントに
4モデルを学習しないことがmodel-freeという定義も存在する（文献[38]p251）．この定義だと例えば，
モデルを推定するアルゴリズムはmodel-freeではない．
11
--- PAGE 22 ---
とって利得の分布は不明で，そのため，アームiの期待値(cid:22) や分散(cid:27)2も不明である．
i i
エージェントはアームを引くことで，利得一つ一つから分布に関する情報を得る．この
分野の主な研究課題は，アームを引く数を決められたもとで，累積的な利得を最大化す
ること [3]や，期待値最大のアームを見つけ出すこと [2]等がある．
詳しい内容に移る前に記号について定義しておく．X をアームiがt回目に引かれた
i;t
時の利得とする．N をアームiが今までに引かれた回数とする．nをアームを引かれた回
i
∑ ∑
数の総和 (n:= K N )とし，X(cid:22) をアームiの平均利得 (X(cid:22) := Ni X =N )
i=1 i i;Ni i;Ni t=1 i;t i
とする．利得の期待値が最大のアームを(cid:3) := argmax 1(cid:20)i(cid:20)K (cid:22) i とし，利得の期待値の
最大値とアームiの利得の期待値との差を∆
i
:=(cid:22)(cid:3) (cid:0)(cid:22)
i
と表記する．
多腕バンディット問題は単純なため，理論的な解析が進んでいる．この問題で理論的
な性能を議論するためにリグレットという量がよく用いられる．リグレットには２種類
あり，累積的な利得を最大化する観点では，累積リグレット，期待値最大のアームを判
別する観点では単純リグレットが使われる．累積リグレットは利得の期待値が最大（最
善）のアームを引き続けた場合と比べて，利得の観点でどれだけ損をしたかを表す量で
ある．具体的には，
∑
(cid:22)(cid:3)n(cid:0) (cid:22)
i
E[N
i
(n)]
1(cid:20)i(cid:20)K
この累積リグレットが小さくなるアルゴリズムが望ましい．
単純リグレットは最善のアームとアルゴリズムが最善として選んだアーム（J とす
n
る）との期待値の差
∑
P(J =i)∆
n i
1(cid:20)i(cid:20)K
である．ここでのP(J =i)はアームiをJ として選ぶ確率である．
n n
以下，累積的な利得を最大化するアルゴリズムを中心に紹介する．累積的な利得を最
大化するために必要なことは，利得を得るために期待値が高そうなアームを引くこと
と，より良いアームを探すために情報の少ないものを引くことのバランスを上手くと
ることである．以下で紹介するUCB1とKL-UCBは利得の推定値とその不確かさに，
ThompsonSamplingは各アームについての期待値の主観確率分布にそれぞれ基づいて，
バランスをとっている．本節では，他にも強化学習で広く使われているepsilon-greedy,
softmaxについても紹介する．
2.4.2 UCB1
UCB1 [3]はその単純さのため，広く用いられている手法であり，利得の推定値とそ
の不確かさを組み合わせた評価基準であるUCB値を定義し，それが最大となるアーム
を選ぶ．UCB1でのUCB値は具体的には
√
2lnt
X(cid:22) + (2.1)
j;Nj(t) N (t)
j
12
--- PAGE 23 ---
である．UCB値は平均利得の項（第一項）により，有望さを，それに加えられる補正項
（第二項）により，相対的な不確かさを評価している．UCB1は，まず始め全てのアー
ムを一度引き，その後は上記のUCB値が最大のアームを引く．
UCB1アルゴリズムでは，アームを引く回数の期待値について
8lnn (cid:25)2
E[N (n)](cid:20) +1+ (2.2)
i ∆2 3
i
という不等式が成り立つと示されている [3]．
2.4.3 KL-UCB
KL-UCBもUCB1と同様に，UCB値に基づきアームを選ぶ方法であるが，UCB1よ
りも小さな累積リグレットの上界が示されており，実験においてもUCB1よりも優れ
た性能を示した手法である [13]. KL-UCBでは，ベルヌーイ分布間のKL距離を元に
してUCB値を定める．具体的には，２つベルヌーイ分布p;q間のKL距離
p 1(cid:0)p
d(p;q):=plog +(1(cid:0)p)log
q 1(cid:0)q
を用いて
( )
lnt+cln(lnt)
argmaxmax q 2[0;1]:d(X(cid:22) ;q )(cid:20) (2.3)
j2K j j;Nj(t) j N
j
(t)
のアームを選ぶ. ここでのcは定数である．この式は有望さとKL距離を使った相対的
な不確かさを評価していると言える．例えば，あまり引かれていないアームではKL距
離の離れ具合の上界 lnt+cln(lnt) は上昇するので，より高いUCB値となる．UCB1と
Nj(t)
異なる点は，単純に訪問数の大小ではなく，ベルヌーイ利得間のKL距離を用いている
ため，例えば，平均利得が低いほど，相対的不確かさの変化に対して，より鋭敏にUCB
値が変動する等の特徴がある．KL-UCBでは，アームを引く回数の期待値について
lnn
lim supE[N (n)](cid:20)
n!1 i d((cid:22)
i
;(cid:22)(cid:3))
という上界が示されている [13]．次善のアームi ̸= (cid:3)について考えると，この上界は
UCB1のものよりも低い．
2.4.4 Thompson Sampling
Thompson sampling [1,69]は得られた利得をもとに，ベイズ推定をしながら，確率
的にアームを選ぶアルゴリズムである．各アームの利得はベルヌーイ分布に従うと仮定
し，各アームの期待値は，ベルヌーイ分布の共役事前分布である，ベータ分布に従うと
する．各アームjについて，利得が1;0であった回数をそれぞれ(cid:11) ;(cid:12) とする．つまり
j j
平均利得を用いると
(cid:11) =X(cid:22) N (t);
i i;Ni i
(cid:12) =(1(cid:0)X(cid:22) )N (t) (2.4)
i i;Ni i
13
--- PAGE 24 ---
である．このとき各アームjに対し，その期待値の主観確率分布はベータ分布B((cid:11) +
j
1;(cid:12) +1)である．Thompson samplingでは各アームj に対し，ベータ分布B((cid:11) +
j j
1;(cid:12) +1)からサンプルした値が最も高いアームを引く．尚，ベータ分布は(cid:11) （(cid:12) ）が
j j j
大きいほど利得が1 (0) の方に歪んだ分布となり，また，(cid:11) ，(cid:12) ともに大きいと尖度
j j
が高い分布となる．累積リグレットの上界は KL-UCB と同等であると証明され，ま
た，KL-UCBと同様の実験でRegretがKL-UCBやUCB1より低いことが示されてい
る [40]．
2.4.5 Epsilon-greedy
epsilon-greedy法 [67]は確率的にアームを選ぶ手法で，強化学習で広く使われてい
る．具体的には，確率1(cid:0)"でargmax X(cid:22) （推定最善）を確率"で一様にランダム
i i;Ni
にアームを選ぶ.
2.4.6 Softmax
softmax法 [67]もまた，確率的にアームを選ぶ手法で強化学習で広く使われている．
softmax法では，各アームiを確率 ∑exp(X(cid:22) i;Ni =T) で選ぶ．ここでのT は温度パラメー
j exp(X(cid:22) j;Nj =T)
タであり，高い（低い）温度では，高い平均利得のアームがより少なく（多く）選ば
れる
2.5 期待値の最大値の推定量
本節と6章では，2.4.1節で紹介したMABの設定のもと，アームを引いて，得られ
た利得の集合（サンプル）からmax (cid:22) を推定する推定量について考える．そして推定
i i
量を7章でMCTSに応用する．推定量の性能は推定値5(cid:22)^の平均自乗誤差に基づいて評
価することが自然である．平均自乗誤差はバイアスの自乗とバリアンス（分散）に分解
出来るので，バイアスと分散の観点からも評価する．バイアスはBias((cid:22)^)=E[(cid:22)^(cid:0)(cid:22)(cid:3)]，
分散はVar((cid:22)^)=E[((cid:22)^(cid:0)E[(cid:22)^])2]である．バイアスは正に大きいほど過大評価，負に大
きいほど過小評価することを意味し，分散が大きいほど評価が安定しないことを意味す
る．簡単のため，最善のアームはただひとつだけ存在すると仮定し議論するが，この議
論は一般化可能である．理論的な解析では，N は確率変数でないと仮定して行う．こ
i
の仮定は先行研究の類似の解析の際の仮定に習っている [18,72]．しかしながら，エー
ジェントがUCB1，epsilon-greedy，softmax等の実際的なサンプリングアルゴリズム
を使う場合，平均利得X(cid:22) に基づいてアームを引くため，N は確率変数である．そ
i;Ni i
のため，6.3節では，実際に，理論での仮定が満たされない状況での性能について計測
している．
5推定値は推定した具体的な値で，推定量は推定値を出力する関数である．
14
--- PAGE 25 ---
6，7章で扱う期待値の最大値の推定量に関連して，先行研究の５つの推定量，AVer-
ageEstimator(AVE),MaximumEstimator(ME),DoubleEstimator(DE),Weighted
Estimator (WE), 及び MixMax estimator (MM)について紹介する．
2.5.1 Average Estimator (AVE)
AVEは観測した全利得の平均をとることで(cid:22)(cid:3)を見積もる．
∑K
N 1
∑K ∑Ni
(cid:22)^ := iX(cid:22) = X :
AVE n i;Ni n i;t
i=1 i=1 t=1
UCTでは，行動をUCB1 [3]に従って選ぶが，その行動の価値X(cid:22) は子の推定値を
i;Ni
もとにAVEで推定されている．
AVEは各アームをどれだけ引くかに強く依存する手法である．その結果，全てのアー
ムの期待値が等しい時を除き，アームを引く回数が増えても，アームの引き方次第で
は推定値が正しい値に収束しないという欠点がある．例えば，各アームiを引いた回数
N がΩ(n)となる時，nが大きくてもAVEのバイアスは0に収束しない．
i
全てのアームに対して，最善のアームを引く回数の割合が1に収束するような場合
にAVEによる推定は収束性の面で妥当である．例えば，UCB1で引くアームを決める
場合である．その場合，次善6のアームを引く回数の期待値O(logn=n)で抑えられる．
このことは，nが増えるにつれて，平均的には最善のアームを引く割合が1に収束する
ことを意味する．しかしながら，収束する前については，少数のアームが高い期待値
であり，その他のアームの期待値低い場合に，AVEによる推定は過度に低い値となる．
また，MCTSの文脈では，どこからシミュレーションするかの選択と行動の価値の推
定は分けた方が良いという報告もある [21]．
∆
i
:=(cid:22)(cid:3) (cid:0)(cid:22)
i
とするとAVEのバイアスと分散は以下の性質を持つ．
∑K
N
Bias((cid:22)^ )=(cid:0) i∆ ;
AVE n i
i=1
∑K
N
Var((cid:22)^ )= i(cid:27)2:
AVE n2 i
i=1
2.5.2 Maximum Estimator (ME)
MEは(cid:22)(cid:3)を平均値の最大値によって見積もる．m:=argmax
j
X(cid:22)
j;Nj
とすると
(cid:22)^ :=X(cid:22)
ME m;Nm
である．MEのバイアスは正であり，上界があることが知られている [4]．
v
u
u tK(cid:0)1 ∑K (cid:27)2
0(cid:20)Bias((cid:22)^ )(cid:20) i :
ME K N
i
i=1
6本論文では，次善とは最善でないという意味で使う．
15
--- PAGE 26 ---
特に，全てのアームの期待値が等しい時，バイアスは大きくなる．分散の上界について
も，先行研究により
∑K
(cid:27)2
Var((cid:22)^ )(cid:20) i
ME N
i
i=1
と示されている [72]．
2.5.3 Double Estimator (DE)
DE [71]は強化学習でのQ学習の改善のために提案された推定量である．Q学習で
は，各推定値は推定量MEで更新されるが，MEは過度に大きく推定するために上手
く行かないドメインがある．そこで提案されたのがDEであり，DEではその問題は生
じない．
DEは利得の集合を重複のない２つのサブセットに分ける．一つのサブセットを使い，
各アームiの平均利得X(cid:22)(1) を計算して，期待値(cid:22) を推定をする．もう片方のサブセッ
i;Ni i
トを使い，m(2) = argmax X(cid:22)(2) を計算して，(cid:3)を推定する．これをサブセットを入
j j;Nj
れ替えてもう一度行いX(cid:22)(2) とm(1)を計算する．DEはこれらの推定を統合し，
i;Ni
1
(cid:22)^ := (X(cid:22)(2) +X(cid:22)(1) )
DE 2 m(1);N m(1) m(2);N m(2)
により最大値を推定する．N(1);N(2)をそれぞれ１，２つ目のサブセットでのアームi
i i
を引いた回数とすると，バイアスと分散については以下の通り，負のバイアスを持ち，
分散に上限があることが知られている [72]．
0v v 1
u u
(cid:0) 1@
u
t
∑K
(cid:27) i 2 +
u
t
∑K
(cid:27) i 2 A <Bias((cid:22)^ )(cid:20)0;
2 N(1) N(2) DE
i=1 i i=1 i
( )
1
∑K
(cid:27)2 (cid:27)2
Var((cid:22)^ )(cid:20) i + i :
DE 4 N(1) N(2)
i=1 i i
2.5.4 Weighted Estimator (WE)
WE [18]は(cid:22)(cid:3)を各アームの平均利得の重み付き和で推定する．重みは各アームiが
最善であるかどうかの主観確率で決まる．Dを利得の集合とすると，WEの推定値は
∑K
(cid:22)^ := P(i=(cid:3)jD)X(cid:22)
WE i;Ni
i=1
である．バイアスについて，WEはME（正）とDE（負）の間であるという望ましい
性質を持つ7．
Bias((cid:22)^ )(cid:20)Bias((cid:22)^ )(cid:20)Bias((cid:22)^ )
DE WE ME
7これはバイアスについて，MEとDEの内0から遠い方と比べて，WEの方が0に近いことを意味し，
望ましい性質である．しかし，MEとDEの内0に近い方と比べて，WEが0に近いかは場合による．
16
--- PAGE 27 ---
また，分散についても以下が成り立つ．
∑K
(cid:27)2
Var((cid:22)^ )(cid:20) i
WE N
i
i=1
しかしながらWEは最大値についての分布を複数の分布から計算するため，計算コス
トが高い．そのため，計算コストが低い推定量を 6.2節で提案する．
2.5.5 Mixmax Estimator (MM)
MM [36]はAVEとMEの推定値の重み付き和をとるという推定量である．
(cid:22)^(cid:21) :=(1(cid:0)(cid:21))(cid:22)^ +(cid:21)(cid:22)^
MM AVE ME
ここでの(cid:21)(0 < (cid:21) < 1)は重みである．MMはUCTでMario AIを動かす際に，過度
に慎重になるという傾向を改善するために提案された手法である．UCTで用いられて
いる推定量AVEでゲームの局面を評価する場合， 2.5.1節に記したように，その局面
から悪い行動をとった場合の評価により，その局面の評価が過度に低く見積もられる．
その結果として，上手く良い行動を選び続ければ，敵にぶつからずに進めるが，敵にぶ
つかってしまう行動が多い局面で，本来は敵を避けてすぐに進むべきであるのに，立ち
止まってしまうということが観測されていた．その悪さを軽減するために提案された推
定量である．MMのバイアスもまた，AVE（負）と ME（正）の間にあるという望ま
しい性質がある．しかしながら，MMにはAVEとMEの悪さが(cid:21)の大きさに応じて
存在する．つまりMMにのバイアスについて，AVEと同様にサンプルのとり方次第で
最適な値に収束せず，(cid:21)が1に近いほど，バイアスは正に外れる．
2.6 モンテカルロ木探索（MCTS）
木探索とは外界のモデルを使って，現在の状態からあり得る状態を計算していき，最
善の行動を見つける方法であり，人工知能における主題の一つである．MCTS [10]は
木探索の一種で，その中でも最良優先探索の一つである木探索アルゴリズムでは，探索
木と呼ばれる，ゲーム木の部分木を作り，探索のための各種の値を保持する．最良優先
探索は何らかの評価基準に従って，葉節点の良さを定義し，最も良い葉節点を優先して
展開する方法で，MCTSの他にA*探索やダイクストラ法が相当する．MCTSは，良さ
の評価をランダムシミュレーション（ランダムに終局まで手を選ぶ）で行い，その結果
が良いところを優先して展開する．最良優先探索アルゴリズムは図 2.3の４つのステッ
プを繰り返す．
17
--- PAGE 28 ---
1. 葉の選択：探索木の葉の内，最も優先度の高い葉を選ぶ．
2. 展開：必要であれば，葉が展開される．展開では，その葉の子節点を新たに探索
木の葉とし，もとの葉は探索木の内部節点とする．展開したら，新たに探索木に
加えた葉の内から葉を選ぶ（例えば，MCTSでは新たに加えた葉を１つ選び，A*
探索では新たに加えた葉を全て選ぶ）．
3. 評価：選んだ葉を評価する（例えば，MCTSではシミュレーションして評価し，
A*探索ではヒューリスティック関数を使って評価する）
4. 更新：評価した値を木全体で共有する．
図 2.3: 最良優先探索の概要
MCTSの研究において，シミュレーション，プレイアウト，ロールアウトは同じ意
味で使われることもあるが，本研究では，探索木の葉から終端節点まで，ランダムに
手を選ぶことをシミュレーションと呼び，選択のステップで探索木の根から葉まで選ぶ
こととそこからシミュレーションすることを合わせてプレイアウトと呼ぶことにする
（図 2.4）．尚，一般にターン制ゲームのプログラムでは，相手のターンでの思考時間も
無駄にしないように探索するということが行われている．その場合，根では相手の手番
である．簡単のため，本研究では根で手番のプレイヤーの探索方法に限って述べている．
根節点
探索木
葉節点 プレイアウト
シミュレーション
終端節点
図 2.4: プレイアウトとシミュレーション
2.6.1 UCT
UCTはモンテカルロ木探索（MCTS）の代表的なアルゴリズムである．UCTでは，
ゲームの手を多腕バンディット問題（MAB）でのアームとしてモデル化 [3]し，手iの
利得はその手の利得分布に従うとみなしている．以下，UCTのアルゴリズムについて
図 2.3に従って説明する．
図2.3の１つ目のステップである葉の選択は，各節点でMABのアルゴリズムUCB1[3]
に従いUCB値を計算し，その値が最大の手を選ぶことを繰り返して行う．プレイアウ
トt回行った際，今までに節点jを選んだ回数（訪問数と呼ぶ）をN (t)とする．プレ
j
イアウト回数を明示的に示さなくても，説明に不都合がない場合，簡単のためN と略
j
18
--- PAGE 29 ---
記する．X(cid:22) を節点jの利得（jの親節点の手番から見た利得）の推定値（UCTでは
j;Nj
平均利得），とするとUCB値
√
lnn
X(cid:22) +2C (2.5)
j;Nj p N
j
が最大の子節点を選ぶ．ここでのC は十分大きなnに対して，任意の(cid:14) >0で
p
√
P(nX(cid:22) (cid:21)E[nX(cid:22) ]+C nln(1=(cid:14)))(cid:20)(cid:14); (2.6)
j;n j;Nj p
√
P(nX(cid:22) (cid:20)E[nX(cid:22) ](cid:0)C nln(1=(cid:14)))(cid:20)(cid:14) (2.7)
j;n j;Nj p
を満たすパラメータである．C = p1 の時，式 (2.5)のUCB値は式 (2.1)のUCB1で
p
2
のUCB値と同じになる（利得を[0;1]でなく，[(cid:0)1;1]とすることも出来る．その場合
p
はスケールが２倍になるので，C = 2の時にUCB1と同じである）．C を小さくす
p p
ると，推定値がその期待値から離れることはまれという想定のもとで探索することにな
る．これは，利得の推定値が良い子を優先的に選ぶというUCB1の性質の優先度合い
を高めることに繋がり，結果として，利得が高いところでより重点的にシミュレーショ
ンを行うことになる．各手をMABのアームとみなして考えると，MCTSでは，その
先でどこからシミュレーションするかはその時々で異なるため，アームの利得分布が非
定常であるMABに相当する．そのため，MCTSではX(cid:22) の期待値E[X(cid:22) ]まわり
j;Nj j;Nj
の挙動について，MABより性質の悪い問題を扱っているといえる．
図 2.3の２つ目の展開のステップでは，葉を訪問したら展開する方法 [6,8,17,19]と
展開閾値を設け，訪問数が展開閾値になったら展開する方法がある．尚，前者で展開す
る場合と後者で展開閾値を2とした場合とでは多少異なる挙動となる．前者では，展開
されるのが二度目の訪問時の場合（新たに葉になった時に選ばれた場合）と一度目の訪
問時の場合（新たに葉になった時には選ばれなかった場合）があり，後者では二度目の
訪問時の時のみ展開するためである．一般に展開閾値を高く設定することで，探索木の
成長を遅らせて，メモリを節約することが出来る一方で，展開していれば保持していた
はずの情報を活用出来ないというトレードオフが存在する．本論文では，特に断らない
限り，前者を採用した． また，探索の始め，探索木は根とその子節点だけを含んだ状
態とした．
図 2.3の３つ目の評価のステップでは，シミュレーションを行う．シミュレーション
ではゲーム木の終端節点まで一様な確率で手を選び，到達した終端節点の勝ち負けに応
じて利得を得る．利得は勝ち，引き分け，負けでそれぞれ1;0:5;0とした（7章ではプ
ログラムの都合上，利得はそれぞれ1;0;(cid:0)1としている．この場合はスケール２倍にな
るため，C の値も２倍するのが自然で，本研究でもそうした．）利得は離散値1;0:5;0
p
をとるとしているが，代わりに[0;1]の任意の値を使うことも可能である．オセロや囲
碁等のいくつかのゲームではゲームのスコア（オセロでの黒・白の枚数，囲碁での目
数）を勝ち負け以外に利用することも出来る．歴史的には，多くの囲碁プログラムが線
形にスコアを利得に変換して，勝ちや負けの中でもより良いものとそうでないものを
19
--- PAGE 30 ---
区別しようとしてきた．しかしながら，対局での強さは離散値（勝ちと負けがはっきり
と違う値）を使うことで，大いに改善した．最近では，多くの囲碁プログラム（例えば
fuego [19]）では離散値を使っている．本研究では，これに習い，ゲームのスコアが分
かる場合でも，勝ち負けに応じて利得を与えた．また，シミュレーション中の方策（手
の選び方）について，一様な確率で手を選ぶのではなく，ドメイン固有の知識を使い，
確率を偏らせた方が性能が上がるという報告もあるが，本研究ではドメインの知識は使
わず一様な確率で手を選ぶとした．
図2.3の４つ目の更新のステップでは，各節点の平均利得と訪問数を更新する．UCT
では各節点で，その節点自身と子孫から行ったシミュレーションの平均利得と訪問数を
管理する．尚，平均利得と訪問数の代わりに勝ち数，負け数，引き分け数を管理しても
等価である．更新は節点jが選ばれた場合，シミュレーションでの利得（jの親で手番
のプレイヤーにとって）をrとすると
X(cid:22) N +r
X(cid:22) j;Nj j (2.8)
j;Nj+1 N +1
j
N N +1 (2.9)
j j
と行う．節点自身とその子孫から行ったシミュレーションの平均利得は，子の値を使っ
ても更新が可能である．UCTにおける，内部節点j の値の更新は，C を節点j の子，
j
r とn をそれぞれ，節点jから行われた（節点jが葉であった時に行われた）シミュ
j j
レーションの平均利得とその回数とする．節点jの子C の訪問数と平均値は更新済み
j
であること，及び，手番が変わることによる利得の反転（プレイヤーにとっての利得が
rの時，その相手にとっての利得は1(cid:0)r）に注意すると，更新式は
∑
N N +n (2.10)
j k j
k2Cj∑
X(cid:22) N +r n
X(cid:22) 1(cid:0) k k;Nk k j j (2.11)
j;Nj N
j
と表すこともできる．つまり（r とn を除いて）その節点の子の推定値を子の訪問数
j j
で重み付けした和（ 2.5節で紹介したAVE）に相当する．
UCTはこれらの４つのステップを繰り返し，各手の評価の精度を高めるアルゴリズ
ムである [42]．UCTでは，根の最善手として，最大訪問数の手，平均利得が最大の手，
平均利得のlower con(cid:12)dence boundが最大の手を選ぶ等の方法があるが，本研究では，
最大訪問数の手を最善とした（5章では訪問数の修正する関係から平均利得最大の手を
最善とした）．UCTはこのように徐々に精度を高めていくアルゴリズムであるから，い
つ探索を打ち切っても，最善手としてそれなりに良い手を返すことが出来る，anytime
アルゴリズムである．
UCTは上記の通り，UCB1を葉の選択で用いているため，UCB1の性質により，利
得が高いところでより重点的にシミュレーションを行う．そのため，各節点の利得を平
均で推定しても，シミュレーション数に応じて正確になると期待される．実際に十分に
20
--- PAGE 31 ---
探索木が成長した状態で十分にシミュレーションすれば利得の推定値が利得の理論値
（互いに最善を尽くした場合の利得）に収束するということが示されている [42]．
しかしながら，多くの場合，時間等の計算資源に制限があるという前提のもとで最善
手を判別する必要がある．最善がどの手かはその手の利得の推定値をもとにして判断す
る必要があるため，利得の理論値を正確に素早く推定するのが肝要である．しかし，ど
のような値を利得の推定値として用いるべきかは，まだ十分に理解されているとは言え
ず，議論の余地がある．これについては7章で議論する．
尚，MCTSの研究の中には，単純化のために探索木は固定して，解析を行って有効性
を示した手法もあるが [39,68,70]，この設定での実験結果だけでは，性能の評価として
は不完全であると考える．なぜなら，典型的なゲームでは，局面の数は膨大な一方で，
探索資源は限られており，全て展開済みの状態から探索することは難しいため，探索の
過程での葉の展開を組み込むことが重要であると考えるためである．
囲碁等，多くのゲームには，指し手の順序が異なっても同じ局面に至る場合がある．
この場合，古典的なミニマックス探索では，同じ局面の評価を繰り返すことを避ける
ため，transposition tableと呼ばれる表で，局面の評価を保持するという工夫をするの
が一般的である．しかしながら，MCTSの場合，指し手の順序が異なる局面は，同じ
局面であっても異なる局面として扱うことが一般的である．但し，指し手の順序が異
なっても同じ局面に至るという状況の扱いを工夫し，効果を上げた手法も考案されてい
る [15]．
2.6.2 MCTS-Solver
MCTS-Solverは探索木中の終局（終端節点）での勝ち・負けの情報を有効に活用し
て，無駄な探索を減らす方法で，もとはLines of Actionというゲームのために考案さ
れた手法である [74]．プレイアウト結果の更新は通常のUCTと同様に行うが，加えて
ゲームの理論値が勝ち・負け・引き分け・不明かを更新する．具体的には，もし，Max
(Min)手番の節点iのある子節点が，終端節点で，Max(Min)プレイヤーの勝ちとなる
なら，節点iは実質上，終端節点でMax (Min) プレイヤーの勝ちである．また，Max
(Min)手番の節点iの全ての子節点で，Max(Min)プレイヤーの負けであるなら，その
節点は実質上，終端節点でMax (Min) プレイヤーの負けである．このようにして，各
節点から互いに最善を尽くすと勝ちか負けか引き分けか（勝ち確定か負け確定か引き分
け確定か）を計算する．そして，手番にとっての負けに至る手は探索中選ばないように
している．このようにすることで，探索の効率化ができるとともに，負けに至る手の訪
問数が他の手と比べて小さくなるため，通常のUCTと比べて，利得の推定値が素早く
真の値（理論値）に近づくことを期待できる．尚，文献 [74]では手の選択の際，UCB
値に手のカテゴリに基づいた値を付加したものを用いるが，本研究のゲーム木では，特
に手にカテゴリはないので，式(2.5)をそのまま用いた．
MCTS-Solverを使った場合，葉以外の更新をUCTと同様に式(2.11)で表せる．し
21
--- PAGE 32 ---
かし，最善手の値を利用して親，そして祖先の評価をすることが理想的である一方で，
MCTS-Solverでは，手番の負けが確定した手，つまり，最善で負けの手しかない場合
を除き，最善でないと確定した手の古いプレイアウト結果が親・祖先の評価に引き続き
使われてしまっている．ここに改善の余地があると考えられ，この観点から 5章では
改善を試みている．
2.6.3 Accerated UCT
Accerated UCTはUCTの変種で，最近行ったプレイアウト結果をより重要視する
手法である．囲碁，オセロ，havannahで応用され，UCTよりも勝率の面で優れた結果
を示した手法である [29]．最近行ったプレイアウトを優先する理由は，探索が進むにつ
れて，UCB1の性質から推定値が良いところをより訪問することと，真の最善手に対
する推定値が他の手の推定値より良くなると期待されることの二点による．Accerated
UCTではUCTのプレイアウト結果の更新の部分を変更し，親の利得の推定値を算出
する際に子の推定値の重み付き和をとる．t+1回目のプレイアウト終了後，まず，葉
の値を式(2.8)(2.9)で更新する．t+1回目のプレイアウトで節点iが訪問されたとする
と，iの子jが選ばれたなら1そうでないなら0を意味する項I fj is chosengを用い
t+1
て，子jの重みはv を
j
v v (cid:21)+I fj is choseng
j j t+1
という形で更新する．ここでの0 < (cid:21) (cid:20) 1は重みの減り方を調整するパラメータであ
る．従って，節点iを訪問したにもかかわらず，子jが訪問されないとその子jの重み
が下がる．
Accerated UCTでは子節点だけでなく，展開前（その節点が探索木の葉であった時）
のシミュレーション結果に対しても，重みを考慮する．方法としては，探索木の葉iの
展開の際，子の他に仮想的な子を作り，iから行われた今までのシミュレーションの平
均利得X(cid:22) を仮想的な子c に入れて，その子の重みをv =N にしておく．尚，2.6
i;Ni i ci i
節の探索木の成長の仕方ではN は1または0になる．この仮想的な子c は訪問されな
i i
いため，徐々に重みが減るという特徴がある．節点iの子の集合をC とすると，子の
i
重みv を使い，iの利得の推定値は
j
∑
v X(cid:22)
X(cid:22) 1(cid:0) j∑2Ci [fci g j j;Nj (2.12)
i;Ni v
j2Ci [fci g j
という形で更新される．この更新では相手番にとっての利得とするために正負反転し1
を加えている．尚，この更新は(cid:21)=1の時にはUCTと同じになる．加えて，Accerated
UCTでは平均利得の調整だけを行うので，UCB値の第二項はそのまま用いる．文献[29]
ではAcceratedUCTでの終端節点の扱いについて特に述べられていない．本研究では，
終端の節点扱いとして，(cid:21) = 1の時にMCTS-SolverをUCTと組み合わせたものに一
致するように，勝ち負けが確定しても葉の値は，古いプレイアウト結果を利用し，式
(2.8)(2.9)で更新した値を用いた．
22
--- PAGE 33 ---
2.7 その他のMCTSアルゴリズム
本節では，UCTほど実用で使われていないが，理論的な性能が比較的明らかになっ
ている点で優れているMCTSについて紹介する．理論的な性質の解析は，ドメインに
よらない性能を保証することに繋がるため，重要である．それに加えて，MCTSに対
する深い理解を得られる，しかしながら，理論的に優れた手法が，実験的な性能も優れ
ている訳では無いと経験的に知られている．理論的に優れ，実験的にも優れたMCTS
を考案することは今後の課題である．
2.7.1 Sparse Sampling (SS)
Sparse Sampling (SS) [41] は，MDPでの状態価値推定（そこから最善を尽くした
場合の利得の期待値）を目的としたアルゴリズムである．SSは最良優先探索ではない
が，UCTの元となったアルゴリズムの一つである．
SSでは各状態で各行動を規定回数（C 回）を再帰的に行う．具体的には根から実行
し，行動をとって至った次の状態でもC回実行するというように再帰的に実行する．そ
のようにして，状態遷移確率，利得の推定を行う．この手法で特筆すべきなのは，十分
な精度で状態価値推定を行うのに必要なサンプルサイズCは，MDPの状態数には依存
しないということが理論的に示されている点である．これは，滅多なことでは遷移しな
いような状態が多数存在しても，SSでは，それらをあまり考慮しないことにより効率的
に状態価値が推定できるということである．例えば，古典的な方法の一つであるvalue
iterationでは，全状態を辿る必要があるため，状態数に比例する計算時間がかかる．
SSでは，状態遷移確率が手に入らなくても，状態，行動対に対し，次の状態がサン
プル出来れば良い．つまり，状態遷移確率の代わりに次の状態を生成するためのモデル
（生成モデル）があれば良い．この利点は，例えば，複数個の確率変数の最大値をパラ
メータとして次の状態の確率分布が定まる場合に活かせる．なぜなら，この場合，最大
値の分布の計算にはコストがかかるために，状態遷移確率を計算するのは容易ではない
一方で，最大値の分布からサンプリングするのは容易なためである．この生成モデルさ
えあれば実行可能という良さは，SSだけでなく，UCTを含む多くのMCTSアルゴリ
ズムに共通している．
2.7.2 BRUE
BRUE [21]はUCTとは異なり，シミュレーションをどこから実行するのかと手の評
価を分けるアルゴリズムであり，単純リグレットが定数bに対しO(exp((cid:0)bn))で抑え
られるという収束性能の保証を持つ．木での単純リグレットはMABの単純リグレット
を一般化したもので，根から最善を尽くした場合の利得の期待値とアルゴリズムが選
ぶ根の行動の後，最善を尽くした場合の利得の期待値の差である．2.2.4節で紹介した
23
--- PAGE 34 ---
MDPの記法を用いて，根の状態をs ，アルゴリズムが選ぶ行動をa とし，単純リグ
0 0
レットを式で表すとmax Q(s ;a)(cid:0)E[Q(s ;a )]である．尚，単純リグレットの式で
a 0 0 0
は，行動a は探索の結果（確率的）により変わるため，期待値をとっている．
0
UCTの単純リグレットの上界について，プレイアウト数が十分に大きい時の減り方
と，特定のゲーム木で減るまでにかかる平均的なプレイアウト数の両方が解析されてい
るが，それらの観点ではBRUEの方が優れている．前者については，UCTのTheorem
6 [42]で次善手を選ぶ割合は多項式のオーダーであると示されている．そのため，単純
リグレットの減り方も多項式のオーダーであり，前述の上界が示されているBRUEの方
が良い．後者について，UCTで最善な終端節点（根から最善手を選び続けて至る終端節
点）を初めて訪問するまでに平均的には木の深さのhyper-exponentialのオーダーのプ
レイアウトが必要な木が示されている [16]．そのため，少なくともhyper-exponential
なオーダーでプレイアウトを行わない限り，UCTの単純リグレットの上界は小さくな
らない．これに対してBRUEでは，指数オーダーのプレイアウトで済むということが
示されており，UCTで必要なプレイアウト数よりずっと小さいという利点を持つ．
しかしながら，BRUEの解析では，探索木を固定した状況を考えており，現実的な
想定ではない．また，BRUEは一様な確率でのプレイアウトを行い，現状での推定最
善の方策をとった場合の利得をサンプルするというアルゴリズムである．一様な確率で
のプレイアウトというのは，UCTの様に，今までに得た利得に基づき，プレイアウト
の方策を有望そうなところを重視するということを行わないということである．その
ため，二人零和ゲームを始めとしたゲームでの実際的な性能（限られたプレイアウト回
数での性能）はあまり期待できない．但し，シミュレーションと評価を分けるという方
法は有効である可能性がある． 7章では，UCTでの手の期待利得の推定をシミュレー
ションの平均利得で行う代わりに，6章での推定量を使うという形で，シミュレーショ
ンと評価を分けたMCTSを提案する．
2.7.3 単純リグレットの最小化に向けた研究
上記の他にも，最近では，MCTS探索での単純リグレットを減らす方法について理
論面で研究が進んでいる．文献 [25]では，二人零和完全情報ゲームでのMCTSの探索
木を深さ2で固定した場合に相当する，maximinのバンディット問題を考案し，その問
題でのアルゴリズムを提案し，解析した．より具体的には，maximinのバンディット問
題は，2つの行動i2f1;:::;Kg;j 2f1;:::;K gを選ぶとそのi;jに応じた分布に従い
i
利得を得る設定のもと，目的は
arg max min (cid:22)
i;j
i2f1;:::;Kgj2f1;:::;Ki g
を見つけることである．ここでの(cid:22) は行動列i;j に対する分布の期待値である．文
i;j
献 [25]では，提案したアルゴリズムについて，(cid:14)-PAC（" (cid:21) 0と(cid:14) 2 (0;1)に対して，
アルゴリズムで選んだ手の利得の期待値の理論値と，最善手のものとの差が"以下にな
24
--- PAGE 35 ---
る確率が1(cid:0)(cid:14)以上）となるのに必要なプレイアウトの数について理論的に解析してい
る．(cid:14)-PACは言い換えると木における単純リグレットが"以下になる確率が1(cid:0)(cid:14)以上
になることである．
文献 [39]では，maximinのバンディットを拡張し，2つの行動ではなく，いくつかの
行動を選ぶと確率的に利得が得られる木でのアルゴリズムを提案し理論的な解析をし
ている．提案されたアルゴリズムは各葉で平均利得の不確かさの上界，下界を考え，そ
れに基づき探索をする．このアルゴリズムでも，上記と同様（(";(cid:14))-correctと言及され
ている）探索における単純リグレットが確率1(cid:0)(cid:14)以上で"以下になるという条件を満
たすために，必要なプレイアウト数について理論的に解析している．この論文の設定
では，MCTSの探索木を成長させない場合に相当する．もし仮に木を成長させていく
場合，葉からの開始したシミュレーションの数がそれほど大きくなる前に葉が展開さ
れる．そのため，葉の平均利得の不確かさが常に大きい値になってしまう．従って，文
献 [39]のアルゴリズムは，探索木の成長を考えると，非常に緩い上界と下界に基づき，
探索することになるという弱点がある．
2.8 探索ヒューリスティックの動的な調整
本研究では，MCTSにおける「次善手の偏り」という難しさを3節で扱う．3節で用
いるDynamic komiという手法を中心に，他の難しさについてどのように対処してい
るかについて紹介する．一般に，可能な行動の数，行動後の遷移先の数が大きい場合に
最善の行動を見つけるのは難しい．
2.8.1 Dynamic Komi
3章と関連の深いDynamickomiという手法を紹介する．Dynamickomi[5]はゲーム
のスコアがエージェントに手に入るゲームを対象とした手法で，MCTSアルゴリズム
におけるシミュレーションの結果を適切に補正することで，より効果的に探索すること
を目的としている．尚，囲碁において，ゲームのスコアは目数とコミの和に相当する．
Dynamic komiが提案された背景として，MCTSはハンディキャップをもらってもそ
れを上手く活かせないということがある．例えば，囲碁で，平手（ハンディキャップ無
し）の場合と比べて，置き碁（ハンディキャップ有り）が苦手であると知られている．
置碁のように一方のプレイヤーが有利な状況では，有利な側は勝ちをより確実なもの
にする手を，不利な側は不利な状況を拮抗した状態に近づける手を指すことが求めら
れる．しかし，UCTで手を決定すると有利な状態から互角に，不利な状態からより不
利な状態になってしまうことがある．このことの主な原因は，有利な場合はどんな手で
もプレイアウト結果はほぼ勝ちで，不利な場合はその逆となり，各手の評価に差が付か
ず，その結果，悪い手を選びやすくなってしまうことにあると考えられる．
25
--- PAGE 36 ---
Algorithm 1 Score Situational（一部簡略化）
Require: score is the average score of all playouts
phase BoardOccupiedRatio +s
rate 1=(1+exp(c(cid:1)phase))
threshold threshold + rate (cid:1)score return threshold
Dynamic komi [5]は勝ち負けの境界（普通はゲームのスコアが0が境界）を調整す
ることで，この悪さを和らげる手法である．通常のUCTでは，ゲームのスコアを勝
ち負けに変換し，勝ち負けに応じて利得を決める．文献 [5]では，Score Situational，
Value Situational，Linear Handicapという３種類のDynamic komiが提案手法されて
いる．Score Situational（Algorithm 1）は境界を平均スコアへ移動させる手法である．
cとsはパラメータである．\BoardOccupiedRatio"は囲碁ではゲームの進行度合いを
表しているため，今までの手数を対応させることで一般のゲームでも適応できる．Value
Situational（Algorithm2）は全シミュレーションでのエージェントの平均利得（X(cid:22)）が
[X ;X ]に収まるように境界を移動させる．境界の位置を安定させるため，\Ratchet"
lo hi
という変数が導入されている．\Ratchet"は始めは1である．Linear Handicapは囲
碁のルールに依存するため，一般化が難しく，本研究の対象とはしていない．
文献 [5]中の両アルゴリズムでは，それぞれs = 0:75;c = 20; red = 0.45, green =
0.50が用いられている．尚，前者では，s;c を十分大きく設定し，後者では，redを0
にgreenを1に設定することで通常のUCTと同じ振る舞いになる．また，両アルゴリ
ズムでは，初期局面から20手未満の局面で探索を行う時，例外として置き石に応じて
線形にコミを加える．しかし，本論文で扱う状況では置き石に相当するものは無いの
で，省いた．
一般に利得は必ずしも離散値0;1である必要は無く，ゲームのスコアに対し線形に利
得を与えることも可能である．しかしながら，囲碁では，スコアではなく勝ち負けを
扱った方がより効果的であると知られている [6,19]．その一方で勝ち負けの質に対する
さらなる調査がなされ，1;0:5;0以外の利得を試した研究もある [54]．3章では前者の
立場から勝ち負けの境界の調節の方法について議論している．
2.8.2 シミュレーションからの学習
UCTを始めとしたMCTSでは，シミュレーション方策は一様な確率で手を選ぶだけ
であった．しかしながら，シミュレーション結果を元にシミュレーション方策を学習す
ることも可能であり，実際にGeneral Game Playing (GGP)を対象とした研究 [8]や囲
碁を対象とした研究[27]が存在する．これにより，探索木が成長しきらないような条件
（時間的，メモリ的な制約）でも，良い行動がとれるようになる可能性がある．GGPを
対象とした研究については特に比較はなされていないが，囲碁を対象とした研究につい
ては，シミュレーション方策の学習を行う方が，ランダムなシミュレーション方策より
26
--- PAGE 37 ---
Algorithm 2 Value Situational（一部簡略化）
Require: X is the observed winning rate.
if X < X then
lo
if threshold > 0 then
Ratchet threshold
end if
threshold threshold (cid:0)1
else
if X > X and threshold < Ratchet then
hi
threshold threshold +1
end if
end ifreturn threshold
効果的であったという報告されている．
2.9 ハイブリッドな探索手法
本節ではMCTSにおけるハイブリッドな手法を紹介する．ハイブリッドな手法では，
根（付近）で，単純リグレットを最小化する手法により探索資源を分け，そしてそれを
正確に行うために，累積リグレットを最小化する手法を使い，各節点の評価の精度を高
める．
2.6節で紹介した通り，MCTSの代表的なアルゴリズムUCTはMABでの累積リグ
レットを最小化を目指したアルゴリズムUCB1をMCTSに応用したものである．その
ため，推定値の良い手を優先し，シミュレーションを行う葉を選択する．しかしながら，
探索の目的は最善手を判別すること（ゲーム木での単純リグレットを最小化すること）
である．従って，最善手の判別に役立つ葉からシミュレーションを行い，木での単純リ
グレットを小さくするアルゴリズムが理想的である．
しかしながら，木での単純リグレット最小化は簡単ではない．簡単な方法として，例
えば，UCTでのUCB1の代わりにMABでの単純リグレット最小化のアルゴリズムを
使う方法が考えられるが，その方法では，単純リグレットの最小化は難しいと予想され
る．理由は以下による．まず，前提として，MABの単純リグレットの最小化アルゴリ
ズムで，木の単純リグレットを最小化するためには，根の子節点以下はMABでの場合
と近い，つまり，根の子節点の利得の推定値は平均的には利得の理論値に近くなければ
ならない．しかしながら，単純リグレット最小化のアルゴリズムは（少なくとも累積リ
グレット最小化のアルゴリズムよりも）次善手を多く引く傾向にあるため，根の子節点
の推定値（平均利得）が不正確になると予想される．
根の子節点の利得のより正確な推定のためには，MABでの単純リグレット最小化の
アルゴリズムよりも累積リグレット最小化のアルゴリズムを使う方が優れている．それ
27
--- PAGE 38 ---
root
main main tree
strategy
sub-strategy extended tree
図 2.5: ハイブリッドなMCTSの探索木
は，一般の木の場合でもMABと同様に，最善の子を多く選ぶことにつながると期待さ
れるため，その結果として，各節点での利得をシミュレーションの平均利得によって推
定する際により正確な推定が出来ると期待されるためである．従って，節点の利得を正
確に推定するためにUCTが役立つと期待される．
一般にMABでは，単純リグレットの最小化と累積リグレットの最小化は両立でき
ないことが知られている [11]．そのため，MCTS でも，別のアルゴリズムを使った
ハイブリッドな MCTS が提案された．ハイブリッドな方法では，探索の方法を２つ
（primary strategy と sub-strategy と呼ぶ）に分け，単純リグレットを減らすための
primary strategyを根あるいは木の浅い方（main treeと呼ぶ）で用い，累積リグレッ
トを減らすためのsub-strategyを残りの木（extended tree）で用いる（図 2.5）．
近年の研究はハイブリッドな方法が効果的であると示唆している．sub-strategyと
してUCTを用い，情報の価値 (VOI)を近似的に求め，それが最大の手を選ぶという
primarystrategyを用いたMCTSSR+CRという方法が効果的であったという報告[70]
や，primarystrategyにSHOT[14]を用いたH-MCTSが効果的という報告もある[53]．
しかしながら，SHOTは予め，シミュレーションの数を固定しておかなければならな
いという制約があるため，H-MCTSでも同様の制約がある．4章では，他のprimary
strategyを使った，anytimeアルゴリズムを提案する．
2.9.1 利得分布のミニマックス的な更新
MCTSでは，通常，各節点で平均利得だけが保持される．しかしながら，もし，利
得のヒストグラムが保持されていれば，局面について，平均利得と比べてより多くの情
報が引き出せると期待できる．例えば，文献 [28]では，囲碁の攻め合いの局面を，目
数のヒストグラムから推定する手法を提案している．この手法の意図は，MCTSが比
較的苦手とされる攻め合いの局面を見つけ，探索に役立てるということである．尚，利
得の分布はミニマックス（ネガマックス）の考えに基づく方法で更新される [7,68]．そ
のため，推定値が正しい値に収束するのが速いと期待される．
Bayes-UCT [68]はUCTのベイズ的な拡張で，Tesauroは２種類提案した．どちら
のBayes-UCTも子の中で，それぞれのBayes-UCTの値が最大の子を選ぶ．
√ p
Bayes-UCT1:=(cid:22)^ + 2lnt=N (t); Bayes-UCT2:=(cid:22)^ + 2lnt(cid:27) ; (2.13)
i i i i
上記の各変数について説明する．(cid:22)^ は主観確率に基づく子iの利得の期待値で，(cid:22)^ =
i i
∫
(1(cid:0)x)p (x)であり，ここでのp (x)は節点iの手番のプレイヤーにとっての利得の理
x i i
28
--- PAGE 39 ---
表2.1: 類似の手法との比較．表の上半分にに既存手法をまとめた．\minimaxbackup"
の列は各節点の分布がどのように表現されるかを示し，\-"は利得の分布のミニマック
ス的な更新が行われないことを表す．\primary strategy"の列は展開やプレイアウトの
ために葉を選ぶ方法primarystrategyを表す．\sub-strategy"の列はハイブリッドな方
法が使われている場合，木のより深いところで使われている探索方法sub-strategyを意
味し，\-"はハイブリッドな方法を使っていないことを表す．表の下半分には4章での
提案手法をまとめた．提案手法は，離散分布でミニマックスの考えに基づく更新をし，
UCTと組み合わせたハイブリッドな方法である．
既存手法 minimaxbackup primary sub-
strategy strategy
BaumandSmith[7] discrete QSS -
BayesUCT[68] Betadistribution UCB(Bayes) -
H-MCTS[53] - SHOT[14] UCT
p
MCTSSR+CR[70] - ϵ-greedy,UCB ,VOI UCT
((cid:1))
YokoyamaandKitsuregawa[78] discrete[7] QSS[7],UCT (cid:11)(cid:12) search
4章での提案手法
HB+EExpected discrete[7] EExpected UCT
HB+ERobust ERobust
HB+ETerminal ETerminal=QSS[7]
√
HB+BayesUCT1 (cid:22)^ + 2lnt=N [68]
i p i
HB+BayesUCT2 (cid:22)^ + 2lnt(cid:27) [68]
i i
論値がxである確率であり，(cid:27) は確率分布の標準偏差である．一手打てば手番が変わる
i
ため，節点での利得1(cid:0)xはその親節点での利得xに相当する．Bayes-UCTでは，UCT
で平均利得X(cid:22) (式(2.5))を用いていたところを(cid:22)^ に置き換える．実験により，Bayes-
i;Ni i
UCTの(cid:22)^ はより速く節点iの利得の理論値に収束することが示されている．本研究で
i
の手法（4章）との間には，事前分布を使うか，離散分布を使うか，ハイブリッドにする
∑
か等の違いがあるが，本研究では，他の手法に合わせて(cid:22)^ = (1(cid:0)x)p (x)
i x2f0;0:5;1g i
を使った．
他にも分布を使った探索手法としてはBaumのアルゴリズム [7]がある．このアルゴ
リズムは最良優先探索の一種で最善手とその他を見分けるのに最も展開する意味がある
と期待される葉を展開していく．Baumの方法は，節点に対し，確率分布を返す評価関
数の存在を仮定している．
4章では，UCTにより分布を作成し，その分布に基づくハイブリッドなMCTSを提
案する．そのため，提案手法はBaumの方法とは異なり，評価関数が無くても探索が
可能である．また，4章ではBaumの方法をMCTSに相応しく改良する．表 2.1の上
半分に既存手法をまとめた．
29
--- PAGE 40 ---
第3章 モンテカルロ木探索と有利不利
が偏った局面
モンテカルロ木探索（MCTS）は様々なドメインで応用され，成果を上げてきた．古
典的なミニマックス探索では，確かな評価関数があるという仮定のもとで，その性能が
主に探査空間の大きさに依存すると知られている．それに対し，MCTSでは，どのよ
うなゲームの特徴が性能を左右するのかは，明らかではない．MCTSに関する先行研
究では，次善手の分布や，木の形の非一様さといった特徴が状態空間の大きさという特
徴より，MCTSの性能に大きな影響を及ぼすと示されている．本章では，新たな特徴
として，次善手の偏りを提案し，この特徴もまた，MCTSの性能に大きな影響を及ぼ
すことを示す．加えて，その特徴を持つ局面をより上手く扱う，MCTSの簡単な拡張
手法も提案する．次善手の偏りがあるゲーム木は，根のゲームの理論値が引き分けで，
一方のプレイヤーの次善手はもう一方のプレイヤーの次善手と比べて悪いというゲー
ム木である．本章では，多腕バンディット問題のアルゴリズムのMCTSへの応用とし
て，最も広く用いられているUCTだけでなく，様々なアルゴリズムをMCTSに応用
し，実験を行い性能を確かめる．その結果，次善手の偏りにより全てのアルゴリズムで
性能が低下すること，拡張手法を使うことにより，その特徴による性能の低下を抑えら
れることを示した．
尚，本章の内容は発表済みの論文 [33]に加筆，編集したものである．
3.1 背景
モンテカルロ木探索 (MCTS) は囲碁を始めとしたドメインで，成功したアルゴリズ
ムである[26]．また，Generalgameplaying[22]や不完全情報ゲーム[37]やリアルタイ
ムゲーム [56]を含む，様々なドメインにも応用されている．しかしながら，MCTSが
どのドメインに対し効果的でであるかについてはまだ研究の途上である．チェス等のい
くつかのドメインでは，確かな評価関数を利用した古典的なミニマックス探索がMCTS
を凌駕する性能を持つと知られている．また，単純なMCTSの代わりに，MCTSにミ
ニマックス探索して得た評価値を組み合わせたアルゴリズムを使うことで改善するドメ
インもあると報告されている [45]．その一方で，Lines of action では評価関数を組み合
わせることでMCTSはミニマックス探索に並んだという報告がある [73]．
各ドメインのどのような特徴がMCTSの性能に支配的な影響を及ぼすのかをよりよ
く理解するため，人工的なゲーム木を使って研究が行われてきた．Finnssonは，探索
30
--- PAGE 41 ---
空間の大きさはミニマックス探索の性能にとって第一義的な意味をもつ一方で，探索
空間の大きさよりも次善手の分布がMCTSにとってより重大であることを示した [22]．
Ramanujanは節点の種類の一つとして，トラップという，数手で負けとなる節点であ
るが，兄弟を選ぶとゲームが続くという節点を考え，実験的に解析した．その結果．
MCTSは一様な分枝数と深さの木で効果的で，トラップがあると性能が落ちることを示
した [59]．また，Longは不完全情報ゲームで，disambiguation factorと呼ばれる，木
の深さに対する情報集合に含まれる節点の数の減り具合を定義し，不完全情報ゲームに
おいて，あり得る世界を完全情報ゲームとして探索する場合にdisambiguation factor
と葉の価値との相関が性能に大きな影響を及ぼすことを示した [47]．
本章では，新しい特徴として，「次善手の偏り」を定義し，その存在がMCTSアルゴ
リズムの性能を下げるという意味で重要な特徴であることを説明する．そして，ゲーム
木の根のゲームの理論値は引き分けであるが，その一方で，片方のプレイヤーの次善手
は，他方のプレイヤーの次善手より悪いことが多いとしたゲーム木に焦点を当てる．こ
れは，ゲームの理論値の面では局面は互角であるが，最善手を選べなかった場合の損失
の面では有利不利が偏っている状況である．例えば，チェスでチェックメイトに至る長
い手筋が存在し，正しい手を選び続ければ勝てるが，正しい手を一度でも選べないと負
けが決まるような状況である．また，人間対AIという形式のゲーム(e.g. ミズ・パック
マン [32])では，人間のプレイヤーは勝つことは可能であるが難しい局面から始まるこ
ともある．囲碁での攻め合いの局面の一部は，次善手の偏りがある局面と類似の特徴を
持つ．Grafが提示した局面 [28]では，多くのプレイアウトは大差をつけて黒の勝ちで
あるが，そこから互いに最善を尽くすと引き分けか僅差で白の勝ちである．そのため，
その局面及びその先の局面の白の次善手には，黒の次善手より悪い手がより多く含まれ
ていると予想される．加えて，MCTSでは，囲碁のハンディキャップ戦（置き碁）で上
手くハンディキャップを活かせないことが知られている [5]．置き碁はゲームの理論値
は有利なプレイヤーの勝ちと予想されるという点で本章で対象とした性質とは異なるも
のの，一方のプレイヤーが有利であるという点では共通する特徴を持つ局面である．
「次善手の偏り」の存在がMCTSアルゴリズムの性能を下げることの大雑把な説明
は，自分と相手の手をいろいろ試してみても勝つ確率がほとんど変わらないなら，次善
手の中から最善手を見つけることの難しさは増大するということである．この問題を
解決するため，勝ち負けの基準をプレイアウトでのゲームのスコアの頻度に基づいて
調整するという単純な解決策を提示する．実験では，incremental random gameモデ
ルとその変種を用い，提案手法を組み合わせる探索アルゴリズムとして，UCTの他に，
MCTSにKL-UCB [13]，Thompson sampling [1]を応用したものを用いた．ゲーム木
モデルとその変種は標準的なモデルであり，アルファベータ探索やMCTSゲーム木探
索アルゴリズムの性能を評価するのに用いられている [23,42,43,49,59]ものを使った．
その結果，次善手の偏りが実際に全てのアルゴリズムでの性能を下げること，提案手法
により性能の低下が改善されたことを示した．また，incremental random gameモデ
ルでも評価し，実際に改善したことも示した．
31
--- PAGE 42 ---
尚，UCB1等のバンディット問題でのアルゴリズムをMCTSに応用した場合の比較
について，同時着手ゲームであるTronですでになされているが [56]，決定的なゲーム
ではなされておらず，知りうる限りこの研究が初めてである．
3.2 Incremental Random Gameモデルでの次善手の偏
り
本節では，次善手の偏りという特徴を表現するため， 2.2節で紹介した，標準的な
ゲーム木モデルであるincremental random gameモデルに従って，いくつかのゲーム
木モデルを紹介する．
incrementalrandomgameでのゲーム木は，一様な分枝数を持ち，その深さは偶数と
仮定する．木において，整数値が各手（辺）に与えられ，そして，最善手に対応する辺
では値が0で，残りの手は，Max (Min) プレイヤーの手番の節点では負（正）の値を
割り当てる．終端節点でのゲームのスコアは根からその終端節点までの値の和である．
もし，スコアが境界（通常は0）より大きい，小さい，等しい場合，それぞれ，Maxプ
レイヤーの勝ち，負け，引き分けを意味する．そのため，それぞれの節点にはちょうど
１つだけ最善手（辺の値が0）が存在する．そして，文献 [23]と同様に，互いに最善を
尽くして終局するとゲームのスコアは0となる．また，簡単のため，根はMaxプレイ
ヤーの手番とする．
３つのゲーム木モデルConstant, Constant’, Randomについて次善手の偏りを導入
する．パラメータ0 < a (cid:20) bに対し，Constant(a;b)はMax（Min）プレイヤーの次
善手の辺の値が(cid:0)a (b)と決められた値となる木である．Constant’(a;b)はMinプレイ
ヤーの全ての次善手の辺の値は最後（終局前）の手を除きaとなる変種で，最後の手
の内，次善手の辺の値はbとなる．aとbの違いが次善手の偏りを表している．a = b
となる特別な場合では，次善手の偏りは無くなり，Finnssonの扱ったモデルと同じに
なる [22]．パラメータ0<pと0(cid:20)qに対し，Random(p;q)はランダムな木の一つで，
Max（Min）プレイヤーの次善手の値は範囲[(cid:0)p;(cid:0)1] ([2q;2q +p(cid:0)1])の内の整数値
を一様な確率で割り当てる．この木では，パラメータqが次善手の偏りを表している．
q =0という特別な場合では偏りがなくなり，木はincremental random gameのものと
同じになる．図 3.1に木の例を示す．後の実験（図 3.3(a)，3.8(a)）で示されるように，
ゲーム木モデルにおける次善手の偏りはMCTSの性能を劇的に下げると言える．
3.3 利得の差の最大化
本節では，最善手の利得とその他の手の利得との平均的な差を最大化することに基づ
いたMCTSの拡張手法を示す．
32
--- PAGE 43 ---
0 s 1
0 s 2 0 s 3
図 3.1: 分枝数と深さが2のゲーム木モデルの例．四角と丸はそれぞれMax，Minプレ
イヤーの節点を意味する．それぞれの辺は手を表し，その値は最善手で0，次善手はモ
デル次第である．Constant(a;b)では，s =(cid:0)aでs ;s =bである．Random(p;q)で
1 2 3
は，s (cid:24) U([(cid:0)p;(cid:0)1])かつs ;s (cid:24) U([2q;2q +p(cid:0)1]であり，U((cid:1))は一様分布を意味
1 2 3
する．
20000
15000
10000
5000
0
-10 -5 0 5 10 15 20
slanimret
fo
rebmuN
all
optimal
default
min
max
Score
図 3.2: Constant’(1,13)で，終端節点でのゲームのスコアのヒストグラム
Kを根の局面での合法手（その局面で可能な手）の集合とする．(cid:22) を手i 2 Kから
i
シミュレーションを開始した際の利得の期待値とし，特に(cid:22)(cid:3)を最善手からシミュレー
ション開始した場合の利得の期待値とする．最善手の期待値と次善手の内で最も有望に
見える手の期待値との差を
∆:=(cid:22)(cid:3) (cid:0) max (cid:22)
i
i2Knf(cid:3)g
と定義する．概ね，大きな∆の方がより簡単に最善手が見つかると期待される．例え
ば， 2.4.1節の式 (2.2)で紹介したように，UCB1では∆
i
:= (cid:22)(cid:3) (cid:0)(cid:22)
i
とすると，次善
手を引く回数の期待値について
8ln(n) (cid:25)2
E[N (n)](cid:20) +1+
i ∆2 3
i
という不等式が成り立つ．多腕バンディット問題と深さ１の木探索との類似性のため，
木探索でも同様であると期待される．
3.3.1 Constantモデルの一手読み探索での分析
一手読みでのConstantとConstant’を分析することで，次善手の偏りによる難しさ
を示す．ここでの一手読みとは，MCTSにおけるゲーム木の深さを1で固定し，それ
33
--- PAGE 44 ---
以上の展開をしない場合のことで，この時，MCTSはMABと同じになる．∆の大小
と問題の難しさへの影響について，図 3.2を例に示す．これは，Constant’(1,13)で (分
枝数，深さ) が (4,8) とした時の全ての終端節点でのゲームのスコアをヒストグラムに
したものである．図で\all"は全ての終端節点，\optimal"は根での最善手の子孫の終
端節点だけを抜き出したものである．この設定では，根の次善手の辺の値は全て同じ
なので，\all"から\optimal"を除いたヒストグラムが，次善手のヒストグラムである
（これを次善手の数である3で割れば\optimal"とスケールが同じになる）．
図 3.2は，終端節点の多くが正のスコアを持つことを示している．つまり，通常の
勝ち負けのスコアの境界（= 0 であり，図中の\default"）では多くの終端節点がMax
プレイヤーの勝ちの節点である．一般に，次善手の偏りが増えると，利得の期待値が
より非対称になる．それは，根からのシミュレーションでは終端節点が一様な確率で
訪問されるためである．図中では，全体での利得の期待値は0:798であり，最善手から
シミュレーションした場合の利得の期待値0:875に近い値となっている．次善手からシ
ミュレーションした場合の利得の期待値は0:772と計算でき，∆=0:103と小さな値を
持つ. もし，勝ち負けの境界がスコア4から8にある（図中の\min"）と， 全体と最
善手の利得の期待値は0:75であり，∆は0となる．このような小さな∆はMCTSで
の最善手を見つけることの難しさの原因になる．
しかしながら境界を12から13（図中の\max"）にすると，最善手の利得の期待値は
0:515となり，全体の利得の期待値は0:234となる．次善手の利得の期待値は0:14とな
るため，∆は最大値0:375をとる．一般に良い境界は∆を大きく出来，最善手を見つ
けることに貢献する．
DynamickomiのScoreSituationalはこのConstant’には向かない手法である．Score
Situationalは今までに観測されたスコアの平均値を0に近づける．そのため，境界が
平均には，図 3.2で\all"や\optimal"の頻度が低い，スコア9から10になり，その場
合∆はほぼ0である．
プレイヤーには得られないが，全ての終端節点のスコアを使えば，∆を最大化する
ような境界が判明する．良い境界の調整方法を考察するため，実験では，比較対象とし
て，∆を最大化する境界も用いた．Constant(a;b)とConstant’(a;b)では，勝ち負けの
境界がtの時の∆の値は下記のような簡単な式で求まる [79]．
∑
∆(t)= P[s]+0:5P(t)+0:5P(t+a); (3.1)
t<s<t+a
ここでのsは整数値で，0:5は引き分けの利得である．P[x]は最善手から始まるシミュ
レーションの利得がxとなる確率で，P(x)はxが整数値の時P[x]そうでない時には
0となる関数である．Constant(a;b)とConstant’(a;b)では，ある節点を根とした部分
木とその兄弟を根とした部分木はその親節点までの辺の値の総和を除いて，同じである
という特徴がある．そのため，P[x+a]は次善手から始まるシミュレーションのスコア
34
--- PAGE 45 ---
Algorithm 3 Maximum Frequency method
Histogram[score] += 1 return argmax Histogram[t]
t
の確率を表していることに注意されたい．等式
∑ ∑
P[s]= P[s]+P(t+a)
t<s(cid:20)t+a t<s<t+a
を使うと式は
( ) ( )
∑ ∑
∆(t)=(cid:22)(cid:3) (cid:0)(cid:22) = P[s]+0:5P(t) (cid:0) P[s+a]+0:5P(t+a)
s>t s>t
である．
3.3.2 最大頻度法とUCB
実際的な状況での∆を増やすため，Algorithm 3に示した，頻度が最大なスコアに
境界を移すという最大頻度法を提案する．提案手法ではプレイアウト毎に，それぞれの
スコアの頻度を管理し，それに基づき，最も良い境界を返す．式 (3.1)で示したように
a=1かつ１手読みを仮定した場合でsをP[s]を最大化するスコアとすると，∆(t)を
最大化するという意味で最良なtはt = s(cid:0)0:5である1．最善手(cid:3)や各手の期待利得
(cid:22) は不明であるから，スコアsが最善手からのシミュレーションで出る確率P[s]をス
i
コアsが観測された頻度で近似する．
妥当な多腕バンディットのアルゴリズムでは，次善手を引く数の期待値には上界があ
る．例えばUCB1では 3.3節で再掲した 2.4.1節の式 (2.2)のようにO(ln(n))である．
そのためプレイアウトが増えるにつれて，この近似の誤差も小さくなると期待される．
Randomでは，Constantと同様の解析は出来ないため，実際に実験して最大頻度法の
効果を確かめた．
また，境界が変化した際のMABのアルゴリズムの不確かさの評価の修正も合わせ
て提案する．境界が変化した時，期待利得は今までのものとは大きく変化する可能性が
ある．探索を安定させるため，評価を修正する．tを現在の時刻， t を最後に境界が
c
変化した時刻，t′ をその差分つまりt′ = t(cid:0)t とする．また， N′(t)をt の後に手i
c i c
を訪問した数，つまり N′(t)=N (t)(cid:0)N (t )とする．UCB1，KL-UCB，Thompson
i i i c
samplingでtの代わりにt′を使い，それぞれ，式(2.1)，(2.3)，(2.4)を式(3.2), (3.3),
(3.4)へと変更する． √
2lnt′
X + ; (3.2)
i;Ni(t) N′(t)
i
lnt′+cln(lnt′)
maxq 2[0;1]:d(X ;q )(cid:20) ; (3.3)
i i;Ni(t) i N′(t)
i
(cid:11) =X N ′ (t); (cid:12) =(1(cid:0)X )N ′ (t): (3.4)
i i;Ni(t) i i i;Ni(t) i
1スコアの細かさを除いてConstant(a;b)とConstant(1;b=a)はほぼ同じである．
35
--- PAGE 46 ---
t =0のとき，つまり，境界が変化していない時，この式は元の式と同じである．加え
c
て，探索の始め，探索を安定化するため．境界はプレイアウト数が規定の回数に達する
まで，固定した．実験では10とした．
3.4 実験
本節では，実験を通じて，次善手の偏りがMCTSの性能を著しく下げ，また，境界
の調整により，性能の低下を軽減出来ることを示す．本実験では，境界の調整の効果
はUCTにとどまらず，一般的であることを示すため，UCB1だけでなく，KL-UCB
並びに Thompson sampling を MCTS に応用する．図中では，それぞれ \Score" と
\Value"はDynamic komiのScore SituationalとValue Situational（2.8.1節）を意味
する．\MaxFrequency"は提案手法である最大頻度法を意味し，\Plain"は境界の補正
なしのMCTSであり，\Static"は式(3.1)のように∆を最大化する静的な補正を行う．
\Static"は境界の補正で得られる効果の上限を調べるための手法であり，プレイヤーに
は分からない，全終端節点でのスコアの情報を特別に利用して補正値が計算する．
3.4.1 実験設定
文献 [42]のMCTSでの性能の評価にならって，各手法の誤答率をもとに性能を計測
する．誤答率はアルゴリズムが最善手を選べなかった割合である．それぞれの木に対
し，それぞれのアルゴリズムが，各時刻t=2nで誤答したかを数え，誤答率を算出し
た．尚，最善手は最大訪問数をもとにして選ぶとした．木で平均を取ることで，それぞ
れのアルゴリズムの時刻tでの誤答率が得られる．
本節では，(分枝数，深さ) が (4,8) の結果を示す．本節と同様の結果が木の大きさを
変えた場合にも報告されている [79]．全てのMCTSのアルゴリズムで葉節点は二回目
に訪問した時に展開した．Dynamic komiのパラメータは予備実験によりプレイアウト
数625時点での誤答率が最小になるように調整した．Scoreでは，sは0から1まで0:1
毎に，cは0から30まで1毎に試した結果，最善のsとcはそれぞれ0:2と24であっ
た．ValueではX とX はそれぞれ0:05と0:9であった．
lo hi
2.6節で説明したように，MCTSの探索木はプレイアウト数が増える毎に成長する
ため，いずれは探索木の葉が終端節点に達する．終端節点では，勝ち負けというゲーム
の理論値を利用できる．実験では，最大頻度法やDynamic komiといった，勝ち負けの
境界を補正する方法を用いたとしても，終端節点では勝ち負けの境界は常に0として，
勝ち負けを決めている．これは探索結果をゲームの理論値へ収束させるために妥当な設
定である．終端節点以外についても，MCTS-Solver [74]と同様に，ゲームの理論値も
計算した．ゲームの理論値がすでに分かっている場合，UCTの選択の過程で勝ち（負
け）確定の手は常に選ばれる（無視される）．また，根の負け確定の手が存在する場合，
その手の訪問数が多くても，誤答率の計算の際に最善手としては選ばないとした．
36
--- PAGE 47 ---
(a) UCT
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Constant(1,1)
Constant(1,3)
Constant(1,5)
Playouts
(b) UCT+MaxFrequency
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Constant(1,1)
Constant(1,3)
Constant(1,5)
Playouts
図 3.3: Constantでの(a)通常のUCT (Plain)と(b) UCTに最大頻度法を使用したも
のとの比較
3.4.2 Constant Trees
Constantでの実験では，誤答率は異なる乱数シードでの1000回の探索の平均をとっ
て計測した．Constant(a;b)では偏りのパラメータbにより次善手の偏りが調整出来る．
次善手の偏りに応じて，性能の低下が生じることを確かめた．
図 3.3(a)はUCT，つまりUCB1に基づくMCTSの誤答率を図示したものである．
本節の図は縦軸が誤答率で，横軸がプレイアウトの数である．誤答率は始め，0:75で
あった2．それから，プレイアウトを重ねる毎に，基本的には下がり，終端節点まで探
索木が成長する頃には0に収束した．しかしながら，誤答率の収束の速度は木の種類に
大きく依存した結果となった．Constant(1;1)つまり，偏りの無い木ではおよそ100回
プレイアウトで誤答率が収束した一方で，Constant(1;5)では，10000回でもまだ誤答
率が高くなった．Constant(1;5)は非常に難しい状況であることに注意されたい．それ
は，プレイアウトがほとんど全てMaxプレイヤーの勝ちになるためである．木の深さ
が8の時のConstant(1;5)は最も極端な場合で，Minプレイヤーが，次善手（値+5の
手）を一回でも選ぶと，Maxプレイヤーが勝つ．それは，Maxプレイヤーが次善手を
選び続けたとしても，その値は(cid:0)1であり，Maxプレイヤーの手番は４回しかないの
で，合計で(cid:0)4にしかならず，ゲームのスコアは正であることが確定するためである．
2仮に，手を一様な確率で選んだ場合，誤答率の期待値は0:75であり，実験の結果は想定通りである．
37
--- PAGE 48 ---
(a) MCTSwithThompsonsampling
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Constant(1,1)
Constant(1,3)
Constant(1,5)
Playouts
(b) MCTSwithThompsonsampling+MaxFrequency
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Constant(1,1)
Constant(1,3)
Constant(1,5)
Playouts
図3.4: Constantでの(a)MCTSにThompsonsamplingを応用したもの(Plain)と(b)
最大頻度法を加えたものとの比較
UCTの性能は次善手の偏りが増えるほど落ちることを示した．
図 3.3(b) は UCT に最大頻度法を組み合わせた時の誤答率である．Constant(1;3)
やConstant(1;5)では最大頻度法により改善が見られた．また，誤答率の収束までに
必要なプレイアウト数はおよそ1=8であった．しかしながら，補正なしの場合の方が，
Constant(1;1)では多少良かった．それ故，偏った局面で上手くいくことと偏っていな
い局面で上手くいくこととの間にはトレードオフが存在すると言える3．ウェルチのｔ
検定（両側，有意水準0:05）を行った結果，プレイアウト数が少ない場合とプレイアウ
ト数が多く，誤答率がほぼ0になった場合を除き，多くのプロットポイントで誤答率の
差は有意であるという結果となった．
図3.4は同じ実験を，ThompsonSamplingをMCTSに応用したものに対して行った
結果である．UCTでの結果と同様に，MCTSの性能はThompson Sampling を使って
も，より偏っている場合に下がり，また，最大頻度法を用いることで，収束の速度は改
善した．KL-UCBでの結果（図 3.5）も同様の傾向であった．
Constant(1;5)とConstant’(1;13)で実験を行った．図 3.6(a)はConstant(1;5)で拡
張手法を組み合わせたUCTでの誤答率を計測したものである．実験結果は，Plainは誤
3次善手の偏りが無い状況での最大頻度法の悪さは境界をより安定させることで，和らげることが出来る．
また，トレードオフがあっても，最大頻度法は複数エージェントで合議をして手を決める[48,51]際に役立
つと言える．それは，エージェントの多様性が強さに貢献すると知られているためである[48]．
38
--- PAGE 49 ---
(a) MCTSwithKL-UCB
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Constant(1,1)
Constant(1,3)
Constant(1,5)
Playouts
(b) MCTSwithKL-UCB+MaxFrequency
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Constant(1,1)
Constant(1,3)
Constant(1,5)
Playouts
図 3.5: Constantでの(a) MCTSにKL-UCBを応用したもの (Plain) と (b)最大頻度
法を加えたものとの比較
答率が10000回プレイアウトまで高いが，拡張手法により，誤答率が効果的に下がるこ
とを示した．また，MaxFrequencyとStaticは最も効果的であった．図3.6(b)と3.6(c)
は同一の設定下，KL-UCBやThompson samplingをMCTSに応用した場合の結果で
ある．結果はUCTと同様に拡張手法を適用することで誤答率が下がった．但し，拡張
手法を適用した場合の誤答率は概ねUCTの方が，KL-UCTやThompson samplingを
MCTSに応用したものより，低くなった．Score（図 3.6(a)）とStatic（図 3.6(b)と
3.6(c)）で不安定な挙動が観測されている．探索木が成長した際に，終端節点での勝ち負
けだけは，境界を0とした場合の勝ち負けであるのに対し，他の節点では異なる境界に
基づく勝ち負けであることにより，この不安定さが引き起こされたと予想される．同様
の比較をConstant’(1;13)にて行った．結果を図 3.7に示す．MaxFrequencyとStatic
は効果的であった一方で，ScoreとValue はほぼ全ての時刻でPlainよりも高い誤答率
となった．これは図 3.2で示したように，境界の位置により∆ = 0となるようなこと
が多かったためであると予想される．また，Plainの誤答率は収束前に上昇した．この
理由は，偏った木のため，Plainでは利得の推定値が全体的に高めであることと十分探
索が進み，終端節点まで，探索木の一部が成長し，最善手の値が正しい値に収束する際
にその値が大きく下がることの2点にあると推察される．
39
--- PAGE 50 ---
3.4.3 Random Trees
Incremental random treeでの実験では，200本の木を生成し，各木に対し，200回，
各アルゴリズムを実行した．そのため，誤答率は40000回の探索の平均である．これ
は，文献 [42]と同じ設定である．
図3.8と3.9はRandom(4;q)において，次善手の偏りがUCTとThompsonsampling
に基づくMCTSの性能を下げることを示している．ここでのq は偏りを示している．
Constantと同様に，誤答率の収束の速さは図.3.8(a)や3.9(a)に示した通り，特に境界
の調整をしない場合，木の種類に強く依存する．偏りが無いRandom(4;0)では，およそ
1000プレイアウトで誤答率が0に収束したのに対し，Random(4;3)では，10000プレイ
アウトでもまだ誤答率は高かった．図3.8(b)と3.9(b)に示した通り，MaxFrequencyは
PlainよりもRandom(4;2)及びRandom(4;3)で良い結果となった．その一方でPlain
の方がRandom(4;0)とRandom(4;1)では良くなり，Constantで観測されたトレード
オフがRandomでも観測された．UCTで拡張手法あり，なしの誤答率の差はプレイア
ウト数[16;1024]の時，統計的に優位（有意水準0:05で両側のウェルチのｔ検定）で
あった．プレイアウト数が大きく，誤答率がほぼ0に収束した場合では，その差は有意
では無かった．KL-UCBをMCTSに応用した結果（図 3.10）は同様であった．
各拡張手法を用いた場合の様々な木での誤答率を比較するため，Random(4;3)での
結果を図 3.11に示した．MaxFrequencyは安定して，改善したものの，Constant(1;5)
での改善ほど劇的ではなかった．また，Valueは効果的であった．ScoreはPlainより
も高い誤答率となることもあった．
3.5 まとめ
本節では「次善手の偏り」という特徴がMCTSの性能を劇的に下げることを示した．
この特徴量は，最善手を逃した場合の損失の片方のプレイヤーともう片方のプレイヤー
との差である．実験では，ConstantとRandomと大きく分けて二種類の木を使った．そ
して，UCB1をMCTSに応用したUCTだけでなく，KL-UCBやThompsonSampling
をMCTSに応用したものも利用した．KL-UCBとThompson Samplingは多腕バン
ディットでUCB1よりも効果的であると確認された手法である．それらの木に対して，
次善手の偏りが大きいほど，UCTだけでなく，他のMCTSの性能も下がることを観測
した．
また，本章では，囲碁等のゲームのスコアをエージェントが利用出来るゲームを対象
として，プレイアウトでの勝ち負けの境界を調整する手法の一つである最大頻度法を提
案し，それにより，性能の低下が和らぐことが明らかにした．この手法は最善手と次善
手の利得の期待値の差を最大化することに基づく手法である．実験を通じて，最大頻度
法により，ConstantとRandomの木の探索での性能の低下が抑えられることを示した．
Randomの木では，一般に，平均的なスコアが次善手の方が高い場合もあり得るた
40
--- PAGE 51 ---
め，最善手と次善手の利得の期待値の差を最大化することは難しい．そのような状況で
も最大頻度法により改善することを示した．但し，その度合いはConstantの木ほど劇
的な改善では無く，また，最大頻度法でも，偏りによる性能の低下は観測された．その
ため，次善手の偏りという特徴を持つ局面はMCTSには難しい局面であると言える．
41
--- PAGE 52 ---
(a) UCT
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Plain
Score
Value
MaxFrequency
Static
Playouts
(b) MCTSwithKL-UCB
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Plain
Score
Value
MaxFrequency
Static
Playouts
(c) MCTSwithThompsonsampling
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Plain
Score
Value
MaxFrequency
Static
Playouts
図 3.6: Constant(1,5)での誤答率の比較（拡張手法あり，なし）
42
--- PAGE 53 ---
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Plain
Score
Value
MaxFrequency
Static
Playouts
図 3.7: UCT, Constant’(1,13)での誤答率
(a) UCT
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Random(4,0)
Random(4,1)
Random(4,2)
Random(4,3)
Playouts
(b) UCT+MaxFrequency
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Random(4,0)
Random(4,1)
Random(4,2)
Random(4,3)
Playouts
図 3.8: (a)UCTと(b)最大頻度法を使ったUCTのRandomでの比較．
43
--- PAGE 54 ---
(a) MCTSwithThompsonsampling
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Random(4,0)
Random(4,1)
Random(4,2)
Random(4,3)
Playouts
(b) MCTSwithThompsonsampling+MaxFrequency
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Random(4,0)
Random(4,1)
Random(4,2)
Random(4,3)
Playouts
図 3.9: (a)Thompson samplingを使ったMCTSと(b)最大頻度法を組み合わせたもの
との比較
44
--- PAGE 55 ---
(a) MCTSwithKL-UCB
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Random(4,0)
Random(4,1)
Random(4,2)
Random(4,3)
Playouts
(b) MCTSwithKL-UCB+MaxFrequency
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Random(4,0)
Random(4,1)
Random(4,2)
Random(4,3)
Playouts
図 3.10: (a)KL-UCBを使ったMCTSと(b)最大頻度法を組み合わせたものとの比較
45
--- PAGE 56 ---
(a) UCT
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Plain
Score
Value
MaxFrequency
Playouts
(b) MCTSwithKL-UCB
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Plain
Score
Value
MaxFrequency
Playouts
(c) MCTSwithThompsonsampling
1
0.8
0.6
0.4
0.2
0
1 10 100 1000 10000 100000
etar
eruliaF
Plain
Score
Value
MaxFrequency
Playouts
図 3.11: Random(4;3)での各MABアルゴリズムに基づくMCTSの比較
46
--- PAGE 57 ---
第4章 分布を使ったハイブリッド
MCTS
モンテカルロ木探索（MCTS）の一種，UCTは，多腕バンディット問題（MAB）にお
ける累積的な利得を最大化することを目的としたUCB1アルゴリズムに基づいている．
しかしながら，MABの最近の研究で，累積的な利得を最大化することと最善のアーム
を見分けることのためには，それぞれ異なるアルゴリズムを使う方が効率的であること
が明らかになっている．本章では，MCTSにおいて，最善手を見つけるという観点か
ら，平均だけでなく，分布を使った手法を提案する．提案手法はハイブリッドなMCTS
で，木の浅い方ではネガマックスの考えに基づいた更新を行い，残りはUCTを適用す
る．実験により，提案手法は，一様な分枝数，深さの木を除き，UCTを上回ることが
確かめられた．
尚，本章の内容は発表済みの論文 [34]に加筆，編集したものである．
4.1 背景
UCTは2.6節で論じたように，累積リグレットの最小化のアルゴリズムであるUCB1
に基づいている [3,42]．これにより，平均利得が良い節点からのシミュレーションが優
先されるため，各節点の利得の期待値を平均利得で推定するのに相応しいと言える．し
かしながら，ゲームをプレイする状況では，利得の期待値を推定するよりも，根での最
善手を見つけることが重要である．それらの目標は相互に関係しているものの，異なる
点がある．MTD(f)の研究 [57]では(cid:11)(cid:12)探索の文脈でこの違いについて扱った．同様の
観点から，MABでの累積リグレットの代わりに単純リグレットに基づくアルゴリズム
をMCTSに応用することで性能は改善することが示されている [46,53,70]．しかしな
がら，木探索では直接的に単純リグレットを最小化することは難しい．
本章では，利得の分布を利用した新しいMCTSを提案する．提案手法は既存研究の
２つのアイデアの組み合わせに基づいている．１つは 2.9節で紹介した，探索木を根
付近（main tree）とそれ以外（extended tree）に分けた，ハイブリッドMCTSであ
る [53,70]．もう１つは，利得の分布に基づく，最善手の信頼度合いの評価である [7]．
提案手法では，extended treeをUCTで探索することで，main treeの葉の利得の分布
を得て，分布に基づき，最善手の信頼度合いを評価する．そのようにして，最善手の信
頼度合いに最も影響を与えるという意味で，main treeの最も重要な葉が分かる [7]．提
案手法は，最も重要な葉をUCTで探索をするということを繰り返し，最善手の判別を
47
--- PAGE 58 ---
root
main tree
l
main
l uct extended tree for UCT
random simulation
図 4.1: 提案手法で扱う探索木の二つの部分
目指した手法である．
4.2 信頼度合いの修正に基づく探索
本節では，徐々に根での選択の信頼度合いを修正していく新しいハイブリッドMCTS
を提案する．提案手法は図 4.1のように探索木を二つに分けて，primary strategyのた
めにmaintreeを保持し，sub-strategyのためにextendedtreeを保持する．そして，最
良優先探索の様に，逐次的に探索木を展開し，評価を改訂する．概要は図4.2に示した．
ステップ1から4はprimarystrategyに従い，ステップ2(a)から2(d)はsub-strategy
に従う．提案手法ではsub-strategyとしてUCTを用いる．まずは 4.2.2節で紹介する
primary strategy を使い，main treeのl を決める．それからUCTに従い，budget
main
回（通常は１回）プレイアウトを行う．新たに作られた葉は始め，extendedtree（UCT
の探索木）に含まれるが， 4.2.3節で説明する状況を満たせば，main treeに含まれる
ようになる． 2.9節の表 2.1の下半分にprimary strategyとsub-strategyをまとめた．
4.2.1 利得の分布の更新
提案手法では，利得として，勝ち，引き分け，負けに対応する３種類f0;0:5;1g を用
い，探索木のそれぞれの節点で，サイズ３の配列で利得の分布を保持する．分布の更新
方法として，main treeとextended treeでの異なる方法を導入する．この更新の枠組
みをhybrid backup (HB) と呼ぶ．
main treeの各節点では，利得の分布の更新方法として，ネガマックスの考え方に基
づいたBaumの方法 [7]を採用する．この方法では，内部節点j の確率分布は子孫の
節点の確率分布に従って決まる．c(i)(x)とc(i)(x)をそれぞれ，利得の理論値がx以下，
以上である確率とする．すなわち，p (x)を節点iの利得の理論値がxである確率とす
i
ると
∑ ∑
c(i)(x):= p (k); c(i)(x):= p (k) (4.1)
k:k(cid:20)x i k:k(cid:21)x i
である．特にc(i)(x)はp (x)の累積密度分布（CDF）である．逆に，確率p (x)は簡単
i i
にc(i)(x)やc(i)(x)から計算できる．main treeの節点jのCDF c(j)(x)は
∏
c(j)(1(cid:0)x)= c(c)(x) (4.2)
c2successor(j)
で更新する．尚，分布は兄弟間で独立と仮定する．
48
--- PAGE 59 ---
1. 葉の選択： 4.2.2節で定義した方法に基づき，最も価値のあるmaintreeの葉l
main
を選ぶ．
2. 評価：葉l の評価を改善するため，以下のように，l を根としたUCTの
main main
プレイアウトをbudget 回行う：
(a) 選択：最も価値のある葉l をl からUCB値最大の子を逐次的に選ぶ
uct main
ことで特定する．
(b) 展開： 2.6節で記した，MCTSの展開の状況を満たすと，葉l を展開し，
uct
新たな葉の中から一様な確率でl を選ぶ．
uct
(c) 評価： 葉から始めるシミュレーションによって，葉を評価する．
(d) 更新：シミュレーションで得た利得をl とl の間の各節点でヒストグ
main uct
ラムとして保持する．
3. 展開： 評価の後，UCTの木の節点で， 4.2.3節で記す条件を満たすものがあれ
ば，mainの木に組み入れる．
4. 更新： l と根との間の各節点利得の分布を式 (4.2)で更新する．
main
図 4.2: 提案手法の概要
extended treeの各節点では利得f0;0:5;1gのヒストグラムを保持し，それを正規化
し利得の確率分布とする．プレイアウトが終了後，その結果に対応して，利得のヒスト
グラムを更新する．そして，main treeの葉（extended treeの根）lでの利得の確率分
布を，lでの利得の理論値の主観確率分布p ((cid:1))とする．
l
提案手法では，main treeでのネガマックスに基づく更新をすることで，UCTのよ
うに頻度分布を更新する場合と比べて，利得の推定値が速く正しい値に収束すると期待
できる．
4.2.2 探索の手法
本節では，main treeのl を選ぶためのprimary strategyを紹介する．l は，
main main
図4.2のステップ 1で示した様に，UCT探索を開始する節点である．根をRとする．
節点m 0 を現在までの探索によって推定された，根での最善手argmax c2successor(R) (cid:22)^ c
∑
とする．(cid:22)^ は節点cの分布から計算される利得の平均，つまり (1(cid:0)x)p (x)であ
c x c
る1．根で手番のプレイヤー視点での，節点R とm の利得の推定値をそれぞれ(cid:22)^′
0 R
(=1(cid:0)(cid:22)^ ) とと(cid:22)^′ (=(cid:22)^ ) とする．不確かさU を根Rの利得の推定値とm の利
R m0 m0 0
得の推定値との差分，つまり
U :=(cid:22)^′ (cid:0)(cid:22)^′ (4.3)
R m0
1節点cの親節点で手番となるプレイヤーにとっての利得であるため，xではなく1(cid:0)xである．
49
--- PAGE 60 ---
とする．一般にU (cid:21)0である．
U は，下記の理由により，探索を続けることの効用を表していると解釈出来る．(cid:22)^′
R
は，main treeの全ての葉の真の利得がそれぞれの分布に従って独立に決まると仮定し
た場合の，根Rの利得の理論値の期待値である．また，(cid:22)^′ は，同様の仮定での推定最
m0
善手m の理論値の期待値である．言い換えると， (cid:22)^′ は葉の利得の理論値が決まっ
0 R
てから，根の利得の理論値（つまり根の最善手の利得の理論値）の期待値である．そ
して，(cid:22)^′ は先に最善手m を決めてから，葉の利得の理論値を決まったとした場合の
m0 0
m の理論値の期待値である．つまり，U は，葉の分布に従って利得が決まると仮定し
0
た場合に，葉の利得の理論値が全て明らかになった後に最善手を選んだ時の期待値と，
全て明らかになる前に推定最善手を選んだ時の期待値の差ということである．従って，
U の値が小さいことは，推定最善手に対して，本当に最善であると確信出来ることに
対応する．また，探索の目的は最善手を判別することであるので，その意味では，U の
値が大きいほど探索を続けるべきであると解釈出来る．
提案手法では，根の各手の利得の分布の分散を最小化するのではなく，U を最小化
することで最善手を見分けることを目指す．目標は，最善手を見分けるために，最も優
先して探索すべき葉lを見つけることである．提案手法において，ステップ1で葉lを
選んだとする．U を葉lをUCTで探索（ステップ2）後のU の値とする．この値は実
l
際にステップ2が完了するまでは不明である．U とステップ2の前のU 値との関係性
l
について，下記が言える．もし，U ≪U なら，それは，他の手と最善手が明確に区別
l
出来るようになったということを意味する．もし，U ≫U なら，それは，今までのU
l
の値が不正確で，修正する必要があると分かることを意味する．そのため，文献 [7]の
議論と同様に，どちらの場合もlは高い優先度を持つと解釈する．
HB+ETerminal
葉lを選択する妥当な方法の一つは，差の絶対値jU (cid:0)Ujの期待値を考え，最大の
l
lを選ぶことである．この方法では，葉lを訪問するとlの利得の理論値rが明らかに
なると仮定する．U^=r は葉lが終端節点で，その理論値がrだと確定した時のU とす
l l
る．他の葉の分布はlの探索により，変化しないと仮定すると，U^=rを直接的に計算出
l
来る．そして，探索後にlの理論値がrだと確定する確率をlの利得の理論値がrであ
る主観確率p (r)として，HB+ETerminalでは，絶対値の差jU^=r (cid:0)Ujをその確率で重
l l
みをつけて和，つまり期待値をとる．そして，その値が最大の葉を選ぶ．
∑
argmax p (r)jU^=r(cid:0)Uj (4.4)
l r2f0;0:5;1g l l
この方法は，MCTSによる分布に代えて，予め学習した評価関数による分布を使うと
QSS [7]と等価である．
50
--- PAGE 61 ---
HB+EExpected
通常のゲームでは，探索中に終端節点を訪問することは，終端でない節点を訪問こと
と比べて稀であると想定されている．この想定にもとづき，葉lを訪問し，lからプレ
イアウトした結果，利得rが出る確率をp (r)とし，lの理論値は不明と仮定するモデ
l
ルを提案する．葉lの分布が新たなプレイアウト結果rにより変化し，その結果として
変化したU の値をU^+rとする．HB+EExpectedでは，差jU^+r(cid:0)Ujの期待値が最大の
l l l
葉lを選ぶ．式で表すと以下となる．
∑
argmax p (r)jU^+r(cid:0)Uj (4.5)
l r2f0;0:5;1g l l
HB+ERobust
HB+ERobustは，各葉lでU を最大化するという意味で最も運の悪いプレイアウト結
l
果を考え，それに基づき葉を決めるmainstrategyである．U が現在のU より大きいこ
l
とは，現在の推定の誤差が大きいことを示唆する．この考えを発展させ，HB+ERobust
ではU^+r を全て考え，U を最大化するlを選ぶ．式で表すと
l l
argmax
l
max r2f0;0:5;1gU^
l
+r (4.6)
である．全ての葉lにおいて，max r2f0;0:5;1gU^
l
+r (cid:21)U であるため，
argmax max U^+r =argmax max jU^+r(cid:0)Uj (4.7)
l r2f0;0:5;1g l l r2f0;0:5;1g l
である．U の値が増え得るlを選んで，数回のプレイアウト結果で実際にU の値が増え
るなら，現在のU の値は不安定と言える．このstrategyはU の値に大きな影響を与え
る節点を先に探索することで，不安定さを改善すると期待される．U+rは根での推定最
l
善手m に対して，最善である確信度合いが下がるほど大きな値になる．そのため，推
0
定最善手m （推定最善以外の手）の子孫の葉ではrが0 (1)の時に最大化される．こ
0
こでのrは根でのプレイヤー視点の利得である．葉lで手番のプレイヤーが根で手番の
プレイヤーと異なるなら，根での利得rは葉では1(cid:0)rである．
HB+BayesUCT
HB+ETerminal，HB+EExpected，HB+ERobustは最終的に不確かさU を最小化するこ
とを目標として葉lを選択しているが，代わりに，BayesUCT [68]を適用して葉lを選
ぶことも出来る．HB+BayesUCTは根から葉までBayes-UCBの値 (2.13)
√ p
Bayes-UCT1:=(cid:22)^ + 2lnt=N (t); Bayes-UCT2:=(cid:22)^ + 2lnt(cid:27) ; (4.8)
i i i i
が一番大きな節点を選ぶ．２種類の値があるが，本研究では，それぞれprimarystrategy
に応用した．
51
--- PAGE 62 ---
4.2.3 実装の詳細
提案手法では，extendedtreeの節点が十分に探索されると図4.2のステップ3で記し
たようにmaintreeの節点になる．実験では，maintreeの全ての葉lの子節点はlを20
回以上訪問し，かつ，lの子節点について，最小でも2回以上訪問した時のみ，子を全て
同時にmain treeの節点とした．訪問数の条件は特にHB+ETerminalやHB+EExpected
では必要である．もし，仮に，ある葉節点の分布で頻度が正となる利得が1つしかない
場合，これらのstrategyでは，そのような葉節点は最小の優先度を持ち，ほとんど選
ばれないということが起こりえる．例えば，もし，節点lでのプレイアウトの結果が常
に勝ちならば，ETerminalとEExpectedでは同じプレイアウト結果が出ると見なされ，そ
の結果U =U となる．HB+ERobustはこのような問題は生じない．尚，事前分布（例
l
えば，予め頻度分布を増やしておく等）を考えることで上記の問題を避けることが出来
るが，その場合，増やす量が微小な場合，上記の問題はほとんど解決しない一方で，増
やす量が多い場合，探索自体に大きな影響があると予想される．適切な増やし方は不明
であり，本研究では，事前分布を用いる代わりに訪問数の条件を設けた．
HB+ETerminal，HB+EExpected，及び HB+ERobust では各葉の優先度の計算は，そ
れぞれの葉の分布の変化の影響を分布の更新時に合わせて計算することで，効率化で
きる [7]．提案手法でもこれに従った．優先度の計算量はmain treeの節点の数に比例
する．しかしながら，main treeの節点の数は，extended treeのものよりもずっと少
ないため，計算コストは普通は目立たない．また，primary strategyとUCTで計算コ
ストはbudgetによって調整できる．budgetはl を選ぶ毎に行うプレイアウトの数
main
である．budgetが2以上なら，U を複数回のプレイアウト後に見積もることになる．
l
HB+ERobustでは，budgetが2以上でも，U は計算コストが増えることなく見積もれ
l
る．それは，HB+ERobustの性質から，各葉lでU^+rの最大値を与える利得rはbudget
l
によらず同じであるためである．加えて，HB+ETerminalではプレイアウトが複数回で
も1回でもU は同じと見なされるので，HB+ERobustと同様にして，複数回プレイア
l
ウトでも計算コストは増えない．しかしながら，HB+EExpected ではU は複数回プレ
l
イアウトに応じて計算する必要があり，計算コストが増えるという欠点がある．
勝ち負けが確定した節点の扱いについては，MCTS-Solver [74]に倣った．main tree
では，ネガマックスに基づく分布の更新を行うので，勝ち負けが確定した節点の利得の
分布は自然と，正しい値に収束する．しかしながら，extended treeではUCTを用い
ているため，勝ち負けが確定したかの計算は必要である．また，提案手法のmain tree
では，引き分けが確定した節点は選ばれないが，その一方でUCTでは，平均利得の安
定のため，選ばれ得る点に注意されたい．
52
--- PAGE 63 ---
4.3 実験
様々なアルゴリズムの性能を比較するため，incremental random treeで実験を行っ
た．全ての木は基本的に一様な幅と深さを持つように生成したが，幾つかのサブツリー
は幅と深さにばらつきを持たせるため，枝刈りした．枝刈りの過程では，それぞれ内部
節点を確率P で終端節点にした．そして，その節点より下のサブツリーは枝刈りされ
る．尚，そのようにしても根の利得の理論値は変わらない．本研究では，スコアの偏り
のパラメータBiを導入し，ゲームにバリエーションを持たせるため，全ての葉のゲー
ムのスコアにBiを加える．それぞれの辺の値が整数のため，Biが非整数値（例えば
0:5）の場合，終端節点のゲームの結果は引き分けには成りえず，勝ち負けだけになる．
4.3.1 誤答率の比較
4.2節で導入したHB+ETerminal,HB+EExpected,HB+ERobust,HB+BayesUCT1,及
びHB+BayesUCT2という６つのアルゴリズム（提案手法の５つの変種とUCT）で実
験を行った．実験ではbudgetは1で固定し，全てのアルゴリズムでMCTS-Solver [74]
を使い，勝ち負けの確定を扱った．探索のはじめ，全ての手の利得の平均値は同じため，
推定最善手をランダムに初期化した．推定最善手は，現在の推定最善手の平均値を他の
手の平均値が真に上回った時のみ，置き替える．
ゲーム木自体は予め用意するが，それぞれのアルゴリズムの探索木は根とその子の
みから始まり，徐々に節点を増やしていく（ 4.2節）．(分枝数，深さ)が(4, 12)と(8,
8)の２つの設定で実験を行った．加えて，終端確率P は0，0:1，0:2とした．それぞれ
の木の設定で400本の木を作り，それぞれの木に対し，各アルゴリズムで200回探索
した．
本実験では，文献 [42]にならい，誤答率で各アルゴリズムの性能を計測した．誤答
率とは，最善手を選べなかった割合である．
結果を表 4.1にまとめた．提案手法HB+ERobustは終端確率P が0でない時に，一
番良い結果となった．その一方で，UCTはP が0の時に最も良い結果となった．これ
らの結果は，一様な分枝数でない時，trapという負けの手がある時にUCTの性能は下
がるという既存研究 [58,59]の知見に矛盾しない．HB+ERobustとUCTの性能はスコ
アのバイアスが木に加わると改善した．その一方でBayesUCTの性能は他と比べて良
くなかった．ヒストグラムとBayesUCTの原論文で用いられているベータ分布の違い
が性能に大きな影響を与えたと予想される．また，原論文では探索木は成長させない設
定である一方で，本研究では探索の間に探索木が成長するということもまた大きな違い
である．図 4.3の結果は，終端確率が正の時，HB+ERobustはUCTや他の手法よりも
誤答率が速く減ることを示している．
53
--- PAGE 64 ---
表 4.1: 4000プレイアウト時の誤答率．Bは分枝数，Dは最大深さ，Pは終端確率，Bi
はスコアのバイアスである．それぞれの設定で，誤答率最小の場合赤字で表示した
B-D P Bi HybridBackup(HB) UCT
ETerminal EExpected ERobust BayesUCT1 BayesUCT2
4-12 0.0 0 0.079025 0.078487 0.078575 0.037750 0.129050 0.013088
0.5 0.060987 0.058975 0.029712 0.057512 0.085550 0.016987
0.1 0 0.001275 0.001212 0.000188 0.005525 0.017175 0.040137
0.5 0.000163 0.000238 0.000000 0.001225 0.000988 0.000213
0.2 0 0.000000 0.000000 0.000000 0.000650 0.000450 0.002288
0.5 0.000000 0.000000 0.000000 0.000000 0.000000 0.000000
0.0 1 0.050350 0.046487 0.025087 0.066387 0.145387 0.023013
0.1 0.001213 0.001225 0.000325 0.034000 0.028837 0.007738
0.2 0.000000 0.000000 0.000000 0.001088 0.002325 0.000025
0.0 2 0.008500 0.008138 0.001512 0.003912 0.067475 0.000000
0.1 0.000113 0.000100 0.000000 0.001438 0.007950 0.000013
0.2 0.000000 0.000000 0.000000 0.000000 0.000450 0.000000
8-8 0.0 0 0.264350 0.251387 0.301200 0.777625 0.174887 0.016825
0.1 0.090300 0.074125 0.046825 0.093362 0.068025 0.098437
0.2 0.007125 0.004425 0.001187 0.036912 0.014563 0.067225
表 4.2: プレイアウト一回当たりにかかる平均時間（ミリ秒）
Budget P = 0.0 P = 0.1 P = 0.2
HB+ERobust 1 0.151095 0.079921 0.041580
10 0.021505 0.015718 0.011130
20 0.016167 0.012834 0.008930
Budget P = 0.0 P = 0.1 P = 0.2
UCT 1 0.012968 0.008496 0.004249
4.3.2 Budgetを増やすことでの計算の効率化
実験で最も良かったHB+ERobustの計算コストについて調べた．primarystrategyと
してHB+ERobust を使った場合，UCTと比べて計算コストが大きい．しかしながら，
maintreeの葉を一回選ぶ毎のプレイアウト回数であるbudgetを大きくすることで，プ
レイアウト一回当たりの計算コストを減らすことが出来る．その一方で，プレイアウト
数当たりの性能が下がる可能性もある．本節では，budgetを増やした時の性能を明ら
かにする．
実験では，かかった時間全体を計測し，プレイアウト数で割ることにより，プレイア
ウト一回辺りの平均的な消費時間を算出した．分枝数−深さが４−１２の木に対し4000
プレイアウトを行った．つまり，main treeで4000/budget回葉を選ぶということであ
る．ハイブリッドアルゴリズムでのUCT探索中に，extended treeの根すなわちmain
treeの葉の理論値が分かる場合がある．そのような場合，それ以上UCT探索しても意
味が無いため，プレイアウト数がbudgetに達する前に，UCT探索は止めて，maintree
54
--- PAGE 65 ---
の分布を更新する．実験時の環境として，AMD Opteron Processor 6274, 2.2 GHzを
使用し，Linux上で実行した．
図4.4に示したように，budgetを増やすことで，誤答率は少し増加した．しかしなが
らその差は限定的であった．表 4.2にHB+ERobustとUCTでのプレイアウト一回の平
均消費時間を示した. 計算効率はbudgetを増やすことで，改善した．UCTはbudget
20でのHB+ERobust よりも速いが，プレイアウト一回当たりおよそ１msかかるとい
う典型的な状況ではその差はほとんど無視できる．
4.4 まとめ
本節では，最善手に対する確信度合いに基づく，探索を続けることの効用から，シ
ミュレーションを開始する節点を決めるという新しいMCTSを提案した．最善手に対
する確信度合いは各節点の利得の分布に基づき推定される．提案手法では探索木を根
付近の探索木main treeとそれ以外の探索木extended treeに分け，それぞれの探索木
で別のアルゴリズムを使う．main treeでの各節点の利得の分布を，main treeの葉の
利得の分布からネガマックスの考え基づく更新をして推定し，main treeの葉の分布は
extended treeをUCTで探索することによって推定する．各イテレーションでは，最
善手の推定に資するmain treeの葉をUCTで探索する．実験では，提案手法は一様な
深さで一様な分枝数の木を除いて，UCTを超える性能を示した．提案手法の幾つかの
strategyの内，最も運の悪いプレイアウト結果を想定して葉の優先度を決める方法が最
も良い結果となった．
一様な木ではUCTよりも性能が悪く，そうでない場合に良いということは，本質的
なトレードオフが存在する可能性もあるが，提案手法では，終局の情報が無いとシミュ
レーションをどこから開始するのかの割り振りは上手く行かない一方で，終局の情報が
有るとそれを活かして割り振りが上手く出来るという解釈が可能である．それは逆に，
UCTとMCTS-Solverでは，提案手法ほど，終局の情報を上手く活かせていないとい
うことであり，改善の余地があるということでもある．5章では，終局の情報が手に入
る場合に絞って，UCTとMCTS-Solverの改善策を議論する．
55
--- PAGE 66 ---
(a) （分枝数，深さ）（4;12），終端確率0.0
1
0.1
0.01
0.001
0.0001
1e-05
0 500 1000 1500 2000 2500 3000 3500 4000
etar
eruliaF
Terminal Bayes1
Robust Bayes2
Expected UCT
#Playouts
(b) （分枝数，深さ）（4;12），終端確率0.1
1
0.1
0.01
0.001
0.0001
1e-05
0 500 1000 1500 2000 2500 3000 3500 4000
etar
eruliaF
Terminal Bayes1
Robust Bayes2
Expected UCT
#Playouts
(c) （分枝数，深さ）（4;12），終端確率0.2
1
0.1
0.01
0.001
0.0001
1e-05
0 500 1000 1500 2000 2500 3000 3500 4000
etar
eruliaF
Terminal Bayes1
Robust Bayes2
Expected UCT
#Playouts
(d) （分枝数，深さ）（8;8），終端確率0.0
1
0.1
0.01
0.001
0.0001
1e-05
0 500 1000 1500 2000 2500 3000 3500 4000
etar
eruliaF
Terminal Bayes1
Robust Bayes2
Expected UCT
#Playouts
(e) （分枝数，深さ）（8;8），終端確率0.1
1
0.1
0.01
0.001
0.0001
1e-05
0 500 1000 1500 2000 2500 3000 3500 4000
etar
eruliaF
Terminal Bayes1
Robust Bayes2
Expected UCT
#Playouts
(f) （分枝数，深さ）（8;8），終端確率0.2
1
0.1
0.01
0.001
0.0001
1e-05
0 500 1000 1500 2000 2500 3000 3500 4000
etar
eruliaF
Terminal Bayes1
Robust Bayes2
Expected UCT
#Playouts
図 4.3: 誤答率の比較
56
--- PAGE 67 ---
(a) 終端確率0.0
1
0.1
0.01
0.001
0.0001
1e-05
0 500 1000 1500 2000 2500 3000 3500 4000
etar
eruliaF
budget 1
budget 10
budget 20
#Playouts
(b) 終端確率0.1
1
0.1
0.01
0.001
0.0001
1e-05
0 500 1000 1500 2000 2500 3000 3500 4000
etar
eruliaF
budget 1
budget 10
budget 20
#Playouts
図 4.4: HB+ERobustでの誤答率．budgetはそれぞれ1, 10, 20．
57
--- PAGE 68 ---
第5章 子孫の勝敗確定時のシミュレー
ション結果の修正
ゲームにおいて終局は，重要である．終局では，勝ち負けが確定し，そして，終局の
前の局面でも，手番次第で，最善を尽くした場合の勝ち負けが確定するためである．し
かし，モンテカルロ木探索（MCTS）における終局の扱いをどうすべきかについてはま
だ明らかでない．本章ではMCTSでの終局の扱い方について議論し，最善を尽くした
場合の勝ち負けの計算とともに，勝ち負けが確定した手とその祖先の評価の調整を行う
手法を3種類提案した．実験の結果，提案手法は既存手法より優れた性能を示した．ま
た，提案手法について最善手の評価の観点から分析した．
尚，本章の内容は発表済みの論文 [80]に加筆，編集したものである．
5.1 背景
2.6節で紹介した通り，MCTSの代表的なアルゴリズムUCTは探索木中の手の評価
を，シミュレーションで行い，それを繰り返すことで各手の評価の精度を高めていく．
UCTは，十分に探索すると最善手を選ぶ確率が1に収束することが示されている [42]．
しかしながら，実際にゲーム等で用いる際には，多くの場合，限られた時間の中で各
局面と可能な手を調べ，最善手を選ぶ必要がある．その観点から考えると，効率的な探
索のために終局の情報は非常に重要である．終局では勝ち負けが確定し，子の結果と手
番次第では，親自身についても最善での勝ち負けが確定するというように，再帰的に最
善でない手が判明する可能性があるためである．
MCTSでの終局の扱いについての先行研究としては文献 [82]や，Monte-Carlo Tree
Search Solver (MCTS-Solver) が挙げられる．MCTS-Solverは探索木中の終局（終端
節点）での勝ち・負けの情報を用いて，無駄な探索を減らす方法である．手番にとって
の負けに至る手は探索中選ばないようにして効率化している．
しかしながら，この方法は改善の余地があると考えられる．MCTS-Solverを使った
探索の場合，ある節点iが最善で勝ち，もしくは負けと確定したとしても，それまでの
シミュレーション結果がiの先祖で保持されたままになっている．つまり節点iの最善
の結果とは異なるシミュレーション結果が先祖で保持されているということである．こ
れは望ましくないと考えられる．
本章では，以上の観点から，MCTS-Solverによる勝敗の確定の計算とシミュレーショ
ン結果の調整を組み合わせることで，MCTSの性能の改善を目指す．本章の手法は，終
58
--- PAGE 69 ---
局が探索木中にある場合に限った改善であるので，この改善が役立つ局面は限られる
反面，4章での提案手法のように一様な木ではっきりと悪くなることも無い．また，シ
ミュレーション結果を調節するだけであるので，簡便で，計算コストも低いという長所
もある．
5.2 提案手法
本節では，MCTS-Solverの拡張として，３種類の方法を提案する．提案手法では，シ
ミュレーション終了後，通常のUCTと同様に利得の推定値と訪問数を更新するだけで
なく，最善を尽くした場合の勝ち負けを計算し，勝ち負けが確定した際に，訪問数及び
利得の推定値を修正する．そして手番にとっての負けの手はそれ以降の探索中に訪問し
ない．その際の修正の対象では，負けが確定した手がある節点だけを考えれば十分であ
る．もし，節点jに勝ちが確定した手があった場合，節点jはjの手番の勝ちが確定す
るため，節点jに至る手はjの親iの手番の負けが確定した手である．そのため，その
手j 1は探索中に二度と訪問しないので，節点jの手については修正を考える必要はな
く，節点iの負けの手jについての修正を考えれば十分である．
提案手法では，葉節点では，まず，式(2.8)と(2.9)（ 2.6節参照）で値を更新する．
さらに，負け確定の手があれば，その都度，後述の手法により，訪問数，利得の推定値
を修正する．そして，葉節点の祖先の更新は式(2.10)と(2.11)を用いる．これらの式
により，負け確定の手がある節点での値の修正が，自然な形で祖先の値に反映される．
5.2.1 利得の推定値の修正：Replacement MCTS-Solver
互いに最善を尽くした場合の勝ち負けが判明した場合の単純な修正方法を提案する．
MCTSにおいて，最善の勝ち負けが判明する前の節点の推定値は，ランダムシミュレー
ションの結果である．Replacement MCTS-Solver (Replacement) では，最善の勝ち負
けが判明した場合，その値で推定値を置き換える．これは，節点で保持している，今
までのシミュレーションの結果を，その節点以降，常に最善手を選ぶというシミュレー
ションの結果で置き換えることに相当する．Replacementはもしある手jが負けと確定
した場合，X(cid:22) (cid:0)1 と修正する．Replacement では訪問数は特に修正を行わない
j;Nj
ため，UCTと同様に増える．
5.2.2 訪問数の修正：All Removal MCTS-Solver
前節の手法Replacementでは，負けと確定した手をその訪問数分だけ負けが出たと
して計算するため，シミュレーションがある程度行われた後で負けが確定した場合は大
1節点jとその親節点iについて，確定的なゲームを対象としているので，便宜上，節点jに至る節点i
の手と節点jを同一視しているが，以下，勝ち負けについては，手jと記した場合は，節点iで手番のプレ
イヤーにとっての，節点jと記した場合は，節点jで手番のプレイヤーにとっての勝ち負けを意味する．
59
--- PAGE 70 ---
Max A
Min
high B C
win D E
図 5.1: Replacementの問題点．節点DがMaxの勝ちと判明し，兄弟節点Eの勝ち負
けは不明とする．この時，節点Bから節点Dに至る手が（少なくとも単独では）節点
Bの最善手となることは無い．しかしながら，Replacementでは，勝ち負けが判明した
節点Dとその先祖Bの推定値を書き換えるため，節点Dの影響で節点Aから節点B
に至る手は過度に高い推定値になる．特に，節点Dの訪問数が大きい時に顕著である．
きく推定値が変化し得る．その結果，まだ勝ち負けが確定していない，確定した節点に
至る手が，それ以降しばらくの間探索されないということが起こり得る．これはMCTS
の性質として望ましくないと予想される．また，上記と関連して，負けが確定した手は
（全ての手が負けの場合を除き）最善手では無いにもかかわらず，その手の推定値を親
の推定値を算出するのに使っているため，負け確定の手をもつ節点の推定値は過度に高
くなる（図 5.1）．これもまた望ましくないと予想される．そこで，負けが確定したら，
その訪問数も調整するとともに，負け確定の手の値を使わないという手法を提案する．
このように，UCB値の第2項も合わせて調整することで，負けが確定した影響で，そ
の先祖の推定値が大きく下がり，しばらく訪問されないという状況を避けられると期待
出来る．この手法をAll Removal MCTS-Solver (ARemoval) とする. ARemovalでは，
もしある手j が負けと確定した場合，"は十分小さい値として，N ", X(cid:22) (cid:0)1
j j;Nj
とする．
5.2.3 利得の推定値の変化に応じた訪問数の修正：Inconsistency Re-
moval MCTS-Solver
前節の手法ARemoval では，特に探索の始めで，近くに終端節点がある場合に探索
が偏りがちになると予想される．具体的には，ある手を先に探索した結果，その子孫
の手で負け確定の手があって，その結果訪問数が減って，再びその手を探索するという
ことが起こり得る．また，訪問数が少なくなる分，まだ勝ち負けが確定していない手
のシミュレーション結果の違いに大きく影響されてしまい，推定がばらつくことも予想
される．これらもまた，MCTSの性質として望ましくないと考えられる．推定値を安
定させることとそれを正しい値に近づけることを両立させるのが理想的である．節点i
の手j で負けが確定した場合にReplacementのような修正がどれだけ影響を及ぼすか
を考えると，今までの推定値が負けの利得(cid:0)1から離れているほど，節点iとその先祖
の推定値に大きな変化を与え得る．そこで，提案手法では，負けが確定した際の推定値
の修正による変化が大きいほど訪問数を減らし，推定値の修正の影響を和らげること
を目指す．この手法をInconsistency Removal MCTS-Solver (IRemoval) とする．具体
60
--- PAGE 71 ---
Max (8;7) (9;6)
Min
(4;3) (4;4) (5;2) (4;4)
win
(2;1) (2;2) (2;2)
(3;0)
図 5.2: 提案手法IRemovalの更新の例左下の節点の子孫でのシミュレーション結果が
Maxプレイヤーの勝ちとなり，かつ左下の節点がMaxプレイヤーの勝ちと確定した時
の提案手法の更新の例．各節点の(a;b)は節点のMaxプレイヤーの勝ち数，負け数が
それぞれa;bを意味する．
的には，IRemoval はt+1回目のシミュレーションである手jが負けと確定した場合，
まず，シミュレーションで負けが確定しない場合の推定値を式(5.1)で求める．そして，
式(5.2)でその値が負けに対応する値(cid:0)1から離れているほどjの訪問数を減らす．更
に，式 (5.3)で推定値を正しい値である(cid:0)1にする．
X(cid:22) N (t)+r
X(cid:22) j;Nj(t) j (5.1)
j;Nj(t+1) N (t)+1
j
(1(cid:0)X(cid:22) )
N (t+1)
j;Nj(t+1)
(N (t)+1) (5.2)
j 2 j
X(cid:22) (cid:0)1 (5.3)
j;Nj(t+1)
シミュレーション結果を保持する方法として，シミュレーションの（勝数，負数）と
（勝率，訪問数）は一対一に対応する．利得に言い換えるとそれぞれ，シミュレーショ
ンの利得が（1の回数，0の回数）と（平均利得，訪問数）である．勝率と訪問数の代
わりに勝数と負数を保持する方法で，IRemovalの修正を行う場合，手jで負けが確定
すると手jのシミュレーション結果の内，手番にとっての勝ちの数を0にし，取り除い
た分だけ祖先のシミュレーション結果も取り除く．例を図 5.2に示す．例では簡単のた
め手番ではなくMaxプレイヤーにとっての勝ちと負けを考えている．
このようにすることで，ARemovalより訪問数が減りにくく，ARemovalで予想さ
れる，探索初期での推定値の不安定さを，和らげることが出来ると期待される．また，
Replacement で手j で負けが確定した際の２つの懸念である，大きな変化と節点j の
親iの推定値が過度に大きくなることによる不具合もReplacementほどではないと期
待される．ARemovalは，節点iの利得の推定値を算出するために，負け確定の手jの
推定値を使っているという意味ではReplacement と同じではあるが，負け確定の手の
訪問数は，負け確定時の調整の分だけ，Replacementに比べて少なくなるためである．
例えば，手j が終端節点が近い場合，負け確定するまでの訪問数は少なくなる．また，
jが終端節点が遠い場合でも，手jが負け確定するまでには，子孫で順に負けが確定し
ていく必要があり，その際に徐々に訪問数が減ると期待される．そのため，いずれにせ
よ，手jが負け確定した際の訪問数は少なく，その結果として，手jの修正が節点iの
推定値にもたらす影響も少ないと期待される．
61
--- PAGE 72 ---
5.3 実験
5.3.1 実験設定
実験で用いたゲーム木について説明する．本実験では 2.2節で紹介したincremental
randomtreeに，ランダムで終局を割り当てることでゲーム木を作成した．Max（Min）
の次善手の値の幅[(cid:0)a(cid:0)1]（[1;a]）はa = 5としている．本節の木は3章の木とは異
なり，プレイヤーの有利不利が対象である．また，本節の木は4章と同様，終局の割り
当ては，最大深さでは，確率1で終局，根を除いたそれ以外の節点では，既定の確率
P（終端確率と呼ぶ）で終局とした．尚，このように終局をランダムに決めても，ゲー
ムのスコアの理論値は0である．実験では，引き分けもMaxプレイヤーの勝ちとして
p
扱った．UCTのパラメータC は1= 2とした．各手法では，訪問数を修正するため，
p
最善手として選ぶのは，利得の推定値が最大の手とした．
5.3.2 実験結果
誤答率の変化
実験では，(幅，最大深さ) が (4;12), (8;8), (16;16)のゲーム木に対し，P =0:1;0:2
の2種類の終端確率を設定し，それぞれの設定に対し，各アルゴリズム，UCT+MCTS-
Solver (UCT)，Accerated UCT + MCTS-Solver (Accerated)，UCT + Replacement，
UCT+ARemoval，UCT+IRemovalで探索し，利得の推定値が最も高い手を選ばせ
て，最善手以外を選んだ比率（誤答率）を計測した．Acceratedの(cid:21)は0:9;0:99;0:999
の内，予備実験で最良の結果であった0:999を用いた. 尚，各設定に対し，ゲーム木を
800本生成し，それぞれの木に対し各200回探索した．プロットは10プレイアウト毎
に行っている．もし，根節点が勝ちと確定した場合はそれ以上の探索は行っていない．
分枝数4，最大深さ12の結果（図 5.3）はARemovalの誤答率がUCTの誤答率と比
べて有意に高くなった2．特に，図 5.2(a)のプレイアウト数800から1200では，UCT
と比べて，IRemoval, Replacementの誤答率は有意な差となった．尚，Acceratedの誤
答率とUCTとの誤答率にはほとんど差がつかなかった．このことは本章の他の実験で
も同様であった．また，終端確率0.2でプレイアウト数が5000手前でプロットが無く
なっているのは，全ての探索（800(cid:2)200）でプレイアウト数5000までに根節点の勝ち
が確定しているためである．
分枝数 8，最大深さ 8 （図 5.4），終端確率 0.1 の時の誤答率は，UCT と比べて
Replacement はプレイアウト数 1000 から 2500 辺りまで，有意に低くなったものの
3000辺りから，逆に有意に高くなった．ARemoval は全体的高く，IRemoval は低くな
り，特にプレイアウト数400辺りから有意に差がついた．終端確率0.2の時の誤答率は，
2有意水準0:05のウェルチのt検定で，両側検定を行った．
62
--- PAGE 73 ---
(a) 終端確率0.1
0.1
0.08
0.06
0.04
0.02
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 終端確率0.2
0.1
0.08
0.06
0.04
0.02
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.3: （分枝数，最大深さ）＝ (4;12) での誤答率の推移
UCTよりも，ARemoval, Replacement は有意に高くなった．しかし，IRemovalはプ
レイアウト数300から3000辺りまでは有意に下回った．
より大きな木である，分枝数16，最大深さ16の時の誤答率（図 5.5）は，終端確率
0.1，0.2の時も同様の傾向で，プレイアウト数200辺りからARemoval は有意に高く
なった．また，Replacement は始めUCTより誤答率が有意に高いものの，プレイアウ
ト数4000ほどから低くなり，IRemoval もUCTより有意に高くなることがあるもの
の，プレイアウトを重ねるとより低くなった．
今度はより難しいと考えられる，手の値の比率が異なる木を設計して，誤答率を計測
した．このゲーム木モデルでは，手の値の幅のパラメータaの値を根節点以外の手で
は，根節点の手の数倍にするという形で変えた．こちらの方がずっと簡単ではあるが，
3章で扱った難しさとシミュレーション結果が偏っているという点で共通した難しさを
持つゲーム木モデルである．手の値の比率が異なるゲーム木モデルでは，よりはっきり
とした差がつくと期待される．今までの木では，根節点の手の値が大きく異なる場合も
多いと予想され，その場合は手法によらず，すぐに最善手を選べるようになり，そのよ
うな場合では差がつきにくいと予想される．その一方で，根節点の手の値に対しその他
の手の値が大きい状況では，最善手を見つけるため，根節点の手の相対的に小さな違い
を見分ける必要があるので難しいと考えられるためである．具体的には，根節点の手で
は引き続きa=5とし，それ以外では根節点の手の値の2倍のa=10として，実験を
行った．その結果を図 5.6，図 5.7，図 5.8に示す． 結果は手の値の比率が1倍の時よ
りも全体的に誤答率に差がついた．傾向としては比率が1の時と同様にIRemovalの性
能が良かった．
63
--- PAGE 74 ---
(a) 終端確率0.1
0.1
0.08
0.06
0.04
0.02
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 終端確率0.2
0.1
0.08
0.06
0.04
0.02
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.4: （分枝数，最大深さ）＝ (8;8) での誤答率の推移
最善手の推定値の分析
本節では，誤答率に差がついた理由についてより詳しく調べる．特に，各手法で差が
ついた，手の値の比率の異なるゲーム木モデルで，手の値の比率を2とした時の結果に
ついて分析する．分析のため，各ゲーム木における探索200回の最善手の利得の推定
値の平均と分散を算出した．その値の全体的な傾向を見るため，800本のゲーム木での
値の平均をとった．
このゲーム木では，互いに最善を尽くすと，Maxプレイヤー（根節点はMaxプレイ
ヤーの手番）の勝ちであり，互いに最善を尽くした場合の利得は1であるので，利得の
推定値が1に近いほど，正確であることを意味する．そして分散が小さいほど，各探索
による違い3によらず，推定値が安定しているということを意味する．尚，根節点の勝
ちが確定すると推定値は1になる．以下，実験結果について提案手法毎に記す．
まずARemoval について記す，分枝数4，最大深さ12，終端確率0:1（図 5.9）の時
の推定値の平均はプレイアウト数4000辺りからUCTが一番低くなった．しかし，誤
答率（図 5.5(a)）はUCTのほうがARemovalより低くなった．ARemoval は平均的に
は，最善手の利得をより正しく見積もることができているが，UCTよりも誤答率が高
くなっているということである．分散はARemoval が高くなった．つまり，プレイア
ウト数が同じでも，推定値が各探索で比較的大きく変わるということである．終端確率
0:2（図 5.10）の時の推定値も，UCTよりもARemoval が高く，その一方で，分散は
ARemovalが高く，特にプレイアウト数が少ない場合に顕著であった．これはARemoval
では，終端節点まで探索木が成長し，ある節点で勝ちが確定した場合，その親では負け
確定の手に対する調整が行われ，訪問数が減るため，推定値が安定しないということと
3乱数により，シミュレーション結果が異なり，その結果としてシミュレーションを開始する節点の優先
順位が異なる可能性があり，探索結果に違いが生じ得る．
64
--- PAGE 75 ---
(a) 終端確率0.1
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 終端確率0.2
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.5: （分枝数，最大深さ）＝ (16;16) での誤答率の推移
予想され，特に終端確率0.2の時は頻繁に起きたためであると予想される．また，分枝
数8，最大深さ8の時（図 5.11，5.12），も同様の傾向で，UCTと比べて推定値の平均
は高くなった．また，その差は，分枝数4の時と比べて，多少大きくなった．分散は，
終端確率0.2でプレイアウトが少ない場合に特に高くなった．分枝数16，最大深さ16，
終端確率0.1の時（図 5.9），平均値は0.4付近でほとんど上昇しなかった．終端確率
0.2の時（図 5.10）も同様に，多少は上昇傾向が見られるものの，平均値はまだ0.4付
近でほとんど上昇しなかった．これらは木が大きいため8000回プレイアウトしただけ
では，最善手がどの手か判断するのに十分でない場合が多いということを示唆する．分
枝数4の時より，分枝数8の時のほうがUCTと比べて推定値が正確であったことは，
UCTの推定値が負けが確定した手の値も利用して算出するため，分枝数が大きいほど，
その値に影響され，ARemovalと比べて正しい値になりづらいということを示唆する．
UCTとの誤答率の差について，ARemoval は分枝数16の木を除いて，分枝数が高い
ほど改善する傾向になったのはそのためであると予想される，
次に Replacement について記す．分枝数 4，最大深さ 12 （図 5.9，5.10）の時の
Replacementの推定値の平均はUCTよりも少し高くなった．分散は終端確率0.1の
時に多少UCTと比べて上昇した．分枝数8，最大深さ8の時（図 5.11図 5.12）は，
Replacementの推定値はUCTと同程度で分散については，Replacementはプレイアウ
トが増えるにつれて，UCTと比べて少し上昇する傾向が見られた．これはReplacement
で訪問数が大きな節点の勝ち負けが確定し，推定値を書き換えたためと予想される．UCT
と同様にReplacement では負けが確定した手の値も利用して節点の利得の推定値を算
出するため，分枝数が大きいほど，その値に影響されやすいと予想される．そして，負
け確定なら全て負けとして計算するので，負け確定でも古いプレイアウトを使うUCT
と比べて，その影響は大きいと予想され，推定値の分散の結果はこれを支持している．
65
--- PAGE 76 ---
(a) 終端確率0.1
0.1
0.08
0.06
0.04
0.02
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 終端確率0.2
0.1
0.08
0.06
0.04
0.02
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.6: 手の値の比率2，（分枝数，最大深さ）＝ (4;12) での誤答率の推移
そのような理由により分枝数8の時はUCTほど誤答率が下がらなかったと考えられる．
最後に，IRemovalについて記す．IRemovalはARemoval のように訪問数を調整し
ていて，Replacement のように推定値を修正し，その値が親の推定値を計算するのに
使われる手法である．最善手の推定値の平均と分散の分析を通じて，IRemovalの推定
値の平均は既存手法と同程度かそれより高く，ARemovalとReplacementの高い方を
超えない程度であった．しかし，分散についてはARemoval のように大きくなること
はなく，Replacementのようにプレイアウトが増加するにつれて分散が安定して上昇す
る傾向は，全ての手法で上昇傾向の分枝数16，終端確率0.2の木を除けば，無かった．
分散は総じて低く，UCTと同程度に抑えられていた．平均はUCTよりも高く，分散
はUCTと同程度であるので，推定値の平均と分散の観点からも，IRemovalの良さを
支持する結果となった．
5.4 まとめ
本章では，探索中にある手の勝ち負けが確定するような状況について，その節点を訪
問した過去のプレイアウト結果をどう扱うかについて議論した．そして，3つの提案手
法に対し，その性能について，実験で評価した．その結果，特にInconsistencyRemoval
MCTS-Solver (IRemoval) は既存手法よりも優れた性能を示した．加えて，性能につい
て最善手の推定値の平均と分散をとり，各手法を比較し，分析をした．その結果，既存
手法と比べてIRemovalの最善手の利得を見積もりは正確であり，各探索で既存手法と
同程度に安定していた．
提案手法は4章のものと異なり，終局が近くに無い場合は通常のUCTと変わらない
ため，適用しても悪影響は無い．また，分布を使わない，かつ，更新の際にプレイアウ
66
--- PAGE 77 ---
(a) 終端確率0.1
0.1
0.08
0.06
0.04
0.02
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 終端確率0.2
0.1
0.08
0.06
0.04
0.02
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.7: 手の値の比率2，（分枝数，最大深さ）＝ (8;8) での誤答率の推移
トで辿った節点の値を子節点の推定値に基づき更新するだけであるため，計算のコスト
はより少なく抑えられる．
本章の結果は換言すると，終端節点（終局）が根の近くにある場合，終局での利得の
理論値を上手く用いて，間違っているシミュレーション結果を取り除く等を行い，節点
の推定値を正しい値に修正（加えて不確かさの評価も修正）することで改善したという
ことである．これは，一般の場合でも成り立つ可能性がある．つまり，終端節点が根の
近くに無く，理論値が分からない場合でも，節点の推定値をより正しい値に修正するこ
とで，MCTSの性能の改善出来る可能性があるということである．その立場から，6章
では推定値について，より基礎的な視点から論じ，新しい推定量を提案する．また，7
章では6章で提案した推定量を実際にMCTSに応用し，改善を目指す．
67
--- PAGE 78 ---
(a) 終端確率0.1
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 終端確率0.2
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.8: 手の値の比率2，（分枝数，最大深さ）＝ (16;16) での誤答率の推移
(a) 平均
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
drawer
detamitsE
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 分散
0.05
0.04
0.03
0.02
0.01
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitamitse
fo
ecnairaV
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.9: 終端確率 0:1，手の値の比率2，（分枝数，最大深さ）＝ (4;12) での最善手の推
定値の平均と分散の推移
68
--- PAGE 79 ---
(a) 平均
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
drawer
detamitsE
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 分散
0.05
0.04
0.03
0.02
0.01
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitamitse
fo
ecnairaV
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.10: 終端確率 0:2，手の値の比率2，（分枝数，最大深さ）＝ (4;12) での最善手の推
定値の平均と分散の推移
(a) 平均
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
drawer
detamitsE
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 分散
0.05
0.04
0.03
0.02
0.01
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitamitse
fo
ecnairaV
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.11: 終端確率 0:1，手の値の比率2，（分枝数，最大深さ）＝ (8;8) での推定値の平
均と分散の推移
69
--- PAGE 80 ---
(a) 平均
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
drawer
detamitsE
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 分散
0.05
0.04
0.03
0.02
0.01
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitamitse
fo
ecnairaV
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.12: 終端確率 0:2，手の値の比率2，（分枝数，最大深さ）＝ (8;8) での推定値の平
均と分散の推移
(a) 平均
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
drawer
detamitsE
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 分散
0.05
0.04
0.03
0.02
0.01
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitamitse
fo
ecnairaV
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.13: 終端確率 0:1，手の値の比率2，（分枝数，最大深さ）＝ (16;16) での最善手の
推定値の平均と分散の推移
70
--- PAGE 81 ---
(a) 平均
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
drawer
detamitsE
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
(b) 分散
0.05
0.04
0.03
0.02
0.01
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitamitse
fo
ecnairaV
UCT
Accerated
IRemoval
ARemoval
Replacement
#Playouts
図 5.14: 終端確率 0:2，手の値の比率2，（分枝数，最大深さ）＝ (16;16) での最善手の
推定値の平均と分散の推移
71
--- PAGE 82 ---
第6章 期待値の最大値の推定量
複数の確率変数が存在する状況下で，その期待値の最大値を求めることは，強化学
習，探索等をはじめとした分野で重要である．本章では，期待値の最大値の新しい推定
量Simpli(cid:12)ed Weighted Estimator（SWE）を提案し，多腕バンディット問題の設定で，
既存の推定量と実際的，理論的側面から議論する．推定量SWEが算出する推定値は各
アームからサンプリングした値の平均の重み付き和であり，各アームの重みはアームが
最善である確率をもとに決めている．理論的な分析では，新しい推定量のバイアスは妥
当な仮定のもとでサンプルサイズが増加するにつれて0に収束することを示した．さ
らに，分散についても，上界を示した．実験では，強化学習でよく使われる，UCB1，
softmax，epsilon-greedyで，アームを引いて集めたサンプルに対し，推定値を算出し，
提案手法の性能を評価した．推定量はサンプルサイズに対する，バイアス，バリアンス
（分散），平均自乗誤差の観点から評価した．様々なバンディット問題の設定の下で，提
案手法は常に最良ではないものの，多くの設定で良い結果となった．
尚，本章の内容は文献 [35]として発表済みのものを編集，加筆したものである．
6.1 背景
本章では，2.4.1節で紹介した多腕バンディット問題を題材に，複数の確率変数の期待
値の最大値を，サンプリングをもとに推定する問題を考え，新たな方法を提案する．期
待値の最大値を見積もるという問題は重要であることを３つの例を挙げて説明する．１
つ目の例は，強化学習である．強化学習での最善の方策を構成する行動は，その行動以
後，最善方策をとると仮定したもとで，累積期待利得が最大の行動を定義される．従っ
て，その最善方策を見つけるためには，期待値の最大値を見積もる必要がある．しかし
ながら，Q学習で使われる，maximum estimator（ME）は正しい値より高く見積もる
性質があり，そのため，学習の速度が落ちるドメインの存在が知られている [71]．２つ
目の例は，sponsored search auctions [77]である．この問題は多腕バンディット問題の
一種で，期待値の最大値の見積もりの誤差が経済的な損失に繋がるという問題である．
３つ目の例は，2.6節で紹介した，モンテカルロ木探索（MCTS）である．MCTSでも，
強化学習と同様に，最善手を判別するために，期待値の最大値が必要である．MCTSの
代表的なアルゴリズムであるUCT [42]では，子の価値の推定を子孫から行ったシミュ
レーション結果の平均で行う．これは，サンプルの平均による推定量（AVE）を用いて
いると解釈出来る．UCTは累積的な損失を最小化することを目的としたUCB1 [3]に
72
--- PAGE 83 ---
基づくアルゴリズムであるから，良さそうなところを多く選ぶという特質があり，漸近
的には，最善のアームを引く頻度が1に近づくものの，多数の子の推定値が低い場合
に，AVEの推定は低くなり，推定値が正しい値に近づくまでが遅い．従って，期待値
の最大値の推定方法として改善の余地がある．2.5節で紹介したように，他にも様々な
推定方法が研究されてきたが，既存の推定量には精度や計算コストの面でやはり改善
の余地がある．尚，期待値の最大値の推定量に関して，一般には不偏推定量は存在しな
い [72]．そのため，優れた推定量を見つけることは簡単では無く，不偏では無い推定量
から，より良いものを選ぶ必要がある．
推定量の評価を行うにあたって，なるべく小さいサンプルサイズに対して，より正確
に，より安定して推定値を算出することが望ましい．本章では，バイアスと分散と平均
自乗誤差で推定量の性能を評価する．
本章で提案する新しい推定量は，サンプル中の利得の平均ではなく，利得の重み付き
和をとる．各利得の重みはサンプルが得られたアームが最善である尤もらしさに応じて
決まる．理論的な分析により，提案手法の推定値のバイアスは妥当な仮定のもとで，サ
ンプルサイズを増やせば0に収束することを示す．加えて実験を行い，提案手法と既存
手法の実際的な性能を確かめる．実験では，強化学習で使われる主要なアルゴリズムを
用いてサンプルを集めた．様々な設定で，そのように集めたサンプルに対する推定の誤
差を評価する．
6.2 Weighted Estimator Based on Upper Con(cid:12)dence
Bound
本節では， 2.5節で紹介した既存手法WEのアイデアに基づき，新たな推定量Sim-
pli(cid:12)edWeightedEstimator (SWE)を提案し，理論的な性質について分析する．分析で
は， 2.5節と同様に，多腕バンディット問題の設定で，アームiを引いた回数N は確
i
率変数でないと仮定して行っている．
SWEはWEを簡略化し平均利得とサンプルサイズだけを使うので．分布を使うWE
よりも計算コストが少ないという長所がある．SWEは(cid:22)^を全ての利得（サンプルDの
要素）の重み付き和で推定する．各アームでの平均利得の重み付き和で推定するとも言
∑
える．W をアームiの平均利得の重みとした． K W =1となるように正規化して
i i=1 i
いる．推定量SWEは
∑K
(cid:22)^ := W X(cid:22) (6.1)
SWE i i;Ni
i=1
である．最善のアーム(cid:3)に対応する重みW(cid:3) が比較的大きい時にこの推定値は正確に
なる．SWEでは，今までのサンプルに対し，アームiが最善である尤もらしさを考え，
W をそれに応じて決める．直感的には，アームiの平均利得が他のアームの平均利得
i
よりも遥かに大きいなら，アームiは本当に(cid:3)であると予想される．そのため，平均利
得の差に着目する．一般に，最善のアームとそれ以外のアームの平均利得について，最
73
--- PAGE 84 ---
善のアームの平均利得がそれ以外のアームの平均利得を下回ることは稀であり，以下の
補題が成り立つ．
補題 1. 任意の"(cid:21)0と任意のアームj 2f1:::Kgnf(cid:3)g,
0 ( √ ) 1
2
P(X(cid:22) j;Nj (cid:0)X(cid:22) (cid:3);N(cid:3) (cid:21)")(cid:20)2exp @(cid:0)2 "√
N
j
N
+
j N p(cid:3)
N(cid:3)
A :
以下，簡単のため，任意のアームk;lについて
( p )
2
N N
N := p kpl
k;l
N + N
k l
とする．
Proof. 任意の定数a (0(cid:20)a(cid:20)1)について以下の不等式が成り立つ．
P(X(cid:22)
j;Nj
(cid:0)X(cid:22)
(cid:3);N(cid:3)
(cid:21)")
(cid:20)P(X(cid:22) (cid:0)(cid:22) (cid:21)a("+∆ ))
j;Nj j j
+P(X(cid:22) (cid:3);N(cid:3) (cid:0)(cid:22)(cid:3) (cid:20)(cid:0)(1(cid:0)a)("+∆ j ))
(cid:20)exp((cid:0)2fa("+∆ )g2N )
j j
+exp((cid:0)2f(1(cid:0)a)("+∆
j
)g2N(cid:3))
１つ目の不等式は，X(cid:22) j;Nj (cid:0)(cid:22) j < a("+∆ j )かつ(cid:0)(1(cid:0)a)("+∆ j ) < X(cid:22) (cid:3);N(cid:3) (cid:0)(cid:22)(cid:3) な
らばX(cid:22) j;Nj (cid:0)X(cid:22) (cid:3);N(cid:3) <"+∆ j (cid:0)(cid:22)(cid:3)+(cid:22) j ="の対偶から導かれる．２つ目の不等式は，
Hoeffdingの不等式 [30]から導出される．∆ (cid:21)0であることと２項のバランスをとる
j
p
ため， a= p Np(cid:3) とすると
Nj+ N(cid:3)
0 ( √ ) 1
2
(cid:20)2exp @(cid:0)2 (∆ +")√ N j N p(cid:3) A
j
N
j
+ N(cid:3)
0 ( √ ) 1
2
(cid:20)2exp @(cid:0)2 "√ N j N p(cid:3) A
N
j
+ N(cid:3)
( )
=2exp (cid:0)2"2N j;(cid:3) :
補題 1はX(cid:22) j;Nj (cid:0)X(cid:22) (cid:3);N(cid:3) > "が成り立つ確率の上界を与えている．つまり観測され
た平均利得について，最善でないアームが最善のアームを"だけ上回っている確率であ
p
る．p Njp N(cid:3) は直感的には，引いた回数N
j
;N(cid:3)に基づいて，差の信頼度合い，つま
Nj+ N(cid:3)
り，差がついたのは偶然か否かを評価していると言える．尚，上記の不等式で右辺を最
小化するaの値は一般には不明である．例えば，N と"次第では，２項のバランスを
i
とらずに，単にa=1=2をとる方が小さくなることもある．しかしながら，２項のバラ
ンスをとることで，一方の項が相対的に指数的に大きくなることを避けることが出来，
多くの場合で，右辺を小さく出来ると期待される．
74
--- PAGE 85 ---
サンプルの重みw~ をこの確率の上界に基づいて定め，式(6.1)のアームの重みW を
i i
w~ N
W = i i (6.2)
i n~
として定める．ここでのn~は正規化項であり，
∑
n~ = w~ N (6.3)
i i
i
である．任意のアームkとlに対し，d を観測された平均利得の差X(cid:22) (cid:0)X(cid:22) と
k;l k;Nk l;Nl
定める．w~ を以下のように定める．
i
8 ( )
< exp (cid:0)2(cd )2N (i̸=m);
m;i m;i
w~ :=
i :
1 (i=m);
ここでのc(cid:21)0 はパラメータであり，m:=argmax X(cid:22) である，つまり観測された
i i;Ni
平均利得が最良のアームである．
w~ について0(cid:20)c(cid:20)1の場合の意味について以下でより詳しく述べる．w~ は既存手
i i
法WEでのアームiの重みP(i=(cid:3)jD)（サンプルDを観測した後のi=(cid:3)の主観確率）
と関連がある．ベイズの定理より
P(i=(cid:3))
P(i=(cid:3)jD)= P(Dji=(cid:3))
P(D)
である．事前分布について，任意のアームi;j について，P(i = (cid:3)) = P(j = (cid:3))と仮定
すると，比について
P(i=(cid:3)jD):P(j =(cid:3)jD)=P(Dji=(cid:3)):P(Djj =(cid:3))
となる．尚，サンプルDは，観測値の集合
D =fX ;:::X ;
1;1 1;N1
.
.
.
X ;:::X g
K;1 K;NK
である．以下では，議論の明確化のため，各アームiの平均利得について，確率変数（X
i
とする）とその具体的な値（サンプルDでの平均）X(cid:22) とで表記を使い分ける．i=(cid:3)の
∧
i;Ni
もとで，事象f X =X(cid:22) gについて考えると，この事象は平均利得だけに着目して
j j j;Nj
∧
おり，個々の利得には着目していないため，事象fDgの起こる確率はf X =X(cid:22) g
j j j;Nj
よりも小さい，つまり
0 (cid:12) 1
(cid:12)
∧ (cid:12)
P(Dji=(cid:3))(cid:20)P @ X j =X(cid:22) j;Nj (cid:12) (cid:12)
(cid:12)
i=(cid:3)A
j
75
--- PAGE 86 ---
となる．0(cid:20)c(cid:20)1とするとP(Dji=(cid:3))の上界について以下が成り立つ．
0 (cid:12) 1
(cid:12)
∧ (cid:12)
P(Dji=(cid:3))(cid:20)P @ X j =X(cid:22) j;Nj (cid:12) (cid:12)
(cid:12)
i=(cid:3)A (6.4)
j
0 (cid:12) 1
(cid:12)
∧ (cid:12)
(cid:20)P @ X j (cid:0)X i =X(cid:22) j;Nj (cid:0)X(cid:22) i;Ni (cid:12) (cid:12)
(cid:12)
i=(cid:3)A (6.5)
j
0 (cid:12) 1
(cid:12)
∧ (cid:12)
(cid:20)P @ X
j
(cid:0)X
i
(cid:21)d
j;i
(cid:12) (cid:12)i=(cid:3)A (6.6)
(cid:12)
j
0 (cid:12) 1
(cid:12)
∧ (cid:12)
(cid:20)P @ X
j
(cid:0)X
i
(cid:21)cd
j;i
(cid:12) (cid:12)i=(cid:3)A (6.7)
(cid:12)
j
(cid:20)minP(X (cid:0)X (cid:21)cd ji=(cid:3)) (6.8)
j i j;i
j 0 0 ( √ ) 1 1
2
cd N N
(cid:20)min @ 2exp @(cid:0)2 √j;i pj i AIfd (cid:21)0g+Ifd <0gA (6.9)
j;i j;i
j N j + N i
0 0 ( √ ) 11
2
cd N N
(cid:20) min @ 2exp @(cid:0)2 √j;i pj i AA (6.10)
j:dj;i (cid:21)0 N j + N i
( ( p ) )
2
cd N N
(cid:20)2exp (cid:0)2 pm;i pm i (6.11)
N + N
m i
=2w~ (6.12)
i
上記の不等式で，右辺が十分に小さければ，
P(Dji=(cid:3))(cid:25)2w~
i
であり，
P(i=(cid:3)jD):P(j =(cid:3)jD)(cid:25)w~ :w~
i j
つまり，w~ とw~ の比はWEでのアームiとjの重みの比と近いと期待できる．
i j
c>1の時は上記の(6.7)の不等式が成り立たず，P(Dji=(cid:3))との関係が不明になる．し
( (cid:12) )
∧ (cid:12)
かしながら，それ以降は成り立つ．つまり，2w~ は差についての確率P X (cid:0)X (cid:21)cd (cid:12)i=(cid:3)
i j j i j;i
の上限であると言える．この確率は平均利得が全てのアームjでi（=(cid:3)と仮定）をcd
j;i
以上上回っている確率であり，この確率でアームの最善度合いを評価するのは妥当で
ある．それは以下の理由による．もし，アームiが本当に(cid:3)なら，多くのアームj で
cd <0になり，そうでないアームがあったとしてもcd は小さく，結果としてその
j;i j;i
確率は大きくなると期待できる．加えて，もし，アームiが本当は(cid:3)でないなら，多く
のアームj でcd > 0が成り立ち，d は大きくなり，確率は小さくなると期待でき
j;i j;i
る．以上のように，cを一般化してc>1の場合も考えるのは，WEとの関連性の解釈
は成り立たないものの，有効であると期待される．実験では，c>1も扱っている．
パラメータの範囲の有効性について0(cid:20)c(cid:20)1だけでなく1<cにもあることを説明
したが，大雑把にはcが低いほどAVEに近く，高いほどMEに近い振る舞いとなる．
76
--- PAGE 87 ---
実際，もし，c!0なら，w~ は，i̸=mで0であり，結果として，式 (6.1)の重みW
i i
はIfi=mgとなり，SWEはMEと同じになる．同様に，もしc=0なら，W はN =n
i i
であり，この場合SWEはAVEと同じになる．
N は全てのアームiでnに関する広義単調増加関数であることに注意すると，c>0
i
でのSWEのバイアスが0に収束する条件について以下の定理が成り立つ．
定理1. SWEのバイアスは以下の２条件を満たすとき0に収束する．つまりlim n!1Bias((cid:22)^
SWE
)=
0が成り立つ．条件 (1) 任意のアームiについてlim n!1N
i
=1である．条件 (2) 任
意のアームj ̸=(cid:3)について，
( ( ) )
lim
3N
j exp (cid:0)
c∆
j
2 N(cid:3)
=0; (6.13)
n!1 N(cid:3) c+1 2
上記の定理の意味することは，SWEのバイアスは次善手の引く割合が最善手のもの
より極端に多くても（例えば100倍）定数倍なら0に収束するということであり，さら
に極端な場合として，N
j
がN(cid:3)に対して指数的に増えても，c;∆
j
次第では，0に収束
するということである．そのため，SWEは収束性に関して，AVEよりもアームの引き
方に対する依存度合いが低く，よりロバストである．尚，MEはその観点ではSWEよ
りもロバストである．MEでは，全てのアームjでサンプルが十分に存在する，つまり
lim n!1N
j
=1という条件さえ満たせば，バイアスが0に収束するためである．この
ことは，jBias((cid:22)^ )jは(K(cid:0)1)P(m̸=(cid:3))で抑えられるため，定理 1と同様にして示
ME
される．SWEはサンプルに対して重みを与えているため，各アームのサンプルサイズ
に多少依存してしまう．それ故，バイアスの収束性を示すためには，条件 (2) のような
サンプルサイズに関する条件は本質的に必要なものであると考えられる．
∑
Proof. 利得は0以上，1以下であるので，jX(cid:22) i;Ni (cid:0)X(cid:22) (cid:3);N(cid:3) j(cid:20)1であり，W(cid:3) =1(cid:0) j̸=(cid:3) W j
から
jBias((cid:22)^
SWE
)j=jE[(cid:22)^
SWE
](cid:0)(cid:22)(cid:3) j
(cid:12) 2 3(cid:12)
(cid:12) (cid:12)
(cid:12) ∑ (cid:12)
= (cid:12) (cid:12)
(cid:12)
E 4 W i (X(cid:22) i;Ni (cid:0)X(cid:22) (cid:3);N(cid:3) ) 5(cid:12) (cid:12)
(cid:12)
i̸=(cid:3)
∑ [ ]
(cid:20) E W i (cid:1)jX(cid:22) i;Ni (cid:0)X(cid:22) (cid:3);N(cid:3) j
i̸=(cid:3)
∑
(cid:20) E[W ]: (6.14)
i
i̸=(cid:3)
である．E[W ]の上界について，
i
E[W ]=P(m=(cid:3))E[W jm=(cid:3)]+P(m̸=(cid:3))E[W jm̸=(cid:3)]
i i i
[ (cid:12) ]
(cid:12)
w~ N (cid:12)
(cid:20)P(m̸=(cid:3))+E ∑ i i (cid:12)m=(cid:3)
j̸=(cid:3)
w~
j
N
j
+N(cid:3) (cid:12)
N
(cid:20)P(m̸=(cid:3))+ iE[w~ jm=(cid:3)] (6.15)
N(cid:3) i
77
--- PAGE 88 ---
( p )
2
である．N i;j = p
NipNj
という表記を用いると，式(6.15)の第一項について
Ni+ Nj
P(m̸=(cid:3))(cid:20)P(9i;X(cid:22) i;Ni >X(cid:22) (cid:3);N(cid:3) )
∑
(cid:20) P(X(cid:22) i;Ni >X(cid:22) (cid:3);N(cid:3) )
i̸=(cid:3)
∑ ( )
(cid:20) 2exp (cid:0)2(∆ )2N (6.16)
i i;j
i̸=(cid:3)
である．最後の不等式は，補題 1の議論と同様に導かれる．任意のアームj について
N !1であるから，式 (6.16) は0に収束する．式 (6.15)の第二項についても補題 1
j
の議論と同様にして，アームi̸=(cid:3)と定数d (0<d<∆ )について，
i
E[w~ jm=(cid:3)]
i
=E[(Ifd (cid:21)dg+Ifd <dg)w~ jm=(cid:3)]
m;i m;i i
Ifd (cid:21)dg(cid:20)1かつw~ (cid:20)1であるから
m;i i
( )
(cid:20)exp (cid:0)2(cd)2N(cid:3);i +P(d(cid:3);i <d)
( ) ( )
(cid:20)exp (cid:0)2(cd)2N(cid:3);i +2exp (cid:0)2(∆
i
(cid:0)d)2N(cid:3);i
である．そのためd= ∆i とすると，
c+1
( ( ) )
N N c∆ 2
N(cid:3)
iE[w~
i
jm=(cid:3)](cid:20)
N(cid:3)
i3exp (cid:0)2
c+
i
1
N(cid:3);i
である．∆
i
とcは定数であるので，もしN
i
(cid:20)N(cid:3)なら，8j;N
j
!1より，最後の項
は0に収束する．もしN
i
>N(cid:3)なら，N(cid:3);i >N(cid:3)=4であることを用いて，
( ( ) )
(cid:20)
N
i3exp (cid:0)
c∆
i
2 N(cid:3)
N(cid:3) c+1 2
である．この項もまた，定理 1の式 (6.13)で記した条件により，0に収束する．
バイアスだけでなく，分散についても理論的なことが示される．
定理 2. SWEの分散について，以下が成り立つ．
∑K
(cid:27)2
Var((cid:22)^ )(cid:20) i
SWE N
i
i=1
この不等式は文献 [18]のTheorem 3の証明に従って示される．この上界は少し，悲
観的すぎるかもしれない．次節の実験で，分散についても調査する．
6.3 実験
本節の実験では，UCB1，softmax，epsilon-greedyといった強化学習での著名なアル
ゴリズムに従い，サンプルする場合の推定量のバイアス，分散，自乗誤差（MSE）につ
78
--- PAGE 89 ---
いて示す．これらのアルゴリズムでは，今までのサンプルの平均利得に基づいて，アー
ムを選ぶため，各アームを引く回数は確率変数となる． 2.5節で紹介した既存の推定
量についての分析や6.2節の提案手法の分析では，簡単のため，各アームを引く回数
は確率変数でないという仮定をおいたが，そうでない場合の性能についても概ね理論
に従っていることを示す．また，参考のため，全てのアームを一様な確率で引く場合
（uniform）での結果も示した．比較対象は既存の推定量AVE，ME，DE及びパラメー
タを調節したMMである．
各アームの利得は，ベータ分布B(2(cid:22);2(1(cid:0)(cid:22)))に従うこととした．ここでの(cid:22)は
アームの利得の期待値である. それぞれの設定では実験を1000回行い平均をとった．
X(cid:22) は始め，0とした．softmaxの温度パラメータはT =1とし，epsilon-greedyのパ
i;Ni
ラメータは"=0:1とした．MMのパラメータは(cid:21)=0:3と0:7，提案手法のパラメー
タはc=1, 2, 4とした．
まず，バイアスや分散がサンプルが増える毎にどのように減るのかを精査した．10
本のアームがあり，2本は高い期待値0:8，残りは低い期待値0:1であるという設定で
実験をした．
softmax，UCB1，epsilon-greedyに従って，サンプルを集めた場合の結果について
図 6.1, 図 6.2，図 6.3に示した．加えて，参考のため，一様な確率で各アームを引い
てサンプルを集めた場合（uniform）の結果も図 6.4に示した．全てのサンプリング方
法において，理論と同様にMEとAVEはそれぞれ最も正と負の方向に大きいバイアス
となった．また，MMはMEとAVEの間であり，MMは 2.5での分析から予想され
る通り，特にsoftmax, uniformで収束し損なっている．SWEのバイアスはMMと同
様にAVEとMEの間であったが0に収束するという意味では，より好ましい性質があ
る．パラメータcの値として，2や4といったより大きな値を使うと，SWEのバイアス
はより速く収束したが，分散は少し高くなった．DEの分散は他と比べて少し高くなっ
た．これはDEがサンプルを分け，結果的に利得の推定に使えるサンプルサイズが減る
ためであると予想される．epsilon-greedyはこの設定にはあまり向かない．全てのアー
ムの平均利得の初期値が0であり，利得はほとんど確かに正であるため，最初に引いた
アームを優先するようになる．そしてそれは少なくとも他のアームが引かれる（確率
"K(cid:0)1）まで続くため，長い間，最初に引いたアームを引くことになる．このような状
K
況下でも，SWEは良い推定値である．
推定量の様々な設定下でのロバストさを測るため，アームの数10で固定し，高い期
待値（(cid:22)=0:8）のアームの数を変化させ，それぞれの設定下で各種の値を計測した．こ
の実験では，サンプリング手法として，UCB1を用いた．結果 (図 6.5) は高い期待値
のアームの数により，ほとんど全ての推定量でバイアスが大きな影響を受けること示し
た．SWEでc=2や4としたものとDEだけが，バイアスが安定して0付近となった．
DEの分散が他の推定量のものよりも高いため，SWEがこの実験では良い成績であっ
たと言える.
また，高い期待値と低い期待値のアームの割合2 : 3に保ったまま．アームの数を増
79
--- PAGE 90 ---
やした場合で実験し，別の尺度での推定量のロバストさを計測する．結果は図 6.6に示
した．アームの数が増えるにつれて，多くの推定量，特にMEとAVEで，正と負の方
向に大きなバイアスになる傾向が見られた．DE，MM（=0:7），SWE（c=2）がバ
イアスを0に近く保つことが出来た．また，DEの分散は他のものよりも高い傾向が見
られた．
6.4 まとめ
本研究では，multi-armed bandit の設定で，平均利得の最大値を見積もる方法につ
いて議論し，新しい推定量Simpli(cid:12)ed Weighted Estimator（SWE）を提案した．SWE
では，アームiが最善であるとした仮定した時，iの平均利得とその最大値との差の尤
もらしさをもとに，サンプルの各要素に重みづけし，重み付き和をとり，その値を推定
値とする方法である．この手法は，負のBiasである単純にサンプルの平均を推定値と
する方法（AVE）と正のバイアスである平均利得の最大値を推定値とする方法（ME）
の間の性質を持ち，始めは前者に近く，十分にサンプルが集まると後者に近くなるとい
う手法であり，パラメータcを導入し，その近づく速さを調整する．理論的な解析によ
り，一部のアームのサンプルサイズが極端な割合で増えることがなく，全てのアームの
サンプルサイズが十分に大きければ，提案手法のバイアスは0に収束することを示し
た．実験ではUCB1，softmax，epsilon-greedyという３つの代表的なアルゴリズムに
対する安定性，期待値が高いアームが増えることに対する安定性，アームが増えるこ
とに対する安定性を見た．実験でMSEを計測した結果，設定しだいで，既存手法が上
回る場合があるものの，様々な設定に対する安定してMSEが低いという点で提案手法
（パラメータc=2）は優れていた．
80
--- PAGE 91 ---
0.3
0.2
0.1
0.0
0.1
0.2
0.3
0.4
0.5
0 50 100 150 200
No. of samples collected
saiB
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.14
0.12
0.10
0.08
0.06
0.04
0.02
0.00
0 50 100 150 200
No. of samples collected
ecnairaV
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.40
0.35
0.30
0.25
0.20
0.15
0.10
0.05
0.00
0 50 100 150 200
No. of samples collected
ESM
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
図 6.1: 左から順にSoftmaxに従って，サンプルサイズを増やした時の推定量のバイア
ス，分散，MSE．バイアスとMSEについてSWEは素早く0に近づいていることが分
かる．他の推定量ではAVE ≪ MM ≪ 0 < MEという関係であった．DEはサンプル
サイズが小さい時に高い分散の値であることが分かる．
81
--- PAGE 92 ---
0.3
0.2
0.1
0.0
0.1
0.2
0.3
0.4
0.5
0 50 100 150 200
No. of samples collected
saiB
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.14
0.12
0.10
0.08
0.06
0.04
0.02
0.00
0 50 100 150 200
No. of samples collected
ecnairaV
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.40
0.35
0.30
0.25
0.20
0.15
0.10
0.05
0.00
0 50 100 150 200
No. of samples collected
ESM
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
図 6.2: 左から順にUCB1に従ってサンプルサイズを増やした時の推定量のバイアス，
分散，MSE．図 6.1と同様，AVEとMMは大きなバイアスであることが分かる．
82
--- PAGE 93 ---
0.3
0.2
0.1
0.0
0.1
0.2
0.3
0.4
0.5
0 50 100 150 200
No. of samples collected
saiB
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.14
0.12
0.10
0.08
0.06
0.04
0.02
0.00
0 50 100 150 200
No. of samples collected
ecnairaV
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.40
0.35
0.30
0.25
0.20
0.15
0.10
0.05
0.00
0 50 100 150 200
No. of samples collected
ESM
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
図 6.3: 左から順にepsilon-greedyに従ってサンプルサイズを増やした時の推定量のバ
イアス，分散，MSE．
83
--- PAGE 94 ---
0.3
0.2
0.1
0.0
0.1
0.2
0.3
0.4
0.5
0 50 100 150 200
No. of samples collected
saiB
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.14
0.12
0.10
0.08
0.06
0.04
0.02
0.00
0 50 100 150 200
No. of samples collected
ecnairaV
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.40
0.35
0.30
0.25
0.20
0.15
0.10
0.05
0.00
0 50 100 150 200
No. of samples collected
ESM
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
図 6.4: 左から順にuniformに従ってサンプルサイズを増やした時の推定量のバイアス，
分散，MSE．図 6.1と同様，AVEとMMは大きなバイアスであることが分かる．
84
--- PAGE 95 ---
0.3
0.2
0.1
0.0
0.1
0.2
0.3
0.4
0.5
0 2 4 6 8 10
No. of arms with high expectations
saiB
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.14
0.12
0.10
0.08
0.06
0.04
0.02
0.00
0 2 4 6 8 10
No. of arms with high expectations
ecnairaV
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.40
0.35
0.30
0.25
0.20
0.15
0.10
0.05
0.00
0 2 4 6 8 10
No. of arms with high expectations
ESM
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
図 6.5: (cid:22)=0:8のアームの数に対する推定値：左からそれぞれバイアス，分散，MSE
（UCB1で40回プレイ時）．
85
--- PAGE 96 ---
0.3
0.2
0.1
0.0
0.1
0.2
0.3
0.4
0.5
5 10 15 20 25
No. of arms
saiB
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.14
0.12
0.10
0.08
0.06
0.04
0.02
0.00
5 10 15 20 25
No. of arms
ecnairaV
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
0.40
0.35
0.30
0.25
0.20
0.15
0.10
0.05
0.00
5 10 15 20 25
No. of arms
ESM
AVE
ME
DE
MM 0.3
MM 0.7
SWE 1
SWE 2
SWE 4
図6.6: アームの数に対する各種の値の依存度合い．左からバイアス，分散，MSE（UCB1
に従って，40回プレイ時 ）．
86
--- PAGE 97 ---
第7章 MCTSにおける期待値の最大値
の推定量の改善
MCTSの代表的なアルゴリズムUCTはシミュレーションを複数回行い，その利得
の平均に基づき最善手を判別する．しかしながら，最善手を判別するために，子の局面
の利得の理論値の最大値を推定し，比較する必要があり，最善の判別のための推定を，
シミュレーションの利得の平均値で行うことには改善の余地がある．本章では，平均値
の代わりに，6章で提案した，期待値の最大値の推定量Simpli(cid:12)ed Weighted Estimator
を応用した探索手法を提案する．二人零和完全情報ゲームを対象に，実験を通じて，推
定値の正確さ，ばらつき，最善手を選べるかの観点からその効果を議論する．実験の結
果，序中盤に近い局面のモデルでは安定して効果があり，終盤に近い局面のモデルでは
一部の状況で効果があった．この結果は，前者のモデルの方がMABに近い性質を持つ
ことから，事前の予想と整合するものである．
尚，本章の内容は発表済みの論文 [81]に記した実験の他に，追加実験を行い，加筆，
編集したものである．
7.1 背景
モンテカルロ木探索 (MCTS) は，囲碁やGeneral Game Playing等で成功したアル
ゴリズム [8,61]で，ゲーム分野での主要な探索アルゴリズムの一つである．MCTSの
代表的なアルゴリズムUCTはシミュレーションを繰り返し行い，その平均利得が良い
手を優先して深く読む．より詳しく述べると，UCTでは，各手の利得の理論値をシミュ
レーションの平均利得で推定するということである．しかしながら，各手の利得の推定
を平均で行うことが最良かは明らかでない．
ゲームでの探索の主要な目的は良い手（特に最善手）を素早く見つけることである．
最善手は，以後互いに最善を尽くすという仮定のもとでの利得の期待値が最大の手で
あり，それを判別するためには利得の期待値の最大値を十分な精度で推定する必要があ
る．UCTは，手の利得の推定を平均によって行うが，有望そうな葉からより多くのシ
ミュレーションを行うという性質のため，漸近的には，正しい値に収束する．しかしな
がら，6章の実験で確かめたように，少数の手の期待値が高く，多数の手の期待値が低
い場合，多数の手の期待値の影響で推定値が低くなり，正しい値になるまでに時間がか
かる [35]ため，実用的な観点から見て，改善の余地がある．
例えば，一人ゲームでは，Mario AI Benchmarkにおいて，UCTの推定値を単にシ
87
--- PAGE 98 ---
ミュレーションの平均利得でなく，子の内で最大の利得の値を重視するという工夫を
することで，計算資源が一定のもとで行動選択が改善したという研究がある [36]．ま
た，UCTでの節点の推定値において，分散も用いて評価し，はっきりと推定値が悪い
場合に，その値を親の評価に用いないという工夫をして，Sailing [42]で改善を示した
研究 [9]や，加えて，二人ゲームでは，囲碁において，探索アルゴリズムはUCTでは
ないものの，平均をとるよりも最大をとるほうが，シミュレーションが十分なされた場
合に推定値の誤差が少ないという実験結果 [17]もある．また，最近選んだ子の推定値
をより重視して親の推定値を計算するという工夫をしたAccerated UCT [29]は，囲碁
等のゲームでUCTより優れた結果となったという研究もある．
本章では引き続き二人零和有限確定完全情報ゲームを対象に，MCTSにおける利得
の推定の方法とそれによるMCTSの性能の改善について議論する．特に，6章で提案
した推定量，Simpli(cid:12)ed Weighted Estimator [35]をMCTSに組み合わせた手法を新た
に提案する．SWEは6章で確かめたように，MABでは効果があったが，木の設定で
は不明である．そこでMABからの設定の離れ具合が異なる，2種類の木で実験を行っ
た．１つはリーフが確率的に利得を返す設定で，現実のゲームでは序中盤に近い状況に
重点をおいたモデルに対応する．もう１つはリーフが確定的に利得を返す設定で，終盤
に近い局面のモデルに対応する．実験では，提案手法と既存手法の性能について，最善
手を見つけるまでの早さと，その推定値が正確かの観点から調査する．
7.2 UCTSWE
UCTでの手の利得の推定はシミュレーションの利得の平均で行っているが，より優
れた最大値の推定量を使うことで改善出来ると期待される．それは，以下の理由によ
る．簡単のため，特に局面の遷移が確定的（局面とそこで指した手に対して，次の局
面が一意に決まる）ゲームを対象に説明する．まず，最善手は理論値が最も良い手であ
り，最善手の判別のために理論値を見積もる必要がある．そのため，手の利得の推定の
目標はその手の利得の理論値を正確に見積もることである．そして，手の利得の理論値
は次の局面での候補手の利得の理論値の最大値である．従って，利得の期待値の最大値
を正確に見積もることが出来れば，性能を改善出来ると期待される．
本章では，6章で提案し，多腕バンディット問題（MAB）で優れた性能を示したSWE
をUCTに組み合わせた手法を提案する．具体的には，提案手法では，既存手法UCT
での手の利得の推定方法を平均利得（AVE）から，SWEに変え，子の推定値からSWE
に従って，親の推定値を計算する．
incremental random treeをMCTSで探索することと6章でのMABの設定でアー
ムを引くことは異なる点があり，重要な点を２点挙げる．１つは，MCTSでは，利得
の推定値に基づいてシミュレーションする葉を決める一方で，後者では，アームの平均
利得に基づいて引くアームを決めるので，平均利得はアームの選択に影響しても，平均
利得から計算される，推定値の大小は影響しないという点である．もう１つは，MCTS
88
--- PAGE 99 ---
では，利得は独立同一分布からほとんど得られないということである．推定最善を重視
し推定値を算出する方法として，6章で比較したように様々な方法が考えられるが，例
えばMEを使うと問題が生じる．特に本研究の葉の展開方法では，同じ葉からシミュ
レーションは２度行わないため，全ての節点でMEによる推定を行うと，各節点での利
得の推定値は葉でのシミュレーション１回の利得という，ばらつき大きい値のミニマッ
クス値になる．それを避けるために，最初はAVEで評価し，十分に利得が集まった節
点からMEで評価する等4章でのHybrid MCTS [34,53]の枠組みで応用することも考
えられるが，その場合，その節点がMin手番，Max手番かによって，推定値が負，正
に偏るという別の問題も生じる．具体的には，兄弟間でMEで推定されたものとAVE
で推定されたものがあると推定値が著しく不均一になり安定しないという問題である．
本章では，比較のため，独立同一分布で何度も利得が得られるという意味では多腕バン
ディット問題により近いゲーム木（ 7.3節で導入）も扱う．
提案手法では，UCTでAVEの代わりにSWEを使い，推定最善を重視する．SWE
を使うことで推定最善の重みを，訪問数が大きく（十分に利得が集まる）につれて滑ら
かに増やすことができる．但し，MCTSの場合，MABとは異なり，ある手を選んだ場
合に得られる利得の分布は定常ではない．そのため， 6.2節の補題 1が成り立たない．
この点に関して，提案手法では，2.6節で紹介した，推定値がその期待値の付近から離
れることは稀であるという，UCTにおけるC に関する想定に基づき，SWEの重みの
p
計算に適用する．具体的には，提案手法では，SWEにおける子節点iの重みw~ として
i
8 ( ( ) )
><
exp (cid:0) cdmp;i
p
NmpNi
2
(i̸=m);
w~ = Cp Nm+ Ni
i >:
1 (i=m);
を使用する．UCTにSWEを適用するにあたり，このように重みを修正した．また，
2.6節で紹介したAccerated UCTと同様に，展開前のシミュレーション結果について
は，仮想的な子を作って利用した．
7.3 確率的な利得が得られるゲームのモデル
本節では， 2.2.1 節で紹介した incremental random tree と比べて，より多腕バン
ディット問題に近いと考えられるゲーム木について紹介する．このゲーム木は大雑把に
は，incremental random treeの終端節点で確率的に利得が得られるとしたものである．
2.2.4節のMarkov decision processと比較すると，状態の遷移が確率的でないという意
味では，より単純であり，二人ゲームであるという意味では，より複雑である．
考案した木についてより詳しく述べる．この木では，incremental random treeのよ
うに，各辺に値を割り当て，終端節点でのスコアを計算する．終端節点iでのスコアを
s とする．全ての終端節点について，取りうるスコアの最大値，最小値をそれぞれU;L
i
とする．終端節点iで得られる，Maxにとっての利得をr とすると，r が(cid:0)1;1にな
i i
る確率をそれぞれP(r i =1):= s U i (cid:0) (cid:0) L L，P(r i =(cid:0)1):= U U (cid:0) (cid:0) s L i で定める．
89
--- PAGE 100 ---
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 50000 100000 150000 200000 250000 300000 350000 400000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
p
図7.1: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝(8;2)の誤答率の推移
p
木はスコアに基づき，終端節点で得られる利得の期待値を決めているため，incremental
random treeと同様に，最善手が分析者に予め分かるという長所を持つ．特にこの木で
の根での利得の期待値の理論値は0である．
7.4 実験
本実験では，SWEによる推定値を使ったUCT (UCTSWE)について，ゲーム木モ
デルでの誤答率と利得の推定値を実測し，議論する．誤答率は最善手を選べなかった場
合を誤答とした，全ての試行に対する誤答の割合である．推定値は全ての試行に対し
て，平均をとった．UCTとAccerated UCTを比較対象とした．Accerated UCTのパ
ラメータ(cid:21)の値は原論文 [29]での囲碁，Havannahの対戦実験で最も良い成績であっ
た(cid:21)=0:9999とした．各アルゴリズムで，探索終了後，最善手として推定値が最良の
手を選ぶ方法も考えられるが [10]，ロバストと経験的に知られている，訪問数最大の手
を選ぶという方法を採用した．また，ゲーム木は200本生成し，それぞれゲーム木に対
し200回探索した．尚，本研究では，引き分けはMax（根で手番のプレイヤー）の勝
ちとして，Max（Min）プレイヤーの次善手の辺の値は値域[(cid:0)128;(cid:0)1]（[1;128]）の一
様分布に基づき決めている．
7.4.1 確率的な利得が得られる木
7.3節で導入した，確率的な利得が得られるモデルで実験を行った．実験は，木の
（分枝数，深さ）を (8, 2)，(8, 4)，(16, 2)，(16, 4)，(32, 2) として行った．尚，この
p
実験では，SWEのパラメータは0:5とし，C = 2とした．
p
各設定での誤答率の結果を図 7.1，7.2，7.3，7.4，7.5に示した．縦軸を誤答率とし，
横軸をプレイアウト数とした．
誤答率について，おおよその傾向として，提案手法は，プレイアウト数が少ない間は
UCT等より誤答率が高いものの，やがて提案手法の誤答率が最も小さくなった．設定
に関する傾向として，分枝数が大きいほど，誤答率が下がるまでに必要なプレイアウト
90
--- PAGE 101 ---
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 50000 100000 150000 200000 250000 300000 350000 400000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
p
図7.2: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝(8;4)の誤答率の推移
p
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 50000 100000 150000 200000 250000 300000 350000 400000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
p
図 7.3: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (16;2)の誤答率の
p
推移
数が多くなり，木の深さが深いほど，プレイアウト数が少ない時の提案手法の誤答率が
高くなった．
各アルゴリズムで探索した際の，各節点の利得の推定値の推移を計測した結果を
図 7.6,7.7,7.9,7.8,7.10に示した．利得の推定値はMaxプレイヤー視点での利得（Max
プレイヤーが勝ちなら1，負けなら(cid:0)1）で表記した．図中のroot,opt,best,best’sbest
は各節点の推定値を表し，節点はそれぞれ，根，根の最善の子，根の推定最善（子の中
で推定値最大）の子，推定最善の子の推定最善の子を意味する．縦軸を各節点での利得
の推定値とし，横軸をプレイアウト数とした．尚，各節点の推定値は0が理論値であ
る．全体として，提案手法，Accerated UCT，UCTの順で，推定値はより素早く正し
い値に収束する傾向がみられた．
7.4.2 確定的な利得が得られる木
次に，終端節点で確定的な利得が得られる２種類の木で実験を行った．１つはincre-
mental random treeである．もうひとつは，incremental random treeで枝刈りを行っ
た場合に相当する木である．
91
--- PAGE 102 ---
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 50000 100000 150000 200000 250000 300000 350000 400000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
p
図 7.4: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (16;4)の誤答率の
p
推移
0.4
0.35
0.3
0.25
0.2
0.15
0.1
0.05
0
0 50000 100000 150000 200000 250000 300000 350000 400000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
p
図 7.5: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (32;2)の誤答率の
p
推移
Incremental Random Tree
まず予備実験として，incremental random treeをUCTで探索し，利得の推定値を
計測した．そして，推定最善の子の推定値を重視することで，推定値が真の値に近づく
余地があるかを検証する．より具体的には，実験で，利得の推定値が利得の理論値から
どれだけ離れているかをプレイアウト数毎に計測し，推移を見た．UCTの探索には2.6
節で紹介したMCTS-Solverを併用した．もし，各節点の勝ち負けが確定したら，その
利得の推定値を正しい値（勝ち負けならそれぞれ1;0;(cid:0)1）に書き換えた1．5章で導
入したMCTS-Solverの変種ReplacementMCTS-Solver(Replace)[80]をUCTに組み
合わせた場合 (UCTReplace) の推定値も計測した．この手法では，勝ち負けが確定し
たら，その節点の値を正しい値に書き換えるだけでなく，書き換えた節点の訪問数分だ
け，先祖の値も書き換える．
実際にUCTで探索した際の，各節点の利得の推定値の推移を計測した結果が図 7.11
である．本節の木では，各節点の推定値は1が理論値であり，その値により早く近づく
方が望ましい．尚，利得の推定値は区間[(cid:0)1;1]中の値をとるが，実験結果は，正の値を
取ることがほとんどであったので負の部分は省いた．また，全ての試行において，8000
15章で議論したように，この書き換えは祖先の推定値には影響しない．この書き換えの意図は節点の勝
ち負けが確定が確定した際にその節点の利得の推定値として自然な値を計測することにある．
92
--- PAGE 103 ---
(a) UCT
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(b) AcceratedUCT(cid:21)=0:9999
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(c) UCTSWEc=0:5
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.6: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (8;2) での利得の推
p
定値の推移
プレイアウト時点で，MCTS-Solverにより根の勝ち負けが判明することはなかった．
UCTにおいて，親の利得の推定値は子の推定値の重み付き平均であるので，基本的には
推定値はbest’s best (cid:20) bestであり，root (cid:20) bestである2．実験の結果，MCTS-Solver
の種類にかかわらず，プレイアウト数が増えるとbest’s bestの推定値はrootよりも利
得の理論値である1に近くなった．つまり，プレイアウトが多い時は推定最善の手をよ
り重視した方が利得の理論値に近い推定値となる．これは，推定最善の子をより重視し
て，親の推定値を算出するという方向性が有望であることを示している．
まず予備実験と同様の設定と（分枝数，深さ）を(8,12)とした設定で，各アルゴリズ
2best’sbest>bestとなる例外は，MCTS-Solverの挙動と勝敗確定時の書き換えによって生じる．簡
単のため，best=optとし，optの子節点はA，Bの２つだけとする．子節点A，BでそれぞれMinの勝
ち，負けが多く出ているとする．その時，optの推定値はBの推定値以下，かつAがoptの推定最善の子
となる．そして，AがMinの負け（利得1）と確定した場合．optの最善の子はBになり，前述の例外が
起きる．尚，ReplaceではAの勝敗が確定するとoptを含むAの祖先も書き換えるため，この例外は起こ
らない
93
--- PAGE 104 ---
(a) UCT
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(b) AcceratedUCT(cid:21)=0:9999
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(c) UCTSWEc=0:5
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.7: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (8;4) での利得の推
p
定値の推移
ムの誤答率を計測した．以下の実験では，SWEのパラメータはc=0:01;0:05;0:1;0:5
p
の4種類とし，C の値を 2を基準として1;1:5;2倍して計測した．尚，誤答率につ
p
p
いては，以下の全ての実験でC = 2とした時が8000プレイアウトでのUCTの誤答
p
率が最も低かった．また，SWEと組み合わせるMCTS-Solverとして，Replaceを使っ
た．従って，c=0とすると提案手法はUCTReplaceと同じになる．
（分枝数，深さ）が (8, 6) の際の実験結果（図 7.12(a)）では誤答率は，SWEのパラ
メータcを適切に調整しても，UCTの誤答率からほとんど改善しなかった．特にc=0:5
では悪化した．また，確率的な利得が得られる木の実験と比べて，プレイアウトの数が
少ないため，UCTとAccerated UCTの間に差はほとんどつかなかった．尚，8000プ
レイアウト行うまでに，探索木の一部が終端節点まで達したが，根の勝ち負けが確定す
ることは無かった．
次に（分枝数，深さ）を (8, 12) に設定した，より大きな木での結果を（図 7.12(b)，
94
--- PAGE 105 ---
(a) UCT
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(b) AcceratedUCT(cid:21)=0:9999
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(c) UCTSWEc=0:5
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.8: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (16;4) での利得の
p
推定値の推移
図 7.12(c)）に示した．尚，この設定では，全ての探索で，探索木の葉が終端節点まで
達したことは一度もなく，従って，MCTS-Solverの影響が無いため，UCTReplaceは
UCTと同じである．（分枝数，深さ）が8 − 12での結果はC が２倍の時（図 7.11(c)）
p
では多少改善したが，C が１倍の時（図 7.11(b)）はcを調整してもほとんど改善しな
p
い．また，c=0:5では悪化した．
つづいて，各手法での推定値について調査した．UCTとの違いがよく分かるように，
SWEのパラメータを調節した中で最も大きいc=0:5の結果を載せる．図 7.11と同じ
設定でのUCTSWEの推定値のプレイアウト数による変化を図 7.13に示した．図 7.13
で各推定値はUCTReplace (図 7.10(b)) のものより互いに近くなった．SWEのパラ
メータcの推定値に対する影響を調べるため，プレイアウト数4000と8000での最善手
の推定値と次善手の推定値についてさらに詳しく調べた．全探索中の推定値の平均値，
及びそれらの標準偏差について表 7.1に示した．パラメータcを増やすと，4000プレ
95
--- PAGE 106 ---
(a) UCT
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(b) AcceratedUCT(cid:21)=0:9999
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(c) UCTSWEc=0:5
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.9: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (16;2) での利得の
p
推定値の推移
イアウト時の最善手の推定値は変わらないものの，次善手の推定値が低くなり，真の値
(cid:0)1に近づいた．8000プレイアウト時も4000プレイアウトと同様の傾向であった．
次に，（分枝数，深さ）を (8;12) とした場合の推定値の推移について図 7.14に4000,
8000プレイアウト時の最善手と次善手の推定値について表 7.2に示した．図 7.14では，
図 7.11や図 7.13の場合と異なり，プレイアウトを増やしても，最善手の推定値の平均
値がほとんど改善しなかった．表 7.2では，cを大きくするにつれて，次善手だけでな
く最善手も推定値が下がる傾向見られた．また，どちらの推定値の標準偏差も上がった．
平均的には最善の推定値は次善のものよりも大きくなったが，その場合で標準偏差が大
きかったということは，最善手の推定値を次善手の推定値が上回ることも多かったと予
想される．そのことが提案手法の性能の悪さの一因と予想される．
96
--- PAGE 107 ---
(a) UCT
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(b) AcceratedUCT(cid:21)=0:9999
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(c) UCTSWEc=0:5
0.1
0.05
0
-0.05
-0.1
0 50000 100000 150000 200000 250000 300000 350000 400000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.10: 確率的な利得が得られる木，C = 2，（分枝数，深さ）＝ (32;2) での利得の
p
推定値の推移
特異節点を含む木
推定最善の評価を重視したUCTSWEでの改善はincremental random treeでは限定
的であった．最善を重視するのが良いのはどのような場合か明らかにするため，新たな
ゲーム木モデルを導入する．このモデルでは， 2.2節のincremental random treeの変
種として，辺にランダムに値を割り当てるだけでなく，ランダムに特異節点を作る．特
異節点以降の辺の値はどの辺も0とする．つまり，終端節点の勝ち負けを決める辺の値
の総和は根節点から辿って特異節点まで和をとったら，それ以降どんな辺を辿っても変
わらないという意味で特異節点は擬似的な終端節点である．そのため，特異節点以降で
シミュレーションを開始すると互いに最善を尽くした場合の結果が必ず返ってくるとい
う性質がある．実験では，難しさの調節のため，規定深さ以下の節点（本実験では，深
さ3，つまり根から3手以内の節点）では特異節点は作らず，また，最善の子節点（最
善手を指した後の局面に相当する節点）は特異節点にしないという制約を加えた．それ
97
--- PAGE 108 ---
(a) UCT+MCTS-Solver
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(b) UCT+ReplacementMCTS-Solver
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.11: C = 2，（分枝数，深さ）＝ (8;6) での利得の推定値の推移
p
p
表 7.1: C = 2，（分枝数，深さ）＝ (8;6)，各プレイアウト数での利得の推定値（左
p
が最善手，右が次善手，上段が平均値，下段が標準偏差）
Playout UCT c = 0 c = 0.1 c = 0.5
Ave 0.5265 -0.1572 0.4719 -0.1851 0.4515 -0.1892 0.4736 -0.2628
4000
Std. dev 0.1050 0.3092 0.1994 0.2789 0.1856 0.2780 0.2443 0.2615
Ave 0.7016 -0.1585 0.8775 -0.1952 0.8685 -0.2017 0.8523 -0.2996
8000
Std. dev 0.0557 0.3090 0.0792 0.2691 0.0873 0.2671 0.0973 0.2289
以外の節点は確率0:1で特異節点とした．この木を特異節点を含む木とよぶ．この木で
は特異節点の存在により，はっきりと悪い子節点出来得るため，その推定値に親が影響
されないことがincremental random treeと比べてより重要と考えられる．
まず，特異節点を含む木での，各アルゴリズムでの誤答率を計測した．UCTでの子
の選択をUCB1ではなく，一様に行う場合，つまり訪問数が同じになるように子を訪
問し（ランダムにタイブレークし）た場合の誤答率も計測した．この場合は推定値最良
のものを最善手として選んだ．結果を図 7.15示す．一様に探索した場合，誤答率がほ
とんど減少しないのに対し，SWEを使うとより速く誤答率が下がった．尚，一様な探
索での，プレイアウト数8000の時の探索木の葉の最大深さは5であり，特異節点を訪
問し得る深さまで探索木が成長している．同様に，C が2;1:5倍の時もSWEを使うこ
p
p
とで改善した．しかしながらC を調整した中でUCTの結果が最も良かったC = 2
p p
の場合で，c=0:5とした場合では改善しなかった．
98
--- PAGE 109 ---
p
(a) Cp= 2（分枝数，深さ）＝(8;6)
0.6
0.5
0.4
0.3
0.2
0.1
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
Accerated
UCT
UCTSWE
UCTReplace
#Playouts
p
(b) Cp= 2（分枝数，深さ）＝(8;12)
0.6
0.5
0.4
0.3
0.2
0.1
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
Accerated
UCT
UCTSWE
#Playouts
p
(c) Cp=2 2（分枝数，深さ）＝(8;12)
0.6
0.5
0.4
0.3
0.2
0.1
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
図 7.12: incremental random treeで，c=0:5とした時の誤答率の推移
前節と同様に特異節点を含む木を探索した際の推定値について調査した．結果を図7.16
に示した．図 7.16では，図 7.14と異なり，best’s bestだけでなく，bestにも緩やかな
上昇傾向が見られ，正しい値1に近づいた．表 7.2と同様に特異節点を含む木でも最
善手と次善手の推定値を表 7.3に示す．cが大きいほど次善手の推定値が低いだけでな
く，次善手に至っては標準偏差も低く抑えられた．最善手は推定値は高くなるが，その
標準偏差も高くなった．平均的には最善手も次善手もより正確な値となったと言える．
これは，cが大きくなるほど最善手の推定値が下がった表 7.2とは異なる傾向である．
但し，最善手の推定値の標準偏差はincremental random treeと同様にcが大きいほど
高くなった．
最後に，より大きな木として，分枝数を16に増やした場合の誤答率を図 7.17に示
す．分枝数，深さ16 - 12の木も8 - 12の木と同様にC が高い場合にSWEを用いる
p
p
ことで誤答率が改善した．この設定ではC = 2で，c=0:5の時でも，2000プレイ
p
アウト付近では悪化したものの，12000プレイアウト付近では改善した．
99
--- PAGE 110 ---
1
0.8
0.6
0.4
0.2
0
0 1000 2000 3000 4000 5000 6000 7000 8000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.13: UCTSWE（c=0:5，C = 2），（分枝数，深さ）＝ (8;6) での利得の推定値
p
の推移
p
表 7.2: C = 2，（分枝数，深さ）＝ (8;12)，各プレイアウト数での利得の推定値（左
p
が最善手，右が次善手，上段が平均値，下段が標準偏差）
Playout UCT (c = 0) c = 0.1 c = 0.5
Ave 0.2336 -0.1070 0.2322 -0.1089 0.2123 -0.1508
4000
Std. dev 0.0601 0.2197 0.0604 0.2196 0.0846 0.2245
Ave 0.2300 -0.1127 0.2290 -0.1146 0.2127 -0.1613
8000
Std. dev 0.0535 0.2132 0.0541 0.2135 0.0910 0.2208
p
最後に補足として，C の設定の重要性について明らかににするため，C を 2の
p p
0:25;0:5;0:66;1;1:5;2倍した時のUCTの8000プレイアウト時の誤答率について表7.4
にまとめた．C の調整することで性能が上がるが，最も性能を発揮するようなC の
p p
値は木によって異なるという結果となった．
7.5 まとめ
MCTSの代表的な手法，UCTではシミュレーションの利得の平均値で各手の評価を
行っている．本研究では，MCTSにおける，各手の利得の推定方法を平均ではなく，推
定最善の値をより重視して行う手法として，期待値の最大値の推定量である，Simpli(cid:12)ed
p
表 7.3: 特異節点を含む木（分枝数，深さ）＝ (8;12)，C = 2各プレイアウト数での
p
利得の推定値（左が最善手，右が次善手，上段が平均値，下段が標準偏差）
Playout UCT (c = 0) c = 0.1 c = 0.5
Ave 0.1880 -0.2783 0.1906 -0.2805 0.2313 -0.3382
4000
Std. dev 0.0953 0.2243 0.0990 0.2227 0.1889 0.2066
Ave 0.2645 -0.2842 0.2724 -0.2868 0.3603 -0.3556
8000
Std. dev 0.1116 0.2178 0.1158 0.2164 0.2051 0.1979
100
--- PAGE 111 ---
(a) UCT
1
0.5
0
-0.5
-1
0 1000 2000 3000 4000 5000 6000 7000 8000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(b) UCTSWEc=0:5
1
0.5
0
-0.5
-1
0 1000 2000 3000 4000 5000 6000 7000 8000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.14: C = 2（分枝数，深さ）＝ (8;12) での利得の推定値の推移
p
p
表 7.4: C = 2kとした時の8000プレイアウト時の誤答率
p
k 2 1.5 1 0.66 0.5 0.25
incremental random tree 0.266 0.248 0.216 0.182 0.183 0.249
特異節点を含む木 0.147 0.092 0.037 0.006 0.003 0.004
WeightedEstimator(SWE)を用いたUCT(UCTSWE)を新たに提案した．そして，誤
答率，推定値の正確さの観点から改善するかについて実験を通じて調査した．実験では，
確率的な利得が得られる木と確定的な利得が得られる木の大きく分けて２種類のゲーム
木モデルを用いた．確率的な利得が得られる木は，SWEが優れた性能を示した多腕バ
ンディット問題により近いと考えられる．実験の結果，プレイアウト数を増やした際の
誤答率，推定値ともにUCTよりも提案手法の方が優れた性能を示した．確定的な利得
の得られる木として，incremental random treeに加えて，特異な節点（擬似的な終端
節点）をランダム生成するという工夫を加えた木を用いた．実験の結果，incremental
p
random treeでは，UCTのパラメータC が2 2と大きい時に微小ながら改善したも
p
p
のの．C が 2と小さい場合に悪化した．特異な節点を含む木では，UCTのパラメー
p
p
タC が 2と小さい場合を除き改善した．推定値の分析から，悪化した原因の一つは
p
最善手の推定値の標準偏差の高さにあると予想される．
今後の課題の一つ目は一人ゲームで試すことである．二人ゲームでは，悪い手をとっ
ても，相手も悪い手をとれば，互いに最善をとったときと形勢がほとんど大差無いとい
うことが起こるのに対し，一人ゲームでは起こらない．そのため，平均によって利得を
101
--- PAGE 112 ---
推定することでの不正確さは著しくなると予想される．
今後の課題の二つ目はSWEのMCTSへの応用の仕方の再考である．SWEでの重み
の減らし方は独立同一分布と仮定した場合に基づいている．本研究では，UCTの仮定
に基づきC を含む形で重みの減らし方を微調整したものの，この仮定は十分にシミュ
p
レーションをした場合についての仮定であり，シミュレーション数に限りがある場合の
重みの減らし方についてはまだ明らかでない．
102
--- PAGE 113 ---
(a) 一様な探索
0.6
0.5
0.4
0.3
0.2
0.1
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
p
(b) Cp=2 2
0.6
0.5
0.4
0.3
0.2
0.1
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
p
(c) Cp=1:5 2
0.6
0.5
0.4
0.3
0.2
0.1
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
p
(d) Cp= 2
0.6
0.5
0.4
0.3
0.2
0.1
0
0 1000 2000 3000 4000 5000 6000 7000 8000
etar
eruliaF
Accerated
UCT
UCTSWE0.5
#Playouts
図 7.15: 特異節点を含む木（分枝数，深さ）＝ (8;12) でC を調節した場合の誤答率
p
の推移
103
--- PAGE 114 ---
(a) UCT
1
0.5
0
-0.5
-1
0 1000 2000 3000 4000 5000 6000 7000 8000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
(b) UCTSWEc=0:5
1
0.5
0
-0.5
-1
0 1000 2000 3000 4000 5000 6000 7000 8000
noitaulavE
Root
Best
Opt
Best’s best
#Playouts
p
図 7.16: 特異節点を含む木，C = 2（分枝数，深さ）＝ (8;12) での利得の推定値の
p
推移
p
(a) Cp=2 2
0.6
0.5
0.4
0.3
0.2
0.1
0
0 2000 4000 6000 8000 10000 12000 14000 16000
etar
eruliaF
Accerated
UCT
UCTSWE
#Playouts
p
(b) Cp= 2
0.6
0.5
0.4
0.3
0.2
0.1
0
0 2000 4000 6000 8000 10000 12000 14000 16000
etar
eruliaF
Accerated
UCT
UCTSWE
#Playouts
図 7.17: 特異節点を含む木（分枝数，深さ）＝ (16;12) でC を調節した場合の誤答率
p
の推移
104
--- PAGE 115 ---
第8章 結論
本研究では，探索アルゴリズムの内，モンテカルロ木探索（MCTS）を対象に，改善
へ向けた研究を行った．探索アルゴリズムは，外界のモデルを使って，複数の行動（候
補手）の中から良い行動（手）を見つける方法であり，その効率性を改善することで，
例えばゲームにおいて，良い手を効率的に見つけられるようになり，上手くプレイ出来
るエージェントが作れる等の意義がある．探索アルゴリズムの中でも，MCTSは，シ
ミュレーションに基づき，手を評価し，先読みする方法であるため，予め対象のドメイ
ンの知識を持たなくてもそれなりに上手く探索が可能であるという良い特徴を持つ．
本研究では，モンテカルロ木探索（MCTS）について，様々な仮定のもとで，どのよ
うに探索を行うことで性能を発揮出来るかを分析した．そして，既存手法の弱点とそれ
に対する改善策を提示した．探索の対象としたゲーム木の仮定の観点から整理すると
3章では，一方のプレイヤが有利であるゲーム木を扱い，4，5，7章では終端節点の深
さが一様でないゲーム木を扱った．これらのMCTSでの知見が比較的得られていない
ゲーム木のもとでの優れたMCTSについて研究した．また，理論的な仮定という観点
で整理すると4章では，節点での利得の理論値の主観確率分布が得られ，その分布が兄
弟間で独立と仮定した場合にMCTSでどこから，どれだけ重点的にシミュレーション
を行うべきかについて議論し，6，7章では，アームを引く（節点を訪問する）回数が
確率変数でないという仮定のもとでの，優れた期待値の最大値の推定値について議論し
た．加えて，本研究をMCTSの要素技術の観点でまとめると，シミュレーションの利
得の与え方について3章で，シミュレーションを開始する節点の選び方について4，5
章で，推定値の算出の仕方について6，7章で主に議論した．
本研究の意義の１つは上記のように，様々な仮定のもとでどのようなMCTSが良い
のかの観点や，MCTSの要素技術について３つの観点等，多角的にMCTSについての
研究を行い，MCTSに対するより深い理解に繋がることにある．そして，その知見が，
様々な特徴を持つ局面で，万遍なく優れた性能を発揮出来るMCTSを考案するための，
手がかりになると期待される．以下で各章で行った研究について簡単に振り返る．
3章では，局面の特徴として，次善手の偏りを定義し，分析した．この特徴は最善手
を選べなかった時の損失が片方のプレイヤーの方が多く，形勢が大きく偏るが，最善手
を選び続ければ，形勢は互角という特徴であり，囲碁での攻め合いで負けると多くの石
を取られる（取れる）が，互いに最善を尽くしても形勢に大差ないという状況に対応す
る．そして，この次善手の偏りが大きいほどMCTSの性能が下がることを示した．ま
た，ゲーム上のスコアを利用して，シミュレーション結果を補正することで性能の低下
105
--- PAGE 116 ---
を緩和できることを示した．しかしながら依然として，性能の低下はあるという意味
で，MCTSが性能を発揮することが難しい局面であることを示した．
4章では，多腕バンディット問題において，最善手を見分けるためのアルゴリズムと
最善手をより多く選ぶためのアルゴリズムが異なり，かつ両立は出来ないという知見か
ら，木探索においても，アルゴリズムを使い分けることで改善を目指した．提案手法は，
MCTSでのプレイアウトをモデル化して，節点での利得の理論値の主観確率分布に基
づく，探索を続けることの価値というメタ情報を使い，シミュレーションをする葉を決
めるという初めての探索手法である．提案手法の効果を実験を通じて調査した結果，終
局の深さが一様なゲーム木では悪化したが，終局の深さがばらついているゲーム木では
改善した．この結果は，終局での勝ち負け情報が手に入らない場合に，提案手法の方法
では期待されるほど効果的にどこからシミュレーションするかを決められていないと
いうことである．その一方で，勝ち負け情報が手に入る場合には，効果的にシミュレー
ション開始節点を決められているということである．囲碁や，オセロ等のゲーム木は大
きく，ゲームの始めの方の局面では，終局は近くには無いという観点では，一様な深さ
の木の方が現実的な想定だが，十分に終局に近づいた場合，深さが一様でないゲーム木
の方が一様な場合よりも現実的な想定である．また，チェスのようなゲームでは，開始
局面から数手で終局し得るため，深さが一様でないゲーム木に近いと思われる．尚，ミ
ニマックス探索と比べて，MCTSではチェスでの性能が低いと経験的に知られている
が，確かな評価関数が手に入らないような場合，例えばGeneral Game Playingのよう
な設定では，MCTSも比較的有効であると考える．
5章では，終局に着目し，利得の理論値が判明したら，それに応じて今までのシミュ
レーション結果を修正するという手法を提案した．4章の手法では終局の深さ一様な木
では無い場合に改善し，一様な木では改善しなかった．一様な木では無い場合，ある
手を選ぶとまだゲームが続くが，他の手では終局するという局面がある．この手法は
終局が近くにある場合にその情報を上手く使うことを意図した手法であり，既存手法
MCTS-Solverの改善を試みた，初めての研究である．提案手法は4章の手法と比べて，
分布を使わず，main treeの節点全てを辿らない済む分，計算が高速である．実験の結
果，既存手法よりも優れた結果となった．この手法は終局が近くに無い場合は通常の
UCTと変わらないため，適用しても悪影響は無いという長所がある．
6章，7章では，5章での知見を一般化し，より一般的な状況での改善を目指し，終
局に限らず，新たに得た情報に基づき，今までのシミュレーション結果を解釈し直すと
いう方向で研究を行った．
6章では，最善手は期待利得が最大の手という事実に基づき，最大値の推定量につい
て，多腕バンディット問題（MAB）を対象に議論した．そして，各アームの推定値の
重み付き平均を取ることで，期待値の最大値の推定する新たな推定量を提案し，分析し
た．提案手法の重みは最善のアームである確率の上限に基づいた重みである．理論的な
分析を通じて，アームを引く回数に対する推定値のバイアスの収束性，及び，推定値
の分散についても上界を示した．加えて，実験を行った結果，提案手法は，設定に対す
106
--- PAGE 117 ---
る推定値の安定性の面で優れた性能を示した．また，バイアスと分散のバランスの観
点から，実験結果を解釈すると，UCTでの推定値の算出方法である，平均による推定
は，分散を減らすことを重視し過ぎていると明らかになった．提案手法は，直接的には
sponsored search auctionsのような，多腕バンディット問題の設定下で，期待値の最大
値を推定する問題に適用出来る．応用としては，最善である確率の上界をどのように与
えるかは議論の余地があるものの，MCTS以外にも，強化学習に適用可能である．
7章では，6章で提案した推定量を，UCTに実際に応用し，二人零和完全情報ゲーム
を対象に，提案手法を分析した．最善である確率の上界が推定量の重みを計算するため
に必要となるが，7章では上界をUCTの収束性の仮定に基づき定めた．実験では，終
端節点で確率的な利得が得られる場合と確定的な利得が得られる場合の大きく分けて２
種類の木を用いた．ゲームでの序中盤では，終端節点まで探索木が成長すること稀で，
前者に近いと考えられ，逆に，終盤では，後者に近いと考えられる．また，前者の方が
MABに近い木である．実験の結果，確率的な利得が得られる木では，プレイアウト数
が大きい時，提案手法は既存手法より，最善手を選べるかの観点で，有効であった．確
定的な利得が得られる木での実験において，広く探索するような設定では，UCTより
も提案手法の方法の方が，良い結果となった．しかしながら，より狭く探索するような
設定では，UCTの方が良い結果となった．また，実験では，推定値についても分析を
行った．その結果，確定的な利得が得られる木で，狭い探索をした際に，改善が限定的
であった原因の一つは，推定の標準偏差の高さにあることを示唆する結果となった．本
研究は，推定値のバイアスと分散のどちらをどれだけ優先すべきかという観点で，6章
で提案した推定量をMCTSに応用した初めて研究である．
最後に，本研究に関連して，より引いた視点から，MCTSについて今後研究すべき
課題について記す．まず，応用に近い課題として，3章に関連して，探索するドメイン
の特徴による，探索アルゴリズムの使い分け，調整が挙げられる．これはつまり，ドメ
インの特徴を捉える方法と，それに適したアルゴリズムを選択，あるいはアルゴリズ
ムを調整する方法を用いることで，全体として性能を上げるということである．例え
ば， 2.2.3節で紹介したGeneral Video Game Playing（GVGP）で，好成績を残して
いるエージェントの多くは，複数の探索アルゴリズムをゲームの特徴に合わせて使い分
けており，典型的にはA*とMCTSを使い分けている．例えば，迷路のような問題で
はA*が役立つ．GVGPのゲーム中のオブジェクトは数種類であることが多いので，A*
でいくつかのオブジェクトへの経路探索を行うことで出口に行ける．MCTSにおいて
は，ナイーブにランダムシミュレーションをすると，迷路の壁にぶつかってばかりで，
出口に進まず，出口に達することは難しい．その一方で射撃等，玉を撃って敵に当てる
と利得が得られるようなゲームで，玉が出てから敵に当たるまで時間がかかる場合，時
間の分だけ深く読まない限り，利得が得られない．その一方で，一旦敵に向かって玉を
撃ってしまえば，その後は何をやっても，ランダムシミュレーションでも，玉が敵に当
たり利得が得られる．その場合，MCTSでは，玉を敵に向かって打つということを出
来る一方で，事前知識なしでのA*では，深く読めず難しい．どのように特徴を捉える
107
--- PAGE 118 ---
か，それに対してどの探索アルゴリズムを使うか，調整するかについてはまだ研究の途
上である．
基礎に近い課題としては，4章に関連して，最善手を見つけるためのシミュレーショ
ンの割り振り方法や7章に関連して，手の利得の推定方法についての研究も重要であ
る．これらは相互に関連するため，統合的な視点からさらに研究を発展させる必要があ
ると考える．
謝辞
金子知適准教授には研究に関わる全般的な指導をして頂きました．田中哲朗教授には
game programing seminarや勉強会でお世話になりました．山口和紀教授にはKYゼ
ミででお世話になりました．その他GRACOゼミ，game programing seminar，KYゼ
ミの参加者の皆様，金子研の皆様には議論に参加して頂き，有用なコメントを多数頂き
ました．日本学術振興会には，特別研究員として採用して頂き，研究の励みになっただ
けでなく，研究費を特別研究員奨励費16J07455として支援して頂きました．
心より御礼申し上げます．
108
--- PAGE 119 ---
参考文献
[1] Shipra Agrawal and Navin Goyal. Analysis of thompson sampling for the multi-
armedbanditproblem.InProceedingsofthe25thAnnualConferenceonLearning
Theory (COLT), June 2012.
[2] Jean-Yves Audibert and S(cid:19)ebastien Bubeck. Best arm identi(cid:12)cation in multi-
armed bandits. In COLT-23th Conference on Learning Theory-2010, pp. 13{p,
2010.
[3] Peter Auer, Nicol(cid:18)o Cesa-Bianchi, and Paul Fischer. Finite-time analysis of the
multiarmed bandit problem. Machine Learning, Vol. 47, No. 2-3, pp. 235{256,
2002.
[4] Terje Aven. Upper (lower) bounds on the mean of the maximum (minimum) of
anumberofrandomvariables. Journal of Applied Probability,Vol.22,No.3,pp.
723{728, 1985.
[5] P. Baudi(cid:20)s. Balancing mcts by dynamically adjusting the komi value. ICGA
Journal-InternationalComputerGamesAssociation,Vol.34,No.3,p.131,2011.
[6] Petr Baudi(cid:20)s and Jean loup Gailly. PACHI: State of the art open source go
program. InAdvances in Computer Games,Vol.7168,pp.24{38.Springer,2012.
[7] Eric B. Baum and Warren D. Smith. A bayesian approach to relevance in game
playing. Arti(cid:12)cial Intelligence, Vol. 97, No. 1, pp. 195{242, 1997.
[8] YngviBjornssonandHilmarFinnsson. Cadiaplayer: Asimulation-basedgeneral
game player. Computational Intelligence and AI in Games, IEEE Transactions
on, Vol. 1, No. 1, pp. 4{15, 2009.
[9] Zahy Bnaya, Alon Palombo, Rami Puzis, and Ariel Felner. Con(cid:12)dence backup
updates for aggregating mdp state values in monte-carlo tree search. In Eighth
Annual Symposium on Combinatorial Search, 2015.
[10] Cameron Browne, Edward J. Powley, Daniel Whitehouse, Simon M. Lucas, Pe-
ter I. Cowling, Philipp Rohlfshagen, Stephen Tavener, Diego Perez, Spyridon
Samothrakis, and Simon Colton. A survey of monte carlo tree search methods.
IEEE Trans. Comput. Intellig. and AI in Games, Vol. 4, No. 1, pp. 1{43, 2012.
109
--- PAGE 120 ---
[11] S(cid:19)ebastien Bubeck, R(cid:19)emi Munos, and Gilles Stoltz. Pure exploration in (cid:12)nitely-
armed and continuous-armed bandits. Theoretical Computer Science, Vol. 412,
No. 19, pp. 1832 { 1852, 2011. Algorithmic Learning Theory (ALT 2009).
[12] MurrayCampbell, A.JosephHoane, andFeng-hsiungHsu. Deepblue. Arti(cid:12)cial
intelligence, Vol. 134, No. 1-2, pp. 57{83, 2002.
[13] Olivier Capp(cid:19)e, Aur(cid:19)elien Garivier, Odalric-Ambrym Maillard, R(cid:19)emi Munos, and
Gilles Stoltz. Kullback-leibler upper con(cid:12)dence bounds for optimal sequential
allocation. Annals of Statistics, Vol. 41, No. 3, pp. 1516{1541, Jun. 2013.
[14] T.Cazenave. Sequentialhalvingappliedtotrees. IEEE Trans. Comput. Intellig.
and AI in Games, Vol. 7, No. 1, 2015.
[15] Benjamin E Childs, James H Brodeur, and Levente Kocsis. Transpositions and
move groups in monte carlo tree search. In Computational Intelligence and
Games, 2008. CIG’08. IEEE Symposium On, pp. 389{395. IEEE, 2008.
[16] P.-A.CoquelinandR.Munos. Banditalgorithmsfortreesearch. InUncertainty
in Arti(cid:12)cial Intelligence, 2007.
[17] R(cid:19)emi Coulom. Efficient selectivity and backup operators in monte-carlo tree
search. In Computers and games, pp. 72{83. Springer, 2007.
[18] Carlo D’Eramo, Marcello Restelli, and Alessandro Nuara. Estimating maximum
expected value through gaussian approximation. In International Conference on
Machine Learning, pp. 1032{1040, 2016.
[19] Markus Enzenberger, Martin Muller, Broderick Arneson, and Richard Segal.
Fuego{anopen-sourceframeworkforboardgamesandgoenginebasedonmonte
carlo tree search. Computational Intelligence and AI in Games, IEEE Transac-
tions on, Vol. 2, No. 4, pp. 259{270, 2010.
[20] Jelle Van Eyck, Jan Ramon, Fabian Guiza, Geert Meyfroidt, Maurice
Bruynooghe, and Greet Van den Berghe. Guided monte carlo tree search for
planning in learned environments. In Asian Conference on Machine Learning,
pp. 33{47, 2013.
[21] Zohar Feldman and Carmel Domshlak. Simple regret optimization in online
planning for markov decision processes. J. Artif. Intell. Res., Vol. 51, pp. 165{
205, 2014.
[22] Hilmar Finnsson and Yngvi Bj(cid:127)ornsson. Game-tree properties and mcts perfor-
mance. In Proceedings of 2nd International General Game Playing Workshop
(GIGA2011), pp. 23{30, 2011.
110
--- PAGE 121 ---
[23] Timothy Furtak and Michael Buro. Minimum proof graphs and fastest-cut-(cid:12)rst
search heuristics. In IJCAI, pp. 492{498, 2009.
[24] RalucaD.Gaina,DiegoP(cid:19)erez-Li(cid:19)ebana,andSimonM.Lucas.Generalvideogame
for 2 players: framework and competition. In Computer Science and Electronic
Engineering (CEEC), 2016 8th, pp. 186{191. IEEE, 2016.
[25] Aur(cid:19)elien Garivier, Emilie Kaufmann, and Wouter M. Koolen. Maximin Action
Identi(cid:12)cation: A New Bandit Framework for Games. In 29th Annual Confer-
ence on Learning Theory (COLT), Vol. 49 of JMLR Workshop and Conference
Proceedings, New-York, United States, June 2016.
[26] Sylvain Gelly, Levente Kocsis, Marc Schoenauer, Mich(cid:18)ele Sebag, David Silver,
Csaba Szepesv(cid:19)ari, and Olivier Teytaud. The grand challenge of computer go:
Monte carlo tree search and extensions. Commun. ACM, Vol. 55, No. 3, pp.
106{113, March 2012.
[27] TobiasGrafandMarcoPlatzner. Adaptiveplayoutsforonlinelearningofpolicies
during monte carlo tree search. Theoretical Computer Science, Vol. 644, pp. 53{
62, 2016.
[28] TobiasGraf,LarsSchaefers,andMarcoPlatzner. Onsemeaidetectioninmonte-
carlo go. In Computers and Games, pp. 14{25. Springer, 2014.
[29] JunichiHashimoto,AkihiroKishimoto,KazukiYoshizoe,andKokoloIkeda. Ac-
celerated uct and its application to two-player games. In Advances in Computer
Games, pp. 1{12. Springer, 2011.
[30] Wassily Hoeffding. Probability inequalities for sums of bounded random vari-
ables. Journal of the American statistical association, Vol. 58, No. 301, pp.
13{30, 1963.
[31] Hiroyuki Iida, Makoto Sakuta, and Jeff Rollason. Computer shogi. Arti(cid:12)cial
Intelligence, Vol. 134, No. 1, pp. 121{144, 2002.
[32] N.IkehataandT.Ito. Monte-carlotreesearchinms.pac-man. InComputational
Intelligence and Games (CIG), 2011 IEEE Conference on, pp. 39{46, Aug 2011.
[33] Takahisa Imagawa and Tomoyuki Kaneko. Enhancements in monte carlo tree
search algorithms for biased game trees. In Computational Intelligence and
Games (CIG), 2015 IEEE Conference on, pp. 43{50. IEEE, 2015.
[34] Takahisa Imagawa and Tomoyuki Kaneko. Monte carlo tree search with robust
exploration. In International Conference on Computers and Games, pp. 34{46.
Springer, 2016.
111
--- PAGE 122 ---
[35] Takahisa Imagawa and Tomoyuki Kaneko. Estimating the maximum expected
value through upper con(cid:12)dence bound of likelihood. In 2017 Conference on
Technologies and Applications of Arti(cid:12)cial Intelligence, to appear.
[36] Emil Juul Jacobsen, Rasmus Greve, and Julian Togelius. Monte mario: plat-
forming with mcts. In Proceedings of the 2014 Annual Conference on Genetic
and Evolutionary Computation, pp. 293{300. ACM, 2014.
[37] Nicolas Jouandeau and Tristan Cazenave. Monte-carlo tree reductions for
stochastic games. In Shin-Ming Cheng and Min-Yuh Day, editors, Technolo-
gies and Applications of Arti(cid:12)cial Intelligence, 19th International Conference,
TAAI 2014, Taipei, Taiwan, November 21-23, 2014. Proceedings, Vol. 8916 of
Lecture Notes in Computer Science, pp. 228{238. Springer, 2014.
[38] Leslie Pack Kaelbling, Michael L. Littman, and Andrew W. Moore. Reinforce-
ment learning: A survey. Journal of arti(cid:12)cial intelligence research, Vol. 4, pp.
237{285, 1996.
[39] Emilie Kaufmann and Wouter Koolen. Monte-carlo tree search by best arm
identi(cid:12)cation. arXiv preprint arXiv:1706.02986, 2017.
[40] EmilieKaufmann,NathanielKorda,andR(cid:19)emiMunos. Thompsonsampling: An
asymptotically optimal (cid:12)nite-time analysis. In NaderH. Bshouty, Gilles Stoltz,
Nicolas Vayatis, and Thomas Zeugmann, editors, Algorithmic Learning Theory,
Vol. 7568 of Lecture Notes in Computer Science, pp. 199{213. Springer Berlin
Heidelberg, 2012.
[41] Michael Kearns, Yishay Mansour, and Andrew Y. Ng. A sparse sampling al-
gorithm for near-optimal planning in large markov decision processes. Machine
learning, Vol. 49, No. 2, pp. 193{208, 2002.
[42] Levente Kocsis and Csaba Szepesv(cid:19)ari. Bandit based monte-carlo planning. In
Machine Learning: ECML 2006, pp. 282{293. Springer, 2006.
[43] R. E. Korf and D. M. Chickering. Best-(cid:12)rst minimax search. Arti(cid:12)cial Intelli-
gence, Vol. 84, pp. 299{337, 1996.
[44] T.L.LaiandHerbertRobbins.Asymptoticallyefficientadaptiveallocationrules.
Advances in Applied Mathematics, Vol. 6, No. 1, pp. 4{22, 1985.
[45] Marc Lanctot, Mark H. M. Winands, Tom Pepels, and Nathan R. Sturtevant.
Monte carlo tree search with heuristic evaluations using implicit minimax back-
ups. In 2014 IEEE Conference on Computational Intelligence and Games, CIG
2014, Dortmund, Germany, August 26-29, 2014, pp. 1{8. IEEE, 2014.
112
--- PAGE 123 ---
[46] Yun-Ching Liu and Y. Tsuruoka. Regulation of exploration for simple regret
minimizationinmonte-carlotreesearch. InComput. Intellig. and Games (CIG),
2015 IEEE Conference on, pp. 35{42, Aug 2015.
[47] Jeffrey Richard Long, Nathan R. Sturtevant, Michael Buro, and Timothy Fur-
tak. Understanding the success of perfect information monte carlo sampling
in game tree search. In Maria Fox and David Poole, editors, Proceedings of the
Twenty-FourthAAAIConferenceonArti(cid:12)cialIntelligence,AAAI2010,Atlanta,
Georgia, USA, July 11-15, 2010. AAAI Press, 2010.
[48] LeandroSorianoMarcolino,AlbertXinJiang,andMilindTambe.Diversitybeats
strength?-towards forming a powerful team. In 15th International Workshop on
Coordination, Organisations, Institutions and Norms (COIN 2013), 2013.
[49] Dana S. Nau. An investigation of the causes of pathology in games. Arti(cid:12)cial
Intelligence, Vol. 19, No. 3, pp. 257{278, 1982.
[50] DanaS.Nau. Pathologyongametreesrevisited,andanalternativetominimax-
ing. Arti(cid:12)cial intelligence, Vol. 21, No. 1-2, pp. 221{244, 1983.
[51] TakuyaObata,TakuyaSugiyama,KunihitoHoki,andTakeshiIto. Consultation
algorithm for computer shogi: Move decisions by majority. In Computers and
Games, pp. 156{165. Springer, 2011.
[52] Judea Pearl. Asymptotic properties of minimax trees and game-searching pro-
cedures. Arti(cid:12)cial Intelligence, Vol. 14, No. 2, pp. 113{138, 1980.
[53] Tom Pepels, Tristan Cazenave, Mark H. M. Winands, and Marc Lanctot. Min-
imizing simple and cumulative regret in monte-carlo tree search. In Computer
Games, pp. 1{15. Springer, 2014.
[54] Tom Pepels, Mandy J. W. Tak, Marc Lanctot, and Mark H. M. Winands.
Quality-based rewards for monte-carlo tree search simulations. In Torsten
Schaub,GerhardFriedrich,andBarryO’Sullivan,editors,ECAI 2014 - 21st Eu-
ropean Conference on Arti(cid:12)cial Intelligence, 18-22 August 2014, Prague, Czech
Republic-IncludingPrestigiousApplicationsofIntelligentSystems(PAIS2014),
Vol.263ofFrontiersin Arti(cid:12)cialIntelligenceand Applications,pp.705{710.IOS
Press, 2014.
[55] Diego Perez-Liebana, Spyridon Samothrakis, Julian Togelius, Tom Schaul, Si-
mon M Lucas, Adrien Cou(cid:127)etoux, Jerry Lee, Chong-U Lim, and Tommy Thomp-
son. The 2014 general video game playing competition. IEEE Transactions on
Computational Intelligence and AI in Games, Vol. 8, No. 3, pp. 229{243, 2016.
113
--- PAGE 124 ---
[56] P. Perick, D.L. St-Pierre, F. Maes, and D. Ernst. Comparison of different selec-
tionstrategiesinmonte-carlotreesearchforthegameoftron. InComputational
Intelligence and Games (CIG), 2012 IEEE Conference on, pp. 242{249, Sept
2012.
[57] Aske Plaat, Jonathan Schaeffer, Wim Pijls, and Arie de Bruin. Best-(cid:12)rst (cid:12)xed
depth minimax algorithms. Arti(cid:12)cial Intelligence, Vol. 87, pp. 255{293, 1996.
[58] Raghuram Ramanujan, Ashish Sabharwal, and Bart Selman. On adversarial
search spaces and sampling-based planning. In ICAPS, Vol. 10, pp. 242{245,
2010.
[59] RaghuramRamanujan,AshishSabharwal,andBartSelman. Onthebehaviorof
uct in synthetic search spaces. In Proc. 21st Int. Conf. Automat. Plan. Sched.,
Freiburg, Germany, 2011.
[60] A.L.Samuel. Somestudiesinmachinelearningusingthegameofcheckers. IBM
Journal of Research and Development, Vol. 3, No. 3, pp. 210{229, 1959.
[61] DavidSilver,AjaHuang,ChrisJMaddison,ArthurGuez,LaurentSifre,George
VanDenDriessche,JulianSchrittwieser,IoannisAntonoglou,VedaPanneershel-
vam, Marc Lanctot, et al. Mastering the game of go with deep neural networks
and tree search. Nature, Vol. 529, No. 7587, pp. 484{489, 2016.
[62] David Silver, Thomas Hubert, Julian Schrittwieser, Ioannis Antonoglou,
Matthew Lai, Arthur Guez, Marc Lanctot, Laurent Sifre, Dharshan Kumaran,
Thore Graepel, et al. Mastering chess and shogi by self-play with a general
reinforcement learning algorithm. arXiv preprint arXiv:1712.01815, 2017.
[63] David Silver, Julian Schrittwieser, Karen Simonyan, Ioannis Antonoglou, Aja
Huang, Arthur Guez, Thomas Hubert, Lucas Baker, Matthew Lai, Adrian
Bolton, et al. Mastering the game of go without human knowledge. Nature,
Vol. 550, No. 7676, pp. 354{359, 2017.
[64] David Silver, Richard S. Sutton, and Martin Mu(cid:127)ller. Sample-based learning
and search with permanent and transient memories. In Proceedings of the 25th
international conference on Machine learning, pp. 968{975. ACM, 2008.
[65] DavidSilverandJoelVeness.Monte-carloplanninginlargepomdps.InAdvances
in Neural Information Processing Systems, pp. 2164{2172, 2010.
[66] StephenJ.J.SmithandDanaS.Nau. Ananalysisofforwardpruning. InAAAI,
pp. 1386{1391, 1994.
114
--- PAGE 125 ---
[67] Richard S. Sutton and Andrew G. Barto. Reinforcement learning: An introduc-
tion, Vol. 1. MIT press Cambridge, 1998.
[68] Gerald Tesauro, V. T. Rajan, and Richard Segal. Bayesian inference in monte-
carlotreesearch. Inthe 26th Conference on Uncertainty in Arti(cid:12)cial Intelligence
(UAI 2010), 2010.
[69] William R. Thompson. On the likelihood that one unknown probability exceeds
another in view of the evidence of two samples. Biometrika, pp. 285{294, 1933.
[70] David Tolpin and Solomon Eyal Shimony. MCTS based on simple regret. In
AAAI, 2012.
[71] Hado van Hasselt. Double q-learning. In Advances in Neural Information Pro-
cessing Systems, pp. 2613{2621, 2010.
[72] Hado van Hasselt. Estimating the maximum expected value: an analysis of
(nested) cross validation and the maximum sample average. arXiv preprint
arXiv:1302.7175, 2013.
[73] Mark H. M. Winands, Yngvi Bjornsson, and Jahn-Takeshi Saito. Monte carlo
treesearchinlinesofaction. ComputationalIntelligenceandAIinGames, IEEE
Transactions on, Vol. 2, No. 4, pp. 239{250, 2010.
[74] Mark H. Winands, Yngvi Bjornsson, and Jahn-Takeshi Saito. Monte-carlo tree
search solver. In Proceedings of the 6th international conference on Computers
and Games, CG ’08, pp. 25{36, Berlin, Heidelberg, 2008. Springer-Verlag.
[75] David H. Wolpert and William G. Macready. No free lunch theorems for op-
timization. IEEE transactions on evolutionary computation, Vol. 1, No. 1, pp.
67{82, 1997.
[76] DavidH.WolpertandWilliamG.Macready. Coevolutionaryfreelunches. IEEE
Transactions on evolutionary computation, Vol. 9, No. 6, pp. 721{735, 2005.
[77] Min Xu, Tao Qin, and Tie-Yan Liu. Estimation bias in multi-armed bandit
algorithms for searchadvertising. In Advances in Neural Information Processing
Systems, pp. 2400{2408, 2013.
[78] DaisakuYokoyamaandMasaruKitsuregawa. Arandomizedgame-treesearchal-
gorithmforshogibasedonbayesianapproach.InPRICAI,pp.937{944.Springer,
2014.
[79] 今川孝久, 金子知適. 難しさが手番で異なる局面でのモンテカルロ木探索の性能の
改善. ゲームプログラミングワークショップ2013論文集, pp. 162{169, nov 2013.
115
--- PAGE 126 ---
[80] 今川孝久, 金子知適. モンテカルロ木探索における子孫の勝敗確定時のプレイアウ
ト結果の修正. ゲームプログラミングワークショップ 2016 論文集, Vol. 2016, pp.
13{20, 2016.
[81] 今川孝久,金子知適. モンテカルロ木探索における状態価値の推定方法の改善. ゲー
ムプログラミングワークショップ 2017 論文集, Vol. 2017, pp. 34{41, 2017.
[82] 伊藤毅志, 佐藤慎太郎. モンテカルロ木探索における確定勝利優先アルゴリズム.
ゲームプログラミングワークショップ2008論文集, Vol. 2008, pp. 140{143, oct
2008.
116