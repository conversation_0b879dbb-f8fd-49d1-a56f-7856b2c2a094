# LIENS ET RESSOURCES EXTERNES - MODÈLES AZR

## 📚 Documentation Officielle

### Papers Académiques
- **Paper Principal ArXiv** : https://arxiv.org/abs/2505.03335
- **Version HTML** : https://arxiv.org/html/2505.03335v2
- **PDF Direct** : https://gptshop.ai/paper/AbsoluteZero.pdf
- **ResearchGate** : https://www.researchgate.net/publication/391493002_Absolute_Zero_Reinforced_Self-play_Reasoning_with_Zero_Data

### Repositories de Code
- **GitHub Officiel** : https://github.com/LeapLabTHU/Absolute-Zero-Reasoner
- **veRL Framework** : https://github.com/volcengine/verl
- **Modèles HuggingFace** : https://huggingface.co/collections/andrewzh/absolute-zero-reasoner-68139b2bca82afb00bc69e5b

### Sites Officiels
- **Page Projet** : https://andrewzhao112.github.io/absolute-zero-reasoner/
- **Logs W&B** : https://wandb.ai/andrewzhao112/AbsoluteZeroReasoner

## 🌐 Ressources Multilingues

### 🇺🇸 Ressources Anglaises

#### Articles Techniques
- **Medium Analysis** : https://medium.com/@lbq999/absolute-zero-self-learning-model-433a6af76437
- **LinkedIn Technical** : https://www.linkedin.com/pulse/training-ai-reasoners-without-human-data-absolute-zero-david-borish-sn0dc
- **Deep Learning Weekly** : https://www.deeplearningweekly.com/p/deep-learning-weekly-issue-404

#### Communautés
- **Reddit r/singularity** : Discussions sur l'IA autonome
- **Reddit r/LocalLLaMA** : Implémentations locales
- **Papers with Code** : Reproductions et benchmarks

### 🇨🇳 Ressources Chinoises

#### Articles Spécialisés
- **HyperAI超神经** : "Absolute Zero：零数据强化自博弈推理"
- **ChatPaper.ai** : Analyse détaillée du paper
- **LinkResearcher** : "绝对零监督Absolute Zero：类AlphaZero自博弈赋能大模型推理"
- **知乎 (Zhihu)** : "人工智能自我进化新范式，无需人类数据也能超越SOTA"

#### Plateformes Techniques
- **掘金 (Juejin)** : Tutoriels d'implémentation
- **CSDN** : Analyses techniques approfondies
- **腾讯云** : Documentation développeur
- **清华&通院** : Annonces officielles

### 🇫🇷 Ressources Françaises

#### Formation et Éducation
- **Udemy** : "Apprentissage par renforcement avec Python"
- **WeCours** : "Intelligence artificielle A-Z™"
- **École Intelligence Artificielle** : Formations spécialisées

#### Recherche Académique
- **HAL Science** : Thèses sur l'apprentissage automatique
- **Université d'Ottawa** : Cours IA et apprentissage par renforcement

### 🇪🇸 Ressources Espagnoles

#### Tutoriels
- **Aprende Machine Learning** : "Aprendizaje por Refuerzo"
- **DataCamp** : "Aprendizaje por Refuerzo en Python"
- **Medium Español** : Articles sur l'apprentissage par renforcement

#### Recherche
- **UPM** : Universidad Politécnica de Madrid - Thèses IA

### 🇩🇪 Ressources Allemandes

#### Articles Techniques
- **AllAboutAI** : "LLMs vs KI-Agenten: Unterschiede und Anwendungsfälle"
- **Anatomie eines KI-Agenten** : Documentation technique

### 🇯🇵 Ressources Japonaises

#### Communautés
- **Techno-Edge** : "検索しないAI検索エンジン「ZeroSearch」"
- **Note.com** : "『絶対零度』AI、研究者に衝撃を与える"
- **5ch (2channel)** : Discussions techniques IA

### 🇧🇷 Ressources Portugaises

#### Analyses
- **Reddit Brasil** : "Apresentamos o Absolute Zero Reasoner"
- **AllAboutAI Brasil** : "Modelo Absolute Zero Reasoner (AZR)"
- **LinkedIn Brasil** : Analyses multiples sur l'IA autonome

## 🛠️ Frameworks et Outils

### Frameworks d'Entraînement
- **veRL** : https://github.com/volcengine/verl
- **TRL (Transformers RL)** : https://github.com/huggingface/trl
- **Ray RLlib** : https://docs.ray.io/en/latest/rllib/

### Outils d'Inférence
- **vLLM** : https://github.com/vllm-project/vllm
- **SGLang** : https://github.com/sgl-project/sglang
- **Text Generation Inference** : https://github.com/huggingface/text-generation-inference

### Environnements d'Exécution
- **Python Executor** : Intégré dans le repository AZR
- **Code Execution Sandbox** : Solutions sécurisées
- **Docker Containers** : Isolation d'environnement

## 📊 Benchmarks et Datasets

### Benchmarks Mathématiques
- **GSM8K** : https://github.com/openai/grade-school-math
- **MATH** : https://github.com/hendrycks/math
- **MathQA** : https://math-qa.github.io/

### Benchmarks Programmation
- **HumanEval** : https://github.com/openai/human-eval
- **MBPP** : https://github.com/google-research/google-research/tree/master/mbpp
- **CodeContests** : https://github.com/deepmind/code_contests

### Datasets de Raisonnement
- **BigBench** : https://github.com/google/BIG-bench
- **HellaSwag** : https://rowanzellers.com/hellaswag/
- **CommonsenseQA** : https://www.tau-nlp.org/commonsenseqa

## 🎓 Ressources Éducatives

### Cours en Ligne
- **CS285 Berkeley** : Deep Reinforcement Learning
- **Stanford CS234** : Reinforcement Learning
- **DeepMind x UCL** : Reinforcement Learning Course

### Livres Recommandés
- **"Reinforcement Learning: An Introduction"** - Sutton & Barto
- **"Deep Reinforcement Learning Hands-On"** - Maxim Lapan
- **"Artificial Intelligence: A Modern Approach"** - Russell & Norvig

### Tutoriels Pratiques
- **OpenAI Spinning Up** : https://spinningup.openai.com/
- **Stable Baselines3** : https://stable-baselines3.readthedocs.io/
- **Ray RLlib Tutorials** : https://docs.ray.io/en/latest/rllib/

## 🏢 Organisations et Laboratoires

### Laboratoires de Recherche
- **LeapLab THU** : Tsinghua University (Créateurs d'AZR)
- **OpenAI** : Recherche en IA générale
- **DeepMind** : Recherche en IA avancée
- **Anthropic** : Sécurité et alignement IA

### Entreprises Impliquées
- **ByteDance** : Volcano Engine (veRL)
- **Hugging Face** : Modèles et outils
- **NVIDIA** : Infrastructure GPU
- **AMD** : Support ROCm

## 📰 Actualités et Veille

### Sources d'Information
- **AI Research** : https://www.airesearch.com/
- **Papers with Code** : https://paperswithcode.com/
- **Towards AI** : https://towardsai.net/
- **The Gradient** : https://thegradient.pub/

### Newsletters Spécialisées
- **The Batch** : DeepLearning.AI
- **AI Research** : Weekly updates
- **Import AI** : Jack Clark's newsletter
- **The Algorithm** : MIT Technology Review

## 🔧 Outils de Développement

### Environnements de Développement
- **Google Colab** : Notebooks gratuits avec GPU
- **Kaggle Kernels** : Environnement de data science
- **Paperspace Gradient** : ML platform
- **AWS SageMaker** : Cloud ML platform

### Monitoring et Tracking
- **Weights & Biases** : https://wandb.ai/
- **MLflow** : https://mlflow.org/
- **TensorBoard** : https://www.tensorflow.org/tensorboard
- **Neptune** : https://neptune.ai/

### Visualisation
- **Matplotlib** : Graphiques Python
- **Plotly** : Visualisations interactives
- **Seaborn** : Visualisations statistiques
- **Streamlit** : Applications web ML

## 🌟 Projets Connexes

### Modèles Similaires
- **AlphaZero** : Self-play pour jeux
- **MuZero** : Model-based RL
- **Agent57** : RL généraliste
- **Gato** : Agent multi-tâches

### Techniques Apparentées
- **Constitutional AI** : Anthropic
- **RLHF** : Reinforcement Learning from Human Feedback
- **Self-Supervised Learning** : Apprentissage auto-supervisé
- **Meta-Learning** : Apprentissage à apprendre

## 📞 Contact et Support

### Support Technique
- **GitHub Issues** : Repository officiel AZR
- **Discord Communities** : Serveurs IA/ML
- **Stack Overflow** : Questions techniques
- **Reddit Communities** : r/MachineLearning, r/artificial

### Conférences et Événements
- **NeurIPS** : Conference on Neural Information Processing Systems
- **ICML** : International Conference on Machine Learning
- **ICLR** : International Conference on Learning Representations
- **AAAI** : Association for the Advancement of Artificial Intelligence

---

**Note :** Cette liste de ressources est maintenue à jour au moment de la création. Les liens peuvent évoluer et de nouvelles ressources peuvent apparaître. Il est recommandé de vérifier régulièrement les sources officielles pour les dernières mises à jour.
