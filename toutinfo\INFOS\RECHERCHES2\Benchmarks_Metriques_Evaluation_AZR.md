# Benchmarks et Métriques d'Évaluation pour Modèles AZR

## Table des Matières
1. [Benchmarks Mathématiques](#benchmarks-mathématiques)
2. [Benchmarks de Programmation](#benchmarks-de-programmation)
3. [Métriques de Performance](#métriques-de-performance)
4. [Protocoles d'Évaluation](#protocoles-dévaluation)
5. [Métriques Spécifiques AZR](#métriques-spécifiques-azr)
6. [Comparaisons Inter-Modèles](#comparaisons-inter-modèles)
7. [Métriques de Qualité](#métriques-de-qualité)
8. [Validation Statistique](#validation-statistique)

## 1. Benchmarks Mathématiques {#benchmarks-mathématiques}

### 1.1 GSM8K (Grade School Math 8K)

**Description :** Dataset de 8,500 problèmes mathématiques de niveau école primaire nécessitant un raisonnement multi-étapes.

**Formule d'Évaluation :**
```
Accuracy_GSM8K = (Nombre de réponses correctes) / (Nombre total de problèmes)
```

**Métriques Détaillées :**
```python
def evaluate_gsm8k(model_responses, ground_truth):
    """
    Évaluation complète sur GSM8K
    """
    correct = 0
    total = len(ground_truth)
    
    for response, truth in zip(model_responses, ground_truth):
        # Extraction de la réponse numérique
        predicted_answer = extract_numerical_answer(response)
        true_answer = extract_numerical_answer(truth)
        
        # Comparaison avec tolérance
        if abs(predicted_answer - true_answer) < 1e-6:
            correct += 1
    
    accuracy = correct / total
    
    return {
        "accuracy": accuracy,
        "correct_count": correct,
        "total_count": total,
        "error_rate": 1 - accuracy
    }
```

**Résultats AZR :**
- **Qwen2.5-7B Base :** 27.5%
- **AZR (Base) :** 38.4% (+10.9 points)
- **AZR (Coder) :** 39.1% (+11.6 points)

### 1.2 MATH Dataset

**Description :** Dataset de problèmes mathématiques de niveau lycée/université couvrant 7 domaines.

**Domaines Couverts :**
- Algebra
- Counting & Probability
- Geometry
- Intermediate Algebra
- Number Theory
- Prealgebra
- Precalculus

**Formule d'Évaluation :**
```
Accuracy_MATH = Σ(Accuracy_domain_i × Weight_domain_i) / Σ(Weight_domain_i)
```

**Métriques par Domaine :**
```python
def evaluate_math_dataset(model_responses, ground_truth, domains):
    """
    Évaluation par domaine sur MATH
    """
    domain_results = {}
    
    for domain in domains:
        domain_responses = filter_by_domain(model_responses, domain)
        domain_truth = filter_by_domain(ground_truth, domain)
        
        domain_accuracy = calculate_accuracy(domain_responses, domain_truth)
        domain_results[domain] = {
            "accuracy": domain_accuracy,
            "count": len(domain_truth)
        }
    
    # Calcul de l'accuracy globale pondérée
    total_weighted_accuracy = sum(
        result["accuracy"] * result["count"] 
        for result in domain_results.values()
    )
    total_count = sum(result["count"] for result in domain_results.values())
    
    overall_accuracy = total_weighted_accuracy / total_count
    
    return {
        "overall_accuracy": overall_accuracy,
        "domain_results": domain_results
    }
```

## 2. Benchmarks de Programmation {#benchmarks-de-programmation}

### 2.1 HumanEval

**Description :** 164 problèmes de programmation Python avec tests unitaires.

**Métriques Pass@k :**
```
Pass@k = E[1 - C(n-c, k) / C(n, k)]
```

**Où :**
- `n` : Nombre total de samples générés
- `c` : Nombre de samples corrects
- `k` : Nombre de samples considérés
- `C(n, k)` : Combinaison de n éléments pris k à k

**Implémentation :**
```python
def calculate_pass_at_k(results, k):
    """
    Calcul de Pass@k pour HumanEval
    """
    total_problems = len(results)
    pass_at_k_scores = []
    
    for problem_results in results:
        n = len(problem_results)  # Nombre de samples
        c = sum(problem_results)  # Nombre de samples corrects
        
        if n < k:
            pass_at_k = c / n if n > 0 else 0
        else:
            # Formule exacte de Pass@k
            pass_at_k = 1 - math.comb(n - c, k) / math.comb(n, k)
        
        pass_at_k_scores.append(pass_at_k)
    
    return sum(pass_at_k_scores) / total_problems
```

**Résultats AZR :**
- **Qwen2.5-7B Base :** 52.0%
- **AZR (Base) :** 55.2% (+3.2 points)
- **AZR (Coder) :** 61.6% (+9.6 points)

### 2.2 MBPP (Mostly Basic Python Problems)

**Description :** 1,000 problèmes Python de niveau débutant à intermédiaire.

**Métriques d'Évaluation :**
```python
def evaluate_mbpp(model_solutions, test_cases):
    """
    Évaluation MBPP avec exécution de tests
    """
    results = []
    
    for solution, tests in zip(model_solutions, test_cases):
        try:
            # Exécution de la solution
            exec_globals = {}
            exec(solution, exec_globals)
            
            # Test de tous les cas
            all_passed = True
            for test_input, expected_output in tests:
                try:
                    actual_output = eval(test_input, exec_globals)
                    if actual_output != expected_output:
                        all_passed = False
                        break
                except:
                    all_passed = False
                    break
            
            results.append(all_passed)
            
        except:
            results.append(False)
    
    return {
        "pass_rate": sum(results) / len(results),
        "passed_count": sum(results),
        "total_count": len(results)
    }
```

## 3. Métriques de Performance {#métriques-de-performance}

### 3.1 Métriques de Base

**Accuracy (Précision) :**
```
Accuracy = (TP + TN) / (TP + TN + FP + FN)
```

**Precision :**
```
Precision = TP / (TP + FP)
```

**Recall (Rappel) :**
```
Recall = TP / (TP + FN)
```

**F1-Score :**
```
F1 = 2 × (Precision × Recall) / (Precision + Recall)
```

### 3.2 Métriques de Confiance

**Calibration Score :**
```python
def calculate_calibration(confidences, accuracies, num_bins=10):
    """
    Calcul du score de calibration
    """
    bin_boundaries = np.linspace(0, 1, num_bins + 1)
    bin_lowers = bin_boundaries[:-1]
    bin_uppers = bin_boundaries[1:]
    
    ece = 0  # Expected Calibration Error
    
    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
        # Sélection des échantillons dans ce bin
        in_bin = (confidences > bin_lower) & (confidences <= bin_upper)
        prop_in_bin = in_bin.mean()
        
        if prop_in_bin > 0:
            accuracy_in_bin = accuracies[in_bin].mean()
            avg_confidence_in_bin = confidences[in_bin].mean()
            
            ece += np.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin
    
    return ece
```

### 3.3 Métriques de Robustesse

**Adversarial Robustness :**
```python
def evaluate_adversarial_robustness(model, test_data, epsilon=0.1):
    """
    Évaluation de la robustesse adversariale
    """
    original_accuracy = evaluate_accuracy(model, test_data)
    
    # Génération d'exemples adversariaux
    adversarial_data = generate_adversarial_examples(
        model, test_data, epsilon
    )
    
    adversarial_accuracy = evaluate_accuracy(model, adversarial_data)
    
    robustness_score = adversarial_accuracy / original_accuracy
    
    return {
        "original_accuracy": original_accuracy,
        "adversarial_accuracy": adversarial_accuracy,
        "robustness_score": robustness_score
    }
```

## 4. Protocoles d'Évaluation {#protocoles-dévaluation}

### 4.1 Protocole Standard AZR

```python
class AZREvaluationProtocol:
    """
    Protocole d'évaluation standardisé pour AZR
    """
    
    def __init__(self, config):
        self.config = config
        self.benchmarks = self._load_benchmarks()
        
    def comprehensive_evaluation(self, model):
        """
        Évaluation complète d'un modèle AZR
        """
        results = {}
        
        # 1. Évaluation sur benchmarks standards
        for benchmark_name, benchmark in self.benchmarks.items():
            benchmark_results = self._evaluate_benchmark(model, benchmark)
            results[benchmark_name] = benchmark_results
        
        # 2. Évaluation des capacités AZR spécifiques
        azr_results = self._evaluate_azr_capabilities(model)
        results["azr_specific"] = azr_results
        
        # 3. Analyse de la progression temporelle
        temporal_results = self._evaluate_temporal_progression(model)
        results["temporal_analysis"] = temporal_results
        
        # 4. Métriques de qualité des tâches générées
        task_quality_results = self._evaluate_task_quality(model)
        results["task_quality"] = task_quality_results
        
        # 5. Calcul des scores agrégés
        aggregated_scores = self._calculate_aggregated_scores(results)
        results["aggregated"] = aggregated_scores
        
        return results
    
    def _evaluate_azr_capabilities(self, model):
        """
        Évaluation des capacités spécifiques à AZR
        """
        return {
            "task_generation_quality": self._evaluate_task_generation(model),
            "self_improvement_rate": self._evaluate_self_improvement(model),
            "diversity_maintenance": self._evaluate_diversity(model),
            "difficulty_progression": self._evaluate_difficulty_progression(model)
        }
```

### 4.2 Validation Croisée

```python
def cross_validation_evaluation(model, dataset, k_folds=5):
    """
    Évaluation par validation croisée k-fold
    """
    fold_size = len(dataset) // k_folds
    fold_results = []
    
    for i in range(k_folds):
        # Division train/test
        test_start = i * fold_size
        test_end = (i + 1) * fold_size
        
        test_data = dataset[test_start:test_end]
        train_data = dataset[:test_start] + dataset[test_end:]
        
        # Entraînement sur le fold
        model_copy = copy.deepcopy(model)
        model_copy.train(train_data)
        
        # Évaluation sur le test
        fold_result = evaluate_model(model_copy, test_data)
        fold_results.append(fold_result)
    
    # Agrégation des résultats
    mean_accuracy = np.mean([r["accuracy"] for r in fold_results])
    std_accuracy = np.std([r["accuracy"] for r in fold_results])
    
    return {
        "mean_accuracy": mean_accuracy,
        "std_accuracy": std_accuracy,
        "confidence_interval": (
            mean_accuracy - 1.96 * std_accuracy / np.sqrt(k_folds),
            mean_accuracy + 1.96 * std_accuracy / np.sqrt(k_folds)
        ),
        "fold_results": fold_results
    }
```

## 5. Métriques Spécifiques AZR {#métriques-spécifiques-azr}

### 5.1 Learnability Score

```python
def calculate_learnability_score(task, model_performance):
    """
    Calcul du score de learnability d'une tâche
    """
    difficulty = calculate_task_difficulty(task)
    diversity = calculate_task_diversity(task)
    solvability = calculate_task_solvability(task, model_performance)
    
    # Formule de learnability
    learnability = (
        0.4 * (1 - abs(difficulty - 0.6)) +  # Difficulté optimale ~0.6
        0.3 * diversity +                     # Diversité élevée
        0.3 * solvability                     # Solvabilité modérée
    )
    
    return max(0, min(1, learnability))
```

### 5.2 Self-Improvement Rate

```python
def calculate_self_improvement_rate(performance_history, window_size=100):
    """
    Calcul du taux d'auto-amélioration
    """
    if len(performance_history) < window_size * 2:
        return 0.0
    
    # Performance récente vs ancienne
    recent_performance = np.mean(performance_history[-window_size:])
    old_performance = np.mean(performance_history[-2*window_size:-window_size])
    
    improvement_rate = (recent_performance - old_performance) / old_performance
    
    return improvement_rate
```

### 5.3 Task Diversity Index

```python
def calculate_task_diversity_index(task_buffer):
    """
    Calcul de l'indice de diversité des tâches
    """
    if len(task_buffer) < 2:
        return 1.0
    
    # Extraction des features de toutes les tâches
    features_matrix = np.array([
        extract_task_features(task) for task in task_buffer
    ])
    
    # Calcul de la matrice de distances
    distance_matrix = pdist(features_matrix, metric='euclidean')
    
    # Diversité = distance moyenne normalisée
    mean_distance = np.mean(distance_matrix)
    max_possible_distance = np.sqrt(features_matrix.shape[1])
    
    diversity_index = mean_distance / max_possible_distance
    
    return diversity_index
```

## 6. Comparaisons Inter-Modèles {#comparaisons-inter-modèles}

### 6.1 Tableau de Comparaison Standard

```python
def generate_comparison_table(models, benchmarks):
    """
    Génération d'un tableau de comparaison standardisé
    """
    results_table = {}
    
    for model_name, model in models.items():
        model_results = {}
        
        for benchmark_name, benchmark in benchmarks.items():
            score = evaluate_model_on_benchmark(model, benchmark)
            model_results[benchmark_name] = score
        
        # Calcul du score moyen
        model_results["average"] = np.mean(list(model_results.values()))
        
        results_table[model_name] = model_results
    
    return results_table
```

### 6.2 Tests de Significativité

```python
def statistical_significance_test(results_a, results_b, alpha=0.05):
    """
    Test de significativité statistique entre deux modèles
    """
    from scipy import stats
    
    # Test t de Student pour échantillons appariés
    t_statistic, p_value = stats.ttest_rel(results_a, results_b)
    
    is_significant = p_value < alpha
    effect_size = (np.mean(results_a) - np.mean(results_b)) / np.std(results_a - results_b)
    
    return {
        "t_statistic": t_statistic,
        "p_value": p_value,
        "is_significant": is_significant,
        "effect_size": effect_size,
        "confidence_interval": stats.t.interval(
            1 - alpha, len(results_a) - 1,
            loc=np.mean(results_a - results_b),
            scale=stats.sem(results_a - results_b)
        )
    }
```

## 7. Métriques de Qualité {#métriques-de-qualité}

### 7.1 Code Quality Metrics

```python
def evaluate_code_quality(generated_code):
    """
    Évaluation de la qualité du code généré
    """
    metrics = {}
    
    # Complexité cyclomatique
    metrics["cyclomatic_complexity"] = calculate_cyclomatic_complexity(generated_code)
    
    # Métriques de Halstead
    halstead_metrics = calculate_halstead_metrics(generated_code)
    metrics.update(halstead_metrics)
    
    # Lisibilité
    metrics["readability_score"] = calculate_readability_score(generated_code)
    
    # Conformité aux standards
    metrics["style_compliance"] = check_style_compliance(generated_code)
    
    # Score de qualité global
    metrics["overall_quality"] = (
        0.3 * normalize_complexity(metrics["cyclomatic_complexity"]) +
        0.2 * normalize_halstead(metrics["difficulty"]) +
        0.3 * metrics["readability_score"] +
        0.2 * metrics["style_compliance"]
    )
    
    return metrics
```

### 7.2 Mathematical Reasoning Quality

```python
def evaluate_mathematical_reasoning_quality(solution_trace):
    """
    Évaluation de la qualité du raisonnement mathématique
    """
    quality_metrics = {}
    
    # Cohérence logique
    quality_metrics["logical_consistency"] = check_logical_consistency(solution_trace)
    
    # Complétude des étapes
    quality_metrics["step_completeness"] = evaluate_step_completeness(solution_trace)
    
    # Clarté de l'explication
    quality_metrics["explanation_clarity"] = evaluate_explanation_clarity(solution_trace)
    
    # Utilisation correcte des concepts
    quality_metrics["concept_usage"] = evaluate_concept_usage(solution_trace)
    
    # Score global de qualité du raisonnement
    quality_metrics["reasoning_quality"] = np.mean([
        quality_metrics["logical_consistency"],
        quality_metrics["step_completeness"],
        quality_metrics["explanation_clarity"],
        quality_metrics["concept_usage"]
    ])
    
    return quality_metrics
```

## 8. Validation Statistique {#validation-statistique}

### 8.1 Intervalles de Confiance

```python
def calculate_confidence_intervals(results, confidence_level=0.95):
    """
    Calcul des intervalles de confiance
    """
    alpha = 1 - confidence_level
    n = len(results)
    mean = np.mean(results)
    std_error = stats.sem(results)
    
    # Intervalle de confiance pour la moyenne
    ci_lower, ci_upper = stats.t.interval(
        confidence_level, n - 1, loc=mean, scale=std_error
    )
    
    return {
        "mean": mean,
        "std_error": std_error,
        "confidence_interval": (ci_lower, ci_upper),
        "margin_of_error": ci_upper - mean
    }
```

### 8.2 Bootstrap Sampling

```python
def bootstrap_evaluation(model, dataset, n_bootstrap=1000):
    """
    Évaluation par bootstrap pour estimation robuste
    """
    bootstrap_scores = []
    
    for _ in range(n_bootstrap):
        # Échantillonnage avec remise
        bootstrap_sample = np.random.choice(
            dataset, size=len(dataset), replace=True
        )
        
        # Évaluation sur l'échantillon bootstrap
        score = evaluate_model(model, bootstrap_sample)
        bootstrap_scores.append(score)
    
    # Statistiques bootstrap
    mean_score = np.mean(bootstrap_scores)
    std_score = np.std(bootstrap_scores)
    
    # Intervalles de confiance percentiles
    ci_lower = np.percentile(bootstrap_scores, 2.5)
    ci_upper = np.percentile(bootstrap_scores, 97.5)
    
    return {
        "bootstrap_mean": mean_score,
        "bootstrap_std": std_score,
        "confidence_interval": (ci_lower, ci_upper),
        "bootstrap_scores": bootstrap_scores
    }
```

Cette documentation complète fournit tous les outils nécessaires pour évaluer rigoureusement les modèles AZR selon les standards académiques et industriels.
