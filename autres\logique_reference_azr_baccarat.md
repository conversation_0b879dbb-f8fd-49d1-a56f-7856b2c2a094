# 🎯 LOGIQUE DE RÉFÉRENCE AZR BACCARAT

## 📊 STRUCTURE DES INDICES

### 🔢 DÉFINITION DES 5 INDICES
- **Index 1** : PAIR/IMPAIR
- **Index 2** : SYNC/DESYNC  
- **Index 3** : Combiné (PAIR_SYNC, PAIR_DESYNC, IMPAIR_SYNC, IMPAIR_DESYNC)
- **Index 4** : P/B/T (séquence complète)
- **Index 5** : S/O (conversions)

## 🔥 PHASE BRÛLAGE

### 📋 ÉTAT INITIAL
```
Brûlage = Manche 0 ET Main 0
Utilisateur renseigne brûlage → Main 1 (cartes distribuées)
Compteur manche reste à 0
```

### 📊 INDICES APRÈS BRÛLAGE
```
Index 1 : ✅ Informations données (PAIR/IMPAIR du brûlage)
Index 2 : ✅ Informations données (SYNC/DESYNC initial)
Index 3 : ✅ Informations données (combinaison 1+2)
Index 4 : ❌ Aucune info (pas de P/B/T pour brûlage)
Index 5 : ❌ Aucune info (pas de S/O pour brûlage)
```

## 🎮 LOGIQUE DES MANCHES

### 🎯 RÈGLE FONDAMENTALE
- **Compteur manche** = Nombre de manches P/B terminées
- **TIE ne termine pas** les manches
- **P/B termine** les manches

### 🔄 TRAITEMENT SELON RÉSULTAT

#### Si TIE :
```
1. Enregistrement : TIE pour manche actuelle
2. Compteur manche : RESTE IDENTIQUE (manche pas terminée)
3. Index 1-3 : ✅ Mis à jour
4. Index 4 : ✅ TIE ajouté
5. Index 5 : ❌ Pas de nouvelle conversion
```

#### Si P ou B :
```
1. Enregistrement : P/B pour manche actuelle
2. Compteur manche : INCRÉMENTE (manche terminée)
3. Index 1-3 : ✅ Mis à jour  
4. Index 4 : ✅ P/B ajouté
5. Index 5 : ✅ Conversion S/O (si ≥2 manches P/B)
```

## 🎯 EXEMPLE COMPLET DE RÉFÉRENCE

```
BRÛLAGE
├─ Main 0, Manche 0
├─ Utilisateur : PAIR
├─ Index 1: PAIR, Index 2: SYNC, Index 3: PAIR_SYNC
├─ Index 4: [], Index 5: []
└─ Compteur manche: 0

MAIN 1 (Manche 1)
├─ Utilisateur : TIE IMPAIR
├─ Index 1: IMPAIR, Index 2: DESYNC, Index 3: IMPAIR_DESYNC
├─ Index 4: [TIE], Index 5: []
└─ Compteur manche: 0 (manche pas terminée)

MAIN 2 (Manche 1 suite)
├─ Utilisateur : PLAYER PAIR
├─ Index 1: PAIR, Index 2: DESYNC, Index 3: PAIR_DESYNC
├─ Index 4: [TIE, PLAYER], Index 5: []
└─ Compteur manche: 0 → 1 (Manche 1 terminée par PLAYER)

MAIN 3 (Manche 2)
├─ Utilisateur : BANKER IMPAIR
├─ Index 1: IMPAIR, Index 2: SYNC, Index 3: IMPAIR_SYNC
├─ Index 4: [TIE, PLAYER, BANKER], Index 5: [O]
└─ Compteur manche: 1 → 2 (Manche 2 terminée, PLAYER→BANKER = O)
```

## 🧠 LOGIQUE DES PRÉDICTIONS

### 📊 MATÉRIAU POUR INDEX 5 (S/O)
```
Après 2 manches P/B enregistrées :
Manche 1 : P/B₁
Manche 2 : P/B₂

Conversion S/O :
- Si P/B₁ = P/B₂ → S (Same)
- Si P/B₁ ≠ P/B₂ → O (Opposite)
```

### 🎯 PRÉDICTIONS
```
Les prédictions prédisent des S/O
Basées sur l'analyse des patterns dans Index 5
```

## 🔢 COMPTEURS ET AFFICHAGE

### 📊 COMPTEUR DE MANCHES
- **Compte seulement** : Les manches terminées par P/B
- **Ignore** : Les TIE (ne terminent pas les manches)
- **Affichage** : Nombre de manches P/B enregistrées + 1

### 📊 COMPTEUR DE MAINS
- **Compte** : Chaque distribution de cartes (P/B/T)
- **Incrémente** : À chaque clic sur un bouton

## ✅ VALIDATION

1. ✅ Compteur manche = Nombre de manches P/B terminées
2. ✅ TIE ne termine pas les manches
3. ✅ Index 1-3 toujours alimentés
4. ✅ Index 4 inclut tout (P/B/T)
5. ✅ Index 5 seulement après 2 manches P/B
6. ✅ Prédictions = S/O basées sur Index 5
