# 🧭 GUIDE DE NAVIGATION ROLLOUTS ET CLUSTERS

## 🎯 **COMMENT NAVIGUER DANS LES SECTIONS ROLLOUTS ET CLUSTERS**

Ce guide vous aide à naviguer efficacement dans l'organisation des paramètres rollouts et clusters d'AZRConfig.

---

## 🔍 **RECHERCHE RAPIDE PAR ROLLOUT**

### **🔍 ROLLOUT 1 (ANALYSEUR) - SECTION N**
➡️ **Ligne ~489** dans AZRConfig  
**Responsabilité** : Analyse des biais structurels et patterns  

#### **🎯 Je veux modifier...**
- **Timing d'analyse** → `rollout1_analysis_time_ms`, `rollout1_index_time_ms`
- **Qualité d'analyse** → `rollout1_min_hands_quality`, `rollout1_quality_threshold`
- **Impacts croisés** → `rollout1_so_start_offset`, `rollout1_impact_strength_threshold`
- **Désynchronisation** → `rollout1_desync_period_min`, `rollout1_desync_period_start_init`
- **États combinés** → `rollout1_combined_pair_sync_influence`, `rollout1_combined_impair_sync_influence`
- **Performance** → `rollout1_low_performance_threshold`, `rollout1_high_performance_threshold`

### **🎲 ROLLOUT 2 (GÉNÉRATEUR) - SECTION O**
➡️ **Ligne ~534** dans AZRConfig  
**Responsabilité** : Génération de séquences candidates optimales  

#### **🎯 Je veux modifier...**
- **Timing génération** → `rollout2_generation_time_ms`, `rollout2_sequence_time_ms`
- **Nombre séquences** → `rollout2_candidates_count`, `rollout2_strategy_count`
- **Probabilités** → `rollout2_max_probability`, `rollout2_alternative_probability`
- **Longueurs** → `rollout2_fixed_length`, `rollout2_sequences_count`
- **Seuils génération** → `rollout2_confidence_threshold`, `rollout2_quality_threshold`
- **Classification signaux** → `rollout2_signal_confidence_high`, `rollout2_confidence_level_excellent`
- **Stratégies fallback** → `rollout2_fallback_strategy_1`, `rollout2_fallback_justification_1`
- **Récompenses** → `rollout2_optimal_difficulty`, `rollout2_excellence_bonus`

### **🎯 ROLLOUT 3 (PRÉDICTEUR) - SECTION P**
➡️ **Ligne ~625** dans AZRConfig  
**Responsabilité** : Sélection finale et prédiction optimale  

#### **🎯 Je veux modifier...**
- **Timing prédiction** → `rollout3_prediction_time_ms`, `rollout3_evaluation_time_ms`
- **Confiance** → `rollout3_default_confidence`, `rollout3_fallback_probability`
- **Critères sélection** → `rollout3_so_priority_weight`, `rollout3_coherence_weight`
- **Seuils évaluation** → `rollout3_excellent_threshold`, `rollout3_good_threshold`
- **Récompenses calibrées** → `rollout3_confidence_bonus_correct`, `rollout3_optimal_risk`

---

## 🏗️ **RECHERCHE RAPIDE PAR CLUSTER**

### **🏗️ CLUSTERS - SECTION Q**
➡️ **Ligne ~676** dans AZRConfig  
**Responsabilité** : Système de clusters AZR (8 clusters × 3 rollouts)  

#### **🎯 Je veux modifier...**
- **Timing clusters** → `cluster_analysis_time_ms`, `cluster_generation_time_ms`
- **Poids rollouts** → `cluster_rollout1_weight`, `cluster_rollout2_weight`, `cluster_rollout3_weight`
- **Spécialisations** → `cluster_pattern_specializations`
- **Confiance calibrée** → `min_calibration_factor`, `calibrated_confidence_weight`

---

## 🔄 **RECHERCHE RAPIDE PARAMÈTRES GÉNÉRAUX**

### **🔄 ROLLOUTS GÉNÉRAUX - SECTION R**
➡️ **Ligne ~708** dans AZRConfig  
**Responsabilité** : Paramètres transversaux à tous les rollouts  

#### **🎯 Je veux modifier...**
- **Configuration générale** → `n_rollouts`, `rollout_temperature`, `rollout_step_size`
- **Optimisations CPU** → `parallel_rollouts`, `thread_pool_size`, `process_pool_size`

---

## 🎯 **RECHERCHE PAR FONCTIONNALITÉ**

### **⏱️ OPTIMISATION DU TIMING**
- **Rollout 1** : `rollout1_analysis_time_ms` (60ms)
- **Rollout 2** : `rollout2_generation_time_ms` (50ms)
- **Rollout 3** : `rollout3_prediction_time_ms` (60ms)
- **Clusters** : `cluster_total_time_ms` (170ms total)

### **🎯 AJUSTEMENT DE LA CONFIANCE**
- **Rollout 2** : `rollout2_signal_confidence_high`, `rollout2_confidence_threshold`
- **Rollout 3** : `rollout3_default_confidence`, `rollout3_min_confidence`, `rollout3_max_confidence`
- **Clusters** : `calibrated_confidence_weight`, `min_calibration_factor`

### **📊 CONFIGURATION DES RÉCOMPENSES**
- **Rollout 2** : `rollout2_excellence_bonus`, `rollout2_optimal_difficulty`
- **Rollout 3** : `rollout3_confidence_bonus_correct`, `rollout3_optimal_risk`
- **Clusters** : `cluster_rollout2_weight`, `cluster_rollout3_weight`

### **🔧 OPTIMISATION CPU**
- **Généraux** : `parallel_rollouts`, `thread_pool_size`, `n_rollouts`
- **Hardware** : `cpu_cores`, `process_pool_size`

---

## 📋 **CONVENTIONS DE NOMMAGE ROLLOUTS/CLUSTERS**

### **🎯 STRUCTURE DES NOMS**
```
[rollout_number]_[category]_[parameter]_[unit]

Exemples :
- rollout1_analysis_time_ms
- rollout2_candidates_count  
- rollout3_excellent_threshold
- cluster_rollout1_weight
```

### **📊 CATÉGORIES PRINCIPALES**
- `_time_ms` : Paramètres de timing en millisecondes
- `_threshold` : Seuils de décision
- `_weight` : Facteurs de pondération
- `_count` : Nombres et quantités
- `_probability` : Probabilités et estimations
- `_confidence` : Paramètres de confiance
- `_bonus` : Facteurs de bonus/malus

---

## 🛠️ **EXEMPLES D'UTILISATION PRATIQUE**

### **Exemple 1 : Optimiser le Rollout 1 (Analyseur)**
```python
# Améliorer la qualité d'analyse
config.rollout1_quality_threshold = 0.8
config.rollout1_min_hands_quality = 60

# Ajuster les influences des états combinés
config.rollout1_combined_pair_sync_influence = 0.15
```

### **Exemple 2 : Configurer le Rollout 2 (Générateur)**
```python
# Augmenter le nombre de séquences candidates
config.rollout2_candidates_count = 6
config.rollout2_strategy_count = 6

# Ajuster les probabilités stratégiques
config.rollout2_max_probability = 0.9
config.rollout2_alternative_probability = 0.75
```

### **Exemple 3 : Optimiser le Rollout 3 (Prédicteur)**
```python
# Ajuster les critères de sélection
config.rollout3_so_priority_weight = 0.8
config.rollout3_quality_weight = 0.7

# Modifier les seuils d'évaluation
config.rollout3_excellent_threshold = 0.85
config.rollout3_good_threshold = 0.65
```

### **Exemple 4 : Configurer les Clusters**
```python
# Ajuster les poids des rollouts dans les clusters
config.cluster_rollout2_weight = 0.5
config.cluster_rollout3_weight = 0.5

# Modifier le timing des phases
config.cluster_analysis_time_ms = 70
config.cluster_generation_time_ms = 40
```

### **Exemple 5 : Optimiser les Rollouts Généraux**
```python
# Augmenter le nombre de rollouts
config.n_rollouts = 24  # 3 par cœur au lieu de 2

# Ajuster la température d'exploration
config.rollout_temperature = 0.8
config.rollout_step_size = 0.12
```

---

## 🚀 **NAVIGATION RAPIDE PAR LIGNE**

### **🔗 LIENS DIRECTS VERS LES SECTIONS**
- **🔍 Rollout 1 (Analyseur)** → Ligne ~489
- **🎲 Rollout 2 (Générateur)** → Ligne ~534
- **🎯 Rollout 3 (Prédicteur)** → Ligne ~625
- **🏗️ Clusters** → Ligne ~676
- **🔄 Rollouts Généraux** → Ligne ~708

### **📊 PROPRIÉTÉS DYNAMIQUES**
- **rollout2_rewards** → Ligne ~1658 (propriété calculée)
- **rollout3_rewards** → Ligne ~1686 (propriété calculée)
- **cluster_reward_weights** → Ligne ~1710 (propriété calculée)

---

## 🏆 **RÉSUMÉ**

La nouvelle organisation des rollouts et clusters vous permet de :

✅ **Naviguer rapidement** vers le bon rollout ou cluster  
✅ **Modifier facilement** les paramètres spécialisés  
✅ **Comprendre clairement** les responsabilités de chaque composant  
✅ **Maintenir efficacement** l'architecture AZR  

🎯 **Utilisez ce guide** pour exploiter pleinement la puissance de l'organisation rollouts et clusters d'AZRConfig !
