#!/usr/bin/env python3
"""
Script d'extraction de texte depuis des fichiers PDF
Extrait le contenu textuel de tous les PDF d'un dossier et sauvegarde en fichiers .txt
"""

import os
import sys
from pathlib import Path
import logging
from typing import List, Optional
from datetime import datetime

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_extraction.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def install_dependencies():
    """Installe les dépendances nécessaires"""
    try:
        import PyPDF2
        import pdfplumber
        logger.info("Dépendances déjà installées")
        return True
    except ImportError:
        logger.info("Installation des dépendances...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "PyPDF2", "pdfplumber"])
            logger.info("Dépendances installées avec succès")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de l'installation des dépendances: {e}")
            return False

def extract_text_pypdf2(pdf_path: Path) -> Optional[str]:
    """Extrait le texte avec PyPDF2"""
    try:
        import PyPDF2
        text = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\n--- PAGE {page_num + 1} ---\n"
                        text += page_text
                except Exception as e:
                    logger.warning(f"Erreur page {page_num + 1} de {pdf_path.name}: {e}")
        return text if text.strip() else None
    except Exception as e:
        logger.error(f"Erreur PyPDF2 pour {pdf_path.name}: {e}")
        return None

def extract_text_pdfplumber(pdf_path: Path) -> Optional[str]:
    """Extrait le texte avec pdfplumber (plus robuste)"""
    try:
        import pdfplumber
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\n--- PAGE {page_num + 1} ---\n"
                        text += page_text
                except Exception as e:
                    logger.warning(f"Erreur page {page_num + 1} de {pdf_path.name}: {e}")
        return text if text.strip() else None
    except Exception as e:
        logger.error(f"Erreur pdfplumber pour {pdf_path.name}: {e}")
        return None

def extract_pdf_text(pdf_path: Path) -> Optional[str]:
    """Extrait le texte d'un PDF en utilisant plusieurs méthodes"""
    logger.info(f"Extraction de {pdf_path.name}...")
    
    # Essai avec pdfplumber d'abord (plus robuste)
    text = extract_text_pdfplumber(pdf_path)
    if text:
        logger.info(f"✓ Extraction réussie avec pdfplumber: {pdf_path.name}")
        return text
    
    # Fallback vers PyPDF2
    text = extract_text_pypdf2(pdf_path)
    if text:
        logger.info(f"✓ Extraction réussie avec PyPDF2: {pdf_path.name}")
        return text
    
    logger.error(f"✗ Échec d'extraction: {pdf_path.name}")
    return None

def save_extracted_text(text: str, output_path: Path, pdf_name: str):
    """Sauvegarde le texte extrait"""
    try:
        # Ajout d'informations d'en-tête
        header = f"""# EXTRACTION TEXTUELLE - {pdf_name}
# Généré automatiquement le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Source: {pdf_name}
# ================================================================

"""
        
        full_text = header + text
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(full_text)
        
        logger.info(f"✓ Texte sauvegardé: {output_path.name}")
        return True
    except Exception as e:
        logger.error(f"Erreur sauvegarde {output_path.name}: {e}")
        return False

def process_all_pdfs(pdf_dir: Path, output_dir: Path) -> dict:
    """Traite tous les PDFs d'un dossier"""
    if not pdf_dir.exists():
        logger.error(f"Dossier PDF introuvable: {pdf_dir}")
        return {"success": 0, "failed": 0, "files": []}
    
    # Création du dossier de sortie
    output_dir.mkdir(exist_ok=True)
    
    # Recherche des fichiers PDF
    pdf_files = list(pdf_dir.glob("*.pdf"))
    if not pdf_files:
        logger.warning(f"Aucun fichier PDF trouvé dans {pdf_dir}")
        return {"success": 0, "failed": 0, "files": []}
    
    logger.info(f"Trouvé {len(pdf_files)} fichiers PDF à traiter")
    
    results = {"success": 0, "failed": 0, "files": []}
    
    for pdf_path in pdf_files:
        try:
            # Extraction du texte
            text = extract_pdf_text(pdf_path)
            
            if text:
                # Nom du fichier de sortie
                output_name = pdf_path.stem + "_extracted.txt"
                output_path = output_dir / output_name
                
                # Sauvegarde
                if save_extracted_text(text, output_path, pdf_path.name):
                    results["success"] += 1
                    results["files"].append({
                        "pdf": pdf_path.name,
                        "txt": output_name,
                        "status": "success",
                        "size": len(text)
                    })
                else:
                    results["failed"] += 1
                    results["files"].append({
                        "pdf": pdf_path.name,
                        "txt": None,
                        "status": "save_failed",
                        "size": 0
                    })
            else:
                results["failed"] += 1
                results["files"].append({
                    "pdf": pdf_path.name,
                    "txt": None,
                    "status": "extraction_failed",
                    "size": 0
                })
                
        except Exception as e:
            logger.error(f"Erreur générale pour {pdf_path.name}: {e}")
            results["failed"] += 1
            results["files"].append({
                "pdf": pdf_path.name,
                "txt": None,
                "status": "error",
                "size": 0
            })
    
    return results

def generate_report(results: dict, output_dir: Path):
    """Génère un rapport de traitement"""
    
    report_path = output_dir / "extraction_report.md"
    
    total_files = results['success'] + results['failed']
    success_rate = (results['success'] / total_files * 100) if total_files > 0 else 0
    
    report_content = f"""# RAPPORT D'EXTRACTION PDF
Généré le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Résumé
- **Fichiers traités**: {total_files}
- **Extractions réussies**: {results['success']}
- **Échecs**: {results['failed']}
- **Taux de réussite**: {success_rate:.1f}%

## Détails par fichier

"""
    
    for file_info in results["files"]:
        status_emoji = "✅" if file_info["status"] == "success" else "❌"
        size_info = f" ({file_info['size']:,} caractères)" if file_info["size"] > 0 else ""
        
        report_content += f"### {status_emoji} {file_info['pdf']}\n"
        report_content += f"- **Statut**: {file_info['status']}\n"
        if file_info["txt"]:
            report_content += f"- **Fichier généré**: {file_info['txt']}{size_info}\n"
        report_content += "\n"
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        logger.info(f"✓ Rapport généré: {report_path}")
    except Exception as e:
        logger.error(f"Erreur génération rapport: {e}")

def main():
    """Fonction principale"""
    
    logger.info("=== DÉMARRAGE EXTRACTION PDF ===")
    
    # Installation des dépendances
    if not install_dependencies():
        logger.error("Impossible d'installer les dépendances")
        return 1
    
    # Chemins
    current_dir = Path.cwd()
    pdf_dir = current_dir / "PDF"
    output_dir = pdf_dir / "Extraction"
    
    logger.info(f"Dossier PDF: {pdf_dir}")
    logger.info(f"Dossier sortie: {output_dir}")
    
    # Traitement
    results = process_all_pdfs(pdf_dir, output_dir)
    
    # Rapport
    generate_report(results, output_dir)
    
    # Résumé final
    logger.info("=== RÉSUMÉ FINAL ===")
    logger.info(f"✅ Réussites: {results['success']}")
    logger.info(f"❌ Échecs: {results['failed']}")
    logger.info(f"📁 Fichiers générés dans: {output_dir}")
    
    return 0 if results['failed'] == 0 else 1

if __name__ == "__main__":
    exit(main())
