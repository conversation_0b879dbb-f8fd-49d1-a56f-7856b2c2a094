# 📡 MODULE 3.5 : COMMUNICATION INTER-CLUSTERS

## 🎯 **OBJECTIFS D'APPRENTISSAGE**

À la fin de cette section, vous maîtriserez :
- ✅ Les protocoles de communication entre clusters AZR
- ✅ L'implémentation de consensus distribué intelligent
- ✅ La synchronisation et coordination des clusters
- ✅ Les mécanismes de tolérance aux pannes

---

## 🌐 **ARCHITECTURE DE COMMUNICATION**

### **📊 Modèle de Communication Hybride**

L'architecture AZR utilise un modèle hybride optimisé :

```
🏗️ MODÈLE HYBRIDE AZR
┌─────────────────────────────────────────────────┐
│                COORDINATEUR GLOBAL              │
│           (Consensus et Synchronisation)        │
└─────────────────┬───────────────────────────────┘
                  │
    ┌─────────────┼─────────────────┐
    │             │                 │
┌───▼───┐    ┌───▼───┐         ┌───▼───┐
│CLUSTER│◄──►│CLUSTER│◄── ... ─►│CLUSTER│
│   1   │    │   2   │         │   8   │
└───────┘    └───────┘         └───────┘
     ▲            ▲                 ▲
     │            │                 │
     ▼            ▼                 ▼
[Shared Memory Locale + Message Passing Global]
```

### **🔄 Patterns de Communication**

#### **1. Shared Memory Locale**
- **Usage** : Communication intra-cluster
- **Avantage** : Latence ultra-faible (<1ms)
- **Limitation** : Portée locale uniquement

#### **2. Message Passing Global**
- **Usage** : Communication inter-clusters
- **Avantage** : Flexibilité et robustesse
- **Latence** : Acceptable (5-10ms)

#### **3. Consensus Distribué**
- **Usage** : Agrégation des résultats
- **Algorithme** : Vote pondéré par confiance
- **Tolérance** : Jusqu'à 3 clusters défaillants

---

## 📡 **PROTOCOLES DE COMMUNICATION**

### **🎯 Protocole de Base**

```python
import threading
import queue
import time
import json
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

class MessageType(Enum):
    """Types de messages inter-clusters"""
    TASK_ASSIGNMENT = "task_assignment"
    RESULT_SUBMISSION = "result_submission"
    CONSENSUS_REQUEST = "consensus_request"
    CONSENSUS_RESPONSE = "consensus_response"
    HEARTBEAT = "heartbeat"
    ERROR_REPORT = "error_report"
    COORDINATION_SYNC = "coordination_sync"

@dataclass
class ClusterMessage:
    """Message standardisé entre clusters"""
    message_type: MessageType
    source_cluster: int
    target_cluster: Optional[int]  # None pour broadcast
    payload: Dict[str, Any]
    timestamp: float
    message_id: str
    priority: int = 1  # 1=normal, 2=high, 3=urgent

class InterClusterCommunication:
    """
    Système de communication inter-clusters pour AZR
    
    Basé sur l'analyse de l'implémentation azr_baccarat_predictor.py
    et les optimisations identifiées pour 8 clusters
    """
    
    def __init__(self, num_clusters=8):
        self.num_clusters = num_clusters
        
        # Queues de communication par cluster
        self.message_queues = {
            cluster_id: queue.PriorityQueue(maxsize=100)
            for cluster_id in range(num_clusters)
        }
        
        # Queue globale pour broadcast
        self.broadcast_queue = queue.Queue(maxsize=200)
        
        # État des clusters
        self.cluster_states = {
            cluster_id: {
                'status': 'idle',
                'last_heartbeat': time.time(),
                'message_count': 0,
                'error_count': 0
            }
            for cluster_id in range(num_clusters)
        }
        
        # Coordinateur global
        self.global_coordinator = GlobalCoordinator(self)
        
        # Métriques de communication
        self.comm_metrics = {
            'messages_sent': 0,
            'messages_received': 0,
            'broadcast_count': 0,
            'consensus_rounds': 0,
            'average_latency': 0.0
        }
        
        # Thread de monitoring
        self.monitoring_thread = threading.Thread(
            target=self._monitor_communication,
            daemon=True
        )
        self.monitoring_thread.start()
    
    def send_message(self, message: ClusterMessage):
        """
        Envoie un message à un cluster spécifique ou en broadcast
        """
        message.timestamp = time.time()
        message.message_id = self._generate_message_id()
        
        try:
            if message.target_cluster is None:
                # Broadcast à tous les clusters
                self._broadcast_message(message)
            else:
                # Message ciblé
                self._send_targeted_message(message)
            
            self.comm_metrics['messages_sent'] += 1
            
        except Exception as e:
            self._handle_communication_error(message, e)
    
    def _send_targeted_message(self, message: ClusterMessage):
        """
        Envoie un message à un cluster spécifique
        """
        target_queue = self.message_queues[message.target_cluster]
        
        # Priorité pour l'ordre dans la queue
        priority_item = (message.priority, time.time(), message)
        
        try:
            target_queue.put(priority_item, timeout=0.1)
        except queue.Full:
            # Queue pleine - message urgent seulement
            if message.priority >= 3:
                # Vider un message de faible priorité
                try:
                    target_queue.get_nowait()
                    target_queue.put(priority_item, timeout=0.1)
                except queue.Empty:
                    pass
    
    def _broadcast_message(self, message: ClusterMessage):
        """
        Diffuse un message à tous les clusters
        """
        for cluster_id in range(self.num_clusters):
            if cluster_id != message.source_cluster:
                targeted_message = ClusterMessage(
                    message_type=message.message_type,
                    source_cluster=message.source_cluster,
                    target_cluster=cluster_id,
                    payload=message.payload.copy(),
                    timestamp=message.timestamp,
                    message_id=message.message_id,
                    priority=message.priority
                )
                self._send_targeted_message(targeted_message)
        
        self.comm_metrics['broadcast_count'] += 1
    
    def receive_messages(self, cluster_id: int, timeout: float = 0.1) -> List[ClusterMessage]:
        """
        Reçoit les messages pour un cluster donné
        """
        messages = []
        cluster_queue = self.message_queues[cluster_id]
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                priority, timestamp, message = cluster_queue.get_nowait()
                messages.append(message)
                self.comm_metrics['messages_received'] += 1
                
                # Mise à jour métriques latence
                latency = time.time() - message.timestamp
                self._update_latency_metrics(latency)
                
            except queue.Empty:
                break
        
        return messages
```

---

## 🎯 **CONSENSUS DISTRIBUÉ INTELLIGENT**

### **⚖️ Algorithme de Consensus AZR**

```python
class DistributedConsensus:
    """
    Algorithme de consensus distribué spécialisé pour AZR
    """
    
    def __init__(self, communication_system):
        self.comm = communication_system
        self.consensus_history = deque(maxlen=100)
        
    def achieve_consensus(self, cluster_results: List[Dict], timeout: float = 0.15):
        """
        Atteint un consensus intelligent sur les résultats des clusters
        
        Args:
            cluster_results: Résultats de chaque cluster
            timeout: Timeout pour le consensus
            
        Returns:
            Résultat de consensus avec métriques
        """
        consensus_start = time.time()
        
        # 1. VALIDATION DES RÉSULTATS
        valid_results = self._validate_cluster_results(cluster_results)
        
        if not valid_results:
            return self._fallback_consensus()
        
        # 2. CONSENSUS PAR VOTE PONDÉRÉ
        weighted_consensus = self._weighted_voting_consensus(valid_results)
        
        # 3. VALIDATION CROISÉE
        cross_validated = self._cross_validate_consensus(
            weighted_consensus, 
            valid_results
        )
        
        # 4. MÉTRIQUES DE QUALITÉ
        consensus_quality = self._evaluate_consensus_quality(
            cross_validated,
            valid_results
        )
        
        # 5. ENREGISTREMENT HISTORIQUE
        consensus_record = {
            'result': cross_validated,
            'quality': consensus_quality,
            'participating_clusters': len(valid_results),
            'consensus_time': time.time() - consensus_start,
            'timestamp': time.time()
        }
        
        self.consensus_history.append(consensus_record)
        
        return consensus_record
    
    def _weighted_voting_consensus(self, valid_results):
        """
        Consensus par vote pondéré selon la confiance
        """
        # Accumulation des votes pondérés
        vote_weights = {}
        confidence_weights = {}
        
        for result in valid_results:
            prediction = result['prediction']
            confidence = result['confidence']
            cluster_reliability = self._get_cluster_reliability(result['cluster_id'])
            
            # Poids composite : confiance × fiabilité cluster
            composite_weight = confidence * cluster_reliability
            
            if prediction not in vote_weights:
                vote_weights[prediction] = 0.0
                confidence_weights[prediction] = 0.0
            
            vote_weights[prediction] += composite_weight
            confidence_weights[prediction] += composite_weight
        
        # Sélection du consensus
        if vote_weights:
            consensus_prediction = max(vote_weights.items(), key=lambda x: x[1])[0]
            total_weight = sum(vote_weights.values())
            consensus_confidence = confidence_weights[consensus_prediction] / total_weight
        else:
            consensus_prediction = 'B'  # Fallback
            consensus_confidence = 0.33
        
        return {
            'prediction': consensus_prediction,
            'confidence': min(consensus_confidence, 1.0),
            'vote_distribution': vote_weights,
            'consensus_method': 'weighted_voting'
        }
    
    def _cross_validate_consensus(self, consensus, valid_results):
        """
        Validation croisée du consensus
        """
        # Calcul de l'accord inter-clusters
        agreements = []
        
        for result in valid_results:
            if result['prediction'] == consensus['prediction']:
                agreements.append(result['confidence'])
        
        # Métriques de validation croisée
        if agreements:
            cross_validation_score = np.mean(agreements)
            agreement_rate = len(agreements) / len(valid_results)
        else:
            cross_validation_score = 0.0
            agreement_rate = 0.0
        
        # Ajustement de confiance basé sur validation croisée
        adjusted_confidence = (
            consensus['confidence'] * 0.7 + 
            cross_validation_score * 0.3
        )
        
        validated_consensus = consensus.copy()
        validated_consensus.update({
            'confidence': adjusted_confidence,
            'cross_validation_score': cross_validation_score,
            'agreement_rate': agreement_rate,
            'validation_method': 'cross_cluster'
        })
        
        return validated_consensus
    
    def _get_cluster_reliability(self, cluster_id):
        """
        Calcule la fiabilité d'un cluster basée sur son historique
        """
        cluster_state = self.comm.cluster_states[cluster_id]
        
        # Facteurs de fiabilité
        factors = {}
        
        # 1. Taux d'erreur récent
        error_rate = cluster_state['error_count'] / max(1, cluster_state['message_count'])
        factors['error_reliability'] = 1.0 - min(error_rate, 1.0)
        
        # 2. Fraîcheur du heartbeat
        time_since_heartbeat = time.time() - cluster_state['last_heartbeat']
        factors['heartbeat_reliability'] = max(0.0, 1.0 - time_since_heartbeat / 10.0)
        
        # 3. Historique de consensus
        recent_consensus = list(self.consensus_history)[-10:]
        cluster_agreements = [
            1.0 for record in recent_consensus
            if cluster_id in [r.get('cluster_id') for r in record.get('participating_results', [])]
        ]
        
        if cluster_agreements:
            factors['consensus_reliability'] = np.mean(cluster_agreements)
        else:
            factors['consensus_reliability'] = 0.8  # Valeur par défaut
        
        # Score de fiabilité composite
        reliability_score = np.mean(list(factors.values()))
        
        return max(0.1, min(1.0, reliability_score))  # Borné entre 0.1 et 1.0
```

---

## 🔄 **SYNCHRONISATION ET COORDINATION**

### **⏰ Coordinateur Global**

```python
class GlobalCoordinator:
    """
    Coordinateur global pour synchronisation des clusters
    """
    
    def __init__(self, communication_system):
        self.comm = communication_system
        self.coordination_state = {
            'current_phase': 'idle',
            'active_clusters': set(),
            'synchronization_barriers': {},
            'global_clock': 0
        }
        
        # Thread de coordination
        self.coordination_thread = threading.Thread(
            target=self._coordination_loop,
            daemon=True
        )
        self.coordination_thread.start()
    
    def coordinate_execution(self, task_data):
        """
        Coordonne l'exécution distribuée d'une tâche
        """
        coordination_id = self._generate_coordination_id()
        
        # 1. PHASE DE PRÉPARATION
        self._broadcast_preparation_phase(coordination_id, task_data)
        
        # 2. SYNCHRONISATION DES CLUSTERS
        ready_clusters = self._wait_for_cluster_readiness(coordination_id)
        
        # 3. LANCEMENT COORDONNÉ
        self._broadcast_execution_start(coordination_id, ready_clusters)
        
        # 4. COLLECTE DES RÉSULTATS
        results = self._collect_cluster_results(coordination_id, ready_clusters)
        
        # 5. CONSENSUS ET FINALISATION
        final_result = self._finalize_coordination(coordination_id, results)
        
        return final_result
    
    def _wait_for_cluster_readiness(self, coordination_id, timeout=0.1):
        """
        Attend que les clusters soient prêts pour l'exécution
        """
        ready_clusters = set()
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Vérification des messages de préparation
            for cluster_id in range(self.comm.num_clusters):
                messages = self.comm.receive_messages(cluster_id, timeout=0.01)
                
                for message in messages:
                    if (message.message_type == MessageType.COORDINATION_SYNC and
                        message.payload.get('coordination_id') == coordination_id and
                        message.payload.get('status') == 'ready'):
                        
                        ready_clusters.add(message.source_cluster)
            
            # Arrêt si tous les clusters sont prêts
            if len(ready_clusters) >= self.comm.num_clusters * 0.75:  # 75% minimum
                break
        
        return ready_clusters
    
    def _coordination_loop(self):
        """
        Boucle principale de coordination
        """
        while True:
            try:
                # Monitoring des heartbeats
                self._check_cluster_heartbeats()
                
                # Nettoyage des anciennes coordinations
                self._cleanup_old_coordinations()
                
                # Gestion des erreurs de clusters
                self._handle_cluster_failures()
                
                time.sleep(0.05)  # 50ms de cycle
                
            except Exception as e:
                # Log de l'erreur et continuation
                print(f"Erreur dans coordination_loop: {e}")
                time.sleep(0.1)
```

---

## 🛡️ **TOLÉRANCE AUX PANNES**

### **🔧 Mécanismes de Récupération**

```python
class FaultTolerance:
    """
    Mécanismes de tolérance aux pannes pour clusters AZR
    """
    
    def __init__(self, communication_system):
        self.comm = communication_system
        self.failure_detector = FailureDetector(communication_system)
        self.recovery_strategies = {
            'cluster_timeout': self._handle_cluster_timeout,
            'cluster_error': self._handle_cluster_error,
            'communication_failure': self._handle_communication_failure,
            'consensus_failure': self._handle_consensus_failure
        }
    
    def handle_failure(self, failure_type, failure_data):
        """
        Gère une panne selon son type
        """
        if failure_type in self.recovery_strategies:
            return self.recovery_strategies[failure_type](failure_data)
        else:
            return self._handle_unknown_failure(failure_type, failure_data)
    
    def _handle_cluster_timeout(self, failure_data):
        """
        Gère le timeout d'un cluster
        """
        cluster_id = failure_data['cluster_id']
        
        # Marquer le cluster comme défaillant temporairement
        self.comm.cluster_states[cluster_id]['status'] = 'timeout'
        
        # Redistribuer la charge si nécessaire
        if self._count_active_clusters() < self.comm.num_clusters * 0.5:
            # Moins de 50% des clusters actifs - mode dégradé
            return self._activate_degraded_mode()
        
        # Tentative de récupération
        return self._attempt_cluster_recovery(cluster_id)
    
    def _activate_degraded_mode(self):
        """
        Active le mode dégradé avec clusters réduits
        """
        active_clusters = [
            cluster_id for cluster_id in range(self.comm.num_clusters)
            if self.comm.cluster_states[cluster_id]['status'] not in ['timeout', 'error']
        ]
        
        if len(active_clusters) >= 3:  # Minimum pour consensus
            return {
                'mode': 'degraded',
                'active_clusters': active_clusters,
                'expected_performance': 0.7  # Performance réduite
            }
        else:
            return {
                'mode': 'emergency',
                'active_clusters': active_clusters,
                'expected_performance': 0.4  # Performance très réduite
            }
```

---

## 📊 **MÉTRIQUES DE COMMUNICATION**

### **📈 Monitoring Avancé**

```python
class CommunicationMetrics:
    """
    Métriques avancées pour communication inter-clusters
    """
    
    def __init__(self):
        self.metrics = {
            'latency_distribution': deque(maxlen=1000),
            'throughput_history': deque(maxlen=100),
            'error_rates': deque(maxlen=100),
            'consensus_quality': deque(maxlen=100)
        }
    
    def generate_communication_report(self):
        """
        Génère un rapport détaillé de communication
        """
        return {
            'latency_stats': self._calculate_latency_stats(),
            'throughput_stats': self._calculate_throughput_stats(),
            'reliability_stats': self._calculate_reliability_stats(),
            'consensus_stats': self._calculate_consensus_stats(),
            'recommendations': self._generate_optimization_recommendations()
        }
    
    def _generate_optimization_recommendations(self):
        """
        Génère des recommandations d'optimisation
        """
        recommendations = []
        
        # Analyse de latence
        if self.metrics['latency_distribution']:
            avg_latency = np.mean(list(self.metrics['latency_distribution']))
            if avg_latency > 0.02:  # 20ms
                recommendations.append({
                    'type': 'latency_optimization',
                    'description': f'Latence élevée détectée: {avg_latency*1000:.1f}ms',
                    'suggestion': 'Optimiser la sérialisation des messages ou réduire la charge'
                })
        
        return recommendations
```

---

## 📝 **POINTS CLÉS À RETENIR**

1. **📡 Communication Hybride** : Shared memory + Message passing optimisé
2. **🎯 Consensus Intelligent** : Vote pondéré par confiance et fiabilité
3. **⏰ Synchronisation** : Coordination globale avec barrières
4. **🛡️ Tolérance aux Pannes** : Récupération automatique et mode dégradé
5. **📊 Monitoring** : Métriques complètes pour optimisation

---

## 🎯 **EXERCICE PRATIQUE**

**Implémentation :** Créez un système de communication simple :

```python
class SimpleCommunication:
    def __init__(self, num_clusters=4):
        self.num_clusters = num_clusters
        self.message_queues = {}
        
    def send_message(self, source, target, message):
        # TODO: Envoyer un message
        pass
        
    def receive_messages(self, cluster_id):
        # TODO: Recevoir les messages
        pass
        
    def broadcast(self, source, message):
        # TODO: Diffuser à tous les clusters
        pass
```

---

**🎉 FÉLICITATIONS ! Vous avez terminé le module Rollouts et Clusters !**

**➡️ Prochaine section : [Module 4 - Mathématiques](../04_MATHEMATIQUES/01_Formulation_Complete.md)**
