# veRL Framework - Documentation Technique Complète

**Source:** https://rocm.blogs.amd.com/artificial-intelligence/verl-large-scale/README.html  
**Date:** 24 avril 2025  
**Auteurs:** <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>

## Vue d'Ensemble

**veRL (Volcano Engine Reinforcement Learning for LLMs)** est un framework open-source pour l'entraînement RLHF (Reinforcement Learning from Human Feedback) à grande échelle, optimisé pour les GPU AMD Instinct avec support ROCm complet.

## Points Clés

### 1. Avantages du Framework veRL
- **Scalabilité:** Support multi-nœuds avec orchestration Ray
- **Performance:** Intégration FSDP, Megatron, vLLM, SGLang
- **Efficacité:** Allocation dynamique des ressources
- **Compatibilité:** Support AMD ROCm et NVIDIA CUDA

### 2. Support AMD ROCm
- **Version supportée:** v0.3.0.post0
- **Optimisations:** Modifications spécifiques pour GPU AMD
- **Docker:** Images pré-configurées disponibles
- **Performance:** Résultats comparatifs MI300X vs H100

## Architecture Technique

### Composants Principaux

#### 1. Moteurs d'Entraînement
- **FSDP (Fully Sharded Data Parallel):** Parallélisation des données
- **Megatron:** Parallélisation des modèles
- **Optimisation mémoire:** Gradient checkpointing, parameter offloading

#### 2. Moteurs d'Inférence
- **vLLM:** Inférence haute performance
- **SGLang:** Génération structurée
- **Parallélisation tensorielle:** Support multi-GPU

#### 3. Orchestration
- **Ray:** Coordination training/inference
- **Allocation dynamique:** Optimisation des ressources
- **Overlap:** Parallélisation des phases

### Algorithmes RLHF Supportés

1. **PPO (Proximal Policy Optimization)**
2. **GRPO (Group Relative Policy Optimization)**
3. **ReMax**
4. **REINFORCE++**
5. **RLOO**
6. **PRIME**

## Installation et Configuration

### Prérequis Système
- **GPU:** AMD Instinct MI300X ou NVIDIA H100
- **OS:** Ubuntu 22.04.4 LTS
- **ROCm:** Version 6.3.1+
- **Docker/Podman:** Pour containerisation

### Dockerfile ROCm
```dockerfile
FROM rocm/vllm:rocm6.2_mi300_ubuntu20.04_py3.9_vllm_0.6.4

# Configuration environnement
WORKDIR $PWD/app
ENV PYTORCH_ROCM_ARCH="gfx90a;gfx942"

# Installation vLLM
RUN pip uninstall -y vllm && \
    git clone -b v0.6.3 https://github.com/vllm-project/vllm.git && \
    cd vllm && \
    MAX_JOBS=$(nproc) python3 setup.py install

# Installation dépendances
RUN pip install "tensordict<0.6" --no-deps && \
    pip install accelerate codetiming datasets dill hydra-core \
    liger-kernel numpy pandas peft "pyarrow>=15.0.0" \
    pylatexenc "ray[data,train,tune,serve]" torchdata \
    transformers wandb orjson pybind11 && \
    pip install -e . --no-deps
```

### Lancement Docker
```bash
docker run --rm -it \
    --device /dev/dri \
    --device /dev/kfd \
    -p 8265:8265 \
    --group-add video \
    --cap-add SYS_PTRACE \
    --security-opt seccomp=unconfined \
    --privileged \
    -v $HOME/.ssh:/root/.ssh \
    -v $HOME:$HOME \
    --shm-size 128G \
    -w $PWD \
    verl-rocm
```

## Configuration d'Entraînement

### Variables d'Environnement AMD
```bash
export HIP_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export ROCR_VISIBLE_DEVICES=$HIP_VISIBLE_DEVICES
GPUS_PER_NODE=8
```

### Préparation des Données
```bash
# Exemple avec GSM8K
python3 examples/data_preprocess/gsm8k.py --local_dir ../data/gsm8k
```

### Chargement des Modèles
```bash
# Modèles supportés
python3 -c "import transformers;transformers.pipeline('text-generation', model='Qwen/Qwen2-7B-Instruct')"
python3 -c "import transformers;transformers.pipeline('text-generation', model='deepseek-ai/deepseek-llm-7b-chat')"
```

## Scripts d'Entraînement

### PPO (Proximal Policy Optimization)
```bash
MODEL_PATH="Qwen/Qwen2-7B-Instruct"
TP_VALUE=2
INFERENCE_BATCH_SIZE=32
GPU_MEMORY_UTILIZATION=0.4

python3 -m verl.trainer.main_ppo \
    data.train_files=$train_files \
    data.val_files=$test_files \
    data.train_batch_size=1024 \
    data.max_prompt_length=1024 \
    data.max_response_length=512 \
    actor_rollout_ref.model.path=$MODEL_PATH \
    actor_rollout_ref.actor.optim.lr=1e-6 \
    actor_rollout_ref.model.use_remove_padding=True \
    actor_rollout_ref.actor.ppo_mini_batch_size=256 \
    actor_rollout_ref.actor.ppo_micro_batch_size_per_gpu=16 \
    actor_rollout_ref.model.enable_gradient_checkpointing=True \
    actor_rollout_ref.rollout.tensor_model_parallel_size=$TP_VALUE \
    actor_rollout_ref.rollout.name=vllm \
    actor_rollout_ref.rollout.gpu_memory_utilization=$GPU_MEMORY_UTILIZATION \
    critic.optim.lr=1e-5 \
    critic.model.path=$MODEL_PATH \
    algorithm.kl_ctrl.kl_coef=0.001 \
    trainer.n_gpus_per_node=8 \
    trainer.nnodes=1 \
    trainer.total_epochs=50
```

### GRPO (Group Relative Policy Optimization)
```bash
MODEL_PATH="Qwen/Qwen2-7B-Instruct"
TP_VALUE=2
INFERENCE_BATCH_SIZE=40
GPU_MEMORY_UTILIZATION=0.6

python3 -m verl.trainer.main_ppo \
    algorithm.adv_estimator=grpo \
    data.train_files=$train_files \
    data.val_files=$test_files \
    data.train_batch_size=1024 \
    data.max_prompt_length=512 \
    data.max_response_length=1024 \
    actor_rollout_ref.model.path=$MODEL_PATH \
    actor_rollout_ref.actor.optim.lr=1e-6 \
    actor_rollout_ref.actor.use_kl_loss=True \
    actor_rollout_ref.actor.kl_loss_coef=0.001 \
    actor_rollout_ref.actor.kl_loss_type=low_var_kl \
    actor_rollout_ref.rollout.n=5 \
    trainer.total_epochs=50
```

## Entraînement Multi-Nœuds

### Configuration Slurm
```bash
# Script Slurm pour cluster multi-nœuds
sbatch slurm_script.sh
```

### Orchestration Ray
- **Initialisation:** Configuration automatique du cluster Ray
- **Coordination:** Synchronisation entre nœuds
- **Monitoring:** Surveillance des ressources distribuées

## Benchmarks de Performance

### Résultats PPO

| Plateforme | Modèle | TP_VALUE | Batch Size | GPU Memory | Throughput (Token/GPU/Sec) | Convergence (Acc.) |
|------------|--------|----------|------------|------------|----------------------------|-------------------|
| H100 | Qwen2-7B-Instruct | 2 | 32 | 0.4 | 907.24 | 87.6% |
| MI300X | Qwen2-7B-Instruct | 2 | 32 | 0.4 | **921.24** | 87.5% |
| H100 | deepseek-llm-7b-chat | 4 | 32 | 0.4 | 623.52 | 70.3% |
| MI300X | deepseek-llm-7b-chat | 4 | 32 | 0.4 | **767.47** | 70.3% |

### Résultats GRPO

| Plateforme | Modèle | TP_VALUE | Batch Size | GPU Memory | Throughput (Token/GPU/Sec) | Convergence (Acc.) |
|------------|--------|----------|------------|------------|----------------------------|-------------------|
| H100 | Qwen2-7B-Instruct | 2 | 40 | 0.6 | 1544.30 | 90.0% |
| MI300X | Qwen2-7B-Instruct | 2 | 40 | 0.6 | **1747.94** | 89.7% |
| H100 | deepseek-llm-7b-chat | 2 | 110 | 0.4 | 1624.42 | 71.2% |
| MI300X | deepseek-llm-7b-chat | 2 | 110 | 0.4 | **1899.04** | 70.9% |

### Observations Clés
- **Performance AMD:** MI300X surpasse H100 en throughput
- **Convergence:** Précision équivalente entre plateformes
- **Efficacité:** GRPO plus performant que PPO
- **Scalabilité:** Performance linéaire avec le nombre de GPU

## Optimisations Spécifiques AMD

### 1. Modifications Kernel ROCm
- **PR #360:** Support AMD (ROCm Kernel)
- **Compatibilité:** Exécution stable sur GPU AMD
- **Performance:** Optimisations spécifiques HIP

### 2. Corrections Ray
- **PR #51104:** Gestion HIP_VISIBLE_DEVICES
- **Allocation:** Ressources dynamiques fiables
- **Stabilité:** Prévention des conflits de devices

### 3. Variables d'Environnement
```bash
# Spécifique AMD (différence cruciale vs CUDA)
export HIP_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export ROCR_VISIBLE_DEVICES=$HIP_VISIBLE_DEVICES
```

## Configuration Système Recommandée

### Hardware AMD Instinct MI300X
- **CPU:** Intel Xeon Platinum 8480+
- **Mémoire:** 2048 GiB DDR5 4400 MT/s
- **GPU:** 8x AMD Instinct MI300X 192GB HBM3
- **Stockage:** 30.72 TB NVMe SSD
- **Réseau:** InfiniBand pour multi-nœuds

### Software Stack
- **OS:** Ubuntu 22.04.4 LTS
- **ROCm:** 6.3.1+
- **Python:** 3.9+
- **PyTorch:** Version ROCm
- **vLLM:** 0.6.3+

## Ressources et Documentation

### Liens Officiels
- **Repository:** https://github.com/volcengine/verl
- **Documentation:** https://verl.readthedocs.io/
- **Releases:** https://github.com/volcengine/verl/releases
- **Tutoriels AMD:** https://github.com/volcengine/verl/blob/main/docs/amd_tutorial/

### Support Communauté
- **Issues GitHub:** Rapports de bugs et demandes
- **Discussions:** Forum communautaire
- **Contributions:** PRs et améliorations

## Conclusion

veRL représente une solution mature et performante pour l'entraînement RLHF à grande échelle, avec un support excellent pour les GPU AMD Instinct. Les performances supérieures du MI300X par rapport au H100 en font une option attractive pour les déploiements de production.
