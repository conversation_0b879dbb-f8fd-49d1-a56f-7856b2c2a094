# Absolute Zero Reasoner - Repository GitHub Officiel

**URL:** https://github.com/LeapLabTHU/Absolute-Zero-Reasoner

## Informations Générales

- **Organisation:** LeapLabTHU (Tsinghua University)
- **Étoiles:** 1.4k
- **Forks:** 224
- **Licence:** MIT
- **Langage principal:** Python (91.2%), Shell (8.8%)

## Structure du Repository

### Dossiers Principaux
- `absolute_zero_reasoner/` - Code principal de l'implémentation AZR
- `assets/` - Images et ressources visuelles
- `data/` - Données d'entraînement et d'évaluation
- `extras/` - Utilitaires supplémentaires
- `scripts/` - Scripts d'entraînement et d'évaluation
- `verl/` - Framework de reinforcement learning (fork)

### Fichiers Importants
- `README.md` - Documentation principale
- `requirements.txt` - Dépendances Python
- `LICENSE` - Licence MIT
- `.gitignore` - Configuration Git

## Installation et Configuration

### Environnement
```bash
conda create -n azr python=3.10
conda activate azr
conda install nvidia/label/cuda-12.4.1::cuda-toolkit
cd verl
pip install -e .
cd ..
pip install wheel
pip install flash-attn --no-build-isolation
pip install -r requirements.txt
pip uninstall vllm
pip install vllm==0.7.3
pip install transformers==4.47.1
pip install "math-verify[antlr4_9_3]"
pip install debugpy
```

### Traitement des Données
```bash
python -m absolute_zero_reasoner.data_construction.process_code_reasoning_data
```

## Entraînement

### Amorçage (Seeding) - Optionnel
```bash
export OUTPUT_SEED_PATH=data/<new_ded_abd_seed_data_name>.jsonl
export OUTPUT_CODE_F_SEED_PATH=data/<new_ind_seed_data_name>.jsonl
bash scripts/seeding/<7b|14b|coder3b|coder7b|coder14b|llama>.sh
```

### Auto-jeu (Self-play)
```bash
# Exigences matérielles :
# 3B models: 2 x 80GB GPUs
# 7/8B models: 4 x 80GB GPUs  
# 14B models: 8 x 80GB GPUs

bash scripts/selfplay/<7b|14b|coder3b|coder7b|coder14b|llama>.sh
```

### Reprise d'Entraînement
Pour reprendre un entraînement, ajouter l'ID wandb dans le script :
```bash
trainer.wandb_run_id=<run_id>
```

### Conversion de Checkpoints
```bash
python -m absolute_zero_reasoner.utils.convert2hf \
  <veRL_ckpt_path>/actor \
  <veRL_ckpt_path>/actor/huggingface/ \
  <hf_ckpt_path>
```

## Template de Prompt

Le système utilise le template Deepseek R1 :

```
A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> <answer> answer here </answer>. User: {question}\nAssistant: <think>
```

## Résultats Principaux

### Performance Générale
| Modèle | Base | #data | Code Avg | Math Avg | Total Avg |
|--------|------|-------|-----------|-----------|-----------|
| Qwen2.5-7B | - | - | 52.0 | 27.5 | 39.8 |
| AZR (Ours) | Base | **0** | 55.2 +3.2 | 38.4 +10.9 | 46.8 +7.0 |
| AZR (Ours) | Coder | **0** | **61.6** +5.0 | 39.1 +15.2 | **50.4** +10.2 |

### Scaling Results
| Modèle | Variant | Code Avg | Math Avg | Total Avg |
|--------|---------|-----------|-----------|-----------|
| Qwen2.5-3B Coder | + AZR | 54.9 +3.7 | 26.5 +7.7 | 40.7 +5.7 |
| Qwen2.5-7B Coder | + AZR | 61.6 +5.0 | 39.1 +15.2 | 50.4 +10.2 |
| Qwen2.5-14B Coder | + AZR | 63.6 +3.6 | 43.0 +22.8 | 53.3 +13.2 |

## Flux Algorithmique

### 1. PROPOSE
Le modèle génère des tâches de raisonnement de trois types :
- **Abduction** : Prédire l'entrée
- **Déduction** : Prédire la sortie  
- **Induction** : Inférer le programme

### 2. SOLVE
Le modèle tente de résoudre les tâches auto-générées avec vérification par exécution Python.

## Récompenses Intrinsèques Personnalisables

Possibilité d'ajouter des récompenses personnalisées dans `azr.reward.generation_reward_config` :
- Récompenses de diversité
- Récompenses de complexité
- Autres métriques personnalisées

## Avertissement de Sécurité

⚠️ **ATTENTION** : L'exécuteur Python est basique et destiné uniquement à la recherche. Il n'est pas sécurisé pour les environnements de production.

## Liens Utiles

- **Page Projet:** https://andrewzh112.github.io/absolute-zero-reasoner/
- **Paper:** https://arxiv.org/abs/2505.03335
- **Modèles:** https://huggingface.co/collections/andrewzh/absolute-zero-reasoner-68139b2bca82afb00bc69e5b
- **Logs:** https://wandb.ai/andrewzhao112/AbsoluteZeroReasoner

## Contact

**Auteur principal:** Andrew Zhao  
**Email:** <EMAIL>

## Remerciements

- **veRL framework** - Base du code d'entraînement RL
- **vLLM** - Pour les rollouts
- **QwQ Repository** - Composants d'exécution Python
- **PRIME** - Structure du README

## Citation

```bibtex
@misc{zhao2025absolutezeroreinforcedselfplay,
      title={Absolute Zero: Reinforced Self-play Reasoning with Zero Data}, 
      author={Andrew Zhao and Yiran Wu and Yang Yue and Tong Wu and Quentin Xu and Yang Yue and Matthieu Lin and Shenzhi Wang and Qingyun Wu and Zilong Zheng and Gao Huang},
      year={2025},
      eprint={2505.03335},
      archivePrefix={arXiv},
      primaryClass={cs.LG},
      url={https://arxiv.org/abs/2505.03335}, 
}
```

## Roadmap

- ✅ Code d'entraînement publié
- ⏳ Code d'évaluation
- ⏳ Mise à jour veRL
- ⏳ Amélioration de l'exécuteur Python
