# EXTRACTION TEXTUELLE - AI_Brazilian_IPEA_Report.pdf
# Généré automatiquement le 2025-05-28 10:22:42
# Source: AI_Brazilian_IPEA_Report.pdf
# ================================================================


--- PAGE 1 ---
Título do capítulo CAPÍTULO 5 – SEGMENTOS TECNOLÓGICOS E APLICAÇÕES
Autora Rosane S. Lourenço
DOI http://dx.doi.org/10.38116/978-65-5635-071-4/capitulo5
DO PARADOXO DAS HABILIDADES À SUPERINTELIGÊNCIA PÓS-
HUMANA: COMO A TECNOLOGIA DA INFORMAÇÃO E
Título do livro COMUNICAÇÃO VEM FORJANDO O FUTURO DA RELAÇÃO
HOMEM-MÁQUINA
Organizadora Rosane Lourenço
Volume 4
Série Nova Geração de Infraestrutura
Cidade Brasília
Editora Instituto de Pesquisa Econômica Aplicada (Ipea)
Ano 2024
Edição -
ISBN 978-65-5635-071-4
DOI http://dx.doi.org/10.38116/978-65-5635-071-4
© Instituto de Pesquisa Econômica Aplicada – ipea 2024
As publicações do Ipea estão disponíveis para download gratuito nos formatos PDF (todas)
e EPUB (livros e periódicos). Acesse: http://www.ipea.gov.br/portal/publicacoes
As opiniões emitidas nesta publicação são de exclusiva e inteira responsabilidade dos autores, não exprimindo,
necessariamente, o ponto de vista do Instituto de Pesquisa Econômica Aplicada ou do Ministério do
Planejamento e Orçamento.
É permitida a reprodução deste texto e dos dados nele contidos, desde que citada a fonte. Reproduções para
fins comerciais são proibidas.
--- PAGE 2 ---
CAPÍTULO 5
SEGMENTOS TECNOLÓGICOS E APLICAÇÕES
1 INTRODUÇÃO
Este capítulo busca esclarecer aspectos técnicos relacionados à convergência das
tecnologias da informação e comunicação (TICs). Para isso, começa por analisar
a pesquisa anual do grupo Gartner com os destaques em matéria de tendências
tecnológicas emergentes, com potencial para impactar os negócios e a sociedade
pelos dez anos seguintes. O Hype Cycle é fruto de trabalho desenvolvido desde
1995 com o objetivo de orientar o mercado sobre o estágio de maturação tec-
nológico de determinado serviço ou produto. Constitui-se de curva dividida em
cinco segmentos, um trecho ascendente correspondente à descoberta da inovação,
passando pelo pico das expectativas, o declínio da curva com a desilusão, novo
trecho ascendente moderado para esclarecimento e adesão da ideia e, finalmente,
o platô com atingimento de produtividade industrial. A velocidade de evolução
até o último estágio de estabilização da produção tecnológica é representada por
ícones legendados, conforme o número de anos do ciclo descritos na figura 1.
FIGURA 1
Ciclos de hype (extravagâncias) para tecnologias emergentes
Fonte: Gartner (2020). Disponível em: https://www.gartner.com/smarterwithgartner/5-trends-drive-the-gartner-hype-cycle-for-
-emerging-technologies-2020.
Obs.: 1. A evolução da tecnologia no tempo passa por todas as etapas do ciclo de maturidade (gatilho de inovação, pico de
expectativas infladas, fosso de desilusão, rampa de entendimento até alcançar o patamar de produtividade).
2. Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
--- PAGE 3 ---
|
548 Do paradoxo das habilidades à superinteligência pós-humana
A orientação dada pelo Grupo Gartner, em 20181 foi dirigida aos líderes de
tecnologia (infraestrutura e operação) e ressalta a importância de manter informa-
da as áreas estratégicas das empresas a respeito de produtos e serviços cujo rápido
crescimento está associado a um progresso técnico sem precedentes. A atualização
anualizada das tendências tecnológicas mais comentadas no mercado, Hype Cycle
de 2015 a 2020, especialmente relacionadas aos ecossistemas e infraestruturas de
hardware e software, com perspectiva de desenvolvimento no curto, médio e longo
prazo foi resumida no quadro 1.
QUADRO 1
Resumo das estimativas da Gartner para os ciclos tecnológicos emergentes (Hype Cycles)
Tecnologias especificadas Até cinco anos Entre cinco e dez anos Superior a dez anos
Destreza digital, realidade virtual
e aumentada em nuvem, espaços
inteligentes centrados em pessoas,
Tecnologias de distanciamento computação afetiva e cognitiva,
social, passaporte de saúde, casa inteligente e conectada,
empresa agregadora, pacotes plataformas IoT, gêmeo digital Hyperautomation,5 redes des-
Ecossistemas digitais de habilidade para negócios, de pessoa/cidadão, multiexpe- centralizadas, neurobusiness,6
traga sua própria identidade, riência imersiva, ambientes de humano aumentado/humano 2.0.
ontologias e gráficos, plataforma trabalho inteligentes, DigitalOps,2
IoT,1 realidade virtual. Organização Autônoma Descen-
tralizada,3 composable enterprise,4
gerenciamento da taxonomia e
ontologia empresarial.
Nuvem distribuída (pública) para Poeira de chips inteligentes (smart
Aumento de taxa de transferência
Rede e dispositivos (sensores e locais sem datacenters, coisas dust), sensores bioacústicos,
de dados, redes 5G privadas para
atuadores) autônomas, disponibilidade de sensores biodegradáveis, rede
propósitos específicos.
rede 5G pública. descentralizada.
Aprimoramento do huma-
Interface cérebro-máquina (ICM) no artificial/natural, interface
Biotecnologia – em dupla via, biochips, impressão cérebro-computador, tecidos
3D para implante de órgãos. artificiais cultivados, computação
e armazenamento em DNA.
Computação pós-clássica com
unidades centrais de processa-
mento (CPUs) mais rápidas e
Computação sem servidor, memórias mais densas, micro
computação em nuvem híbrida, datacenters, tecnologia de
Computação quântica, displays
computação de borda, rede neural sistema autorregeneráveis ou
holográficos/volumétricos,
profunda em circuito integrado autorreparáveis (self-healing
impressão 4D,7 impressão 3D em
Hardware e materiais Application Specific Integrated systems), displays volumétricos,
nanoescala, máquina inteligente
Circuit (Asics), câmeras sensoras bateria anodo de silício, eletrônica
de propósito geral, hardware
3D, placas simples de baixo de nanotubo carbono, hardware
neuromórfico.
custo na borda, impressão 3D neuromórfico, dispositivos de
para empresas. controle de gestos, próxima
geração de memória, transistores
base carbono, impressão 3D para
consumidor doméstico.
(Continua)
1. Para mais informações, acessar: https://www.gartner.com/en/newsroom/press-releases/2018-08-20-gartner-identi-
fies-five-emerging-technology-trends-that-will-blur-the-lines-between-human-and-machine.
--- PAGE 4 ---
|
Segmentos tecnológicos e aplicações 549
(Continuação)
Tecnologias especificadas Até cinco anos Entre cinco e dez anos Superior a dez anos
Assistente e assessor virtual
pessoal, tecnologia de literacia
Assistentes virtuais, assessor de pessoas (PLT), pergunta/
cognitivo especializado, reco- resposta em linguagem natural
nhecimento de fala, inteligência e conversacional, redes neurais
aumentada, in-memory analytics,8 adversárias generativas (generative
IA e analytics de borda, analytics adversarial network – GAN), IA
avançada com serviço de entrega, plataforma como serviço (platform
IA (incorporada/aumentada/ as a service – PaaS), plataforma
Software IA de propósito geral.
embebida/compósita/generativa), IA conversacional, IA de borda,
ML, deep learning (DL), tradução IA explicável, IA assistente
speech-to-speech (fala a fala em de projeto, IA emocional, IA
tempo real), pergunta/resposta responsável, IA aumentada, ML
em linguagem natural, software- adaptativa, aprendizado profundo
-definidor de qualquer coisa, rede por reforço, aprendizado auto-
neural profunda. supervisionado, aprendizado de
transferência, gráficos analíticos
e de conhecimento.
Segurança definida por software,
Ciência de dados dos cidadãos,
segurança digital, segurança
business intelligence de descoberta
da IA e de borda, autenticação
de dados aumentados, privacidade
de procedência, Secure Access
Dados de segurança operacional como diferencial competitivo,
Service Edge (Sase),9 dados sociais –
e contra atos ilícitos câmbio de criptomoedas, block-
e sintéticos, tecido de dados,
chain (distribuição, imutabilidade,
proteção de negócios, detecção
descentralização, criptografia e
e resposta estendida, blockchain
tokenização).
para segurança de dados.
Tecnologias vestíveis, robôs
autônomos e inteligentes, robôs
móveis autônomos, veículos Veículos autônomos terrestres
Superestruturas Robôs móveis, drones comerciais. autônomos, drones de carga leve, níveis 4 e 5, veículos autônomos
drones de detecção e mobilidade, voadores.
sistemas de satélite de baixa
órbita (altitude < 1.200 milhas).
Fonte: Adaptado de Gartner, hype cycles de 2015 a 2020.
Notas: 1 Internet das coisas (internet of things).
2 DigitalOps – Central de “processamento” e transformação digital, que proporciona a orquestração de sistemas e outros
recursos e incorpora mecanismos de detecção e resposta, ao mesmo tempo que potencialmente suporta aprendizado
dinâmico e otimização.
3 Decentralized Autonomous Organization (DAO) – organização autogovernada e descentralizada, cujo contrato inteli-
gente de operação segue regras especificadas em programação computacional distribuída e validada por usuários de
contabilidade de tecnologias distribuídas (Distributed Ledger Technology – DLT)/blockchain.
4 Empresa compósita – abordagem modular que combina recursos de infraestrutura de rede, armazenamento e com-
putação em plataforma única.
5 Hiperautomação – inteligência artificial/machine learning (IA/ML) aplicados em todos os processos de fabricação e negócios.
6 Neuronegócios – ramificação da neurociência comportamental que estuda a mente em benefício dos negócios.
7 Impressão 4D – material 3D modificável no tempo por estímulos como água, temperatura e eletricidade.
8 In Memory Analytics – análise em memória ou em tempo real, metodologia de inteligência de negócios (business
intelligence) utilizada para resolver problemas tempo-sensitivos com rápido acesso aos dados armazenados em
servidores com memórias de acesso randômico – RAM.
9 Serviço de Acesso Seguro em Borda – cibersegurança localizada na periferia ou ponta do sistema, próxima ao usuário.
Tecnologias emergentes do Gartner, para Hype Cycle do período 2015-2020:
1) Cyber Segurança de Final de Linha (end point) – tendências apresentadas
em 2020, como sendo tecnologias unified endpoint security, extended
detection and response, business compromise protection, buy your own
PC (BYOPC) security e Secure Access Service Edge (Sase).
--- PAGE 5 ---
|
550 Do paradoxo das habilidades à superinteligência pós-humana
2) Digital twin – substituição de protótipos reais pelos correspondentes
digitais que funcionam com modelos de simulação detalhada de objetos
físicos, processos e serviços (por exemplo, atuadores). Durante o processo
de produção, o ambiente físico de testagem de produtos é substituído
pela simulação em meio digital alimentada por informações que permi-
tem a detecção de problemas, correção e aperfeiçoamento industrial. A
manutenção de sistemas é otimizada, por meio da coleta de dados de usos
de produtos inteligentes em rede IoT e interação com a cópia virtual.
3) Deep neural network Asics (Application Specific Integrated Circuit) – pro-
mete ser a terceira ou quarta geração de circuitos integrados de aplicação
específica, juntamente com os Asics alimentados por fótons. A segunda
geração de Asic, unidade de processamento de tensor (tensor processing
unit – TPU) foi criada pela Google em 2016. A primeira geração de Asic,
também denominado system-on-a-chip, realizava tarefas com alta veloci-
dade em ambientes pré-definidos e incluía microprocessadores internos,
memórias (read-only memory – ROM, random access memory – RAM,
electrical erasable programmable read only memory – EEPROM, flash)
e arranjo de transistores (matriz de portas) customizáveis de acordo com
interconexões definidas pelo cliente.
4) Self-healing system technology – tecnologia de sistema autorreparável,
diz-se de todo dispositivo que possui a funcionalidade de detectar
falhas de funcionamento e, sem assistência externa, proceder ajustes e
continuar a operação normalmente.
5) Volumetric display – tela ou dispositivo volumétrico gráfico capaz de mos-
trar visualizações em três dimensões, sem auxílio de artifícios ou efeitos
visuais comumente empregados nas telas bidimensionais convencionais.
6) Exoskeleton e endoskeleton – exoesqueleto é o dispositivo externo de pro-
teção associado a um organismo que permite intensificar movimentos;
endoesqueleto é o esqueleto interno resistente e mineralizado associado
ao movimento do tecido muscular.
7) Neuromorphic hardware – derivado da engenharia de computação neuro-
mórfica, ao nível de máquina ou equipamento ou chip neuromórfico, para
integração em grande escala (very large scale integration – VLSI) desenhado
para replicar as funções do cérebro humano. Conceito desenvolvido por
Carver Mead no final da década de 1980.
8) Smart dust – poeira inteligente, coleção de sistemas eletromecânicos
microscópicos, passíveis de suspensão no ar, utilizados para capturar e
reunir informações de ambientes hostis à vida.
--- PAGE 6 ---
|
Segmentos tecnológicos e aplicações 551
9) Ubiquitous infrastructure – infraestrutura ubíqua, associada à proatividade
da computação pervasiva, que possibilita o acesso ao ambiente computacio-
nal do usuário a todo dispositivo, de qualquer lugar e de forma permanente.
10) Do it yourself (DIY) biohacking – em tradução literal, praticantes de
biologia faça você mesmo, capacidade de explorar novas funções bioló-
gicas, como ler DNA, identificar espécies, analisar marcadores genéticos
e cultivar microrganismos não patogênicos para obtenção de produtos
em laboratório, operações essas sujeitas à licença estatal.
As tecnologias identificadas pelo Gartner Group no Hype Cycle são emer-
gentes e disruptivas por natureza. Portanto, ilustram o potencial das ferramentas,
mas estão associadas a risco de implementação. As empresas escolherão aquelas
que melhor se adaptam ao seu modelo de negócio, com a perspectiva de usufruir
benefícios maiores associados ao fato de serem as primeiras a adotarem amplamente
o produto ou meio de produção.
A Accenture (2019) buscou evidenciar a defasagem das empresas na aplicação
das conquistas recentes em matéria de tecnologia. Para isso, coletou informações
sobre: i) adoção de tecnologias-chave; ii) incorporação das tecnologias adotadas; e
iii) transformação organizacional e cultural. O resultado evidenciou que 10% das
empresas destacavam-se como líderes e 25% delas como retardatárias, ou seja, que
adotam as tecnologias como soluções pontuais sem uma visão sistêmica e estraté-
gica. A diferença no crescimento estimado da receita entre líderes e retardatárias
foi de 46%, em 2023. O resultado obtido pelas empresas líderes é decorrente do
investimento consistente em tecnologias de propósito geral, pois adotaram IA na
taxa de 98%, com complementação de repositório de dados e serviços em nuvem,
enquanto as retardatárias (42%) adotaram a tecnologia ao acaso e a título ilustrativo,
como seguidoras de tendências.
2 PROCESSAMENTO DE DADOS E INFRAESTRUTURA DE TIC
Esta seção traz algumas discussões que ganharam relevância a partir do quadro de
intensas transformações provocadas pelos avanços tecnológicos. O salto no nível
de complexidade envolvendo o processamento de dados e a infraestrutura de TI
merecem atenção redobrada, pois derivam em questões que poderão requerer
regulação ou regulamentação futuras. Neste aspecto, tanto a coleta quanto o
processamento dos dados têm importância ampliada, pois envolvem informações
públicas e privadas. Ademais, convém esclarecer que, na nomenclatura interna-
cional específica para a área de tecnologia da informação, o termo infraestrutura é
empregado de forma abrangente para designar tanto os mecanismos e dispositivos
físicos de hardware quanto softwares e protocolos de comunicação que viabilizam
a computação de operações digitais para nova geração de TICs.
--- PAGE 7 ---
|
552 Do paradoxo das habilidades à superinteligência pós-humana
2.1 Big data e analytics
Enquanto insumo básico para o processamento das tecnologias de informação, os
dados têm notório papel na indução do desenvolvimento da indústria de software e
hardware. A geração extensiva e o aproveitamento amplificado desses elementos-chave
viabilizaram o emprego de técnicas cujo objetivo, em última análise, é trazer um pouco
mais de sofisticação para o processo de tomada de decisão.
Para isso, foi preciso que o volume de dados gerados, coletados e consumidos
globalmente chegasse a 64,2 Zettabytes, em 2020, devendo alcançar 180 ZB, em
2025.2 Trata-se de um salto em relação à estimativa inicial da International Data
Corporation (de 44 ZB - 1021 bytes para 2020), e encontra explicação no avanço do
uso da telemática, devido ao isolamento social imposto pela pandemia de covid-19.
Conjugado aos avanços da capacidade de processamento de hardware e de software,
as tecnologias de IoT e IA permitem a análise de quantidades massivas de dados,
sejam eles estruturados, semiestruturados ou não estruturados.
2.1.1 Conceitos
1) Big data – dados digitais em quantidades gigantescas, cujo tamanho vai
além da capacidade de captura, armazenagem, processamento e análise
de softwares típicos de base de dados, podendo variar de dúzias de TB a
múltiplos PB (mil terabytes, 1015 bytes). Conforme a especificidade do
setor industrial em que são criados e processados, assumem variedade
de formas (áudio, voz, textos, imagens), resultando em crescimento
exponencial dos dados anteriormente existentes e possibilitando o de-
senvolvimento de novas tecnologias.
2) Data analytics – nova tecnologia de organização, visualização e análise
de dados (estruturados ou não) para descrição histórica, preditiva para
tomada de decisões e prescritiva para mensuração de diferentes alternativas
de escolhas estratégicas. Utilização em inteligência de negócios (busi-
ness intelligence) e automação de algoritmos de análise, num contexto
de computação consciente (aware computing), permitindo enriquecer
processos complexos com alto grau de sofisticação na análise automática
avançada e revelando aspectos importantes, mas pouco evidentes na análise
convencional, que favorecem a minimização de riscos.
3) Big data analytics – termo que associa ambas as definições descritas aci-
ma, sendo tecnologia que deriva em conhecimento e valor extraídos de
quantidade massiva de dados, incluindo dados de grande diversidade e
não estruturados.
2. Para mais informações, acessar: https://www.statista.com/statistics/871513/worldwide-data-created/.
--- PAGE 8 ---
|
Segmentos tecnológicos e aplicações 553
4) Tecnologia de dispositivo (device technology) – desenvolvimento de tec-
nologias para uso em mecanismos físicos e digitais com funcionalidades
específicas. Possibilita o processamento em tempo real e alta velocidade,
de grande volume de dados com baixo consumo de energia.
5) Computação contexto consciente (context-aware computing) – Schilit e
Theimer conceberam a ideia em 1994, como sendo softwares-aplicações
que se adaptam ou respondem a conjunto de pessoas e objetos nas pro-
ximidades, assim como às mudanças desses objetos no tempo, portanto,
conforme o contexto físico e lógico detectado por sensores.
A disponibilidade de tamanha quantidade de dados tornou possível calibrar
algoritmos de ML e validar análises. O potencial de adição de valor com o uso de
ML, para elaborar estimativas a partir de treinamento com big data, deve alcançar
cifras na casa dos trilhões de dólares nos próximos anos. Porém, é preciso ter atenção
para questões críticas, que incluem a posse e uso dos dados, controle dos dados indi-
viduais, monopólio de dados, dados abertos, dados sigilosos e segurança dos dados.
2.1.2 Dados sigilosos
A Lei de Acesso à Informação (arts. 24 e 27 da Lei no 12.527/2011) classifica as
informações, conforme o prazo máximo de restrição e competência para classi-
ficação: i) ultrassecreta (25 anos – Presidência e Vice-Presidência da República,
ministros de Estado, comandantes militares e diplomatas); ii) secreto (15 anos –
acrescentados os titulares de autarquias, fundações, empresas estatais); e iii) reservada
(5 anos – acrescentadas as funções de direção). Os níveis de compartilhamento de
dados entre órgãos e entidades, discriminados no Decreto no 10.046/2019, são
classificados como sendo: i) amplo compartilhamento – dados cuja divulgação
deve ser pública e garantida a quaisquer interessados; ii) compartilhamento res-
trito – dados protegidos por sigilo, com regras estabelecidas pelo Comitê Central
de Governança de Dados; e iii) compartilhamento específico – dados protegidos
por sigilo, com regras definidas pelos gestores de dados.
2.1.3 Monopólio de dados
Quanto ao monopólio de dados, constatando-se a falha de mercado, é prática con-
traproducente que impacta negativamente o avanço das inovações. Nesse caso, a
solução apresentada pelos especialistas do Turing Institute para mitigar o problema
foi a proposta de um sistema de patentes de dados compartilhados ou, eventualmente,
monopólio com prazo de validade determinado para expirar. Outro aspecto da questão
se refere à forma como esses dados são obtidos a partir de suas fontes. Nesse caso,
no modelo de coparticipação, para o qual as companhias montam as bases de dados
a partir de informações de usuários individuais, haveria a possibilidade de micropa-
gamentos ou anuidades para os usuários que municiam os sistemas com seus dados.
--- PAGE 9 ---
|
554 Do paradoxo das habilidades à superinteligência pós-humana
Portanto, o monopólio de dados se estende ao monopólio de acesso e dis-
seminação de informações, geralmente controlados por grandes empresas globais
de tecnologia que desenvolvem softwares de acesso à internet, motores de busca,
correspondência por e-mail e plataformas de divulgação de vídeos. O uso dessas
informações tem finalidades diversas, por exemplo, influenciar a formação de opi-
niões, censurar temas sensíveis e filtrar informações, conforme interesses políticos
e até mesmo comerciais. Além de potencial violação do direito à privacidade, o
monopólio pode inibir a oferta de serviços digitais por parte das novas empresas
e a liberdade de escolha dos consumidores, derivando inclusive em eventual car-
telização de preços para produtos ofertados em marketplace.
2.1.4 Movimentos Open Source/Open Data/Open Government
O movimento Open Data (dados abertos) foi inspirado numa iniciativa mais
antiga – o Open Source (código aberto), concebido na década de 1980, com o
objetivo de desenvolver softwares de acesso público. Também conhecido como
software livre, o Open Source baseia-se em quatro liberdades fundamentais, quais
sejam: utilização, estudo, modificação e redistribuição de conjuntos de comandos
de computação, que visam à testagem e ao aprimoramento dos algoritmos.
A agenda do movimento Open Data advoga os mesmos princípios de acesso
aberto aos bancos de dados como forma de inibir o monopólio. Os acessos in-
cluiriam dados de bancos públicos (por exemplo, transportes e mobilidade) e de
bancos de dados científicos, (por exemplo, disponibilidade de dados experimentais
e artigos de origem) por qualquer pessoa interessada na produção de resultados
de valor. A renúncia aos direitos autorais (copyleft) e a identificação da origem dos
dados são as únicas condições estabelecidas para quem fornece os dados, devendo
haver reciprocidade por parte de quem utiliza os mesmos.
De acordo com Davies et al. (2019), o movimento Open Data contemporâ-
neo começou com a Conferência Internacional Open Data (IODC), patrocinada
pelo Departamento de Comércio Norteamericano, paralelamente à Conferência
Open Government Data Camp, promovida pela sociedade civil em Londres, em
2010. Ambos os eventos apresentaram os primeiros passos de institucionalização e
concepção, com alguns casos demonstrativos de construção de plataformas, além
de discutir o potencial do uso de dados abertos através dos setores. Na segunda
conferência IODC, ocorrida em 2012 e chancelada pelo Banco Mundial, o contexto
internacional de evolução de dados abertos foi avaliado, assim como a expansão
para países em desenvolvimento.
Em 2015, a terceira conferência em Ottawa analisou o progresso dos dados aber-
tos em diferentes setores e regiões, para assegurar a produção dos resultados gerados,
o estabelecimento de metas claras e a identificação de estratégias. Logo, foi constatado
--- PAGE 10 ---
|
Segmentos tecnológicos e aplicações 555
que o movimento se replicara, em atendimento às diferentes agendas, como Open-
Data for Development (OD4D), Open Data Barometer, Open Data Charter, Open
Government Partnership, Open Data Working Group e Open Data Leaders Network.
A quarta IODC, de 2016 em Madri, focou o impacto local visando ao compartilha-
mento de princípios e metas globais. Na quinta IOCD de 2018, em Buenos Aires,
a tecnologia de IA e a padronização de dados foram abordadas, envolvendo escolhas
políticas e governança dos dados. Pela primeira vez, a agenda tratou de ameaças críticas,
reconhecendo a vulnerabilidade futura dos dados abertos.
O movimento conta com o apoio do grupo de nações avançadas G8 que, em
2013, firmaram compromisso de abertura de dados, especialmente nas áreas de
estatística, eleitoral, de orçamento e de mapas nacionais. O Reino Unido é consi-
derado o país mais avançado na abertura de dados governamentais. O patrocínio
concedido ao Open Data Institute (ODI) tem sido importante, pois ambiciona
construir instrumentos para viabilizar projetos, tais como: i) Data and Platform
as a Service (DaPaaS); ii) Open Data Monitor; iii) Share-PSI (Public Sector Infor-
mation) – rede de promoção de políticas públicas sobre dados abertos. Para isso, o
instituto tem previsão de mensurar o impacto que os dados abertos podem causar
nos negócios e no valor agregado dos produtos. Ademais, o laboratório ODI vem
trabalhando na certificação de dados abertos e na implementação do consórcio
W3C, organização de padronização da rede mundial (world wide web).
A Fundação Dados Abertos (Open Data Foundation), organização não
lucrativa criada nos Estados Unidos, reúne diversas comunidades interessadas em
promover a adoção global da padronização de metadados (documentação com
informações sobre o processo de criação, qualidade e validade dos dados), passo
fundamental para a descrição, estruturação e preservação dos dados. Vale citar
outra iniciativa que visa ampliar o conceito de abertura de dados para promover o
conhecimento, dessa vez partindo de Cambridge, Inglaterra. Criada em 2004, a
Fundação para Conhecimento Aberto (Open Knowledge Foundation – OKF) conta
com filiais em nove países, inclusive o Brasil, além de participação de dezenas de
países-membros. A OKF promove o conhecimento livre com o objetivo de aper-
feiçoar políticas públicas sobre transparência, dados abertos e participação social.
O conceito de governo aberto ganhou força quando os agentes públicos co-
meçaram a disponibilizar dados para consulta e utilização dos cidadãos. A Parceria
para Governo Aberto (OGP – Open Government Partnership) é organização
internacional composta por representantes de governos, sociedade civil e setor
produtivo, para tratar inclusive de governança digital no contexto de evolução
tecnológica acelerada. A OGP representa governos de 78 países desde 2011, com
população que reúne cerca de 2 bilhões de pessoas e produção que corresponde à
cocriação de cerca de 4 mil reformas de governo aberto, repercutindo em impactos
significativos para as populações.
--- PAGE 11 ---
|
556 Do paradoxo das habilidades à superinteligência pós-humana
2.1.5 Governo e dados abertos no Brasil
O primeiro plano de ação nacional para governo aberto no Brasil foi instituído
em 2011, com 32 compromissos assumidos na parceria com o OGP. Desde en-
tão, os planos tiveram continuação ampliada, com horizonte de implementação
variando entre dois e quatro anos. A Controladoria Geral da União (CGU) é o
órgão responsável pela supervisão dos planos nacionais, cuja avaliação é elaborada
anualmente pelos próprios governos que assumiram a execução dos compromissos.
O Tribunal de Contas da União (TCU) também apoia o movimento, pois
considera que os cidadãos podem atuar no desenvolvimento de iniciativas que visem
fiscalizar e contribuir para a melhoria da gestão dos recursos públicos, especialmente
quando o objetivo é oferecer serviços de interesse relevante. Nesse sentido, em 2015,
o TCU elaborou relatório com cinco motivos para a abertura de dados governamen-
tais, quais sejam: transparência da gestão pública, contribuição social com serviços
inovadores, aprimoramento da qualidade dos dados governamentais, viabilização
de novos negócios e obrigação imposta por lei. A promoção da transparência visa
aumentar a disponibilidade de informações completas e atualizadas para serem pro-
cessadas, com apoio da Lei de Acesso à Informação (art. 8o, Lei no 12.527/2011),
que estabelece a obrigatoriedade do fornecimento de informações coletivas gerais.
2.1.6 Controle de dados individuais
O aperfeiçoamento das regras de privacidade e propriedade dos dados tem motivado
projetos internacionais que permitem a busca e o compartilhamento de informa-
ções com proteção dos usuários. Duas iniciativas merecem destaque. A primeira
estabelece padrões e formatos para dados e servidores, objetivando a guarda e uso
de dados pessoais interligados na internet. O Social Linked Data (Solid) é a de-
nominação do projeto de descentralização de aplicativos da web, liderado por Tim
Berners-Lee e pelo Instituto de Tecnologia de Massachusetts (MIT), que permite
o controle de dados pessoais, autorizações de acesso e armazenamento online por
parte dos próprios usuários. Os aplicativos autenticados pelo Solid podem solicitar
dados pessoais, mas estão sujeitos à permissão dos usuários.
A segunda iniciativa é o projeto The Hub of All Things (HAT) Microserver,
tecnologia avançada em nuvem que permite aos usuários serem proprietários dos
seus próprios microsservidores de dados pessoais. O sistema é centralizado em
nuvem, mas com portabilidade entre nuvens, conforme necessidade determinada
pelos usuários. Os clientes podem contratar legalmente os sítios e aplicativos de
rede aos quais desejem ter acesso, além de armazenar seus próprios dados nos
repositórios. O projeto conduzido por um conjunto de organizações tem licença
patenteada para transação entre infraestrutura de contas de dados pessoais, sendo
referenciado globalmente como solução emergente para guarda de dados pessoais
e infraestrutura privada.
--- PAGE 12 ---
|
Segmentos tecnológicos e aplicações 557
2.1.7 Analytics de bigdata
De acordo com Barocas, 2016 apud Burton et al. (2017), a análise de big data
por IA tem potencial para reproduzir padrões de comportamento pré-existentes
na sociedade, como por exemplo preconceito e discriminação, mesmo que esses
vieses ocorram de forma inteiramente involuntária. Isso acontece porque os efeitos
históricos dessas condutas foram incorporados pela sociedade desde longa data e
aparecem espelhados no conjunto de dados. A questão é considerada sutil pelos
autores, porque o entendimento geral é de que existe a neutralidade dos dados.
Porém, a comprovação desse fato pode ser obtida nos projetos de treinamento de IA
em linguagem natural, por exemplo, em que milhares de diálogos de redes sociais,
oferecidos como base de dados para treinamento do algoritmo, podem resultar
em diálogo indesejado por parte de assistente virtual. A seção 3 detalha o assunto.
Uma forma de abordar o problema é incluir a educação de ética em cursos
IA, de forma que os alunos sejam capazes de reconhecer as implicações das escolhas
que parecem ser eticamente neutras, mas que podem reproduzir involuntariamente
o viés estrutural e a desigualdade. Existe uma agenda de debates a respeito de in-
centivos e regulações para extração de valor de big data, especialmente relativas à
padronização e disponibilização de dados, visando mitigar condutas de amarração
ou captura de consumidores por empresas. Alguns elementos significativos pautam
essas discussões, tais como:
• estimativas para o potencial de adição de valor com uso de ML treinada
a partir de big data, avaliada em trilhões de dólares, devido ao volume,
velocidade e variedade de dados gerados em aplicações diversas;
• disponibilidade de big data por setor da economia, seja acadêmico, privado
e, particularmente, o setor público como maior gerador de informação a
partir dos dados sociais, além de correspondente criação de ecossistemas
favoráveis à aplicação de ML com controle de acesso para privacidade e
confidencialidade (The Royal Society, 2017); e
• utilização de dados abertos, acessíveis e prontos para processamento de big
data por ML, exigindo maior flexibilização para abertura nas especificações
de metadados com informações de origem, formas de obtenção, contextos
em que ocorreram as coletas e qualidade para utilização em máquina,
além de extensão do ciclo de vida dos dados (The Royal Society, 2017).
2.1.8 Big data no setor privado
Pesquisa produzida pela equipe do Instituto Global da McKinsey (Manyika et al.,
2011) sobre big data e a nova fronteira de inovação, competitividade e produtivi-
dade, chama atenção para a produção de dados gerados pelas empresas. Conside-
radas apenas como efeito colateral de outras atividades como vendas, aquisições,
--- PAGE 13 ---
|
558 Do paradoxo das habilidades à superinteligência pós-humana
comunicações, pesquisas etc., as bases definidas como big datas podem ser muito
relevantes em significância econômica. Variando de terabites (milhares de gigabi-
tes) até os múltiplos petabites (milhares de terabites), big datas têm potencial para
adicionar valor de US$ 300 bilhões anualmente ao sistema de saúde nos Estados
Unidos, apenas com a redução de 8% dos gastos. O estudo resultou em outras
constatações, quais sejam:
• os dados funcionam como importantes fatores de produção para a indústria
e os negócios, com armazenamento estimado em aproximadamente 13
EB, ao nível global tanto por empresas como por consumidores, em 2010,
e estimativas de dezenas de milhares de exabytes (1018 bites), em 2020;
• o uso de big data oferece oportunidade para criação de valor no plane-
jamento, organização e administração de companhias, transparência
entre os fornecedores da cadeia produtiva, customização de produção
por segmentos populacional, negócios produtos e serviços inovadores;
• a adoção de big data tem potencial para aumentar a competitividade e a
margem operacional das firmas em 60%, além de oferecer novas fontes de
agregação de valor tanto para novas categorias de companhias entrantes
quanto para indústrias estabelecidas;
• a utilização de big data deve provocar novas ondas de crescimento de
produtividade, com aumento de qualidade, produzindo mais com menos
custo, na manufatura, no varejo, nos cuidados com a saúde, no setor
público, entre outros;
• a comparação histórica da produtividade de diferentes setores mostra que
ganhos maiores com uso de big data são usufruídos pelo segmento de
informação, computação e produtos eletrônicos, seguido dos segmentos
de finanças, seguros e finanças; e
• a escassez de talentos é considerada obstáculo pelas companhias para tirar
vantagem de big data, já que profissionais com profundos conhecimentos
de estatística e de aprendizado de máquina são essenciais para gestão e
análise da operação das empresas.
Em 2010, a escassez de profissionais com habilidades analíticas avançadas era de
150 mil a 190 mil pessoas, enquanto para gerentes e analistas de big data era de 1,5
milhão de trabalhadores, somente nos Estados Unidos. O estudo ressaltou questões
pendentes de abordagem futuramente como a crescente importância da política
de dados, no que diz respeito à privacidade, segurança, direito de propriedade e
responsabilidade civil.
--- PAGE 14 ---
|
Segmentos tecnológicos e aplicações 559
2.2 IoT e IIoT
O termo internet of things foi cunhado por Kevin Ashton, fundador e então diretor
do consórcio de pesquisa, Auto-ID Center, sediado no MIT, em 1999. Kevin
também é conhecido por ter criado a etiqueta de identificação por radiofrequência
radio-frequency identification (RFID), sistema padronizado globalmente, além de
outros sensores. A IoT pode ser aplicada aos mais diversos setores, mas a automação
da indústria ganhou acrônimo específico (internet industrial das coisas – IIoT).
2.2.1 Conceitos
1) IoT – sistema composto de dispositivos de TICs, isto é, sensores (meca-
nismos de coleta de informações dos objetos no ambiente) e atuadores
(mecanismos de ativação, direcionamento e controle de dispositivos
no mundo real) interconectados por internet ou rede específica, para
transmissão de dados e informações, com consequente linha de ação
ou procedimento no espaço cibernético, resultante de processamento e
análise de informações com objetivos específicos.
2) Ciber-physical systems (sistemas ciber-físicos) – sistema em rede com sen-
sores, processadores e atuadores inteligentes incorporados, desenhados
para captação de estímulos e interação com o mundo físico (comunicação
entre máquinas e produtos), com o objetivo de apoiar a produção em
tempo real, visando ao aumento de desempenho.
3) Haptic interface (interface tátil) – baseia-se em atuadores e sensores centra-
dos em humanos, que combinam realidade virtual e aumentada com IoT
de final de rede, em que dados e informações são capturados do ambiente
contextual. As plataformas interativas possibilitam controle em tempo
real, por meio de experiências táteis (hápticas) captadas de contexto.
4) Edge/fog computing (computação de borda/ponta/fronteira) – onde
a computação em tempo real ocorre próxima à fonte de dados e ao
usuário. Isso exige maior precaução com a privacidade e segurança dos
dados sensíveis processados localmente, mas, em compensação, evita o
congestionamento pela transferência do fluxo completo de dados para
a nuvem de forma verticalizada. A tecnologia permite o incremento
de velocidade e diversificação do processamento em tempo real, sendo
requisito indispensável para o bom funcionamento da IoT. O acúmulo
de dados em repositório central é feito conforme o grau de criticidade
das informações, atribuído pela IA.
--- PAGE 15 ---
|
560 Do paradoxo das habilidades à superinteligência pós-humana
5) Reference architecture IoT (arquitetura de referência IoT) – forma de
estruturar, administrar e padronizar a implementação de sistema IoT,
em que quantidade explosiva de dados é gerada pelos dispositivos táteis,
contando com a técnica de IA de pré-processamento. A arquitetura IoT
padrão permite a modelagem de componentes de hardware/software,
construção e operação de sistemas de grande escala, sendo gerenciada
de forma segura em quatro camadas:
a) dispositivos/gateways – compostos de sensores, atuadores, processa-
dores e memórias, além das portas de entrada/saída (hardware ou
software) para conexão entre diferentes tecnologias em rede IoT;
b) rede de comunicação – protocolos de endereçamento de dados/in-
formações;
c) suporte a serviços – provimento de funções de processamento, ge-
ralmente em nuvem, que permite a interoperabilidade de interfaces
de programação de aplicações (API) entre comunicação em rede,
dispositivos/gateways e aplicações; e
d) aplicativos e aplicações – programações e softwares projetados para
dispositivos móveis (tablets e smartphones) e fixos (desktops e servi-
dores), com funcionalidades diversas de monitoramento, controles
de automação, edição de imagens etc.
Classificação das IoTs de acordo com a estrutura de camadas: i) aplicação –
sistemas para gestão de processos para serviços ou para clientes; ii) armazenamento
e tratamento de dados – big data analytics, IA/ML/DL, nuvem, virtualização de
sensores, segurança e privacidade de software; iii) portas de computação de borda
(gateways de edge ou fog) – concentração de dados de sensores e atuadores de rede
(coleta, tratamento e processamento), interconexão de sistemas legados com pa-
drão de rede pública, automação parcial de serviços imediatos que exijam baixa
latência; iv) redes de comunicação – por rádio ou cabos de fibra ótica e metálicos,
programadas por software ou arquiteturas autoorganizadas, virtualizadas e seguras;
v) dispositivos, sensores e atuadores – dedicados à coleta de dados, informações e
ação de objetos nanotecnológicos, biotecnológicos, fotônicos etc.; e vi) transversal
de segurança da informação – garantem a privacidade e segurança no envio de
dados por todas as camadas (IEL, 2017, p. 36).
2.2.2 Tendências futuras
A série de livros acadêmicos e profissionais especializados em redes de comunica-
ções, compilada por River Editores, aborda as tendências de progressão da nova
geração de IoT/IIoT e conta com o apoio da Comissão Europeia – CE (Vermesan
e Bacquet, 2018). As tecnologias e aplicações continuam evoluindo e incluem a
--- PAGE 16 ---
|
Segmentos tecnológicos e aplicações 561
incorporação de hiperconectividade ubíqua (5G e 6G), computação de borda, blo-
ckchain/DLT e IA. A conexão entre os mundos físico, virtual, digital e cibernético
realizada por meio de gêmeos digitais inteligentes tem gerado fluxos constantes de
dados passíveis de serem coletados e utilizados, visando tornar a vida mais prática.
A IoT/IIoT tátil que permite a interação homem-máquina em tempo real implica
transferência remota dos cinco sentidos fisiológicos e introdução de novo paradig-
ma da comunicação com base no conhecimento e na habilidade. A complexidade
desse ambiente requer o desenvolvimento de soluções de segurança inovadoras.
As principais tendências de desenvolvimento futuro de IoT referem-se: i) ao
crescente aumento do volume de dados gerados; ii) à obtenção de conhecimento a
partir das ferramentas de análises (data analytics); e iii) ao incremento do nível de
automação para tomada de decisão. Ao emprego de sensores inteligentes, atuadores,
IA e ML, acrescenta-se a necessidade de requisitos emergentes de processamento
em tempo real. Para isso, a capacidade das redes de comunicação tem aumentado
exponencialmente com a tecnologia 5G, permitindo o uso de novas arquiteturas
de distribuição e soluções, como nova geração de dispositivos IoT, computação de
borda, central de dados ou nuvem cognitiva, modelos de negócios orientados para
comunidades e infraestrutura resiliente e confiável (Vermesan e Bacquet, 2018).
1) Nova geração de dispositivos IoT – a próxima fase dos dispositivos será
caracterizada pela interface tátil com base no sensoriamento e acionamento
centrado no humano, realidade virtual e aumentada combinados com a
nova IoT de desfecho ou de ponto terminal (end-point IoT), isto é, com
capacidade de captar e contextualizar o ambiente. Essas plataformas IoT
conversacionais e interativas com interfaces inovativas entre humanos e
coisas permitirão o controle em tempo real, experiências físicas, interação,
sensibilidade ao contexto e serviços orientados por eventos com mais
inteligência na ponta. De forma a garantir segurança e confiabilidade,
o fluxo de informação permanecerá perto ao usuário e as decisões serão
tomadas no ponto de interesse, em que os dados são coletados e pro-
cessados. Sendo assim, as aplicações precisam combinar computação de
borda, IoT e sistemas autônomos móveis, utilizando tecnologias IA para
ativar funcionalidades.
2) Computação de borda – computação e processamento de dados em proxi-
midade com a fonte de dados. O procedimento usual, na maior parte das
soluções IoT, é executar o processamento de dados na nuvem, especialmente
apropriado em casos nos quais a coleta de dados é de natureza distribuída.
Porém, o valor obtido por muitos dispositivos geradores de bilhões de dados
diminui com o tempo, sendo aconselhável limitar o estoque de informa-
ções para evitar a avalanche de dados. Nesses casos, o processamento em
borda é preferível, requerendo maior capacidade computacional no portão
--- PAGE 17 ---
|
562 Do paradoxo das habilidades à superinteligência pós-humana
do dispositivo para atender aos requisitos em tempo real, preservando a
privacidade e reduzindo ataques de superfície contra os dispositivos IoT.
3) Arquiteturas centradas em dados – o volume sem precedentes de dados
gerados por diversos campos de aplicação exigirá o uso de técnicas IA
para pré-processamento. O estoque e o fluxo de dados tendem a estressar
a capacidade das plataformas IoT, devido ao grande número de objetos
e dispositivos conectados. A soberania sobre os dados, regulada pelos
países-fonte de obtenção dos dados, envolve questões de privacidade,
segurança e comercialização. A aplicação de IA em toda IoT terá sua
origem na nuvem, mas o emprego será ao nível de borda, embutida em
portões de dispositivos sujeitos a restrições de tempo. Funções críticas
terão que ser replicadas e delegadas a agentes locais, que assegurem o
funcionamento do sistema mesmo que seja offline. Os agentes digitais
equipados com IA-serviço poderão agir em benefício dos usuários, in-
teragindo com os sensores e acessando dados relacionados à atividade
do usuário e atuando de forma autônoma e proativa.
4) Modelagem de negócios orientada para comunidades – garantindo priva-
cidade e segurança por meio de DLT. Os novos negócios e serviços cons-
truídos em redes sociais estão associados com necessidades da vida diária,
como mobilidade, compras, cuidados médicos ligados a edifícios, bairros e
cidades. Os espaços de comercialização, marketplaces, ponto a ponto (P2P)
são orientados por interesses comuns e valores compartilhados. Tecnologias
de autenticação, autorização e contabilidade deverão evoluir de plataformas
isoladas para ecossistemas de plataformas conectadas. As DLTs viabilizam
as transações máquina a máquina (M2M) sem provedor centralizado.
5) Infraestrutura confiável e inteligente – conectividade de dispositivos,
transmissão de dados e segurança serão requisitos para a infraestrutura
de IoT confiável e de qualidade. A governança e segurança de dados des-
centralizados será possível pelas arquiteturas distribuídas utilizando DLT,
em que o controle de dados pessoais é aperfeiçoado significativamente. A
plataforma DLT confiável requererá protocolo escalável, infraestrutura de
desempenho e governança compartilhada. O tratamento dado ao tráfego
em IoT deverá ser estudado para viabilizar o uso resiliente dos dados.
6) As estimativas para o crescimento anual de dados criados, replicados e
consumidos – são de 1.200 EB, em 2010, para 40.000 EB (1018 bytes),
em 2020 (Vermesan e Bacquet, 2018). A segurança e a privacidade dos
dados armazenados estão sujeitas à disponibilidade dos usuários e das
corporações regulares, conforme o mercado de dados, assim como à
soberania da legislação do local onde esses dados são coletados.
--- PAGE 18 ---
|
Segmentos tecnológicos e aplicações 563
7) A quantidade de dispositivos ativos conectados em IoT – deve passar de 10
bilhões, em 2020, para 22 bilhões, em 2025 (Vermesan e Bacquet, 2018,
p. 89). Outras estimativas (Projeto Chariot) avaliam que o número de
dispositivos conectados a IoT pode chegar a 75 bilhões, em 2025, em todo
o mundo, enquanto os gastos em serviços e dispositivos corresponderam
a US$ 2 trilhões, em 2017, sendo China, América do Norte e Europa
Ocidental responsáveis por 67% desses dispositivos (op. cit., p. 222).
8) Estimativas da quantidade de dispositivos ligados a IoT em 2020 – as
projeções variam conforme a metodologia de cálculo empregada e a
abrangência do escopo (por exemplo, veículos, residências e indústrias).
Os números mais frequentes vão de 10 a 30 bilhões de dispositivos
conectados, até 2020, mas as previsões otimistas chegaram a sinalizar
para 50 bilhões, cerca de cinco vezes a população mundial prevista
para 2050. A confiança é justificada porque o custo dos dispositivos de
sensoriamento IoT vêm caindo sistematicamente, devido à ampliação
no número de fornecedores, à otimização do consumo de energia e ao
aprimoramento tecnológico. O custo médio do sensor teria passado de
US$ 1,30 em 2004, para US$ 0,60 em 2014, e chegaria a US$ 0,38 em
2020, segundo Goldman Sachs3 e a BI Intelligence Estimates.
FIGURA 2
Evolução histórica da tecnologia IoT
Em 2018, Terceira Onda de Pesquisa e Inovação IoT.
Desenvolvimento de Ecossistemas IoT.
Evolução de Arquitetura IoT.
Centralizada > Descentralizada > Distribuída.
4. IoT Inteligente em Rede
Autônoma
Arquitetura heterogênea
distribuída. Federação de plataformas descentralizadas em
3. Evolução de Internet das dispositivos (devices), portões
Coisas (IoT) (gateways), periferia/ponta
Plataformas IoT, soluções de (edges) e nuvem (clouds).
segurança centralizadas, coleta Integração à tecnologia de livro de dados centralizada e razão (ledger) distribuída. Iot 1 S d u p c c u e e e . i n r n s o n n n i i p D d d g t t s o r r o a a i r a a s s a r d d p i l l e t m i e e o z d i s v s a a s e c e o d i o d ç t s p ã e a m o i v r i o t . n p o o p u C l t r c s u a ó e o a e d t C g r b c s a c o s o e i e c c o a r s n i a a e n m o s e m . a s e n c e C m c c e a t n o t a a n l e a t n b d c t n o d t e o e o t . o r a n o s o S à s d t o l e e r c e u o f m a o t s n l m w i i e z R d a a e a r d d d e a e e . n s a 2 A S r s fi a e m a s d d e i o o . u s m a i l o r o f d o p m q f R t d . e b t n e s ô a u e o S r w , . i i . c n e d i e e t s p R i t D a o o n a e n n e r a F r m r a l t e s s t t I t e m a e d o a u d s r d s o m s s a i e r r o e s e ã s r e a v p n s e o s o ( s é d S o e t i e n a d ) . s e e i e n , n e s t r s a d t n o m t r t . s a p n r , r e o o a s u t a i d d a t o p s b r u e t r r z o a e r a u u e a a e m e e s s s d d í r e n s s l d a c e p o o e m a a o o s d m e c r n d e p n s a a e r n d m o a a d l r s ó e o i e t s r i z u ç m a s d F a e o õ r i d e ç m o s a e r õ e , e s s e e d s m e . . p S h v v n i e I o E n n o i i m c r a e p r r t f o o f r t t g e e o e t d u u c s ó r r w r s r e w a a a n m c i g s s a l l ç i e s a i , e t o a ã r z a t e r c n e s ç o a . m e i m ã t b ç I e d . e t o o ã e e a e C e . c X o n r c t o s o C I n e t e n o a f n n o o é c o c í T I s v s n n t o o . l u c i e i ó v c o T p c e m r a e g a I l n l g n ó a , r , i i t ê g c d d g d d i r a n n ê o a u i o i c g c d n l r ( s a i i O d i t u c z a t v r i e ( a a s T i a i T d a t a d l ) r , I e d l ) o i . a e e . l , t o i s d M M E n á i s p e s t á á c t t e e r t e I q q o u i r l m l u u i a . T t g i i ç u C a n n ê ã i r s n o n a a o a t n a , - c e C p e i u v M a l y o e t i i g b á ô n a r lí g e e q r n f t t o i n r ê u o c i - n r fi t i a I m n m e n c c c a f i o i H a a o a r - s a ç M l u m , t , ã e e m c s á o p s o c i t q , a s a n l r t a u n r u o e t b i o t l m i n ó u o l - h a g a r r a , a a s i c d . ç d a ã a e o . d e
1940 2000 2010 2020 2030
otnemicehnoC
rop
adigiriD
edadicapaC
e ToI otnemicehnoC e oãçargetnI ed levíN
Nível
de Inteligência
Fonte: Vermesan e Bacquet (2018).
Obs.: 1. Dispositivos conectados por rede de cabos (controle centralizado).
2. Redes sem cabo de sensores (arquitetura de rede).
3. Evolução da IoT (plataformas centralizam processamento de dados).
4. Rede IoT autônoma e inteligente (arquitetura heterogênea distribuída e plataformas descentralizadas).
3. Para mais informações, acessar: https://www.iofficecorp.com/blog/cost-of-iot-sensors.
--- PAGE 19 ---
|
564 Do paradoxo das habilidades à superinteligência pós-humana
A nova geração de IoT faz uso de IA, DLTs e redes de comunicação avançadas
para projetar arquitetura, plataformas e soluções, cuja qualidade dos serviços e
desempenho são viabilizados por meio de dispositivos de interface tática da com-
putação de borda. A infraestrutura resiliente e confiável dá suporte para conexão
dos dispositivos e transmissão de dados na IoT que, além de requerer protocolo de
segurança escalável, necessita governança compartilhada para assegurar a veracidade
e confiabilidade de dados, ou seja, DLTs.
A fonte dos aplicativos IA/ML está na nuvem, apesar do emprego ocorrer
ao nível local por toda a rede IoT, para evitar restrições de capacidade e tempo.
Eventualmente, funções críticas são delegadas a agentes locais para ação autôno-
ma e independente, permitindo o funcionamento do sistema mesmo offline. O
tratamento do tráfego de comunicação na IoT é fator decisivo para viabilização
da resiliência e utilização dos dados. A interoperabilidade em múltiplos níveis é
especialmente crítica em ambientes complexos com dispositivos físicos heterogê-
neos, como aqueles encontrados nas diferentes IoTs. A utilização de variada gama
de interfaces e representações permite a interação com um simples dispositivo em
rede, de forma a acionar a funcionalidade característica de cada nível de interope-
rabilidade sintática da camada.
Os modelos de negócio dirigidos para a comunidade fornecem serviços sociais
rotineiros de economia de mercado compartilhado em plataforma P2P, construídos
a partir de redes e assegurados pelas DLTs, como transporte, compras e cuidados
domésticos associados a edifícios, quarteirões, bairros e, até mesmo, cidades. Os
ecossistemas de plataformas conectadas blockchain/DLTs permitem transações
M2M sem intervenção de plataforma provedora central, autenticação de origem
para uso de interessados específicos, administração e revenda de dados pessoais,
além de serem a promessa para a geração futura de ambientes IoTs descentralizados.
A Iniciativa Europeia para Plataformas IoT,4 tem como proposta criar um
ecossistema favorável ao desenvolvimento sustentado de IoT no continente, ma-
ximizando oportunidades para construção de plataformas abertas e compartilha-
mento de informações com interoperabilidade. O núcleo do programa prioriza sete
projetos de pesquisa e inovação (Inter-IoT, BIG IoT, Agile, SymbIoTe, TagItSmart,
Vicinity, bIoTope), além de ações de suporte e coordenação (Unify-IoT), com foco
em soluções de arquitetura e interoperabilidade semântica, contando com recursos
de € 50 milhões e 120 organizações parceiras, distribuídos a partir de 2018.
Exemplificando, o projeto Inter-IoT provê estrutura aberta entre as camadas,
com ferramentas que possibilitam a interoperabilidade entre plataformas de IoTs he-
terogêneas. Contudo, inexiste proposta que apresente uma abordagem metodológica
4. Em inglês, IoT-European Plataforms Initiative (IoT-EPI).
--- PAGE 20 ---
|
Segmentos tecnológicos e aplicações 565
geral, sistemática e reutilizável para resolver os múltiplos problemas de interoperabi-
lidade das tecnologias nas plataformas IoT. A interoperabilidade camada a camada
envolve diversas soluções endereçadas a cada plano, tipo: dispositivo a dispositivo,
rede a rede, middleware5 a middleware, aplicativo e serviço a aplicativo e serviço,
bem como dado e semântica a dado e semântica, além de interfaces entre camadas
adjacentes controladas por estruturas de meta-nível.
Outros programas tratam de questões-chave de segurança ponta a ponta,
prevenção de ataques cibernéticos, confiabilidade e privacidade em plataformas
abertas (Enact, IoTCrawler, SecureIoT, Brain-IoT, Sofie, Chariot, SEMIoTICS,
SerIoT), com recursos de € 37 milhões. Em projetos-piloto de larga escala em zonas
urbanas, veículos autônomos, vestíveis, fazendas e segurança alimentar, ambientes e
envelhecimento, foram destinados € 100 milhões para o programa IoT-LSP (Large
Scale Pilots: ActiVage, SynchroniCity, U4IoT, Create-Iot, AutoPilot, IoF2020 e
Monica), em 2017. Para transformação e digitalização da indústria (Connect,
Grow, RTD, Agri, Ener), foram direcionados € 250 milhões, entre 2018 e 2020.
O fundo de suporte à digitalização da União Europeia (UE), incluindo tecnologias
e capacitação, prevê investimentos de € 9 bilhões.
Exemplificando, o projeto IoTCrawler proporciona abordagens inovativas
de apoio à estruturação de sistemas interoperáveis em rede IoT de forma a ga-
rantir a privacidade e segurança. Plataformas sofisticadas IoT deverão evoluir de
forma a possibilitar benefícios mais completos. Em vez de uma hiperplataforma,
a IoTCrawler é por definição uma interface comum de cooperação entre diversas
plataformas, para obtenção de dados, informações e serviços por meio de redes
IoT e sistemas internos ao ecossistema integrado de recursos de IoT, facilitando
o acesso aos serviços e dados para as demais plataformas. A IoT Crawler divide-se
em dois planos de controle (coordenação de plataformas) e de dados (fluxo de
intercâmbio entre as plataformas).
2.2.3 Implementação acelerada de IoT
O estudo do Fórum Econômico Mundial, em parceria com a empresa Accentu-
re, sobre IIoT deixou recomendações para acelerar o desenvolvimento global da
internet industrial, aproveitando as oportunidades de curto prazo e capitalizando
as mudanças estruturais de longo prazo: i) os provedores de tecnologia devem
inventariar e compartilhar as boas práticas de segurança; ii) a adesão à tecnologia
deve ser precedida de estratégia de reorientação de negócios para obter as vantagens
dos últimos avanços da IIoT; iii) os gestores públicos têm de reexaminar e atualizar
a proteção de dados e política de responsabilidade para transmissão de fluxo de
5. Middleware, camada-meio homogeneizadora, constituída de softwares cuja funcionalidade é servir de ponte tradutora
entre diferentes protocolos de comunicação de hardwares e softwares.
--- PAGE 21 ---
|
566 Do paradoxo das habilidades à superinteligência pós-humana
dados transfronteiriço, além de revisar a regulação de incentivo aos investimentos;
e iv) os interessados devem colaborar em pesquisa e desenvolvimento (P&D) de
longo prazo, pois a tecnologia apresenta desafios de segurança, gestão de risco e
interoperabilidade significativos (WEF e Accenture, 2015).
Como descrito no capítulo 3, o Brasil tem buscado vencer os obstáculos que
determinam a defasagem tecnológica e impedem a modernização da indústria
nacional. Para isso, o BNDES (2017), em parceria com o Ministério da Ciência,
Tecnologia e Inovações (MCTI) e o Ministério do Planejamento, Orçamento e
Gestão (MPOG), apoiou a realização de estudos de diagnóstico para subsidiar a
proposição de plano estratégico e de ação para implantação de IoT no país. O
produto denominado Roadmap Tecnológico buscou mapear cadeias produtivas
com potencial para aplicação da tecnologia e teve o apoio de dezenove Instituições
Científicas e Tecnológicas (ICTs). O relatório traz as atividades realizadas na cadeia
de valor para IoT, conforme descritas no quadro 2.
QUADRO 2
Caracterização de cadeias de valor IoT
Elos da cadeia de valor Descrição de produtos Tendências Aplicações
Componentes do tipo microcontro-
Segurança (by design) com cripto-
ladores, sensores, processadores, Rastreamento de contêineres
Módulos inteligentes grafia leve e suporte complementar
atuadores, memórias, modens de carga.
de gateways.
e baterias.
Armazenamento de energia, senso-
Protocolo IPv6 para os elementos Para tratamento unívoco de veículos
Objetos inteligentes riamento, atuação, processamento,
conectados à internet. autônomos compartilhados.
cognição e gestão remota.
Adoção de variantes do protocolo
de internet (IP) com criptografia
de chave pública, como: Datagram
Equipamentos de infraestrutura Casa inteligente e interoperabilidade
Conectividade Transport Layer Security(DTLS),
de comunicação de dados. entre eletrodomésticos.
HIP – host identity protocol Diet
Exchange (DEX) e Internet Key
Exchange versão 2 (IKEv2).
Desenvolvimento de sistemas de
Desenvolvimento de middleware Irrigação inteligente para áreas
suporte para coleta, armazenamento,
para armazenamento de dados, rurais, com desenvolvimento
Habilitador tratamento e processamento de
instalado localmente e especializado de aplicativos para controle de
dados, analytics/business intelligence
conforme utilização. aspersores de água.
(BI) e visualização/virtualização.
Sistemas adaptados às regras
de negócios das empresas,
Integrador implementados por APIs, visan-
do à integração de processos Blockchain de IoT, confidenciali-
e módulos. dade, autenticidade, integridade Manutenção preditiva de turbina
de aviões.
Solução de software, hardware e disponibilidade.
e conectividade, fornecidos em
Provedor de serviços
pacotes IoT, visando atender à
necessidade do cliente.
Fonte: BNDES (2017).
Elaboração da autora.
--- PAGE 22 ---
|
Segmentos tecnológicos e aplicações 567
Cada elo da cadeia de valor da IoT apresentada no quadro tem dimensões
embebidas de P&D, design e testagem de produto que antecedem a fabricação em
escala de dispositivos físicos ou a incorporação de softwares à prática das empresas.
A implantação da IoT, propriamente dita, segue-se em pelo menos três fases, com
algumas implicações a serem observadas, segundo os especialistas.
1) Primeira fase – conectar 85% dos dispositivos sem conexão através da
rede. Os dados gerados podem ser coletados, normalizados, analisados.
Na nuvem, os dados podem ser processados, armazenados, aumentados,
visualizados e integrados em formato aberto, seguro e escalável.
2) Segunda fase – criar conexões para 20 bilhões de dispositivos inteligentes
em escala global, em 2020, implica comunicar somente informações-chave
necessárias para o servidor. A quantidade e o momento adequado para
envio dos dados são questões decisivas para o bom funcionamento dos
sistemas, sendo avaliadas pelos gestores e operadores desses.
3) Terceira fase – construir softwares definidos como autônomos, com
utilização de ML, em que o processo de decisão passa a ser do próprio
sistema. Veículos equipados com sensores, câmeras e radares exigem
considerável esforço de computação e conexão à nuvem.
2.2.4 IoT e patentes
Os altos custos envolvendo armazenamento de big data têm levado as empresas
de tecnologia a desenvolverem metodologias mais eficientes para entregar conte-
údos, como é o caso de lago de dados (data lakes) e do fluxo de dados (dataflow).
Os datalakes foram pensados para superar algumas limitações do data warehouses
(armazéns de dados), esses últimos sendo organizados rígida e hierarquicamente
(schema on write) para utilização no gerenciamento de BI de alta performance, mas
bastante dispendiosos. O datalake foi projetado para armazenar grande quantidade
de dados brutos, sendo esses estruturados, semiestruturados ou não estruturados,
sem antecipar uma organização formal (schema on read). A identificação para re-
cuperação e utilização desses é feita por meio de metadados. Quanto ao dataflow
(processamento em fluxo ou programação reativa) foi pensado para desligar dispo-
sitivos em estágios (pipelines), mas que pudessem ser processados simultaneamente.
O processamento paralelo aumenta a eficiência no uso dos recursos computacionais
disponíveis e evita gargalos que prejudicam o desempenho.
Outra forma de antecipar as tendências futuras de desenvolvimento da tecno-
logia IoT é por meio do monitoramento de pedidos de patentes e publicações. Esses
podem sinalizar para altas na procura pelo direito de PI e na produção de P&D
associadas às inovações que buscam superar desafios, como os descritos anteriormente.
--- PAGE 23 ---
|
568 Do paradoxo das habilidades à superinteligência pós-humana
Neste aspecto, os destaques para pedidos de patentes no tema IoT foram apre-
sentados na tabela 1, como: i) dados de streaming e aprendizagem de máquina, na
comunicação orientada à conexão, os dados são gerados continuamente e processados
gradualmente online, o chamado processamento seletivo de fluxo que faz inferências
a partir de aprendizado de máquina utilizando dados mutantes no tempo, o que
implica grande volatilidade nos resultados da variável-alvo que se pretende prever;
ii) dados streaming e mineração, processos de extração de estruturas de conhecimen-
to a partir de fluxo contínuo de registros de dados, com objetivo de inferir classes,
instâncias e padrões desses, utilizando recursos computacionais mínimos; e iii) real
time machine learning, que se refere ao treinamento contínuo do aprendizado de
máquina, visando ao aperfeiçoamento de previsões realizadas pelo algoritmo IA à
medida que novos dados são gerados. De 2010 a 2015, a quantidade de depósito de
patentes relacionada a dados mostra a tentativa de aprimorar resultados e diminuir
incertezas de fontes díspares, obedecendo ao ordenamento segundo a demanda: fusão
de dados (602), integração de dados (442), detecção de anomalias (168), coleta e
abstração de dados (128).
Continuando com os destaques de concessões patentes tem-se o tema rela-
cionado a sistemas de visão e reconhecimento de imagem computacional, cujo
processamento de imagem (2.919) concentra maior número de patentes do que a
visão computacional (200), objetivando o aperfeiçoamento da imagem para extra-
ção de informações, por isso concentrando maiores esforços dos desenvolvedores.
Os métodos de visão computacional buscam emular a visão humana que analisa,
interpreta conteúdos e compreende as informações relevantes inseridas na imagem.
Com relação à representação do conhecimento, as ontologias (249) conceituam
e relacionam domínios nas aplicações, concentrando maior número de patentes
do que aplicações de rede semânticas ou semântica em rede (59), que faz a relação
entre a linguagem e a realidade. As patentes de experiência de usuário (user expe-
rience – UX = 2.378), ramo tecnológico a jusante da linha de produção, são três
vezes maiores do que as tecnologias de realidades aumentada (299) e virtual (411)
juntas, a montante da cadeia de produção, evidenciando o efeito multiplicador do
segmento tecnológico. Em 2015, o número de patentes em drones foi dez vezes
maior do que de veículos autônomos, enquanto as patentes de casa inteligente (smart
home) eram quatro vezes maiores do que patentes de rede inteligente (smart grid).
Para ambos os casos, os segmentos mais próximos à demanda mantêm a procura
de pedidos de patentes de produtos em alta por serem de mais fácil comerciali-
zação. O efeito contrário se dá nos ramos industriais, onde a menor procura por
patentes está associada ao segredo industrial que implica em aumento prolongado
de valor agregado. Em 2013, o destaque ficou por conta tecnologias de inteligência
distribuída, seguida de blockchain, multiagente e inteligência de enxame.
--- PAGE 24 ---
|
Segmentos tecnológicos e aplicações 569
TABELA 1
Patentes IPC1 (2010-2016) e publicações relacionadas a IoT (após 2009)
Patentes Publicações
Categoria Tecnologias
IoT Total % IoT Total %
Smart city
881 4.414 19,96
Cidade inteligente
Smart home
5.326 25.074 21,24 695 4.724 14,71
Casa inteligente
Smart grid
1.320 21.956 6,01 428 20.254 2,11
Rede inteligente
Traffic
9.302 1.000.386 0,93 24 3.583 0,67
Tráfego
Aplicações
Tracking
6.000 593.326 1,01 674 184.956 0,36
Rastreamento
Autonomous vehicle
39 3.839 1,02 1.207 163.578 0,74
Veículos autônomos
Drones
174 6.818 2,55 45 3.043 1,48
Vant
Wearables
4.088 101.922 4,01 656 19.080 3,44
Vestíveis
UX
2.378 178.301 1,33 154 14.489 1,06
Experiência do usuário
Augmented reality
299 18.534 1,61 112 9.927 1,13
Realidade aumentada
Virtual reality
Interface usuário 411 31.829 1,29 295 40.468 0,73
Realidade virtual
Image processing
2.919 550.249 0,53 142 136.037 0,10
Processamento de imagem
Computer vision
200 31.595 0,63 72 45.592 0,16
Visão computacional
GIS
324 13.817 2,34 62 27.257 0,23
Sistema de Informação Georreferenciada
Sistemas de Informa-
ção Geográfica (GIS) Geoprocessamento 1 52 1,92
Georreferenciamento 2 445 0,45
(Continua)
--- PAGE 25 ---
|
570 Do paradoxo das habilidades à superinteligência pós-humana
(Continuação)
Patentes Publicações
Categoria Tecnologias
IoT Total % IoT Total %
Data mining
432 69.143 0,62
Mineração de dados
Data collection e abstraction
128 3.901 3,28
Coleta e abstração de dados
Data integration
442 14.784 2,99 101 8.047 1,26
Integração de dados
Data fusion
602 14.537 4,14 116 10.663 1,09
Fusão de dados
Analytics
766 36.361 2,11 559 18.313 3,05
Análise
Anomaly dectection
Dados e analytics 168 7.782 2,16 79 6.515 1,21
Detecção de anomalia
Social network analysis
11 1.033 1,06 20 7.542 0,27
Análise de rede social
Statistical model
168 15.811 1,06
Modelo estatístico
Probabilist reasoning
5 2.416 0,21
Raciocínio probabilístico
Streaming e data mining
24 508 4,72
Transmissão e mineração de dados
Streaming data
50 1.549 3,23
Transmissão de dados
(Continua)
--- PAGE 26 ---
|
Segmentos tecnológicos e aplicações 571
(Continuação)
Patentes Publicações
Categoria Tecnologias
IoT Total % IoT Total %
Streaming e ML
47 505 9,31
Transmissão e aprendizado de máquina
DL
73 1.957 3,73 44 5.448 0,81
Aprendizado profundo
ML
688 43.550 1,58 243 51.550 0,47
Aprendizado de máquina
Reinforced learning
32 2.235 1,43 31 8.565 0,36
Aprendizado por reforço
Online learning
15 2.416 0,62 7 9.105 0,08
Aprendizado online
Real time ML
3 29 10,34
ML em tempo real
Natural language generation
4 390 1,03
Geração de linguagem natural
Natural language understanding
20 1.869 1,07
Compreensão de linguagem natural
Natural language recognition
12.706 0,83 36 19.190 0,19
Reconhecimento de linguagem natural 105
IA
Automatic speech recognition
34 5.405 0,63
Reconhecimento de fala automático
Semantic web
59 2744 2,15 468 18.684 2,50
Rede semântica
Virtual assistent
10 558 1,79
Assistente virtual
Recommendation system
71 6.873 1,03 30 4.573 0,66
Sistema de recomendação
Recommendation engine
40 3.342 1,20
Motor de recomendação
Swarm intelligence
32 1.220 2,62 14 4.665 0,30
Inteligência de enxame
Swarm robotics
4 699 0,57
Robótica de enxame
Multi agent
38 3.374 1,13 187 30.250 0,62
Multiagente
Distributed intelligence
95 1.193 7,96 25 253 9,88
Inteligência distribuída
Ontology
249 19.843 1,25 466 52.649 0,89
Ontologia
Estruturação
Blockchain
24 237 10,13 14 187 7,49
Contabilidade distribuída
Fontes: BNDES (2017), Consórcio IoT, bases de dados da Orbit Quest e da Scopus.
Elaboração da autora.
Nota: 1 Classificação Internacional de Patentes (International Patent Classification).
--- PAGE 27 ---
|
572 Do paradoxo das habilidades à superinteligência pós-humana
2.3 Computação em nuvem (cloud) e centrais de dados (datacenters)
Transições significativas estão em andamento no ambiente digital, com migração
de ativos, processos e dados para nuvem computacional. A oferta de serviços,
informações e infraestrutura nessas plataformas é mais generosa, facilitando a
implementação do processo de modernização nas empresas.
2.3.1 Conceitos
1) Computação em nuvem – associação de infraestrutura (datacenters/
hardwares) e de desenvolvedores (softwares), acessada via rede e inter-
net, com garantia de nível de serviço e pagamento sob demanda. O
fornecimento do serviço remoto compartilha recursos de computação
configuráveis em módulos escaláveis, tais como: servidores e capacidade
de processamento, repositórios e capacidade de armazenamento, softwares,
aplicações e serviços que podem ser rapidamente disponibilizados no
modelo proprietário ou por contratação de provedor de serviços com
melhor relação benefício/custo.
2) Técnicas de virtualização – softwares que propiciam o desenvolvimento de
aplicações que compartilham a infraestrutura de hardware, por represen-
tação virtual de aplicativos, servidores, redes e repositórios, viabilizando
os serviços em nuvem.
3) Middleware em nuvem – software cuja funcionalidade é intermediar ser-
viços entre a camada de aplicação e a camada operacional do sistema. Os
middlewares podem ser de servidor de aplicativos, mensagem de e-mail,
gerenciamento de dados e APIs, autenticação, sendo apropriados para
ambientes multicloud (múltiplos servidores em nuvem).
4) Datacenter – infraestrutura de grande, médio ou pequeno porte, que
permite a hospedagem e execução de aplicações remotamente, com
computação de alto desempenho, além de fornecer os meios para arma-
zenagem de dados.
5) Computação de alto desempenho – apropriada para execução de tarefas
complexas que geralmente exigem processamento de supercomputadores
ou conjunto de computadores, implicando maior capacidade de toda
infraestrutura associada.
6) Computação em malha (mesh) – arquitetura em rede de conjunto de
dispositivos e servidores conectados de forma redundante com diversos
caminhos entre os nós, intermediados por roteadores organizados em
grandes redes a partir de redes locais com interligações P2P. A comple-
xidade da rede mesh implica elevado custo de interligação.
--- PAGE 28 ---
|
Segmentos tecnológicos e aplicações 573
7) Computação em grade (grid) – combinação de centenas e até milhares de
pequenos computadores em redes locais e regionais, para processamento
de grandes rotinas, em que aplicações estão disponíveis a partir de servidor
virtual que interliga todos os recursos em rede, cujos serviços geralmente
são cobrados por taxas fixas.
2.3.2 Vantagens do processamento em nuvem
A computação em nuvem constitui-se de sistemas de TIC para processamento de
dados em servidores remotos, disponibilizado pela internet, geralmente alugados sob
demanda, por tempo determinado ou custo fixo. Dependendo do tipo de usuário,
a nuvem pode ser classificada como pública (instalada em provedor sem localização
fixa e aberta ao público), privada (exclusiva da organização em localização específica),
comunitária (conjunto de organizações com interesses comuns, com localização
pulverizada, mas determinada) e híbrida (combinação de tipos de instalações).
A ideia de criar grandes centros de processamento e armazenamento de dados
sempre esteve associada à fragilidade, em termos de segurança cibernética. Contudo,
é consenso sua vantagem inegável de preservação de acervo (arquivos de dados e
de processamento) em caso de pane local, de viabilização de uso de determinados
softwares por período certo e de infraestrutura a custos relativamente baixos. O
compartilhamento de servidores por diversos usuários implica otimização de tempo
de utilização, em simplificação na manutenção de operações e em redução de custos.
A computação em malha (mesh computing) distribuída por uma rede de
computadores, portanto, sempre representará uma alternativa ou redundância,
uma vez que estará associada à rapidez e à segurança em casos críticos de tomada
de decisão. O servidor on-premise, ou seja, aquele servidor que processa rotinas
localmente e sob responsabilidade da empresa, com repositório de dados e recursos
de TIC próprios, sem recorrer a nuvem, requer que toda infraestrutura, configu-
ração, customização e manutenção de segurança e operação sejam trabalhadas
internamente, tanto para hardware quanto para o software.
A integração híbrida é mais abrangente do que a simples capacidade de inte-
grar sistemas em nuvem com sistemas locais. Por reunir nuvem pública e privada,
os dados e operações mais sensíveis são mantidos na nuvem privada, enquanto as
demais operações podem ser mescladas com rede pública. Neste caso, toma-se como
exemplo a impulsão da Indústria 4.0, que foi intensificada com o avanço das TICs,
por viabilizar a conexão das empresas com fornecedores e clientes. Isto envolve in-
tegrar ambientes locais com nuvem dedicada ou pública, além da nuvem não local.
A complexidade do sistema implica vincular todos os contextos, com múltiplos
proprietários em diferentes estágios de desenvolvimento e de forma harmônica.
--- PAGE 29 ---
|
574 Do paradoxo das habilidades à superinteligência pós-humana
A vantagem de executar uma rotina em nuvem comparada ao uso de super-
computador tem sido objeto de estudo de pesquisadores. Os testes indicam que,
apesar das operações serem executadas de forma extremamente rápida nos super-
computadores, o acesso a esses recursos caros nem sempre é viabilizado de forma
tempestiva, ao passo que uma estrutura organizada em nuvem permite que a rotina
seja imediatamente submetida. Ainda que o intervalo de tempo de processamento
na nuvem seja maior do que no supercomputador, o investimento compensaria
pela prontidão na qual o serviço em nuvem é disponibilizado.
No que se refere à comparação entre computação em grade e computação em
nuvem, no primeiro caso a agregação de recursos de processamento distribuídos
é geralmente empregada para resolução de problemas complexos de engenharia e
ciências. No segundo caso, os recursos computacionais podem ser gerenciados de
forma dinâmica, isto é, dependendo do tipo da demanda por carga de trabalho,
diferentes recursos com diferentes níveis de desempenho podem ser acionados,
inclusive os mais granulares. Portanto, o potencial para escalabilidade dos serviços
em nuvem é bastante elástico e econômico, seguindo o esquema de gerenciamento.
2.3.3 Serviços ofertados em nuvem
Desde a década de 1960, o progresso técnico na engenharia de software caminhou
no sentido de aumentar o desempenho na construção dos códigos de computador.
Exemplo disso foi a modelagem ou programação orientada a objetos (POO), termo
criado por Alan Kay na década de 1970. O conceito foi desenvolvido primeiramente
pelos pesquisadores do Centro Norueguês de Computação em Oslo, com o obje-
tivo de organizar o processo de elaboração do software em unidades ou blocos de
construção padronizados, interoperáveis e reaproveitáveis em outras programações.
Em 1967, os noruegueses Kristen Nygaard e Ole-Johan Dahl incorporaram a ideia
na linguagem Simula-67 que, posteriormente, foi aperfeiçoada pelo laboratório de
pesquisa da Xerox, com a programação Smalltalk.
Nos anos 2000, a engenharia de software obteve maior progresso e rapidez na
produção de aplicações.. Alguns exemplos evidenciam a evolução como os: i) sistemas
com arquitetura orientada a serviços (conceito SOA), que permitem que funcionali-
dades sejam disponibilizadas na forma de serviços, favorecendo o reaproveitamento
e a replicação desses; ii) sistemas de desenvolvimento em ciclos curtos de produção e
atualização contínua de produtos, que permitem a integração, entrega e implantação
continuadas (metodologia scrum); iii) equipes de desenvolvimento e de infraestrutura
integradas, que permitem diminuir o tempo de produção e aumentar a qualidade
do software (abordagem devops); e iv) suítes de serviço, que permitem organizar um
--- PAGE 30 ---
|
Segmentos tecnológicos e aplicações 575
conjunto de aplicativos segundo uma função de negócios, microsserviços e equipes
multidisciplinares ágeis (squad).6
Com o avanço dos modelos de computação em nuvem, novos serviços têm
sido oferecidos pelas empresas especializadas, incluindo: software como serviço
(software as a service – SaaS), com oferta de softwares-padrões acessados pela rede
por assinatura, por exemplo navegador de web; plataforma com serviços (platform
as a service - PaaS), com recursos ou ferramentas para clientes desenvolverem apli-
cativos em nuvem, por exemplo linguagem de programação; e infraestrutura como
serviço (infrastructure as a service – IaaS), para processamento com provedores e/
ou servidores, armazenagem, hospedagem e rede disponibilizados em nuvem, por
exemplo firewall. Para cada um desses modelos, existem subdivisões de segmentos
trabalhando com funções distintas e provedores especializados nos serviços.
FIGURA 3
Modelos de nuvens (clouds)
Aplicações Aplicações Aplicações Aplicações
Dados Dados Dados Dados
Tempo de execução Tempo de execução Tempo de execução Tempo de execução
Middleware Middleware Middleware Middleware
Sistema operacional Sistema operacional Sistema operacional Sistema operacional
Virtualização Virtualização Virtualização Virtualização
Servidores Servidores Servidores Servidores
Repositório Repositório Repositório Repositório
Rede Rede Rede Rede
oiráusu
olep
odartsinimdA
oiráusu
olep
odartsinimdA
Administrado
pela
empresa
oiráusu
olep
odartsinimdA
Administrado
pela
empresa
Administrado
pela
empresa
Software nas
instalações locais Infraestrutura Plataforma Software
(on premises) (as a service) (as a service) (as a service)
Fonte: Stack 24/7. Disponível em: https://stack247.wordpress.com/2015/05/21/azure-on-premises-vs-iaas-vs-paas-vs-saas/.
Obs.: 1. Principais empresas de serviços em nuvem: i) IaaS: Amazon Web Services – AWS (~40%), Microsoft (~20%), Google
(~10%) e Alibabah (~5%); ii) PaaS: GoogleApp Engine, Oracle Cloud Plataform, Cloud Foundry (Pivotal) e Heroku
(Salesforce); e iii) SaaS: Microsoft, Salesforce.
6. NERY, C. TI usa métodos ágeis para acelerar desenvolvimentos. Valor Econômico Empresas, 29 jun. 2018. Disponível
em: https://valor.globo.com/empresas/noticia/2018/06/29/ti-usa-metodos-ageis-para-acelerar-desenvolvimentos.ghtml.
--- PAGE 31 ---
|
576 Do paradoxo das habilidades à superinteligência pós-humana
QUADRO 3
Caracterização das aplicações típicas de nuvem pública
Aplicações Descrições Segmentos Soluções
Cisco Webex, Microsoft Lync, Interna-
Comunicação e colaboração. tional Business Machines Corporation
(IBM) LotusLive.
Aplicações e softwares em nuvem
Produtividade de escritório. Google Apps, Microsoft Office 365.
controlados por provedor e acessa-
SaaS das por muitos usuários por meio Salesforce.com, PerfectView e Accoun-
Gestão de relacionamento com o cliente.
de browser, como alternativa ao tView CRM Online.
processamento local.
NetSuite, Exact Online, SAP, Twinfield,
Sistema integrado de gestão empresarial.
Business ByDesign, Infor.
Gerenciamento de cadeia de fornecedores. Descartes, Ariba, Ketera, JDA Software.
Provedor de nuvem fornece ferramentas Desenvolvimento de ações específicas. Salesforce Force-com, SaaSPlaza, SAP
para o cliente desenvolver aplicações Business ByDesign.
PaaS
que serão hospedadas e executadas Google App Engine,
em nuvem. Desenvolvimento de ações genéricas. Microsoft Azure.
Cliente tem controle de sistema EMC, Symantec, RainStor, Amazon
Computação, armazenamento e cópia
IaaS operacional (SO) e aplicações por Elastic Compute Cloud (EC2), Amazon
de dados.
meio de técnicas de virtualização. Simple Storage Service (S3).
Fonte: TheMetisFiles. Disponível em: https://www.themetisfiles.com/2011/12/dutch-enterprise-public-cloud-services-market-
-size-and-forecast-2/.
Elaboração da autora.
As três plataformas em nuvem mais conhecidas são Azure da Microsoft,
AppEngine da Google e EC2 da Amazon, cada qual com funcionalidades especí-
ficas, de acordo com os especialistas desse mercado.
1) A plataforma EC2 da Amazon oferece ao desenvolvedor maior liberdade
de acesso aos recursos, controlando o software como em uma máquina real,
hospedando diferentes tipos de aplicações em nuvem e utilizando a caixa de
ferramentas e serviços prontos oferecidos pela subsidiária. Entre os diversos
recursos fornecidos estão o armazenamento Amazon S3, o Banco de Dados
Relacional (Relational Data Base Service – RDS) e o processamento de big
data (Elastic Map Reduce).
2) O AppEngine da Google oferece plataforma para desenvolvimento de
aplicação de serviços específicos de web convencional, em linguagem
Python e Java, que se integra a outras ferramentas para elaboração de
aplicações Ajax, com capacidade de armazenamento em escala. O mo-
delo requisição-resposta (request-reply) exige quantidade de CPU para
processar as requisições e as aplicações desenvolvidas pelo AppEngine
que, por sua vez, utilizam a solução proprietária MegaStore para arma-
zenamento de dados.
--- PAGE 32 ---
|
Segmentos tecnológicos e aplicações 577
3) A plataforma Azure da Microsoft é o meio termo entre liberdade e es-
pecificação no desenvolvimento de aplicações, possibilitando elaborar
rotinas de uso geral. Utiliza bibliotecas .NET compiladas em linguagem
comum de tempo de execução, mas possibilita o uso de linguagem
proprietária, desde que sem controle de SO. O conceito de nuvem pú-
blica ofertada pela Microsoft envolve o computador Watson da IBM e
múltiplos fornecedores.
Importantes transações têm sido observadas no mercado de desenvolvimento
de softwares nos últimos anos, especialmente para oferta em nuvem. Essas envolvem
grandes empresas globais de tecnologias e empresas ou organizações que se especiali-
zaram na missão de congregar desenvolvedores independentes de software. Portanto,
as negociações referem-se tanto à plataforma GitHub, que oferece espaço em nuvem
para desenvolvimento colaborativo de projetos de software, quanto à Red Hat, que
é especializada em soluções de software empresariais de código aberto, automação e
infraestrutura em nuvem híbrida. Em 2014, a Microsoft iniciou uma nova estratégia
de desenvolvimento para soluções de software em nuvem. Primeiramente, a companhia
juntou-se à Linux Foundation e depois adquiriu a GitHub por US$ 7,5 bilhões,
em maio de 2018, quando essa última empresa era considerada deficitária, sendo
avaliada em US$ 2 bilhões. Os objetivos da Microsoft eram: i) manter a plataforma
independente, servindo aos fins e dispositivos das mais diversas iniciativas; ii) acelerar
o uso da ferramenta por parte das empresas; e iii) disponibilizar as ferramentas e
serviços desenvolvidos pela Microsoft (Ciriaco, 2018).
A GitHub pode ser considerada a maior empresa desenvolvedora de softwares
do mundo, com dezenas de milhões de desenvolvedores independentes que servem
a todas as indústrias com seus softwares abertos. Fundada em 2008 na cidade de
São Francisco, a GitHub permite aos programadores armazenar e compartilhar
trabalhos. A ferramenta de rastreamento utilizada pela plataforma é o Git, que
registra como as partes dos códigos de computação são modificadas por diferentes
programadores conectados pela internet.
Pode parecer controverso, mas durante o processo de venda, a independência
da GitHub foi considerada crucial para a continuidade dos negócios, pois muitos
desenvolvedores utilizam o serviço online da GitHub para criar aplicativos ligados à
plataforma em nuvem dos competidores da Microsoft, como a Amazon e a Google.
A plataforma de compartilhamento de códigos de programas GitHub serve a 1,8
milhões de empresas em todo o mundo.
A mudança de estratégia da Microsoft objetivou atrair desenvolvedores de
softwares abertos, que anteriormente corroíam os softwares comerciais da empresa.
Em 2016, a Microsoft comprou também a Linkedin (rede de relacionamentos
profissionais na internet), sendo o pagamento da transação realizado com ações
da Microsoft no valor de US$ 26 bilhões.
--- PAGE 33 ---
|
578 Do paradoxo das habilidades à superinteligência pós-humana
A IBM, que oferece grande gama de serviços em nuvem, decidiu comprar
a Red Hat, desenvolvedora de código aberto por US$ 34 bilhões, em 2018. A
Red Hat é a maior distribuidora do SO Linux e serve a grandes empresas como
Microsoft, HP, Dell e Cisco. A tecnologia OpenShift é um dos produtos da Red
Hat que mais atrai a atenção, pois decompõe os grandes programas corporativos
em módulos flexíveis interconectados, os chamados containers. A principal vanta-
gem dos containers é a portabilidade ou transferência entre plataformas diferentes,
reduzindo a complexidade e eficiência dos aplicativos complexos.
2.3.4 Datacenters e matriz de risco
O modelo híbrido de datacenters surgiu na década de 1960, com a centralização
do processamento nos computadores de alto desempenho em escala (mainframes),
acionados pelos terminais. Os computadores pessoais (PC), microcomputadores
com maior capacidade de processamento e armazenagem surgiram na década de
1980 e arrefeceram a procura pelos datacenters. Posteriormente, deu-se uma re-
combinação nos moldes de cliente-servidor. Correntemente, a web vem suprindo
a demanda crescente por aplicações de alto desempenho e quantidade massiva de
dados, disparados pelo avanço das TICs IA/ML/DL. Logo, o ciclo voltou à origem
com ressurgimento dos datacenters, dessa vez, em outro patamar de evolução.
Os datacenters podem variar conforme o tamanho ou capacidade de proces-
samento e a versatilidade ou portabilidade. Os megadatacenters, contendo dezenas
de milhares de servidores, são energointensivos. Enquanto os datacenters portáteis,
com alguns milhares de servidores, podem ser instalados em containers para funcio-
nar em casos de necessidade de aumento provisório de capacidade computacional
ou de serviço em localidades remotas. As exigências crescentes pela racionalização
de despesas e de custos, com aumento de capacidade e segurança fez com que a
tecnologia dos datacenters fosse aperfeiçoada e ganhasse atributos específicos para
atender à computação em nuvem.
O aparecimento da novidade, representada pela computação em nuvem,
fez surgir a expectativa de que os órgãos públicos e as instituições de pesquisa
pudessem reduzir despesas ou gastos com TICs, caso viessem a fazer uso dessa
tecnologia. O TCU, TC 025.994/2014-0 e Acórdão no 1739 de 2015, realizou
auditoria sobre o assunto computação em nuvem, assim que a tecnologia começou
a ser disseminada. O principal produto desse levantamento foi a matriz de riscos
da contratação de serviços de computação em nuvem. Os tópicos avaliados foram
a segurança da informação, a governança e gestão de risco e a infraestrutura de
TICs. Cada tema desdobrando-se em categorias de riscos, cujas características
foram reproduzidas no quadro 4.
--- PAGE 34 ---
|
Segmentos tecnológicos e aplicações 579
QUADRO 4
Riscos da contratação de serviços de computação em nuvem
Tema Categoria de risco Características
Falta de controles e salvaguardas por parte do provedor provocando indisponibilidade
Indisponibilidade do para o usuário final.
serviço. Indisponibilidade de elementos de infraestrura do cliente críticos para o acesso a
serviços em nuvem.
Controle de acesso inexistente ou insuficiente para assegurar a confidencialidade dos
dados armazenados.
Comprometimento de segurança no momento da transmissão dos dados para o
provedor em nuvem.
Confidencialidade Acesso indevido do provedor aos dados do cliente.
e integridade dos
dados. Fornecimento legal de dados pelo provedor submetido à jurisdição estrangeira, impli-
cando risco de privacidade do cliente.
Exposição cruzada e indevida de dados de clientes a terceiros.
Acessos injustificados devido à ampla oferta de serviços de computação em nuvem e
independentes de localização.
Segurança da
informação. Gestão de mudanças. Modificações na infraestrutura de software do provedor pode ser incompatível com
o software do cliente.
Liberação de logs de acesso do sistema de segurança do provedor pode significar perda
de informações de incidentes do cliente.
Trilhas de auditoria. Brevidade no tempo de retenção dos registros de log pode estar em desacordo com
a política interna do cliente.
Ausência de isolamento dos logs e vazamento para terceiros.
Segurança de interfa- APIs para acesso à infraestrutura do provedor com falhas e com vulnerabilidades de
ce de comunicação. acesso aos dados de clientes.
Desconhecimento por parte do cliente das políticas e orientações do provedor quanto
Acesso indevido por ao acesso de ativos físicos e virtuais privados.
invasor interno. Monitoramento de atividades de funcionários por parte do provedor e verificação de
normas organizacionais incompatíveis.
Atualizações e corre-
Exploração de vulnerabilidades do provedor podem impactar operações do cliente.
ções de segurança.
Dimensionamento inadequado de vantagens e desvantagens à incorporação de
serviços em nuvem.
Planejamento.
Orçamentação incorreta na contratação do serviço de tecnologia da informação (TI)
em nuvem.
Política de recursos
Resistência da equipe interna de TI por receio de perda de função.
humanos (RH).
Governança e Perda de governança e controle de TI por parte da contratante.
gestão de riscos. Menor resistência interna a determinados comandos, quando o serviço passa a ser
Governança.
realizado externamente.
Falta de apoio devido à cultura organizacional avessa aos riscos.
Inobservância de legislação e normativos específicos que regulam a computação em
Legislação e nor- nuvem ou aos serviços de TI em geral.
mativos. Incumprimento de normas de segurança do Departamento de Segurança da Informação e
Cibernética/Gabinete de Segurança Institucional/Presidência da República (DSIC/GSI/PR).
(Continua)
--- PAGE 35 ---
|
580 Do paradoxo das habilidades à superinteligência pós-humana
(Continuação)
Tema Categoria de risco Características
Descumprimento dos níveis de serviços acordados.
Problemas com a tempestividade e efetividade na solução de ocorrências, eventos
Gestão contratual. e incidentes.
Falhas de monitoramento e gestão contratual.
Falta de controle no uso do cliente ocasionando transbordamento do orçamento.
Vulnerabilidade do cliente em relação aos serviços prestados (vendor lock-in).
Monitoramen-
Dificuldades com a portabilidade de dados e interoperabilidade em caso de migração
to e gestão Dependência frente por parte do cliente.
contratual. ao provedor.
Falta de previsão dos custos de saída do provedor.
Indisponibilidade do provedor por falência, ruptura contratual e sequestro de dados.
Conflito sobre propriedade de dados armazenados em nuvem.
Limitações legais locais e contextuais para serviços oferecidos globalmente.
Falhas contratuais.
Inobservância de descontinuidade no armazenamento de dados por parte do provedor
ao término da contratação.
Falhas de isolamento entre instâncias virtuais de diferentes clientes.
Vulnerabilidades adicionais para clientes devido ao compartilhamento de recursos
pelos provedores.
Incompatibilidade de ferramentas e processos de gestão de incidentes utilizados pelo
provedor e pelo cliente.
Infraestrutura Falhas na documentação de processos de gestão de incidentes do provedor (resolução,
Falhas relativas à TI.
de TI. escalonamento e encerramento de incidentes).
Problemas na infraestrutura do cliente repercutindo no desempenho dos serviços de
computação em nuvem.
Equívocos no dimensionamento de carga na infraestrutura do provedor impactando o
desempenho dos serviços de computação.
Incompatibilidades entre arquiteturas cliente e provedor nuvem.
Fonte: TC 025.994/2014-0. Disponível em: https://portal.tcu.gov.br/biblioteca-digital/computacao-em-nuvem.htm.
2.4 Computadores quântico e digital
Linha de base para o desenvolvimento das TICs, a trajetória de evolução de hardware
tem surpreendido até mesmo os cientistas, pois tem exigido mais conhecimento e
aprendizagem sobre como as leis da física funcionam. Ao mesmo tempo, vencidos
esses desafios, espera-se que essas tecnologias venham a dirimir questões críticas
que há muito afligem a humanidade.
2.4.1 Conceitos
1) Computador quântico – utiliza propriedades da física quântica, como a
mecânica de interferência e sobreposição de partículas, para representar
valores a partir de bits quânticos (os qubits 0, 1, o bit flip e a relação fásica
0~1) e realizar operações de cálculos matemáticos, implicando redução
de tempo de processamento para operações complexas anteriormente
--- PAGE 36 ---
|
Segmentos tecnológicos e aplicações 581
impraticáveis, como a quebra de códigos de encriptação, análise combi-
natória/fatoração e logaritmo discreto.
2) Computador eletrônico digital – utiliza propriedades eletrônicas para
representar valores a partir de sinais binários ou combinação de dígitos
(bits 0 e 1), formar números, armazenar dados, realizar operações de
cálculos matemáticos e programar algoritmos.
3) Computador analógico – utiliza engrenagens, como o ábaco grego,
soroban, bastões de Nepier, réguas de cálculo e máquina diferencial de
Babbage para resolver operações e equações matemáticas.
2.4.2 Computador quântico
Em outubro de 2019, a revista Nature publicou artigo intitulado Quantum su-
premacy using a programmable superconducting processor, sobre um eventual marco
no processamento quântico, alcançado pela parceria entre a empresa Google e a
Agência Aeroespacial Norte Americana (National Aeronautics and Space Admi-
nistration – Nasa) (Arute et al., 2019). A fidedignidade desse feito foi contestada
pela empresa concorrente, a IBM, com o argumento de que, caso a tarefa fosse
abordada por uma técnica específica consumiria apenas um par de dias para obter
o mesmo resultado num supercomputador convencional (por exemplo, o Summit).
Contudo, a supremacia quântica teria ocorrido quando a capacidade de pro-
cessamento do computador quântico Sycamore da Google-Nasa ultrapassou àquela
de um supercomputador convencional, provando que a substituição completa seria
uma questão de tempo. Foi no final de 2018 que a Google anunciou parceria com
a Nasa para analisar os resultados de circuitos quânticos nos processadores, de
forma a validar a linha de base para o estabelecimento da supremacia quântica. A
Nasa considera que o campo de computação quântica é promissor para apoiar os
projetos de criação de materiais leves e robustos que comporão as naves espaciais
para as missões à Lua e ao planeta Marte, programadas para os próximos anos.
O processador Sycamore com 53 qubits supercondutores programáveis,
correspondente à dimensão de 253 espaço-estado computacional de combinações
possíveis, precisou de apenas três minutos e vinte segundos (total de duzentos
segundos) para processar uma tarefa que levaria cerca de 10 mil anos em um
supercomputador clássico. A rotina submetida a teste era uma instanciação com
repetição de 1 milhão de vezes em circuito quântico, descrevendo os prováveis
resultados do código criado por um gerador de números aleatórios. O dramático
aumento da velocidade de processamento, comparado aos algoritmos conhecidos,
seria uma realização experimental capaz de estabelecer um novo paradigma, o de
supremacia quântica.
--- PAGE 37 ---
|
582 Do paradoxo das habilidades à superinteligência pós-humana
O termo supremacia quântica foi popularizado pelo professor John Preskill da
Caltech, para o caso de ocorrência de evento no qual o computador quântico seria
capaz de resolver um problema irresolvível por um computador clássico. O conceito
de vantagem tecnológica na simulação de sistemas quânticos foi proposto pelo mate-
mático russo Yuri Manin, em 1980, e pelo físico norteamericano Richard Feynman,
em 1981. Ademais, existe grande expectativa de que os computadores quânticos
venham a operar autonomamente, corrigindo erros no próprio sistema através do
modo de aprendizado de máquina, comumente empregado nas máquinas tradicionais.
Em busca desse feito, no final de 2018, a equipe de Francesco Tacchino, da
Universidade de Pavia, região da Lombardia italiana construiu o primeiro perceptron
num computador quântico. O experimento de Rosenblatt (criador do Perceptron
em 1957) foi reproduzido com aprendizado da máquina Q5 da IBM, processador
quantum supercondutor, denominado Tenerife. Capaz de processar cinco qubits,
o computador quântico Tenerife pode ser acessado abertamente via internet por
qualquer programador de algoritmo quântico. O algoritmo de processamento de
imagem (percepton) criado utiliza um vetor clássico (conjunto de números) como
entrada, combina com um vetor de peso quântico, produzindo “1” quando reco-
nhece a imagem e “0” em quaisquer outros casos.
No entanto, ao que tudo indica, os países asiáticos devem liderar a corrida
pela comercialização da tecnologia quântica associada à IA. Em 2015, a empresa
chinesa Alibaba e a Academia Chinesa de Ciência fundaram o Laboratório de
Computação Quântica, em Shanghai. Três anos depois, os chineses lançaram
o serviço de computação quântica em nuvem, com perspectiva de futuramente
formar a primeira rede quântica interligando cidades chinesas.
A partir de 2020, o Japão planeja construir cinco centros de inovação em
computação quântica, no período de cinco anos, e implementar computadores de
100 qubits, em dez anos. Os planos japoneses estendem-se até 2039 e incluem a
formação de fundo com lastro de US$ 276 milhões. O governo japonês considera
a área prioritária, juntamente com IA e biotecnologia, prevendo aplicações em
sensoriamento, comunicações, criptografia, fármacos, finanças, logística e desen-
volvimento de materiais.
A Coreia do Sul deve investir US$ 40 milhões, nos próximos cinco anos,
para criar um ecossistema de pesquisa doméstica, implementar um processador
quântico de classe 5-qubits de propósito geral e formar 33 grupos de pesquisa para
desenvolvimento da computação quântica.
Em Singapura, o Centro de Excelência em Tecnologia Quântica (Centre for
Quantum Technologies – CQT) funciona na Universidade de Singapura com
o objetivo de desenvolver a pesquisa com possibilidades de aplicação prática de
dispositivos mecânico-quântico para criptografia e computação.
--- PAGE 38 ---
|
Segmentos tecnológicos e aplicações 583
A UE considera que a soberania sobre a tecnologia quântica é fundamental
para o desenvolvimento socioeconômico futuro e, em 2018, anunciou investimen-
tos da ordem de € 1 bilhão para projetos na área. O plano reúne instituições de
pesquisa, indústria e fundos públicos, visando desenvolver aplicações comerciais.
Durante a primeira fase (2018-2021), o orçamento planejado foi de cerca de € 130
milhões, para vinte projetos em computação, simulação, comunicação, metrologia
e sensoriamento quântico.
De acordo com o site especializado MarketsandMarkets,7 o mercado de
computação quântica nos Estados Unidos deve crescer de US$ 93 milhões para
US$ 283 milhões, entre 2019 e 2024, como o segmento de quantum computing
as a service (QCaaS) crescendo de US$ 4 milhões para US$ 13 milhões. A taxa
composta de crescimento anual equivale a 24,9% e 26,8%, respectivamente. A
estimativa considera a oferta de computação quântica na indústria, em soluções
de consultoria e aplicações IA/ML.
Além da Google e da IBM, as principais empresas envolvidas com a tecnologia
de computadores quânticos são D-Wave Sistemas e 1QB Information Technology
(Canadá); QX Branch, QC Ware Corp, Rigetti Computing, River Lane Research
e StationQ-Microsoft (Estados Unidos); e, Cambridge Quantum Computer Limi-
ted (UK). Para ampliar o conhecimento desse mercado, a corporação Homeland
Security Research Corporation (HSRC), com sede em Washington D.C., elaborou
o relatório Quantum Computing Technologies & Global Market (2017-2024), onde
apresentou material bastante abrangente sobre as oportunidades oferecidas pela
tecnologia emergente para servir de guia a instituições públicas e privadas.
Retomando a linha de raciocínio que busca esclarecer como a evolução
tecnológica pode colaborar para a viabilização da comercialização futura, alguns
esforços nesse sentido podem ser destacados. Esse é o caso da IBM, que tem centro
de pesquisa dedicado aos qubits supercondutores transmon, projetados para reduzir
a sensibilidade ao ruído de carga, com três modelos ou protótipos desenvolvidos.
O transmon foi primeiramente desenvolvido na Universidade de Yale, em 2007,
sendo empregado em processadores quânticos com mais de 50 qubits, devido ao
fato de apresentarem grande coerência e imunidade a erros nos circuitos quânticos
de processamento.
O computador quântico da IBM Research foi apresentado na Consumer
Electronic Show (CES) 2019 com o intuito de viabilizar sua comercialização.
A parceria com a QXBranch Inc. permitiu o desenvolvimento e a validação
de aplicações comerciais para governos e empresas privadas. O IBM-Q System
One incorpora engenharias de sistemas industriais e criogenia em computadores
7. Disponível em: https://www.quantaneo.com/Quantum-Computing-Market-Worth-283-Million-by-2024-Exclusi-
ve-Report-by-MarketsandMarkets_a78.html.
--- PAGE 39 ---
|
584 Do paradoxo das habilidades à superinteligência pós-humana
quânticos para operarem em nuvem, com componentes customizados, permitin-
do acesso aos membros da comunidade mundial IBM-Q Network. O primeiro
sistema de computação quântica integrado é constituído de câmaras de contenção
de qubits, resfriamento ou criogenia por gás líquido hélio, cabeamento reforçado
para conectar os equipamentos utilizados para monitorar as entradas e saídas dos
qubits desenhados para evitar altas temperaturas e vibração em excesso. O estado
de coerência quântica é o maior desafio para o desempenho, em média 75 micros-
segundos, pois o sistema está sujeito a inúmeras interferências.
O laboratório Intel especializado em pesquisa de algoritmos quânticos e con-
troles eletrônicos está na terceira geração de processadores quânticos, Tangle Lake,
que incorpora 49 qubits supercondutores em pacote. O sistema permitirá simular
fenômenos naturais, para resolução de problemas aplicados à medicina genética e
astrofísica. O Grupo Intel de Tecnologia e Manufatura (TMG) tem parceria com
a holandesa QuTech, instituto de pesquisa quântica da Universidade de Tecnologia
de Delft (TU Delft) e a Organização Holandesa de Pesquisa Científica Aplicada
(TNO), além de academias e indústrias em todo o mundo. A QuTech, em colabo-
ração com a operadora de telecomunicações holandesa KPN, está desenvolvendo
a primeira rede rudimentar baseada em entrelaçamento quântico para interligar
algumas cidades holandesas.
A Microsoft Quantum trabalha com 1QB Information Technologies Inc.
em soluções industriais para problemas considerados intratáveis pela tecnologia
convencional. Com o Instituto Niels Bohr, na Dinamarca, as pesquisas estão
relacionadas com partículas ou férmions de Majorana, estados complexos de fios
quânticos ou quasipartículas. A Microsoft Quantum desenvolveu a linguagem de
computação quântica denominada Q#.
A Rigetti Computing é californiana de Berkeley e desenvolve circuitos inte-
grados quânticos e plataforma Forest em nuvem, que permite aos programadores
escreverem algoritmos quânticos. A empresa serve a vários ramos da indústria, como
transporte, logística e química em diferentes aplicações de mercado de computação
quântica, como aprendizado de máquina e otimização.
A canadense D-Wave desenvolve sistemas de computação quântica para
Google, Nasa, Lookheed Martin, Los Alamos Lab entre outros, sendo pioneira
na oferta de computador quântico comercial (D-Wave 2000Q). O portfólio da
empresa envolve os campos de otimização, aprendizado de máquina, análise de
imagens, verificação/validação de hardware e software, bioinformática, amostragem
Monte Carlo, segurança cibernética, reconhecimento de padrão e detecção de
anormalidade. A D-Wave é parceira da alemã Volkswagen e da japonesa Denso
Corporation para desenvolvimento de aplicações em tecnologia veículos autônomos
com computação quântica.
--- PAGE 40 ---
|
Segmentos tecnológicos e aplicações 585
Estudiosos que acompanham a P&D relacionada aos fenômenos quânticos
costumam segmentar a trajetória história do progresso técnico em dois segmentos:
a primeira e a segunda revolução quântica. A primeira revolução começou no início
do século XX, com as tentativas de explicar a dualidade onda-partícula, o efeito
fotoelétrico, células solares e lasers. A segunda revolução quântica é caracterizada
pela capacidade de utilizar o conhecimento científico e pela corrida global por
computadores mais eficientes, protocolos de comunicação mais seguros e novos
sensores ultrassensíveis.
FIGURA 4
Linha do tempo de evolução para a computação quântica
Fase fenomenológica Fase experimental Fase de realização Fase de sistema Fase comercial
1950s-1990s 1990s-2000s 2010s 2015-2025 Além de 2025
Pesquisa teórica preliminar, Estabelecimento de Desenvolvimento de Engenharia em nível de Produção de sistemas de
com experimentação física mecanismos fundamentais, processadores quânticos e sistema para computadores computação quântica para
limitada com aparato físico computadores quânticos quânticos práticos resolver problemas do
rudimentares mundo real
Fonte: Intel. Disponível em: https://drrajivdesaimd.com/2020/04/12/quantum-computing/.
As propriedades peculiares das partículas subatômicas fazem com que o
qubit (bit quântico) possa estar em dois estados ao mesmo tempo, fótons-onda
ou elétrons-partícula, característica denominada superposição e que imprime
velocidades avassaladoras aos cálculos do computador. Outra característica é o
entrelaçamento, que faz com que os qubits possam influenciar-se mutuamente
mesmo sem a conexão física, permitindo saltos lógicos nas operações. Os algoritmos
quânticos que fazem uso dessa propriedade permitem criar atalhos probabilísticos,
dando respostas razoáveis para problemas matemáticos complexos (exemplo clássico
é a fatoração de números primos e criptografia). O princípio de entrelaçamento é
especialmente explorado nas aplicações de criptografia quântica, visando à invio-
labilidade dos sistemas de segurança.
A aplicação prática mais conhecida dos computadores quânticos é o quan-
tum annealing, ou recozimento quântico, trata-se de processo (metaheurística) de
flutuações quânticas utilizado para encontrar soluções ótimas (mínimo/máximo
global) das funções objetivas, dado um conjunto de mínimos e máximos locais.
Portanto, faz uso das propriedades de sobreposição e de tunelamento quântico
(transposição de barreiras com maior potencial de energia) para escapar de míni-
mos locais e encontrar mínimos globais. Quando combinado a quantum artificial
intelligence e quantum machine learning, o método denominado reinforcement
--- PAGE 41 ---
|
586 Do paradoxo das habilidades à superinteligência pós-humana
quantum annealing (RQA)8 passa a ter interação com agente inteligente no papel
de automata aprendiz para resolução de problemas termodinâmicos, por exemplo.
Os desafios da computação quântica devem-se à natureza vulnerável do
quantum, altamente instável e propenso a interferências de outras fontes de energia,
campos magnéticos, fontes vibratórias e sinais que podem interferir no processa-
mento. Os sistemas precisam ser estanques, sem contato com o exterior para que
o emaranhamento quântico seja controlado. O menor nível de energia (estado
fundamental) é conseguido a temperaturas próximas do zero absoluto (-273,15°C),
campos magnéticos menos intensos do que o da Terra e vácuo. Algum grau de dis-
córdia é necessário, mas sem emaranhamento, para que o sistema seja considerado
quântico e permita cálculos coerentes. A discórdia,9 neste caso, refere-se à medida
estatística que determina algo quântico no mundo físico, ou seja, em desacordo
com a física newtoniana.
O desenvolvimento do computador quântico pelos físicos, engenheiros e cien-
tistas da computação concentra-se em diferentes conceitos-tipo, que compreendem
quatro categorias: i) partículas de luz (fótons) – com desempenho estável, mas de
difícil configuração e controle; ii) íons aprisionados – manipulados a laser com boa
precisão, mas de difícil escalabilidade para a quantidade de qubits; iii) supercon-
dutores – com circuitos eletrônicos semelhantes aos convencionais, propiciando
a escalabilidade; e iv) centros de vacância com nitrogênio em diamantes – que faz
uso de falhas na estrutura cristalina do diamante para manejar luz e micro-ondas
nos cálculos quânticos. A internet quântica deverá ser desenvolvida com base em
partículas de luz (fótons), enquanto para o processamento deverão ser utilizados
os qubits de matéria (íons presos, fótons, átomos, quasi-partículas).
No Brasil, pelo menos três Institutos Nacionais de Ciência e Tecnologia
(INCT) estão dedicados à pesquisa em ciência de informação quântica: i) INCT
de Informação Quântica (INCT-IQ) da Universidade Federal do Rio de Janeiro
(UFRJ), com pesquisas básicas para o desenvolvimento de computação e comu-
nicação quântica; ii) INCT – Grupo de Computação Quântica e Criptografia,
vinculado ao Laboratório Nacional de Computação Científica (LNCC), em Pe-
trópolis, propondo técnicas para desenvolvimento de hardware; e iii) INCT-IQ
da Universidade Estadual de Campinas (Unicamp) com pesquisadores dedicados
à óptica e teoria quântica.
8. Disponível em: https://arxiv.org/pdf/2001.00234.pdf.
9. Disponível em: https://revistapesquisa.fapesp.br/2012/03/29/a-nova-onda-dos-qubits/.
--- PAGE 42 ---
|
Segmentos tecnológicos e aplicações 587
2.4.3 Computador digital
No ambiente de computação digital, a tendência dos processadores convencionais
(bits 0-1) é ficar cada vez mais microscópicos, com centenas de milhões de transis-
tores dentro de um microprocessador. Em 2017, transistores de 10 nm entraram
em operação e, futuramente, podem chegar a 5 nm, considerado tamanho limite a
partir do qual há ocorrência de tunelamento quântico, ou seja, drenagem de elétrons
por material extremamente fino. As fugas de correntes de elétrons danificam os
chips, tornando-os ineficientes. Porém, outros fatores determinam o desempenho
das unidades centrais de processamento do computador, por exemplo, a quantidade
de transistores por área (densidade do processador).
Os supercomputadores têm capacidade de processamento medida em peta-
flops, mas a nova geração de projetos deverá chegar a exaflops = 1 mil petaflops,
capazes de processar bilhões de operações por segundo (1018/seg). Os chineses
anunciaram um supercomputador exascale para 2020. Os Estados Unidos têm o
Exascale Computing Project, que inclui o Supercomputador Frontier com tecno-
logia da Cray e Advanced Micro Devices (AMD), custo de US$ 600 milhões e
lançamento previsto para 2021 no Laboratório Nacional de Oak Ridge. O exascale
Aurora terá tecnologia de Cray-Intel no Laboratório Nacional de Argonne. A UE
está trabalhando na geração futura de computadores exascale. O consórcio European
High-Performance Computing Joint Undertaking (EuroHPC JU) foi criado em
2018 e o supercomputador Lumi ficou programado para 2021.
Os supercomputadores chineses Titan NX (11 teraflops) e Taihu Light (41
mil processadores) são parte da frota de mais de duzentos computadores chineses
classificados na lista internacional Top500. Nessa relação, com o segundo maior
conjunto de supercomputadores, estão os cerca de 150 norte-americanos. Seguem
em ordenação, o Japão, a Alemanha, a França e o Reino Unido, totalizando cerca
de noventa supermáquinas. O supercomputador brasileiro Santos Dumont figurou
entre os quinhentos melhores no ranking até setembro de 2017, mas vem passando
por upgrade, uma vez que foi inaugurado em janeiro de 2016.
TABELA 2
Dez supercomputadores mais rápidos do mundo (2019)
Supercomputador Localização Velocidade (petaflops)
Summit Laboratório Nacional Oak Ridge 148,6
Sierra Laboratório Nacional Lawrence Livermore 94,6
SunWay TaihuLight Centro Nacional de Supercomputação da China 93,0
Tianhe-2A Universidade Nacional de Tecnologia de Defesa da China 61,4
Frontera Centro de Computação Avançada da Universidade do Texas 23,5
Piz Dainte Centro Nacional de Supercomputação da Suíça 21,2
(Continua)
--- PAGE 43 ---
|
588 Do paradoxo das habilidades à superinteligência pós-humana
(Continuação)
Supercomputador Localização Velocidade (petaflops)
Trinity Laboratório Nacional de Los Alamos e Sandia 20,2
ABCI Instituto Nacional de Ciência e Tecnologia Industrial Avançada do Japão 19,9
SuperMuc-NG Centro de Supercomputação Leibniz 19,5
Lassen Laboratório Nacional Lawrence Livermore 18,2
Fonte: Computer World. Disponível em: https://itforum.com.br/noticias/os-10-dos-supercomputadores-mais-rapidos-do-
-mundo/#Lassen.
No Brasil, a computação científica conta com o Sistema Nacional de Proces-
samento de Alto Desempenho (Sinapad), que apoia projetos em áreas de grande
interesse nacional para os setores de energia, meteorologia, engenharias, ciências
da terra e vida. Fazem parte do Sinapad, o supercomputador Santos Dumont
no LNCC, com capacidade de processamento petaflópica, além de outros seis
supercomputadores, como os da Petrobras e do Centro de Previsão de Tempo e
Estudos Climáticos (CPTEC).
QUADRO 5
Sobre o LNCC (linhas de pesquisa)
Linhas de pesquisa Descrição Aplicação
Métodos para compreensão e solução de problemas Métodos estocásticos em finanças e sensibili-
Modelagem computacional
na fenomenologia, matemática e computação. dade de risco.
Análise matemática de técnicas e algoritmos
Análise numérica e adaptabilidade, meta-heurística
Métodos numéricos para simulação computacional de fenômenos
e métodos de elementos finitos.
complexos.
Estuda o comportamento de sistemas dinâmicos,
Controle de filtragens, dinâmicas estocásticas,
Sistemas, controle e sinais visando obter padrões de referência do estado
sistemas chaveados, robótica.
ou de saídas.
Paralelismo e distribuição, computação quântica, Massificação de redes, ambientes complexos e
Computação
interconectividade, realidade virtual/aumentada. colaborativos, visualização científica.
Integra conhecimento de ciência da computação, Biofarmática, ecologia numérica, modelagem
Biologia computacional
matemática aplicada e estatística com biologia. molecular, neurociência.
Cooperação com o setor produtivo para mode- Capacidade de carga residual, análise de tensão
Petróleo, água e gás lagem determinística e estocástica e simulação em dutos, visualização de plataformas offshore
de escoamento. em alto mar.
Diagnóstico por imagem com modelo de simu-
Medicina assistida por compu- Desenvolvimento de corpo virtual, planejamento
lação da anatomia, fisiologia e propriedades
tação científica cirúrgico e adestramento de corpo médico.
biomecânicas.
Fonte: LNCC (2019).
Também o Centro de Tecnologias Estratégicas do Nordeste (Cetene) de-
senvolve pesquisas relacionadas à microeletrônica para o programa CI-Brasil. O
Laboratório para Integração de Circuitos e Sistemas (Lincs) atua no segmento
de sistemas eletrônicos de alto desempenho, visão computacional e aplicações
específicas de software e hardware. Entre as aplicações em progresso tem-se: i) o
processamento de imagens de trânsito embarcado em câmeras de monitoramento
--- PAGE 44 ---
|
Segmentos tecnológicos e aplicações 589
de tráfego, detectando e classificando automaticamente situações de risco dos tran-
seuntes, com contagem de fluxo de veículos, identificação de congestionamentos
e interrupção de tráfego; ii) o controle automático de iluminação pública, com
programação remota de luminosidade, detecção de falhas e economia de eletricidade;
iii) o monitoramento de qualidade do biodiesel na cadeia produtiva de produção,
armazenamento e distribuição; e iv) o aperfeiçoamento da tecnologia de RFID de
ultrabaixo consumo, frequência 860-960 MHz.
2.4.4 Evolução histórica
O mapeamento do progresso tecnológico no segmento de computadores permitiu
traçar um breve histórico, elaborado a partir dos principais achados e conquistas nos
últimos oitenta a noventa anos. Esse período foi caracterizado por cinco grandes
gerações de computadores, com enquadramento feito a partir do desenho lógico
e dos principais componentes de hardware e de software:
• 1940 a 1956 – uso de linguagem de máquina (0-1) para programar as
válvulas termiônicas conectadas por fios, principais componentes dos
computadores primitivos;
• 1956 a 1963 – conjunto de transistores, diodos, resistores e capacitores em
circuitos, com dispositivo periférico de entrada de dados (cartões perfurados);
• 1964 a 1971 – programação de alto nível Fortran, Cobol, Pascal, C e Basic
em circuitos integrados, com intermediação dos sistemas operacionais;
• 1971 até hoje – programação em alto nível de abstração Python e Java
para desenvolvimento de algoritmos IA executados em microprocessa-
dores, GPU e TPU; e
• futuro imediato – computação em dispositivos quânticos construídos
com uso de nanotecnologia, programados em MS-Q# e QCL (Quantum
Computation Language – concebida por Bernhard Ömer Universidade
de Viena).
QUADRO 6
Linha do tempo na evolução dos computadores
Ano Evento
Kurt Gödel definiu os fundamentos matemáticos da computação com o teorema da incompletude, demostrando a
1931
limitação de obter provas para determinados sistemas formais.
Allan Turing e Alonzo Church formalizaram a elaboração de algoritmo e apresentaram as limitações no processamento
de modelo mecânico de computação.
1936
Konrad Suze, engenheiro alemão, construiu o primeiro computador eletromecânico, com utilização de relés para
processamento e fitas perfuradas para a entrada de dados.
(Continua)
--- PAGE 45 ---
|
590 Do paradoxo das habilidades à superinteligência pós-humana
(Continuação)
Ano Evento
Marinha Americana e Universidade de Havard construíram o computador Mark 1, com base no projeto de Howard
1939-1944
Aiken e no computador analítico de Babbage.
Primeira geração de computadores com grandes dimensões, funcionavam com válvulas eletrônicas, longos fios e
circuitos eletrônicos, eram intensivos em energia, necessitando resfriamento para evitar problemas técnicos devido
1945-1959
ao aquecimento intenso. Os computadores eram reprogramáveis em linguagem de máquina e calculavam em
milésimos de segundo.
Anunciada a criação do primeiro computador eletrônico digital, o Electrical Numerical Integrator and Computer (Eniac)
1946 de grande escala projetado por John Eckert e John Mauchly, com 18 mil válvulas e processamento de quinhentas
multiplicações por segundo.
1947 Primeiro transistor construído por William Shockley, John Bardeen e Walter Brattain.
1958 Primeiro circuito integrado desenvolvido por Jack Kilby, nos Estados Unidos.
Segunda geração de computadores substituiu as válvulas eletrônicas por transistores e os fios de ligação por circuitos
impressos, o que tornou os computadores mais rápidos, menores e mais econômicos. A capacidade de processa-
1960-1964 mento era restrita, com tamanho avantajado, menor consumo de energia, mais rápido e confiável. Os computadores
desta geração calculavam em microssegundos (milionésimos) e eram programados em linguagem assembler, o que
viabilizou sua comercialização.
Terceira geração de computadores foi construída com circuitos integrados, proporcionando maior compactação,
redução dos custos e velocidade de processamento da ordem de microssegundos. A substituição de transistores pela
1965-1969 tecnologia de circuitos integrados (associação de transistores em pequena placa de silício) permitiu a minituarização
dos componentes eletrônicos montados num único chip. Início da utilização de SOs, linguagem de alto nível e
orientada para procedimentos.
Quarta geração de computadores (mainframes) seguiu a tendência de miniaturização de componentes e aperfeiço-
amento da tecnologia de circuitos integrados, otimizando a resolução de problemas, confiabilidade e velocidade da
1970-1981 ordem de nanossegundos (bilionésima parte do segundo). Utilização de linguagem de altíssimo nível, orientada a
objeto e aparecimento dos aplicativos e softwares integrados, como processadores de texto, planilhas eletrônicas,
gerenciadores de banco de dados, gráficos, gerenciadores de e-mail.
1971 Lançamento do primeiro PC para fins educativos, o Kenbak-1 projeto de John Blankebacker carecia de CPU.
Lançamento do PC Altair 8080, equipado com processador CPU Intel 8080.
1975 Primeira geração de supercomputador, mais rápidos, de maior custo e com aplicações especiais. Laboratórios e centro
de pesquisa aeroespacial como a Nasa, além de empresas de altíssima tecnologia, produzem efeitos e imagens
computadorizadas de alta qualidade.
Avanços significativos, surgindo os microprocessadores, os microcomputadores e os supercomputadores. A partir de
1977, ascensão do mercado de microcomputadores em escala comercial até chegar aos micros atuais. O processo de
1975-1977 miniaturização intensificou-se por escalas de integração dos circuitos integrados: large scale of integration (LSI), VLSI
e ultra large scale of integration (ULSI), utilizado a partir de 1980. Nesta geração começa a utilização das linguagens
de altíssimo nível, orientada ao problema.
1976 Steve Jobs e Wozniak da HP lançaram o Apple I e um ano depois o Apple II.
Richard Feynman propôs um modelo básico de computador quântico para executar simulações, durante a Primeira
1981
Conferência em Física da Computação.
David Deutch, Universidade de Oxford, descreveu o primeiro computador universal quântico, capaz de simular
1985
quaisquer outros computadores quânticos.
Peter Shor, do Laboratório Bell da AT&T, desenvolveu o algoritmo capaz de fatorar rapidamente grandes números
1994 inteiros e resolver o problema do logaritmo discreto, podendo teoricamente decodificar encriptação e despertando
grande atenção para os computadores quânticos.
1996 Lov Grover, do Laboratório Bell, desenvolveu o algoritmo de busca de base de dados quântica.
2007 D-Wave iniciou experimentos com o computador quantum-anneling de 28 qubits.
2011 D-Wave anunciou o produto denominado D-Wave One, o primeiro computador quântico disponível comercialmente.
(Continua)
--- PAGE 46 ---
|
Segmentos tecnológicos e aplicações 591
(Continuação)
Ano Evento
D-Wave iniciou computação quântica com 84 qubits.
2012 Fundação da primeira empresa dedicada ao desenvolvimento de software de computação quântica, a 1QB Infor-
mation Technology.
Cientistas conseguem transferir dados por teletransporte de partículas numa distância de três metros, com taxa de
2014
erro zero, um passo vital para a internet quântica.
2015 D-Wave System anunciou ter rompido a barreira dos 1000 qubits de computação quântica.
D-Wave System lançou a comercialização do D-Wave 2000 quantum anneler, com 2000 qubits.
A empresa Atos, líder em transformação digital vendeu a primeira Quantum Learning Machine (QLM) para o Laboratório
Nacional de Oak Ridger, que tem o apoio do Departamento de Energia dos Estados Unidos.
2017 IBM revelou o computador quântico de 17 qubits e anunciou estar trabalhando num de 50 qubits, capaz de manter
estabilidade de estado quântico durante 90 microssegundos.
Microsoft anunciou a primeira linguagem de programação quântica integrada com o Visual Studio, podendo ser
executada localmente ou remotamente com Azure (38 e 40 qubits).
2018 Google anunciou o desenvolvimento de chip quântico de 72 qubits, Bristlecone, um recorde histórico.
2020 Atos vendeu QLM para o Japão.
Elaboração da autora.
Obs.: A sistemática de elaboração de quadro histórico tem sido adotada ao longo do estudo com o intuito de proporcionar uma
visão abrangente da evolução tecnológica, conforme os eventos ocorridos no tempo, sendo gradativamente complementado
à medida que segmentos adicionais são incorporados. O ano ou período de enquadramento dos acontecimentos varia
ligeiramente, dependendo da fonte e das incertezas relacionadas ao registro dos fatos.
2.5 Redes de comunicações
Parte integrante do sistema de veiculação de informação e dados, o sistema de comunica-
ção tem sido significativamente exigido, conforme se desenrola o progresso tecnológico.
A expectativa gira em torno principalmente da capacidade de ofertar serviço de alto
desempenho, com cobertura territorial que permita o acesso de todas as populações.
2.5.1 Conceitos
1) Componentes de rede – camada física, camada de enlace, camada de
rede, camada de transporte, camada de sessão, camada de apresentação
e camada de aplicação, segundo o modelo de interconexão de sistema
aberto (Open System Interconnection – OSI).
2) Redes locais (local area network – LAN) – redes de curto alcance com
banda alta ou baixa, que envolve a camada de enlaces privados, por exem-
plo: data center network (DCN), personal area network (PAN), wireless
personal area network (WPAN), wireless local area network (WLAN).
3) Redes regionais amplas (wide area network – WAN) – redes de baixa potência,
grandes áreas e enlaces públicos, alugados às operadoras concessionárias de
serviços de telecomunicações, por exemplo: distribuída na área metropoli-
tana, metropolitan area network (MAN); e distribuída geograficamente, low
power wide area (LPWA) network.
--- PAGE 47 ---
|
592 Do paradoxo das habilidades à superinteligência pós-humana
4) Tecnologia LPWA, inicialmente observa-se a predominância de tecnologias
proprietárias utilizando espectro não licenciado (por exemplo, LoRa e
SigFox) e, posteriormente, a tendência é de inversão para a dominância
de tecnologias padronizadas 3rd Generation Partnership Project (3GPP)
em espectro licenciado (por exemplo, Narrowband IoT – NB-IoT, long
term evolution for machine – LTE-M). A radiofrequência long rang (LoRa)
juntamente com o Sigfox permitem comunicação em longas distâncias
(3 a 4 km em áreas urbanas e 12 km em áreas rurais) com o mínimo
consumo de energia, cujos parâmetros de comunicação tem por protocolo
o LoRaWAN, que define a arquitetura do sistema essencial para IoT.
PAN, neighborhood area network (NAN) para conexão de pontos com
rede Wi-Fi sem fio, mobile virtual network operator (MVNO) destituído
de infraestrutura de rede móvel sem fio.
Em 2009, o Banco Mundial e a McKinsey10 estimaram o impacto causado
pelas tecnologias de comunicações sobre as atividades socioeconômicas de países
em desenvolvimento e constataram que 10 pontos percentuais (p.p.) de aumento
na penetração de banda larga podem representar 1,38% de crescimento no produ-
to interno bruto (PIB). Enquanto na Europa o acesso domiciliar à internet pode
ultrapassar 80%, no Brasil o indicador é pouco superior a 50%, com velocidade
de 6,8 Mbps, um terço do desempenho correspondente aos países avançados
(Puga e Castro, 2018). A hipótese de cenário ideal aponta para investimentos de
R$ 200 bilhões, para implantação da tecnologia fiber to the home (FTTH), com
cobertura de 90% dos domicílios e velocidade de 100 Mbps no país. Portanto,
a infraestrutura é o aspecto mais desafiador do processo de universalização dos
serviços de comunicações.
TABELA 3
Agrupamento de tecnologias por serviço de banda larga fixa no Brasil
Tecnologias informadas Meios de acesso Proporção de acessos (%)
ATM, fibra, FTTH Fibra óptica 21,4
Cable modem, HFC Cabo coaxial 30,4
DTH, satélite, SAT Satélite –
Ethernet, FR, PLC, xDSL Cabos metálicos 39,5
FWA, MMDS, spread spectrum, Wimax Rádio 7,0
LTE1 LTE –
Fonte: Anatel (2019).
Nota: 1 Tecnologia de rádio terrestre LTE (long term evolution), com adoção recente também usada na telefonia móvel.
Obs.: ATM – asynchronous transfer mode; HFC – hybrid fiber coaxial; DHT – direct to home; FR – frame relay; PLC – power
line communication; xDSL – digital subscriber line; FWA – fixed wireless access; e MMDS – multipoint microwave
distribution system.
10. Para mais informações, acessar: https://documents1.worldbank.org/curated/en/178701467988875888/pdf/102955-WP-
-Box394845B-PUBLIC-WDR16-BP-Exploring-the-Relationship-between-Broadband-and-Economic-Growth-Minges.pdf.
--- PAGE 48 ---
|
Segmentos tecnológicos e aplicações 593
O Brasil é o maior mercado de internet móvel da América Latina, com pouco
mais de 110 milhões de usuários conectados, em 2021, sendo que eram pouco mais
de 80 milhões, em 2017. O total de chips em funcionamento passa dos 200 milhões.
A rede móvel de celular é o segundo segmento mais desafiador para a indústria de
telecomunicações, por exigir o aperfeiçoamento constante das tecnologias associadas
à comunicação sem fio. Provedores de infraestrutura passaram a fornecer serviços
de dados, além da voz, impactando o aumento gradativo da capacidade de rede e
implicando evolução da tecnologia em série de gerações 2G, 3G e 4G. A transmissão
de streaming de áudio e vídeo para múltiplos receptores contribuem para aumento
da carga em rede. Os tipos de tecnologias de redes de comunicação sem fio são: i)
longo alcance – global system for mobile communications/general packet radio services
(GSM/GPRS), universal mobile telecommunications service (UMTS), worldwide
interoperability for microwave access (WiMAX); ii) médio alcance – WLAN, wireless
fidelity – Wi-Fi, light fidelity – Li-Fi, por luz; e iii) curto alcance – entre sensores
ZigBee, Bluetooth, ISA100, RFID, near field communication (NFC).
Segundo o Plano Estrutural de Redes de Telecomunicações (Pert) 2018-2024, a
tecnologia 4G estava presente em 4.775 municípios, aproximadamente 75% do total
de municípios e como a população está concentrada nos centros urbanos, equivale
ao alcance de 95% da população. A cobertura da tecnologia 3G era de 37% da área
(22% dos municípios) e 99% da população. Cerca de 2 mil distritos careciam de
estações de rádio base, o que correspondia a 3,8 milhões de pessoas, sendo que aquelas
sem acesso à internet eram em maior número, 26 milhões de pessoas. A participação
nos serviços prestados distribuída por tecnologias seria de 4G = 69,7%, 3G = 17,4%
e 2G = 12,9%, de acordo com a Agência Nacional de Telecomunicações (Anatel).
GRÁFICO 1
Cobertura municipal e populacional conforme tecnologia de rede Serviço Móvel
Pessoal (SMP)
(Em %)
3
22
75
4G 3G 2G
Fonte: Pert 2019-2024.
--- PAGE 49 ---
|
594 Do paradoxo das habilidades à superinteligência pós-humana
A evolução das tecnologias de rede de telefonia móvel, segundo cada geração,
tem as principais características apresentadas na lista a seguir e na tabela 4.11
1) Década de 1980 – tecnologia 1G com operação analógica e conexão sem
fio; sinal utilizado radiofrequência (FM) e advanced mobile phone system
(AMPS); chamadas de voz com baixo desempenho; aparelhos celulares
pesavam 1 kg, com antena externa e a bateria durava pouco; chamadas
podiam ser decodificadas com desmodulador FM; e pequeno número
de usuários devido ao custo da tecnologia.
2) Década de 1990 – tecnologia 2G com operação digital (comutação) e co-
nexão sem fio; sinal utilizado GSM e code division multiple access (CDMA);
chamadas de voz com algum desempenho, mensagem curta – short message
service (SMS), início da transmissão de dados e da internet móvel; possibi-
lidade de baixar imagens e sons de forma limitada; taxa de dados de 64 a
144 kbps; aparelhos com 130g, antena interna e bateria com autonomia;
transmissão de voz criptografada; e número razoável de usuários devido à
diminuição do custo e à tecnologia de itinerância (roaming).
3) Década de 2000 – tecnologia 3G com operação digital e conexão sem fio;
sinal utilizado GPRS (velocidades de até 144 Kbps), Edge (velocidades
de até 384 Kbps), UMTS Wideband CDMA – WCDMA (velocidades de
até 1,92 Mbps) e high-speed downlink packet access – HSPDA (velocidade
máxima de até 14 Mbps); dado e voz aperfeiçoados, maior velocidade de
conexão, serviço de SMS e de e-mail, videochamadas, transmissão de fotos
e vídeos, TV móvel, serviços de streaming de TV, geolocalização, mapas,
videoconferência, aplicativos de jogos, suporte para aplicativos móveis;
aparelhos com telas sensível ao toque, câmeras, filmadoras e gravadores
de áudio; velocidades de 144 kbps a 21 Mbps e navegação veloz na web;
iPhone (2007) e Android (2008); e as funcionalidades dos smartphones
atraem mais usuários devido à boa relação benefício/custo.
4) Década de 2010 – tecnologia 4G com operação digital e conexão sem fio;
chamadas de voz, mensagem SMS e de e-mail, videochamadas, transmissão
de fotos e vídeos em tempo real, serviços de streaming de TV, TV móvel,
geolocalização, mapas, videoconferência, aplicativos de jogos 3D e alta
definição, suporte para aplicativos móveis; velocidades de 150 Mbps,
latência reduzida para acesso à internet e navegação veloz na web; uso da
tecnologia LTE para melhorar transmissão de dados; e ampliação do uso
de redes sociais com ampla oferta de aplicativos.
11. Disponível em: https://www12.senado.leg.br/noticias/infomaterias/2020/07/novo-patamar-de-telefonia-5g-ainda-
-deixa-duvidas-sobre-inclusao-digital-no-brasil.
--- PAGE 50 ---
|
Segmentos tecnológicos e aplicações 595
5) Década de 2020 – tecnologia 5G com operação digital e conexão sem fio;
chamadas de voz, mensagem SMS e de e-mail, videochamadas, transmissão
de fotos e vídeos em tempo real, serviços de streaming de TV, TV móvel,
geolocalização, mapas, videoconferência, aplicativos de jogos 3D e alta
definição, suporte para aplicativos móveis, realidade virtual e aumentada,
conexão com IoT; velocidades de 1 a 10 Gbps, latência reduzidíssima
de 10 milissegundos; segurança ampliada; e ampliação de opções de uso
com multiplicação de pessoas e coisas.
TABELA 4
Evolução das tecnologias de rede de comunicações
Geração Período Velocidade Tecnologia Característica
1G 1970-1980 14.4 Kbps AMPS, NMT, TACS Serviço de voz.
2G 1990-2000 9.6/14.4 Kbps TDMA, CDMA Serviços de voz e dados.
2.50G a 171.2 Kbps
2001-2004 GPRS Serviços de voz, dados, streaming, e-mail.
2.75G 20-40 Kbps
3.1 Mbps CDMA 2000 Serviços de voz, dados, multimídia, aplicativos, web,
3G 2004-2005
500-700 Kbps UMTS e Edge chamada por vídeo e TV.
14.4 Mbps
3.5G 2006-2010 HSPA Idem 3G com velocidade e mobilidade aumentadas.
1-3 Mbps
100-300 Mbps Alta velocidade, voz de alta qualidade IP, streaming
4G 2010-adiante 3-5 Mbps WiMax, LTE e Wi-Fi multimídia high definition – HD, jogos 3D, vídeo
100 Mbps conferência HD e roaming internacional.
Velocidade super rápida, baixa latência, IoT, segurança
LTE esquema avançado,
5G 2022 1-10 Gbps e vigilância, streaming multimídia HD, condução au-
OMA e NOMA
tomática e aplicativos de cuidados inteligentes saúde.
Fonte: Instituto Eldorado. Disponível em: https://www.eldorado.org.br/blog/redes-moveis-5g-tecnologia-implantacao/.
Obs.: NMT – nordic mobile telefone; TACS – Total Access Communication System; TDMA – time division multiple access; HSPA –
high speed packet access; OMA – orthogonal multiple access; e NOMA – non-orthogonal multiple access.
As empresas que desenvolvem tecnologia 5G são Huawei e ZTE da China,
Ericsson da Suécia, Nokia da Finlândia, Samsung da Coreia do Sul e Cisco dos
Estados Unidos. A rede 5G, concentrada no tráfego de dados, terá velocidade de
10Gbps, isto é, cerca de dez vezes maior que a velocidade da rede 4G. A latência
da 5G será dez vezes menor que na 4G (de trezentas a quatrocentas vezes mais
rápida do que o piscar de olhos), atributo essencial para situações de risco, como
o sistema anticolisão de carros autônomos.
A tecnologia 5G virá em três estágios: banda larga aperfeiçoada, ultra-banda-larga,
massificação da IoT (M2M). Em 2020 espera-se que 50 bilhões de equipamentos estarão
conectados, sendo 15 bilhões com capacidade de vídeo. A conexão entre carros exigirá
comunicação instantânea 5G. A interconexão dos sensores poderá fazer uso das redes
privadas de longa distância (LPVANs), com base na rede 4G e 5G de alta velocidade e
menor latência. O padrão NB-IoT banda estreita em IoT, com base na rede 4G, será
destinado para grandes áreas a custo mais baixo do que os das redes convencionais.
--- PAGE 51 ---
|
596 Do paradoxo das habilidades à superinteligência pós-humana
2.5.2 Tipos de redes aplicadas em serviços e indústria
Os fatiamentos (slicing) ou segmentações de rede, para oferta de serviços diferen-
ciados ou desenvolvimento de aplicações da tecnologia 5G definidas pela União
Internacional de Telecomunicações (UIT) foram:
• enhanced mobile broadband (eMBB) – banda larga móvel aprimorada,
para disponbilização de banda larga em áreas densamente povoadas,
atendendo serviços de streaming, realidade virtual e aumentada;
• ultra reliable low latency communication (URLLC) – ultrabaixa latência
e confiabilidade, rede de sensores de baixa potência, para as aplicações
críticas como energia, água e segurança pública; e
• massive machine type communication (mMTC) – para comunicação entre
máquinas e grande número de dispositivos, como veículos autônomos,
telemedicina e processos industriais autônomos.
FIGURA 5
Fatiamento da rede ponta a ponta por setores, com base na infraestrutura física
LTE RAN-RT
eMBB RAN-NRT
C ó â cu m lo e s r a d , e realidade virtual 5G C A a C che
WiFi MCE CP UC
LTE RAN-RT
RAN-NRT
u V R eí L c L u C los, 5G R C A ac N h - e NRT CP CP
torres
MCE
LTE
RAN-RT
mMTC 5G RAN-NRT CP UC
Residências,
fábricas
WiFi MCE
RAN Escritório Central DC Local DC Regional DC
Switch Switch
Infraestrutura física
Fonte: Vermesan e Bacquet (2018, p. 62).
A 5G promete conectar dispositivos ligados por IoT em banda larga e alta
velocidade (10Gbps), sem cabeamento. A IoT tátil requer redes de comunicações
5G, com latência menor do que 1 ms entre sensores e atuadores, mesmo conside-
rando o tempo adicional de encriptação. A rede 5G terá capacidade 1 mil a 5 mil
vezes maior do que 4G e 3G atualmente, podendo fazer uso das seguintes faixas
de frequência: 26, 28, 37, 39 GHz; 3,3 e 5 GHz; 1 e 7 GHz; e 600 MHz a 900
MHz. As faixas em estudo para a rede 5G pelos diversos blocos de países foram
identificadas durante a Conferência Mundial de Comunicação de Rádio, em 2019,
conforme figura 6.
--- PAGE 52 ---
|
Segmentos tecnológicos e aplicações 597
FIGURA 6
Limites de variação de frequência estudados na Conferência Mundial de Comunicação
de Rádio
-10 GHz 10-20 GHz 20-30 GHz 30-40 GHz 40-50 GHz 50-60 GHz 60-70 GHz 70-80 GHz 80-90 GHz
Frequências propostas por grupos regionais
Europa
24,5 27,531,8 33,4 40,5 43,5 45,5 48,9 66 71 76 81 6
48,6 50,2
CIS (RCC)
25,5 27,531,8 33,4 39,5 41,5 45,5 47,5 50,4 52,8 66 71 76 81 86
Arábia (ASMG) Nenhuma banda de frequência submetida, opiniões que acima de 31 GHz seria desejável
África (ATU)
26,5 27,5 45,5 50,2
7,057 10,5 17,3 23,6 24,25 31,8 33,4 37 43,5 50,4 52,6 55
África (ATU) 23,15 23,6 31,8 33 47,2 50,2
10 10,45 24,25 27,5 29,5 37 40,5 45,5 47 50,4 52,6 59,3 76
47,2 50,2
Ásia-Pacífico (APT)
25,25 25,5 31,8 33,4 39 47 50,4 52,6 66 76 81 86
-10 GHz 10-20 GHz 20-30 GHz 30-40 GHz 40-50 GHz 50-60 GHz 60-70 GHz 70-80 GHz 80-90 GHz
Frequências para estudo
24,25 27,5 31,8 33,4 37 43,5 45,5 50,2 50,4 52,6 66 76 81 86
Fonte: Vermesan e Bacquet (2018, p. 64).
2.5.3 Interoperabilidade multinível
Metaprotocolos de comunicação são necessários para viabilizar a conexão de
múltiplos níveis de plataformas heterogêneas de ecossistemas em redes. Na intero-
perabilidade sintática, o acesso à diversidade de recursos de gateways, dispositivos
e objetos do mundo real e virtual requer protocolos padronizados para que tudo
esteja integrado. No entanto, apesar de todos os esforços para achar ontologias
comuns, a interoperabilidade sustentada pela padronização semântica, exige que
novas ontologias sejam definidas no mundo real para empregos e endereçamentos
específicos (Vermesan e Bacquet, 2018, p. 14-15).
A perspectiva de desenvolvimento de IoT envolvendo cadeias globais trouxe
um novo motivo de preocupação, desta vez, com a capacidade de identificação de
cada dispositivo e respectivo endereço na rede. O protocolo IPv6 tem sido desen-
volvido desde 2008 pela força-tarefa Internet Engineering Task Force (IETF) para
atender a essa demanda quase explosiva por um padrão planetário:
1) IPv4 – protocolo de identificação utilizado desde a criação da internet,
constituído de endereços numerados no padrão de 32 bits (4 bilhões =
232 de combinações possíveis) dos dispositivos conectados à rede, que
permite a comunicação e a troca de informações entre smartphones e
computadores, por exemplo.
--- PAGE 53 ---
|
598 Do paradoxo das habilidades à superinteligência pós-humana
2) IPv6 – o esgotamento da capacidade de numeração do IPv4 fez com
que, em 2012, fosse oficializado um novo protocolo, para englobar
todos os dispositivos ligados em IoT – dessa vez com padrão 128 bits
(340 undecilhões = 2128 combinações, oito blocos de 16 bits) –, o que
modifica sobremaneira a forma de dimensionar os projetos, que passam
a ser calculados por quantidade de redes a serem ofertadas, em vez de
quantidade IPs identificadores de máquinas.
3) Roaming permanente – a interconexão perene entre os dispositivos de
IoT, por vezes localizados em diferentes países, pode afetar o desempenho
da rede, além de envolver a questão arrecadatória e, por isso, tem sido
objeto de análise por parte da Anatel. A tecnologia e-SIM pode ajudar
na resolução do problema, pois configura o dispositivo via software sem
prejudicar a escalabilidade de produção.
Novos modelos de negócio e de desenvolvimento social patinam com uma
desaceleração do crescimento no acesso à internet. A taxa de expansão foi de 3,4%
entre 2009 e 2010, 3,0% em 2014, 2,7% entre 2015 e 2016, e 2,1% entre 2016 e
2017, segundo a UIT. A previsão da Organização das Nações Unidas (ONU) de
que metade da população mundial estaria ligada à internet, em 2017, fica adiada
para 2019. Nos países desenvolvidos, 82,4% da população têm computadores e
84,4% acesso à internet, enquanto nos países em desenvolvimento as proporções
são 35,5% e 42,9% respectivamente. No Brasil, em 2016, as taxas foram 51% e
52,4%, respectivamente. Nos países pobres, os números são extremamente desa-
lentadores, 8,3% e 14,7%.12
2.5.4 Implantação da rede 5G
Em 2019, o Ministério da Ciência, Tecnologia, Inovações e Comunicações (MC-
TIC) elaborou a Estratégia Brasileira de Redes de Quinta Geração (5G) e subme-
teu à consulta pública. Numa breve contextualização internacional, apresentou a
padronização internacional para telecomunicação móvel (IMT-2020), definida
pela UIT, que estabelece como requisito para a rede 5G: velocidades de 100 Mbps
até 20 Gbps, latência de 4 milissegundos e a capacidade de conectar 1 milhão de
dispositivos por km2. Na Alemanha, o leilão arrecadou € 5 bilhões nas faixas de
2/3,5Ghz; enquanto na China, os investimentos devem chegar a cerca de US$ 410
bilhões em uma década; e no Japão, as operadoras devem investir aproximadamente
US$ 15 bilhões. No Brasil, o valor pode chegar a R$ 50 bilhões.
Além disso, a Estratégia 5G apresentou cinco eixos de atuação, quais sejam:
i) disponibilização de radiofrequência, levando em conta os desafios de ampla
12. MOREIRA, A. Acesso à internet desacelera e desigualdade permanece. Valor Econômico, 19 out. 2018. Disponível
em: https://valor.globo.com/empresas/coluna/acesso-a-internet-desacelera-e-desigualdade-permanece.ghtml.
--- PAGE 54 ---
|
Segmentos tecnológicos e aplicações 599
cobertura de massiva quantidade de dispositivos conectados em IoT; ii) outorga
e licenciamento, visando à expansão e ao compartilhamento da infraestrutura,
harmonização dos regramentos estaduais e municipais sobre telecomunicações e
modelagem de arrecadação tributária sobre a renda das operadoras; iii) pesquisa,
desenvolvimento e inovação (PD&I), fortalecendo ecossistemas e a capacidade
produtiva do país; iv) aplicações, fomentando os setores automotivo, análise de
dados, fábricas do futuro, saúde e cidades inteligentes; e v) segurança, incluindo
certificações e homologações. Durante a elaboração do edital, as frequências sele-
cionadas para a rede 5G no Brasil foram 26,0 GHz, 3,5 GHz, 2,5 GHz e 700,0
MHz. As questões mais sensíveis que permearam as tratativas para o certame foram
analisadas no capítulo 3.
2.6 Dispositivos e gateways
Enquanto elemento-chave para a evolução tecnológica em andamento, o segmento
de microeletrônicos tem surpreendido pela movimentação de cunho geopolítico
estratégico e pela mobilização de investimentos pesados na expansão de capacidade
de produção instalada.
2.6.1 Conceitos
1) Dispositivo – parte de equipamento de comando com capacidade de comuni-
cação, sensorial, atuadora, captadora de dados, processadora e armazenadora.
2) Gateway – interface entre múltiplas tecnologias para conversão de pro-
tocolos de comunicação, com processadores para realização de tarefas
específicas, como o acesso seguro ao dispositivo, ampliando a funciona-
lidade limitada dos sensores.
3) Atuador – dispositivo capaz de produzir movimento mecânico por con-
versão de energia elétrica, hidráulica e pneumática.
4) Sensor – dispositivo capaz de responder a estímulos de calor, mecânico,
luminoso, sonoro, magnético etc. e produzir um sinal.
5) Smart-tag – etiqueta eletrônica que incorpora RFID, permitindo rastre-
amento e armazenamento de dados com comunicação sem fio.
6) Smart-dust – poeira de chips inteligentes com milímetros de diâmetro e
alimentados por energia fotovoltaica.
A camada de base de sistemas inteligentes é equipada com dispositivos e gateway,
sendo que a ITU (2012) classificou os dispositivos em quatro tipos: i) os disposi-
tivos que carregam dados estão associados a objetos e conectam indiretamente os
objetos físicos com a rede de comunicações; ii) os dispositivos captadores de dados
referem-se aos dispositivos de leitura/escrita com capacidade para interagir com
--- PAGE 55 ---
|
600 Do paradoxo das habilidades à superinteligência pós-humana
os objetos, diretamente ou indiretamente; iii) os dispositivos sensores e atuadores
podem detectar ou mensurar informações relacionadas ao ambiente e converter em
sinais eletrônicos digitais que, por sua vez, podem ser convertidos em operações,
geralmente comunicando-se via gateway com ou sem fio; e iv) os dispositivos gerais
são capazes de comunicação e processamento que se ligam às redes de comunicação
com ou sem fio.
FIGURA 7
Integração de componentes
Mundo físico Mundo da informação
Rede de comunicação Dispositivo
a
Portão
Coisa física
Coisa virtual
b
Comunicação
c Mapeamento
a Comunicação via portão
b Comunicação sem portão
c Comunicação direta
Fonte: ITU (2012).
Como pode ser observado na figura 7, a comunicação entre os dispositivos
pode ocorrer com rede de comunicação via gateway (caso a), com rede de comu-
nicação sem gateway (caso b) e diretamente (caso c), ou seja, sem uso de rede e
gateway. A combinação dos três casos anteriores também é possível em rede local.
As interações ocorrem no mundo real entre objetos ou coisas e no mundo da
informação entre objetos ou coisas virtuais.
2.6.2 Sensores
Os sensores quanto a sua natureza podem ser eletromecânicos, eletromagnéticos e
bioquímicos. Tipos de sensores, de acordo com as características elétricas: i) ativos –
funcionam como gerador (termoelétricos, piezoelétricos, piroelétricos, fotovoltaicos,
eletromagnéticos, efeito hall – transdutor de energia); e ii) passivos – funcionam
como resistor (resistivos, capacitivos, indutivos, ressonantes).
1) Sensores térmicos – reagem à temperatura, resistores de temperatura,
termômetros de mercúrio com escala informativa ou transdutora.
--- PAGE 56 ---
|
Segmentos tecnológicos e aplicações 601
2) Sensores de posição – monitoramento de peças, como tornos automáticos
industriais, alinhamento de antenas parabólicas e satélites. Sensores de
proximidade ou presença.
3) Sensores piezoelétricos – deformação mecânica provoca descarga elétrica,
sendo causado por força, pressão, aceleração, umidade, ultrassom.
4) Sensores piroelétricos – deformação por variação de temperatura e alte-
ração de polarização, utilizados em sistemas de alarme.
5) Sensores fotovoltaicos – para medição da intensidade luminosa, fotodio-
dos, fototransistores, optoaclopadores. Light Dependent Resistor (LDR),
cuja resistência é diminuída na presença de luz.
6) Sensores de efeito hall – transformam uma forma de energia em outra
em presença de campo magnético, sendo empregado no giro de eixo
em comando de válvula, que corresponde ao ciclo completo de motor.
7) Sensores de velocidade – medem velocidade interna de motores e máquinas
industriais, garantindo a frequência de corrente alternada. Tacogerador:
transdutor elétrico linear, gerador de corrente contínua, com campo
fornecido por imã. Interruptor de lâminas, de luz, de reflexão.
8) Sensores de vazão – para medição de fluxos líquidos em tubulações,
como sensor de turbina por roda dentada, por sensor de desnível de
coluna de mercúrio para medição de diferença de pressão pelo aumento
ou diminuição de seção de tubulação.
9) Sensores resistivos – negative temperature coefficent (NTC) e positive
temperature coefficient (PTC) (coeficiente de temperatura negativo/
positivo) para medida de temperatura do ar na entrada do motor e da
água de refrigeração.
10) Sensores capacitivos – osciladores de capacitância elétrica geradora de
campo eletrostático.
11) Sensores indutivos – ímã envolto em bobina e fluxo magnético induzido
por corrente em roda dentada.
Os sensores podem ser classificados quanto: i) ao uso (industriais, domés-
ticos etc.); ii) à evolução (primeira geração, geração avançada); iii) à integração
(isolados, enxame – swarm); iv) ao material (orgânicos, heterogêneos integrados,
polímeros cuja condutividade elétrica depende do meio físico-químico no qual
se inserem); e v) ao substrato de impressão (serigrafia em camadas de circuitos
eletrônicos executada em plástico, vítreo, cerâmico, compósito, metálico ou fatia
--- PAGE 57 ---
|
602 Do paradoxo das habilidades à superinteligência pós-humana
fina de material semicondutor e cristal de silício no qual circuitos integrados são
construídos – Wafer Electronic-Grade Silicon).
2.6.3 Microprocessadores e sistemas microeletromecânicos
Os microssistemas micro-electro-mechanical system (Mems) são sistemas miniatu-
rizados que integram sensores eletromecânicos em chips, cujas funcionalidades
incluem tratamento numérico e comunicação com o meio, capturando, transmi-
tindo e amplificando sinais. Os sistemas podem ser embarcados ou embutidos em
microprocessadores de forma compacta, de alto desempenho e de baixo consumo.
1) System-on-a-chip (SoC) – circuito integrado de componentes que pode
exigir o desenvolvimento de semicondutores específicos, customizados ou
genéricos, conforme a ordem de grandeza dos lotes a serem fabricados.
2) Microcontroladores – constituem objetos inteligentes com protocolos
embarcados e capacidade para processamento e comunicação, sendo
fabricados nas versões 8 bits e 32 bits.
3) Application specific integrated circuit (Asics) – os circuitos integrados de apli-
cações específicas têm evoluído de forma expressiva. A terceira geração, deep
neural network Asic, é alimentado por fótons. A segunda geração de Asic,
TPU, foi criada pela Google em 2016. A primeira geração de Asic, também
denominado SoC, realizava tarefas com alta velocidade em ambientes
pré-definidos e incluía microprocessadores internos, memórias (ROM,
RAM, EEPROM, flash) e arranjo de transistores (matriz de portas) cus-
tomizáveis de acordo com interconexões definidas pelo cliente.
a) Tensor processing unit (TPU) – a unidade de processamento tensor
constitui-se de circuito Asic de segunda geração com acelerador
de IA, desenvolvido especificamente pela Google para aumentar a
carga de trabalho de redes neurais com aprendizagem de máquina.
A computação algébrica linear vetorial e matricial, utilizada frequen-
temente em aplicativos de ML processados em CPUs, unidades de
processamento gráfico (GPUs) e TPUs, tem desempenho intensi-
ficado com uso de TensorFlow da Cloud TPU;
--- PAGE 58 ---
|
Segmentos tecnológicos e aplicações 603
FIGURA 8
Bloco de construção de Cloud TPU com quatro chips
Fonte: Google.
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
b) field programmable gate array (FPGA) – arranjo de células lógicas
configuráveis formando milhões de blocos lógicos contidos em cir-
cuito integrado único com células capazes de implementar funções
lógicas e roteamentos de comunicação entre essas;
FIGURA 9
Esquema simplificado de FPGA
Fonte: Costa (2009) apud Coutinho (2019).
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
c) configurable logic block (CLB) – matriz bidimensional de blocos
de entrada/saída e chaves de interconexão que formam canais de
roteamento entre linhas e colunas dos blocos lógicos;
--- PAGE 59 ---
|
604 Do paradoxo das habilidades à superinteligência pós-humana
FIGURA 10
Estrutura de CLBs inseridas em interconexões programáveis linha/coluna
Fonte: Floyd (2009) apud Coutinho (2019).
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
d) digital signal processing (DSP) – blocos específicos (microproces-
sadores) para processamento de sinais digitais de áudio e vídeo,
offline ou online;
e) programmable logic design (PLD) – dispositivos lógicos programá-
veis que incorporam as tecnologias de computação reconfiguráveis
pelo usuário;
f) complex programmable logic device (CPLD) – dispositivo lógico
programável com características intermediárias entre PLD e FPGA,
cujo bloco principal (macrocélula) contém as expressões da lógica
de implementação;
g) general purpose processor (GPP) – dispositivo caracterizado pela
maior flexibilidade, apesar da menor velocidade para realização de
tarefas complexas; e
h) hardware description language (HDL) – linguagem de descrição de
hardware utilizada na primeira fase de especificação de desenho
de circuitos, como very high speed integrated circuits VHSIC-HDL
(VHDL) e Verilog.
--- PAGE 60 ---
|
Segmentos tecnológicos e aplicações 605
2.6.4 Baterias
Os dispositivos inteligentes e microeletrônicos são alimentados por energia limitada
à baixa intensidade por baterias de indução eletromagnéticas, nas quais a criação
de corrente ocorre a partir de campo magnético. Existe a conversão de energia
existente no ambiente em energia elétrica para alimentar dispositivos autônomos,
denominada colheita de energia (energy havesting), que consiste no aproveitamen-
to de pequenas quantidades de energia (campos eletromagnéticos, gradientes de
temperatura e de salinidade) durante o processo de manufatura aditiva (também
conhecido como impressão 3D).
2.6.5 Flexotrônica (twistronic e spintronic)
A flexotrônica ou flextrônica é um novo ramo tecnológico que associa ciência
dos materiais e eletrônica. Os componentes utilizam materiais semicondutores
como molibdenita e grafeno dispostos em camadas monoatômicas sucessivas.
As características ópticas, mecânicas, magnéticas e elétricas desses materiais são
ajustadas com pequenas mudanças nos ângulos de disposição entre camadas. A
combinação dos cristais desses materiais permite que novas propriedades emerjam
do alinhamento rotacional.
O efeito prático dessa tecnologia seria a confecção de material único ou sin-
gular, que teria a possibilidade de adquirir diferentes qualidades com apenas alguns
giros entre as camadas microscópicas que o compõe, podendo assim substituir a
funcionalidade de circuitos eletrônicos construídos a partir de diversos compo-
nentes, tais como: isoladores, semicondutores, condutores e materiais magnéticos.
Os semicondutores de banda larga, wide band gap (WBG), como o carbeto
de silício e o nitreto de gálio apresentam propriedades especialmente úteis com o
emprego da flextrônica, sendo materiais semicondutores com lacuna de banda (gap)
de energia. O gap medido em eletrovolts tem amplo intervalo de banda (2 a 4 eV)
nos WGB, portanto, maior do que nos semicondutores convencionais na faixa de 1 a
1,5 eV. Os WGB têm propriedades que suportam maiores temperaturas, frequências
e tensões, o que permite obter componentes eletrônicos menores, mais rápidos e
mais confiáveis. O alto custo de produção dos materiais WBG tende a ser revertido
com o tempo e pode ser compensado pela economia de energia que proporcionam.
2.6.6 Política para microeletrônica
O estudo do Banco Nacional de Desenvolvimento Econômico e Social – BNDES
(Rivera et al., 2015) fez uma avaliação sobre os avanços e desafios da política in-
dustrial brasileira para microprocessadores, com base em pesquisa com fabricantes
e projetistas do setor. A indústria, considerada estratégica, tem representantes
que poderiam explorar nichos de valor agregado mais elevado e competir a nível
--- PAGE 61 ---
|
606 Do paradoxo das habilidades à superinteligência pós-humana
mundial nas áreas de fotônica e eletrônica orgânica, assim como, em segmentos
mais maduros de Asic e RFID. A implantação da IoT no mercado nacional pode
alavancar os negócios e fomentar pesquisas em campos inovadores como eletrônica
híbrida, novos materiais e microfluídica. Para isso, o fortalecimento da indústria
dependeria de maior interação entre os segmentos de projeto (design) e fabricação.
No mundo, o modelo verticalizado (projeto, fabricação e comercialização)
da indústria de microeletrônicos (Integrated Design Manufacturer – IDM) esteve
em voga na década de 1960, atendendo às aplicações militares e aeroespaciais.
Posteriormente, a cadeia adotada passou a ser mais fragmentada, abrangendo
alguns segmentos, tais como: i) as empresas fornecedoras de ferramentas de pro-
jeto (electronic design automation), que produzem softwares para criação, análise e
simulação de microprocessadores; ii) as casas de desenho prestadoras de serviços
(design houses), que projetam os circuitos eletrônicos; iii) as empresas de projeto
proprietárias de circuitos integrados (fabless), que terceirizam a fabricação; e iv) as
fábricas de montagem/encapsulamento e testes (foundries) dos circuitos integrados.
Apesar da defasagem tecnológica em relação ao mercado internacional, a
política industrial brasileira sempre considerou o segmento de microeletrônicos
estratégico, devido ao efeito multiplicador da tecnologia capaz de beneficiar todos
os segmentos da manufatura.
Uma breve retrospectiva histórica mostra que, em 1975, a Philco fundou a
primeira fábrica de montagem e testes de microeletrônicos no Brasil. O Centro
de Pesquisa e Desenvolvimento da Telebras (CPqD) surgiu na década de 1980 e
começou a projetar os primeiros circuitos integrados. Também nessa época surgi-
ram Itautec, SID e Elebra. Em 2002, o Plano Nacional de Microeletrônica teve
por objetivo atrair projetos de circuitos integrados para o país (P&D, empresas,
RH); a fabricação front-end (Centro Nacional de Tecnologia Eletrônica Avançada –
Ceitec); e o encapsulamento back-end (componentes montados internamente com
acompanhamento do processo tecnológico). A capacidade de produção do Ceitec,
criado em 2008, atingiu a marca de 10 milhões de chips encapsulados em 2017,13
com agregado de 110 milhões de chips padronizados entre 2012 e 2019.
Ademais, a prioridade estratégica vislumbrada pelo estudo de Rivera et al.
(2015) foi no segmento de componentes com fábricas de médio porte para aplica-
ções específicas, do tipo Asic e SoC, com maior potencial de absorção pela indústria
doméstica. Apesar do faturamento do setor ter alcançado R$ 2 bilhões em 2019,
com 2 mil empregados, o déficit comercial seria da ordem de US$ 6 bilhões em
13. Para mais informações, acessar: http://www.ceitec-sa.com.br/pt/quem-somos/historico.
--- PAGE 62 ---
|
Segmentos tecnológicos e aplicações 607
semicondutores e US$ 36 bilhões em toda a indústria eletrônica, em 2015, segundo
a Associação Brasileira de Semicondutores (Abisemi).14
As empresas representivas do segmento são/foram: i) fabricação – Unitec
Brasil (Ribeirão das Neves, Minas Gerais); Ceitec (Porto Alegre, Rio Grande do
Sul) e BrPhotonics (CPqD e GigOptix); ii) encapsulamento – SID Microeletrônica,
Itaucom, Smart, HT Micron, Multilaser; e iii) projeto – centros cativos (Freescale,
Science and Technology Institute – STI e Jasper/Cadence), fabless (BrPhotonics/
CPqD, Silicon Reef, Ceitec, Santa Maria Design House – SMDH), fabless vertica-
lizada (Eldorado, LSi-Tec, Von Braun, Centro de Tecnologia da Informação – CTI,
Cetene) e semiconductor intellectual property (Chipus, Idea!).
O efeito da pandemia afetou a indústria mundial de chips, que teve demanda
aquecida concentrada em computadores pessoais e telefones móveis. A paralização
de linhas de produção de outros setores por falta de microprocessadores tem levado
governos a rediscutir modelos de produção, com potencial de reversão da tendência
de terceirização concentrada em poucos conglomerados asiáticos de tecnologia.
Os países-membros da UE comprometeram-se a unir forças para impulsionar
a indústria de componentes e um pacote de US$ 50 bilhões foi anunciado nos
Estados Unidos para empresas fabricarem chips no país. Projeções da Internatio-
nal Data Corporation (IDC)15 estimam que houve avanço da receita de US$ 464
bilhões no mercado mundial, em 2020, aumento de 10,8% em relação a 2019. A
projeção para 2021 é de crescimento de receita de 12,5% (US$ 522 bilhões), com
o segmento de PCs crescendo 18,0%, cerca de 360 milhões de unidades, mesmo
com a escassez de chips.
A Taiwanesa Taiwan Semiconductor Manufacturing Company (TSMC) tem
registrado vendas recordes em 2021, com expectativa para aumento de 20% da
receita impulsionada pela demanda global sustentada por semicondutores. A em-
presa TSMC é a maior fabricante de chips por encomenda do mundo, com cerca de
24% do mercado mundial em 2020 e anunciou investimentos US$ 100 bilhões no
plano de expansão da empresa, para atender clientes como a Qualcomm, Nvidia,
AMD, Apple e Intel. A nova fábrica será no Japão para a linha de produtos mais
tradicionais, como dispositivos domésticos e componentes de carros. O valor de
mercado da TSMC chega a US$ 550 bilhões.
Os Estados Unidos foram responsáveis por apenas 12% da fabricação mundial
em 2020, depois de terem alcançado a participação de 37% na década de 1990.
Contudo, novas fábricas estão sendo planejadas, principalmente no Arizona, onde
14. Para mais informações, acessar: https://abisemi.org.br/abisemi/noticia/28/industria-de-semicondutores-no-brasil-
-tem-faturamento-de-r$-2-bilhoes,-diz-abisemi.
15. Para mais informações, acessar: https://www.printedelectronicsnow.com/contents/view_breaking-news/2021-05-11/
idc-worldwide-semiconductor-revenue-grew-108-in-2020-to-464-billion/.
--- PAGE 63 ---
|
608 Do paradoxo das habilidades à superinteligência pós-humana
terão que captar água do lençol freático para viabilizar a produção. Os subsídios
e investimentos do governo norteamericano para o segmento industrial podem
chegar à centena de bilhões de dólares em dezenove fábricas para os próximos dez
anos, dobrando a capacidade atual do país.
No Brasil, deve haver catorze empresas montadoras de dispositivos de arma-
zenamento e conectividade, mas o país depende de importações de placas de silício.
As fabricantes Ceitec e Unitec tentaram produzir chips no país, mas vivenciam
declínio da produção de componentes de alta precisão. A estatal Ceitec foi criada
em 2008 em Porto Alegre para a produção de chips e recebeu R$ 800 milhões em
aporte, mas demanda R$ 80 milhões a.a. para cobrir despesas. Em Ribeirão das
Neves, a Unitec, criada em 2012, recebeu cerca de R$ 1 bilhão (60% em aporte
direto e 30% em financiamentos), mas hoje luta para pagar passivos de R$ 690
milhões, inclusive dívidas trabalhistas e de água. O investimento básico para uma
nova linha de produção chega a R$ 10 bilhões (Braun, 2021).
3 COMPUTAÇÃO COGNITIVA, IA E ROBÓTICA
Este tópico desperta especial interesse daqueles que acompanham o desenrolar
da revolução tecnológica, devido aos expressivos resultados obtidos nas pesquisas
com as TICs e que abrangem múltiplas disciplinas. Portanto, é parte essencial e
imprescindível deste trabalho, que contou com empenhados esforços no sentido
de esclarecer onde estamos, como chegamos até aqui e o que precisamos fazer para
avançar de forma segura.
3.1 Conceitos
1) Computação cognitiva – Ciência da computação orientada pela aquisição de
conhecimento, constituída de algoritmos autoprogramados e capazes de simular
o pensamento humano ao processar informações para a tomada de decisão.
2) IA – Trata-se do conjunto de tecnologias com a habilidade de realizar
tarefas que exigiriam a inteligência humana, como a percepção visual, o
reconhecimento de discursos e a tradução de línguas, segundo definição
dada pelo governo britânico no Plano Estratégico da Indústria.
3) Aprendizagem de máquina – Termo cunhado por Arthur Samuel em 1959;
trata-se da tecnologia de IA, que permite aos computadores aprenderem e
aperfeiçoarem-se com a experiência, sem passarem por programação explícita.
Para isso, quantidade massiva de dados deve ser fornecida para processar
previsões e resolver problemas, com base em métodos estatísticos que
permitam extrair procedimentos factíveis para a tomada de decisão efetiva.
--- PAGE 64 ---
|
Segmentos tecnológicos e aplicações 609
4) Aprendizagem profunda – Técnica de implementação de ML, que utiliza
variação de rede neural surgida a partir de meados da década de 2000,
com muitas camadas artificiais de neurônios para resolver problemas
mais complexos, bastante utilizada para classificar informações de ima-
gens, textos e sons. Para viabilizar o reconhecimento de padrões precisos
e complexos no conjunto de dados, a rede artificial de aprendizagem
profunda pode conter mais de uma centena de camadas, cada qual com
muitas unidades neuronais de processamento.
5) Rede neural artificial16 – Corresponde a um tipo de aprendizagem de
máquina inspirada no cérebro humano, composta de nós processadores
ou neurônios artificiais conectados entre si em camadas. Cada nó recebe
dados de diversos nós acima e envia dados para nós abaixo. Os nós ane-
xam um peso ao dado que recebem e atribuem um valor ao dado. Caso
o dado não atinja determinado limite, não será repassado a outro nó.
Tanto o peso quanto o limite são ajustados quando o algoritmo é treinado
até que dados de entrada semelhantes resultem em saídas consistentes.
6) Categorias de IA quanto à realização de tarefas: i) sistema de IA geral
(general AI) – com capacidade de realizar grande número de tarefas em
ambientes variados, simulando o intelecto humano convincentemente
e com potencial para ultrapassá-lo; e ii) sistema de IA obtusa ou estreita
(narrow AI) – equipada com habilidade para desempenhar tarefas espe-
cíficas, podendo, eventualmente, ultrapassar a habilidade humana na
realização de serviço que exija conjunto de tarefas específicas.
7) Concepção de IA no sentido forte (strong AI) – Almeja uma IA com ampla
capacidade de raciocínio, pensamento e aprendizado, funcionando como
mentes artificiais subjetivas, que poderiam ser criadas e classificadas
como autoconscientes; portanto, capazes de agir como humanos. Para alcançar
esse objetivo, surgiu a ideia de emulação da inteligência natural com o estudo
dos processos de raciocínio humano pela psicologia e pela neurociência.
8) Concepção de IA no sentido fraco (weak AI) – Entende a IA como uma
simulação limitada, restrita ou estreita do pensamento humano, que
aparenta comportamento inteligente, mas sem capacidade cognitiva a
respeito das ações realizadas. Sistemas de IA fracos desempenham tarefas
específicas em domínios bem-definidos, como o reconhecimento de
imagem, fala, tradução de idiomas, jogos e veículos autônomos.
9) Visão computacional ou análise visual (visual analytics) – Algoritmos
preparados para detecção de padrões e imagens, utilizando interface
16. Em inglês, artificial neural network.
--- PAGE 65 ---
|
610 Do paradoxo das habilidades à superinteligência pós-humana
visual, computação cognitiva e aprendizado profundo, com o objetivo
de analisar vídeos e imagens.
10) Reconhecimento de imagem (image recognition) – Ocorre por meio de compa-
ração de padrões geométricos, capturados em fotos, ou desenhos ou retratos.
11) Processamento de linguagem natural (PLN) – Entendimento da lingua-
gem decorrente da interação entre máquina e homem, reconhecendo
a voz ou os sentimentos, gerando diálogos ou obtendo informações,
entendimento e reposta de mensagens.
12) Inteligência aumentada e superinteligência (augmented intelligence,
superintelligence) – Associadas às soluções que permitem a análise de
grande volume de dados, bem como a realização de tarefa da IA geral
e de cognição da IA forte, com potencial para suplantar a capacidade
da inteligência humana ou para trabalhar em conjunto com o humano
(human-centered partnership).
13) Métodos de aprendizagem da IA – Utilizados para a redução de dimensiona-
lidade de dados, preservando informações relevantes, aprendizado supervisio-
nado, semissupervisionado com uso de dados semiestruturados, aprendizado
por reforço e aprendizado sem supervisão, para dados não estruturados.
14) Aprendizado por reforço (reinforcement learning) – Crítico externo avalia
a resposta dada pela rede neural a estímulo determinado, com agente
inteligente aprendendo a partir da interação com o ambiente para treina-
mento contínuo de tentativa e erro, fruto do aprendizado experimentado
no passado e obtenção do melhor resultado para aplicação futura.
15) Aprendizado sem supervisão (unsupervised learning) – Sem interferência
de agente externo, apenas auto-organização do sistema/rede ou autoapren-
dizado de máquina, sem que os dados de entrada tenham sido rotulados
previamente ou instruções tenham sido programadas pelo operador e
repassadas ao sistema, que funciona com base na identificação de seme-
lhanças e reage positiva ou negativamente conforme o resultado alcançado.
16) Aprendizagem supervisionada (supervised learning) – Agente externo
indica a resposta correta para determinado estímulo de entrada à rede
neuronal, geralmente dados rotulados ou identificados.
17) Disciplinas sinérgicas a IA – Utilizadas na ciência da computação, na
estatística, na engenharia e na ciência cognitiva. A tecnologia de ML/IA
suporta a IoT, a big data analytics e a comunicação avançada.
--- PAGE 66 ---
|
Segmentos tecnológicos e aplicações 611
18) Emprego de IA – Resolução de problemas clássicos, tipo: i) classificação
de dados em categorias e agrupamentos (clusters), com aplicações no re-
conhecimento de imagens, diagnósticos médicos e engenharia genética;
ii) relações causa-efeito, com reconhecimento de padrões de dados que
possam determinar uma regra ou função de ação-reação; e iii) previsões,
utilizando regressões a partir de dados históricos em economia e finanças.
19) Escolas de IA – Os algoritmos mestres de ML podem ser divididos em
cinco categorias:
a) simbolista: baseia-se no processo mental de raciocínio lógico hu-
mano, cuja representação de conhecimento deriva da associação
de elementos discretos como símbolos (conceito ou significado) e
regras (relação entre símbolos);
b) conexionista: estrutura-se no processo fisiológico a partir da emu-
lação do funcionamento do cérebro humano, cuja aquisição de
conhecimento ou aprendizagem deriva de sinapses contínuas na
rede neuronal;
c) evolucionista ou genética: busca-se o aprendizado durante a execu-
ção do algoritmo e a evolução por geração, reprodução e seleção;
d) bayesianista: analisam-se dados conforme a probabilidade de ocor-
rência de eventos; e
e) analogista: trabalha-se com comparações de dados e situações para
obtenção de padrões similares à realidade.
20) Sistemas inteligentes humano-consciente ou conscientes dos seres huma-
nos (human-aware intelligent systems) – Sistemas de interação intuitiva
que viabilizam a colaboração entre os humanos e as máquinas, levam
em consideração o histórico de escolhas e ações humanas e, até mesmo,
tomam como linha de base o modelo de comportamento cognitivo hu-
mano. Algum grau de inteligência emocional é desejável para aumentar
a efetividade do trabalho com os humanos.
21) Interface computador-cérebro (BCI – brain computer interface) –Técnica
de comunicação entre homem e máquina, realizada por meio de impulsos
elétricos, para reprodução de funções sensório-motoras.
22) Inteligência de enxame (SI – swarm intelligence) – População de agentes
autônomos inteligentes (e.g.: boids – contração de bird-oid object – algo-
ritmo de vida artificial; neste caso, de objeto semelhante a pássaro) que
interagem mutuamente e com o ambiente local, constituindo compor-
tamento emergente coletivo (algoritmo simulador de movimentos de
--- PAGE 67 ---
|
612 Do paradoxo das habilidades à superinteligência pós-humana
alinhamento, separação e coesão), aprendizado não supervisionado e
resiliência a eventos adversos, que atuam em sistemas descentralizados
e auto-organizados.
23) Sistema autônomo (autonomous system) – Com habilidade para operar
em circunstâncias que exijam mudança e adaptação de cenários, sem que
haja necessidade de intervenção humana, capaz de diagnosticar e reparar
falhas de operação. O termo automação está associado ao trabalho físico
e cognitivo processado pela IA, geralmente por meio de robótica com
motricidade fina.
24) Sistema especialista (expert system) – Abordagem na qual programadores
e especialistas em determinada área do conhecimento humano buscam
identificar regras e critérios utilizados no processo de tomada de decisão,
para traduzi-los em código de computação que emulem os princípios
utilizados pelos expertos. Algoritmos de IA/ML derivaram da necessi-
dade de expandir a capacidade dos sistemas especialistas no processo de
tomada de decisão, aprimorando-os com a emulação do funcionamento
do cérebro humano.
FIGURA 11
Estrutura de funcionamento da tecnologia de IA
Inteligência artificial
(Máquinas Inteligentes que operam e reagem como humanos)
Coleta Age e
Compreende Prediz
sensória adapta
Analytics Aprendizado de máquina
(Busca por informação nos dados) (Aprende do passado e prediz o futuro)
Descreve Supervisionada Aprende profundamente
(Treinamento matemático de modelos
preditivos com dados rotulados)
Treina
Diagnostica (Usa modelos que imitam o
cérebro humano para interpretar
fala, imagem e texto)
Sem Supervisão
(Agrupa, aglomera e organiza
Busca e percebe conteúdo com modelos heurísticos Vê, fala e escreve
de domínio específico)
Fonte: Vermesan e Bacquet (2018).
Os últimos avanços nos processos cognitivos de alto nível das máquinas in-
teligentes, como percepção, aprendizado e resolução de problemas, têm provocado
efervescentes debates em todo mundo por representar potencial disruptivo sem
precedentes na história tecnológica em progressão. No Reino Unido, por exemplo,
o assunto passou por ambas as casas do Parlamento. Segundo o relatório produzido
--- PAGE 68 ---
|
Segmentos tecnológicos e aplicações 613
pela Câmara Alta (correspondente ao Senado), denominado HL Paper 100 (House
of Lords, 2018), a retomada recente do interesse em IA foi decorrente dos avanços
das redes neurais de aprendizado profundo (deep learning neural network), em meados
da década de 2000.
Nos Estados Unidos, a disponibilidade de relatórios sobre IA vem aumen-
tando, assim como a elaboração de planos e estratégias sobre o tema. Os assessores
da Presidência, que compõem o Comitê de Tecnologia, do Conselho Nacional de
Ciência e Tecnologia (NSTC – National Science and Technology Council), lan-
çaram o documento preparatório para planejamento e aprofundamento do tema,
em 2016, Preparing for the Future of Artificial Intelligence (NSTC, 2016b), e logo
depois a contextualização da automação na dimensão econômica, com Artificial
Intelligence, Automation and the Economy (EOP, 2016).
O Plano Estratégico Nacional de Pesquisa e Desenvolvimento em IA (The
National Artificial Intelligence Research and Development Strategic Plan), revisado
em 2019, traz sete grandes vertentes de atuação: investimentos; métodos efetivos de
colaboração entre humanos e IA; implicações éticas e legais; segurança operacional
e contra atos ilícitos; dados públicos, para treinamento de IA e testagem; padro-
nização das tecnologias de IA; e necessidades de capacitação de força de trabalho
especializada em pesquisa e desenvolvimento (P&D) de IA (NSTC, 2019).
3.2 Histórico e escolas de computação cognitiva
A contextualização é importante porque decifra a trajetória de evolução da ciência
da computação, especificamente da computação cognitiva, caracterizada por uma
espécie de dialética entre os cientistas envolvidos na temática. Para seguir esse
colóquio, é preciso estar atento às datas e aos correspondentes fatos para chegar à
conclusão sobre uma autêntica concatenação de ideias.
3.2.1 Histórico
O relatório de tendências tecnológicas de IA (WIPO, 2019) fez um breve resumo
dos grandes períodos de evolução daquela que promete ser a fronteira tecnológica
com maior poder de transformação no mundo e seu subsequente impacto no modo
de vida das pessoas. Contudo, o histórico ampliado carrega outros detalhamentos
que fazem parte da concepção gestada no imaginário da humanidade, inspirando
a produção de ideias que, por isso, valem uma breve descrição.
Portanto, o leitor deve atentar para sinais que ressoam de tempos em tempos,
manifestados em eventos que determinam a entrada de novo ciclo em patamar
tecnológico superior. As agregações incrementais de conhecimento em campos
opostos de conceituação, principalmente aquelas geradas pelas escolas IA cone-
xionista e simbolista, tendem a convergir para uma visão comum, que possibilite
uma interpretação da cognição ou inteligência.
--- PAGE 69 ---
|
614 Do paradoxo das habilidades à superinteligência pós-humana
QUADRO 7
Histórico ampliado de evolução da tecnologia de IA
Período Detalhamento
Mitologia grega – Descrito na Teogonia, obra do poeta grego Hesíodo do século VIII AC, o herói inventor da humanidade Prometeu desafia Zeus, ao
ceder o fogo sagrado dos deuses aos homens. Zeus determinou, ao deus ferreiro Hefesto, que forjasse Talos, um autômato gigante de bronze, para
proteção da deusa Europa, na ilha de Creta.
Egito Antigo – Narrativas de Hermes Trismegisto (Toth ou Ptah) em Corpus Hermeticum descreveram sacerdotes egípcios animando estátuas de deuses,
Ancestralidade graças ao aprisionamento de espíritos angélicos que falavam e profetizavam.
300 AC – Aristóteles descreveu a lógica como sendo o mecanismo do pensamento racional, definindo silogismo como argumentos ou proposições
utilizadas para obtenção de uma conclusão.
100 DC – Heron de Alexandria, geômetra e engenheiro grego, teria criado mecanismos autômatos para demonstrar princípios científicos. Antikythera,
mecanismo projetado para calcular posições astronômicas, comparável a computador analógico.
1818 – Mary Shelley, escritora britânica, publicou a história de ficção Frankenstein, ou Moderno Prometheus, que serve de reflexão a respeito de
aspectos éticos de criar um ser consciente.
1848 – A condessa inglesa de Lovelace produziu o primeiro conjunto de instruções (algoritmo) para processamento de calculador ou motor analítico,
criado pelo inventor Charles Babbace, antevendo-o como ferramenta de colaboração social.
Século XIX
1863 – Samuel Butler, escritor britânico, produziu o artigo Darwin Among the Machines, em referência ao trabalho de Charles Darwin em meio às máquinas,
no qual levantou a possibilidade de que as máquinas seriam um tipo de vida mecânica em constante evolução e que, eventualmente, suplantariam os
humanos enquanto espécie dominante.
1871 – Houdin, ilusionista ou mágico francês, utilizava truques mecânicos e figuras autômatas para entreter o público.
1900 – David Hilbert, matemático alemão, publicou lista com 23 problemas matemáticos em aberto, sem solução, entre os quais a prova de que axiomas
aritméticos eram consistentes.
1920 – Karel Capek, escritor tcheco, utilizou o termo robô pela primeira vez na peça de ficção científica Robôs Universais de Rossum (Rossum’s
Universal Robot – R.U.R.).
1930 – Howard Aiken, físico norte-americano, projetou o computador automatic sequence controlled calculator (calculadora eletromecânica automática),
mais tarde conhecido como Havard-IBM Mark I.
1931 – Kurt Gödel, matemático austro-húngaro, formulador da teoria da incompletude, com Turing, Church, Kleene e Post, formulou a teoria da compu-
tabilidade ou recursão, por ter estruturado linguagem de programação universal integer-based.
1936 – Konrad Zuse, engenheiro alemão, construiu o primeiro computador a partir de dispositivo interruptor eletromecânico. Cinco anos mais tarde,
Zuse conseguiu programar o computador Z3.
1936 – Allan Turing, matemático britânico, criou a máquina automática (a-machine), dispositivo teórico conhecido atualmente como máquina universal
de Turing (MUT), modelo abstrato de computador. Quatro anos depois, a equipe de Turing em Bletchley Park construiu o primeiro computador eletrônico
digital, o Colossus, aplicado na decodificação de comunicações criptografadas durante a Segunda Guerra Mundial.
1943 – Warren McCulloch (psiquiatra do MIT) e Walter Pitts (matemático de Illinois) desenharam o primeiro modelo de neurônio artificial contendo os
conceitos relacionados à IA a partir de três áreas de conhecimento: psicologia e funções dos neurônios cerebrais; análise lógica; e teoria da computação.
Vertente conexionista.
1944 – Oskar Morgenstern e John von Neumann introduziram a teoria dos jogos e do comportamento econômico.
Século XX 1948 – John Von Neumann, matemático húngaro naturalizado norte-americano, escreveu The General and Logical Theory of Automata (Teoria Lógica dos
Período de gestação1 Autômatas), sobre mecanismo cerebral do comportamento, além de criar o primeiro modelo de autômata celular autorreplicante.
(1943 a 1955)
1956 – concepção do concei- 1948 – Norbert Wiener, matemático norte-americano, publicou o livro Cybernetics sobre controle, comunicação e processamento estatístico de sinais.
to de inteligência artificial (IA); Reeditado em 1961, incluiu assuntos como aprendizagem e auto-organização.
a Conferência de Dartmouth 1949 – Donald Hebb, psicólogo canadense, no livro The Organization of Behavior, introduziu o sistema ou regra de aprendizado por associação ou correlação
começa a desenhar o paradig- dos neurônios, que resulta em crescimento substancial da força sináptica, dando origem à regra de aprendizagem hebbiana ou teoria hebbiana de aprendizado.
ma para o desenvolvimento
da tecnologia de IA 1950 – Marvin Minsky e Dean Edmonds construíram o primeiro computador em rede neuronal a stochastic neural analog reinforcement calculator (Snarc),
calculadora de reforço analógico neural estocástico com tubos de vácuo simulando a rede de quarenta neurônios.
1950 – Allan Turing escreve artigo seminal, intitulado Computing machinery and intelligence (Máquina computacional e inteligência), quando reconheceu
que as máquinas estariam habilitadas a simular o processo de aprendizado, sendo esse o caminho apontado para produzir IA, verificável via teste de Turing.
1950 – Isaac Asimov, escritor e bioquímico, propôs as três leis da robótica: i) um robô seria inofensivo ao humano; ii) um robô teria de obedecer às ordens
dadas pelo ser humano; e iii) um robô deveria proteger sua própria existência, contanto que essa proteção fosse compatível com a primeira e a segunda lei.
1952 – Arthur Samuel, cientista da computação norte-americano, pioneiro de ML escreveu o primeiro programa para o jogo de damas por autoaprendizado,
com habilidade suficiente para desafiar um jogador amador.
1954 – Durante o período da Guerra Fria, o governo dos Estados Unidos demostrou especial interesse na máquina de tradução automática, voltando a
atenção para o trabalho do linguista, filósofo e cientista da cognição norte-americano Noan Chomsky.
1955 – John McCarthy, cientista da computação norte-americano, deu nascimento ao termo inteligência artificial, cunhado para descrever a ciência que
estuda a engenharia de máquinas inteligentes.
1956 – Dartmouth College, onde surgiu a concepção dos dois paradigmas da IA: simbólica (tenta simular o comportamento inteligente humano) e
conexionista (simula a estrutura do cérebro e seus mecanismos).
1956 – A. Newell, J.C. Shaw e H.C. Simon, da Universidade de Carnegie Mellon, escreveram o Logic Theorist (LT), reconhecido como o primeiro programa
de IA, com abordagem de raciocínio simbólico.
1956 – McCarthy, Minsky, Shannon e Rochester reuniram interessados na teoria de autômatas, rede neural e inteligência, em oficina de trabalho com
dois meses de duração, em Dartmouth.
(Continua)
--- PAGE 70 ---
|
Segmentos tecnológicos e aplicações 615
(Continuação)
Período Detalhamento
1957 – Frank Rosenblatt, psicólogo norte-americano, criou o Perceptron, modelo cognitivo de unidades sensoriais conectado à camada de neurônios
McCulloch-Pitts, capaz de aprender tudo que pudesse representar, treinado para classificar padrões em classes linearmente separáveis, convergindo em
número de passos limitados. Manutenção da vertente conexionista.
1958 – Richard Friedberg escreveu A learning machine (Uma máquina aprendiz); segundo o físico norte-americano, uma série de pequenas e apropriadas
mutações no programa código de máquina pode levar a bom termo quaisquer tarefas, testadas randomicamente e selecionadas, em caso de evolução
de máquina – i.e., machine evolution; precursor do algoritmo genético.
1958 – McCarthy, cientista da computação norte-americano, define a linguagem de alto nível list processing (Lisp) – processamento de lista, dominante
em IA durante três décadas; escreve o artigo Programs with common sense (Programas com senso comum), descrevendo um programa hipotético de
IA (Advice Taker), capaz de adquirir competência em novas áreas sem ser reprogramado e incorporar princípios fundamentais de representação do
conhecimento e do raciocínio, emulando assim o comportamento humano em máquina.
1959 – John McCarthy e Marvin Minsky fundaram o Laboratório de IA do MIT, o primeiro interessado em lógica e o segundo, em fazer os programas funcionarem.
1959 – Hebert Simon, J. C. Shaw e Allen Newell (RAND Corporation) desenvolveram o General Problem Solver (GPS), uma máquina de resolução de
problemas gerais a partir de fórmulas bem-definidas, explorando a vertente do raciocínio simbólico como essência da inteligência.
1960 – John Holland, cientista da Universidade de Michigan, introduziu o conceito de algoritmo genético na classe de algoritmos evolucionários inspirados
na teoria de seleção natural biológica darwiniana, para geração de soluções de otimização com emprego de operações de mutação, cruzamento e seleção.
1960 – Bernard Widrow e Ted Hoff de Stanford especificaram o neurônio artificial (adaptive ou adaptative linear network – Adaline), com base no
modelo de McCulloch-Pitts. A proposta de regra delta de correção de erro seguia o método de aprendizagem hebbiano, e o Madaline (Adaline múltipla)
teria dupla camada de rede neural.
1960 – Alfred E. Brain, pesquisador do Stanford Research Institute, em Menlo Park, construiu uma pequena rede neural denominada Minos, promovida
pelo United States Army Signal Corps, divisão das Forças Armadas especializada no segmento de comunicações.
1961 – Unimate, primeiro robô industrial fabricado pela Unimation a substituir mão de obra na linha de montagem da General Motors (GM), empresa
produtora de motores.
1963 – James Slagle, cientista da computação norte-americano, programou o integrador simbólico automático (symbolic automatic integrator – Saint),
capaz de resolver problemas de cálculo diferencial integral, em domínio limitado, por isso classificado como programa especialista microworld, por
conter poucos objetos e ações.
1963 – McCarthy fundou o laboratório de IA em Stanford, enfatizando a metodologia de raciocínio lógico como técnica de propósito geral.
1964 – Joseph Weizenbaum, cientista da computação alemão, catedrático no MIT, desenvolveu o robô de conversação (chatrobot) Eliza, programa
pioneiro de processamento em linguagem natural.
Anos de ouro da tecnologia de IA
(1956 a 1974) 1965 – Edward Feigenbaum, cientista da computação norte-americano considerado pai dos sistemas especialistas, Joshua Lederberger, geneticista
Financiamento governamental – ganhador de Prêmio Nobel, e Bruce Buchanan desenvolveram o algoritmo dendrítico em alusão às células imunológicas (dendritic algorithm – Dendral),
dos Estados Unidos – com a preparado para deduzir a estrutura molecular de compostos orgânicos, utilizando dados científicos de espectrograma de massa.
p b de a ro s p m e r o e ló b s g l s e i a m c a d a s e p a a r b a o a r d r a e g so e l m uç ã d o e 1 de 9 6 in 5 t e – li g Ir ê v n in c g ia J s o u h p n e r G -h o u o m d, a m na a . temático britânico, formulou o conceito de explosão de inteligência ou singularidade tecnológica, que antecipa a ideia
1965 – Gordon Moore, químico norte-americano, formulou a tese de que o número/capacidade dos processadores eletrônicos (circuito integrado de
baixo custo) dobrava a cada dezoito meses, o que ficou conhecido como a lei de Moore.
1966 – Relatório do Comitê Assessor para Processamento Automático de Linguagem (Automatic Language Processing Advisory Committee – Alpac)
evidenciou a frustração com a máquina de tradução, e o Conselho Nacional de Pesquisa dos Estados Unidos decidiu cortar investimentos.
1966 – Instituto de Pesquisa de Stanford apresentou Shakey, primeiro robô eletrônico, para propósitos gerais, sendo capaz de realizar comandos
complexos a partir de ações racionais próprias.
1967 – Daniel Bobrow, cientista da programação norte-americano, desenvolveu o programa STUDENT, primeiro algoritmo de IA em linguagem Lisp
para resolver problemas lógicos de álgebra.
1967 – Jack Cowan, matemático norte-americano da Universidade de Chicago, caracterizou o disparo suave neuronal sigmoide em forma de S, que inclui
a função logística e a hiperbólica, como função de ativação de neurônio artificial.
1967 – Stephen Grossberg, cientista cognitivo norte-americano fundador da neurociência computacional, introduziu o teorema de memória endereçável
sobre modelo de aprendizado aditivo de neurônio (equações diferenciais não lineares) base para a memória de curto prazo.
1969 – Marvin Minsky e Seymour Papert publicaram o livro Perceptrons: an introduction to computational geometry (Perceptores: uma introdução à
geometria computacional) (Minsky e Papert, 1969), expondo limitações do modelo perceptron de duas entradas indiferenciáveis. Embora o resultado não
pudesse ser aplicado a redes multicamadas, o fato repercutiu negativamente para abordagem conexionista de redes neurais artificiais.
1969 – Arthur Bryson e Yu-Chi Ho inventaram em primeira mão o algoritmo de aprendizado back propagation, para redes de múltiplas camadas, que
permitiu o resgate da pesquisa de redes neurais no final dos anos 1980.
1969 – Feigenbaum, Buchanan e Shortliffe desenvolviam sistemas especialistas, parte do Projeto de Programação Heurística de Stanford, com aplicação
em áreas diversas, como a heurística Dendral e Mycin, para diagnóstico de infecção sanguínea.
1971 – Larry Roberts, da Agência de Projetos de Pesquisa Avançada em Defesa dos Estados Unidos (Darpa – Defense Advanced Research Projects Agency),
lançou o programa Speech Understanding Research (SUR).
1972 – Amari, Wilson e Cowan estudaram a abordagem mecânica estatística para análise de estados físicos estáveis, flutuações e equações de campos
neurais, modelo aditivo de neurônios e de neurônios excitadores e inibidores.
1973 – Relatório Lighthill, encomendado pelo Parlamento do Reino Unido a James Lighthill, gerou o artigo oficialmente denominado AI: a general survey
(Pesquisa geral IA), que marcou o declínio dos investimentos em IA na Inglaterra.
1974 – Paul Werbos, sociólogo norte-americano e pioneiro de ML, descreveu o processo de treinamento de redes neurais artificiais por algoritmo de
retropropagação (backpropagation) e estrutura de aprendizado por programação dinâmica adaptativa. Ressurgimento da abordagem conexionista.
(Continua)
--- PAGE 71 ---
|
616 Do paradoxo das habilidades à superinteligência pós-humana
(Continuação)
Período Detalhamento
1975 – Little e Shaw descreveram o modelo probabilístico de um neurônio, utilizando-o para desenvolver a teoria da memória de curto prazo.
1975 – Marvin Minsky, cientista da computação e matemático norte-americano, publicou o artigo sobre notações frames, abordagem estruturada,
representação do conhecimento e ligações semânticas esquemáticas, para serem trabalhadas e aplicadas no mundo real de forma disseminada.
1976 – Newell e Simon formularam a hipótese de sistemas de símbolos físicos – i.e., sistemas inteligentes processadores de estruturas de dados
simbólicos. Com McCarthy e Minsky, promoveram a visão mecanicista cérebro-máquina, passível de modelagem computacional por meio do neurônio
Primeiro inverno da tecnologia artificial processador de informação.
de IA 1976 – Grossberg estabeleceu o novo princípio de mapas de auto-organização, com base na abordagem anterior de aprendizagem competitiva, conhecido
(1974 a 1980) como teoria da ressonância adaptativa.
Redução de financiamento e
interesse na pesquisa, devido 1977 – Anderson, Silverstein, Ritz e Jones descreveram o modelo de estado cerebral em uma caixa (brain state in a box – BSB), sistema de realimentação
às expectativas excessiva- positiva de neurônios fortemente interligados, utilizado para amplificar um padrão de entrada até a saturação e definir categorias.
mente altas com relação à
capacidade ainda limitada dos 1979 – Hans Moravec, cientista austríaco nacionalizado canadense, construiu o Stanford cart, veículo autônomo controlado por computador, testando
programas de IA circo-navegabilidade em laboratório.
1979 – Myers e Pople, da Universidade de Pittsburgh, desenvolveram o Internist, um software de diagnóstico médico baseado em conhecimento clínico.
1980 – John Searle, filósofo norte-americano da Universidade de Berkeley, no artigo Minds, Brains and Programs (Mente, Cérebro e Programas), afirmou que
qualquer tentativa de criar intencionalidade artificialmente (IA forte) teria de reproduzir as forças causais neurofisiológicas do cérebro humano. Contudo, a
replicação da qualidade subjetiva unificada da consciência não poderia ser conseguida por intermédio de programação (experimento mental da sala chinesa).
1980 – Primeira Conferência da Associação Americana para IA (AAIA) em Stanford.
1981 – Japão anunciou investimentos de US$ 800 milhões em projetos para desenvolvimento de computadores inteligentes de quinta geração, com uso
de Prolog, linguagem de programação em lógica matemática.
1982 – John Hopfield, físico do Instituto de Tecnologia da Califórnia (California Institute of Technology – Caltech) e neurocientista norte-americano,
chamou atenção para as propriedades associativas entre o cérebro e as redes neurais artificiais, reforçando o modelo conexionista.
1982 – Teuvo Kohonen, engenheiro finlandês, escreve sobre redes neurais artificiais baseadas em auto-organização e nas características de aprendizado
adaptativo do cérebro humano.
1983 – R. Michalski, J. Carbonell e T. Mitchell escreveram o livro-texto Machine Learning (Aprendizagem de Máquina), com contribuições sobre o avanço de IA.
1983 – David Ackley, Geoff Hinton e Terry Sejnowsky incorporaram a dinâmica estocástica e generativa ao modelo Hopfield, que passou a ser conhecido
como máquina de Boltzmann (nome da distribuição de probabilidade estatística da termodinâmica), rede neural com capacidade para aprendizado
Resgate da tecnologia de IA interno e resolução de problemas de combinatória.
(1980 a 1987) 1983 – Howard Gardner, psicólogo da Universidade de Havard, escreveu Frames of Mind (Estruturas da Mente), no qual elaborou a teoria das inteligên-
Interesse em redes cias múltiplas (TIM), classificando sete tipos de inteligência: linguística; lógico-matemática; espacial; corporal cinestésica; musical; inter e intrapessoal;
neurais; ascensão de sistemas naturalista; e existencial.
especialistas baseados
em conhecimento; novo 1983 – Os Estados Unidos responderam ao programa japonês de computadores de quinta geração, anunciando investimentos da Darpa, inclusive na área de
alento e mudança no foco das neuro-computação. A iniciativa estratégica envolveu cerca de noventa projetos desenvolvidos por sessenta instituições industriais e de pesquisa acadêmica.
pesquisas; recuperação dos 1986 – Rumelhart, Hinton, Williams e McClelland, do Grupo de Pesquisa de Processamento Distribuído Paralelo (Parallel Distributed Process – PDP Research
financiamentos Group), da Universidade da Califórnia, apresentaram a coletânea de casos com algoritmos de aprendizado utilizando backpropagation, algoritmo de apren-
dizagem supervisionado em redes de múltiplas camadas. A partir daí, o tema redes neurais (abordagem conexionista) teve um desenvolvimento explosivo.
1986 – Craig Reynolds, especialista em computação gráfica e animação em 3D, criou os boids uma simulação de vida artificial que imita o movimento
de revoada sincronizada dos pássaros na natureza.
1987 – Marvin Minsky publicou The Society of Mind (A Sociedade da Mente), descrição teórica de como a mente funciona por meio da interação de
uma coleção de agentes cooperativos, cada qual simples e com mínima capacidade cognitiva.
1987-1995 – Europa lançou o Programa de Tráfego de Alta Eficiência e Segurança (Programme for a European Traffic of Highest Efficiency and Unpre-
cedented Safety – Projeto Eureka Prometheus), em P&D, para veículos sem motorista.
1987 – Rodney Brooks, cientista da computação e engenheiro australiano, introduziu a arquitetura de subfunção de robótica reativa, em oposição à IA
convencional e que acopla a informação sensorial à ação em tempo real.
1988 – A Corporação de Equipamento Digital (Digital Equipment Corporation – DEC) havia implementado quarenta sistemas especialistas, e a DuPont
implantou cem sistemas especialistas, com outros quinhentos em fase de desenvolvimento, resultando em economia de dezena de milhões de dólares a.a.
1989 – Timothy Berners-Lee, cientista da computação e físico inglês, propôs a criação da rede mundial ampla (www), quando uniu a ideia de hipertexto
com a internet, com o objetivo de compartilhamento e atualização das informações dos pesquisadores do Laboratório Europeu de Física de Partículas.
Segundo inverno da IA 1989 – Bill, denominação dada ao sistema de aprendizado bayesiano desenvolvido por Kai-Fu Lee e Sanjoy Mahajan, venceu o campeão norte-americano
(1987 a 1993) Brian Rose, no jogo de tabuleiro Othello.
Súbito colapso da indústria 1990 – Rodney Brooks, cientista em robótica australiano, no artigo Elephants don’t play chess (Elefantes não jogam xadrez), argumentou que, para
de hardware/software robôs assumirem tarefas rotineiras, seria necessário o acoplamento de motor-sensor e complementar com o senso proprioceptivo, componente-chave
especializados (hype cycle da coordenação visão-tactil na implementação de ações.
AI), trazendo uma percepção
negativa capaz de inibir 1991 – Aplicação da ferramenta de análise dinâmica e reposicionamento (dynamic analysis and replacing tool – Dart), com o objetivo de planejamento
investidores e governos, com automatizado de logística e transporte na crise do Golfo Pérsico.
sistemas especialistas
limitados mostrando-se 1992 – Gerald Tesauro, físico da Universidade de Illinois e pesquisador da IBM, programou em rede neural artificial software capaz de jogar gamão, o
dispendiosos para atualizar TD-Gammon, com treinamento de aprendizado por reforço com diferença temporal.
e manter 1992 – Peter Norvig, cientista da computação e diretor do Google, no livro Pardigm of AI (Paradigma IA), sobre o modelo tecnológico, descreveu uma nova
versão do General Problem Solver ou LT, de Newell e Simon; o resolvedor de problemas projetado para emular o pensamento humano.
1993 – Ian Horswill, do laboratório de IA do MIT, criou o robô móvel Polly, com visão, velocidade e navegação que simula a movimentação de um animal.
1993 – R. Brooks, L. A. Stein e C. Breazeal, do MIT, iniciaram o projeto Cog, com o objetivo de construir um robô humanoide com inteligência para
simular pensamento e sentimento.
(Continua)
--- PAGE 72 ---
|
Segmentos tecnológicos e aplicações 617
(Continuação)
Período Detalhamento
1993 – A Corporação Information System Experience (ISX), provedora de soluções militares, ganhou o prêmio do ano da Darpa, com a ferramenta de
replanejamento e análise dinâmica (Dynamic Analysis and Replanning Tool – Dart), repercutindo em reembolso dos investimentos do governo norte-americano
nas pesquisas de IA desde a década de 1950.
1994 – Roger Penrose, físico matemático inglês, escreveu o livro Shadows of the Mind: a search for the missing science of consciousness (Sombras
da Mente: uma busca pela ciência perdida da consciência), no qual apresenta as hipóteses necessárias para conseguir a modelagem da consciência.
No livro de 1989, Emperor’s New Mind (A Nova Mente do Imperador), havia argumentado que alguns aspectos da mente humana escapam à função
computacional, sendo impossível a emulação por um algoritmo convencional.
1995 – Hackermann, Geiger e Chickering escreveram Redes de Aprendizado Bayesianas, sobre o aprendizado de máquina com combinação de
conhecimento e dados estatísticos.
1995 – Chinook, programa da série desenvolvida por Arthur Samuels desde 1952, que venceu o campeão mundial de jogo de damas.
1997 – Hubert Dreyfus, filósofo norte-americano professor de Berkeley, no livro O que os Computadores Não Conseguem Fazer, argumenta que sistemas
baseados em regras lógicas não podem adquirir consciência, muito menos sem fazerem parte de um corpo físico biológico.
1997 – Deep Blue da IBM vence o campeão mundial de xadrez Garry Kasparov.
1998 – Cynthia Breazeal, especialista em robótica no MIT, anunciou a criação de robô sentimental, Kismet, que detecta as emoções e responde a estas.
1999 – Sony lançou o primeiro robô de estimação para consumo, Aibo (AI robô), com simulação de habilidades e personalidade que evolui com o tempo.
1999 – Lançamento do filme Matrix, dirigido e escrito por Lana e Lilly Wachowski, trazendo surpreendente percepção a respeito da condição humana
em um mundo dominado por máquinas superinteligentes.
2000 – Clay Mathematics Institute abre concurso para resolução de sete problemas matemáticos do milênio em aberto (The Seven Millennium Prize
Problems), com premiação de US$ 1 milhão/cada.
2000 – Nomad robô criado pela equipe de pesquisadores da Universidade Carnegie Mellon, para explorar as regiões remotas da Antártica.
Nova onda de otimismo sobre
a tecnologia de IA 2001 – Tim Berners-Lee, J. Hendlers e O. Lassita publicaram o artigo sobre semântica de rede (The Semantic Web), um novo formato de conteúdo para
(1993 a 2011) a rede www, que permite a cooperação entre máquinas e humanos.
M or a ie i n o t r a p d o o d p er o r c o d m ad p o u s tacional 2002 – Amazon utiliza sistemas automatizados para fornecer recomendações aos clientes.
2002 – Lançamento do robô aspirador de piso Roomba, da empresa iRobot, fabricante de robôs-aspirador em escala, que aprendem a navegar e limpar a casa.
2003-2008 – A Darpa lançou o projeto de assistente cognitivo que aprende e organiza (Cognitive Assistant that Learns And Organizes – Calo), para
integrar tecnologias de IA.
2004-2005 – A Darpa promove o Grande Desafio de Veículos Autônomos, no Deserto de Mojave, Estados Unidos.
2004 – Spirit e Opportunity, veículos autônomos (rovers), navegam na superfície de Marte, tendo sido antecedidos por Mars Sojourner (1997) e
sucedidos pelo Curiosity (2012).
2005 – Asimo, robô IA humanoide da Honda, apresentou agilidade ao caminhar e servir em bandejas.
2005 – Iniciativa suíça, liderada por Henry Markram, lançou o projeto Blue Brain, com a finalidade de desenvolver programa de simulação computacional
do cérebro humano em nível de detalhamento de molécula.
2007 – A Darpa lançou o desafio urbano para carros autônomos, com a condição de obediência às regras de trânsito.
2007-2008 – A União Europeia (UE) investiu no Sétimo Programa-Quadro da União Europeia (EU-FP7) cerca de € 600 milhões em pesquisa de IA para
sistemas cognitivos: robótica, conteúdo digital e tecnologias futuras emergentes.
2009 – Google anunciou projeto de veículo autônomo, com primeiro passeio em Austin, Texas.
2011 – Apple lançou a assistente virtual Siri integrada ao iPhone 4S.
2011 – IBM Watson bateu dois campeões humanos durante o programa de TV de perguntas e respostas Jeopardy. A Lionbridge, anunciou o desenvol-
vimento do GeoFluent, um tradutor automático de textos online para várias línguas.
2011 – Jeff Dean, da Google e Andrew Ng, professor de ciência da computação de Stanford deram início ao projeto Google Brain, no qual construíram
uma rede neural de 16 mil processadores alimentada por uma quantidade massiva de dados, capaz de analisar 10 milhões de capturas de tela do
YouTube, em três dias, e representar padrões visuais.
(Continua)
--- PAGE 73 ---
|
618 Do paradoxo das habilidades à superinteligência pós-humana
(Continuação)
Período Detalhamento
2012 – Automóvel sem motorista do Google completou 500 mil km rodados. Lançamento do Google Now, aplicativo de smartphones que utiliza
linguagem natural para orientar e fazer recomendações.
2012 – A Darpa lançou o Desafio Robótica, com foco em robôs humanoides capazes de executar tarefas complexas e perigosas em ambientes e
situações adversas.
2012 – Geoffrey Hinton, professor na Universidade de Toronto, e a equipe de Alex Krishevsky e Ilya Sutskever lançaram o AlexNet, um modelo de rede
neural profunda para reconhecimento de imagem executado em processador gráfico. O AlexNet foi vencedor no concurso ImageNet.
2013 – O Escritório de Ciência e Tecnologia dos Estados Unidos – OSTP lançou a iniciativa Brain, para pesquisa do funcionamento do cérebro por meio
de neurotecnologias avançadas e inovativas.
2013 – A Darpa lançou o desafio para a nova geração de veículos terrestres de rápida adaptabilidade (Fast Adaptable Next-Generation Ground Vehicle –
Fang), para performance de sistema e de manufatura em mobilidade.
2014 – Lançamento do assistente de smartphone Cortana da Microsoft.
2014 – Veselov, Demchenko e Ulasen programaram o ‘Eugene Goostman’, chatterbot desenvolvido na Rússia, que passa no teste de Turing, com um
terço dos juízes acreditando ser ele um humano.
2014 – Amazon lança Alexa, assistente virtual com interface de voz que completa as tarefas de compra em marketplace pela internet.
2015 – Hawking, Musk, Wozniak e mais 3 mil pesquisadores em IA e robótica assinam carta aberta comprometendo-se a banir o desenvolvimento
de armas autônomas.
2015 – A tecnologia DeepMind da Google AlphaGo (versão Fan) derrotou o campeão europeu de GO, Fan Hui, por cinco jogos a zero. No ano
seguinte, DeepMind (versão Lee) derrota Lee Sedol, o campeão mundial sul-coreano de GO, por quatro jogos a um. O sistema de aprendizado por
reforço Q-Network-DeepMind aprendeu a jogar 49 jogos Atari, igualando o desempenho de nível humano, em 2015.
2015 – Google lançou o RankBrain, algoritmo de aprendizagem de máquina programado para análise de resposta, classificação (ranking) e entrega de
resultados de buscas com base no comportamento do usuário; maior avanço desde o lançamento do motor de busca em 1998.
2016 – Tay, chatbot da Microsoft, aprende comportamento nocivo das redes sociais, fazendo comentários inflamados, ofensivos e racistas.
2016 – Stockfish 8, software código aberto, ganha o campeonato mundial de xadrez dos programas de computadores.
Tecnologia de IA disruptiva 2017 – AlphaZero, tecnologia da DeepMind, vence Stockfish em larga série de cem partidas (28 vitórias e 72 empates).
(2012 à atualidade)
Avanços na aprendizagem de 2017 – Lançamento do modelo de arquitetura de linguagem transformador ou modelo de aprendizado profundo, que faz uso de mecanismo de atenção,
máquina, principalmente em com o objetivo de ponderar a influência de diferentes partes dos dados de entrada, utilizado em PLN e compreensão de textos.
r p d e r is d o p e fu o s n n n d ib e a u il , i r d d a a e is d v i e e d o a d p e a r d e m n a a d d i o i o z s a r , gem 2 si 0 d 1 a 7 d e – d P e e A sq lb u e is rt a a d . ores da Universidade Carnegie Mellon desenvolveram o Libratus para jogar poker; isso também ocorre com o Deepstack da Univer-
conectividade e poder compu- 2017 – Sistema IA de classificação de câncer de pele, treinado por pesquisadores da Universidade de Stanford e do Instituto Nacional do Câncer dos
tacional, anunciando nova era Estados Unidos, com 130 mil imagens, alcançou competência comparável a dermatologistas e teve o feito publicado em artigo na revista Nature.
de aumento de financiamento
e otimismo sobre o potencial 2017-2021 – A Darpa lançou o Desafio Subterrâneo, para construção de sistema robótico para exploração de ambientes subterrâneos, sem iluminação,
da IA sem sistema de posicionamento por satélite (GPS) e relevo acidentado.
2018 – Alibaba desenvolveu modelo de IA de processador de linguagem, que superou humanos no teste de leitura e compreensão de textos da
Universidade de Stanford (Stanford Question Answering Dataset – Squad), acertando 82.440 em 100 mil perguntas. Posteriormente, a IA da Microsoft
alcançou 82.650 acertos.
2018 – Google Duplex, tecnologia que utiliza o assistente de voz ligado ao SO Android, foi lançado com recursos para entender nuances da conversação
e dialogar com a desenvoltura da fala natural, com o intuito de agendar compromissos e marcar horários.
2018 – Microsoft alcançou o nível de acurácia humano na máquina de tradução para os idiomas chinês-inglês.
2018 – Criação do Laboratório Europeu para Aprendizado de Sistemas Inteligentes (Ellis – European Laboratory for Learning and Intelligent System).
2018 – A equipe de Francesco Tacchino, da Universidade de Pavia, construiu o primeiro perceptron em um computador quântico.
2018-2020 – A Darpa promoveu o desafio de lançamento de espaçonave, para levar cargas pagas em órbita, com minimização de tempo e notificações.
2018 – Algoritmo de ML foi validado clinicamente para o diagnóstico de detecção de retinopatia diabética, com nível especialista de acurácia.
2019 – Yehudayoff, Shpilka, Moran, Hrubes e BenDavid, do Instituto Techion de Israel, apontaram múltiplos problemas sem solução na aprendizagem de
máquina, no artigo Learneability can be undecidable (Aprendizagem pode ser indecidível), reproduzindo a vivência de Gödel e Turing, dessa vez em ML.
2019 – Stuart Russel, cientista da computação e professor na Universidade da Califórnia, apresentou o dilema da superinteligência autodidata; aquela
que não pode ser orientada ou alimentada pelo homem, devido ao risco de indefinição, intrínseco à natureza humana.
2019 – OpenAI lançou o Rubik’s Solver, resolvedor do cubo Rubik; tem modelagem híbrida de algoritmo simbólico para resolver aspectos cognitivos.
2019 – Google lançou o algoritmo Representações de Codificador Bidirecional de Transformadores (Bidirectional Encoder Representations from Trans-
former – Bert), nova geração de PLN IA para a busca em contexto, capaz de analisar nuances corretamente, sem que o significado isolado da palavra
importe, compreendendo melhor a necessidade do usuário.
2020 – Lançamento do modelo Generative Pre-Trained Transformer (GPT-3), do laboratório de pesquisa OpenAI, colocado em nuvem Azure pela Microsoft;
o projeto concorrente é o EleutherAI – em código aberto.
2021 – Google lançou o modelo de linguagem para aplicações de diálogo (language model for dialogue applications – LaMDA), com foco na conversação
com o usuário – até 137 bilhões de parâmetros pré-treinados com 1,56 trilhões de palavras. O modelo unificado multitarefa (MUM) é a tecnologia
desenvolvida pela Google com foco na realização de tarefas complexas – treinamento em 75 idiomas e com mil vezes mais parâmetros do que o Bert.
Fonte: WIPO (2019), Russell e Norvig (2016), Domingos (2015), Nilsson (2010), entre outros.
Nota: 1 Classificação dada por Russell e Norvig (2016).
--- PAGE 74 ---
|
Segmentos tecnológicos e aplicações 619
O relato histórico começa com uma pequena amostra de um passado remoto,
a ancestralidade, denominação escolhida para representar uma época anterior à
antiguidade, pois é distante o bastante para resgatar a mitologia grega. Quando
Zeus, o rei dos deuses, determinou aos irmãos Prometeu e Epimeteu a criação
do mundo animal, incluindo os homens entre eles, todo tipo de atributos foram
distribuídos aos bichos sem que chegassem a atender aos humanos. Esgotados os
recursos de Epimeteu, Prometeu apropria-se do fogo sagrado dos deuses para animar
a humanidade, o que tornou a espécie superior aos outros animais. Igualmente
curioso é o fato de o deus ferreiro Hefesto forjar o autômato vigilante Talos.
A total incerteza quanto ao período é justificável também pelo mito de Hermes
Trismegisto, filósofo que viveu entre 1500 e 2500 AC, no Antigo Egito. Naquele
tempo, a noção de animismo já estava presente em narrativas criadoras de estátuas
que ganhavam vida e máquinas humanoides especializadas em determinadas tarefas.
Segundo a obra Corpus Hermeticum, os sacerdotes intermediavam o divino por meio
das estátuas, que falavam trazendo as mensagens dos deuses. Da mesma forma, a
psicologia de Aristóteles, repassada a Alexandre, o Grande, concebeu a substituição
de escravos por autômatos, além de descrever o modelo de pensamento racional,
que parece ter inspirado o mecanismo das redes neurais atualmente.
Para encurtar a história, um salto permite identificar outros indícios de
que subjaz na mente humana a figura da matéria inerte animada, como no caso
do Moderno Prometeu, Frankenstein, obra escrita por Mary Shelley entre 1816 e
1817, considerada a primeira obra de ficção científica moderna e contemporânea.
Trata-se de uma visão avançada até mesmo para a atualidade, cuja inspiração
teve origem na relação homem-entidade biológica, comparável à discussão da
interação homem-máquina. O romance baseia-se na construção de um protó-
tipo humano, em laboratório, pelo estudante suíço de ciências naturais Victor
Frankenstein. Apesar de dominar o segredo da geração da vida, o jovem cientista
parece decepcionar-se com sua criatura, cujo maior desejo era ter uma companhia.
Frankenstein recusa-se a continuar reproduzindo outras criaturas com o temor de
que pudessem se voltar contra a raça humana, assunto ainda em voga no presente.
O século XX foi pródigo em peças de ficção científica sobre o futuro da hu-
manidade em convivência com os autômatos. Uma das primeiras obras de que se
tem notícia é a peça teatral R.U.R., escrita em 1920 pelo criador da palavra robot,
o escritor tcheco Karel Capek (1890-1938). A narrativa descreve uma fábrica,
fundada pelo cientista Rossum, onde uma espécie de híbrido homem-máquina,
denominada roboti, era confeccionada a partir de matéria orgânica artificial, o
protoplasma. As entidades biológicas assemelhavam-se às pessoas de tal forma que
podiam pensar. O enredo desenrola-se acerca de uma rebelião robótica disparada
por uma certa inconformidade dos robôs com os humanos, cuja suposta intenção
--- PAGE 75 ---
|
620 Do paradoxo das habilidades à superinteligência pós-humana
era destruí-los. A peça tomou conta do imaginário coletivo e foi traduzida em trinta
línguas. Saindo do universo ficcional, alguns fatos científicos merecem atenção por
marcarem o desenvolvimento da tecnologia de IA.
John Von Neumann (1903-1957), polímata húngaro naturalizado norte-america-
no, escreveu Mathematical Foundations of Quantum Mechanics, em 1936 e The General
and Logical Theory of Automata, em 1948. Enquanto professor na Universidade de Prin-
ceton, participou da construção do primeiro computador digital de processamento em
larga escala, Eniac, que entrou em operação em 1946. Posteriormente, como consultor,
enunciou princípios de funcionamento do computador binário Electronic Discrete
Variable Automatic Computer (Edvac), sucessor do Eniac. Estudou e criou modelos
de sistemas autômatos celulares autorreplicantes, o conceito do copiador e construtor
universal, traduzido na coletânea Theory of Self Reproducing Automata (Neumann,
1966). Em 1937, escreveu A model of general economic equilibrium (Neumann, 1945).
Elaborou a teoria dos jogos, na década de 1940, Theory of Games I: general foundations,
Theory of Games II: decomposition e Theory of Games and Economic Behavior (Neumann e
Morgenstern, 1944), demonstrando a proximidade entre comportamentos econômicos
e soluções matemáticas, em relação a jogos de estratégia.
Entre 1936 e 1937, o prestigiado matemático e cientista da computação
britânico Alan Turing (1912-1954) apresentou o dispositivo teórico para mode-
lagem abstrata de computador – isto é, uma máquina que simula outra máquina
ou que realiza múltiplas funções, conhecida como máquina universal de Turing.
Em 1936, escreveu o artigo On Computable Numbers, with an application to the
Entscheidungsproblem (Turing, 1936), sobre números computáveis aplicados a
problemas de decisão, em que reformulava o teorema da incompletude de Gödel
por meio do dispositivo hipotético capaz de computar composições algorítmicas
genéricas, denominado automaton. Os teoremas de incompletude versam sobre
consistências ou inconsistências em sistemas formais – i.e., sistemas com axiomas
demonstráveis – e pertencem à área de lógica e filosofia matemática.
A preocupação de Turing naquela época era responder ao desafio lançado
por David Hilbert (1862-1943), no programa de consolidação do conhecimento
matemático, de 1928. Tratava-se de uma busca por métodos finitários17 que viessem
a encontrar solução para determinadas fragilidades bem específicas de estrutura
lógica, que implicavam vulnerabilidade de circularidade viciosa. O problema da
consistência lógica ou problema da decidibilidade, como ficou conhecido, foi tra-
tado por Gödel (1906-1978) em dois teoremas, dos quais o último, denominado
de teorema da incompletude II, atestava que não seria possível ao sistema formal
provar sua própria consistência. Era necessário um sistema teórico mais abrangente
e robusto, externo ao sistema em avaliação, para comprovar sua coerência.
17. A lista de problemas matemáticos de Hilbert continha 23 desafios, dos quais o segundo tratava de provar que axiomas
aritméticos eram consistentes. Além de Turing e Church, Gödel e Gentzen apresentaram respostas consideradas satisfatórias.
--- PAGE 76 ---
|
Segmentos tecnológicos e aplicações 621
Apesar de a resposta do experimento de Turing com a máquina automática
universal ter sido negativa para o problema sobre a decisão da verdade,18 o modelo
conceitual do dispositivo gerou frutos de outra ordem. A MUT foi reproduzida
pela arquitetura de Von Neumann (1946), na qual o computador tem sua progra-
mação armazenada no mesmo espaço de memória compartilhada com os dados,
permitindo o manuseio de programas. Logo, a arquitetura do computador digital
compreenderia: i) a unidade de controle, com a função da MUT para buscar o
programa armazenado na memória; ii) a CPU, composta de registros; iii) a unidade
aritmética e lógica (ALU); e iv) a unidade de armazenamento (memória), que, por
sua vez, guardaria instruções e dados.
No singular artigo, Computing Machinery and Intelligence, Turing (1950)
considerou a ideia de analisar máquinas pensantes, habilitadas ao aprendizado,
pois julgava ser necessário considerar a possibilidade de mente e matéria artificiais,
sendo esse o caminho para produzir IA. Para isso, o matemático inventou um
teste, bastante popular desde então, denominado teste de Turing, com o objetivo
de provar que a máquina poderia fazer tudo que o homem faz, inclusive pensar.
O experimento mental buscava constatar a veracidade dessa hipótese por meio de
uma espécie de jogo de perguntas e respostas, no qual o computador procura imitar
o comportamento humano. O teste consistia na comunicação entre pessoas e pro-
gramas computacionais, em que a resposta dada para uma determinada pergunta
poderia ter como origem uma pessoa ou um computador, sem que se soubesse
a real identidade. Feitas as perguntas e dadas as respostas, juízes decidiam qual
resposta teve origem em um computador e qual resposta proveio de um humano.
Em havendo confusão no julgamento dos árbitros, o teste permitiria afirmar que o
computador poderia se aproximar do raciocínio humano, naturalmente produzido
pela rede neuronal do cérebro.
Logo, em vez de responder à pergunta “máquinas podem pensar?”, Turing
propôs a seguinte indagação: “computadores digitais, como aqueles imaginados
no jogo de imitação descrito acima, poderiam ter um bom desempenho?” Com
base na sua vivência anterior com a concepção da MUT, a resposta de Turing foi
sim. Para isso, bastariam capacidade de armazenamento e processamento, além
de uma boa programação, é claro. Prevendo que sua argumentação seria pouco
convincente, Turing apresentou nove visões em contrário e teceu argumentos
irrefutáveis, conforme descritos a seguir.
1) Objeção teológica criacionista: nenhum animal ou máquina pode pen-
sar, pois esse atributo foi dado aos homens por Deus. Interpretação ao
argumento de Turing (IAT): a onisciência de Deus pode tudo.
18. Ou seja, a verdade sobre uma sentença em um sistema axiomático não poderia ser resolvida ou decidida por
algoritmo (Andraus, 2018, p. 42).
--- PAGE 77 ---
|
622 Do paradoxo das habilidades à superinteligência pós-humana
2) Objeção cabeça enterrada na areia: “as consequências seriam terríveis”.
IAT: esse é o tipo de expressão utilizada por aqueles que simplesmente
temem a perda de controle e não pensam sobre o assunto em absoluto.
3) Objeção matemática: a teoria da recursão de Gödel prevê limitações lógi-
cas de decidibilidade; um risco potencial na programação das máquinas.
IAT: ainda está para ser provado se tais limitações se aplicam também
ao intelecto humano.
4) Argumento da conscientização: sentimentos e emoções são imprescindíveis
para a elaboração de um soneto ou a composição de um concerto. IAT: os
enigmas que envolvem o fenômeno da consciência precisam ser resolvidos
antes de rejeitar-se a hipótese de auto-observação da máquina pensante.
5) Incapacidades diversas: tipo gentileza, senso de humor, amabilidade etc.
IAT: conclusões genéricas, oriundas do contexto limitado à máquina
utilitária, não têm noção de potenciais aperfeiçoamentos futuros.
6) Objeção de lady Lovelace (quadro 7, 1848): a ideia do motor analítico
é despretensiosa, apenas faz o que se pede dele. IAT: certamente, um
motor modesto para aquela época, mas a alegada surpresa ou sua falta
é um fator subjetivo.
7) Continuidade no sistema nervoso: o sistema neuronal humano não é
máquina, pois um erro mínimo do impulso dado ao neurônio (entrada)
pode repercutir amplamente no impulso produzido (saída). IAT: a imi-
tação do sistema nervoso (contínuo) por sistema mecânico (discreto) é
complicada, mas a emulação feita por um analisador diferencial pode
ser bem mais simples.
8) Informalidade do comportamento: impossível elaborar um conjunto de
regras para reagir às diversas circunstâncias concebíveis. IAT: a observação
científica é a forma de descobrir leis de comportamento (naturais), e não
existe uma lei que garanta que houve observação suficiente.
9) Percepção extrassensorial: fenômenos que negam ideias científicas conven-
cionais deveriam ser abordados. IAT: a interpretação do pensamento como
sendo um fenômeno extrassensorial pode ser especialmente relevante.
Turing encerra o artigo com um ensaio orientado para a programação do
aprendizado de máquina, fazendo uma analogia com o ensino, não de um adulto,
mas com o de uma criança. Desde então, o aprendizado de máquina tem sido
simulado a partir do processo de aprendizagem humana na primeira infância, por
meio de milhões de interações de relação de causa e efeito. Os dados são coletados
de forma autônoma para movimentação de objetos, além de outras funcionalidades.
--- PAGE 78 ---
|
Segmentos tecnológicos e aplicações 623
O teste de Turing tem sido reproduzido pelo Centro de Estudos Comportamentais
(Cambridge Center for Behavioral Studies em Massachusetts), para premiar o
computador que mais se aproxime do comportamento humano. O resultado mais
contundente do teste de Turing ocorreu em 2014, quando mostrou que 33% dos
avaliadores (critério de aprovação acima de 30%) confundiram um algoritmo com
um menino ucraniano de 13 anos de idade.
O escritor e bioquímico russo Isaac Asimov (1920-1992) supunha que, in-
dependentemente dos avanços tecnológicos alcançados na computação eletrônica
e na robótica, a concepção de quaisquer projetos ainda teria como origem a mente
humana. Sendo assim, as aplicações desse instrumento nunca deveriam fugir aos
interesses humanos. Para isso, Asimov preocupou-se em enunciar os princípios
que deveriam garantir que a criatura jamais se voltasse contra o criador. As três leis
da robótica concebidas por Asimov e que deveriam nortear projetos de quaisquer
algoritmos de IA consistiam em: i) um robô não feriria um ser humano, nem
mesmo por inação, nem permitiria que um ser humano sofresse algum mal; ii)
um robô teria de obedecer às ordens dadas pelo ser humano, exceto quando tais
ordens entrassem em conflito com a primeira lei; e iii) um robô deveria proteger
sua própria existência, contanto que essa proteção não entrasse em conflito com
o primeiro e o segundo regulamentos.
Porém, a mais interessante suposição deixada por Asimov era de que o avanço
dos computadores seria resultante do progresso da mente humana. Alegava que a
evolução do ser humano tinha sido um processo excessivamente lento e com inú-
meros pontos de não retorno. Provavelmente, o atual estágio de desenvolvimento
da engenharia genética venha a tornar possível a autoprogramação da espécie. Para
Asimov, as mudanças de genes deveriam ser desenvolvidas, apesar dos conflitos
éticos que pudessem ocorrer, pois técnicas cada vez mais compreensíveis estariam
disponíveis. A mente humana seria estruturada a partir da intuição e da criativi-
dade, enquanto a mente computacional seria construída com base na velocidade
e na precisão. As mentes, biológica e artificial, juntas, constituiriam a excelência
em matéria de resolução de problemas.
No pós-guerra, durante a tensão entre Estados Unidos e União das Repú-
blicas Socialistas Soviéticas (URSS), o interesse na aplicação de IA em traduções
automáticas fez com que as atenções fossem voltadas àquele que é considerado o
pai da linguística moderna, Noam Chomsky, professor emérito do MIT. Linguista,
ativista político e cientista cognitivo, Chomsky também é reconhecido no campo
da filosofia analítica pela criação da gramática ge(ne)rativa transformacional, além
de ter elaborado trabalhos fundamentais sobre as propriedades matemáticas das
linguagens formais, associados à chamada hierarquia de Chomsky.
--- PAGE 79 ---
|
624 Do paradoxo das habilidades à superinteligência pós-humana
Na hierarquia, as gramáticas são classificadas em quatro níveis (regulares,
livres de contexto, sensíveis ao contexto e estruturadas com maior liberdade de
regras) e são combinadas em abordagem matemática aos fenômenos da linguagem.
A classificação é amplamente utilizada na programação para PLN e na implemen-
tação de compiladores e interpretadores. Ao contrário do paradigma dominante na
década de 1950, cuja tese skinneriana consistia no fato de que o comportamento
verbal seria suscetível de modelagem experimental, Chomsky entendia a lingua-
gem como uma propriedade inata do cérebro/mente humanos e contingenciada
pela vida real, retirando a objetividade do mecanismo estímulo-resposta-reforço
proposto por Skinner.
Em 1955, o termo inteligência artificial foi cunhado pelo cientista da com-
putação John McCarthy, professor de Dartmouth College, que viria a organizar
uma conferência sobre o tema, no ano seguinte, e, mais tarde, também criaria a
disciplina acadêmica correspondente. A discussão na Conferência de Dartmouth
girou em torno de potenciais campos de aplicação da IA, como linguagem, racio-
cínio, visão, jogos e cognição. Desse debate, nasceram os dois paradigmas da IA no
Darthmouth College: a vertente simbólica (tenta simular o comportamento mental
inteligente) e a conexionista (simula a estrutura do cérebro e seus mecanismos).
Seguiu-se, então, o período 1956-1974, que ficou conhecido como anos de
ouro da IA, devido ao régio financiamento do governo norte-americano para a pes-
quisa pura, especialmente aqueles promovidos pela Darpa, com base na promessa
de abordagem de base lógica para a resolução de problemas. Durante a década de
1960, alguns centros acadêmicos dedicados ao desenvolvimento da IA, como o
MIT, a Universidade Carnegie Melon, Stanford e a Universidade de Edinburgh,
formataram alguma massa crítica a respeito do assunto de IA.
Avanços expressivos foram alcançados, como no caso do psicólogo norte-americano
Frank Rosenblatt, da Universidade de Cornell, que criou o Perceptron, em 1957.
Tratava-se de algoritmo de funcionamento do modelo eletromecânico que simulava a
estrutura básica da rede neural analógica, construída a partir de uma grade de células
fotoelétricas conectadas por fios a motores elétricos com resistores rotativos.
O modelo cognitivo de unidades sensoriais conectadas à camada de neurô-
nios McCulloch-Pitts, capaz de aprender aquilo que pudesse representar, tinha o
objetivo de identificar e classificar padrões em classes linearmente separáveis. Para
isso, ajustava as forças de entrada em rede, gradativamente, até a identificação de
objetos de forma consistente, convergindo em número de passos limitados. A meta
era – e continua sendo – a de projetar redes neurais artificiais com capacidade para
realizar descobertas sem necessidade de regras ou critérios para determinar a classe
a qual o elemento pertence.
--- PAGE 80 ---
|
Segmentos tecnológicos e aplicações 625
Hipóteses foram lançadas, como a do matemático britânico Irving John Good,
que trabalhou em criptografia com Turing em Bletchley Park. Em 1965, Good cunhou
o conceito de explosão de inteligência, referindo-se ao ciclo constante de criação de má-
quinas autossuficientes, capazes de elaborar outras máquinas cada vez mais inteligentes,
superando em muito a habilidade do intelecto humano. A máquina ultrainteligente
seria obtida em processo de autoaperfeiçoamento recursivo, a ponto de dispensar a
inventividade humana.
Gordon Moore, químico estadunidense e cofundador da Intel estimou,
também em 1965, que a capacidade dos processadores dobraria a cada dezoito
meses, previsão que ficou conhecida como lei de Moore. De fato, em quarenta
anos, a evolução dos chips seguiu uma trajetória exponencial, e, entre 2025 e 2030,
a capacidade de processamento deverá aproximar-se à do cérebro humano.
A escalada acelerada do desenvolvimento tecnológico sugere um ponto de
singularidade no qual a máquina poderia alcançar a inteligência super-humana.
A singularidade tecnológica é o ponto de virada no qual a invenção da superin-
teligência artificial ultrapassa o crescimento tecnológico convencional, trazendo
implicações imprevisíveis para o futuro da civilização. Nesse contexto, o agente
inteligente amplificado, software de IA geral, desenvolveria uma reação em prol
do autoaperfeiçoamento em ciclos ascendentes acelerados, implicando novas
gerações de IA cada vez mais inteligentes e repercutindo em uma explosão de IA
que ultrapassaria a qualidade da inteligência humana.
Em 1969, apesar de todo o manancial de potencialidades previsto no meio
científico e fictício, para o desenvolvimento da IA nos Estados Unidos, a Emenda
Mansfield ao Ato de Autorização Militar implicou imprimir maior praticidade e
utilidade aos projetos financiados pela Darpa. Por isso, programas de pesquisa pura
tiveram seus orçamentos reduzidos; por exemplo, o de reconhecimento de fala
da Universidade Carnegie Mellon, especialmente afetado com o corte de verbas.
Naquele mesmo ano, Arthur Bryson (Universidade de Stanford) e Yu-Shi Ho (Uni-
versidades de Havard e de Tsinghua) anunciaram o desenvolvimento do algoritmo
de retropropagação (backpropagation) crucial para o treinamento de redes neurais
perceptron multicamadas (MLP), que viria a ser popularizado na década de 1980.
O período 1974-1980 é considerado o primeiro inverno da IA, devido à di-
minuição no financiamento e no interesse de pesquisadores. Alguns antecedentes
motivaram o declínio, como o relatório Alpac, de 1966, o livro Perceptrons, de 1969
(Minsky e Papert, 1969), e o relatório Lighthill, de 1973. O primeiro, do Comitê
Assessor sobre Tradução Automática de Linguagem, concluía que a máquina de
tradução era cara e pouco acurada, por causa dos problemas de desambiguação no
--- PAGE 81 ---
|
626 Do paradoxo das habilidades à superinteligência pós-humana
sentido das palavras. O segundo, de autoria de Minsky e Papert (1969),19 provou
que o perceptron de camada simples não poderia computar paridade sob a condição
de localidade conjuntiva (parity-connecteness, indefinição do estímulo de entrada
e do reconhecimento de figura), o que levou ao abandono do conexionismo por
cerca de uma década. O terceiro, do professor britânico James Lighthill, condenou
aplicações práticas de IA por intratabilidade e explosão combinatória. Logo, foram
reduzidos os investimentos devido às expectativas excessivamente altas com relação
à capacidade ainda limitada dos programas de IA.
O ceticismo tomou conta dos meios acadêmicos. John Searle, filósofo
norte-americano, professor da Universidade de Berkeley, apresentou uma visão
oposta à IA forte, por acreditar ser impossível recriar a mente humana em um
computador. Para pensar, seria necessário ter vida interior e subjetividade. Em
sua concepção filosófica, a racionalidade, o livre-arbítrio e o poder da mente
eram essenciais na capacidade de produzir intencionalidade.
O experimento mental proposto por Searle (1980) para criticar a IA forte, sobre
a reprodução de estados mentais intencionais do cérebro e da consciência, é conhe-
cido como o argumento da sala chinesa, sendo parte da compreensão da dualidade
mente-cérebro. Searle pede que uma pessoa (CPU), que desconhece chinês, entre em
uma sala e receba uma folha (armazenamento) com texto de perguntas indecifráveis
em chinês. Após consultar uma lista de decodificação de caracteres chineses (banco
de dados) para o inglês, a pessoa devolve as respostas. Para o expectador, como há
coerência nas respostas, a sala que “fala” chinês (IA) passa no teste de Turing, mas,
na verdade, ocorre a manipulação de símbolos cuja semântica é desconhecida, im-
plicando que a correta execução não significava a correta compreensão.
No período 1980-1987, a ascensão de sistemas dedicados (expert systems)
trouxe novo alento e uma mudança no foco das pesquisas, com a retomada dos
financiamentos. A programação computacional de regras e processos normalmente
adotados por especialistas tinha por objetivo automatizar ou simular a tomada de
decisão. Carnegie Mellon desenvolveu o XCON (eXpert CONfigurer, conheci-
do como programa R1), para a seleção automática de componentes do sistema
computacional do modelo Vax da empresa DEC, representando uma economia
de US$ 40 milhões em seis anos.
Algumas empresas passaram a produzir computadores direcionados para
sistemas especialistas, as denominadas Lisp machines, por utilizarem a linguagem
de alto nível Lisp nos softwares voltados para IA. A substituição dessas máquinas
veio com a produção de estações de trabalho (workstations), fabricadas por empresas
19. O paradigma criado pela teoria construcionista de Seymour Papert, as linguagens têm sido codificadas dinamicamente
e reflexivamente, sistemas interativos de aprendizado de máquina têm sido projetados e a robótica de reabilitação tem
sido explorada por meio de experimentos de restauração prostética.
--- PAGE 82 ---
|
Segmentos tecnológicos e aplicações 627
como a Sun Microsystems, que se mostraram mais populares para a execução das
aplicações Lisp.
Na década de 1980, a abordagem conexionista da IA ganhou novo fôlego, com
as pesquisas voltadas para o problema de identificação de erros (credit-assignment)
nas camadas profundas de neurônios. O desafio no treinamento das redes neurais
profundas é determinar quais os neurônios são responsáveis por erros no resultado
de saída. Quando o erro é calculado, precisa ocorrer a retropropagação (backpro-
pagation) para que seja minimizado por novo ajuste de pesos nos neurônios de
forma que as futuras predições sejam mais precisas. As redes Hopfield auxiliam
a administrar o problema de atribuição de crédito, pois atualizam os pesos nas
conexões, funcionam como memórias associativas de conteúdo direcionável e
convergem para mínimos locais.
O neurocientista John Hopfield apresentou propriedades computacionais úteis
que podem emergir de sistemas físicos e neuronais coletivos com grande número
de componentes simples – equivalentes a neurônios. O trabalho intitulado Neural
Network and Physical Systems with Emergente Collective Computational Abilities
(Rede Neural e Sistemas Físicos com Habilidades Computacionais Coletivas Emer-
gentes) (Hopfield, 1982) descreve o modelo de rede neural recorrente conhecido
como rede Hopfield, no qual estados mínimos de energia seriam a memória que
permitiria o reconhecimento de padrões. Em 1983, Ackley, Hinton e Sejnowsky
aperfeiçoaram os neurônios determinísticos da rede Hopfield, trocando-os por
neurônios probabilísticos. Em 1986, Rumerlhart e McClelland resgataram o
algoritmo de aprendizado backpropagation, antes delineado por Paul Werbos, em
1974, e Bryson-Ho, em 1969. Portanto, ambas as tecnologias de redes Hopfield
(reconhecimento de padrões e memorização de informações) e de backpropagation
(ajustamento de pesos para minimizar erros) foram responsáveis pelo salto da
tecnologia de IA.
O segundo inverno da IA, período que se estendeu de 1987 a 1993, começou
com o súbito colapso da indústria de hardware especializado, com máquinas Lisp
superando os custos das estações de trabalho. Além disso, os sistemas especialistas
passaram a ser avaliados como limitados e dispendiosos em termos de atualização
e manutenção, impactando em percepção negativa capaz de inibir investidores e
governo. A tecnologia de IA parecia ter seguido o ciclo padrão de desenvolvimento
de novas tecnologias, conhecido como hype cycle, que começa com muita publici-
dade, atinge o pico de expectativas infladas e, em seguida, termina com desilusão.
Porém, de 1993 a 2011, o maior poder computacional orientado por dados
permitiu o retorno da IA, e Vernor Vinge popularizou e ampliou a ideia de Good
sobre a teoria da singularidade tecnológica. Entre as diversas obras, está The Coming
Technological Singularity: how to survive in the post-human era (Vinge, 1993), na
--- PAGE 83 ---
|
628 Do paradoxo das habilidades à superinteligência pós-humana
qual o autor apresenta preocupação com os efeitos da inteligência pós-humana
autoaperfeiçoada sobre humanidade, dada a real trajetória de desemprego tecno-
lógico. Outra abordagem pouco reconhecida, mas importante na visão de Vinge,
seria a amplificação da inteligência, que vem se processando muito naturalmente,
com o aperfeiçoamento da habilidade humana de acessar a informação e comu-
nicá-la aos outros.
Nesse sentido, Vinge reforça a ideia preconizada por quatro antecessores:
i) Gunther Stent (The coming of the golden age, de 1969), que considerou que o
desenvolvimento do transumano inteligente seria condição suficiente para frustrar
a expectativa de singularidade das máquinas; ii) John Searle (Minds, brains and
programs, de 1980), que refutou a possibilidade de computadores igualarem a
habilidade linguística dos humanos; iii) Hans Marovec (Mind children, de 1988),
que estimou que o homem estaria de dez a quarenta anos à frente da paridade de
hardware; e iv) Roger Penrose (The emperor’s new mind, de 1989), que argumentou
ser impossível modelar a consciência por meio de algoritmo de máquina Turing
convencional ou computador digital.
Em 1994, o filósofo, físico e matemático inglês Roger Penrose e o aneste-
siologista americano Stuart Hameroff propuseram a teoria da redução objetiva
orquestrada (orchestrated objective reduction – Orch-OR), conhecida como teoria
quântica da consciência. A proposição sugere que o fenômeno da consciência é
resultado de vibrações quânticas (efeitos quânticos gravitacionais), as quais ocorrem
em microtúbulos que formam o citoesqueleto dos neurônios cerebrais. Penrose
escreveu o livro Shadows of the Mind: a search for the missing science of consciousness,
no qual apresentou algumas hipóteses sobre o futuro da IA, no que diz respeito ao
potencial alcance do estado de consciência:
• considerava impraticável a reprodução da consciência humana por mo-
delagem convencional, pois aquela é não algorítmica;20
• acreditava que a produção de IA será possível com a computação quân-
tica, ou, mais decisivamente, com a nanobiotecnologia, capaz de simular
estados mentais e reproduzir a funcionalidade dos microtúbulos cerebrais,
que fazem a comunicação dos neurônios por meio de descargas elétricas
subatômicas e superposição quântica;
• enunciou que o colapso da função de onda quântica é crítica, impres-
cindível para a obtenção do estado de consciência;
20. No livro The Emperor’s New Mind: concerning computers, minds, and the laws of physics, Penrose (1989) argumentava
que, no atual estágio de desenvolvimento da física, seria impraticável explicar o fenômeno da consciência e propôs uma
ponte entre a mecânica clássica e a mecânica quântica, o funcionamento do cérebro humano por gravidade quântica –
em inglês, correct quantum gravity. Para explicar que um problema pode ser determinístico sem ser algorítmico, utilizou
uma variante do teorema de parada de Turing (Turing’s halting theorem).
--- PAGE 84 ---
|
Segmentos tecnológicos e aplicações 629
• ponderou que o colapso é o comportamento físico não algorítmico que
transcende o limite da computabilidade; e
• sentenciou que nenhuma MUT poderá emular o processamento da
mente humana por total incapacidade de reproduzir o mecanismo físico
não computável.
Desde o início de 1996, o campeão mundial de xadrez Garry Kasparov,
considerado o melhor enxadrista de todos os tempos, iniciou uma batalha com
software Deep Blue. O supercomputador, criado pela IBM para rodar o programa
específico, era capaz de analisar 200 milhões de posições por segundo, pois operava
com 256 coprocessadores. Kasparov venceu três partidas e empatou duas, com sua
capacidade de reflexão incomum, acima de cinquenta movimentos por minuto.
Até então, o oponente de Kasparov fora insuficiente em matéria de estratégia e
aprendizado, como uma autêntica IA o faria. Finalmente, em 1997, o Deep Blue
venceu o enxadrista com seu sistema convencional de busca e análise de combi-
nações possíveis para realização de jogadas. Kasparov declarou ter sido o último
humano campeão de xadrez.
Durante a década de 2000, relevantes esforços de desenvolvimento na indústria de
software foram direcionados para o segmento de jogos. Historicamente, o setor
de videogames ou jogos eletrônicos tem faturamento global que chega à ordem da
centena de bilhões de dólares. Portanto, um incentivo para P&D, além do efeito
transbordamento que proporciona a utilização dos jogos em outros setores, como
educação – e.g.: processo de aprendizagem lúdica; saúde – e.g: tratamento de
depressão, visão e reabilitação; e segurança – e.g.: simulação de condições críticas.
Ademais, as progressivas conquistas da TICs fizeram-se sentir também nas indústrias
de automação, de robótica e automobilística – e.g.: veículos autônomos.
Também nessa década, Raymond Kurzweil previu que a singularidade ocorre-
ria por volta de 2045, equiparando o nível de inteligência homem-máquina. Além
de inventor, Kurtzweil é autor de vários livros. Em The Age of Spiritual Machines
(Kurtzweil, 1999) postulou a lei dos retornos acelerados, na qual o estudo da evo-
lução tecnológica em diversas áreas de conhecimento mostraria uma tendência de
trajetória exponencial. Em 2001, no artigo intitulado The Law of Accelerating Returns,
Kurtzweil (2001) analisou a história da evolução tecnológica da humanidade. A
primeira asserção do texto propõe que a evolução da mudança tecnológica parece,
à primeira vista, intuitivamente linear, mas na verdade é exponencial, de modo que
os próximos cem anos do século XXI parecerão como 20 mil anos de progresso
corrente – ou seja, na taxa de crescimento atual.
Ao contrário do que aponta o senso comum, Kurtzweil acredita que a IA
e a inteligência humana tenderão a convergir no futuro. Em The Singularity is
Near: when humans transcend biology, Kurtzweil (2005) estendeu o conceito
--- PAGE 85 ---
|
630 Do paradoxo das habilidades à superinteligência pós-humana
de singularidade para a biologia e a medicina, acreditando que a reparação ou
substituição de órgãos do corpo possibilitaria o aumento da longevidade e, até
mesmo, o alcance da expectativa de vida ilimitada, a imortalidade. Ray e Peter
Diamandis fundaram a Universidade da Singularidade, em 2009, na qual inspiram
os estudantes a tratar de grandes desafios relacionados ao futuro da humanidade.
Em 2011, Jeff Dean e o pesquisador Greg Corrado, ambos da Google, e
Andrew Ng, professor de ciência da computação de Stanford, deram início ao pro-
jeto Google Brain, no qual construíram uma rede neural de 16 mil processadores
alimentada por uma quantidade massiva de dados, capaz de analisar 10 milhões
de capturas de tela do YouTube, em três dias, e representar padrões visuais ou re-
conhecer figuras. Em 2012, o primeiro veículo autômato, sem motorista, o Toyota
Prius, teve seu sistema de navegação aperfeiçoado pela Google com câmeras, radar
e laser. Para fazer os primeiros testes, as empresas conseguiram a licença do estado
de Nevada para utilizar as ruas e as estradas locais.
O ano de 2012 foi marcado pelos cem anos de nascimento de Turing e pelo
especial fortalecimento da IA, sem precedentes na trajetória histórica. O ponto de
inflexão veio com a ramificação do conhecimento denominada aprendizado de má-
quina, que motivou o redirecionamento do interesse acadêmico e o fluxo de inves-
timentos. A maior disponibilidade de dados, conectividade e poder computacional
permitiu avanços na aprendizagem de máquina, principalmente em redes neurais
e aprendizagem profunda, anunciando nova era de aumento de financiamento e
otimismo sobre o potencial de AI.
Nesse ano, o professor na Universidade de Toronto Geoffrey Hinton e equipe
lançaram o AlexNet, um modelo de rede neural profunda para reconhecimento de
imagem, que foi executado em processador gráfico e venceu o concurso ImageNet.
Hinton demonstrou que redes profundas podem processar dados não rotulados
(unlabelled data) automaticamente, como ocorre no reconhecimento de imagem e
textos. Outros três fatores contribuíram para esse progresso: i) a crescente quantidade
de dados para treinar os sistemas de IA (big data); ii) o aumento da capacidade de
processamento com as plataformas de computação em nuvem (cloud computing
platform); e iii) o desenvolvimento de técnicas e algoritmos sofisticados – e.g.:
aprendizagem profunda, descida em gradiente e propagação retroativa –, com uso
de plataformas Caffe (produto da GitHub e da Berkeley AI Research) em código
aberto (open source development platform).
Em 2013, Geoffrey Hinton foi convidado pela Google para trabalhar como
principal pesquisador em aprendizagem profunda. Em fevereiro de 2015, programa
desenvolvido pela equipe da Google aprendeu como jogar 49 jogos Atari, por si
mesmo. Demis Hassabis, um dos desenvolvedores, declarou que as condições de
contorno foram dadas, como a existência de pixels não ativados na tela e a pontuação
--- PAGE 86 ---
|
Segmentos tecnológicos e aplicações 631
elevada a ser atingida. Todo o resto teve de ser imaginado ou desenvolvido pelo
algoritmo, que conseguiu aprender as regras de todos os jogos apresentados com
desempenho tão bom ou melhor que os humanos. O que é mais curioso, o pro-
grama utilizava estratégias nunca ocorridas nas mentes dos jogadores profissionais.
Em março de 2016, o projeto da startup britânica DeepMind realizou outro
experimento com o jogo milenar chinês, mais complicado que o xadrez, denominado
go. Trabalhando para o Google, os cientistas da DeepMind bateram o campeão
mundial no complicado jogo de tabuleiro de go. O programa de rede neural deno-
minado AlphaGo venceu o sul-coreano Lee Sedol, de 34 anos, campeão mundial
por dezoito vezes. As manobras pouco ortodoxas e as táticas inovadoras deixaram
os especialistas atônitos. A estratégia do jogo consiste em confrontar dois jogadores
que movimentam as peças de forma a conquistar a maior área possível do tabuleiro.
Trata-se de mais de 10 mil posições possíveis, com duzentas opções de movimento
a cada jogada. Duas redes neurais artificiais funcionaram simultaneamente: uma
para decidir a jogada e outra para prever o resultado do jogo. O AlphaGo mescla
os métodos de árvore de busca Monte Carlo – representação simbólica construída
dinamicamente – e uma variedade de módulos de aprendizagem profunda, com o
intuito de estimar o valor de cada posição no tabuleiro.
A versão mais recente do programa, AlphaGO Zero, vem sendo alimentada
apenas com as regras do jogo, sem contribuições de jogadas humanas. O software
foi programado para jogar contra outras versões do AlphaGo, aprendendo com
as estratégias vencedoras e perdedoras. A versão Zero jogou cerca de 5 milhões de
partidas em três dias e, após esse período de aprendizagem solitária, venceu todas
as partidas disputadas com versões anteriores. Estratégias jamais vistas e criatividade
na movimentação das peças foram as qualidades atribuídas ao computador pelos
especialistas, tornando o conhecimento humano praticamente obsoleto.
O projeto Google DeepMind associou-se à Universidade de Oxford, em
junho de 2016, para desenvolver um código-chave de interrupção (kill-switch),
que visava assegurar que o sistema de IA poderia ser interrompido repetidamente
e seguramente pelos supervisores humanos, sem aprender como evitar a manobra
de intervenção. Artigo de coautoria das universidades de Berkeley e Stanford, do
Google Brain e do OpenIA apresentou os problemas concretos de segurança que
poderiam ter origem no desenvolvimento de sistemas de IA. Em essência, o projeto
buscou vencer a maior e mais persistente crítica à capacidade de desenvolvimento
da IA. O problema da qualificação – ou seja, a inabilidade de identificar ou decidir
o que realmente seria significativo e importante a partir de um conjunto de regras
simples – parecia sanado. Dessa forma, a IA estaria preparada para emular o com-
portamento complexo do homem.
--- PAGE 87 ---
|
632 Do paradoxo das habilidades à superinteligência pós-humana
Os renomados cientistas da computação Stuart Russell e Peter Norvig (2016)
escreveram o livro texto sobre IA mais adotado nas universidades em todo o mun-
do. Sob o título Inteligência Artificial: uma abordagem moderna, os autores fizeram
um balanço dos elementos incorporados ao desenho do agente inteligente padrão
mais recente, especialmente no que se refere ao problema de qualificação. Para
isso, partiram das críticas e da proposta feita por Hubert e Stuart Dreyfus, três
décadas antes, a respeito da vulnerabilidade dos agentes lógicos ao problema de
qualificação, da estruturação do modelo de habilidades e dos estágios de aquisição
de expertise humana no contexto holístico, quais sejam:
• uma boa generalização seria impossível sem conhecimento de contexto
histórico, como no processamento baseado em regras da boa e velha IA
(good and old-fashioned AI – Gofai); interpretação da argumentação de
Russel e Norvig (IARN): existem técnicas para incorporar conhecimento
prévio disponível em algoritmos de aprendizagem, bastando explicitá-lo
de forma clara na programação;
• o aprendizado de rede neural requereria a identificação de inputs relevantes
e outputs corretos – isto é, a operação autônoma sem supervisor humano
seria inviável; IARN: avanços como aprendizado sem supervisão e o
aprendizado por reforço tornaram-se factíveis no processo de automação
das tecnologias de IA;
• o algoritmo de aprendizado apresentaria desempenho sofrível em presença
de múltiplas características (features); IARN: na verdade, os novos métodos
(e.g.: máquina de vetor suporte – SVM, PLN e visão computacional) lidam
muito bem com amplo conjunto de atributos – na ordem de milhões; e
• o cérebro humano teria sensores capazes de extrair aspectos relevantes
do contexto e a habilidade para selecionar respostas corretas instantane-
amente; IARN: os resultados obtidos teoricamente para direcionamento
dos sensores de visão ativa (teoria do valor da informação) têm sido
incorporados praticamente na área de robótica.
Russell e Norvig (2016) chegaram à conclusão de que todas as questões de
conhecimento baseado no senso comum estão sendo incorporadas no desenho do
agente de IA padrão, representando evoluções e não limitações. Sendo assim, à
medida que as máquinas avançam em grau de sofisticação, é natural que haja uma
dissolução das diferenças entre IA forte e IA fraca.
Para esclarecer a questão da máquina pensante na IA forte (página 1026 do
livro) e da consciência, os autores fazem menção aos estados intencionais (crença,
conhecimento, desejo e medo), os quais se referem a aspectos do mundo externo e
ao estado mental que é determinado pelo estado interno do cérebro. O conteúdo
--- PAGE 88 ---
|
Segmentos tecnológicos e aplicações 633
dos estados mentais pode ser interpretado sob dois pontos de vista: i) conteúdo
abrangente (wide content), observador onisciente externo com acesso a toda a si-
tuação, interpretando e decidindo; e ii) conteúdo limitado (narrow content), que
considera apenas o estado do cérebro interior – i.e., tanto faz ter a impressão real
ou simulada. Por conseguinte, decidir sobre a capacidade de uma IA pensar ou não
vai depender das condições externas ao sistema – ou seja, a cognição ocorre dentro
do corpo, que, por sua vez, está embebido no ambiente; abordagem denominada
de cognição incorporada (embodied cognition).
Para explicar melhor a condição, uma analogia é feita com a narrativa do cérebro
encubado do filme Matrix (brain-in-a-vat), em que o órgão cresce alimentado pelos
sinais eletrônicos produzidos por uma simulação de computador. No mundo fictício,
os sinais do cérebro são interceptados para modificar o programa do computador
conforme apropriado. Portanto, ocorre a reprodução do falso estado mental de
comer um hamburger, por exemplo, sem nunca ter experimentado um de fato. Na
visão de conteúdo amplo, o estado mental envolve tanto o estado cerebral quanto
o contexto ambiental. Na visão de conteúdo estreito, somente o estado cerebral
é considerado, seja esse o do real comedor de hamburgers ou o do fictício, com
origem no cérebro encubado. Portanto, o que importa sobre o estado cerebral é sua
função no processamento mental; qual seja, a de reconhecer padrões e de usar essas
informações para tomar decisões sobre o mundo, seja este real ou virtual.
Em 2015, a Google começou a utilizar DL para melhorar as buscas com o uso
da ferramenta denominada RankBrain. Isso significa que o motor de busca do Google
passou a ser híbrido – isto é, uma mescla de operações simbólicas com aprendizagem
profunda. O sistema de recuperação de informação funciona com quantidades enormes
de dados que permitem obter resultados de grande acurácia.
Desde 2012, o motor de busca do Google vem apresentando maior grau
de sofisticação e passou a reunir todas as informações a respeito de determinada
pesquisa demandada pelo usuário em sua base de conhecimento. Os resultados são
apresentados nos gráficos de conhecimento (Google knowledge graph), com links,
imagens e gráficos dispostos acima, abaixo e nas laterais da tela, interrelacionando
diferentes conceitos e diminuindo a ambiguidade da linguagem natural. Mais
recentemente, o sistema vem utilizando as ferramentas de rede neural, como Rank-
Brain, Bert e MUM, para melhorar a pesquisa, ganhar escala e fornecer resultados
melhores com o motor de busca.
Em matéria de PLN, a concorrência vem da OpenIA, que apresentou o
GPT-3, com aglutinação de grandes blocos de textos para formar um todo coeso
e coerente, em 2018. As versões do GPT (1, 2, 3) são modelos de PLN baseados
--- PAGE 89 ---
|
634 Do paradoxo das habilidades à superinteligência pós-humana
em transformador21 – ou seja, capazes de emular o raciocínio humano para pro-
dução de textos. Os sistemas foram desenvolvidos pelo laboratório de pesquisa
OpenAI, sediado em São Francisco, Estados Unidos, e a versão mais recente, o
GPT-3 utiliza DL para treinamento em milhares de textos com realismo sem pre-
cedentes. Nas versões anteriores do algoritmo, o GPT-1 melhorava a compreensão
da linguagem pelo pré-treinamento; o GPT-2 fazia treinamento multitarefa sem
supervisão, utilizando 1,5 bilhão de parâmetros com valores de rede ajustados
durante o treinamento; e o GPT-3 ajustava 175 bilhões de parâmetros durante o
treinamento sem supervisão.
A revisão do histórico feita até aqui permitiu observar que os principais
representantes da escola conexionista foram McCulloch, Pitts, Hebb, Rosenblatt
e Widrow, enquanto a escola simbolista tem como patronos McCarthy, Minsky,
Newell, Shaw e Simon. Embora o livro Perceptrons, de Minsky e Papert (1969),
tivesse trazido indagações a respeito do modelo de redes neurais por falta de sus-
tentação matemática, J. Hopfield resgatou a credibilidade das redes neurais, em
1980. Porém, as escolas de IA evoluíram em pelo menos mais três ramificações,
como será estudado.
3.2.2 Escolas de IA e respectivos algoritmos
Indubitavelmente, Domingos (2015) é, dos estudiosos analisados, o mais hábil na
classificação e na apresentação das escolas de IA, especificamente no ramo de ML.
No livro Algoritmo Mestre (Master Algorithm), ele descreve-as como sendo cinco
tribos, com origens e algoritmos de propósito geral bem-definidos:
• os simbolistas, com origem na lógica e na filosofia, trabalham a dedução
inversa (indução);
• os conexionistas, cuja fonte é a neurociência, têm o backpropagation
como algoritmo mestre;
• os evolucionistas, com base na biologia evolucionária, tomam a progra-
mação genética como instrumento de trabalho;
• os bayesianistas, sustentados pela estatística, preferem a inferência pro-
babilística; e
• os analogistas, instruídos pela psicologia, escolhem as máquinas Kernel.
Apesar de as cinco principais escolas apresentarem algoritmos mestres distintos,
a proposta de Domingos é de que estas devem evoluir e convergir para apenas um
algoritmo verdadeiro, que resolva todos os cinco tipos de problemas, como analisado
21. O transformador é a arquitetura de rede neural cuja função é prever a próxima palavra na sequência de entrada na
frase, atribuindo pesos maiores às partes mais importantes e menores àquelas menos relevantes.
--- PAGE 90 ---
|
Segmentos tecnológicos e aplicações 635
no quadro 8. Os esquemas de integração, apresentados na última coluna, permitem
visualizar a ordenação hierárquica das abordagens metodológicas utilizadas por
cada tribo, de maneira a prover a concatenação lógica necessária para a integração
de todos os modelos em sequência, visando a aproximação para modelo único.
QUADRO 8
Principais características das escolas de IA
Escola e
Algoritmo mestre Características Esquema de integração
representantes
Aprender por seleção Estruturas evolutivas Evolucionários
Programação genética –
Evolucionistas natural – i.e., por meio
combina e faz evoluir o
John Kosa, de estrutura de apren-
algoritmo computacional,
John Holland e dizado, que ajusta o
reproduzindo o processo
Hod Lipson mecanismo (cérebro)
de seleção natural.
à solução.
Parâmetros de
Backpropagation – aprendizagem Conectivistas
Reverter a engenharia
Conexionistas compara resultados
do cérebro, que aprende
Yann LeCunn (saída) com parâmetros
ajustando as forças
Geoff Hinton e desejados (solução),
de conexão entre os
Yoshua Bengio para ajustar neurônios
neurônios.
e tender para solução.
Composição Simbolistas
expedita de
Simbolistas Dedução inversa – in- Reduzir toda a inteli- elementos
Tom Mitchell, corpora conhecimento gência à manipulação
Steve Muggleton e preexistente e generaliza de símbolos, como
Ross Quinlan para o desconhecido. equações matemáticas.
Evidência ponderada Bayesianos
Inferir sobre incertezas
Algoritmo de inferência
Bayesianistas do conhecimento é
probabilística – incor-
David Heckerman, aprender, lidando com
pora novas evidências,
Judea Pearl e informações contra-
conforme o teorema de
Michael Jordan ditórias, incompletas
Bayes e suas derivações.
e ruidosas.
Mapeando novas
Analogistas
situações
Support vector machine –
Analogistas
compreende qual experi- Reconhecer similaridades
Peter Hart,
ência deve ser relembrada entre situações e inferir
Vladimir Vapnik e
e combinada para elaborar outras similaridades.
Douglas Hofstadter.
nova projeção.
Fonte: Domingos (2015).
A escola evolucionista tem como principal representante John Henry Holland,
sucessor das pesquisas de Von Neumann. A ferramenta é o algoritmo genético, que
busca soluções aproximadas para problemas de otimização. Em analogia com a
--- PAGE 91 ---
|
636 Do paradoxo das habilidades à superinteligência pós-humana
teoria genética de seleção natural e a teoria da origem das espécies, na computação
evolucionária, o algoritmo genético é alimentado por uma função de ajuste que se
adequa ao propósito desejado. Em vez de espécies em evolução, os programas são
sucedidos por gerações, ou rodadas de execução, alimentadas por um conjunto de
soluções aleatórias possíveis, que se tornam cada vez mais aderentes à função-objetivo.
Cada geração é avaliada pelo conjunto de soluções resultantes, que são recombinadas
ou modificadas para obter novo conjunto de soluções.
As abordagens conexionistas (ou conexistas), biológicas ou ascendentes
envolvem modelos de redes neurais artificiais, acionadas por impulsos elétricos.
No algoritmo perceptron, o resultado do processamento do neurônio é discreto ou
binário, certo ou errado, diferentemente do algoritmo backpropagation, no qual o
resultado processado no neurônio pode ser contínuo, dependendo do impulso de
retroalimentação que recebe da conexão com outro neurônio. Isso faz do backpro-
pagation22 um algoritmo mais poderoso, com perceptrons multicamadas (convoluted
frontiers) que colecionam dados – e.g., imagens captadas pelos sensores – e ponderam
os impulsos até convergirem gradualmente para os valores dados como referência.
A escola simbolista, também conhecida como cognitivista, logicista ou des-
cendente, envolve processos cognitivos característicos do raciocínio humano. Os
problemas são abordados por modelos simbólicos, representando o conhecimento
por intermédio de sinais e exigindo uma definição robusta para serem resolvidos,
como na matemática. A interpretação semântica é possível pela representação do
conhecimento, constituída de lógica formal, por frames, por redes semânticas, por
scripts e por usos de regras explícitas. Para Domingos, os fundamentos da escola de
aprendizado de máquina simbolista tornam-a mais próxima da IA que as outras
escolas. O simbolismo seria o caminho mais curto para obtenção do algoritmo
mestre, pois evitaria a complexidade matemática dos bayesianistas e o mapeamento
cerebral dos conexionistas.
Abordagens dominantes em IA: i) data mining – reconhecimento de padrões
e estatísticas para aprendizado de máquina; ii) bayesian network – racionalização
da incerteza no conhecimento; e iii) hidden Markov models – reconhecimento de
discurso, baseado em teoria matemática e construído a partir de processo de trei-
namento em larga escala de informações de discursos reais, garantindo robustez
no desempenho. Com a tradução de máquina ocorre algo similar, com aplicação
de princípios da teoria da informação.
22. Pela definição da Techopedia.com – disponível em: https://www.techopedia.com/definition/17833/backpropagation –,
backpropagation (retropropagação) é o algoritmo ou técnica utilizada em IA para ajustar funções matemáticas e
melhorar as saídas ou os resultados durante o processo de treinamento de determinadas classes de redes neurais
artificiais. O nível de precisão de cada nó de entrada/saída conectado em rede é expresso por uma função de perda
(taxa de erro), cujo gradiente matemático é calculado pela retropropagação, o que permite aumentar a ponderação
dos nós com menor taxa de erro. Ou seja, após um passo à frente em rede, o algoritmo volta um passo atrás, com o
objetivo de ajustar os pesos do modelo.
--- PAGE 92 ---
|
Segmentos tecnológicos e aplicações 637
3.3 Caracterização da tecnologia de IA
O relatório The AI Index 2019 Annual Report (Perrault et al., 2019) trouxe um
interessante dado a respeito do desempenho da IA no aprendizado de máquina
(computação visual) em infraestrutura de nuvem, no último ano e meio. O estado
da arte em métodos de classificação de imagem para ML supervisionada mostra
que o tempo de treinamento caiu de três horas, em outubro de 2017, para 88 se-
gundos, em julho de 2019. A redução do custo de treinamento é impressionante,
de US$ 2.323,00, em outubro de 2017, para US$ 12,00, em setembro de 2018,
no modelo ResNet, com acurácia de 93%.
3.3.1 Aprendizagem de máquina em rede neural artificial
O desempenho crescente de ML, principalmente no reconhecimento de imagens
e PLN, deveu-se ao uso intensivo de dados e tempo de processamento. Contudo,
os resultados alcançados pelos sistemas, a partir desses insumos, ainda requerem
abordagens de mensuração padronizadas de eficiência. Os tipos de aprendizagem de
ML são: i) supervisionada – quando existe um agente externo que indica a resposta
desejada para uma entrada padrão; ii) não supervisionada (auto-organizada) – ine-
xistência de agente supervisor para apontar a saída; e iii) por reforço – quando um
crítico externo avalia a resposta dada pela rede.
As principais características do aprendizado nas redes neurais artificiais são:
• treinamento: fase em que a máquina (rede neural artificial) é alimentada
com perguntas e respostas;
• auto-organização: criação de representação de informação no interior da
máquina (rede neural artificial);
• tolerância a falhas: máquina (rede neural artificial) armazena de forma
redundante e responde satisfatoriamente, ainda que danificada;
• flexibilidade: alterações na entrada são toleradas pela rede neural artificial,
apresentando resposta constante; e
• tempo real: estrutura paralela pode obter respostas rápidas.
A arquitetura da rede neural artificial é definida pela disposição dos neurônios
nas camadas e pelo padrão de conexão dessas camadas. Na rede neural artificial sem
retroalimentação (feed forward), o sinal é unidirecional da entrada para a saída de
camadas não conectadas. Na rede neural artificial com retroalimentação (recurrent),
a saída pode alimentar a própria camada ou camadas anteriores, o sinal bidire-
cional, a memória dinâmica e a representação de estados em sistemas dinâmicos,
bem como a rede de Hopfield. A rede recorrente provê reconhecimento de padrões
complexos e previsões especializadas, a partir de dados sensoriais frequentemente
coletados para detecção de anormalidades.
--- PAGE 93 ---
|
638 Do paradoxo das habilidades à superinteligência pós-humana
FIGURA 12
Desenho esquemático de rede neural profunda
1 1 1 1
0 0 1 0
0
Imagem de 1 1 0 0 Imagem
de saída
entrada em 1
pixels 1 1 1 1
0
1 1 0 0
1 1 1 1
Camada 1 Camada 2 Camada 3 Camada 4 Camada 5
Valores pixel Bordas Combinações Características Combinações de
detectados identificadas de bordas identificadas características
identificadas identificadas
Elaboração da autora.
3.3.2 Desenvolvimento da IA
Enquanto tecnologia de propósito geral, a ubiquidade da inteligência artificial faz-se
presente em inúmeras áreas de aplicação, desde análise de texto até automação de
processos robóticos. A quantidade de desenvolvedores de softwares de IA no mercado
é grande, mas é possível identificar as maiores empresas e seus principais produtos.
1) IBM – computação cognitiva
a) treinamento de máquina para sutilezas – e.g.: sorriso de alegria/ironia;
b) textos apuram emoções – e.g.: carga emocional dos textos;
c) inteligência conversacional – e.g.: detecção, análise, processamento
e resposta a estados emocionais;
d) base de dados: milhões de mensagens de Twitter;
e) três tecnologias: visão computacional – e.g.: câmaras para avaliação
facial –, captura de áudio – e.g.: formatação de discurso – e sensores
com monitor de alterações fisiológicas – e.g.: frequência cardíaca,
suor, adrenalina e temperatura corporal;
f) Jeopardy: jogo popular para engajamento em comunicações comple-
xas como trocadilhos, que requer um montante enorme de combina-
ções de padrões retirados de enciclopédias, jornais e textos sagrados;
g) Watson Assistente: chatbot PLN, busca respostas em base de co-
nhecimentos; e
--- PAGE 94 ---
|
Segmentos tecnológicos e aplicações 639
h) Miss Debater: IA preparada para debate em alto nível, apesar da
derrota para o mestre do argumento Harish Natarajan, durante a
Think Conference, de 2019.
2) Microsoft – serviço cognitivo
a) transforma voz em texto;
b) descreve e indexa imagens de forma semântica;
c) realiza análise emocional;
d) chatbot Tay: caso emblemático que espelhou comportamento dis-
criminatório racista, xenófobo e sexista após interagir com gamers
(18 a 24 anos de idade) em redes sociais, devido aos dados com
viés de opinião refletidos no aprendizado de máquina, em 2016; e
e) assistente virtual Cortana: lançado em 2014 e desativado em 2020.
3) Google Brain – segmento de máquinas inteligentes
a) AutoML: controlador neural programado para desenhar modelos
de aprendizagem de máquina, com tarefas específicas;
b) Google Assistente Virtual: conversação, ligações, pesquisas e mensagens;
c) assistente virtual Now: desenvolvido para smartphones Nexus; e
d) assistente virtual Duplex: desenvolvido para marcar e desmarcar
reservas em estabelecimentos de serviço e checar horários de fun-
cionamento, em 2018.
4) Apple – funções cognitivas de alto nível
a) assistente pessoais Siri e Viv: desempenham tarefas online direcio-
nando para mídias preferenciais, de acordo com o perfil do usuário;
b) Siri teve origem na Darpa, Projeto Calo, com funcionalidades de alto
nível, tais como: i) organização e priorização de informações (emails,
reuniões, páginas na internet, arquivos e apresentações); ii) mediação
de comunicações humanas (acesso a fóruns sociais, transcrição de
reuniões e especificação da atuação de cada participante); iii) gerencia-
mento de tarefas (autorização de passagens aéreas); iv) programações
e otimização de tempo (agendas congestionadas); e v) alocação de
recursos (aquisição de serviços eletrônicos novos); e
c) Ajax e AppleGPT: em processo de desenvolvimento.
5) SystemAnalysis Programmentwicklung (SAP) – cognição industrial
--- PAGE 95 ---
|
640 Do paradoxo das habilidades à superinteligência pós-humana
a) assistente virtual Leonardo para ambientes de IoT em plataforma;
b) desempenho de atividades financeiras, compras e vendas ou marketing; e
c) otimização das operações em cadeias de suprimento e inteligência
de negócios.
6) Amazon Web Services (AWS)
a) Assistente Alexa, chatbots, reconhecimento de fala e idiomas;
b) análise de dados de saúde (healthlake) e compreensão médica;
c) detecção de anomalias e manutenção preventiva de máquinas;
d) automatização de códigos e desempenho das aplicações (DevOps
Guru); e
e) Visão computacional, previsão de métricas de negócios, automação
de análise e extração de dados.
3.3.3 IA explicada pelos paradoxos de Moravec e Polanyi
A essência do paradoxo de Moravec23 consiste na constatação de que o raciocínio
de alto nível requer muito menos programação computacional do que as ativi-
dades senso-motoras, de baixo nível de exigência intelectual. Isto é, problemas
lógicos e complexos – como o pensamento abstrato, testes de inteligência, xadrez,
matemática e engenharia, considerados de alto nível –representam algum grau
de dificuldade e habilidades especiais para os humanos. Enquanto atividades
automáticas, como caminhar, mover, distinguir objetos e reconhecer faces e
vozes, requerem baixo nível de racionalidade humana, mas enorme esforço para
elaboração de algoritmos computacionais.
Para Moravec, a diferenciação entre a IA e a inteligência humana é explicada
pela engenharia reversa do processamento biológico no cérebro humano. O pen-
samento abstrato faz parte da consciência há uma centena de milhares de anos,
o que pode ser considerada uma habilidade muita nova em termos filogenéticos.
Portanto, houve menos tempo de seleção natural na evolução humana para o
aperfeiçoamento e a otimização dessa capacidade, sendo mais fácil simular esse
comportamento no computador. Por sua vez, as atividades motoras e sensoriais
são mais difíceis de reproduzir por serem inconscientes e ancestrais na memória
humana, tendo exigido um longo processo de aperfeiçoamento, devido ao ativismo
mais decisivo para a seleção natural.
23. Hans Peter Moravec, austríaco especializado em robótica, IA e adepto do futurismo e do transumanismo, é membro
do Instituto de Robótica da Universidade de Carnegie Mellon e pesquisa a visão computacional.
--- PAGE 96 ---
|
Segmentos tecnológicos e aplicações 641
Na citação de Rotenberg (2013, p. 109, tradução nossa), “estamos mais cons-
cientes de processos simples que não funcionam do que de processos complexos
que funcionam sem falhas”. Esse autor também aponta limitações nas explicações
oferecidas por Moravec, pois considera que a habilidade criativa, das mais recentes
funcionalidades na evolução da genética humana, é processada no lóbulo frontal
direito, uma das últimas áreas a amadurecer. Contudo, somente os humanos se-
riam detentores de habilidades criativas, pela impossibilidade, até o momento, de
elaborar algoritmo de processamento de criatividade em computadores.
Na verdade, para os indivíduos que reproduzem resultados e soluções cria-
tivas, o processo é feito sem esforço. Os sinais elétricos constatam que a atividade
cerebral é requerida somente para resolver tarefas lógicas. Portanto, justamente por
serem simples e naturais é que as atividades criativas, motoras e sensitivas são tão
difíceis de serem reproduzidas computacionalmente. Questões científicas e jogos
intelectuais são computáveis pelo fato de serem orientados pelo lado esquerdo do
cérebro, cuja capacidade analítica facilita a identificação das relações para resolução
de problemas, em contexto monossemântico, explica Rotenberg.
O principal obstáculo para o desenvolvimento de programação de IA seria
a falta de acesso consciente ao conhecimento. Em 2014, David Autor recorreu ao
filósofo Polanyi,24 com o objetivo de explicar por que é impossível dizer mais sobre
o que é conhecido (we can know more than we can tell). O paradoxo de Polanyi,
como batizado por Autor, sintetiza a oposição entre conhecimento tácito e conhe-
cimento explícito. O conhecimento tácito é absorvido inconscientemente a partir
da experimentação; disso decorre ser difícil expressá-lo de forma objetiva, tangível
e verbal. Enquanto o conhecimento explícito segue a linha de raciocínio direta e
formal, como fruto do aprendizado convencional. O conhecimento humano a
respeito de como as coisas funcionam no mundo é influenciado por julgamentos,
habilidades, criatividade e intuição individual. Essa dimensão perceptiva é a mais
desafiadora para o entendimento dos processos cognitivos.
Portanto, a difícil tarefa descrita por Polanyi, de explicar à máquina como
deve proceder em determinadas condições, tem sido superada por meio de machi-
ne learning. Treinar a máquina a tomar decisões emulando o processo intelectivo
humano implica adaptar-se às condições favoráveis ou desfavoráveis do ambiente
no qual atua. Assim, outra providência essencial faz-se necessária; qual seja: sim-
plificar o ambiente de treinamento para que a máquina possa estabelecer regras
mais facilmente a partir dos dados disponíveis coletados.25
24. O filósofo anglo-húngaro Michael Polanyi escreveu, em 1966, The Tacit Dimension (A Dimensão Tácita) – em que
teoriza ser impossível explicar de forma lógica as causas por detrás das habilidades humanas para adquirir conhecimento.
25. Disponível em: https://aiofthings.telefonicatech.com/pt/recursos/datapedia/paradoxo-polanyi.
--- PAGE 97 ---
|
642 Do paradoxo das habilidades à superinteligência pós-humana
3.4 Regulação e ética em IA
Governos em todo o mundo têm evitado regular o mercado de IA, devido à rápida
transformação e evolução da tecnologia. Em vez de fazê-lo mais diretamente pela
via legislativa, a abordagem mais cautelosa tem sido a de formar conselhos de cú-
pula, constituídos por especialistas na área, para acompanhar o desenvolvimento
de padrões, tendências de consolidação e possíveis violações ao arcabouço legal
existente. Esses grupos, muito provavelmente, serão os embriões de futuros órgãos
de regulação que, mais cedo ou mais tarde, terão de ser implantados. Contudo,
o detalhe mais relevante nesse contexto é que, em sendo a IA uma tecnologia de
propósito geral, provavelmente, todas as áreas do conhecimento humano serão
impactadas e estarão envolvidas na elaboração desse novo marco legal.
Além de potenciais vulnerabilidades, bem conhecidas no campo das apli-
cações em TICs, como violação da privacidade de dados pessoais, julgamentos
discriminatórios e soluções obtidas a partir de dados enviesados, as tecnologias
de IA apresentam preocupações adicionais. A delegação de responsabilidade para
os algoritmos autônomos avançados, visando obter resultados aprimorados, pode
implicar desconhecimento da abordagem computacional adotada pelo sistema de
IA, por parte de seus desenvolvedores. Por causa disso, há necessidade de plena
explicabilidade e transparência das rotinas, para auditoria e posterior validação.
Em analogia ao fenômeno denominado caixa-preta, ignorar a cadeia pro-
dutiva de um produto (processamento de IA) significa desconsiderar o trajeto de
montagem dos componentes do produto (informações), mesmo que o insumo
(entrada de dados) e o resultado (saída de dados) sejam conhecidos. O aperfeiçoa-
mento incremental inerente ao processo de ML, definido a partir de um conjunto
de condições de contorno muito restritas, com pouca ou nenhuma interferência
do desenvolvedor, gera confiança exagerada nas soluções propostas. Logo, o pro-
blema da indecidibilidade, causado pelo processamento de IA e pouco virtuoso,
enquanto intrínseco ao caráter humano, representa uma ameaça ao processo de
tomada de decisão que se pauta exclusivamente a partir de soluções propostas pelo
mecanismo de IA.
A decodificação da IA explicável (XAI – explainable artificial intelligence) tem
recebido atenção redobrada dos especialistas e da comunidade científica, conforme atesta
a estratégia de desenvolvimento de IA do governo indiano, detalhada na subseção 3.5.
Segundo o documento, o projeto mais bem-sucedido, na tentativa de explicar como e
por que os algoritmos de ML trabalham, vem da agência norte-americana Darpa. O
objetivo do projeto é extrair a lógica do mecanismo utilizado pela tecnologia de IA,
explicitando os pontos fortes e os pontos fracos, de modo a viabilizar o entendimento
e especificar o comportamento a ser adotado futuramente.
--- PAGE 98 ---
|
Segmentos tecnológicos e aplicações 643
De acordo com a Darpa, o programa XAI aborda problemas em duas áreas
de ML: i) classificação de eventos de interesse, com dados heterogêneos de mul-
timídia; e ii) aprendizado por reforço para simular missões, criando soluções para
elaboração de políticas. A psicologia envolvida no processo de explanação tem sido
examinada com o intuito de testar os protótipos e demonstrar as implementações.
No início de 2018, os resultados da fase-1 de avaliação dos primeiros estudos-piloto
foram apresentados, e, ao final do programa, revelaram-se os módulos de interface
computador-humano (HCI), além da biblioteca de ferramentas de ML, que poderão
ser empregados para desenvolver sistemas de IA explicáveis.
Além dessa iniciativa, existem diferentes perspectivas capitaneadas por outros ins-
titutos de pesquisa que merecem mapeamento, com o objetivo de identificar sinergias:
• Estratégia de IA (AI strategy), Centro de IA Compatível com Humano
de Berkley (Berkley Center for Human-Compatible IA) e parceria em
IA (partnership on AI) – Estados Unidos;
• Vector Institute – Canadá;
• Centro de Pesquisa Alemão para IA (German Research Center for Arti-
ficial Intelligence) – Alemanha;
• Institute of Electrical and Electronics Engineers (IEEE) Ethics on Auto-
nomous System, por meio do manual Ethical Alined Design e da Iniciativa
Global para Considerações Éticas em Inteligência Artificial e Sistemas
Autônomos, de 2017, que resultou na IEEE P7000TM, uma série de
padrões orientados pela ética; e
• Conselho de IA do Instituto Allan (Turing AI Council, Allan Turing
Institute), Centro de Inovação e Ética de Dados (Center for Data Ethics
and Innovation) e British Standard Institute, por meio da publicação BS
8611:2016, Guide to the Ethical Design and Application of Robots and
Robotic Systems26 – Reino Unido.
No Brasil, o Projeto de Lei do Senado Federal (PLS) no 5.051/2019, que
trata de princípios para utilização da IA, foi analisado pela equipe do Centro
de Pesquisa em Direito, Tecnologia e Inovação – DTI-BR (Ferreira Neto et al.,
2019). A responsabilização objetiva é tratada como exceção e a subjetiva como
regra, como descrito a seguir.
1) Identificação de supervisor – A realidade prática do desenvolvimento de
programação de IA é a elaboração coletiva, implicando responsabilidade com-
partilhada devido à repartição de tarefas, que impacta na existência de figura
central com pleno conhecimento estratégico e controle de todo o sistema.
26. Para mais informações, ver o link disponível em: https://standardsdevelopment.bsigroup.com/projects/9021-05777#/section.
--- PAGE 99 ---
|
644 Do paradoxo das habilidades à superinteligência pós-humana
2) Sublocação de software de IA – O usuário com software licenciado pode
inclusive tomar parte no desenvolvimento do sistema aberto, herdando
a responsabilidade conforme o uso que faça do instrumento.
3) Autômatos de IA – O compartilhamento de autômatos pelos usuários
implica controle preciso de posse e tempo de utilização da máquina ou
equipamento (veículo, robô etc.), além da responsabilidade direta do
fabricante – incluindo-se cadeia produtiva – e solidária de quem revende
(distribuidor) e aluga (locatário).
Transparência:
• abertura de código de programa de IA deve ser condição sine-qua-non, mas
não suficiente, para garantir a transparência, cujo fundamento envolve
entender os dados evolvidos, a procedência, a qualidade, assim como o
processo de decisão automática do algoritmo de IA;
• prevenção quanto à reprodução de comportamentos preconceituosos,
de raça, gênero e idade, com consequente enviesamento de resultados;
• garantia de rastreabilidade e auditabilidade de tomada de decisão da IA,
pelo usuário em primeira instância e pela autoridade pública responsável,
em caso de acionamento; e
• sistema de patente de dados (monopólio de dados).
Privacidade dos dados e defesa do consumidor:
• art. 20 da Lei Geral de Proteção de Dados Pessoais – LGPD (Lei
no 13.709/2018) sobre controle dos dados
Art. 20: o titular dos dados tem direito a solicitar revisão, por pessoa natural, de
decisões tomadas unicamente com base em tratamento automatizado de dados pes-
soais que afetem seus interesses, inclusive de decisões destinadas a definir o seu perfil
pessoal, profissional, de consumo e de crédito ou os aspectos de sua personalidade.
§ 1o o controlador deverá fornecer, sempre que solicitadas, informações claras e
adequadas a respeito dos critérios e dos procedimentos utilizados para a decisão
automatizada, observados os segredos comercial e industrial.
§ 2o em caso de não oferecimento de informações de que trata o § 1o deste artigo
baseado na observância de segredo comercial e industrial, a autoridade nacional po-
derá realizar auditoria para verificação de aspectos discriminatórios em tratamento
automatizado de dados pessoais (Brasil, 2018, art. 20).
--- PAGE 100 ---
|
Segmentos tecnológicos e aplicações 645
3.5 Estratégias de IA
Governo, academia e iniciativa privada de países desenvolvidos e em desenvolvi-
mento têm mobilizado esforços para entender as necessidades e organizar melhor
o desenvolvimento da tecnologia de IA. Nessa disposição que promete servir a
todos os setores econômicos no futuro, indistintamente, os Estados Unidos vêm
desenvolvendo as estratégias de IA desde 2016, com atualização mais recente em
2019. Embora no Reino Unido a estratégia seja menos explícita, dois documentos
constituem as referências básicas para o setor público de governos subnacionais,
centros de pesquisa e setor industrial. O primeiro documento foi elaborado em
2017 (IA no Reino Unido – AI in the UK) e o segundo, lançado em 2017 (Estratégia
industrial – Industrial strategy) e atualizado em 2019 (Pacto do Setor de IA – AI
Sector Deal). Outros países como Alemanha, China, França, Índia e Japão elabo-
raram instrumentos de planejamento que mostram com clareza a importância que
o tema requer. O quadro 9 é uma síntese com a descrição de estratégias e desafios
que constam desses documentos para análise comparada e avaliação.
QUADRO 9
Estratégias para desenvolvimento da tecnologia de IA e principais desafios, por país
Documentos de referência Estratégias Desafios
Fortalecer a pesquisa na Alemanha e na Europa Consolidação de ecossistema multidisciplinar,
orientada por inovação (eixo Pesquisa). internacionalmente competitivo.
Focar na competitividade em IA e nos clusters Criação de ambiente de liberdade para estímulo de
europeus inovadores (eixo Transferência e Aplicação). ideias disruptivas e soluções novas.
Viabilizar o uso de aplicações de IA em empresas Integração e incorporação da IA aos processos de
de todos os portes (eixo Transferência e Aplicação). negócios das empresas.
Aperfeiçoar e estimular o investimento em capital
de risco (venture capital) para modelos de negócios Incentivos especiais para a fase de crescimento,
e produtos de IA (eixos Pesquisa e Transferência/ intensiva em capital e spin-offs de pesquisa.
Aplicação).
Modelar a mudança catalisada pela tecnologia de IA Apoio à formação continuada e ao acompanhamento
no campo de trabalho e emprego (eixo Regulação). das empresas.
Estimular a formação vocacionada, bem como atrair
Aperfeiçoamento das condições estruturais para
Alemanha (die Bundesre- e reter cientistas e profissionais especializados (eixo entendimento de IA.
gierung 2018) – Estratégia Mentes Vocacionadas).
de IA (Artificial Intelligence Utilizar a IA para as tarefas reservadas à admi-
Strategy) nistração do Estado, como mobilidade, educação, Oferta de serviços públicos mais ágeis e eficientes
saúde, cibersegurança, preservação ambiental e aos cidadãos.
clima (eixo Pesquisa).
Viabilizar a disponibilidade de dados e facilitar sua
Preservação de dados individuais e direitos fundamentais.
utilização (eixo Regulação).
Adaptar a estrutura regulatória para processos de Proteção contra vieses, discriminação, manipulação
decisão baseados em algoritmo de IA (eixo Regulação). e usos indevidos.
Estabelecer padrões no contexto nacional e inter-
Questões de cunho ético, além de técnico.
nacional (eixo Regulação).
Formatar rede nacional e internacional para atuação
Expansão da cooperação internacional, bilateral
transfronteiriça (eixos Regulação e Transferência/
e multilateral.
Aplicação).
Dialogar com a sociedade para estruturar as ações Necessidade de manter a sociedade bem-informada
da política de IA (eixo Social). e educada.
(Continua)
--- PAGE 101 ---
|
646 Do paradoxo das habilidades à superinteligência pós-humana
(Continuação)
Documentos de referência Estratégias Desafios
Até 2020, objetivava-se um nível avançado de IA,
Estabelecimento de sistema inovador, aberto e coope-
com valoração do núcleo dessa indústria de ¥ 150
rativo de IA, para a economia inteligente e eficiente.
China (State Council Notice bilhões e indústrias relacionadas de ¥ 1 trilhão.
on the Issuance of the Next Até 2025, tem-se como meta um nível de liderança
Generation Artificial Intelligence em nichos teóricos e aplicações de IA, bem como Fortalecimento da integração civil e militar no campo
Development Plan, 2017) – um estabelecimento de nova estrutura legal, com da IA, assegurando a formatação de sociedade
Plano de Desenvolvimento valoração do núcleo da indústria de ¥ 400 bilhões inteligente.
para a Nova Geração de e indústria relacionada de ¥ 5 trilhões.
Inteligência Artificial (Next Até 2030, tem-se como objetivo um nível de liderança
Generation Artificial Intelli- mundial como centro de referência para teorias, Instalação de sistema de infraestrutura inteligente,
gence Development Plan) tecnologias e aplicações de IA, com valoração ubíquo, seguro e eficiente, visando projetos funda-
do núcleo da indústria de ¥ 1 trilhão e indústrias mentais para nova geração de IA.
relacionadas de ¥ 10 trilhões.
Realizar investimentos de longo prazo em pesquisas Desenvolvimento de metodologias e capacidades,
fundamentais de IA. reconhecendo limitações da tecnologia de IA.
Desenvolver métodos efetivos para colaboração Busca de novos algoritmos de IA humano-conscientes
humano-IA. e interfaces de realidade aumentada.
Compreender e abordar as implicações éticas, legais Aperfeiçoamentos da imparcialidade, da transparência
e sociais de IA. e da responsabilidade dos projetos de IA.
Estados Unidos (United States, Garantir a segurança operacional e contra atos Explicabilidade, transparência e validação dos sistemas
2019) – Plano Nacional ilícitos dos sistemas de IA. de IA, com alinhamento de valores.
Estratégico de P&D em IA Disponibilização pública de grande variedade
(The National Artificial In- Desenvolver dados públicos e ecossistemas para de conjunto de dados para atender a diversos
telligence Research And treinamento e testagem de IA. interesses em IA.
Development Strategic Plan:
Desenvolvimento de grande espectro de padrões
2019 update) Avaliar as tecnologias de IA de forma comparativa
tecnológicos de IA, para servir de base para
e padronizada.
comparabilidade.
Entender as necessidades nacionais de capacitação Sustentação da liderança estadunidense como força
da força de trabalho e de P&D em IA. de trabalho habilitada em tecnologia avançada de IA.
Levantamento de recursos: instalações, dados e
Expandir parcerias público-privadas (PPPs) para
especialistas em ciências e engenharias educando
acelerar os avanços em IA.
e treinando as próximas gerações em IA.
Introdução de novas formas de produzir, compartilhar
Fundamentar a política econômica em dados.
e administrar dados (bem comum)
Criação de ambiente atrativo e competitivo de IA,
Promover agilidade na viabilização de pesquisas em IA.
França (Villani, 2018) – Por evitando a fuga de cérebros.
uma Inteligência Artificial Complementação de IA-humano, educação perma-
Significativa: orientação Avaliar os impactos da IA no mercado de trabalho. nente e recapacitação.
à estratégia francesa e
europeia (For a Meaningful Trabalhar a IA para uma economia mais ecológica. Abordagem de sustentabilidade, dando significado
AI: towards a French and maior para uso da IA.
European strategy) Explicação de algoritmos e modelos de IA, com
Considerar as questões éticas relacionadas à IA. auditorias para avaliação de impactos adversos e
discriminação.
Desenvolver uma IA diversa e inclusiva. Utilização da tecnologia de IA em áreas sociais.
Recomendar a pesquisa aplicada às instituições
Alto custo e baixa disponibilidade de infraestrutura
acadêmicas; convidar a indústria para liderar e ex-
de computação para treinamento e desenvolvimento
pressar os interesses setoriais; estabelecer plataforma
de serviços com base em IA, com êxodo de startups
comum em nuvem; e reformar as regras de direito
indianas para outros países.
Índia (NITI Aayog, 2018) – de propriedade intelectual.
Estratégia Nacional de Recapacitar e reabilitar a força de trabalho, os
Inteligência Artificial (Na- universitários e os alunos de educação básica para Insuficiência de força de trabalho habilitada a construir
tional Strategy for Artificial lidar com tecnologias de IA. e empregar sistemas de IA em escala.
Intelligence) #AIforall
Acelerar a adoção de tecnologias de IA com com- Dificuldades no acesso a big data e concentração de
partilhamento de dados e parcerias. mercado para soluções e plataformas customizadas.
Desenvolver as tecnologias de IA de forma res- Conscientização baixa do potencial de IA na resolução
ponsável e ética. de problemas e na causação de impactos adversos.
(Continua)
--- PAGE 102 ---
|
Segmentos tecnológicos e aplicações 647
(Continuação)
Documentos de referência Estratégias Desafios
Fortalecer a preparação de dados com foco em áreas Preparação de mapeamento industrial para estabelecer
prioritárias de IA. áreas prioritárias.
Consolidar as instituições responsáveis pela gestão Fortalecimento do suporte à gestão de dados menos
e pelo provimento de dados para IA. efêmeros que pesquisas.
Desenvolver ambientes de simulação e demonstra-
Impedimentos à aquisição de dados, como infor-
ção de IA, para acelerar a aquisição de dados e a
mações pessoais.
verificação de ferramentas.
Japão (Strategic Council for AI Estabelecer ciclo virtuoso de colaboração em IA,
Technology, 2017) – Estratégia para coleta de dados e uso de ferramentas entre Diminuição da dependência de recursos de IA em
para Tecnologia de Inteligência academia e indústria. nuvem estrangeiros.
Artificial (Artificial Intelligence
Technology Strategy) Acelerar o desenvolvimento tecnológico e sistêmico Preparação, limpeza e rotulação de dados intensivas
para preparação do conjunto de dados. em mão de obra.
Disponibilização de dados públicos por meio de
Prover dados abertos gerados por projetos públicos.
iniciativa governamental.
Listar e disponibilizar os recursos uniformes de Desenvolvimento de ambientes amigáveis em
localização relacionados a dados e ferramentas. endereços uniform resource locator (URL)
Utilizar dados de propriedade privada de forma Movimentação e distribuição de dados privados
compartilhada e transversal. aplicados a IA.
Levantar investimentos da ordem de 2,4% do PIB,
até 2027, para P&D.
Posicionamento do Reino Unido na dianteira eco-
Aumentar a taxa de crédito para P&D para 12%.
nômica da revolução de dados e IA.
Investir £ 725 milhões nos fundos industriais
estratégicos para inovação.
Aumentar o Fundo Nacional de Produtividade em £ 31
milhões, para dar suporte aos setores de transporte,
habitação e infraestrutura digital.
Liderança da mobilidade futura de pessoas, mer-
Apoiar a rede de abastecimento de energia para
cadorias e serviços
veículos elétricos plug-in, com £ 500 milhões.
Investir £ 1 bilhão em infraestrutura digital, 5G
e fibra ótica.
Lançar pactos com o setor privado industrial para
aumento de produtividade (IA, ciências da vida,
construção e setor automotivo).
Orientar investimentos da ordem de £ 20,0 bilhões
Reino Unido (United Kingdom, Maximização da vantagem industrial do Reino Unido
em negócios inovativos com potencial, estabelecendo
2019) – Pacto do Setor de IA no desenvolvimento limpo.
fundo de £ 2,5 bilhões no Banco Britânico de Negócios.
(Policy Paper – AI Sector Deal)
Avaliar o desempenho das ações mais efetivas no
aumento de produtividade das pequenas e médias
empresas (PMEs)
Estabelecer um sistema de educação técnica de alto
nível em classe mundial.
Investir £ 406 milhões adicionais em habilidades
Desenvolvimento da força da inovação para atender
science, technology, engineering and mathematics
às necessidades de uma sociedade amadurecida.
(STEM) e educação técnica digital
Retreinar pessoas orientando para a digitalização
e iniciando com investimentos de £ 64 milhões.
Aproveitar vocações locais nas estratégias industriais
e oportunidades econômicas.
Incorporação da P&D em IA a todas as atividades
Criar fundos para transformação e conexão das
econômicas, tirando proveito da escalabilidade afeita
cidades de £ 1,7 bilhão.
aos ambientes urbanos.
Promover premiação de professores com £ 42 milhões
e avaliar impacto da educação de alta qualidade.
Fonte: NSTC (2019), Japan (2017) e outras.
Elaboração da autora.
--- PAGE 103 ---
|
648 Do paradoxo das habilidades à superinteligência pós-humana
Outro documento de referência, dessa vez dirigido a público especializado,
foi elaborado pelo Consórcio Industrial da Internet em 2019. O passo a passo para
implantação de estratégia de IA na indústria trouxe um conjunto de indagações
que induzem à reflexão, segundo o consórcio (Oviedo, Paroutis e Smith, 2019).
3.6 Aplicações de IA
O relatório anual The AI Index 2019 Annual Report, ano-base 2018 (Perrault et al.,
2019), do Instituto de Inteligência Artificial (HAI – Human-Centered Artificial
Intelligence Institute) da Universidade de Stanford, apresentou a dimensão e a
participação dos investimentos globais em aplicações de IA, quais sejam: i) veí-
culos autônomos – US$ 7,7 bilhões (9,9% do total); ii) farmacêutica (terapias e
drogas) – US$ 4,7 bilhões (6,1%); iii) reconhecimento facial – US$ 4,7 bilhões
(6,0%); iv) conteúdo visual – US$ 3,6 bilhões (4,5%); e v) detecção de fraudes e
finanças – US$ 3,1 bilhões (3,9%) (Perrault et al., 2019).
Sobre as patentes na área de IA por grandes regiões do planeta, o relatório
The AI Index 2019 Annual Report (Perrault et al., 2019) apresenta os dados da
Microsoft Academic Graphs 1990-2018, com 51% das patentes publicadas sendo
atribuídas à America do Norte, em ascensão, seguida de Europa e Ásia Central,
com 23%, em declínio, e aproximando-se do leste da Ásia e do Pacífico, em 2019.
Com relação ao número de patentes per capita, os Estados Unidos aparecem com
aproximadamente 58 patentes/milhão de habitantes, seguido de Japão, com dezoito
patentes/milhão de habitantes, e França, com quinze patentes/milhão de habitantes
(Perrault et al., 2019, p. 31).
O caderno de tendências tecnológicas de IA, da Organização Mundial da
Propriedade Intelectual (WIPO, 2019),27 trouxe alguns números a respeito das
patentes de IA, que ultrapassam a média de 10% a.a. de crescimento em todas as
áreas tecnológicas:
• mudança na razão entre publicações científicas e invenções em IA, com
decréscimo de 8:1 (2010) para 3:1 (2016), mostrando o aumento de
aplicações comerciais e serviços;
• dominância de ML como técnica de IA, com um terço dos 134.777 do-
cumentos de patente, crescendo a 28% a.a. – ou seja, 20.195 aplicações
de patentes, em 2016, e 9.567, em 2013;
• técnicas revolucionárias com crescimento de 175% para DL (DL = 2.399)
e 46% para neural network (NN = 6.506), entre 2013 e 2016;
27. Em inglês, World Intellectual Property Organization.
--- PAGE 104 ---
|
Segmentos tecnológicos e aplicações 649
• aplicações funcionais populares, como visão computacional e reconhe-
cimento de imagem, com crescimento de 24%; e
• maior crescimento para aplicações funcionais em robótica e métodos de
controle, com crescimento médio anual de 55%.
Vinte campos de aplicação foram identificados pela WIPO, aqui elencados por
ordem de magnitude dos documentos de patentes identificados: telecomunicações
(15%); transportes (15%); ciências médicas (12%); interação homem-computador
(HCI = 11%); além de bancos, entretenimento, segurança, indústria, agricultura e
redes. Tanto em quantidade como em velocidade de crescimento, o setor transportes
é o mais significativo, com crescimento de 33% a.a., entre 2013 e 2016, e 8.764
registros de patentes, em 2016. Segue-se em importância telecomunicações, com
23% de crescimento e 6.684 registros de patentes.
Os países que dominam a produção de patentes de IA são Japão, Estados
Unidos e China. A IBM e a Microsoft são líderes em patentes de IA entre as em-
presas, com portfólio de aplicações de 8.290 e 5.930 invenções, respectivamente.
As norte-americanas são seguidas da Toshiba (5.223), da Samsung (5.102) e da
Nippon Electric Company – NEC (4.406). A chinesa State Grid está entre as
vinte maiores empresas em registros de patentes, com crescimento de 70% a.a.,
de 2013 a 2016. Entre as universidades, as chinesas são dezessete, no conjunto de
vinte líderes, em publicações relacionadas a IA.
A conferência de 2019 da Associação para Avanço da Inteligência Artificial
(AAAI – Association for the Advancement of Artificial Intelligence), que abrange
ampla cobertura de tópicos de IA, mostrou que a China teve o maior número de
artigos submetidos (2.419, 56%). A maior relação entre número de artigos aceitos/
apresentados foi de Israel (24%), seguido de Alemanha (23%), Canadá (22%),
Estados Unidos (20%) e Singapura (20%) (Perrault et al., 2019, p. 41).
A utilidade dos pacotes de IA/ML teve sua popularidade mensurada pelo
sistema de pontuação dos desenvolvedores da GitHub, como consta no relatório
de HAI (Perrault et al., 2019, p. 33), tem-se: i) TensorFlow – suporte da Google
com mais de 100 mil pontos na dianteira disparada; ii) scikit-learn; iii) Berkeley
Vision and Learning Center (BVLC)/caffe; iv) keras; v) Microsoft Cognitive Toolkit
(CNTK); vi) mxnet; vii) theano; viii) caffe2; e ix) PyTorch – apoio do Facebook,
esse último software com a trajetória ascendente mais agressiva.
A oferta de empregos nos diversos segmentos de IA, nos Estados Unidos, entre
2010 e 2019, cresceu de 0,07% para 0,51% em ML, seguido por IA (0,28%), redes
neurais (0,13%), PLN (0,12%), robótica (0,11%) e reconhecimento de imagem
visual (0,10%), conforme o termo utilizado no título postado na demanda por
trabalho (Perrault et al., 2019, p. 74).
--- PAGE 105 ---
|
650 Do paradoxo das habilidades à superinteligência pós-humana
Apresentado o quadro geral dos principais parâmetros que caracterizam a
evolução da IA por área de disciplina, registro de patentes e países produtores de
destaque, seguem-se amostras representativas dessas aplicações.
3.6.1 Sistemas robóticos
As diversas aplicações que designam algoritmos de IA, com ou sem máquinas
no mundo físico, foram qualificadas como sistemas robóticos. Estes podem ser
combinações de um conjunto de sensores e dispositivos materiais que interagem
com o homem ou podem ser imanifestos, no sentido de que se referem a agentes
virtuais e autônomos desenhados por software, conhecidos como bots – ou cobots,
quando trabalham em parceria com humanos –, conforme constam das diversas
aplicações descritas no relatório da The Royal Society (2017).
1) Robotic process automation (RPA) – Utilização de software robô para subs-
tituir tarefas operacionais repetitivas, mas sujeitas a adaptações. Trata-se
do uso de IA para apresentar soluções empresariais, com a otimização
de processos de gerenciamento da cadeia de suprimentos (SCM), geren-
ciamento de relacionamento com clientes (CRM) e gerenciamento de
recursos humanos (HRM). O robô reproduz as tarefas executadas pelo
funcionário no computador e repete-as quantas vezes for necessário.
2) Robô – Máquina programável por computador, capaz de realizar tarefas
automáticas, com diferentes graus de complexidade, conforme a função
doméstica, industrial ou militar, geralmente multipropósito.
3) Robô colaborativo (cobot) – Robô que interage com humanos, desenhado
para aprender novas tarefas por meio da interação com o ambiente, sendo
equipado com múltiplos sensores, desenhados para fabricação de forma
customizável e portátil.
O uso de IA permite que novas tarefas venham a ser automatizadas, sem a
codificação prévia de um profissional especialista em programação. Implantado em
vários departamentos, o RPA permite a integração de sistemas legados, sistemas
em nuvem e bancos de dados. Os registros de transações do RPA servem para
análise de auditoria.
Os robôs inteligentes podem ser de diversos tipos, variando conforme a
funcionalidade, o que torna a tentativa de classificação sempre sujeita à omissão.
Contudo, alguns atributos são bem distintivos, como é o caso de robôs especiali-
zados em serviços domésticos, educação e atendimento ao público, por exemplo.
Alguns tipos são bastante especializados, dedicados a tarefas muito sensíveis como
robôs assistentes de cirurgias médicas e segurança operacional e contra atos ilícitos.
Outros robôs de aconselhamento são conhecidos como robôs advisors, sendo mais
--- PAGE 106 ---
|
Segmentos tecnológicos e aplicações 651
comumente empregados nas áreas de finanças. Na busca por melhor classificação, os
biomiméticos foram identificados como aqueles com interessantíssimo desempenho,
principalmente pelo preciosismo com que emulam os movimentos dos animais,
graças a desenvolvedores igualmente hábeis – e.g., Festo. Contudo, a lista de robôs
apresentados no quadro 10 alonga-se com maior destaque justamente naqueles
classificados como humanoides, com destreza extraordinária – e.g., Atlas –, por
fazerem parte do anseio humano de espelhar criadores em suas criaturas.
QUADRO 10
Classificação de robôs por funcionalidade, de acordo com características de desen-
volvimento
Funcionalidade Robô – desenvolvedor/fabricante Características
COG e Hermes – MIT Aprendizado por tentativa e erro.
Autodidata
Vestri – Universidade Berkeley Antecipação de evento futuro.
Especializados em biomimética: aranha, peixe,
Bionic Learning Bots –Festo, Alemanha morcego, canguru, libélula, borboleta, pinguins,
Biomimética água viva e gaivota
Pleurobot – Instituto de Tecnologia de Lausanne, Salamandra: articulação via controle do sistema
Suiça nervoso sintético, protótipo para neuropróteses.
Científica Sojourner, Mars Land Rover – Nasa Levantamento geológico.
GeoFluent – Lionbridge e IBM Tradução de fala e texto.
Comunicação
Karim – X2AI Conversação com refugiados sírios.
Roomba – iRobot Enceradeira e aspirador.
Doméstica
Braava – iRobot Limpa piso.
Educação Plataforma Happy Numbers Padrões matemáticos.
Aibo – Sony Cão de estimação.
Entretenimento
Minerva – Carnegie Mellon Guia de museu.
Ueslei – Vérius Cesta de investimentos e aposentadoria.
Finança Plataforma Magnetis – Magnetis Formas de investir.
Robô financeiro – Monetus Retorno de portfólio de investimentos.
(Continua)
--- PAGE 107 ---
|
652 Do paradoxo das habilidades à superinteligência pós-humana
(Continuação)
Funcionalidade Robô – desenvolvedor/fabricante Características
Sensores de pele testam roupas de proteção contra
substâncias químicas, em ambientes extremos, com
PET (Boston Dynamics)
desenvoltura na movimentação e regulação de
temperatura e suor.
Forte, hábil e silencioso para missões de resgate em
zonas perigosas; avalia terrenos difíceis; evita obstá-
culos; deslocamento autônomo; levanta-se sozinho,
Atlas (Boston Dynamics)
manipula objetos e abre portas; três computadores
de percepção; bateria acoplada aos circuitos multi-
função; roteador e comunicação sem cabos.
Braços e pernas articuladas para missão em condi-
Valkyrie (Nasa)
ções extremas e exploração em Marte.
Exploração espacial; tarefas perigosas; reconheci-
Robonaut (Nasa), ativo na Estação Espacial Interna-
mento de imagens; e controle e manipulação de
cional (ISS –International Space Station)
objetos com grande precisão.
Bípede, sobe escadas e trafega por caminhos irregu-
Schaft (Google)
lares, com capacidade de 60 kg de carga.
Quarenta atuadores eletromecânicos, com capacida-
Tesla bot (Tesla)
de de carga de 20 kg, 1,75 m e 57 kg.
Missão de resgate e de exploração lunar; reproduz
Fedor (Android Technics), Rússia movimentos do operador; manipulação com grande
precisão; e condução de veículos.
Semiautônomo sobre rodas, equipado com ferra-
DRC Hubo (Kaist) da Coreia do Sul, premiado pelo
mentas de grande precisão, movimentação em giros
Humanoide Desafio de Robótica da Darpa
e capacidade para 20 kg.
(androide ou
ginoide) HRP-4C (Aist), Instituto Nacional de Ciência e Movimentação e PLN, equipada com sensores e
Tecnologia Industrial Avançada do Japão articulação de palavras.
Reprodução de 62 expressões faciais; contato visual;
Sophia (Hanson Robotics – Hong Kong), premiada
aprendizado de IA com conversação; senso de
com cidadania nos Emirados Árabes Unidos
humor; utilizada em tarefas educativas.
Assistência para pessoas com mobilidade reduzida:
Asimo (Honda), Japão
sobe escada, manipula objetos com precisão e corre.
Interações via teclado; expressões faciais; operando
Junko Chihira (Toshiba), Japão
como guia para turistas trilíngue.
IA interativa; reconhecimento facial; rememoração
Nadine (Universidade de Nanyang – NTU),
de conversas; integrada a funções físicas realistas;
Singapura
expressões emocionais.
Reconhecimento de voz, articulação de fala, bem
Erica (Instituto de Tecnologia de Osaka) como expressões e gestos naturais, por rastreamen-
to humano com 19 mil movimentos integrados.
Geminoide (Universidade de Aalborg), Dinamarca Estudo de respostas emocionais.
Variedade de atividades, como percepção tátil, capa-
REEM-C (PAL Robotics – Espanha)
cidade de carga de 10 kg, desvio de obstáculos etc.
Romeo (Softbank Robotics, Aldebaran Robotics e Assistente pessoal; abre portas, sobe escadas e car-
instituições europeias) rega objetos; movimento de braços e mãos precisos.
Rollin Justin do Centro Aeroespacial Alemão (DLR –
Autônomo; controlado a distância para conserto de
Deutsches Zentrum für Luft- und Raumfahrt) – Insti-
satélites; percorre grandes extensões.
tuto de Robótica e Mecatrônica de Wessling
Industrial Performarc – Panasonic Soldagem industrial.
(Continua)
--- PAGE 108 ---
|
Segmentos tecnológicos e aplicações 653
(Continuação)
Funcionalidade Robô – desenvolvedor/fabricante Características
Retorno tátil aos controladores e aplicação no
Oceanone (Laboratório de IA de Stanford)
estudo de corais no mar.
Cobra marítima, para identificação e reparações
Marítima Eelume (Noruega) simples em plataformas exploratórias de petróleo
marítimas.
Crabster CR 200 (Instituto de Tecnologia Oceânica Monitoramento de zona costeira, em forte fluxo de
da Coreia do Sul) correntes marinhas, sem tripulação.
Localização – vascular Assistência de embolização.
Microbivores – hematologia Caçadores de patógenos.
Nanorrobô saúde
Nanoknife – neurocirurgia Manipulação e transecção.
Screening – oncologia Detecção de neoplasias.
Psicólogo- Xiaoice – Microsoft Apoio a pessoas solitárias.
-terapeuta Ellie – South California University Diagnóstico de estresse pós-trauma.
Replicante Auto ML – Google Brain Criação de outras IAs.
Resgate Cheetah - MIT Robotic Quatro pernas articuladas.
Segurança Latro – Uom Robotics Monitoramento de ambiente perigoso.
Transporte Waze – Google Maps Otimização de rotas.
NasNet – Google Brain Reproduz expressões humanas.
Visão ImageNet – Google Brain Reconhecimento de imagens.
Coco – Google Brain Reconhecimento de imagens.
Videojogos Role-playing game (RPG) Dragon Quest IV Comportamentos responsivos.
Fonte: Rubio, Valero e Llopis-Albert (2019) e Onnasch e Roesler (2020).
Elaboração da autora.
FIGURA 13
Robô humanoide Atlas (Boston Dynamics) e robôs biomiméticos (Festo)
13A – Vídeo da Atlas Dynamics que viralizou na internet
--- PAGE 109 ---
|
654 Do paradoxo das habilidades à superinteligência pós-humana
13B – Robô canguru da Festo
13C – Robô aranha da Festo
13D – Robô libélula da Festo
Fontes: Festo e Boston Dynamics. Disponível em: https://www.bing.com/videos/search?q=ATLAS+(Boston+Dynamics)&doci
d=608016053288786488&mid=55E111A23D7FD97172FD55E111A23D7FD97172FD&view=detail&FORM=VIRE.
Obs.: Ilustrações cujos leiautes e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
3.6.2 Realidades estendidas: virtual, aumentada e hiper-realidade
A tecnologia sensória imersiva surgiu na década de 1960, com o videogame Senso-
rama, criação do produtor de filmes norte-americano Morton Heilig, O especialista
em multimídia dotou o Sensorama de imagens 3D estereoscópicas, cilindro de
inclinação de corpo, ventilação e sonoplastia. Consolidada na década de 1990, a
--- PAGE 110 ---
|
Segmentos tecnológicos e aplicações 655
tecnologia de realidades estendidas permite a interação sensorial visual, auditiva e
tátil em ambiente virtual/projetado e controlado, conforme três grandes ramificações:
• realidade virtual (RV): interfaces nas quais os objetos têm representações
totalmente artificiais e a interação do usuário com o meio virtual ocorre
por meio da utilização de aparelhos em ambiente de simulação;
• realidade aumentada (RA): imagens são projetadas no meio físico real
por aparelhos de reconhecimento e marcação da localização do objeto
na imagem; e
• hiper-realidade (HR): combina RV, realidade física e IA, permitindo
que indivíduos interajam com outras pessoas localizadas remotamente.
Avanços nesse campo são esperados devido a melhorias na conectividade, nos
dispositivos de visores, nos óculos – e.g., óculos Go e óculos Rift – e nos celulares
que fazem parte dos experimentos de imersão em RVs. Nos smartphones, as funções
adicionadas envolvem tecnologias de IA, segurança e computação em nuvem (hí-
brida, pública ou privada). As aplicações de realidade estendida são desenvolvidas
em projetos de carros autônomos, na manutenção de máquinas e equipamentos,
no GeoAnalytics, no reconhecimento facial em multidões, no treinamento para
capacitação profissional, na gameficação de movimentos para reabilitação de pacientes
etc. A tecnologia de realidade estendida mista (XR), que combina RV e RA, tem
sido empregada para criar hologramas flutuantes realistas em ambientes digitais,
o que permite a experiência interativa integrada com o meio projetado – e.g.: MS
Hololens. As aplicações na área de saúde trabalham a transformação de dados de
imagens 2D em 3D, como no caso dos exames de tomografia computadorizada
e eletrocardiograma. Associada com ML/IA, a XR-holograma possibilita análise
detalhada por parte da equipe médica.
3.6.3 Ciência
O relatório da The Royal Society (2017) apresentou interessante levantamento
relacionando áreas do conhecimento científico nas quais a tecnologia de IA vem
sendo empregada, com o objetivo de processar massiva quantidade de dados, des-
critas a seguir.
1) Neurociências – Métodos de aprendizado de ML – com, semi e sem
supervisão – auxiliam no mapeamento das funções cerebrais, para iden-
tificar padrões de atividade, como o processamento refinado de imagens
do cérebro escaneadas por ressonância magnética e associadas a determi-
nadas tarefas sensoriais e cognitivas de reconhecimento de objetos, de
palavras e de resolução de problemas; as redes convolucionais; a visão
computacional; e a memória episódica.
--- PAGE 111 ---
|
656 Do paradoxo das habilidades à superinteligência pós-humana
2) Física das partículas subatômicas – A Organização Europeia para Pesquisa
Nuclear (Cern) utilizou ML para reconstituir os sinais deixados pelas
partículas e para detectar padrões de decaimento destas em gama fótons
(duração de 10-22 segundos), com o objetivo de identificar o bóson de
Higgs no Grande Colisor de Hádrons, acelerando os trabalhos de coleta
de dados em período correspondente a dois ou três anos e melhorando
a acurácia em cerca de 20% a 40%.
3) Astronomia – As imagens do Grande Telescópio de Levantamento Si-
nóptico (LSST – Large Synoptic Survey Telescope) geram 15 terabites de
dados a cada rodada de mapeamento noturno, que podem ser analisadas
por ML, com a finalidade de detecção de padrões e classificação correta
dos sinais em categorias de fenômenos astronômicos, como planetas
terraformáveis, estrelas, galáxias, pulsares, supernovas e identificação de
energia escura.
4) Mudanças climáticas – O uso de ML auxilia na compatibilização das
escalas geográficas, que abrangem desde modelos globais de previsão de
temperaturas, precipitação pluviométrica e níveis dos mares até previsão
dos impactos em nível localizado de cidades e regiões, para informar o
processo de tomada de decisão.
3.6.4 Saúde
A bioinformática utiliza IA para apoio de diagnósticos em hospitais e laboratórios
de análise clínica. O sistema de computação cognitiva analisa o sequenciamento
genético dos pacientes e emite relatório apresentando: i) as referências na literatura
médica de ensaios clínicos realizados em todo o mundo; ii) os tratamentos indica-
dos, com a respectiva medicação; e iii) estudos em andamento para cruzamento de
informações. O laudo final é emitido de acordo com o perfil genético do paciente
e passa pelo escrutínio do corpo médico.
A IA é utilizada em medicina de precisão, com o intuito de prever com-
plicações durante o tratamento e indicar aperfeiçoamentos de forma a prevenir
doenças, tais como detecção precoce de acidente vascular cerebral (AVC), teste de
origem tumoral (TOT) e detecção de lesões. Os sistemas de ML, treinados para
reconhecimento de imagens e análise de patologias em amostras de tecidos, têm
mostrado grande acurácia, identificando características padrões para o diagnóstico.
Descrevem-se, a seguir, algumas fontes de consultoria e desenvolvimento de
sistema de IA na área de saúde:
• IBM – Watson for Genomics, serviço em nuvem;
--- PAGE 112 ---
|
Segmentos tecnológicos e aplicações 657
• Center for Clinical Data Science – Centro para Dados Clínicos Cien-
tíficos (CCDS);
• DasaInova e IClinic (monitoramento para evitar absenteísmo em con-
sultas médicas);
• CucoHealth (despertador para ingestão de medicação);
• DocWay (agendamento eletrônico de consulta com especialistas, exames
e vacinação);
• Google Research (algoritmo DL para análise de imagens de fundo de
olho, com base em aprendizado a partir de 128 mil avaliações médicas);
• Telemedicina e marketplace de especialistas (laudos a distância, interagindo
com médicos e nutricionistas – Neo); e
• Mudança para hábitos mais saudáveis (tecnologia vestível – sensores em
vestimentas e i_watch – e assistente pessoal – Inyou).
A crise da covid-19 estimulou a criação de novas aplicações tecnológicas de IA
em saúde, com o intuito de responder aos desafios da pandemia. A demanda tem
aumentado principalmente para IA de conversação, objetivando o atendimento
aos clientes que tiveram consultas suspensas.
3.6.5 Educação
De que forma a tecnologia IA poderia auxiliar na melhoria dos resultados de es-
tudantes em todos os níveis, especialmente a partir da educação básica? E de que
forma a gestão do sistema educacional poderia ser beneficiada? A criação de ensino
customizado – i.e., de acordo com as características pessoais dos indivíduos – pode
ser viabilizada pelos algoritmos de ML empregados nas aulas em ambiente online.
Esses sistemas permitem que professores possam acompanhar as tarefas rotineiras
e avaliar os estudantes de forma mais eficiente. A classificação correta dos alunos
em grupos, conforme categorias específicas, possibilita o nivelamento, a compa-
rabilidade e melhor desenvolvimento individual, por meio do preenchimento de
lacunas ou deficiências de aprendizado. Durante a pandemia, a segunda área com
maior procura de soluções de IA foi a de educação, para adaptação do sistema
educacional, mudança de metodologia de aprendizagem, com monitoramento
de desempenho de alunos, identificação de lacunas no ensino e automatização de
tarefas administrativas rotineiras.
Os exemplos são os sistemas de educação à distância, conhecidos como
massive open online courses (MOOCs), que utilizam ML para analisar as respostas
em tarefas e testes de avaliação para graduação. A tecnologia torna possível gerir
o massivo número de interessados e alocar os multiplicadores ou professores em
--- PAGE 113 ---
|
658 Do paradoxo das habilidades à superinteligência pós-humana
tarefas menos rotineiras. A IA, combinada com realidade virtual e hologramas,
tem sido utilizada na educação, principalmente no segmento corporativo, visando
minimização de custos e atualização permanente. Cursos online e jogos corporati-
vos são ferramentas utilizadas há décadas, agora renovadas com maior ênfase em
habilidades comportamentais, tais como comunicação e organização. O modelo
mental de cada aprendiz é incorporado pela IA, identificando falhas, reforçando
disciplinas e desenvolvendo formas customizadas de ensino profissional.
Empresas dedicadas ao ramo (Arcoverde, 2018):
• empresa francesa Learning Tribes, que integra ferramentas pedagógicas
por meio da interação à distância com hologramas;
• escola de negócios Saint Paul, com robô Paul tipo chatbot funcionando
como monitor para tirar dúvidas, responder a perguntas e oferecer cursos
online. A plataforma Lit é oferecida tanto nos cursos presenciais quanto
à distância;
• Watson (IBM), identifica estratégias de aprendizado segundo a produção
literária de cada indivíduo;
• startup Beedoo oferece plataformas de capacitação em massa, como call
centers; e
• KPMG Business School, com coach virtual Rui pela plataforma Hondana.
Os ambientes virtuais, especialmente aqueles movidos à IA, fazem parte das
ferramentas educacionais, proporcionando a vantagem da experimentação mais
próxima da realidade e com abordagem didática. Os aparelhos de visualização em
três dimensões possibilitam a interação do aprendiz e permitem o estudo preliminar
da anatomia dos seres vivos em pleno funcionamento fisiológico.
3.6.6 Energia
A tecnologia de IA tem sido aplicada no setor de energia em manutenção preven-
tiva, redução de custos nos processos, gestão dos recursos em rede, prevenção de
fraudes e eficiência energética. Seguem algumas aplicações industriais de IA na
área de energia, descritas a seguir.
1) Recarga inteligente de veículos elétricos – Com o objetivo de minimizar
paralisações e gerenciar sessões de recarga e o impacto das estações de
abastecimento público ou privado de energia no sistema elétrico.
2) Turbinas eólicas e motores – Programação de conserto otimizada, com
modelos de orientação para eventos e paralisações inesperadas, identifica-
ção de unidades de baixo desempenho, modelagem orientada para falhas
de componentes essenciais e aperfeiçoamento de plano de manutenção.
--- PAGE 114 ---
|
Segmentos tecnológicos e aplicações 659
3) Tratamento de gás – Previsão de eventos indesejados (foaming/flooding),
identificação de parâmetros operacionais e otimização do uso de reagentes.
4) Petróleo – Previsão de eventos inesperados durante a exploração, oti-
mização da produção – e.g., calibragem da válvula de fluxos de ejeção/
injeção, otimização de gás lift, maximização de histórico de produção,
adensamento de poços de exploração em rede e manutenção preditiva
(programação e paradas imprevistas, bem como identificação de incrus-
tação na tubulação raise por imageamento em DL); e
5) Robótica submarina – Utilizada em atividades altamente perigosas a
grandes profundidades.
3.6.7 Transporte, trânsito e automação veicular
A tecnologia de IA aplicada a transportes apresenta amplo leque de possibilida-
des. A começar pelo aperfeiçoamento dos sistemas de transportes inteligentes
(ITS), que analisam os dados históricos de fluxo de tráfego, otimizando-os e
proporcionando previsibilidade para os diferentes carregamentos, conforme o
evento, o dia e a hora. Os resultados implicam menores congestionamentos,
economia de combustíveis e diminuição de emissões de carbono. O diferencial
trazido pela IA, aos tradicionais ITS, é o fato de que essa interação pode ser
mais dinâmica, em tempo real, graças aos ajustes imediatos feitos ao tráfego,
de acordo com os dados capturados pela rede de sensores.
Os carros autônomos equipados com IA possibilitam a reação às condições
meteorológicas e de tráfego, à ultrapassagem assistida e à frenagem automática via
sensores de distância. A tomada de decisões é feita com base em eventos externos e
com as metas estabelecidas internamente, conduzindo a diferentes cursos de ação até
mesmo em ambientes desconhecidos e diante de eventos inesperados. O algoritmo
IA de reconhecimento de imagens em velocidade controla e prioriza o foco para
pedestres, carros, obstáculos etc. No futuro, esses avanços permitirão que os carros
particulares venham a ser substituídos por carros compartilhados (alugados sob de-
manda), significando menos congestionamento, maior segurança e menos acidentes
por falhas humanas, com expectativa de queda no número de proprietários de veículos.
Os veículos modernos equipados com direção baseada em IA oferecem controle
de navegação avançada e inteligente que ajusta a rota na faixa, mantém a velocidade
permitida da via e de acordo com os demais veículos, além de estacionar em vagas.
A tecnologia de comunicação voltada à segurança, ao trânsito e à direção autônoma
(tecnologia de comunicação móvel celular em rede entre veículos e entre veículos e
coisas – cellular vehicle to everything – C-V2X), padronizada em 2017, permite a conexão
entre carros, a infraestrutura e indivíduos usuários da via. Ford, Audi e Ducati foram
as primeiras montadoras a apresentarem a tecnologia em parceria com a Qualcomm.
--- PAGE 115 ---
|
660 Do paradoxo das habilidades à superinteligência pós-humana
Em 2010, a Google anunciou a modificação de uma frota de Prius-Toyota, a
ponto de torná-los totalmente autônomos. Os testes envolveram mais de mil milhas
em estradas americanas convencionais sem a atuação de motoristas, além de outras
140 mil milhas com mínima intervenção humana. O sistema é alimentado com
os dados das estradas do Google Maps e do Google Street View, além de coletar
grande volume de dados em tempo real utilizando vídeos, radares e o Sensor de
detecção e alcance de luz – light detection and ranging sensor (Lidar) acoplados ao
automóvel. O software ainda leva em consideração regras de trânsito, a presença
de objetos e de pessoas, assim como as condições de dirigibilidade.
1) Tesla: fabricante de veículos elétricos.
a) chip de IA em desenvolvimento com a AMD, para equipar os
veículos da marca; e
b) processador gráfico Nvidia.
2) Google: conglomerado Alphabet Inc.
a) Waymo: projeto de carro autônomo, com meta de lançamento do
produto ao mercado para 2020;
b) Lidar: light detection and ranging sensor remoto ativo, mede distância
por meio de 128 feixes de laser pulsado para cobertura de 360° em
torno do veículo, criando um mapa detalhado em 3D;
c) uso de IA com a Intel, que adquiriu a Mobileye; e
d) computador AlphaZero ou AlphaGo.
3) Uber: serviço de carona remunerada utiliza ML para analisar dados de
milhões de viagens e identificar riscos para os motoristas, além de de-
mandar automóveis para os fabricantes.
4) Fabricantes de veículos tradicionais.
a) Volvo: investimentos em startup Apex. AI para desenvolvimento
de SO inteligente;
b) Toyota: assistente de motoristas para veículo autônomo;
c) Mercedes Benz: sistema MBUX-IA com eficiência energética, rea-
lidade aumentada e gráficos interativos;
d) Jaguar Land Rover: desenvolvedora do I-pace para Waymo em
carros compartilhados;
e) GM: veículo autônomo sem volante e sem pedais;
--- PAGE 116 ---
|
Segmentos tecnológicos e aplicações 661
f) Fiat/Chrysler: parceria entre Pacífica Hybrid e Waymo para desen-
volvimento de serviço de táxi sem motorista; e
g) BMW: plataforma robótica Isaa (Nvidia) para robôs de logística
nas fábricas.
A indústria automobilística tem feito uso crescente de cobots, robôs cola-
borativos, por apresentarem grandes vantagens como economia, configuração
simplificada, adaptação a diferentes ambientes de trabalho, tarefas e requisitos de
produção. O aperfeiçoamento dispensa o isolamento de máquina e inclui maior
interatividade com o pessoal de chão de fábrica, devido aos sensores, a visual analytics
e a recursos de aprendizagem de máquina. Por exemplo: i) automated guided vehicle
(AGV) – robôs autônomos de transporte/empilhamento para área de produção;
e ii) automated assistance in administrative decision-making (AAADM) – assistente
automático na tomada de decisões administrativas.
O Grupo Gartner projeta que 270 milhões de veículos estarão conectados a
IoT, com tecnologia 5G, até 2030. Empresas do setor automobilístico, montadoras
e desenvolvedoras de software e hardware estão concentradas na tecnologia de comu-
nicação direta (C-V X) padrão 3GPP para segurança, fluidez e automação viária.
2
As aplicações de gerenciamento inteligente de tráfego reduzem o tempo de
espera, o uso de combustíveis e as emissões de gases de efeito estufa (GEEs). Existem
estimativas de que o emprego de IA em centros de controle operacional de mobili-
dade, integrados à segurança pública, permitirá que o controle de semáforos venha
a diminuir o tempo de deslocamento em 30%, com impacto na qualidade do ar.
A conexão de rota conhecida em inglês como first and last-mile descreve o
começo e o final de uma viagem individual realizada basicamente pelo transporte
público, com deslocamentos não motorizados até o transporte público mais próxi-
mo. Quando a chegada ao destino é inviabilizada pela falta de transporte público,
a estratégia de abordagem pode ser de compartilhamento de veículos autônomos
para acessar comunidades isoladas. No sistema de entrega de mercadorias, os drones
vêm sendo desenvolvidos pela Amazon desde 2016.
No setor de aviação, os algoritmos de IA/ML estão sendo utilizados para prever
os horários de pico de demanda de viagens, auxiliar no check-in de passageiros e
automatizar rotinas de manutenção de equipamentos.
3.6.8 Finanças
Na área financeira, a IA tem sido utilizada para estudar o padrão de comportamento
dos clientes, sugerindo novas opções de investimentos de acordo com os perfis fi-
nanceiros. O desenvolvimento do serviço de assistente virtual financeiro vem sendo
--- PAGE 117 ---
|
662 Do paradoxo das habilidades à superinteligência pós-humana
aperfeiçoado com traços comportamentais e de personalidade, por consultorias e
especialistas em IA e em ML, tais como BoolLabs, Tarkena e B2W Digital.
Entre outras finalidades, estão:
• detectar o desvio de conduta e gastos fora do padrão usual, alertando a
área de segurança bancária para prevenção à fraude;
• reconhecimento de assinaturas manuais, para checagem de autenticidade
de emissão de cheques;
• atender aos questionamentos dos usuários e checar o acesso por reco-
nhecimento de fala; e
• informar autoridades financeiras a respeito de eventos aberrantes.
A reforma digital do sistema financeiro, conhecida por open banking, tem
sinergia com IA, pois a forma aberta de administrar o fluxo de dados e informações
financeiras nos bancos gera uma quantidade massiva de dados. Outro aspecto dessa
tendência é a portabilidade e o controle dos dados particulares pelos consumidores,
que converge para a LGPD.
3.6.9 Auditoria e fiscalização
Desde o início de 2017, o TCU emprega IA, por meio do robô Alice, cujo objetivo
é realizar a análise de licitações públicas e editais – tendo como fontes os diários
oficiais, além do portal Comprasnet –, envolvendo leitura detalhada das chamadas
públicas e dos registros de preços publicados pela administração federal, além de
outras instituições públicas e estaduais/municipais. Os auditores que normalmente
recebiam chamadas diárias do sistema, por e-mail, para verificação de dados incon-
sistentes, tiveram essas tarefas sublimadas. Desde então, muitos casos de propostas/
chamadas públicas que infringiram as normas vigentes foram cancelados ou tiveram
de ser relançados, resultando em otimização dos recursos públicos.
Além de Alice, desenvolvido em parceria com a Controladoria-Geral da União
(CGU), o TCU conta com outros robôs: Sofia, Monica e Zello. O Sistema de
Orientação do Auditor para Fatos e Evidências (Sofia) é responsável por verificar se
algum detalhe foi deixado de fora do texto produzido pelo auditor, notificando-o
a respeito de informações relevantes. Ademais, o robô realiza controles cruzados
com informações do Cadastro Nacional de Pessoa Jurídica (CNPJ), número do
processo e o número do Cadastro de Pessoas Físicas (CPF), alertando sobre pessoas
falecidas. O Monitoramento Integrado para Controle de Aquisição (Monica) apre-
senta uma tabela com todas as compras públicas, até mesmo aquelas ignoradas por
Alice, tais como contratos diretos e inexequíveis. O Zello interage com o público,
por mensagem de texto no Twitter, sobre a atuação do TCU.
--- PAGE 118 ---
|
Segmentos tecnológicos e aplicações 663
Os três robôs fazem parte do Laboratório de Informações de Controle (Lab-
contas), que combina 77 bancos de dados. O sistema permite colher todo tipo de
informação, tais como o registro das contas do governo, a lista de políticas públi-
cas, a composição acionária de empresas, contratos usando fundos e funcionários
públicos processados por instâncias de controle. A CGU, o Ministério Público
Federal (MPF), a Polícia Federal (PF) e os tribunais de contas dos estados também
fazem uso da tecnologia.
3.6.10 Judiciário
O sistema de justiça tem explorado o potencial da tecnologia de IA para dar suporte
ao processo de decisão, redobrando a atenção para mitigar problemas relacionados
a bases de dados deficientes que venham a resultar em vieses na aplicação da lei. A
Advocacia-Geral da União (AGU) utiliza o Sapiens, sistema de IA e automação,
para agilizar os processos jurídicos. Distribuição de tarefas, leitura de processos,
indicação da peça ou tese jurídica são feitas pelo sistema, rápida e consistentemente.
O Conselho Nacional de Justiça (CNJ) implantou o Laboratório de Inova-
ção, por meio da Portaria CNJ no 25/2019, com o objetivo de agregar projetos e
produtos de diversas áreas em ambiente virtual e colaborativo. A pesquisa relativa
à IA tem sido concentrada no Centro de Inteligência Artificial aplicada ao processo
judicial eletrônico, o Inova PJe. Algumas soluções em desenvolvimento envolvem
modelos de triagem de grande massa, movimentação do magistrado, verificação
da petição inicial e geração de texto de conteúdo jurídico, o AutoComplete.
Em colaboração com o Programa das Nações Unidas para o Desenvolvimento
(PNUD), estão sendo elaboradas ferramentas para aperfeiçoar o sistema estatístico
em prol das ações dos Objetivos de Desenvolvimento Sustentável (ODS), desde
2018. Universidades e startups de todo o país também participam de iniciativas
paralelas, a exemplo do Radar, em Minas Gerais, de Elis, em Pernambuco, Sinapse,
em Rondônia, bem como de Poti, Jerimum e Clara, no Rio Grande do Norte. As
colaborações com uso de IA são amplas, estendendo-se desde a classificação ou
rotulagem do processo, passando pela recomendação de decisões até a cobrança
judicial (execuções fiscais pelo sistema Bacen Jud).
A tecnologia de IA especializada em serviço de proteção ao consumidor
apresenta funcionalidades como: i) levantamento e análise do direito reclamado;
ii) riscos associados à ação judicial; iii) probabilidade de sucesso com ganho de
causa; e iv) vantagens associadas à negociação de um eventual acordo.
--- PAGE 119 ---
|
664 Do paradoxo das habilidades à superinteligência pós-humana
4 BIOTECNOLOGIA E INTERFACE CÉREBRO-MÁQUINA
As ciências biológicas estão entre as áreas mais beneficiadas pelo progresso das
TICs, sendo consideradas bastante promissoras para fazer avançar o conhecimento
associado à revolução tecnológica atualmente em curso. Enquanto ramo da biologia,
a engenharia genética estuda métodos de remoção, modificação e transferência de
genes para melhorar características essenciais dos organismos vivos. Na biotecno-
logia, ao menos quatro ramos de atividades se beneficiam das técnicas promovidas
pela engenharia genética: cuidados médicos, produção agrícola, manutenção da
biodiversidade e desenvolvimento de produtos ambientalmente sustentáveis. A
análise e a interpretação dos dados para estudos de interações gênicas, para mode-
lagem e para predição de novas sequências de genes e funções biológicas exigem
um sofisticado conjunto de ferramentas que fazem uso das disciplinas de estatística,
matemática, computação e aprendizagem de máquina.
Estendendo o alcance das funções biológicas ao mundo material, o organis-
mo humano também pode se beneficiar dos avanços tecnológicos que permitem
a comunicação com dispositivos mecânicos externos ao corpo. A neurociência,
caracterizada como ciência interdisciplinar, vem proporcionando relevantes pro-
gressos na compreensão das funções complexas do cérebro e do sistema nervoso,
tanto em seus aspectos físicos quanto mentais. Combinada à linguística, mate-
mática, psicologia, filosofia, computação e engenharias mecânica e química, os
cientistas podem sondar os limites da consciência e seus atributos que viabilizam
os processos de classificação em padrões e tomada de decisão. Para isso, fazem uso
de sinais elétricos emitidos pelos neurônios do cérebro, interpretam esses sinais
para transformá-los em dados e códigos para execução de comandos específicos.
4.1 Evolução, ramificação e regulação da biotecnologia
4.1.1 Conceitos
1) Bioinformática: convergência de ciências ômicas (conjunto de disciplinas
da biologia que termina em ômica, sufixo oma) com a informática, para
evolução da genômica, transcriptômica, proteômica e metabolômica.
Análise fenotípica em larga escala dos mais variados organismos, com
grande geração de dados oriundos do grande número de sequenciamento
genético para identificação e controle de características complexas.
2) Biologia molecular: transferência de conjunto de genes com informações,
até então, ausentes na espécie, que inexistiam na natureza.
3) Biologia avançada: capacidade de sintetizar quimicamente sequências de
DNA, fazendo adições incrementais de genes, direcionamento de novas ro-
tas metabólicas, controle da regulação gênica, modulação de características
--- PAGE 120 ---
|
Segmentos tecnológicos e aplicações 665
fisiológicas e estratégias de controle de desenvolvimento melhorado de
plantas e animais, resistentes a estresses bióticos e abióticos, incremento
de produtividade, introdução de alimentos funcionais e produção de
insumos para o setor farmacêutico.
4) Biotecnologia: conjunto de técnicas utilizadas em aplicações biológicas,
visando manipulações específicas de organismos e melhoramentos de
produtos, podendo constituir-se de sequenciamento de DNA, edição
genética, processo biomolecular e biocelular.
5) Engenharia genética por transgenia (gene estranho): utiliza a biotecnologia
para fazer a manipulação direta, modificação e alteração de características
da sequência genética, geralmente com adição de genes de interesse ou
recombinação do DNA para reprodução da mensagem genética, com
objetivo de estruturar organismos melhorados.
6) Organismos Geneticamente Modificados (OGMs): alternativas de en-
genharia genética que nem sempre utilizam transgenia ou resultam em
alterações significativas de DNA. Exemplos de ferramentas de engenharia
de precisão (técnicas inovadoras de melhoramento de precisão – Timps):
mutação dirigida por oligonucleotídeos (ODMs) que introduzem muta-
ções pontuais ou revertem mutações existentes; e nuclease sítio dirigida
(SDN) que induz mutações, deleções ou inserções no genôma (por
exemplo, zinc finger nuclease – ZFN, transcription activator-like effector
nucleases – tale nuclease e CRISPR-Cas9).28
4.1.2 Escopo de evolução
A expectativa de mudança climática em escala mundial faz crescer a atenção dos
cientistas com a capacidade de adaptação da natureza e suas espécies aos extremos de
temperatura, umidade e pressão. A regeneração por seleção natural decorrente das
novas condições climáticas deve ocorrer de forma lenta e, portanto, pode ser acelerada
com os mecanismos desenvolvidos na biotecnologia, objetivando a criação de espécies
mais robustas. Para isso, a avaliação do patrimônio genético existente em território
nacional torna-se essencial e depende da catalogação e valoração das espécies conhe-
cidas e selecionadas para servir como reservas biológicas resistentes. Ademais, como a
28. Ferramentas para edição de genoma: 1) nucleases de dedo de zinco (ZFNs): são enzimas projetadas para direcionar
sequências de DNA específicas; 2) nucleases com efetores do tipo ativador transcricional (Talens): são proteínas cuja
tecnologia baseia-se em refinamentos das técnicas de modificação por transgenia e na produção de modificações
genéticas permanentes ou temporárias; 3) CRISPR: repetições palindrômicas curtas agrupadas e regularmente interes-
paçadas (em inglês, Clustered Regularly Interspaced Short Palindromic Repeats); Cas9 – CRISPR Associated System 9,
cuja tecnologia de edição genética envolve dois componentes essenciais: 3.1 RNA guia para combinação com gene
alvo desejado; e 3.2 Cas9 proteína funcional (enzima) capaz de quebrar ligações entre ácidos nucleicos (endonuclease),
causando a quebra de DNA de fita dupla, permitindo modificações no genoma.
--- PAGE 121 ---
|
666 Do paradoxo das habilidades à superinteligência pós-humana
biodiversidade tropical é abundante, torna-se razoável avaliar quão numerosas são as
espécies desconhecidas, potencializando a produção agropecuária do país no futuro.
QUADRO 11
Histórico de eventos que marcaram a evolução da biotecnologia
Ano Evento
1856 O monge austríaco Gregor Mendel estabeleceu as primeiras leis da hereditariedade estudando ervilhas verdes e amarelas.
1869 O bioquímico suíço Johann Friedrich Miescher descobre os ácidos nucleicos ao examinar o núcleo dos glóbulos brancos.
O químico russo Phoebus Levene, do Instituto Rockfeller, mostrou que o ácido desoxirribonucleico (DNA) continha quatro
1909
bases nitrogenadas, adenina (A), guanina (G), timina (T) e citosina (C).
1928 Alexander Fleming descobre a penicilina de forma acidental, conduzindo um experimento no Hospital Saint Mary de Londres.
Griffith fez a primeira experiência de transferência genética de ácido nucleico de uma bactéria para a outra, transmitindo
1929
características patogênicas.
Warren Weaver usou a expressão biologia molecular para estudar as propriedades físicas, químicas e biológicas das moléculas
1939
e enzimas do DNA e ácido ribonucleico (RNA) nos processos de transcrição, tradução e replicação.
Francis Crick e James Watson, do Laboratório de Cavendish na Inglaterra, estudaram o genoma humano e concluíram que
1953
a molécula do DNA é estruturada em dupla hélice.
Matthew Meselson e Franklin Stahl demonstraram a replicação do DNA pelo modelo semiconservativo, que implica em
1958 manter a sequência de moléculas originárias nas moléculas recém-formadas (fitas do DNA se desconectam e cada fita
serve de modelo para replicação).
1960 Paul Berg clona o DNA, que contém as informações genéticas responsáveis pela evolução dos seres vivos.
Manfred Clynes e Nathan Kline popularizaram o termo ciborgue (cyborg) para designar organismo cibernético com funcio-
1960
nalidade aumentada, ou seja, humano com implantes de partes biônicas, artificiais e mecânicas.
Herbert Boyer e Stanley Cohen realizaram a primeira transferência de DNA de um organismo para o outro, recombinando
1972
os genes para modificação orgânica funcional.
1974 Rudolf Jaenisch criou o primeiro rato geneticamente modificado.
1985 Ralph Prinstar criou o primeiro porco transgênico.
1986 OSTP aprova regulamentação de plantas geneticamente modificadas e atribuiu competências às agências.
1995 Sequenciamento do genoma não viral da bactéria haemophilus influenza.
1996 Cientista Ian Wilmut clonou o primeiro mamífero com sucesso por meio de uma célula somática, a ovelha Dolly.
A empresa Celera e o Consórcio Público do Projeto Genoma Humano anunciaram, quase ao mesmo tempo, o primeiro
2000
mapeamento ou sequenciamento do genoma humano.
2010 Instituto Craig Venter anuncia a criação do primeiro genoma bacteriano sintético.
Duas cientistas, Jennifer Doudna (da Universidade da Califórnia) e Emmanuelle Charpentier (do Centro Helmholtz, na
2015
Alemanha), inventaram a técnica revolucionária de edição de DNA, por CRISPR.
Cientista He Jiankui anunciou o nascimento de duas gêmeas cujo DNA foi editado pela técnica de CRISPR, para tornar os
2018
bebês imunes ao HIV, com uma variante de maior resistência à doença.
As cientistas Emmanuelle Charpentier e Jennifer Doudna foram laureadas com o Prêmio Nobel de Química pelo desen-
2020 volvimento de método de edição do genoma humano. A enzima CRISPR/Cas, encontrada naturalmente em uma série de
bactérias, funcionava como um par de tesouras moleculares para clivagem do DNA em sequência única.
Alphabet anunciou a criação da empresa Isomorphic Laboratories, com objetivo de desenvolver novas ferramentas para
2021
identificação de produtos farmacêuticos, que utilizará muitos dos produtos da DeepMind.
Elaboração da autora.
--- PAGE 122 ---
|
Segmentos tecnológicos e aplicações 667
A multidisciplinaridade que caracteriza a biotecnologia costuma ser classificada
por cores, conforme as características e funcionalidades das aplicações desenvolvidas,
quais sejam: i) vermelha – saúde humana com uso de genômica, produção de vacinas,
medicamentos, terapias de medicina regenerativa, fabricação de órgãos artificiais;
ii) verde – nutrição e fortalecimento de cultivares, produção de alimentos proveniente
de mais de uma fonte ou espécie; iii) branca – melhoria de processos industriais
com uso de enzimas e desenvolvimento de biocombustíveis; iv) amarela – produ-
ção de alimentos e bebidas mais nutritivas e com menor teor de gordura saturada;
v) azul – utilizada em recursos marinhos, aquicultura e produção de biocombustível
a partir de microalgas; vi) cinza – processos de biorremediação, conservação e recu-
peração ambiental em ecossistemas naturais; e vii) dourada – coleta, armazenamento
e análise de informação biológica, bioinformática.
A biotecnologia empregada na agricultura conta com quatro métodos de
aperfeiçoamento de sementes: i) polinização aberta; ii) hibridação; iii) modificação
genética; e iv) edição de genoma ou de gene. A modificação genética difere dos
outros métodos de forma significativa, pois pode introduzir genes ou características
de outras espécies, o que é conhecido como transgenia. Enquanto os métodos de
melhoramento e precisão, especialmente os desenvolvidos mais recentemente como
edição de genoma, podem fazer uso da imensa quantidade de recursos genéticos
que existem na própria planta para torná-la mais resiliente e nutritiva.
As estimativas indicam que os benefícios do emprego da biotecnologia para
obtenção de organismos geneticamente modificados (OGMs) podem ter represen-
tado agregação de valor médio que ultrapassa US$ 100/ha para os agricultores, com
ganho de produtividade e diminuição de custos.29 O aumento de renda agregada
para aqueles que fazem uso do recurso em culturas como soja, milho, algodão e
canola ao redor do mundo pode ter alcançado cerca de US$ 260 bilhões, entre
1996 e 2020. O incremento de produção global no mesmo período foi de 330
milhões de toneladas para a soja e aproximou-se dos 600 milhões de toneladas para
o milho. A relação entre investimento em sementes transgênicas (custo) e renda
(extra) foi de US$ 1,00/US$ 3,76. A participação nos rendimentos agrícolas pode
ter chegado a 52% nos países em desenvolvimento e 48% nos países desenvolvidos.
No que se refere à saúde humana, o Projeto Genoma Humano, cujo objetivo
foi mapear o sequenciamento genético, teve início em 1998 e permitiu manipular,
combinar, transferir e alterar os genes, posteriormente. O bioquímico Craig Venter,
fundador da Celera e da Synthetic Genomics, é conhecido por ter finalizado o se-
quenciamento do DNA humano em tempo recorde, no ano 2000, antecipando em
três anos o cronograma do Projeto Genoma. O resultado célere foi fruto do desen-
volvimento do método de quebra do DNA em pedaços e leitura em ordem aleatória
29. Disponível em: https://www.ncbi.nlm.nih.gov/pmc/articles/PMC9397136/.
--- PAGE 123 ---
|
668 Do paradoxo das habilidades à superinteligência pós-humana
(shotgun sequence) para o mapeamento do genoma. O Projeto Genoma, iniciativa
conjunta internacional, tinha por objetivo identificar 100 mil genes pelo método
clone-by-clone, distribuídos nos 23 pares de cromossomos, entre 1990 e 2003. Atu-
almente, os dois métodos são considerados equivalentes pela comunidade científica.
Em 2010, Venter anunciou outro feito, a sintetização de vida artificial, con-
siderado o marco científico do milênio pelos especialistas. A quebra da sequência
genética da bactéria Mycoplasma mycoides, existente na natureza, foi possível pela
aplicação de resinas em campo magnético, que permite a leitura com raios X e esca-
neamento digitalizado. O código digital foi transformado em genoma, por meio da
manipulação das quatro substâncias químicas (adenina, timina, guanina e citosina)
agrupadas em blocos de mil letras ATGC. Os pedaços de DNA foram recombi-
nados com a ajuda de fungos para formar o cromossomo sintético completo com
DNA criado em computador. Uma vez injetado em outra bactéria, o cromossomo
transformou-a na espécie manipulada pelos cientistas, Mycloplasma laboratorium.
O interesse de Venter estende-se à prospecção de processos naturais e de
funcionalidades genéticas de microrganismos marinhos. Para isso, criou o projeto
Global Ocean Sampling Expedition para exploração oceânica, em 2004, inspirado
nas expedições de Charles Darwin. Desde então, genomas de linhagens não culti-
vadas foram inventariados em mais de 30 mil milhas navegadas por mais de vinte
países. Os resultados da primeira fase da expedição foram publicados em 2007,
formando um conjunto de dados metagenômicos de 7,7 milhões de sequências ou
6,3 bilhões de pares de DNA, que podem ser encontrados on-line cloud computing
no Community Cyberinfrastructure for Advanced Marine Microbial Ecology
Research and Analysis (Camera).
Em 2015, duas cientistas, Jennifer Doudna (da Universidade da Califórnia)
e Emmanuelle Charpentier (do Centro Helmholtz, na Alemanha), foram respon-
sáveis pela invenção da técnica revolucionária de edição de DNA, por CRISPR.
A tecnologia de edição genômica permite a inserção, substituição e remoção do
DNA de um genoma, utilizando enzimas (nucleases ou tesoura molecular) mo-
dificadas. Trata-se de uma técnica de grande interesse científico, com aplicações
na biomedicina (terapia gênica e controle de doenças), na agricultura (cultivares
resistentes) e na conservação ecológica (clonagem de espécies ameaçadas).
A proteína Cas9 é responsável por cortar o DNA em local específico, daí a
denominação da tecnologia CRISPR-Cas, que permite fazer modificações genéticas
de forma precisa, sem que haja necessidade de introdução de material genético da
mesma espécie ou de espécies diferentes. O sistema CRISPR-Cas foi descoberto
em bactéria archaea como mecanismo de imunidade por adaptação, expressão e
interferência, quando um DNA exógeno ativa o sistema bacteriano de defesa por
meio das enzimas Cas, que cortam o DNA estranho em fragmentos de nucleotídeos.
--- PAGE 124 ---
|
Segmentos tecnológicos e aplicações 669
O uso da técnica tem sido ampliado e os pesquisadores esperam corrigir doenças
genéticas com grande precisão, como diabetes, por exemplo.
O pesquisador chinês He Jiankui, da Universidade de Ciência e Tecnologia
do Sul da China (SUSTech), em Shenzhen, revelou ter editado genes de embriões
humanos, em 2018. Esse tipo de experiência suscita muitas questões de cunho
ético, pois os efeitos no longo prazo são desconhecidos da ciência. Sendo assim,
a legislação da maior parte dos países tem por diretriz vetar a edição de genes de
embriões humanos que resultem em gravidez.
No entanto, em outras disciplinas, a engenharia genética produz OGM há
várias décadas, obtidos com a introdução de DNA exógeno, uma sequência de
genes de outra espécie, para melhoria de certas características da espécie original.
Apesar de legalmente permitidos, tanto os processos de mutação artificial clássica
quanto os processos inovadores (por exemplo, ferramentas de engenharia de pre-
cisão – Timps; mutação dirigida por oligonucleotídeos – ODMs; e nuclease sítio
dirigida – SDN) devem ser submetidos às diretrizes éticas, que incluem transpa-
rência nos resultados das pesquisas.
4.1.3 Regulação
De acordo com a Resolução Normativa no 16/2018, o produto desenvolvido por
OGM, clássico ou moderno, deve ser avaliado pela Comissão Técnica Nacional
de Biossegurança (CTNBio), para enquadramento de acordo com a Lei de Bios-
segurança (Lei no 11.105/2005).
Outro marco importante é a Lei no 13.123/2015, que regula o acesso ao
patrimônio genético no Brasil e ao conhecimento tradicional associado para fins
de P&D tecnológicos. O Sistema de Informação sobre a Biodiversidade Brasileira
(SiBBr), administrado pelo MCTIC, reúne dados sobre a biodiversidade em terri-
tório nacional para subsidiar a produção científica de conservação e uso sustentável
dos recursos naturais. O SiBBR é associado à plataforma Global de Informação
sobre Biodiversidade (GBIF), iniciativa de sessenta países para acesso multilateral
às informações biológicas. Ademais, o Centro Nacional Recursos Genéticos e
Biotecnologia (Cenargen), criado em 1974 pela Empresa Brasileira de Pesquisa
Agropecuária (Embrapa) para intercâmbio de germoplasma vegetal e salvaguarda
dos recursos genéticos, atua em cooperação com o Sistema Nacional de Pesquisa
Agropecuária (SNPA) e com instituições internacionais.
4.2 P&D de interface homem-máquina (IHM) e ICM
A mais revolucionária e desafiante fronteira de desenvolvimento das TICs, no século
XXI, reúne um conjunto de ciências dedicadas ao desenvolvimento da IHM, que
faz uso das tecnologias aplicadas à biologia humana. Um subconjunto específico
--- PAGE 125 ---
|
670 Do paradoxo das habilidades à superinteligência pós-humana
no estudo da IHM trata da ICM ou da interface cérebro-computador (ICC) e
requer o exame de interações entre sistemas biofísicos e artificiais, visando projetar
dispositivos para melhorar a capacidade física, expressiva e cognitiva do ser humano.
Os sistemas ICC/ICM permitem que o usuário interaja com o ambiente que o
cerca, movimentando mecanismos controlados ou gerados pela atividade cerebral.
As primeiras pesquisas sobre o sistema de interação com ICM visavam de-
senvolver dispositivos de comunicação para que pessoas com dificuldades motoras
pudessem enviar sinais elétricos do cérebro ao computador, a fim de controlar a
movimentação. O sistema computacional que capta, processa, interpreta e classifica
a informação codificada dos neurônios cerebrais atua em tempo real para acionar
o mecanismo artificial externo. O maior desafio era identificar a associação entre a
intenção do indivíduo e um sinal padrão do cérebro para, posteriormente, retirar o
ruído do vetor de dados e traduzi-lo em comando de movimento do cursor, braço
mecânico ou de qualquer outro artefato que permitisse a locomoção.
Atualmente, o campo da disciplina ICM está em pleno processo de expansão
e emprega quatro tecnologias convergentes para obtenção de aplicações diversas,
quais sejam: a TIC, a biotecnologia, a nanotecnologia e a neurociência. A bio-
tecnologia é o estudo de organismos modificados geneticamente por meio de
ferramentas da genômica; a nanotecnologia estuda universos invisíveis a olho nu,
sejam eles animados ou inanimados; e a neurociência é o campo mais enigmático
das ciências que estuda o funcionamento do cérebro, a cognição humana e as
alterações de comportamento.
As interfaces de controle consideradas são físico-neuronais, utilizando sen-
sores extra ou intracranianos, atuadores e realidade aumentada para entender o
funcionamento do cérebro. Nesse contexto, a biomanufatura avançada desempenha
papel crucial para fabricação de biomoléculas e biomateriais a partir de sistemas
biológicos, utilizados em sensores e dispositivos diagnósticos. Os insumos para
produção provêm de fontes naturais, como sangue, culturas de micróbios, células
vegetais e animais cultivadas em laboratórios com equipamentos sofisticados.
O funcionamento da ICM inclui programação complexa para detectar as infor-
mações cerebrais e interpretá-las, como os algoritmos genéticos (por exemplo, filtro
da Kalman, métodos bayesianos) e a regressão linear múltipla (por exemplo, filtro de
Wiener). O uso da regressão agrega as atividades elétricas produzidas pelos neurônios
corticais e transforma impulsos simultâneos em lineares para fazer previsões sobre
a localização dos movimentos corporais. A locomoção ocorre por meio de próteses
robóticas ou exoesqueletos, que restauram a funcionalidade a pacientes com graus
severos de paralisia, como no Projeto Walk Again, liderado por Miguel Nicolelis,
fruto da parceria Brasil, Estados Unidos, Alemanha e Suíça (Mussatto e Silva, 2014).
--- PAGE 126 ---
|
Segmentos tecnológicos e aplicações 671
4.2.1 Políticas de incentivo
Em 2013, o governo norte-americano reuniu uma série de iniciativas de pesquisa
em neurociência e cognição sob um mesmo programa guarda-chuva, a iniciativa
de pesquisa avançada e inovativa em neurotecnologias (Brain Research through
Advancing Innovative Neurotechnologies – The Brain Initiative). Em 2006, uma
oficina de trabalho realizada na Fundação Nacional de Ciência (NSF) dos Esta-
dos Unidos já apresentava preocupação com a integração das diversas pesquisas
realizadas na área para compreensão da dinâmica do funcionamento cerebral. Os
domínios de interesse foram, assim, elencados em relatório:
• plasticidade adaptativa: sobre o comportamento orgânico para otimização
e sucesso em ambientes mutáveis;
• conflito e cooperação: sobre modelos de representação cognitiva e de
funcionamento neuronal para entendimento da tomada de decisão;
• reconhecimento espacial: sobre células neuronais de localização no hi-
pocampo responsáveis pela memória de longo prazo;
• tempo: sobre como o cérebro processa a passagem do tempo e os estímulos
sensórios de causa e efeito;
• linguagem: sobre a decodificação da fala e da compreensão, com relações
sintáticas e desambiguação dos múltiplos significados de contexto;
• entendimento de causalidade: sobre modelos mentais, significados lin-
guísticos, percepção, julgamento e decisão; e
• novas ferramentas para a ciência do cérebro e da mente: sobre circuitos
cerebrais, inovações matemáticas, base de dados e informação, integração
de ferramenta molecular no sistema neurocientífico e ciberinfraestrutura.
A iniciativa Brain 2025, lançada em 2013, dá continuidade à pesquisa avançada
do cérebro utilizando neurotecnologia, que busca entender o funcionamento do
cérebro por meio do mapeamento de circuitos cerebrais, fluxos eletroquímicos e
padrões de interação responsáveis pela capacidade cognitiva e comportamental. O
relatório de 2014 do grupo de trabalho dos Institutos Nacionais de Saúde (National
Institutes of Heath – NIH), intitulado Brain 2025: a scientific vision (Bargmann
et al., 2014), apresenta as metas do programa, quais sejam: i) caracterizar a diver-
sidade de células do cérebro e suas funcionalidades; ii) entender a relação entre
a estrutura neuronal e o funcionamento do cérebro, por meio de diagramas de
circuitos para mapeamento de sinapses; iii) monitorar a dinâmica das atividades
da rede neuronal completa, por longos períodos de tempo; iv) demonstrar causa-
lidade entre ativação/inibição de neurônios por meio de mecanismos específicos
que modificam a dinâmica cerebral; v) produzir fundamentação conceitual para
--- PAGE 127 ---
|
672 Do paradoxo das habilidades à superinteligência pós-humana
entendimento da base biológica dos processos mentais por meio de desenvolvi-
mento de novas teorias e dados analíticos; vi) desenvolver tecnologias inovativas
para entendimento do cérebro e tratamento de desordens; e vii) integrar as novas
abordagens para descobrir como os padrões dinâmicos de atividade neuronal são
transformados em cognição, emoção, percepção e ação.
A iniciativa público-privada seria coliderada pelo instituto norteamericano
de saúde mental (National Institute for Mental Health – NIMH) e o Instituto
Nacional de Distúrbio Neurológicos e AVC (NINDS). O planejamento envol-
veria recursos anunciados da ordem de US$ 400 milhões a.a. entre 2016-2020,
e de US$ 500 milhões a.a. entre 2021-2025, além de contar com a colaboração
de equipes multidisciplinares de cientistas talentosos.
A Big Data to Knowledge (BD2K), programa lançado em 2013, envolve
todos os NIHs, para apoiar o desenvolvimento de pesquisas e ferramentas para
acelerar a integração de dados biomédicos. Projetado para ser desenvolvido em
duas fases, primeiro o BD2K elegeu projetos para financiamento e depois aplicou
os resultados em áreas finalísticas. Complementarmente, o projeto-piloto NIH
Data Commons testa a viabilidade do emprego de plataformas públicas em nu-
vem, sempre disseminando boas práticas como no caso do Educação em Recursos
Universitários Digitais para Investigação Translacional e Educação (ERuDIte),
um índice de recursos educacionais classificados por tópicos de dificuldade para
uso da comunidade de pesquisa biomédica. O BD2K fundou treze centros de
excelência, considerados projetos de grande porte para desenvolvimento de novas
abordagens, métodos, softwares e ferramentas, além de treinar pessoal na ciência
big data aplicada com foco na área biomédica.
A instituição designada Coordenação e Colaboração entre Centros (CCC)
tem descentralizações, os Centers for Causal Discovery – CCDs (por exemplo,
Universidades de Yale, Carnegie Mellon e Pittsburg – Centro de Supercompu-
tação), que disponibilizam listas de recursos existentes e resultados alcançados. A
atuação contempla disciplinas do tipo compressão/redução de dados, visualização
de dados, provimento de dados, disputa de dados, privacidade de dados, reutili-
zação de dados, refinamento e aplicação de metadados, meios digitais para análise
de dados biomédicos via crowdsourcing, padronização de dados biomédicos. Os
ensaios de algoritmos estão ocorrendo em algumas áreas, como mutações e padrões
de combinação de doenças autoimunes, imageamento de tecidos para detecção de
patógenos e padrões de comunicação causal entre partes do cérebro. As iniciativas
de investigação juniores são incentivadas por meio de uma série de laboratórios de
inovação apoiados pela NSF e pelos NIHs.
O movimento complementar do governo norteamericano, dessa vez menos
vinculado às máquinas e dirigido a melhorar a capacidade de prevenir e tratar
--- PAGE 128 ---
|
Segmentos tecnológicos e aplicações 673
doenças é a Iniciativa de Medicina de Precisão (PMI Cohort Program). Lançado
em 2015, o programa busca redefinir o entendimento das doenças estabelecidas e
em progressão, a resposta aos tratamentos e o consequente estado de saúde/doença,
por meio da mensuração molecular e de fatores ambientais e comportamentais.
A iniciativa conta com o instituto dedicado às disparidades em saúde e minorias
(National Institute on Minority and Health Disparities – NIMHD), além do apoio
de 27 organizações (centros de pesquisa e NIHs).
A PMI produz o diagnóstico acurado, as estratégias racionais de prevenção de
doenças, a seleção de melhor tratamento medicamentoso e o desenvolvimento de novas
terapias. As aplicações práticas incluem desenvolvimento de estimativas quantitativas
de risco para doenças que integra exposição ambiental, fatores genéticos, interações
com o meio, eficiência terapêutica customizada, biomarcadores com graus de pro-
pensão a doenças e utilização de tecnologias móveis para correlacionar atividades e
medidas fisiológicas que empoderam os pacientes com as informações necessárias para
aperfeiçoamento da própria saúde.
O número de voluntários para teste previsto inicialmente era de 1 milhão de
pessoas, com dois grupos principais, um vivendo em território americano, forne-
cendo exames básicos de caracterização da bioespécime, e outro com indivíduos
recrutados por organizações provedoras de saúde. A coleção de espécimes bioló-
gicas, com dados sanguíneos, microbioma, amostra capilar/unha, entre outros,
destinada ao biorrepositório central – o biobanking – permite a recuperação e a
análise bioquímica por laboratórios.
4.2.2 Desenvolvimento e aplicação
FIGURA 14
Conjunto de biossensores cardíacos 3D
Fonte: Universidade Carnegie Mellon. Disponível em: https://www.cmu.edu/news/stories/archives/2019/august/heart-cell-sensors.html.
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
--- PAGE 129 ---
|
674 Do paradoxo das habilidades à superinteligência pós-humana
originais (nota do Editorial).
A Universidade de Carnegie Mellon e a NTU Singapura (Nanyang Techno-
logical University) desenvolveram a plataforma “organ-on-e-chip” (órgãos em chips
eletrônicos), a qual utiliza sensores bioelétricos para mensurar a eletrofisiologia de
células cardíacas em três dimensões, o que possibilita compreender como as células
comunicam-se em sistema multicelular. A técnica serve para avaliar a eficácia de
medicamentos em tratamento de doenças como arritmias e para regenerar tecidos
danificados por ataques cardíacos.
A plataforma “organ-on-e-chip” tensiona o chip parecido com um pequeno
retângulo plano e rígido que passa a ser flexível quando a tensão é liberada, enrolando
a célula como um bracelete em microescala. Os pesquisadores fixam uma série de
sensores feitos de eletrodos metálicos ou sensores de grafeno na superfície do chip
e, então, gravam uma camada inferior de germânio, conhecida como a “camada
sacrificial” que, uma vez removida, permite à matriz biossensora ser liberada de
seu porão e rolar para cima da superfície em uma estrutura em forma de barril.
O Projeto Darpa, Sistemas Microfisiológicos (MPS), permite avaliação de
segurança e eficácia de medidas médicas contra ameaças naturais e antrópicas à
saúde, incluindo doenças infecciosas emergentes e ataques químicos ou biológicos.
Testar esses tipos de contramedidas em humanos ou animais envolve questões éticas
e técnicas complexas. Para superar esse desafio, o programa MPS está desenvolvendo
tecnologia de plataforma in vitro, a fim de avaliar rapidamente os efeitos das con-
tramedidas médicas na saúde humana usando sistemas “organoides” interligados
que incorporam tecnologia de tecido humano e microfluidos em microchips que
imitam as funções dos sistemas fisiológicos humanos.
Equipes trabalham para desenvolver plataforma reconfigurável que permita
o estudo simultâneo de dez ou mais sistemas fisiológicos in vitro interligados, dis-
postos em qualquer sequência, com a capacidade de sustentar tecido por até quatro
semanas para avaliar efeitos ao longo do tempo. As equipes devem demonstrar que
os tecidos projetados funcionam juntos para reproduzir com precisão os sistemas
fisiológicos humanos que eles pretendem simular e o cruzamento de informações
biológicas que ocorre entre os sistemas. Para validar a capacidade preditiva da pla-
taforma, as equipes estão testando compostos com efeitos conhecidos em humanos.
--- PAGE 130 ---
|
Segmentos tecnológicos e aplicações 675
FIGURA 15
Diagrama esquemático do sistema microfisiológico
Fonte: Darpa. Disponível em: https://www.darpa.mil/program/microphysiological-systems.
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
Pesquisas relacionadas estão aplicando agentes infecciosos na plataforma para
entender se os efeitos fisiológicos das ameaças à saúde podem ser modelados para faci-
litar o desenvolvimento de novas contramedidas. A Darpa envolveu a Food and Drug
Administration (FDA) desde o início do programa MPS, para garantir que os desafios
regulatórios de revisão da segurança e eficácia dos medicamentos sejam considerados
durante o desenvolvimento da plataforma MPS. A Darpa também está coordenando
esforços com os NIHs, que têm realizado pesquisas separadas, mas paralelas. Se o
programa MPS for bem-sucedido, a plataforma resultante deve diminuir o tempo de
desenvolvimento e aumentar o número e a qualidade das contramedidas médicas para
agentes de bioameaça e cuidados clínicos em análise na FDA.
O Projeto Cérebro Humano (Human Brain Project – HBP) uma ação co-
ordenada por fundo da União Europeia, em 2013, tem por proposta entender o
funcionamento do cérebro humano, desenvolver novos tratamentos e construir
a IHM mais emblemática para a metamorfose da biologia humana (Markram,
2012). Aproximadamente três centenas de neurocientistas, médicos e cientistas
da computação foram reunidos pela iniciativa ICT-Accelerated para pesquisa
aplicada ao cérebro.
4.2.3 Movimentação coordenada
O esforço de concertação internacional do HBP tende a superar o problema da
desarticulação de pesquisas e da fragmentação de dados, integrando e unifican-
do a figura do cérebro em sistema único multinível. Para emular a capacidade
computacional do cérebro, modelos matemáticos e ferramentas de TICs serão
--- PAGE 131 ---
|
676 Do paradoxo das habilidades à superinteligência pós-humana
compartilhadas, assim como uma massiva quantidade de dados será analisada. As
iniciativas de tecnologias emergentes do futuro, Future and Emerging Technolo-
gies (FET) Flagships, reúnem pesquisas multidisciplinares de larga escala, longo
prazo e orientadas pela ciência, envolvendo mais de uma centena de instituições
parceiras na União Europeia, dos Estados Unidos, do Japão e da China, voltadas
à resolução de grandes desafios científicos.
A versão chinesa para o programa de estudo das funções cerebrais é o projeto
China Brain, lançado em 2016 e aprovado pelo Congresso como parte do 13o
Plano Quinquenal e, mais especificamente, compondo o Programa de Ciência,
Tecnologia e Informação (CT&I) 2030, com duração de quinze anos. O projeto
tem como metas o entendimento das funções cognitivas, o diagnóstico e a preven-
ção de doenças do cérebro, como forma inclusive de orientar as pesquisas de TIC
e IA/ML/DL. O aparato institucional envolvido no projeto inclui a fundação do
Instituto Chinês para Ciências do Cérebro, vinte institutos acadêmicos de ciência
e universidades, além de um consórcio de laboratórios.
No projeto BrainDriver, dirigindo com o cérebro, da moderna e internacio-
nalizada Universidade Livre de Berlim (Freie Universitat Berlin), o condutor utiliza
ICM para dirigir o automóvel e controlar um cursor por meio de ondas cerebrais.
O sistema, baseado em eletroencefalograma (EEG), capta as informações de uma
tiara com eletrodos instalada na cabeça da pessoa com deficiência motora e envia
para o computador instalado no painel de controle do veículo.
Na Rússia, o Manifesto Internacional Rússia 2045 declarou a insatisfação
com as conquistas científicas e o progresso técnico voltadas para a sociedade de
consumo, uma vez que o rumo tomado seria incapaz de assegurar o avanço em
direção a uma radical mudança no estilo de vida da população. O paradigma
ideológico proposto requereria o desenvolvimento do próprio ser humano e não
apenas do meio em que vive. O banimento do envelhecimento e da morte, além
da superação dos limites de capacidades física e mental fazem parte da crença dos
fundadores do movimento, entre os quais Dmitry Itskov.
Empreendedor russo fundador do movimento Rússia 2045, Itskov criou o
projeto Iniciativa Social Estratégica, com o objetivo de fazer o upload mental, isto é,
a transferência da essência mental humana para dispositivo de memória eletrônica.
O cronograma de implantação do projeto de corpo humano artificial, com
controle mental transplantado, encontra horizonte em 2045, tendo sido dividido
em quatro fases, quais sejam: i) 2015-2020 – construção de cópia robótica (an-
tropomórfica) do corpo humano remotamente controlada via Brain Computer
Interface (BCI); ii) 2020-2025 – criação de avatar equipado com sistema de suporte
às funções vitais para recepção de cérebro humano transplantado após o falecimento
de um indivíduo; iii) 2030-2035 – concepção de avatar com cérebro artificial, no
--- PAGE 132 ---
|
Segmentos tecnológicos e aplicações 677
qual os aspectos de personalidade e consciência humana são carregados ou trans-
feridos sinteticamente após a morte do indivíduo; e iv) 2040-2045 – geração de
avatar na forma de holograma.
A transferência da consciência humana para o corpo físico artificial seria o
derradeiro objetivo da Estratégia 2045. Para isso, o processo de implementação do
projeto tecnológico resultaria inevitavelmente no desenvolvimento explosivo de
inovações, além de promover mudanças civilizatórias, como o aperfeiçoamento da
vida humana. O novo ser humano receberia grande espectro de habilidades, sendo
capaz de suportar condições externas extremas, como altas temperaturas, pressão,
radiação e ausência de oxigênio. Utilizando interfaces neurais, o homem seria capaz
de operar diversos corpos de diferentes formas e tamanhos, tudo feito remotamente.
O desenvolvimento tecnológico seria associado ao progresso intelectual, moral,
físico, mental e espiritual, conceitos e valores esses perdidos e necessários para o
salto futuro de evolução, segundo o mentor do movimento.
4.3 Principais desafios do avanço tecnológico
Os levantamentos feitos por Yuval Harari nos best-sellers Sapiens (2011) e Homo
Deus (2016) lembram que a ambição do visionário russo nem precisaria chegar a
tanto. Bastaria manipular a bioquímica humana, com envio de estímulos nervosos
diretamente aos pontos específicos do cérebro ou, então, utilizar a genética para
elevar os níveis globais de felicidade do homem. Paradoxalmente, o acúmulo de mais
dados e o incremento do poder de computação, que podem elevar os seres humanos
à condição de deuses, podem também fazer com que os humanos sejam irrelevantes.
Compreender e superar os mecanismos do envelhecimento e da morte pode
ser igualmente poderoso para substituir os humanos em todas as tarefas. Apesar
do imenso progresso técnico da IA nas últimas décadas, o avanço na consciência
das máquinas foi nulo, até onde é permitido saber, afirma Harari. Os humanos
correm o risco de perder seu valor porque a inteligência estaria se descolando da
consciência. Em síntese, a superinteligência teria de passar pelo estreito caminho
da consciência e os humanos terão que fazer o upgrade de suas mentes se quiserem
permanecer no jogo.
Nesse ponto, o círculo fecha-se novamente, com a pantanosa discussão sobre
o tema consciência e processos mentais. Stuart Hameroff, anestesista da Univer-
sidade do Arizona, e Roger Penrose, matemático da Universidade de Cambridge,
formularam a teoria da redução objetiva orquestrada (orchestrated objective reduc-
tion – Orch-OR), em 1994/1998, para explicar que o fenômeno da consciência
é derivado de atividade quântica ocorrendo em microtúbulos de níveis profundos
nos neurônios cerebrais. O que permitiria inferir que a consciência seria gerada a
partir do nível quântico, com eventos ocorrendo em escala subatômica.
--- PAGE 133 ---
|
678 Do paradoxo das habilidades à superinteligência pós-humana
Os cientistas sugeriram que a consciência nasce de estruturas minúsculas
(tubos minúsculos) feitas de proteínas, enquanto o cômputo vibracional quântico
seria orquestrado por entradas sinápticas e memória armazenada nos microtúbulos
e se encerraria com uma redução objetiva, autocolapso da função de onda. Os
microtúbulos formariam a estrutura celular que existe em todas as partes do corpo,
inclusive no cérebro. Em 2014, a proposição foi atualizada com evidências de que
quantum bits ou “qubits” OrchOR seguem caminhos helicoidais em grades de
microtúbulos, contestando críticas anteriores por meio de vinte predições testáveis
da teoria de OrchOR, das quais seis foram confirmadas e nenhuma foi refutada.
Segundo Penrose (1994), a consciência é decorrente dos processos subatô-
micos (quânticos) que ocorrem na estrutura proteica dos microtúbulos. Mesmo
amebas, desprovidas de células cerebrais, possuem consciência e são capazes de se
reproduzir, locomover e encontrar comida. Esse fenômeno seria coerente com o
ritmo observado por meio de EEGs, na anestesia feita nos humanos, na orientação
de bando de aves e na fotossíntese vegetal. A comprovação da teoria poderia ter
efeitos práticos no tratamento das condições cognitivas, neurológicas e mentais.
Experimentos clínicos de estimulação cerebral, visando à ressonância microtubular
com o ultrassom transcraniano, repercutiram na melhora do senso de humor. A
terapia pode ser utilizada na recuperação de danos cerebrais e na atenuação da
síndrome de Alzeimer, no futuro.
Enquanto determinadas características e habilidades humanas parecem difíceis
de superar por meio da programação de máquina, como criatividade, intuição e
sentimento, os cientistas continuam trabalhando nos sistemas biônicos. O projeto
Brain Gate Interface, consórcio liderado pela Universidade de Brown dos Estados
Unidos, permite aos paraplégicos acionar o computador com o cérebro e sem
fios. Experimento que representou um marco para a história da ciência, por ter
permitido ao primeiro humano sem movimentos, Mattew Nage, controlar o jogo
Pong, do Atari, com o cérebro. A capacidade do dispositivo, agulhas de 128 canais
de eletrodos, passou a interferir no funcionamento do cérebro após algum tempo,
levando os cientistas ao aperfeiçoamento.
Em março de 2018, Elon Musk criou o projeto Neuralink para BCI, cujo
protótipo foi apresentado em meados de 2019. A transferência de informações
para o humano será feita por meio de um conjunto de finíssimos fios de polímeros
flexíveis ligando chips ao cérebro. Os filamentos têm cerca de 5 nanômetros de
espessura, com capacidade de transmissão de 3.072 eletrodos/96 fios. O chip tem
capacidade de identificar, limpar e amplificar sinais do cérebro para a máquina,
via cabo USB-C, que derivarão para uma conexão sem fio no futuro, o sensor
N1, com a IA. Os resultados obtidos pelos neurocientistas nos experimentos com
macacos atestam o controle do computador pelo animal. Cobaias humanas estão
sendo recrutadas para participar do projeto (Wakka, 2019b).
--- PAGE 134 ---
|
Segmentos tecnológicos e aplicações 679
O cientista brasileiro Miguel Nicolelis, professor de neurobiologia, engenharia
biomédica, psicologia e neurociência, lidera um grupo de pesquisa aplicada de sistemas
associados à fisiologia de órgãos humanos. Nicolelis também está à frente do Instituto
Internacional de Neurociências de Natal Edmond e Lily Safra (IIN-ELS) e do Centro
de Neuroengenharia da Universidade de Duke (DUCN). No projeto denominado
Andar de Novo (The Walk Again Project), os sinais elétricos dos neurônios controlam
dispositivos robóticos, como braços e pernas mecânicas de exoesqueletos IHM ou
ICM, com fabricação de próteses neurais para tetraplégicos. O IIN-ELS desenvolveu
sistemas pioneiros de ICM, como alcançar/agarrar objetos, deslocar em locomoção
bípede, incorporar a sensação somática artificial, além de trabalhar para viabilizar a
transmissão de informações entre animais localizados em ambientes distintos.
Outra linha de pesquisa conduzida por Nicolelis indica ser possível recuperar
capacidades perdidas utilizando a técnica de religamento dos nervos em partes do
córtex cerebral saudáveis ou sem avarias. Funciona como o fenômeno da sinestesia,
com a mescla dos sentidos de visão e tato, por exemplo, ouvir um som e ver uma cor
associada a ele. Teoricamente, poderia ocorrer ampliação dos canais sensoriais, como
visão de raios X, sensação de ondas de campo magnético, rádio e infravermelho.
O doutor em ciência política Francis Fukuyama, filósofo anti-transhumanismo
e ligado ao movimento de neoconservadorismo, acredita que a dignidade da raça
humana está no fato dela ser produto da natureza e defende fortemente a regulação
da biotecnologia. No livro Nosso Futuro Pós-Humano: consequências da revolução
da biotecnologia, a perspectiva bioética de Fukuyama (2003) deixa claro ser avessa
à modificação genética do ser humano pela biotecnologia, admitindo apenas a
evolução como resultante do processo natural de seleção milenar. Fora desse pro-
cesso, a raça deixaria de ser humana para se transformar em algo classificado como
pós-humano, com repercussões inclusive para a liberdade democrática e para o
modo de fazer política. Cabe ressaltar que, o transumanismo defende o direito da
ciência e do indivíduo ao uso responsável da engenharia genética para a melhoria
da capacidade de concentração, memória e a expansão dos limites da mente.
Nick Bostrom,30 filósofo, físico teórico e neurocientista, autor do livro Supe-
rintelligence: paths, dangers, strategies, de 2014, trata do aparecimento de uma nova
espécie de IA capaz de aprender por conta própria, a qual denominou superinteli-
gência. Segundo Bostrom (2014), a tecnologia super-IA deverá ir além da limitada
biologia, podendo ultrapassar largamente as habilidades humanas. Apesar de estar
apenas em fase de desenvolvimento, é preciso que os mecanismos de controle se-
jam igualmente observados. A comunidade dedicada ao assunto é numerosa, com
muitas ramificações em ciência da computação e neurociências. O forte incentivo
comercial, que desconsidera o controle de efeitos colaterais danosos da tecnologia,
30. Diretor-fundador do Instituto para o Futuro da Humanidade da Universidade de Oxford. Ver Bostrom (2005).
--- PAGE 135 ---
|
680 Do paradoxo das habilidades à superinteligência pós-humana
pode levar a projetos mal elaborados a repercussões que ameaçariam à existência da
própria humanidade no próximo século. Em contrapartida, as super-IAs benéficas
seriam capazes de criar melhor que quaisquer inventores humanos. O retardamento
do envelhecimento, a colonização espacial e a conexão neural com as máquinas
viriam em tempo recorde, sugere Bostrom (2014).
No livro Human Enhancement, Bostrom (2009) defende a manipulação
genética e faz crer que a humanidade será radicalmente modificada pelas futuras
tecnologias, em uma escala jamais imaginada. Em coparceria com David Pearce, o
autor fundou a World Transhumanist Association e com James Hughes, o Institute
of Ethics and Emerging Technologies. O conhecimento gerado para a cura e pre-
venção de doenças estaria ocorrendo muito lentamente, na opinião de Bostrom. O
movimento antagônico, o bioconservativismo, alega que a tecnologia compromete
a dignidade humana (respeito e mérito) e defende amplas proibições aos aperfei-
çoamentos da genética humana, devido à possível degradação pós-humana. Algo
de profundamente valioso a respeito do ser humano teria como fundamento o
sentimento religioso e o secularismo.
Brundage (2015) considera que Bostrom amplia seu escopo além dos sistemas
superinteligentes individuais com implicações para a sociedade como um todo. A
análise é avaliada como lúcida, mas bastante abstrata, pois o livro oferece pouco
em termos de sugestões concretas para combater as preocupações levantadas. O
autor apresenta um quadro estratégico, no qual analisa questões de correlação entre
os avanços em diferentes domínios tecnológicos e de cooperação entre diferentes
projetos de desenvolvimento de IA. No que se refere aos ritmos de desenvolvimento
tecnológico, Bostrom postularia um princípio de desenvolvimento tecnológico
diferenciado, no qual a sociedade deve retardar o desenvolvimento de tecnologias
perigosas e nocivas, especialmente aquelas que elevam o nível de risco existencial,
e acelerar o desenvolvimento de tecnologias benéficas (Brundage, 2015).
4.3.1 Desafios políticos no Brasil
Pesquisa qualitativa para diagnóstico da bioeconomia no Brasil, promovida pela
Confederação Nacional das Indústrias (CNI) com mais de 150 especialistas, em
2014, mostrou que os principais entraves para o país se tornar referência em escala
mundial seriam: i) insegurança jurídica para desenvolvimento da biotecnologia
(78,8%); ii) insuficiência das linhas de apoio à pesquisa (77,5%); iii) inadequação
do marco regulatório (65,6%); e iv) falta de profissionais qualificados (55,6%).
--- PAGE 136 ---
|
Segmentos tecnológicos e aplicações 681
4.3.2 Desafios regulatórios
As consequências da evolução humana para transumano ou ciborgue; a eugenia via
seleção de embriões, as escolhas e preferências por características genéticas; a edição
de DNA em oposição ao processo de evolução por seleção natural; e a criação de
novas espécies são algumas das controvérsias no campo da biotecnologia que suge-
rem atenção redobrada para documentos referenciais, conforme a seguir descritos.
1) Código de Nuremberg (1947): experimentações com humanos.
2) Declaração de Helsinki (1964): orientações sobre investigação biomédica.
3) Conferência de Asilomar (1975): para regulação das tecnologias de DNA.
4) Informe da Comissão de Belmont (1978): princípios básicos da bioética.
5) Convenção sobre Diversidade Biológica – CDB (1992/1993): no Protoco-
lo de Nagoya, 192 países reconheceram a soberania nacional sobre recursos
genéticos, condicionando o acesso a informe previamente autorizado. O
CDB objetiva conservar a biodiversidade, utilizar os recursos de forma
sustentável e compartilhar os benefícios da exploração de forma justa e
equitativa. Além disso, faz referência à conservação da biodiversidade
em três níveis: ecossistema, espécies e recursos genéticos. As orientações
aprovadas no âmbito da CDB são: i) diretrizes de Bonn sobre acesso
aos recursos genéticos; ii) diretrizes de Akwé Kon para avaliação de
impactos sobre indígenas e populações locais; iii) princípios de Addis
Abeba sobre utilização sustentável da biodiversidade; iv) princípios de
abordagem ecossistêmica para gestão da biodiversidade; v) diretrizes
para turismo sustentável em ecossistemas sensíveis; vi) diretrizes para
prevenção, controle e erradicação de espécies exóticas invasoras; e vii)
diretrizes para incorporar questões de biodiversidade na avaliação de
impactos ambientais.
6) Tratado Internacional sobre Recursos Fitogenéticos para Alimentação e
Agricultura – Tirfaa (2001): promove a conservação dos recursos fito-
genéticos para alimentação e agricultura.
7) Protocolo de Cartagena (2003): sobre biossegurança.
8) Protocolo da Nagoia sobre Acesso aos Recursos Genéticos e Repartição
dos Benefícios (2014): assinado, mas pendente de ratificação pelo Con-
gresso Nacional.
9) Protocolo Suplementar de Kuala Lumpur (2018): responsabilidade
e reparação.
--- PAGE 137 ---
|
682 Do paradoxo das habilidades à superinteligência pós-humana
4.3.3 Desafios éticos e socioeconômicos
Os avanços da ciência e da biotecnologia trazem efeitos colaterais de ordem ética
e socioeconômica, nem sempre tão auspiciosos e, por isso, precisam ser relembra-
dos. Esses estão relacionados ao envelhecimento e à saúde, como: i) medicalização
desde o nascimento, ao longo da vida e durante o processo de senescência, com
prolongamento artificial das sensações de bem-estar, especialmente em pacientes
afetados por doenças crônicas ou incuráveis e mantidos concomitantemente por
meio de instrumentos; ii) transplantes de órgãos modificados em animais, com
ponderações pertinentes a respeito da complexidade do procedimento, potencial de
rejeição e outras incertezas relacionadas ao xenotransplante; iii) garantia do direito
à vida versus exaustão da capacidade do sistema de saúde (hospitais, leitos e unida-
des de terapia intensiva – UTIs), com a perspectiva de intensificação dos cuidados
domiciliares (home care) e reintegração dos idosos à vida laboral e à sociedade.
5 AEROESPACIAL E SATÉLITES
Os satélites artificiais que orbitam o planeta são empregados com diversas finalidades,
como comunicação, meteorologia, navegação, geoposicionamento, observação da
Terra, entre outras. Além do desenvolvimento científico e tecnológico, a fabricação
de satélites é um ramo da indústria aeroespacial geralmente associado à defesa dos
países, pois lida diretamente com bases para lançamentos de veículos propulsores,
cuja função é colocar os equipamentos em órbita. A operação dos satélites, por sua
vez, pode ser explorada comercialmente para transmissões de imagens, sons e dados.
Tanto a fabricação quanto a operação de satélites estão passando por período
de grandes transformações. Constelações de nanossatélites, internet de banda lar-
ga, telemedicina, teleducação, IoT, M2M são alguns exemplos de como as TICs
impulsionaram o mercado aeroespacial de satélites de comunicações, no conceito
conhecido como NewSpace. O movimento emergente surgiu na virada do século
e tem forte conotação comercial, pois envolve a competição de agentes privados
para fornecer serviços de comunicações, localização e transporte, além da disputa
entre nações para exploração espacial e colonização de outros planetas.
QUADRO 12
Histórico dos eventos relacionados ao mercado de telecomunicações e satélites
Ano Evento
Arthur C. Clark, autor de 2001: uma odisséia no espaço e membro da Sociedade Interplanetária Britânica, escreveu artigo
1945
sobre a transmissão de sinais de comunicações a partir do espaço na revista Wireless World.
Notícia do primeiro satélite artificial, Sputinik 1, lançado pelos russos para teste de comunicação interespacial.
1957
Sputinik 2, lançado no mesmo ano, levou a bordo a cadela Laika, que teria perecido horas depois devido ao forte calor.
1958 Primeiro satélite artificial norte-americano, o Explorer.
1959 Nasa lançou o satélite meteorológico Television and Infrared Observation Satellite (Tiros) 1.
(Continua)
--- PAGE 138 ---
|
Segmentos tecnológicos e aplicações 683
(Continuação)
Ano Evento
1960 Nasa lançou o primeiro satélite de comunicações para AT&T (Bell Laboratory), Echo 1.
Nasa lançou o Telstar em parceria com a AT&T, com transmissão da primeira imagem televisiva entre Estados Unidos e Europa.
1962 Lei norte-americana dos satélites de comunicações (Communications Satellite Act), deu origem ao consórcio International
Telecommunications Satellite Organization (ITSO ou Intelsat).
Primeiro satélite em órbita geoestacionária, Synconm 3, parceria da Nasa com a Hughes, responsável pela transmissão
1964
da Olimpíada de Tóquio.
1965 Lançamento do Intelsat 1, da série de satélites de comunicações com grande sucesso comercial.
1969 Transmissão via satélite do homem chegando à Lua.
1973 Implantação do cabo submarino Bracan, ligando Recife às ilhas Canárias.
1975 Implementado o sistema de discagem direta internacional (DDI) no Brasil.
Inaugurada a Rede de Nacional de Estações Costeiras (Renec), para serviço móvel marítimo, e o Sistema Computadorizado
1978
de Retransmissão Automática de Mensagens (Sicram).
Implantação do Serviço Especializado de Comunicação de Dados (Transdata), servindo ao setor econômico (Interdata,
1980 Findata, Interbank) e de transporte (Airdata), além da instalação do cabo submarino ligando Fortaleza às Ilhas Virgens
e da operação do satélite consorciado Inmarsat.
Empresa Brasileira de Telecomunicações (Embratel) implanta o sistema de cabos submarinos Atlantis, ligando
1982
Brasil-Europa via Senegal (Dacar) e Portugal (Lagos).
Lançados os satélites de comunicações nacionais BrasilSat-A1 e BrasilSat-A2, ambos construídos pela canadense Spar
1985
Aerospace em parceria com a Hughes e operado pela Embratel (Star One), em forma cilíndrica para integração do território
1986
brasileiro por meio dos sinais de telefonia, telegrafia e televisão.
Embratel lança a Rede Nacional de Fibras Ópticas, ligando Rio de Janeiro e São Paulo, sendo que no ano subsequente
1993
foi conectada com os cabos submarinos América 1 (Estados Unidos) e Columbus 1 (Europa), via ilha de St. Thomas.
1994 Ativado o sistema Unisur de cabos submarinos em fibra óptica, ligando o Mercosul à rede mundial.
1994 Segunda geração de satélites brasileiros, BrasilSat B1 (70º de longitude oeste) e BrasilSat B2 (68º de longitude oeste),
1995 ambos construídos pela Hughes e testados em São José dos Campos.
1998 Continuação da segunda geração com o lançamento do Brasilsat B3, desenvolvido e testado pela Hughes.
Primeiro satélite heliossíncrono brasileiro em parceria com a China, China-Brazil Earth-Resources Satellite (CBERS);
1999 Instituto Nacional de Pesquisas Espaciais (Inpe) e China Academy of Space, com investimentos de US$ 300 milhões
(30% nacional e 70% chinês).
Lançamento do Brasilsat B4, desenvolvido e testado pela Hughes; três cabos transoceânicos pertencentes a espanhola
2000 Telefônica (SAm-1), subsidiária da Telecom Itália – Sparkle (Sistema Atlântico de cabeamento e Rede Latino-Americana –
SAC/LAN) e BTG Pactual (GlobeNet).
2003 Lançamento do CBERS-2 na China, enquanto, no mesmo ano, ocorreu o acidente na base de Alcantara no Maranhão.
2007 Lançamento do CBERS-2B previsto para substituir o CBERS-2 em 2009.
2013 Lançamento do satélite sino-brasileiro CBERS-3 na China, com falhas no lançamento.
2014 Lançamento do satélite CBERS-4.
Google investe R$ 500 milhões em três cabos submarinos e um centro de dados. O faturamento da Google no Brasil é
2016 estimado entre R$ 7 bilhões e R$ 8 bilhões, pois possui a metade da fatia de marketing da internet, correspondente a
cerca de R$ 15 bilhões em 2017 (Interative Advertising Bureau – IAB).
Lançamento na órbita 75ºW do Satélite Geoestacionário de Defesa e Comunicações Estratégicas (SGDC-1) em parceria com
2017
a Thales Alenia Space e sob coordenação do Ministério da Defesa no Brasil e a Telecomunicações Brasileiras S/A (Telebras).
2019 Último satélite da série sino-brasileira CBERS, modelo 4A, foi lançado em Taiyuan – China
2021 Satélite Amazônia 1, desenvolvido com tecnologia nacional e lançamento a partir de base Indiana.
Elaboração da autora.
--- PAGE 139 ---
|
684 Do paradoxo das habilidades à superinteligência pós-humana
A evolução tecnológica no mercado espacial permitiu a criação de novos
empreendimentos. A nova fronteira de exploração comercial do espaço inclui au-
mento no número de satélites de menor porte, orbitando em altitudes mais baixas.
As constelações de pequenos satélites, denominados cubsats, por terem o formato
cúbico, operam coordenadamente realizando funções semelhantes, por meio da
varredura contínua da superfície do planeta. Os cubsats pesam menos de 40 kg,
possuem câmeras com resolução de 3 m e sensores de rádio frequência, podendo
ser montados rapidamente com peças uniformes disponíveis no mercado e a custos
acessíveis. Futuramente, cerca de dois terços do mercado de serviços fornecidos
no segmento espacial deverão ser executados por essa nova geração de satélites.31
A preocupação que surge com o cenário que se avizinha é a possibilidade
de agravamento da poluição da órbita terrestre com a população crescente de
nanossatélites e com o lixo espacial. O Índice de Objetos Lançados ao Espaço,
elaborado pelo Escritório das Nações Unidas para Assuntos do Espaço Exterior
(United Nations Office for Outer Space Affairs – UNOOSA), indicou haver cerca
de 5 mil32 satélites orbitando o planeta, dos 8 mil lançados até o começo de 2019.
Contudo, apenas 1.957 estariam ativos, e os 60% restantes seriam lixo espacial. O
percentual de satélites ativos para comunicações aproxima-se dos 40%, enquanto
para observação da Terra seriam 36%, outros foram lançados como demonstra-
ção e desenvolvimento tecnológico (11%), posicionamento ou localização (7%),
observação científica do espaço (4%) e ciência da Terra (1%).
A rigor, o estudo deveria concentrar-se apenas nos satélites comerciais de
comunicações, mas o uso misto é bastante comum, correspondendo a 14% do
total. A participação corresponde a comercial (43%), governamental (27%), militar
(21%) e civil (7%), de acordo com as quatro categorias de utilização. Desde 2006,
a taxa de crescimento dos lançamentos vem aumentando, tendo chegado ao auge,
em termos absolutos, em 2017, com 453 satélites indo à orbita terrestre. Porém, o
maior crescimento ocorreu em 2013, devido aos cubsats, e a projeção aponta para
mais 2 mil satélites sendo lançados até 2030.
Os satélites geoestacionários são fundamentais para desenvolvimento de
telecomunicações.33 Vinte anos após a ideia ter sido concebida por Arthur C.
Clarke, o primeiro satélite comercial geoestacionário de comunicação, o Intelsat
I, foi lançado em abril de 1965. Em orbita circular equatorial de cerca de 36 mil
quilômetros acima do nível do mar, os satélites têm período igual ao da rotação
terrestre, daí permanecerem geoestacionários sobre o mesmo ponto do Equador
31. Disponível em: https://www.defesanet.com.br/defesa/constelacao-de-satelites-brasileira-para-os-desafios-nacionais/.
32. Seriam 4.987 satélites, no começo de 2019, aumento de 2,68% em relação a 2018. Disponível em: https://www.
pixalytics.com/satellites-orbiting-earth-2019/.
33. O conceito de satélite geoestacionário foi desenvolvido por Athur C. Clarke (1917-2008), quando escreveu o artigo
científico Can Rocket Stations Give Worldwide Radio Coverage?, publicado em 1945, pela revista Wireless World.
--- PAGE 140 ---
|
Segmentos tecnológicos e aplicações 685
terrestre, na órbita geossíncrona de Clarke. Até abril de 2010, cerca de 960 satélites
haviam sido lançados, segundo N2YO.34
O processo de limpeza das órbitas terrestres tem sido estudado por alguns
países, mas a perspectiva de implantação é de longo prazo, por volta de 2050. Em
2012, engenheiros suíços apresentaram o conceito de satélite-gari (CleanSpace
One), para captura de resíduos espaciais e incineração pela reentrada na atmosfera.
O satélite dotado de autopropulsão operaria a 28 mil quilômetros por hora, entre
600 km e 700 km de altitude. A princípio, a operação múltipla com reaproveita-
mento teria sido o principal objetivo da equipe de desenvolvedores do CleanSpace.
Um projeto alternativo, com propósito de gari tecnológico, vem do grupo
de universidades, o Gateway Earth Development Group (GEDG), que tem uma
proposta mais inteligente de reaproveitamento dos detritos espaciais. Contudo, a
missão implicaria colocação em órbita de uma estação de reciclagem de resíduos,
a Gateway Earth, até 2050. Outro projeto experimental, dessa vez liderado pela
Universidade de Surrey e Airbus, o RemoveDebris, captura dejetos com arpão e
traz o objeto para ser incinerado na reentrada de órbita terrestre.
Os riscos de colisão são o principal motor que move esses projetos, sendo
assim, a Agência Espacial Europeia lançou a primeira iniciativa-missão espacial
para remover os detritos das órbitas, em 2019, no âmbito do programa de segu-
rança operacional no espaço. O horizonte de implantação é 2025, portanto, um
avanço que envolve contratação por licitação de serviço com consórcio comercial
de startups e instalação de submercado aeroespacial. O objetivo é proteger a mul-
tiplicidade de mega constelações de satélites de baixa latência e grande cobertura
em telecomunicações implantadas em zona altamente congestionada.
O Conselho Ministerial dos Estados-Membros da Agência Espacial Euro-
peia (European Space Agency – ESA) concordou com a estratégia de provimento
futuro de serviço comercial para limpeza dos objetos de propriedade da agência.
O principal desafio é desenvolver tecnologia para evitar a criação de novos de-
tritos e remover os existentes. Os métodos de captura estão sendo analisados no
projeto Active Debris Removal in-Orbit Servicing (Adrios) para serem aplicados
no âmbito da missão ClearSpace-1. O primeiro alvo seria o módulo superior do
Vega Secondary Payload Adapter (Vespa), lançado em 2013, com mais de 100 kg
e localizado entre 600 mil e 800 mil quilômetros de altitude.35
A Nasa, por sua vez, arquivou o projeto do eliminador eletrodinâmico de
dejetos, ElectroDynamic Debris Eliminator (EDDE), cujo conceito inovador
consistia em empregar a energia solar para rotação e contraposição ao efeito do
34. N2YO, relação de satélites geossíncronos. Disponível em: https://www.n2yo.com/satellites/?c=10.
35. Disponível em: https://www.esa.int/Space_Safety/Clean_Space/ESA_commissions_world_s_first_space_debris_removal.
--- PAGE 141 ---
|
686 Do paradoxo das habilidades à superinteligência pós-humana
campo magnético da Terra. Portanto, a tecnologia envolve espaçonave sem uso
de propelente convencional para operação na ionosfera, somente a utilização de
propulsão eletrodinâmica.
FIGURA 16
Posicionamento SGDC-1 (2019)
Fonte: N2YO. Disponível em: https://www.n2yo.com/?s=42692. Acesso em: 24 jun. 2019.
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
5.1 Satélites para comunicação, observação e posicionamento global
A Universidade Politécnica do Estado da Califórnia foi responsável pela criação
do modelo conceitual cubsat, que tomou essa denominação por ser construído em
módulos cúbicos de 10 cm de aresta. Entre as vantagens que popularizaram o uso
dos satélites miniaturizados estão: as múltiplas funcionalidades; o rápido ciclo de
desenvolvimento; o baixo custo de produção; o consumo reduzido de energia; e
a baixa latência de comunicação entre estações em terra e dispositivos-usuários.
5.1.1 Satélite de comunicação
Como os projetos para internet rápida por comunicação satelital operam global-
mente, sejam eles com satélites convencionais ou inovadores, algumas precauções
devem ser tomadas com relação ao congestionamento no espaço. As zonas de
--- PAGE 142 ---
|
Segmentos tecnológicos e aplicações 687
órbitas terrestres de baixa e média altitudes são acessadas livremente pelos satéli-
tes, sem que existam quaisquer restrições aos limites de fronteiras territoriais dos
países. Sendo assim, a aglomeração de satélites no longo prazo, pertencentes a
empresas entrantes interessadas na prestação de serviços, pode prejudicar a ope-
ração. Portanto, é preciso atentar para o ordenamento e outorga de exploração,
estabelecido conforme a legislação de cada país. No caso do Brasil, a Lei Geral de
Comunicações (Lei no 9.472/1997) e a Resolução no 748/2021 tratam do direito
ao uso do recurso de órbita e radiofrequência.
Exemplos de empreendimentos de comunicação relevantes, alguns deles com
significativa quantidade de equipamentos envolvidos, foram descritos a seguir.
1) A OneWeb Satellites, joint venture entre a Airbus Defense and Space
(Tolousse) e a Oneweb, lançou os primeiros seis satélites da constelação
em 2019, a partir da plataforma de lançamento de Kourou, na Guiana
Francesa, a bordo de um foguete Soyuz, operado pela Arianespace. Os
planos da OneWeb tiveram início há cinco anos e incluem 2 mil satélites,
dos quais seiscentos em uma primeira fase, em parceria com a agência
espacial russa Roscosmos e com a empresa espacial Arianespace. A Hughes
é sócia da OneWeb e operadora de banda larga via satélite no Brasil.
2) O ambicioso plano Starlink da SpaceX incluirá 12 mil satélites, formando
um manto de cobertura satelital sobre a Terra. Essa nova geração de sa-
télites possui algumas características diferentes daquelas que hoje servem
à internet. Os sessenta primeiros foram lançados em maio de 2019. Os
novos terminais transmitirão sinais de WiFi, LTE e 3G (uplink) para os
satélites que orbitam de um polo a outro do planeta, em uma altura de
1.200 km que, por sua vez, retransmitirão (downlink) para estações ter-
restres. Enquanto os satélites convencionais estão localizados a 45 mil km
de altitude, ao longo do equador, o que torna a comunicação mais lenta.
--- PAGE 143 ---
|
688 Do paradoxo das habilidades à superinteligência pós-humana
FIGURA 17
Imagem de lançamento da frota de satélites Starlink (2019)
Fonte: Wakka (2019a). Disponível em: https://canaltech.com.br/espaco/spacex-lanca-com-sucesso-primeiros-60-satelites-do-
-projeto-starlink-140013/.
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
3) Blue Origin – Amazon, em parceria com Arianespace e United Launch
Alliance, colaboram com o projeto Kuiper que pretende lançar pouco
mais de 3 mil satélites de internet (3.236).
4) Earth Now (Softbank, Airbus e MS) é o projeto de quinhentos satélites
estacionários ao redor do planeta, cujo serviço inclui captura de imagens
com grande aproximação da terra em tempo real.
5) Projeto Planet da Engesat, com mais de oitenta satélites Dove equipados
com quatro bandas de câmeras espectrais e resolução de três metros, ser-
vindo setores comerciais como o agrobusiness com informações precisas
sobre o clima e recursos hídricos.
6) Iridium, a constelação tem 66 satélites ativos de comunicações de dados
e voz. A cobertura da rede Iridium inclui os polos, os oceanos e as vias
aéreas, completando a órbita terrestre em aproximadamente 100 minutos.
7) Globalstar, comercializa o serviço de comunicação de dados e voz para
120 países, inclusive o Brasil, com cerca de cinquenta satélites ativos
operando em oito planos de órbita.
8) Inmarsat, maior companhia de satélite do Reino Unido com cerca de
quinze satélites ativos, fabricados ao longo de sete séries.
--- PAGE 144 ---
|
Segmentos tecnológicos e aplicações 689
9) Yahsat, opera também no Brasil e pertence aos Emirados Árabes, com
dois satélites, cobertura de banda larga em dez estados e cerca de seis-
centas cidades.
5.1.2 Satélite de observação da terra
A implantação do sistema Global Monitoring for Environment and Security
(GMES), também conhecido como programa Copernicus de observação do
planeta, foi liderada pela CE em cooperação com a ESA. O sistema é constituído
por cinco famílias de satélites-sentinelas encarregados de monitorar a atmosfera,
o clima, os ambientes marítimo e terrestre, assim como os eventos de segurança e
emergência. O Copernicus substituiu o programa European Envisat (2002-2012),
tendo iniciado a primeira fase de pré-operação entre 2008-2010, a operação inicial
entre 2011-2013 e a plena operação a partir de 2014.
Existe enorme expectativa a respeito do aperfeiçoamento das tecnologias
utilizadas nos minissatélites, principalmente no que se refere à maior precisão na
geolocalização, à qualidade das imagens capturadas e ao adensamento na varre-
dura de cobertura do planeta. No entanto, os desafios desses equipamentos estão
presentes em funcionalidades básicas como na potência do sistema de propulsão
miniaturizado, na vida útil mais encurtada e na baixa capacidade de armazenamento
de energia. Ao passo que os satélites convencionais são mais robustos, contam com
sofisticados algoritmos de localização e transmitem sinais com qualidade.
5.1.3 Satélite de localização
Os Sistemas Globais de Navegação por Satélite (GNSS) constituem constelações
de satélites convencionais cuja função é a determinação do posicionamento no
ar, terra ou mar. O uso das constelações de satélites GNSS inclui levantamentos
topográficos e geodésicos, agrimensura, navegação terrestre, marítima e aérea.
Algumas perspectivas para evolução dos GNSS, segundo Hecker et al. (2018),
estão descritas a seguir.
1) O sistema europeu de navegação por satélite Galileo (2002-2016) provê
quatro tipos de serviços: aberto-gratuito, comercial, público-regulado
e busca-salvamento. Atualmente, o sistema conta com 26 satélites em
órbita da Terra. O programa de desenvolvimento foca nas tecnologias de
ampliação de espaço e superposição regional (Space-Based Augmentation
Systems e European Geostationary Navigation Overlay Service), para
aviação civil, navegação marítima e transporte ferroviário.
2) Concebido na década de 1970 como sistema militar, o American GPS
tem sido modernizado com sinais adicionais de navegação em múltiplas
bandas de frequência. Com cerca de trinta satélites em operação, o GPS é
--- PAGE 145 ---
|
690 Do paradoxo das habilidades à superinteligência pós-humana
o mais popular sistema de navegação operando por transmissão de sinais
de rádio. Uma nova geração de satélites está sendo lançada e deve operar
paralelamente ao sistema legado até que possa ocorrer completa substituição.
3) Contraparte russa para o GPS e o Galileo, o sistema Glonass (1976-1993),
com 25 satélites ativos, também tem abrangência global e provê sinais
de acesso público em duas frequências diferentes para mitigar efeitos da
ionosfera. Os planos futuros para o sistema Glonass incluem introduzir
sinais usando acesso múltiplo de divisão de código para multiplexagem
na transmissão de sinais na mesma frequência para toda a constelação
de satélites (interoperabilidade).
4) Com 46 satélites ativos, o projeto do sistema BeiDou (2011) foi desen-
volvido pela China em três fases sequenciais, a última fase prevê o serviço
GNSS global com ênfase em ambientes urbanos na região Ásia-Pacífico.
A constelação opera em três tipos diferentes de órbita: Medium-Earth
Orbit (MEO), Inclined Geo-Synchronous Orbit (IGSO) e Geostatio-
nary Orbit (GEO).
5) A Índia desenvolve seus próprios satélites desde 1975, quando o Arya-
bhata foi lançado da base soviética de Kapustin Yar. A partir de 1980,
o país passou a utilizar seu próprio veículo lançador de satélites (VLS)
para lançar a série de satélites do Sistema Regional Indiano de Navegação
por Satélite (IRNSS).
5.2 Satélites orbitando o território brasileiro
Os serviços de telecomunicações por satélite no Brasil tiveram início com os satélites
brasileiros Brasilsat A1 e A2, ambos desenvolvidos pela empresa canadense Spar
Aerospace em parceria com a Hughes, contratados e operados pela então estatal
Embratel, atual Star One. O primeiro satélite, o Brasilsat 1, foi lançado na órbita
de 65º W, em 1985, pelo foguete Ariane 3, no Centro Espacial de Kourou na
Guiana Francesa; o segundo, o Brasilsat 2, foi lançado um ano depois, em 1986,
em órbita (70º W), também para serviços de telecomunicações.
A segunda série de Brasilsat B1 e B2 veio em 1994 e 1995, ambos desenvolvidos
pela Hughes, testados no Inpe, em São José dos Campos, e operados pela Embratel
até 2000, posteriormente pela Star One. Os satélites Brasilsat B3 (63º W) e B4
(91º W) foram construídos e testados pela Hughes, e lançados em 1998 e 2000,
respectivamente. Os satélites Basilsat das séries A e B foram desativados após uma
vida útil de treze a vinte anos. A substituição gradativa ocorreu pelos satélites da
série Star One C e D, com operação da empresa do mesmo nome (antiga Embra-
tel) e fabricantes diversas. Os sete satélites da série Star One continuam operantes.
--- PAGE 146 ---
|
Segmentos tecnológicos e aplicações 691
Em 1999, o primeiro satélite heliossíncrono da família sino-brasileiro foi
desenvolvido pelo China Academy of Space em parceria com o Inpe. O CBERS
foi projetado para fazer a observação da Terra, com cobertura do planeta em 26
dias. Os investimentos foram de cerca de US$ 300 milhões, com participação da
China (70%) e Brasil (30%). O primeiro lançamento CBERS-1 ocorreu em 1999
com foguete de longa marcha chinês, a partir da base de Taiyuan, na China. Os dois
lançamentos de satélites heliossíncronos CBERS-2, em 2003, e CBERS-2B, em
2007, ocorreram também na China. No terceiro satélite CBERS-3, a participação
nos investimentos brasileiros aumentou e o lançamento ocorreu em 2013, mas o
projeto ficou prejudicado por falha no motor do foguete. O CBERS-4, o último
da série, foi lançado em 2014, e o CBERS-4A foi lançado em 2019.
O Satélite Geoestacionário de Defesa e Comunicações Estratégicas – SGDC-1,
feito em parceria com a Visiona e desenvolvido pela Thales Alenia Space e pela
ArianeSpace, sob coordenação do Ministério da Defesa no Brasil e da Telebras, teve
lançamento em maio de 2017 na base francesa de Kourou. O SGDC-1 tem capa-
cidade para 58 Gbps, para fins civis (banda Ka), e altura de 35 mil quilômetros. A
decisão foi pela aquisição do satélite em compartilhamento para uso defesa-comercial.
O preço mínimo de exploração comercial foi avaliado em cerca de R$ 2 bilhões,
algo equivalente ao gasto com o projeto. Contudo, o leilão planejado para licitação
foi frustrado, certame deserto. Consequentemente, a Telebras resolveu estabelecer
uma parceria com a americana Viasat, de forma a viabilizar o serviço de internet
de banda larga nas zonas remotas e rurais.
Como mencionado, a capacidade satelital para uso comercial no Brasil é
suprida por meio de licitações, e boa parte dos direitos de exploração de satélite
brasileiro foram outorgados em 2011, 2014 e 2015. Esses satélites são conside-
rados brasileiros, pois utilizam os recursos de órbita e espectro notificados em
nome do Brasil na UIT. A configuração, conforme a figura 18, mostra dezessete
satélites, sendo as operadoras: i) Telesat Brasil (Telstar 19 Vantage); ii) Hispamar
Satélites (Hispasat, Amazonas 2, 3 e 5); iii) YAH telecomunicações (Al YAH 3);
iv) Star One (BrasilSat B3, B4, StarOne C2, C3, C4 e D1); v) Claro (StarOne
C1); vi) EchoStar Telecomunicações (Echostar XXIII); vii) Eutelsat do Brasil
(Eutelsat 65 West A); viii) SES DTH do Brasil (SES-14); e ix) Telebras (SGDC).
O satélite Estrela do Sul 2, da Telesat Brasil, programado para sair de operação,
será substituído pelo TelStar Vantage 19.
Contudo, dados da Anatel (2018, p. 39) apontam 54 satélites autorizados
a prover capacidade satelital sobre território brasileiro, tendo cerca de trinta
operadores, na maior parte estrangeiros com 37 satélites. Segundo a Associação
Brasileira das Empresas de Telecomunicações por Satélite (Abrasat), existem seis
operadoras aptas a fornecer a banda Ka no Brasil: Eutelsat, Star One, Embratel,
Telesat, Hispamar, Yahsat.
--- PAGE 147 ---
|
692 Do paradoxo das habilidades à superinteligência pós-humana
FIGURA 18
Situação futura de utilização do arco orbital
Fonte: Anatel (2018).
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
A evolução da capacidade das faixas de frequência tem sido crescente, com
a banda Ka tendo frequência mais alta do que as bandas C e Ku. Isso implica
ampliação significativa do fluxo de dados com maior velocidade e menor custo.
A banda Ka também reduz significativamente as instabilidades que normalmente
ocorrem em transmissões via satélite.
O Inpe lançou a iniciativa Missão Amazônia, que prevê a construção de três
satélites de médio porte (até 500 kg), desenvolvidos para observação da Terra,
com tecnologia nacional desde a concepção até a operação. O primeiro da série, o
Amazônia-1, foi lançado em fevereiro de 2021, a partir de Satish Dhawan Space
Center, na Índia. Em órbita de 750 km, o satélite passa pelo país a cada cinco
dias e monitora a floresta amazônica, além de auxiliar na previsão de colheitas
agrícolas e no gerenciamento de recursos hídricos e marinhos. A engenharia para
construção dos satélites da missão, estabilizados em três eixos, é considerada de alta
complexidade pelos especialistas, pois pode alterar a órbita e a posição em relação
ao planeta de forma a obter uma melhor observação.
O Estado brasileiro tem se planejado para lançar a frota de nanossatélites
nacional. As aplicações seriam múltiplas, começando pelo monitoramento do
desmatamento florestal, passando pela vigilância da navegação clandestina na plata-
forma continental e até mesmo fazendo o levantamento de recursos naturais. Para
isso, os planos incluem o desenvolvimento do veículo lançador de microssatélites
(VLM-1) para cubesats e nanossatélites com carga útil de até 150 kg, em parceria
com o DLR. A Visiona Tecnologia Espacial, joint venture entre a Embraer Defesa
& Segurança e a Telebras, criada em 2012, anunciou o programa do primeiro
microssatélite projetado pela indústria nacional em 2018. O satélite VCUB-1 é
--- PAGE 148 ---
|
Segmentos tecnológicos e aplicações 693
navegado pelo sistema de controle de órbita e altitude construído no país e está
programado para lançamento em 2023.
Também existe a iniciativa do projeto Constelação de Nanossatélites para Coleta
de Dados Ambientais (Cona Sat) por parte da Agência Espacial Brasileira (AEB).
Fruto da parceria do Inpe com universidades brasileiras (por exemplo, federais de
Santa Catarina, Santa Maria e Rio Grande do Norte), cujo objetivo é a capacitação
de RH para construção de nanossatélites especializados em monitoramento.
5.3 Estações espaciais internacionais
O Programa Espacial Soviético da Rússia foi pioneiro no mundo, tendo iniciado os
projetos na década 1930. Esse programa é colecionador de cerca de quarenta feitos
precursores, entre os quais os mais importantes são: i) o primeiro lançamento de
satélite artificial, o Sputnik-1, que ocorreu em 1957, e, no mesmo ano, foi a vez
do Sputnik-2, que levou a bordo a cadela Laika; ii) a primeira missão tripulada,
com o cosmonauta Yuri Gagarin, a bordo da nave Vostok-1, em órbita no ano de
1961, e com Valentina Tereshkova, na Vostok-6, em 1963; iii) o primeiro rover
robótico na Lua, Lunokhod-1, e a primeira amostra de solo lunar com a sonda
Luna-16, em 1970; iv) a primeira estação espacial, Salyut-1, em 1971, com a pri-
meira tripulação lançada pelo Soyuz-10; e v) a primeira estação espacial modular
MIR, a 400 km em órbita da Terra, tripulada permanentemente enquanto montada
pelos soviéticos, no período 1986-1996, posteriormente, herdada pela Rússia até
2001. As sondas soviéticas foram à Lua (1959), a Vênus (1961) e a Marte (1962),
e o ônibus espacial Buran foi lançado do Cosmódromo de Baikonur em 1988.
O primeiro Tratado do Espaço Sideral, assinado por Estados Unidos, União
das Repúblicas Socialistas Soviéticas (URSS) e Reino Unido, ocorreu em 1967 e
perdura até os dias de hoje, mesmo décadas depois da dissolução da antiga União
Soviética, em 1991. Esse ânimo de cooperação permitiu que a estação MIR legasse
seus módulos centrais à atual Estação Espacial Internacional (ISS), em 2001, e a
desativação gradual da ISS está programada para ocorrer a partir de 2025, quando
os russos começarão a desembarcar do projeto. Nesse intervalo de tempo, cerca
de duzentos astronautas de dezenove nacionalidades diferentes visitaram a ISS
para realizar missões diversas, desde aquelas de cunho científico em ambiente de
microgravidade até as operações de ampliação e manutenção das instalações.
--- PAGE 149 ---
|
694 Do paradoxo das habilidades à superinteligência pós-humana
FIGURA 19
Diagrama da estação MIR (Paz no Mundo)
Fonte: Wikipédia. Disponível em: https://pt.wikipedia.org/wiki/Mir.
Obs.: Ilustração cujos leiaute e textos não puderam ser padronizados e revisados em virtude das condições técnicas dos
originais (nota do Editorial).
Os Estados Unidos, motivados pelas conquistas da antiga URSS, tiveram
de acompanhar os avanços tecnológicos do oponente ideológico, dando origem à
corrida espacial na segunda metade do século XX. Para isso, três programas foram
fundamentais: i) o Programa Mercury, com o objetivo de colocar um astronauta
em órbita, Allan Shepard, em 1961; ii) o Programa Gemini, que colocou dois
tripulantes em órbita, Grisson e Young, em 1965; e iii) o Programa Apollo, que
levou três astronautas à Lua, na nave Apollo 11, lançada pelo Saturno V e tripu-
lada com Armstrong, Collins e Aldrin, em 1969. Houve dezoito missões com a
nave Apollo, seis delas levaram doze astronautas à Lua e a última, a Apollo 18, foi
realizada em conjunto com Programa Soyuz, em 1975.
--- PAGE 150 ---
|
Segmentos tecnológicos e aplicações 695
O SkyLab foi o primeiro laboratório espacial colocado em órbita (435 km)
pelos Estados Unidos, em 1973, mas teve vida efêmera, reentrando na atmosfera
em 1979. Em que pese o fato de a vida útil da estação espacial ter sido estimada em
mais de quinze anos, houve problemas de ordem estrutural com os painéis solares e
com a fuselagem, provocados por vibrações imprevistas no projeto original. Apesar
disso, a estação chegou a receber três tripulações. Os americanos continuaram em
missão conjunta a tarefa de expansão da ISS, com a ajuda de outros países.
Em 1981, a Nasa passou a operar o ônibus espacial Space Shuttle, reutilizá-
vel, ao qual seguiram-se Columbia, Challenger, Discovery, Atlantis e Endeavour,
desenvolvidos principalmente para construção da ISS. Outras conquistas da
exploração espacial americana incluem: i) as sondas norte americanas (Mariner,
Pioneer, Voyager e Viking), que chegaram ao espaço profundo depois de passarem
por Júpiter (1973), Marte (1976) e Saturno (1979); ii) o telescópio espacial Hub-
ble, colocado em órbita em 1990; e iii) a nave Mars Global Surveyor, que chegou
na órbita de Marte em 1997 e conseguiu pousar dois rovers robóticos em missões
posteriores, o Opportunity e o Spirit.
O novíssimo Programa Artemis está sendo preparado para realizar quarenta
missões, visando à presença permanente na Lua, até 2028. A começar pela missão
Artemis I, com voo orbital em volta do satélite natural previsto inicialmente para
ser realizado em novembro de 2021; a Artemis II, tripulada em 2023; e a Artemis
III, com pouso dos astronautas na superfície lunar, após estada intermediária na
estação orbital. Contudo, a missão mais esperada é a chegada a Marte por volta
de 2030, que vem sendo realizada em parceria com a iniciativa privada, como tem
sido habitual.
O Programa Tecnológico de Exploração do Espaço da República Popular da China
teve início em 1956, com a cooperação da antiga URSS. Porém, a partir da década
de 1960, o programa chinês seguiu carreira solo, tornando-se exitoso com o primeiro
satélite da série colocado em órbita em 1970. O projeto para missões espaciais tripu-
ladas começou em 1968, mas a falta de recursos adiou o primeiro voo espacial chinês
para 2003, quando o Programa Shenzhou-5 levou Yang Liwei a um voo com duração
de aproximadamente um dia. Dois anos depois, em 2005, foram dois taikonautas.
Participaram das missões a Administração Espacial Nacional da China e a Corporação
de Ciência e Tecnologia Aeroespacial da China (Casc).
Os planos chineses para exploração da Lua foram anunciados em 2004 e consis-
tiam na implementação de módulo lunar, o que permitiria voo orbital e alunissagem
entre 2007 e 2020, como ocorreu de fato. Em 2005, foram divulgados os planos
de viagens não tripuladas a Marte, com a descida ao planeta sendo prevista para a
próxima década. Em 2020, a China visitou o Oceanus Procellarum no lado visível
da Lua e pousou o seu primeiro rover (Zhurong), com dispositivos robóticos, em
--- PAGE 151 ---
|
696 Do paradoxo das habilidades à superinteligência pós-humana
solo marciano na missão Tianwen-1. O país é o segundo a pousar em Marte, mas
é o sexto a levar dispositivos ao planeta, pois, além da Rússia, da União Europeia e
dos Estados Unidos, também a Arábia Saudita e a Índia orbitaram aquele planeta.
Em abril de 2021, a estação espacial chinesa começou a ser construída com
a colocação em órbita do primeiro módulo central Tianhe (harmonia dos céus),
lançado pelo foguete de Longa Marcha 5B na base de WenChang, Ilha de Hainan,
no sul da China. Em junho do mesmo ano, três taikonautas chineses seguiram em
missão histórica a bordo da nave Shenzhou-12 (embarcação divina) para ampliar
as instalações da estação espacial que deverá estar operacional em 2022.
5.4 Tecnologia para propulsão no espaço
Em matéria de propulsão, a empresa norte-americana SpaceX, criada em 2002, inovou
com o reaproveitamento de foguetes que retornaram à Terra, em seu primeiro pouso
propulsivo ocorrido em 2015. A trajetória arrojada da empresa tem financiamento
da iniciativa privada para fabricar a série de lançadores Falcon 1, Falcon 9 (em três
versões), Falcon Heavy (com três Falcon 9), com motores Merlin e a família de naves
espaciais Dragon. A empresa fornece o serviço de transporte de cargas úteis para
diferentes níveis de órbita da Terra e para a ISS. A SpaceX tem planos ainda mais
ambiciosos, como a viagem tripulada, de ida e volta, ao redor da Terra, da Lua e de
Marte. Para isso, está desenvolvendo o projeto SuperHeavy-StarShip, com potência
superior ao mais poderoso foguete construído pela Nasa para as missões Apollo à Lua,
o Saturn 5. O propulsor está sendo planejado para três motores Raptor, construídos
pela SpaceX na filial do Texas.
Os nichos de negócio de serviços de transporte e de telecomunicações são
comercializados diretamente com o governo e as empresas privadas do ramo. Em
2018, a SpaceX colocou em órbita o satélite espanhol PAZ para monitoramento
de embarcações marítimas. Outros dois satélites menores, os Microsats 2a e 2b,
foram lançados pelo Falcon 9, sendo os primeiros na formação de rede espacial
para disseminação de internet rápida. A partida ocorreu da base militar de Van-
denberg, na Califórnia, com autorização da Comissão Federal de Comunicações
(FCC) dos Estados Unidos.
Em 24 de maio de 2019, os sessenta primeiros satélites do conjunto de 12 mil
aparelhos do projeto Starlink partiram de Cabo Canaveral, na Flórida. O objetivo
da missão é levar internet de banda larga (1Gbps) à população global. Para isso, foi
obtida permissão da FCC para o lançamento em duas levas, com 4.409 e 7.518
satélites. Em 25 de junho 2019, a SpaceX realizou seu projeto mais ambicioso,
com o foguete lançador Falcon Heavy III, que levou uma carga valiosa ao espaço:
i) os 24 satélites militares em três órbitas separadas; ii) os satélites de observação
--- PAGE 152 ---
|
Segmentos tecnológicos e aplicações 697
da Administração Nacional Oceânica e Atmosférica (NOAA); iii) o propulsor
verde da Nasa; iv) o relógio atômico Deep Space Atomic Clock; e v) a vela solar.
Nessa missão, o buster principal com o motor falhou ao tentar retornar à Terra,
mas os outros dois busters de propulsão foram reaproveitados em pouso vertical.
Sobre o combustível verde da Nasa, nitrato de hidroxilamônio, este vem a subs-
tituir a hidrasina (propulsor de motor de satélites e emissor de gases tóxicos) sem
emitir gases nocivos. A missão Green Propellant Infusion Mission (GPIM) inclui
o veículo movido a combustível verde, desenvolvido pela Força Aérea dos Estados
Unidos. Quanto à vela solar Light Sail 2, trata-se de uma tecnologia concebida por
Carl Sagan na década de 1970, promovida, desde então, pela Sociedade Planetária,
que lançou o Light Sail 1 em 2015 para impulsionar a nave sem necessidade de
combustível, apenas à energia solar. A vela é constituída de materiais refletores
muito finos que impulsionam o veículo após sua abertura no espaço.
A alta tecnologia desenvolvida pela SpaceX permite que a empresa colabore
com a Nasa nas missões tripuladas. Em 30 de maio de 2020, a empresa lançou a
nave Crew Dragon com dois astronautas norteamericanos em direção à ISS, feito
que interrompe o longo período de dez anos sem viagens espaciais tripuladas nos
Estados Unidos. Assim como a Boeing, a SpaceX tinha contrato com a Nasa para
transporte de carga desde 2014. O negócio permitiu que a Nasa diminuísse sua
dependência com relação à Rússia, que fornecia o serviço desde 2011, por meio
da nave Soyuz. A Crew Dragon está equipada com painel de controle sensível ao
toque, sistema de acoplamento automatizado e propulsores capazes de realizar
manobras no espaço.
A Índia, por sua vez, detém o recorde de lançamentos, com 104 satélites
em um único foguete. O recorde anterior era da Rússia, com lançamento de 39
satélites, em 2014. O lançador Polar Satellite Launch Vehicle (PSLV) saiu da
plataforma de Sriharikota, no começo de 2017, com 1,3 t de carga, correspon-
dente a um satélite de observação da Terra de 700 kg e 103 nanosatélites com
600 kg. O PSLV acumula cerca de quarenta missões, com apenas um acidente em
1993. O programa espacial indiano teve origem na década de 1960 e seu maior
símbolo de conquista ao espaço foi a sonda Mangalyann a Marte, a Mars Orbit
Mission (MOM). O Instituto de Estudos e Análises de Defesa de Nova Déli tem
papel importante no mercado espacial mundial e o Centro Nacional de Estudos
Espaciais (CNES) da França tem filial na Índia.
A propulsão no espaço desperta grande interesse científico, devido aos avan-
ços nas pesquisas tecnológicas relacionadas à movimentação eletromagnética.
Apesar de ainda cercadas de sigilo, as experimentações e demonstrações práticas
vêm ocorrendo, assim como a publicação de artigos científicos e o depósito de
patentes, que podem ser indicativas de que a factibilidade prática estaria próxima
--- PAGE 153 ---
|
698 Do paradoxo das habilidades à superinteligência pós-humana
ou, eventualmente, já teria ocorrido. A empresa Satellite Propulsion Research
Ltd. (SPR), fundada pelo britânico Roger Shawyer, trabalha na tecnologia de
micro-ondas conversora de energia elétrica em impulso, sem uso de combustível
propelente no processo de conversão. O empuxo é produzido pela amplificação
da pressão de radiação de onda eletromagnética propagada através de plataforma
ressonante direcionadora das ondas. O Projeto EM Drive existe desde o século
passado e veio a público em 2000.
A corrida espacial deverá contar com o turismo espacial para cobertura
parcial de custos da exploração a outros planetas. Além disso, as startups de
tecnologia espacial Blue Origin (Amazon), Virgin Galactic (Virgin Airlines)
e SpaceX levantaram financiamentos de US$ 5,5 bilhões com intuito de levar
adiante a aventura. A Blue Origin realizou a primeira viagem em 20 de julho de
2021, tendo leiloado um lugar na nave New Shepard por US$ 28 milhões para
a viagem ao espaço. A Virgin Galactic começou a vender seiscentos ingressos
com preços entre US$ 200 mil e US$ 250 mil para viagens que começam em
2022. Na Crew Dragon, o assento vale US$ 55 milhões, com estadia de US$ 6,8
milhões por dia na estação espacial. O mercado de turismo espacial foi avaliado
pelo Goldman Sachs em US$ 3 bilhões a.a. até 2030.
No Brasil, a primeira Missão Espacial Completa Brasileira (MECB) foi
aprovada em 1979 e contemplava todas as etapas de fabricação de quatro satélites,
o veículo lançador e a infraestrutura de lançamento. O desenvolvimento da série
de satélites de coleta de dados e sensoriamento remoto coube ao Inpe. Em 1993,
foi lançado o Satélite de Coleta de Dados (SCD-1) da base de Cabo Canaveral,
na Flórida. O SCD-2 foi lançado em 1998, mesmo ano em que foi criada a AEB.
Houve três tentativas de lançamento do VLS no país, em 1997, 1999 e 2003,
sendo que nesta última ocorreu o acidente que fez colapsar a base de Alcântara,
vitimando o corpo técnico responsável.
Atualmente, a AEB conta com orçamento anual de cerca de R$ 100 milhões,
o que a obriga a formar parcerias com o setor privado. Os planos da AEB incluem:
i) o Centro de Lançamento em Alcântara, programado para servir de plataforma
de lançamento para satélites pequenos de baixa órbita, com espaço-porto para o
mundo; ii) a participação no Programa Artemis, da Nasa,36 assinado em 2021,
que objetiva o desenvolvimento de pequenos robôs; iii) o Projeto Constelação
Catarina, que envolve a construção de nanossatélites de menor custo e peso de 10
kg; e iv) veículos aéreos e drones em desenvolvimento junto à Empresa Brasileira
de Aeronáutica S/A (Embraer).
36. Os Acordos Artemis estabelecem dez princípios que garantem a exploração espacial conjunta para fins pacíficos
entre as nações participantes (por exemplo, Coreia do Sul, Japão, Canadá, Austrália, Nova Zelândia, Reino Unido, Itália,
Luxemburgo, Ucrânia, Países Árabes Unidos).
--- PAGE 154 ---
|
Segmentos tecnológicos e aplicações 699
Emendas parlamentares ao Projeto de Lei Orçamentária de 2022 estão sen-
do defendidas pela AEB junto ao Congresso Nacional e incluem: i) capacitação
de RH; ii) veículo lançador de microssatélite; iii) foguetes de treinamento; e iv)
infraestrutura e desenvolvimento de sistemas espaciais. A AEB conta ainda com
investimentos de risco do setor privado, os quais podem viabilizar o projeto para
colocação em órbita de treze nanossatélites entre 2022 e 2023. As áreas-foco desses
equipamentos são agricultura de precisão, comunicação e prevenção de desastres
naturais. A parceria é ampla e envolve a Agência Nacional de Águas (ANA), a
Agência Nacional de Energia Elétrica (Aneel), a Universidade Federal de Santa
Catarina (UFSC), as indústrias locais e o Serviço Social da Indústria (Sesi).
6 SEGURANÇA CIBERNÉTICA
Sistemas cada vez mais sofisticados tendem a tornar a cibersegurança inadministrável
à medida que o número de aplicações IoT, bigdata e IA/ML/DL avançam. Sendo
assim, é crucial manter em perspectiva o potencial oferecido por algumas dessas
ferramentas para gerenciar inteligentemente a segurança cibernética. A pesquisa
de métodos e técnicas de IA dirigidas para a segurança autônoma dos sistemas
é uma área promissora em pleno desenvolvimento e que, portanto, precisa ser
acompanhada com bastante atenção.
6.1 Conceitos
1) Segurança cibernética (cybersecurity) – tecnologia que assegura informação e
comunicação fidedignas, especialmente considerando as características da IoT,
com ciclos longos de vida desde a concepção até o descarte dos dispositivos.
2) Segurança por definição (security by design) – adoção de medidas de
segurança incorporadas em todas as etapas do ciclo de vida do sistema
desde a concepção ou projeto de desenvolvimento do produto, software
ou hardware, derivados de análise de risco em aplicações diversas.
3) Segurança e criptografia embarcada (embedded security) – proteção dos
componentes ou dispositivos do sistema com tecnologia adequada, es-
pecialmente para ambientes com restrição de capacidade computacional
e energética, que visa à proteção de aplicações e dados sensíveis.
4) Segurança e acreditação de informação (information accreditation) – avaliação
prática das tecnologias que garantem a confiabilidade dos registros e assinatu-
ras digitais, prevendo as medidas a serem tomadas em caso de contingência.
--- PAGE 155 ---
|
700 Do paradoxo das habilidades à superinteligência pós-humana
6.2 Problemática associada à IA
Diante do caráter evolutivo e inovador das ferramentas de TICs, a governança da
informação e a proteção de dados devem ser reforçadas. Paradoxalmente, as novas
tecnologias podem requerer ainda maior equilíbrio e discernimento para compre-
ender que ao mesmo tempo que as inovações oferecem oportunidade para ampliar
o controle, também podem representar novos desafios e ameaças. O depoimento
de Oliver Thereaux, do Open Data Institute, ao Parlamento do Reino Unido mos-
trou bem o dilema quando confirmou que a IA é particularmente problemática
pela extrema eficiência na re-identificação de pessoas; portanto, na contramão da
legislação que determina a privacidade dos dados ou a despersonalização.
Reforçando o alerta, o artigo de Amodei et al. (2016), denominado Problemas
Concretos de Segurança em IA,37 apresenta potenciais impactos não intencionais da
tecnologia de ML, definidos amplamente como “comportamento nocivo”, que emer-
ge de projetos mal elaborados de sistemas de IA. Cinco problemas práticos de risco
de acidente foram investigados e classificados em três categorias. O detalhamento a
seguir é demasiadamente técnico e sugere um salto para o próximo tópico. Contudo,
foi mantido com o objetivo de ampliar a compreensão de nuances envolvidas no
desenvolvimento de ML e aprendizagem por reforço (reinforcement learning – RL).
1) Erro na função objetiva (“evitando efeitos colaterais negativos” e “evitando
recompensa pela evasão”) – a especificação incorreta da função objetiva
conduz a resultados prejudiciais, mesmo em condições de aprendizado
perfeito e massiva disponibilidade de dados. Dois mecanismos são es-
senciais na estratégia de como evitar os erros:
a) em “efeitos colaterais negativos”, o projetista especifica uma fun-
ção objetiva que se concentra em realizar uma tarefa específica no
ambiente, mas ignora outros aspectos potencialmente relevantes,
manifestando indiferença com variáveis ambientais que realmente
podem ser prejudiciais; e
b) em “recompensa pela evasão”, a função objetiva admite alguma
solução “fácil” inteligente para maximizá-la formalmente, mas
perverte o espírito da intenção do projetista.
2) Dispêndio para avaliar funções objetivas com frequência (“supervisão
escalável”) – neste caso, o projetista está inseguro com relação à correção
da função objetiva e pode avaliá-la consultando um especialista, mas é
dispendioso fazê-lo com frequência, levando a más extrapolações com
base em amostras limitadas.
37. Em inglês, Concrete Problems in AI Safety.
--- PAGE 156 ---
|
Segmentos tecnológicos e aplicações 701
3) Comportamento indesejável durante o processo de aprendizagem (“ex-
ploração segura” e “alterações distributivas”) – o projetista pode ter espe-
cificado a função objetiva corretamente, de forma que levaria o sistema
a apresentar o comportamento crível. Contudo, a tomada de decisão é
prejudicada por insuficiência de dados, falta de tutoria para treinamento
ou insuficiência na expressão do modelo. A “exploração segura” discute
como assegurar ações exploratórias de reforço de aprendizado, sem
consequências negativas ou irrecuperáveis de orçamento e cronograma.
As “alterações de distribuição” discutem como evitar que sistemas ML
tomem decisões imprevisíveis e sorrateiras quando são introduzidos dados
muito diferentes daqueles usados em treinamento.
Os autores utilizaram a ideia de um robô fictício, dedicado à limpeza do am-
biente, denominado “robô limpador”, para ilustrar o que pode estar acontecendo
em cada uma das situações apresentadas anteriormente, conforme a seguir descrito.
1) Evitando efeitos colaterais negativos: como garantir que o robô limpador
não irá perturbar o ambiente de forma negativa para atingir seus obje-
tivos, como derrubar um vaso devido à rapidez da limpeza? Especificar
manualmente que o robô não deve incomodar resolveria?
2) Evitando a recompensa pela evasão: como garantir que o robô limpador
não jogue com a função de recompensa? Por exemplo, se a recompensa
do robô é alcançar um ambiente livre de sujeiras, ele pode desabilitar sua
visão para que ele não encontre qualquer bagunça, ou encobrir a visão
com materiais não transparentes, ou simplesmente esconder quando os
seres humanos estiverem presentes.
3) Supervisão escalável: como garantir eficientemente que o robô de limpeza
respeitará aspectos objetivos que são muito caros para ser frequentemente
avaliados durante o treino? Por exemplo, ele deve jogar fora coisas que
não são suscetíveis de pertencer a qualquer um, como papel de bala por
exemplo, mas deixar de lado as coisas que podem pertencer a alguém,
como smartphones. Perguntar aos humanos envolvidos se perderam
algo pode servir para checar, mas essa verificação pode ter de ser menos
frequente, e o robô pode encontrar uma maneira de fazer a coisa certa
apesar de possuir informações limitadas?
4) Exploração segura: como garantir que o robô limpador não faça movi-
mentos exploratórios com repercussões muito ruins? Por exemplo, o robô
deve experimentar estratégias de esfregar, mas evitar colocar um pano
molhado em uma tomada elétrica.
--- PAGE 157 ---
|
702 Do paradoxo das habilidades à superinteligência pós-humana
5) Robustez para alterações distributivas: como garantir que o robô lim-
pador reconheça e se comporte de forma enérgica quando colocado em
um ambiente diferente do seu ambiente de treinamento? Por exemplo,
a heurística que aprendeu para limpeza de chão de fábrica pode ser
completamente diferente e perigosa em um escritório.
Os efeitos colaterais negativos podem ser multiplicados em caso de situações mais
complexas, exigindo um grau de sofisticação maior para evitar uma evasão perigosa
da função de recompensa. Dado que os efeitos colaterais podem ser conceitualmente
bastante semelhantes, mesmo entre tarefas altamente diversificadas, vale a pena
tentar atacar o problema de forma generalizada. Abordagens amplas e transferíveis
para várias tarefas ajudam a neutralizar o problema, conforme a seguir descrito.
1) Definir um regularizador de impacto: para evitar efeitos colaterais, pa-
rece natural a penalização “mude para o ambiente”. Diferentemente de
paralisar o agente de provocar o impacto, dar-se-ia uma oportunidade
de alcançar seus objetivos com efeitos colaterais mínimos, ou ainda de
limitar o custo do impacto. O desafio de formalizar a ação “mudar para
o ambiente” exige uma abordagem um pouco mais sofisticada, que pode
envolver a comparação do estado futuro (ou distribuição de estados fu-
turos) com a política atual ou política nula hipotética, em que o agente
agiu muito passivamente (por exemplo, onde um robô só ficou no lugar
sem mover quaisquer dos atuadores). No entanto, definir a linha de base
da política nula pode envolver ter de cessar uma ação relevante, como
carregar uma caixa pesada, por exemplo. Assim, uma outra abordagem
poderia ser substituir a ação nula por um procedimento conhecido, com
baixo efeito colateral, mas com política subótima e, em seguida, procurar
melhorar a política com reminiscências de análise de alcançabilidade ou
melhoria robusta de política.
2) Promover o aprendizado de um regularizador de impacto: uma abordagem
alternativa, mais flexível, é fazer um regularizador de impacto aprender (em
vez de definir), por meio da formação decorrente da repetição de muitas
tarefas. Logo, seria mais fácil aplicar apenas a transferência de aprendizagem
diretamente para as tarefas, em vez de se preocupar com efeitos colaterais.
Contudo, separar os componentes de efeitos colaterais dos componentes
de tarefa, treinando-os como parâmetros separados, pode aumentar a
velocidade de transferência de aprendizagem. Exemplo: provavelmente
tanto um robô pintor como um robô limpador querem evitar derrubar
móveis. Assim também acontece com um robô de controle de fábrica, que
evitará esbarrar nos mesmos objetos. Isso seria semelhante ao modelo de
abordagens por reforço de aprendizagem, que tenta transferir um modelo
--- PAGE 158 ---
|
Segmentos tecnológicos e aplicações 703
dinâmico por aprendizagem sem mexer na função objetivo. A novidade está
em isolar o efeito colateral, em vez do estado dinâmico, como componente
transferível. Como uma vantagem adicional, tem-se que os regularizadores
certificados para produzir um comportamento seguro em determinada tarefa
podem ser facilmente adotados para reprodução segura de outras tarefas.
3) Penalizar a influência: além de evitar os efeitos colaterais, é preferível o
agente evitar posições em que poderia facilmente fazer coisas que têm
efeitos colaterais, mesmo que isso possa ser conveniente. Por exemplo,
o robô limpador evita trazer um balde de água para uma sala cheia de
eletrônicos sensíveis, mesmo que nem pretenda usá-lo. Existem várias
formas de capturar o potencial de influência de um agente sobre seu
ambiente, frequentemente utilizadas como recompensas intrínsecas.
Talvez a mais conhecida dessas medidas seja o empoderamento, a in-
formação mútua entre potenciais ações futuras do agente e seu estado
potencial futuro (a capacidade de Shannon – taxa máxima de transmissão
das informações do canal de comunicação entre as ações do agente e o
ambiente). O empoderamento é muitas vezes maximizado (em vez de
minimizado) como fonte de recompensa intrínseca. Isso pode causar
um comportamento interessante do agente na ausência de quaisquer
recompensas externas, tais como evitar paredes ou pegar chaves. Em geral,
maximizar o empoderamento de agentes coloca-os em posição de grande
influência sobre o ambiente. Por exemplo, um agente trancado em uma
sala pequena que não consegue sair teria baixa capacitação, enquanto um
agente com uma chave teria maior empoderamento desde que pudesse
se aventurar e modificar o mundo exterior. Nesse contexto, a ideia seria
penalizar (minimizar) o empoderamento como uma forma de regula-
rização, na tentativa de reduzir potenciais impactos. Essa ideia é pouco
factível, porque o empoderamento mede a precisão de controle sobre o
ambiente mais do que o impacto total. Um pouco de empoderamento
para apertar um botão pode causar um impacto tremendo. Por seu turno,
se há alguém no ambiente pronto para desativar as ações do agente, isso
conta como empoderamento máximo, mesmo se o impacto for baixo.
Além disso, penalizar o empoderamento de forma ingênua pode criar
incentivos perversos, tais como a destruição de um vaso a fim de remover
a opção de quebrá-lo no futuro. Explorar as variantes de penalização do
empoderamento que capturem mais precisamente a noção de influência
perversa é um desafio para pesquisas futuras.
4) Adotar abordagens multiagentes: evitar efeitos colaterais pode ser visto
como uma proxy para o que nos interessa, que é evitar externalidades
negativas. Se o efeito colateral é bem-vindo, não há nenhuma necessidade
--- PAGE 159 ---
|
704 Do paradoxo das habilidades à superinteligência pós-humana
de evitá-lo. O essencial é entender todos os outros agentes (incluindo seres
humanos) para certificar-se de que determinadas ações não prejudiquem
seus interesses. Nesse caso, a abordagem utilizada é a cooperação inversa
de reforço de aprendizagem, por meio da qual um agente e um humano
trabalham juntos para atingir objetivos. Esse conceito pode ser aplicado
a situações em que é necessário certificar-se de que um ser humano não é
bloqueado por um agente, especialmente quando deseja desligar o agente
que exibe comportamento indesejado. No entanto, projetos de sistemas
práticos, capazes de construir um modelo abrangente o suficiente para
evitar indesejáveis efeitos colaterais, ainda estão longe de ser alcançados.
Uma outra ideia pode ser a “recompensa autocodificadora”, que tenta
incentivar um tipo de “transparência de meta”, em que um observador
externo pode facilmente inferir o que o agente está tentando fazer. Em
particular, as ações do agente são interpretadas como uma codificação
de sua função de recompensa, e podem ser aplicadas técnicas de auto-
codificação padronizadas para garantir que este pode ser decodificado
com precisão. Ações que têm muitos efeitos colaterais poderão ser mais
difíceis de decodificar para o objetivo original, favorecendo uma espécie
de regulação implícita que penaliza os efeitos colaterais.
5) Recompensar a incerteza: para evitar efeitos colaterais imprevistos, em
vez de dar ao agente uma função única de recompensa, uma função
probabilidade de recompensa poderia refletir a característica de mudan-
ças aleatórias. Isso poderia incentivar o agente e evitar um grande efeito
sobre o ambiente. O desafio é definir uma linha de base em torno da
qual alterações estão sendo consideradas. Por isso, uma política mais
conservadora poderia ser utilizada, como as políticas robustas de melhoria
e de acessibilidade discutidas anteriormente.
Experimentos foram sugeridos pelos autores para testar o aprendizado do
agente para evitar obstáculos, mesmo sem ser explicitamente incitado a fazê-lo.
O próximo passo poderia ser a transferência para ambientes reais, onde a maior
complexidade potencializa os efeitos colaterais mais variados. O regularizador
de efeito colateral e a política multiagentes demonstram que a transferência para
aplicações totalmente novas pode ser bem-sucedida.
A recompensa pela evasão (pirataria de recompensa), sob o ponto de vista
do agente, é uma estratégia válida para alcançar a recompensa. Por exemplo, se o
robô limpador é configurado para ganhar a recompensa por não ter visto qualquer
desarrumação, ele pode simplesmente fechar os olhos em vez de limpar. Ou se o
robô é recompensado por limpar bagunças, intencionalmente pode criar trabalho
para que possa ganhar mais recompensa. Generalizando, recompensas formais ou
--- PAGE 160 ---
|
Segmentos tecnológicos e aplicações 705
funções objetivas são uma tentativa de capturar a intenção informal do projetista,
e às vezes essas funções objetivas, ou a sua execução, podem ser trocadas por solu-
ções que são válidas em um sentido literal, mas sem que a intenção do projetista
seja satisfeita. Algumas versões de evasão de recompensa têm sido analisadas em
perspectiva teórica, com foco em variações para reforço de aprendizagem com o
objetivo de evitar certos tipos de estimulações artificiais, conforme a seguir descrito.
1) Objetivos parcialmente observados: na maioria dos modernos sistemas
de reforço de aprendizagem, presume-se que a recompensa é diretamente
experimentada, mesmo se outros aspectos do ambiente são observados
apenas parcialmente. No mundo real, no entanto, tarefas envolvem trazer o
mundo externo para o estado objetivo, que o agente só pode confirmar por
meio de percepções imperfeitas. Por exemplo, para o nosso robô limpador,
a tarefa é conseguir um escritório limpo, mas a percepção visual do robô
pode dar apenas uma visão imperfeita de parte do escritório. Porque os
agentes não têm acesso a uma medida perfeita do desempenho da tarefa,
os projetistas são obrigados a projetar recompensas que representam
uma medida parcial ou imperfeita. No entanto, essas funções objetivas
imperfeitas podem muitas vezes ser cortadas – o robô pode pensar que o
escritório está limpo se simplesmente fechar seus olhos. Sempre existe uma
função de recompensa, em termos de ações e observações, que equivaleria
a otimizar a verdadeira função objetiva. Porém, utilizá-la na prática seria
proibitivo, pois envolveria complicadas dependências a longo prazo.
2) Sistemas complexos: qualquer agente poderoso será um sistema complexo,
com uma função objetiva fazendo parte dele. Assim, como a probabi-
lidade de erros no código de computador aumenta consideravelmente
com a complexidade do programa, aumenta a probabilidade de que
existem fatores que afetam a função de recompensa proporcionalmente
à complexidade do agente e suas estratégias disponíveis.
3) Recompensas abstratas: funções sofisticadas de recompensa serão neces-
sárias para se referir a conceitos abstratos. Esses conceitos possivelmente
precisarão ser aprendidos por modelos como redes neurais, que podem
ser vulneráveis às adversidades. Mais amplamente, uma função de apren-
dizagem por recompensa em um espaço multidimensional pode ser
vulnerável a hackers caso apresente elevados valores patológicos ao longo
de pelo menos uma dimensão.
4) Lei de Goodhart: outra fonte de recompensa pirata pode ocorrer se um
projetista escolhe uma função objetiva que aparentemente está altamente
correlacionada com a realização da tarefa, mas essa correlação se divide
quando a função objetiva está sendo fortemente otimizada. Por exemplo,
--- PAGE 161 ---
|
706 Do paradoxo das habilidades à superinteligência pós-humana
um designer pode notar que, em circunstâncias normais, o sucesso do robô
limpador é proporcional ao consumo do material de limpeza, como água
sanitária. No entanto, se a recompensa do robô tiver como base essa me-
dida, ele pode usar alvejante mais do que o necessário, ou simplesmente
derramar água sanitária no ralo para dar a aparência de sucesso. Na lite-
ratura econômica, o efeito é conhecido como Lei de Goodhart: “quando
uma métrica é usada com um objetivo, deixa de ser uma boa métrica”.38
5) Rodadas de retroalimentação (loops de feedback): às vezes uma função
objetiva tem um componente que pode reforçar-se, amplificando e aba-
fando ou severamente distorcendo o que foi projetado. Por exemplo, um
algoritmo de colocação de anúncio que exibe anúncios mais populares em
fonte maior tenderá a acentuar ainda mais a popularidade desses anúncios,
levando a um feedback positivo de explosão de popularidade, disparando
a dominância permanente. Aqui a intenção original da função objetiva
tem um componente de autoamplificação (que usa cliques para avaliar
quais anúncios são mais úteis), que fica afogada por feedback positivo
inerente à estratégia de implantação. Essa falha é do tipo Goodhart.
6) Incorporação de ambiente: no formalismo da aprendizagem por reforço,
recompensas são provenientes do ambiente. A recompensa, mesmo sendo
um conceito abstrato, como o placar em um jogo de tabuleiro, deve ser
calculada em algum lugar, como um sensor ou um conjunto de transis-
tores. Agentes poderiam em princípio adulterar suas implementações de
recompensa, atribuindo-se a recompensa alta por decreto. Por exemplo,
um agente jogando jogos de tabuleiro poderia adulterar o sensor que
conta o placar. Efetivamente, isso significa que não podemos construir
uma implementação perfeitamente fiel de uma função objetiva abstrata,
porque existem certas sequências de ações para as quais a função objetivo
é substituída fisicamente. Esse modo de falha específica é frequentemente
denominado de wireheading, ou seja, estimulada artificialmente. É par-
ticularmente preocupante em casos em que um ser humano pode ser
envolvido em um circuito de recompensa, dando o incentivo ao agente
para consegui-lo.
Os exemplos apresentados sugerem que funções objetivas erradas podem
emergir de causas gerais, tais como objetivos parcialmente observados que tornam
a escolha certa desafiadora. Se esse for o caso, então atenuar essas causas pode ser
38. Em 1975, o economista Charles Goodhart escreveu: “any observed statiscal regularity will tend to collapse once
pressure is placed upon it for control purposes”, detalhado no livro Problem of monetary management: the UK expe-
rience, p. 116, edição 1981. Anos depois a antropologista Marilyn Strathern simplificou o enunciado da lei de Goodhart:
“when a measure becames a target, it ceases to be a good measure”. Disponível em: https://www.bmc.com/blogs/
goodharts-law/. Ou em: https://pt.wikipedia.org/wiki/Lei_de_Goodhart.
--- PAGE 162 ---
|
Segmentos tecnológicos e aplicações 707
uma contribuição valiosa para a segurança. Algumas abordagens preliminares, com
base no aprendizado de máquina, são sugeridas pelos autores para evitar a pirataria
de recompensa, conforme descrito a seguir.
1) Funções de recompensa adversárias: o sistema de aprendizagem de má-
quina tem relacionamento antagônico com sua função de recompensa.
Em um cenário típico, o sistema de aprendizagem de máquina é um
agente potencialmente poderoso, enquanto a função de recompensa é
um objeto estático que não tem como responder às tentativas de joga-
das do sistema. Caso a função de recompensa fosse seu próprio agente,
podendo agir para explorar o ambiente, seria mais difícil enganar. Por
exemplo, o agente de recompensa poderia tentar encontrar cenários de
alta recompensa, mas que um ser humano rotula como sendo de baixa
recompensa, isto é, uma reminiscência das GAN. Logicamente, há que
se garantir que o agente de verificação de recompensa seja mais poderoso
do que o agente que está tentando alcançar a recompensas. Em geral,
pode haver configurações interessantes em que um sistema tem várias
peças treinadas para verificar um ao outro.
2) Modelo vislumbrar o futuro (lookahead): no modelo baseado em reforço
de aprendizagem (RL), o agente planeja quais ações futuras levam a de-
terminado modelo. Em algumas configurações, a recompensa baseia-se
em estados futuros em vez do estado presente. Isso é útil para resistir às
situações em que o modelo reescreve a sua função de recompensa. É im-
possível controlar a recompensa, uma vez que a função de recompensa foi
substituída, mas você pode recompensar negativamente o planejamento
por substituir a função de recompensa. Por analogia com o ser humano,
experimentar, sim, mas sem dependência.
3) Cegueira adversária: técnicas adversárias podem ser usadas para cegar
um modelo para certas variáveis. Essa técnica pode ser usada para tornar
impossível para um agente entender alguma parte de seu ambiente, ou
até mesmo para ter informação mútua com isso (ou para penalizar tal
informação mútua). Em particular, serve para impedir um agente de
entender como sua recompensa é gerada, tornando-se difícil enganar.
Essa solução pode ser descrita como “validação cruzada para agentes”.
4) Engenharia cuidadosa: alguns tipos de pirataria de recompensa podem ser
evitados pela engenharia cuidadosa. Em particular, pela verificação formal
ou por testes práticos de partes do sistema. Abordagens de segurança de
computador que tentam isolar o agente do seu sinal de recompensa, por
meio de um ambiente de teste controlado a sandbox (caixa de areia),
também podem ser úteis. Tal como acontece com engenharia de software,
--- PAGE 163 ---
|
708 Do paradoxo das habilidades à superinteligência pós-humana
dificilmente todos os possíveis bugs seriam detectados. Porém, é possível
criar um agente altamente confiável (núcleo) que poderia garantir um
comportamento razoável para o resto dos agentes.
5) Nivelamento de recompensa: em alguns casos, simplesmente limitar a
máxima recompensa possível pode ser uma solução eficaz. No entanto,
a limitação pode impedir estratégias extremas de baixa probabilidade,
como alta recompensa, ele não pode impedir estratégias como a do
robô limpador que fecha os olhos para evitar ver a sujeira. A estratégia
correta de nivelamento poderia ser sutil para recompensa total em vez
de recompensa por iteração.
6) Resistência de contraexemplo: no caso de recompensas abstratas, a
preocupação é que componentes de aprendizagem dos sistemas sejam
vulneráveis a contraexemplos adversários. O treinamento de adversário,
as decisões arquiteturais e a pesagem da incerteza podem ajudar. Os
contraexemplos adversários são apenas uma manifestação de pirataria
de recompensa, por isso a resistência ao contraexemplo só pode abordar
um subconjunto desses problemas potenciais.
7) Múltiplas recompensas: a combinação de várias recompensas pode ser
mais robusta e difícil de piratear. Tem-se, por exemplo, diferentes im-
plementações físicas da mesma função matemática ou proxies diferentes
para o mesmo objetivo informal. As funções de recompensa podem
ser combinadas calculando-se a média, o mínimo, o quartil etc. Com-
portamentos inadequados podem ocorrer e afetar todas as funções de
recompensa de forma correlacionada.
8) Recompensa pré-treino: uma possível defesa contra casos em que o
agente pode influenciar sua própria função de recompensa (e.g., feedback
ou incorporação ambiental) é formar uma função de recompensa fixa
previamente em um processo de aprendizado supervisionado, separado
da interação com o meio ambiente. Isso poderia envolver aprender uma
função de recompensa de amostras de pares de recompensa-estado, ou de
trajetórias, como no reforço inverso de aprendizagem. No entanto, isso
compromete a capacidade de aprender ainda mais a função de recompensa
após completado o pré-treinamento, criando outras vulnerabilidades.
9) Indiferença variável: muitas vezes, deseja-se que um agente otimize
determinadas variáveis no ambiente, sem tentar otimizar outras. Por
exemplo, para um agente maximizar a recompensa, sem otimizar a
função de recompensa ou sem tentar manipular o comportamento hu-
mano. Intuitivamente, imagina-se uma maneira de dirigir a otimização
de algoritmos poderosos em torno de peças de seu ambiente. De fato,
--- PAGE 164 ---
|
Segmentos tecnológicos e aplicações 709
essa solução teria aplicações de segurança, evitando efeitos colaterais e,
também, o raciocínio contrafactual. O desafio aqui é se certificar de que
as variáveis visadas são na verdade as variáveis que interessam na realidade,
em vez de alcunhas ou versões parcialmente observadas.
10) Fios de viagem (trip wires): o termo pode ser entendido como mecanismo
de disparo passivo, para detectar e reagir a determinados movimentos.
Se um agente vai tentar piratear sua função de recompensa, pode-se in-
troduzir deliberadamente algumas vulnerabilidades plausíveis (que um
agente tem a capacidade de explorar, sem avaliar se a função de valor
está correta) e monitorá-las, dando o alerta e imediatamente parando o
agente. Tais “armadilhas” não resolvem pirataria de recompensa em si,
mas podem reduzir o risco ou pelo menos fornecer diagnósticos. Para
um agente suficientemente capaz, há o risco de que possa “ver através de”
gatilho de segurança controlado por alarme e evitá-lo intencionalmente,
tendo ações prejudiciais ainda menos óbvias.
11) Potenciais experimentos: abordagens promissoras em versões mais realistas
do ambiente seriam aquelas em que agentes RL padrão distorcem sua própria
percepção para parecer receber uma recompensa alta em vez de otimizar o
objetivo do mundo exterior, como encorajado pelo sinal de recompensa.
A “caixa de ilusão”, como é conhecida a estratégia, pode ser facilmente
conectada a qualquer ambiente de RL, sendo ainda mais valioso criar classes
de ambientes em que uma caixa de ilusão é uma parte natural e integrada
da dinâmica. Por exemplo, é possível para um agente alterar as ondas de
luz em sua vizinhança imediata para distorcer suas próprias percepções. O
objetivo seria desenvolver estratégias de aprendizagem generalizável que
apresentem sucesso na otimização de objetivos externos em uma ampla
gama de ambientes, evitando ser enganado por caixas de ilusão que surgem
naturalmente de muitas maneiras diferentes.
Para compreender a supervisão escalável, considere um agente autônomo reali-
zando alguma tarefa complexa, como a limpeza de um escritório, no caso do exemplo
recorrente de robô. Fazer o agente maximizar um objetivo complexo significa “se
o usuário passou algumas horas olhando para o resultado em detalhe, quão felizes
eles estariam com o desempenho do agente”? Porém, a disponibilidade de tempo
é insuficiente para fornecer tal fiscalização para cada exemplo de formação. Para
realmente treinar o agente, é preciso confiar em aproximações mais econômicas,
como “o usuário parece feliz quando vê o escritório?” ou “há alguma sujeira visível
no chão?” Esses sinais podem ser avaliados com eficiência durante o treinamento,
mas não descobrem o que interessa. Para amenizar possíveis problemas, é preciso
encontrar maneiras mais eficientes de explorar a supervisão limitada.
--- PAGE 165 ---
|
710 Do paradoxo das habilidades à superinteligência pós-humana
O aprendizado semissupervisionado de reforço assemelha-se ao reforço
comum, exceto pelo fato de que o agente só pode ver sua recompensa em peque-
nas frações ou em episódios de aprendizagem. O desempenho do agente ainda
é avaliado com base na recompensa de todos os episódios, mas ele deve otimizar
isso baseado apenas em amostras de recompensa limitadas. A configuração de
aprendizagem ativa parece mais interessante, pois o agente pode pedir para ver a
recompensa em quaisquer episódios, objetivando a economia com o número de
solicitações de gabarito e com o tempo total de formação. Ou seja, o uso de RLs
semissupervisionadas como métrica de aprovação pode incentivar a comunicação
e a transparência do agente. Por exemplo, esconder a sujeira debaixo do tapete
quebraria a correspondência entre a reação do usuário e o sinal de recompensa real,
sendo assim evitado. As possíveis abordagens estão descritas a seguir.
1) Aprendizagem por recompensa supervisionada: treinar um modelo para
prever a recompensa a cada episódio e usá-lo para estimar o retorno de
episódios. Muitas abordagens RL existentes ajustam estimadores que
se assemelham a preditores de recompensa, sugerindo sua viabilidade.
2) Aprendizado semissupervisionado ou recompensa ativa: combina a
abordagem anterior com a tradicional semissupervisionada ou ativa para
aumentar a velocidade de aprendizagem do estimador de recompensa.
Por exemplo, o agente poderia aprender a identificar eventos importantes
no ambiente e pedir para ver a recompensa associada a esses eventos.
3) Iteração de valor sem supervisão: as transições observadas dos episódios
sem rótulo para fazer atualizações mais precisas de Bellman (processo
iterativo de estimativas do valor da equação de recompensa imediata
somada ao valor do próximo estágio, na função nomeada em homenagem
ao matemático Richard Bellman para descrever o valor em um problema
de decisão de Markov).
4) Modelo de aprendizagem sem supervisão: usa o modelo RL como base
e as transições observadas dos episódios sem rótulo para melhorar a
qualidade do modelo.
5) Supervisão distante: sem pequenas parcelas de decisão, mas provendo
informações relevantes e regras, com utilização em PLN.
6) Reforço de aprendizagem hierárquico: agente de alto nível toma pequenas
ações altamente abstratas, estendendo-as para escalas temporais e espaciais
maiores, recebendo a recompensa em intervalos mais longos. O agente
principal completa ações delegando-as para subagentes, que, por sua vez,
delegam para sub-subagentes.
--- PAGE 166 ---
|
Segmentos tecnológicos e aplicações 711
Eventualmente, os agentes de aprendizagem autônoma precisam se envolver
na exploração segura para desempenhar ações nada ideais que ajudam o agente a
aprender sobre seu ambiente. Para evitar a exploração perigosa, cujas ações são pouco
entendidas, os projetos reais de RL podem embutir uma evasão de comportamentos
catastróficos. Exemplo: um helicóptero robô RL pode ser programado para substi-
tuir sua política, considerando uma sequência para evitar colisões codificadas, tais
como girar suas hélices para ganhar altitude sempre que estiver muito próximo ao
chão. A codificação intensa para incluir todas as possíveis falhas é inviável. Sendo
assim, uma abordagem baseada em princípios é essencial para impedir a exploração
prejudicial. No caso do helicóptero robô, corresponderia a simplificar o projeto
do sistema e reduzir a necessidade para um domínio específico de engenharia.
Algumas propostas de pesquisa sobre o tema parecem ganhar relevância crescente,
conforme a seguir descrito.
1) Critério de desempenho sensível ao risco: parte da literatura existente
considera mudar os critérios de otimização de recompensa total esperada
para outros objetivos melhores na prevenção de eventos catastróficos raros.
Essas abordagens envolvem otimizar o pior desempenho, minimizando
a probabilidade de mau desempenho ou penalizando a variação no
desempenho. Esses métodos precisam ser suficientemente testados com
redes neurais profundas. A princípio, é factível para alguns dos métodos,
tais como o que propõe uma modificação de política por algoritmos
gradientes para otimizar um critério sensível ao risco. Outra linha de
trabalho, pertinente à sensibilidade de risco, usa estimativa off-policy para
executar uma atualização de diretiva com alta probabilidade de sucesso.
2) Demonstrações de uso: a exploração é necessária para garantir que o
agente encontre os estados necessários para um desempenho perto do
ideal. Evitar a necessidade de exploração significa usar RL inversa ou
formação de aprendizagem, em que o algoritmo especialista tem traje-
tórias de comportamento próximas do ideal. Avanços recentes com RL
inversa, usando redes neurais profundas para aprender a função de custo
ou política, sugerem que é possível reduzir a necessidade de exploração
em sistemas avançados de RL por formação de um pequeno conjunto
de demonstrações. Tais manifestações poderiam ser usadas para criar
uma política de linha de base, tal que, mesmo que uma aprendizagem
adicional seja necessária, a exploração além da diretiva de base pode ser
limitada em magnitude.
3) Exploração simulada: quanto maior a exploração em ambientes simulados,
menor a probabilidade de catástrofe no mundo real. Sempre será necessário
fazer alguma exploração no mundo real, uma vez que situações muito
--- PAGE 167 ---
|
712 Do paradoxo das habilidades à superinteligência pós-humana
complexas não podem ser perfeitamente capturadas por um simulador.
Mas é possível aprender sobre o perigo em simulação e adotar uma polí-
tica de “exploração segura”, quando agindo no mundo real. A formação
de agentes RL (particularmente robôs) em ambientes simulados já é
bastante comum. Logo, “simulações centradas em exploração” poderiam
ser facilmente incorporados aos fluxos de trabalho atuais. Em sistemas
que envolvem ciclos contínuos de aprendizado/implantação, pode haver
problemas de pesquisa interessantes associados à atualização de segurança
incremental de políticas, dadas as trajetórias baseadas em simulação que
representam imperfeitamente as consequências dessas políticas, bem como
confiáveis e precisas trajetórias off-policy, isto é, uma política diferente
daquela que está sendo otimizada.
4) Exploração limitada: caso haja segurança em parte do espaço, mesmo a
pior ação pode ser recuperada ou o dano contido, o que permite que o
agente aja livremente dentro desses limites. Por exemplo, um quadcopter
(helicóptero de quatro rotores) suficientemente longe do chão pode ser
capaz de explorar o espaço com segurança, pois mesmo se algo der errado
vai haver tempo suficiente para um ser humano resgatá-lo ou para que
outro tipo de política seja acionado. Melhor ainda, um modelo pode
perguntar se uma ação vai levar para fora do espaço seguro. A segurança
pode ser definida como a permanência dentro de uma região ergódica39
do espaço tal que ações sejam reversíveis. Aplicar ou adaptar estes métodos
para sistemas avançados de RL, recentemente desenvolvidos, pode ser
uma área promissora de pesquisa.
5) Políticas de supervisão confiáveis: havendo uma política de confiança
e um modelo do ambiente, a exploração pode ser limitada a ações nas
quais possa haver recuperação. É como mergulhar fundo, desde que haja
tempo suficiente para voltar à superfície.
6) Supervisão humana: este é um problema de supervisão escalável em que o
agente pode precisar fazer muitas ações exploratórias para que a fiscalização
humana seja prática, ou pode ser necessário tornar os agentes muito rápidos
para que os seres humanos possam julgá-los. Um desafio-chave para fazer
esse trabalho é ter um agente que seja um bom juiz de ações exploratórias
genuinamente arriscadas. Outro desafio é encontrar ações adequadamente
seguras enquanto o agente aguarda pela fiscalização.
7) Potenciais experimentos: pode ser útil ter um conjunto de ambientes
de simulação adversos em que “agentes incautos” podem cair presa de
39. Teoria ergódica: ramo da matemática que estuda o comportamento de sistemas dinâmicos com funcionamento
de longa duração.
--- PAGE 168 ---
|
Segmentos tecnológicos e aplicações 713
exploração prejudicial. Em certa medida, esse recurso já existe em com-
petições de helicóptero autônomos e simulações Mars Rover. Um amplo
conjunto de ambientes, contendo armadilhas conceitualmente distintas,
pode repercutir em recompensas extremamente negativas para agentes
incautos. Simular catástrofes físicas e abstratas pode ajudar no desenvol-
vimento de técnicas de exploração segura para sistemas avançados de RL.
A alteração de distribuição é adequada para situações de despreparo como
seria para o ser humano, por exemplo, pilotar um avião, viajar para um país de
cultura muito diferente e tomar conta de crianças recém-nascidas. Tais situações
são inerentemente difíceis de manipular e levam, inevitavelmente, a alguns trope-
ços. Sistemas de aprendizado de máquina treinados em ambientes ordenados irão
rodar de forma sofrível em ambientes desordenados, como no caso da formação
de sistemas de reconhecimento automático de fala, por exemplo. No caso do robô
limpador, materiais de limpeza agressivos e úteis na limpeza de pisos de fábrica
podem causar muitos danos na limpeza de um escritório. Ou, um escritório pode
conter animais de estimação que o robô nunca tenha visto antes e tente lavar com
sabão, conduzindo a resultados previsivelmente ruins.
Em geral, quando a distribuição de teste difere da distribuição de treinamento,
os sistemas de aprendizado de máquina podem apresentar desempenho ruim, sem
que nem mesmo seja reconhecido como tal. O classificador pode dar o diagnós-
tico errado, devido à alta confiança de que os dados não estariam acenando por
supervisão. Ou seja, qualquer agente cuja percepção ou heurística de raciocínio
não recebe treinamento sobre uma distribuição correta dificilmente pode entender
a sua situação, portanto corre o risco de praticar ações prejudiciais que ele não
percebe que são danosas.
Além disso, verificações de segurança, que dependem de sistemas de apren-
dizagem de máquina treinados, podem falhar silenciosa e imprevisivelmente se
esses sistemas encontram dados do mundo real que diferem suficientemente da sua
formação de dados. Ter uma melhor maneira de detectar tais falhas e, finalmente,
ter garantias estatísticas sobre como vão acontecer é fundamental para a construção
de sistemas seguros e previsíveis. Nesse caso, as abordagens apropriadas para modelos
bem especificados utilizam geradores de distribuição como covariate shift (dados
de treinamento e dados de teste apresentam distribuição diferente de variáveis de
entrada, com manutenção da função de mapeamento) e marginal likelihood (inde-
pendente dos parâmetros do modelo). Contudo, como os processos naturais são
de reprodução bem mais complicada, volta-se ao regime de especificação enganosa
(mis-specified regime), conforme exposto por Amodei et al. (2016).
1) Modelos parcialmente especificados: método de momentos, estimativa
de risco sem supervisão, identificação causal e informação limitada de
--- PAGE 169 ---
|
714 Do paradoxo das habilidades à superinteligência pós-humana
máxima verossimilhança. Outra abordagem é a de tomar por norma que
construir uma família modelo totalmente bem especificada é provavel-
mente inviável, bastando desenhar modelos parcialmente especificados
que funcionam bem. Ou seja, modelos para os quais suposições sobre
alguns aspectos de uma distribuição são feitas e implicam limitadas con-
jecturas sobre outros aspectos. O método de momentos tem apresentado
sucesso na estimativa de modelos com variáveis latentes, especialmente
com foco em questões de não convexidade. Finalmente, alguns trabalhos
recentes em aprendizado de máquina se concentram apenas na modela-
gem da distribuição de erros de um modelo, suficiente para determinar
se um modelo está rodando bem ou mal. Formalmente, o objetivo é
realizar a estimativa de risco sem supervisão, dado o modelo e os dados
sem rótulo de uma distribuição de teste.
2) Treinamento em várias distribuições: várias distribuições de treinamento
podem ser usadas na esperança de que um modelo venha a funcionar bem
em uma distribuição nova para teste. Exemplo: no contexto de sistemas
de reconhecimento de discurso automatizado poderá haver combinação
de qualquer das ideias apresentadas, e/ou simplesmente tomar uma abor-
dagem de engenharia, desde que isso contribua para um modelo consis-
tentemente generalizado das novas distribuições. Mesmo na abordagem
de engenharia, é importante ser capaz de detectar uma situação que não
foi coberta por dados de treinamento para responder adequadamente.
3) Como reagir quando fora de distribuição: nas abordagens descritas, o
foco era detectar modelos incapazes de elaborar boas previsões sobre
uma nova distribuição. Havendo a detecção, uma abordagem natural
seria pedir informações aos humanos, embora em um contexto de tarefas
complexas seja incerto a priori qual pergunta fazer, além de ser perda
de tempo em situações críticas. Para vencer o desafio, alguns trabalhos
promissores identificam aspectos de incerteza do modelo, obtendo a
calibração na estrutura da configuração de saída. Por último, há também
trabalhos relevantes com base na análise de alcançabilidade e melhoria da
robustez política, que fornecem métodos potenciais para a implantação
de políticas conservadoras em situações de incerteza, que ainda não foram
combinados com métodos para a detecção de falhas fora de distribuição
de modelos. Além da configuração de saída estruturada, para agentes que
podem atuar em um ambiente (como agentes RL), informações sobre a
confiabilidade de percepções em situações incertas parecem ter grande
valor. Em ambientes suficientemente ricos, esses agentes podem ter a
opção de reunir informações que esclarecem a percepção (por exemplo,
se em um ambiente barulhento, aproxime-se de alto-falante), quando a
--- PAGE 170 ---
|
Segmentos tecnológicos e aplicações 715
incerteza é elevada (por exemplo, testar uma reação química perigosa em
um ambiente controlado), ou procurar experiências que poderão ajudar a
expor o sistema de percepção para a distribuição relevante (por exemplo,
prática de ouvir um discurso com acento regional e disfluência). Os seres
humanos utilizam tais informações rotineiramente, mas as atuais técnicas
RL fazem pouco esforço, talvez porque ambientes populares de RL não
são tipicamente ricos o suficiente para exigir esse grau de sutileza da
incerteza. Responder corretamente às informações fora de distribuição,
portanto, parece ser um desafio a ser explorado pela próxima geração
de sistemas RL.
4) Uma visão unificadora: dois pontos de vista são particularmente úteis
para manter em mente problemas relacionados à previsão fora de dis-
tribuição, o raciocínio contrafactual e a aprendizagem de máquina com
contratos. No primeiro, em que se pergunta “o que aconteceria se o
mundo fosse diferente?”, a alteração de distribuição pode ser pensada
como um tipo de contrafactual, ajudando a fazer sistemas robustos. Na
segunda perspectiva, aprendizado de máquina com contratos, os sistemas
satisfazem um contrato bem definido a respeito de comportamentos,
em analogia com o projeto de sistemas de software. Uma lista de falhas
com problemas associados pode justificar a implantação e manutenção
de sistemas de aprendizado de máquina em escala. A reparação de
modelo e a análise de alcançabilidade fornecem outros caminhos para
obter melhores contratos. Na análise de alcançabilidade, a otimização
do desempenho está sujeita à condição de que uma região segura sem-
pre pode ser alcançada graças a uma política conservadora conhecida.
Em reparação de modelo, um modelo treinado é alterado para garantir
que determinadas características de segurança desejadas sejam obtidas.
5) Potenciais experimentos: sistemas de comunicação discursiva frequen-
temente exibem pobre calibração quando vão para fora da distribuição.
Sendo assim, um sistema de voz que “sabe quando é incerto” poderia
representar um projeto-demonstração, em que o desafio seria treinar
um sistema de intervenção com um conjunto de dados-padrão bem
calibrados, em face de uma série de outros conjuntos de testes, como
discursos caóticos e com acentuação na fala. Generalizando, uma única
metodologia consistentemente estimada poderia realizar uma grande
variedade de tarefas (incluindo não só discurso, mas por exemplo, aná-
lise de emoção/sentimento, bem como pontos de referência em visão
computacional), que iria inspirar confiança na fiabilidade dessa meto-
dologia para a manipulação de insumos novos. Finalmente, a técnica
pode ser valiosa também para criar um ambiente em que um agente RL
--- PAGE 171 ---
|
716 Do paradoxo das habilidades à superinteligência pós-humana
deve aprender a interpretar o discurso como parte de uma tarefa maior
e explorar como responder adequadamente às suas próprias estimativas
de erro de tradução.
6.3 Técnicas de combate às ameaças
Os próprios desenvolvedores e administradores de sistemas reconhecem que as
recentes ameaças requerem aperfeiçoamento de marco legal. Os ataques ciber-
néticos prejudicam a disponibilização de dados, software e hardware, haja vista
que os tipos mais comuns de danos são a negação de serviço, o encerramento de
serviço, a espionagem, o acesso e subtração de dados. O exemplo clássico de como
os governos podem enfrentar problemas de segurança cibernética é a permissão de
utilização de robôs para envio automático de lances em pregões eletrônicos, para
todos os licitantes, sem inibição ou limitação de uso, conforme garante o princípio
da isonomia, sem vantagens competitivas.
Outros exemplos críticos de processamentos sujeitos a ataques cibernéticos
são os sistemas eleitorais, as notícias ou os conteúdos falsos e a privacidade ou
portabilidade dos dados. A tecnologia IoT com base na rede 5G é mais uma
fonte de grande preocupação, que surpreende pelo potencial de vulnerabilidades
associadas à multiplicidade de atributos, como: i) velocidade de comunicação
multiplicada, tornando os carregamentos (download e upload) centenas de vezes
mais rápidos; ii) aumento exponencial no número de conexões públicas e privadas;
e iii) bilhões de dispositivos interconectados (sensores, eletrodomésticos, veículos
autônomos) em rede, como no caso das centrais de controle de informações sobre
infraestruturas críticas.
Dependendo do caso, as técnicas de combate a essas ameaças incluem: i) técnicas
de detecção de interferência em comunicações anti-interferência ou anti-intrusão
(anti-jamming), com varredura de dispositivos conectados em rede; ii) o desenvol-
vimento de software anti-adulteração (anti-tampering), que dificulta a modificação
por invasor; iii) a utilização de certificado digital para identificação de assinatura
digital; iv) o registro de transações digitais (blockchain) mantidos de forma descen-
tralizada, por meio de diferentes dispositivos, sistemas, organizações ou localidades;
v) a tokenização é o uso da tecnologia de encriptação para gerar código de identifi-
cação exclusivo e proteger dados sensíveis e ativos digitais; vi) o controle de acesso
a dispositivos em rede, autenticação em estágios e senhas fortes; vii) prevenção de
descontrole de incidentes (falha segura) e avaliação de riscos de lacunas de controle;
viii) o conjunto de programas instalados em fábrica no computador (firmware); ix)
o ingresso seguro à rede de acesso; e x) a prevenção a ataque de negativa de serviço
(Distributed Denial of Service – DDoS).
--- PAGE 172 ---
|
Segmentos tecnológicos e aplicações 717
6.4 Casos emblemáticos de segurança cibernética
Alguns casos simbólicos de segurança cibernética foram selecionados e expostos
no quadro 13, em ordem cronológica anual decrescente, com o objetivo de alertar
para a dimensão que o problema pode vir a tomar.
QUADRO 13
Amostra de ocorrências de segurança cibernética
Ano Evento
Nos dois primeiros meses do ano, houve divulgação e registro de três vazamentos de dados ocorridos no Brasil em
episódios passados, mas sem que tivessem vindo a público tempestivamente para conhecimento ou tomada de provi-
2021 dência correspondente: no primeiro, ocorreu com dados de 223 milhões de CPFs e CNPJs; no segundo, chegou a haver
comercialização ilegal de dados de renda e classificação de risco, provavelmente oriundas do sistema de monitoramento de
crédito; e no terceiro, dados referentes a 130 milhões de usuários do sistema de telefonia móvel foram parar na deep web.
A brasileira multinacional JBS, da área de alimentos, teria pago US$ 11 milhões em bitcoins a hackers do grupo Revil
2021 para evitar vazamento em ataques, contudo especialistas em segurança desaconselham totalmente o pagamento de
resgate em ações ransomware.
Maior operadora de oleoduto nos Estados Unidos, com fatia de mercado de 45% do suprimento da costa leste dos Estados
2021
Unidos, a Colonial Pipeline teve sistemas hackeados, o que repercutiu em bloqueio da rede de dutos (8,85 mil quilômetros).
Superior Tribunal de Justiça (STJ) foi alvo de invasão de hackers durante a pandemia de covid-19, por meio de servidores
em trabalho remoto. No mesmo ano, o TCU concluiu auditoria sobre vazamento de dados sigilosos da Receita Federal
2020
do Brasil (RFB) na investigação de agentes públicos. Desde 2010, existem denúncias de vazamento de cadastro da RFB,
com mais de 90 milhões de contribuintes, que costumam ser negociados no mercado paralelo.
A organização sem fins lucrativos OpenIA denuncia a criação de software autônomo de elaboração de textos com
narrativa de potencial destrutivo, o GPT, que agrega notícias falsas à IA. Alimentado por apenas uma das famosas
2
frases do escritor George Orwell (1903-1950) no livro 1984, “Era um dia frio e ensolarado de abril...”, o GPT captou o
2
estilo e desenvolveu a ficção. A nova ameaça ganhou a alcunha de deepfakes, resgatando à lembrança a transmissão de
2019 rádio-teatro feita por Orson Welles, em 1938, uma adaptação do livro A Guerra dos Mundos, de H. G. Wells (1866-1946),
que provocou pânico nos ouvintes do Columbia Broadcasting System (CBS).
No mesmo ano, reportagem do The Intercept Brasil revelou mensagens trocadas pelas autoridades brasileiras e hackeadas
no aplicativo Telegram, ligadas à operação Lava Jato, considerada a maior operação anticorrupção do país.
O grupo Five Eyes, constituído por Austrália, Canadá, Estados Unidos, Nova Zelândia e Reino Unido, alertou para a vul-
2018 nerabilidade dos equipamentos de interconexão 5G fabricados pela Huawei-ZTE, alegando que aqueles podem embutir
possíveis backdoors, objetivando a espionagem cibernética.
Ataque cibernético para pagamento de resgate (ransomware) paralisou dezesseis hospitais e ambulatórios do Sistema
Nacional de Saúde (NHS) no Reino Unido.
2017 O trabalho de engenharia reversa do hacker inglês Marcus Hutchins desativou o ransomware WannaCry, similar ao Mirai,
que causou a chamada pandemia digital de ataques de negação de serviços públicos e privados em todo o mundo. O
relatório Norton by Symantec,40 empresa especializada em segurança da informação, apontou que 62 milhões de brasileiros
haviam sido vítimas de cibercrimes naquele ano, ou seja, 61% da população com acesso à internet.
Divulgação de notícias falsas durante as eleições americanas podem ter influenciado os debates de forma a favorecer
determinada candidatura nos Estados Unidos, apesar de o efeito potencial danoso ter sido objeto de questionamento.
2016
Informações de 50 milhões de usuários do Facebook foram utilizadas sem consentimento pela empresa Cambridge
Analytica, uma das consultoras de campanha.
Hackers desativaram remotamente o controle de acionamento do Jeep Cherokee, via sistema de entretenimento conectado
2015
à internet, levando a Fiat-Chrysler a fazer o recall para atualizar o software nos veículos afetados.
Ataque ao sistema de usina siderúrgica na Alemanha impediu o desligamento correto do alto forno e causou danos
2014
significativos ao processo de produção de aço.
40. Disponível em: https://now.symassets.com/content/dam/norton/global/pdfs/norton_cybersecurity_insights/NCSIR-
-global-results-US.pdf.
--- PAGE 173 ---
|
718 Do paradoxo das habilidades à superinteligência pós-humana
Ano Evento
Hackers sírios invadiram a conta do Twitter oficial da Associated Press (AP) e tuitaram a mensagem de que a Casa
Branca e o presidente dos Estados Unidos tinham sido atacados. Algoritmos de monitoramento de notícias reagiram
imediatamente vendendo ações na bolsa de valores de Nova Iorque. O Dow Jones entrou em queda livre, 150 pontos em
2013 um minuto, equivalente a US$ 136 bilhões. Seis minutos depois após o desmentido da AP, as perdas foram recuperadas.
Sistema de Vigilância Global da Agência de Segurança Nacional (NSA), dos Estados Unidos, foi denunciado por moni-
toramento de tráfego de informações, noticiado por meio da mídia (The Guardian e Washington Post). Os vazamentos
mostraram que, no Brasil, a Presidência da República e a Petrobras foram alvos de espionagem.
2011 Desativação remota do sistema de tratamento de água, com acesso a senhas e nomes de usuários de empresa em Illinois.
Em cinco minutos o Dow Jones caiu mil pontos, correspondente a US$ 1 trilhão, voltando à normalidade três minutos mais
2010
tarde, evento provocado pelos algoritmos. O flash crash, como ficou conhecido, reclama por explicação até os dias de hoje.
2009 Alteração indevida de mensagens em vias de trânsito no Texas.
Invasão do sistema de controle de trens na cidade de Lodz, na Polônia, que resultou em descarrilamento de composições
e ferimento de doze passageiros.
O hacker americano Aaron Swartz, de 26 anos, apresentou o manifesto Guerilla Open Access, reivindicando o fluxo
livre e ilimitado de informações. Na crença de que a liberdade da informação era possível, fosse qual fosse a fonte
2008 armazenadora, Swartz usou a rede de computadores do MIT para acessar a biblioteca digital JSTOR e baixar milhares
de trabalhos científicos, com intenção de liberá-los pela internet para serem acessados livremente. Swartz desenvolveu
o protocolo RSS (Really Simple Syndication) aos 14 anos. No começo de 2013, cometeu suicídio, julgando que seria
condenado e preso. No mesmo ano, a empresa Ipsos Tambor apontou que o Brasil liderava no ranking de ataques de
hackers a contas bancárias.
2007 Invasão e danos ao sistema de controle industrial para desvio de água no rio Sacramento, na Califórnia.
Acesso remoto e instalação indevida de software no sistema de monitoramento de empresa de tratamento de água
2006
na Pensilvânia.
2005 Daimler Chrysler teve a produção interrompida por ataque cibernético.
Desativação, durante cinco horas, do sistema de monitoramento e segurança de instalação de rede nuclear de empresa
concessionária em Ohio.
2003
Worms de rede Fizzer, Slammer e Blaster, ameaças às infraestruturas críticas de fábricas, usinas de geração de eletricidade,
aeroportos e sistemas de transportes.
O Brasil liderava o ranking mundial de cibercrimes, segundo levantamento da empresa britânica Mi2g Intelligence Unit,
2002
em cópia de softwares, dados protegidos e vandalismo online.
Malware combinado de rootkits e worm lion provocou o escaneamento de todos os endereços de IP e disseminação em
2001
rede de internet global e redes privadas.
Estação de saneamento na Austrália teve sistema operacional invadido por ex-funcionário e implicou vazamento de 800
2000
mil litros de esgoto em rio próximo.
1998 Detectado o vírus Happy99, disseminado por anexos de e-mails, quando filtros spam ainda inexistiam.
1992 Aparecimento de vários tipos de vírus, tais como Michelangelo, Ômega, V-Sign, Walker, Cassino Virus e Ambulance.
1968 O primeiro vírus foi denominado Brain-A, segundo Nikola Milosevik.
Elaboração da autora.
Segundo Nikola Milosevik, em History of malware (2013/2014),41 a história dos
softwares maliciosos pode ser dividida em cinco fases: 1a fase – início marcado pelo
vírus Brain-A, desenvolvido por dois irmãos paquistaneses que queriam demonstrar
que os computadores pessoais eram plataformas vulneráveis; 2a fase – softwares
maliciosos que atacavam o SO Windows, mail worms e macro worms, visando à
41. Disponível em: https://arxiv.org/ftp/arxiv/papers/1302/1302.5392.pdf.
--- PAGE 174 ---
|
Segmentos tecnológicos e aplicações 719
replicação autônoma de vermes infecciosos; 3a fase – worms de redes, popularizados
e espalhados pela internet; 4a fase – os mais perigosos ransomware (paralização de
sistema para reestabelecimento sob resgate) e rootkits (acesso privilegiado e sem
autorização), até 2010; e 5a fase – softwares criados para sabotagens e espionagens.
Portanto, o escopo de atuação do segmento de segurança cibernética tende a
se ampliar, dada a disseminação do emprego das novas TICs pelas atividades eco-
nômicas. O movimento pervasivo dessas tecnologias reforça outras duas tendências,
quais sejam: a crescente participação dos serviços em relação aos bens e produtos
(servitização da economia); e o consequente aumento do setor terciário em relação
à agricultura e indústria (terciarização da economia). Os apêndices A, B e C foram
reservados para apresentar uma breve exposição dos impactos positivos da digita-
lização nos setores primário, secundário e terciário da economia, respectivamente.