#!/usr/bin/env python3
"""
Vérification finale de la centralisation des paramètres rollouts et clusters

Ce script effectue une vérification exhaustive pour s'assurer que TOUS les paramètres
concernant les rollouts et clusters sont bien centralisés dans leurs sections respectives
et qu'il n'y a AUCUNE duplication.
"""

import sys
import os
import re
from collections import defaultdict

# Ajouter le répertoire parent au path pour importer le module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyser_duplications_parametres():
    """Analyse les duplications de paramètres dans AZRConfig"""
    
    print("🔍 VÉRIFICATION FINALE - CENTRALISATION ROLLOUTS ET CLUSTERS")
    print("=" * 80)
    
    # Lire le fichier
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            lignes = f.readlines()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return False
    
    # Patterns pour identifier les définitions de paramètres
    pattern_definition = r'^\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*[a-zA-Z_\[\]]+\s*=\s*'
    
    # Dictionnaire pour stocker les définitions
    definitions = defaultdict(list)
    
    # Analyser chaque ligne
    for num_ligne, ligne in enumerate(lignes, 1):
        ligne_clean = ligne.strip()
        
        # Ignorer les commentaires purs et lignes vides
        if ligne_clean.startswith('#') or not ligne_clean:
            continue
            
        # Chercher les définitions de paramètres
        match = re.match(pattern_definition, ligne_clean)
        if match:
            param_name = match.group(1)
            definitions[param_name].append({
                'ligne': num_ligne,
                'contenu': ligne_clean[:100] + ('...' if len(ligne_clean) > 100 else '')
            })
    
    return definitions

def identifier_duplications_critiques(definitions):
    """Identifie les duplications critiques de paramètres rollout/cluster"""
    
    # Paramètres rollout/cluster à vérifier
    parametres_rollout_cluster = [
        # Rollout 1
        'rollout1_analysis_time_ms', 'rollout1_min_hands_quality', 'rollout1_combined_pair_sync_influence',
        # Rollout 2  
        'rollout2_generation_time_ms', 'rollout2_candidates_count', 'rollout2_max_probability',
        'rollout2_optimal_difficulty', 'rollout2_excellence_bonus',
        # Rollout 3
        'rollout3_prediction_time_ms', 'rollout3_default_confidence', 'rollout3_excellent_threshold',
        'rollout3_confidence_bonus_correct', 'rollout3_optimal_risk',
        # Clusters
        'cluster_analysis_time_ms', 'cluster_rollout1_weight', 'cluster_rollout2_weight', 'cluster_rollout3_weight',
        'min_calibration_factor', 'max_calibration_factor', 'calibrated_confidence_weight',
        # Rollouts généraux
        'n_rollouts', 'rollout_temperature', 'rollout_step_size', 'parallel_rollouts',
        # Valeurs de base communes
        'zero_value', 'one_value', 'half_value'
    ]
    
    duplications_critiques = {}
    
    for param in parametres_rollout_cluster:
        if param in definitions and len(definitions[param]) > 1:
            duplications_critiques[param] = definitions[param]
    
    return duplications_critiques

def verifier_sections_organisees():
    """Vérifie que tous les paramètres sont dans les bonnes sections"""
    
    print("\n🏗️ VÉRIFICATION DES SECTIONS ORGANISÉES")
    print("=" * 50)
    
    try:
        from azr_baccarat_predictor import AZRConfig
        config = AZRConfig()
        
        # Sections avec leurs paramètres attendus
        sections_verification = {
            'SECTION N - ROLLOUT 1 (ANALYSEUR)': [
                'rollout1_analysis_time_ms', 'rollout1_index_time_ms', 'rollout1_min_hands_quality',
                'rollout1_combined_pair_sync_influence', 'rollout1_step_increment'
            ],
            'SECTION O - ROLLOUT 2 (GÉNÉRATEUR)': [
                'rollout2_generation_time_ms', 'rollout2_candidates_count', 'rollout2_max_probability',
                'rollout2_fixed_length', 'rollout2_signal_confidence_high', 'rollout2_optimal_difficulty'
            ],
            'SECTION P - ROLLOUT 3 (PRÉDICTEUR)': [
                'rollout3_prediction_time_ms', 'rollout3_default_confidence', 'rollout3_excellent_threshold',
                'rollout3_confidence_bonus_correct', 'rollout3_optimal_risk'
            ],
            'SECTION Q - CLUSTERS': [
                'cluster_analysis_time_ms', 'cluster_rollout1_weight', 'cluster_rollout2_weight',
                'min_calibration_factor', 'calibrated_confidence_weight'
            ],
            'SECTION R - ROLLOUTS GÉNÉRAUX': [
                'n_rollouts', 'rollout_temperature', 'rollout_step_size', 'parallel_rollouts'
            ],
            'SECTION A - VALEURS DE BASE': [
                'zero_value', 'one_value', 'half_value', 'correlation_player_value'
            ]
        }
        
        sections_completes = 0
        total_parametres_verifies = 0
        parametres_presents = 0
        
        for section, parametres in sections_verification.items():
            parametres_section_presents = 0
            parametres_manquants = []
            
            for param in parametres:
                total_parametres_verifies += 1
                if hasattr(config, param):
                    parametres_section_presents += 1
                    parametres_presents += 1
                else:
                    parametres_manquants.append(param)
            
            pourcentage = (parametres_section_presents / len(parametres)) * 100
            
            if parametres_section_presents == len(parametres):
                print(f"✅ {section}: {parametres_section_presents}/{len(parametres)} ({pourcentage:.0f}%)")
                sections_completes += 1
            else:
                print(f"❌ {section}: {parametres_section_presents}/{len(parametres)} ({pourcentage:.0f}%)")
                print(f"   Manquants: {', '.join(parametres_manquants)}")
        
        pourcentage_global = (parametres_presents / total_parametres_verifies) * 100
        print(f"\n📊 RÉSULTAT GLOBAL:")
        print(f"   ✅ Sections complètes: {sections_completes}/{len(sections_verification)}")
        print(f"   ✅ Paramètres présents: {parametres_presents}/{total_parametres_verifies} ({pourcentage_global:.1f}%)")
        
        return sections_completes == len(sections_verification) and parametres_presents == total_parametres_verifies
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def verifier_proprietes_dynamiques():
    """Vérifie que les propriétés dynamiques fonctionnent"""
    
    print("\n🔧 VÉRIFICATION DES PROPRIÉTÉS DYNAMIQUES")
    print("=" * 50)
    
    try:
        from azr_baccarat_predictor import AZRConfig
        config = AZRConfig()
        
        proprietes_test = [
            ('rollout2_rewards', 'Récompenses Rollout 2'),
            ('rollout3_rewards', 'Récompenses Rollout 3'),
            ('cluster_reward_weights', 'Poids clusters'),
            ('confidence_calibration', 'Calibration confiance')
        ]
        
        proprietes_reussies = 0
        for prop_name, description in proprietes_test:
            if hasattr(config, prop_name):
                try:
                    prop_value = getattr(config, prop_name)
                    if isinstance(prop_value, dict) and len(prop_value) > 0:
                        print(f"  ✅ {prop_name}: {description} - {len(prop_value)} éléments")
                        proprietes_reussies += 1
                    else:
                        print(f"  ⚠️  {prop_name}: {description} - Dictionnaire vide")
                except Exception as e:
                    print(f"  ❌ {prop_name}: {description} - Erreur: {e}")
            else:
                print(f"  ❌ {prop_name}: {description} - MANQUANT")
        
        print(f"\n📊 RÉSULTAT PROPRIÉTÉS:")
        print(f"   ✅ Propriétés fonctionnelles: {proprietes_reussies}/{len(proprietes_test)}")
        
        return proprietes_reussies == len(proprietes_test)
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification des propriétés: {e}")
        return False

def main():
    """Fonction principale de vérification"""
    
    # Analyser les duplications
    definitions = analyser_duplications_parametres()
    if not definitions:
        return False
    
    # Identifier les duplications critiques
    duplications_critiques = identifier_duplications_critiques(definitions)
    
    # Afficher les résultats des duplications
    print(f"\n❌ DUPLICATIONS CRITIQUES DÉTECTÉES")
    print("=" * 50)
    
    if duplications_critiques:
        print(f"🔴 {len(duplications_critiques)} paramètres rollout/cluster dupliqués:")
        
        for param, occurrences in duplications_critiques.items():
            print(f"\n🔴 {param} - {len(occurrences)} définitions:")
            for i, occ in enumerate(occurrences, 1):
                print(f"   {i}. Ligne {occ['ligne']}: {occ['contenu']}")
    else:
        print("✅ AUCUNE DUPLICATION CRITIQUE DÉTECTÉE!")
    
    # Vérifier les sections organisées
    sections_ok = verifier_sections_organisees()
    
    # Vérifier les propriétés dynamiques
    proprietes_ok = verifier_proprietes_dynamiques()
    
    # Résultat final
    print(f"\n" + "=" * 80)
    print("📊 RÉSULTAT FINAL DE LA VÉRIFICATION")
    print("=" * 80)
    
    if not duplications_critiques and sections_ok and proprietes_ok:
        print("🎉 PARFAIT! CENTRALISATION COMPLÈTEMENT RÉUSSIE!")
        print("✅ Aucune duplication critique")
        print("✅ Toutes les sections organisées sont complètes")
        print("✅ Toutes les propriétés dynamiques fonctionnent")
        print("🏆 Tous les paramètres rollouts et clusters sont parfaitement centralisés!")
        return True
    else:
        print("⚠️  CORRECTIONS ENCORE NÉCESSAIRES:")
        if duplications_critiques:
            print(f"   ❌ {len(duplications_critiques)} paramètres avec duplications critiques")
        if not sections_ok:
            print(f"   ❌ Sections organisées incomplètes")
        if not proprietes_ok:
            print(f"   ❌ Propriétés dynamiques défaillantes")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
