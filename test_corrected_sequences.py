#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test des Séquences Corrigées - Rollout 2 → Rollout 3

Ce script teste la correction :
- Rollout 2 : 4 séquences de 3 P/B → 4 séquences de 3 S/O uniques
- Évite les doublons S/O
- Utilise le dernier résultat historique pour la conversion
"""

import sys
import os
import itertools
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import AZRCluster, AZRConfig

def test_corrected_sequences():
    """Test des séquences corrigées avec longueurs et logique appropriées"""
    
    print("🔧 TEST SÉQUENCES CORRIGÉES - ROLLOUT 2 → ROLLOUT 3")
    print("=" * 55)
    
    # Initialisation
    config = AZRConfig()
    cluster = AZRCluster(cluster_id=1, config=config)
    
    print("✅ Configuration et cluster initialisés")
    print(f"   • Longueur Rollout 2 : {config.rollout2_fixed_length} P/B")
    print(f"   • Longueur Rollout 3 : {config.rollout3_fixed_length} S/O")
    print(f"   • Nombre de séquences : {config.rollout2_sequences_count}")
    print(f"   • Total possibilités : {config.rollout2_total_possibilities}")
    
    # ========================================================================
    # TEST 1 : GÉNÉRATION DES 8 POSSIBILITÉS THÉORIQUES (3 P/B)
    # ========================================================================
    
    print("\n📊 TEST 1 : Toutes les possibilités théoriques (3 P/B)")
    print("-" * 50)
    
    # Générer toutes les 8 possibilités de longueur 3
    all_8_possibilities = []
    for sequence_tuple in itertools.product(['P', 'B'], repeat=3):
        sequence = list(sequence_tuple)
        pattern = ''.join(sequence)
        all_8_possibilities.append({
            'sequence': sequence,
            'pattern': pattern
        })
    
    print(f"✅ Possibilités générées : {len(all_8_possibilities)}")
    print("📋 Liste complète (3 P/B) :")
    for i, poss in enumerate(all_8_possibilities, 1):
        print(f"   {i}. {poss['pattern']} → {poss['sequence']}")
    
    # ========================================================================
    # TEST 2 : CONVERSION S/O AVEC DIFFÉRENTS HISTORIQUES
    # ========================================================================
    
    print("\n🔄 TEST 2 : Conversion S/O avec historiques")
    print("-" * 40)
    
    # Test avec historique se terminant par 'B'
    print("📊 Historique se terminant par 'B' :")
    for poss in all_8_possibilities:
        so_sequence = cluster._convert_pb_sequence_to_so_with_history(poss['sequence'], 'B')
        so_pattern = ''.join(so_sequence)
        print(f"   {poss['pattern']} → {so_pattern} ({poss['sequence']} → {so_sequence})")
    
    # Test avec historique se terminant par 'P'
    print("\n📊 Historique se terminant par 'P' :")
    for poss in all_8_possibilities:
        so_sequence = cluster._convert_pb_sequence_to_so_with_history(poss['sequence'], 'P')
        so_pattern = ''.join(so_sequence)
        print(f"   {poss['pattern']} → {so_pattern} ({poss['sequence']} → {so_sequence})")
    
    # ========================================================================
    # TEST 3 : DÉTECTION DES DOUBLONS S/O
    # ========================================================================
    
    print("\n🔍 TEST 3 : Détection des doublons S/O")
    print("-" * 35)
    
    # Analyser les doublons avec historique 'B'
    so_patterns_b = []
    for poss in all_8_possibilities:
        so_sequence = cluster._convert_pb_sequence_to_so_with_history(poss['sequence'], 'B')
        so_pattern = ''.join(so_sequence)
        so_patterns_b.append(so_pattern)
    
    unique_so_b = set(so_patterns_b)
    duplicates_b = len(so_patterns_b) - len(unique_so_b)
    
    print(f"📊 Avec historique 'B' :")
    print(f"   • Séquences P/B : {len(all_8_possibilities)}")
    print(f"   • Séquences S/O uniques : {len(unique_so_b)}")
    print(f"   • Doublons S/O : {duplicates_b}")
    print(f"   • Patterns S/O uniques : {sorted(unique_so_b)}")
    
    # Analyser les doublons avec historique 'P'
    so_patterns_p = []
    for poss in all_8_possibilities:
        so_sequence = cluster._convert_pb_sequence_to_so_with_history(poss['sequence'], 'P')
        so_pattern = ''.join(so_sequence)
        so_patterns_p.append(so_pattern)
    
    unique_so_p = set(so_patterns_p)
    duplicates_p = len(so_patterns_p) - len(unique_so_p)
    
    print(f"\n📊 Avec historique 'P' :")
    print(f"   • Séquences P/B : {len(all_8_possibilities)}")
    print(f"   • Séquences S/O uniques : {len(unique_so_p)}")
    print(f"   • Doublons S/O : {duplicates_p}")
    print(f"   • Patterns S/O uniques : {sorted(unique_so_p)}")
    
    # ========================================================================
    # TEST 4 : GÉNÉRATION OPTIMISÉE DU ROLLOUT 2
    # ========================================================================
    
    print("\n🎲 TEST 4 : Génération optimisée Rollout 2")
    print("-" * 40)
    
    # Données simulées pour le test
    analyzer_report = {
        'signals_summary': {
            'top_signals': [
                {'signal_name': 'IMPAIR_TO_PLAYER', 'signal_type': 'pb_prediction', 'strength': 0.8, 'confidence': 0.85, 'strategy': 'player_focus'},
                {'signal_name': 'PAIR_TO_BANKER', 'signal_type': 'pb_prediction', 'strength': 0.75, 'confidence': 0.8, 'strategy': 'banker_focus'},
                {'signal_name': 'SO_SAME_PREDICTION', 'signal_type': 'so_prediction', 'strength': 0.7, 'confidence': 0.75, 'target_outcome': 'S', 'strategy': 'same_focus'},
                {'signal_name': 'PAIR_SYNC_PATTERN', 'signal_type': 'pattern', 'strength': 0.65, 'confidence': 0.7, 'strategy': 'pair_sync_exploitation'}
            ],
            'exploitation_ready': True,
            'overall_confidence': 0.8
        },
        'generation_guidance': {
            'primary_focus': 'IMPAIR_patterns',
            'secondary_focus': 'so_patterns',
            'avoid_patterns': [],
            'optimal_sequence_length': 3,
            'confidence_thresholds': {'high': 0.7, 'medium': 0.6, 'low': 0.5},
            'exploitation_strategy': 'moderate',
            'risk_level': 'low'
        },
        'quick_access': {
            'current_state': 'IMPAIR_SYNC',
            'next_prediction_pb': 'P',
            'next_prediction_so': 'S',
            'prediction_confidence': 0.85,
            'alert_level': 'HIGH',
            'exploitation_ready': True
        },
        'indices_analysis': {
            'pbt': {'pbt_sequence': ['P', 'B', 'P', 'B', 'P']},  # Dernier P/B = 'P'
            'impair_pair': {'position_types': ['IMPAIR', 'PAIR', 'IMPAIR', 'PAIR', 'IMPAIR']},
            'desync_sync': {'sync_sequence': ['SYNC', 'DESYNC', 'SYNC', 'DESYNC', 'SYNC']},
            'combined': {'combined_sequence': ['IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC']}
        },
        'synthesis': {'analysis_quality': 0.8},
        'sequence_metadata': {'total_hands_analyzed': 50}
    }
    
    # Génération des séquences optimisées
    sequences = cluster._rollout_generator(analyzer_report)
    
    # Extraire les vraies séquences
    actual_sequences = sequences.get('sequences', []) if isinstance(sequences, dict) else sequences
    
    print(f"✅ Séquences générées : {len(actual_sequences)}")
    
    # ========================================================================
    # TEST 5 : ANALYSE DES SÉQUENCES GÉNÉRÉES
    # ========================================================================
    
    print("\n📊 TEST 5 : Analyse des séquences générées")
    print("-" * 40)
    
    generated_so_patterns = []
    
    for i, sequence in enumerate(actual_sequences):
        # Extraire les données de séquence
        if isinstance(sequence, dict):
            sequence_data = sequence.get('sequence_data', [])
            pb_data = sequence.get('sequence_data_pb', [])
            so_data = sequence.get('sequence_data_so', [])
            probability = sequence.get('estimated_probability', 0.0)
            last_historical = sequence.get('last_historical_pb', 'N/A')
        else:
            sequence_data = sequence
            pb_data = []
            so_data = []
            probability = 0.0
            last_historical = 'N/A'
        
        print(f"   Séquence {i+1} :")
        print(f"      S/O : {sequence_data}")
        if pb_data:
            print(f"      P/B : {pb_data}")
        if so_data:
            print(f"      S/O : {so_data}")
        print(f"      Probabilité : {probability:.3f}")
        print(f"      Historique : {last_historical}")
        
        if len(sequence_data) == 3 and all(x in ['S', 'O'] for x in sequence_data):
            pattern = ''.join(sequence_data)
            generated_so_patterns.append(pattern)
        
        print()
    
    # ========================================================================
    # TEST 6 : VALIDATION FINALE
    # ========================================================================
    
    print("✅ VALIDATION FINALE")
    print("-" * 20)
    
    # Vérifier l'unicité des patterns S/O générés
    unique_generated = set(generated_so_patterns)
    no_duplicates = len(generated_so_patterns) == len(unique_generated)
    
    # Vérifier le nombre correct
    correct_count = len(actual_sequences) == config.rollout2_sequences_count
    
    # Vérifier les longueurs
    correct_lengths = all(len(seq.get('sequence_data', [])) == 3 for seq in actual_sequences if isinstance(seq, dict))
    
    print(f"📊 Nombre correct de séquences : {'✅' if correct_count else '❌'}")
    print(f"📊 Pas de doublons S/O : {'✅' if no_duplicates else '❌'}")
    print(f"📊 Longueurs correctes (3 S/O) : {'✅' if correct_lengths else '❌'}")
    print(f"📊 Patterns S/O générés : {sorted(unique_generated)}")
    
    # ========================================================================
    # CONCLUSION
    # ========================================================================
    
    print("\n🎉 CONCLUSION")
    print("-" * 15)
    
    if correct_count and no_duplicates and correct_lengths:
        print("✅ CORRECTION PARFAITEMENT IMPLÉMENTÉE !")
        print("✅ Rollout 2 génère 4 séquences de 3 P/B")
        print("✅ Conversion en 4 séquences S/O uniques")
        print("✅ Pas de doublons S/O")
        print("✅ Utilise le dernier résultat historique")
        print("✅ Rollout 3 reçoit 4 options S/O distinctes")
    else:
        print("⚠️ Correction partiellement réussie")
        print("🔧 Ajustements nécessaires")
    
    print(f"\n🎯 LOGIQUE CORRIGÉE :")
    print(f"• Rollout 2 : 3 P/B + dernier historique = 3 S/O")
    print(f"• 8 possibilités P/B → 4-6 possibilités S/O uniques")
    print(f"• Sélection des 4 meilleures S/O par probabilité")
    print(f"• Rollout 3 choisit parmi 4 séquences S/O distinctes")

if __name__ == "__main__":
    test_corrected_sequences()
