#!/usr/bin/env python3
"""
Validation de la centralisation des paramètres des rollouts

Ce script vérifie que toutes les valeurs codées en dur ont été remplacées
par des références à la configuration centralisée.
"""

import re
from typing import List, Dict, <PERSON><PERSON>

def extract_hardcoded_values_in_rollouts(file_content: str) -> List[Dict]:
    """Extrait les valeurs potentiellement codées en dur dans les rollouts"""
    
    hardcoded_values = []
    lines = file_content.split('\n')
    
    # Patterns de valeurs codées en dur à détecter
    patterns = [
        r'\b0\.[0-9]+\b',  # Nombres décimaux
        r'\b[1-9][0-9]*\b',  # Nombres entiers > 0
        r'max\(\s*1\s*,',  # max(1, ...)
        r'range\(\s*1\s*,',  # range(1, ...)
        r'len\([^)]+\)\s*[+\-*/]\s*[0-9]+',  # len(...) + nombre
    ]
    
    # Zones des rollouts à analyser
    rollout_zones = [
        ('_rollout_analyzer', 'Rollout 1 - Analyseur'),
        ('_rollout_generator', 'Rollout 2 - Générateur'), 
        ('_rollout_predictor', 'Rollout 3 - Prédicteur'),
        ('_analyze_complete_', 'Méthodes d\'analyse'),
        ('_generate_', 'Méthodes de génération'),
        ('_evaluate_sequence_quality', 'Méthodes d\'évaluation')
    ]
    
    current_zone = None
    
    for line_num, line in enumerate(lines, 1):
        # Détecter la zone actuelle
        for zone_pattern, zone_name in rollout_zones:
            if zone_pattern in line and 'def ' in line:
                current_zone = zone_name
                break
        
        # Ignorer les commentaires et docstrings
        if line.strip().startswith('#') or '"""' in line or "'''" in line:
            continue
            
        # Ignorer les lignes de configuration
        if 'self.config.' in line:
            continue
            
        # Chercher les patterns de valeurs codées en dur
        for pattern in patterns:
            matches = re.finditer(pattern, line)
            for match in matches:
                # Exclure certains cas légitimes
                if any(exclude in line for exclude in [
                    'range(len(',  # range(len(...)) est OK
                    'enumerate(',  # enumerate est OK
                    'time.time()',  # time.time() est OK
                    'logger.',  # logging est OK
                    'print(',  # print est OK
                    'format(',  # format est OK
                    'f"',  # f-strings sont OK
                    "f'",  # f-strings sont OK
                ]):
                    continue
                
                hardcoded_values.append({
                    'line_number': line_num,
                    'line_content': line.strip(),
                    'matched_value': match.group(),
                    'zone': current_zone or 'Zone inconnue',
                    'pattern': pattern
                })
    
    return hardcoded_values

def check_rollout_config_sections(file_content: str) -> Dict[str, bool]:
    """Vérifie la présence des sections de configuration pour chaque rollout"""
    
    required_sections = {
        'ROLLOUT 1 - ANALYSEUR SPÉCIALISÉ': False,
        'ROLLOUT 2 - GÉNÉRATEUR SPÉCIALISÉ': False,
        'ROLLOUT 3 - PRÉDICTEUR SPÉCIALISÉ': False
    }
    
    for section_name in required_sections.keys():
        if section_name in file_content:
            required_sections[section_name] = True
    
    return required_sections

def check_config_usage_in_rollouts(file_content: str) -> Dict[str, int]:
    """Compte l'utilisation des paramètres de configuration dans les rollouts"""
    
    config_usage = {
        'rollout1_': 0,
        'rollout2_': 0, 
        'rollout3_': 0,
        'config.one_value': 0,
        'config.two_value': 0,
        'config.zero_value': 0
    }
    
    for param_prefix in config_usage.keys():
        pattern = rf'self\.config\.{re.escape(param_prefix)}'
        matches = re.findall(pattern, file_content)
        config_usage[param_prefix] = len(matches)
    
    return config_usage

def validate_rollout_configuration():
    """Validation principale de la configuration des rollouts"""
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return
    
    print("🔍 VALIDATION DE LA CENTRALISATION DES PARAMÈTRES ROLLOUTS")
    print("=" * 65)
    
    # 1. Vérifier les sections de configuration
    print("\n📋 1. VÉRIFICATION DES SECTIONS DE CONFIGURATION")
    print("-" * 50)
    
    config_sections = check_rollout_config_sections(file_content)
    all_sections_present = True
    
    for section, present in config_sections.items():
        status = "✅" if present else "❌"
        print(f"{status} {section}")
        if not present:
            all_sections_present = False
    
    if all_sections_present:
        print("\n✅ Toutes les sections de configuration sont présentes")
    else:
        print("\n⚠️  Certaines sections de configuration manquent")
    
    # 2. Vérifier l'utilisation des paramètres
    print("\n📊 2. UTILISATION DES PARAMÈTRES DE CONFIGURATION")
    print("-" * 50)
    
    config_usage = check_config_usage_in_rollouts(file_content)
    total_usage = sum(config_usage.values())
    
    for param_type, count in config_usage.items():
        if count > 0:
            print(f"✅ {param_type:<20} : {count:3d} utilisations")
        else:
            print(f"⚠️  {param_type:<20} : {count:3d} utilisations")
    
    print(f"\n📈 Total utilisations config : {total_usage}")
    
    # 3. Détecter les valeurs codées en dur restantes
    print("\n🔍 3. DÉTECTION DES VALEURS CODÉES EN DUR RESTANTES")
    print("-" * 55)
    
    hardcoded_values = extract_hardcoded_values_in_rollouts(file_content)
    
    if not hardcoded_values:
        print("✅ Aucune valeur codée en dur détectée dans les rollouts !")
    else:
        print(f"⚠️  {len(hardcoded_values)} valeurs potentiellement codées en dur détectées :")
        print()
        
        # Grouper par zone
        zones = {}
        for value in hardcoded_values:
            zone = value['zone']
            if zone not in zones:
                zones[zone] = []
            zones[zone].append(value)
        
        for zone, values in zones.items():
            print(f"📍 {zone} ({len(values)} valeurs) :")
            for value in values[:5]:  # Afficher les 5 premières
                print(f"   Ligne {value['line_number']:4d}: {value['matched_value']:<10} | {value['line_content'][:60]}...")
            if len(values) > 5:
                print(f"   ... et {len(values) - 5} autres")
            print()
    
    # 4. Recommandations
    print("\n💡 4. RECOMMANDATIONS")
    print("-" * 25)
    
    if all_sections_present and total_usage > 50 and len(hardcoded_values) < 10:
        print("🏆 EXCELLENT : Configuration bien centralisée !")
        print("✅ Sections présentes")
        print("✅ Bonne utilisation des paramètres")
        print("✅ Peu de valeurs codées en dur")
    elif all_sections_present and total_usage > 20:
        print("👍 BON : Configuration en bonne voie")
        if len(hardcoded_values) > 0:
            print("⚠️  Remplacer les valeurs codées en dur restantes")
    else:
        print("⚠️  AMÉLIORATION NÉCESSAIRE :")
        if not all_sections_present:
            print("❌ Ajouter les sections de configuration manquantes")
        if total_usage < 20:
            print("❌ Augmenter l'utilisation des paramètres centralisés")
        if len(hardcoded_values) > 20:
            print("❌ Réduire significativement les valeurs codées en dur")
    
    # 5. Statistiques finales
    print(f"\n📊 STATISTIQUES FINALES")
    print("-" * 25)
    print(f"Sections config    : {sum(config_sections.values())}/3")
    print(f"Utilisations config: {total_usage}")
    print(f"Valeurs codées dur : {len(hardcoded_values)}")
    
    score = 0
    if all_sections_present:
        score += 40
    score += min(40, total_usage * 2)  # Max 40 points pour utilisation
    score += max(0, 20 - len(hardcoded_values))  # Pénalité pour valeurs codées en dur
    
    print(f"Score global       : {score}/100")
    
    if score >= 90:
        print("🏆 EXCELLENT")
    elif score >= 70:
        print("👍 BON")
    elif score >= 50:
        print("⚠️  ACCEPTABLE")
    else:
        print("❌ NÉCESSITE AMÉLIORATION")

if __name__ == "__main__":
    validate_rollout_configuration()
