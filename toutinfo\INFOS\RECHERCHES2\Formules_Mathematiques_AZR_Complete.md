# Formules Mathématiques Complètes des Modèles AZR

## Table des Matières
1. [Formulation Fondamentale](#formulation-fondamentale)
2. [Fonction Objectif Principale](#fonction-objectif-principale)
3. [Système de Récompenses](#système-de-récompenses)
4. [Algorithme REINFORCE++ Adapté](#algorithme-reinforce-adapté)
5. [Métriques de Qualité](#métriques-de-qualité)
6. [Optimisation et Gradients](#optimisation-et-gradients)
7. [Formules de Complexité](#formules-de-complexité)
8. [Équations de Diversité](#équations-de-diversité)

## 1. Formulation Fondamentale {#formulation-fondamentale}

### Fonction Objectif Principale d'AZR

La fonction objectif centrale du modèle AZR est définie comme :

```
J(θ) = max_θ E_z~p(z) [
    E_(x,y*)~f_e(·|τ),τ~π_θ^propose(·|z) [
        r^propose_e(τ,π_θ) + λ E_y~π_θ^solve(·|x) [r^solve_e(y,y*)]
    ]
]
```

**Où :**
- `θ` : Paramètres du modèle unifié
- `z` : Contexte d'exemples passés
- `p(z)` : Distribution des contextes
- `τ` : Tâche proposée
- `π_θ^propose` : Politique de proposition de tâches
- `π_θ^solve` : Politique de résolution de tâches
- `f_e(·|τ)` : Fonction d'environnement générant (x,y*) à partir de τ
- `r^propose_e` : Récompense de proposition (learnability)
- `r^solve_e` : Récompense de résolution (correctness)
- `λ` : Coefficient d'équilibrage (typiquement λ = 1.0)

### Décomposition des Politiques

**Politique de Proposition :**
```
π_θ^propose(τ|z) = softmax(f_θ^propose(z))
```

**Politique de Résolution :**
```
π_θ^solve(y|x) = softmax(f_θ^solve(x))
```

**Modèle Unifié :**
```
θ = θ_shared ∪ θ_propose ∪ θ_solve
```

## 2. Système de Récompenses {#système-de-récompenses}

### Récompense de Learnability

La récompense de learnability mesure si une tâche est "apprenante" :

```
r^propose_e(τ,π_θ) = I[τ is valid] × learnability_score(τ,π_θ)
```

**Calcul du Score de Learnability :**
```
learnability_score(τ,π_θ) = {
    1.0  si difficulty(τ) ∈ [d_min, d_max] et diversity(τ) > δ_min
    0.5  si difficulty(τ) ∈ [d_min, d_max] et diversity(τ) ≤ δ_min  
    0.0  sinon
}
```

**Paramètres typiques :**
- `d_min = 0.3` (difficulté minimale)
- `d_max = 0.8` (difficulté maximale)
- `δ_min = 0.5` (seuil de diversité)

### Récompense de Correctness

```
r^solve_e(y,y*) = {
    1.0  si y = y* (solution correcte)
    0.0  sinon
}
```

### Récompense Combinée

```
R_total = r^propose_e(τ,π_θ) + λ × r^solve_e(y,y*)
```

## 3. Algorithme REINFORCE++ Adapté {#algorithme-reinforce-adapté}

### Gradient de Politique pour AZR

**Gradient de Proposition :**
```
∇_θ J^propose = E_z,τ [∇_θ log π_θ^propose(τ|z) × (r^propose_e(τ,π_θ) - b^propose)]
```

**Gradient de Résolution :**
```
∇_θ J^solve = E_x,y [∇_θ log π_θ^solve(y|x) × (r^solve_e(y,y*) - b^solve)]
```

**Gradient Total :**
```
∇_θ J = ∇_θ J^propose + λ × ∇_θ J^solve
```

### Baselines pour Réduction de Variance

**Baseline de Proposition :**
```
b^propose = E_τ~π_θ^propose [r^propose_e(τ,π_θ)]
```

**Baseline de Résolution :**
```
b^solve = E_y~π_θ^solve [r^solve_e(y,y*)]
```

### Mise à Jour des Paramètres

```
θ_{t+1} = θ_t + α × ∇_θ J
```

**Où :**
- `α` : Taux d'apprentissage (typiquement 1e-6 à 5e-6)

## 4. Métriques de Qualité {#métriques-de-qualité}

### Difficulté d'une Tâche

**Pour les tâches de Déduction :**
```
difficulty_deduction(τ) = 0.3 × norm_lines(τ) + 0.4 × norm_cyclomatic(τ) + 0.3 × norm_halstead(τ)
```

**Normalisation :**
```
norm_lines(τ) = min(lines_count(τ.program) / 20, 1.0)
norm_cyclomatic(τ) = min(cyclomatic_complexity(τ.program) / 10, 1.0)
norm_halstead(τ) = min(halstead_difficulty(τ.program) / 50, 1.0)
```

### Complexité Cyclomatique

```
CC(P) = E - N + 2P
```

**Où :**
- `E` : Nombre d'arêtes dans le graphe de contrôle
- `N` : Nombre de nœuds
- `P` : Nombre de composants connectés

**Calcul pratique :**
```
CC(P) = 1 + nombre_de_conditions_de_branchement
```

### Métriques de Halstead

**Vocabulaire :**
```
n = n1 + n2
```

**Longueur :**
```
N = N1 + N2
```

**Difficulté :**
```
D = (n1/2) × (N2/n2)
```

**Effort :**
```
E = D × N
```

**Où :**
- `n1` : Nombre d'opérateurs uniques
- `n2` : Nombre d'opérandes uniques
- `N1` : Nombre total d'opérateurs
- `N2` : Nombre total d'opérandes

## 5. Équations de Diversité {#équations-de-diversité}

### Score de Diversité

```
diversity_score(τ_new, T_existing) = {
    1.0                           si |T_existing| = 0
    mean(d(τ_new, τ_i))          sinon
    τ_i ∈ last_k(T_existing)
}
```

**Distance entre Tâches :**
```
d(τ_1, τ_2) = ||features(τ_1) - features(τ_2)||_2
```

### Extraction de Features

**Pour les tâches de programmation :**
```
features(τ) = [
    AST_depth(τ.program),
    num_variables(τ.program),
    num_functions(τ.program),
    num_loops(τ.program),
    num_conditions(τ.program),
    input_complexity(τ.input),
    output_complexity(τ.output)
]
```

### Distance AST (Abstract Syntax Tree)

```
AST_distance(P1, P2) = edit_distance(AST(P1), AST(P2)) / max(|AST(P1)|, |AST(P2)|)
```

## 6. Optimisation et Gradients {#optimisation-et-gradients}

### Fonction de Loss REINFORCE++

```
L(θ) = -E_τ,y [log π_θ(τ,y) × (R(τ,y) - b(τ,y))]
```

**Avec clipping pour stabilité :**
```
L_clipped(θ) = -E_τ,y [min(
    log π_θ(τ,y) × A(τ,y),
    clip(log π_θ(τ,y), 1-ε, 1+ε) × A(τ,y)
)]
```

**Où :**
- `A(τ,y) = R(τ,y) - b(τ,y)` : Avantage
- `ε = 0.2` : Paramètre de clipping

### Régularisation KL

```
L_total(θ) = L_clipped(θ) + β × KL(π_θ || π_θ_old)
```

**Divergence KL :**
```
KL(π_θ || π_θ_old) = E_τ,y [π_θ_old(τ,y) × log(π_θ_old(τ,y) / π_θ(τ,y))]
```

### Gradient Clipping

```
g_clipped = {
    g                    si ||g||_2 ≤ max_norm
    g × max_norm/||g||_2 sinon
}
```

**Typiquement :** `max_norm = 1.0`

## 7. Formules de Complexité {#formules-de-complexité}

### Complexité Temporelle

**Génération de Tâche :**
```
T_propose = O(L_context × d_model + L_task × d_model)
```

**Résolution de Tâche :**
```
T_solve = O(L_task × d_model + L_solution × d_model)
```

**Validation :**
```
T_validate = O(execution_time(program))
```

### Complexité Spatiale

**Mémoire du Modèle :**
```
M_model = O(n_params × sizeof(float))
```

**Buffer de Tâches :**
```
M_buffer = O(buffer_size × (L_task + L_solution))
```

## 8. Métriques d'Évaluation {#métriques-dévaluation}

### Précision (Accuracy)

```
Accuracy = (Nombre de solutions correctes) / (Nombre total de tâches)
```

### Score de Learnability Moyen

```
Avg_Learnability = (1/N) × Σ_{i=1}^N learnability_score(τ_i, π_θ)
```

### Diversité du Buffer

```
Buffer_Diversity = (1/|B|²) × Σ_{i,j∈B, i≠j} d(τ_i, τ_j)
```

### Progression de Difficulté

```
Difficulty_Progression = corr(timestep, difficulty_score)
```

**Où :**
- `corr` : Coefficient de corrélation de Pearson
- `timestep` : Temps d'entraînement
- `difficulty_score` : Score de difficulté moyen des tâches générées

## 9. Hyperparamètres Optimaux {#hyperparamètres-optimaux}

### Paramètres d'Entraînement

```
α = 1e-6        # Taux d'apprentissage
λ = 1.0         # Coefficient d'équilibrage
β = 0.01        # Coefficient de régularisation KL
ε = 0.2         # Paramètre de clipping PPO
γ = 0.99        # Facteur de discount (si applicable)
```

### Paramètres de Qualité

```
d_min = 0.3     # Difficulté minimale
d_max = 0.8     # Difficulté maximale
δ_min = 0.5     # Seuil de diversité minimale
buffer_size = 1000  # Taille du buffer de tâches
```

### Paramètres de Modèle

```
d_model = 4096      # Dimension du modèle
n_layers = 32       # Nombre de couches
n_heads = 32        # Nombre de têtes d'attention
context_length = 2048   # Longueur de contexte
```

## 10. Conditions de Convergence {#conditions-de-convergence}

### Critère d'Arrêt

```
convergence = (|J(θ_t) - J(θ_{t-k})| < ε_conv) ∧ (t > t_min)
```

**Où :**
- `ε_conv = 1e-4` : Seuil de convergence
- `k = 100` : Fenêtre de comparaison
- `t_min = 1000` : Nombre minimal d'itérations

### Stabilité du Buffer

```
buffer_stability = std(difficulty_scores_recent) < σ_max
```

**Où :**
- `σ_max = 0.1` : Écart-type maximal acceptable

Ces formules mathématiques constituent le fondement théorique complet des modèles AZR, permettant une implémentation rigoureuse et une compréhension approfondie du paradigme Absolute Zero Reasoner.
