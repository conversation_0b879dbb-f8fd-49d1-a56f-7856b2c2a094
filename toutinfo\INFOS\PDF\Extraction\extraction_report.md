# RAPPORT D'EXTRACTION PDF
Gén<PERSON><PERSON> le 2025-05-28 10:28:12

## Résumé
- **Fichiers traités**: 23
- **Extractions réussies**: 23
- **Échecs**: 0
- **Tau<PERSON> de ré<PERSON>ite**: 100.0%

## Détails par fichier

### ✅ AI_Arabic_UAE_Guide.pdf
- **Statut**: success
- **Fichier généré**: AI_Arabic_UAE_Guide_extracted.txt (71,565 caractères)

### ✅ AI_Arabic_Yemen_Book.pdf
- **Statut**: success
- **Fichier généré**: AI_Arabic_Yemen_Book_extracted.txt (206,048 caractères)

### ✅ AI_Brazilian_IPEA_Report.pdf
- **Statut**: success
- **Fichier généré**: AI_Brazilian_IPEA_Report_extracted.txt (490,961 caractères)

### ✅ AI_German_Book.pdf
- **Statut**: success
- **Fichier généré**: AI_German_Book_extracted.txt (361,691 caractères)

### ✅ AI_Italian_Parliament_Report.pdf
- **Statut**: success
- **Fichier géné<PERSON>**: AI_Italian_Parliament_Report_extracted.txt (167,121 caractères)

### ✅ AI_Korean_University_Report.pdf
- **Statut**: success
- **Fichier généré**: AI_Korean_University_Report_extracted.txt (114,348 caractères)

### ✅ AlphaZero_MCTS_Reference.pdf
- **Statut**: success
- **Fichier généré**: AlphaZero_MCTS_Reference_extracted.txt (13,956 caractères)

### ✅ AZR_Mathematical_Formulas.pdf
- **Statut**: success
- **Fichier généré**: AZR_Mathematical_Formulas_extracted.txt (171,921 caractères)

### ✅ AZR_Paper_ArXiv.pdf
- **Statut**: success
- **Fichier généré**: AZR_Paper_ArXiv_extracted.txt (171,921 caractères)

### ✅ DeepSeekMath_Benchmarks.pdf
- **Statut**: success
- **Fichier généré**: DeepSeekMath_Benchmarks_extracted.txt (76,287 caractères)

### ✅ Deep_Learning_Russian_Book.pdf
- **Statut**: success
- **Fichier généré**: Deep_Learning_Russian_Book_extracted.txt (803,442 caractères)

### ✅ Deep_RL_Chinese_Book.pdf
- **Statut**: success
- **Fichier généré**: Deep_RL_Chinese_Book_extracted.txt (556,763 caractères)

### ✅ Definitive_Guide_Policy_Gradients.pdf
- **Statut**: success
- **Fichier généré**: Definitive_Guide_Policy_Gradients_extracted.txt (140,753 caractères)

### ✅ Go_AI_Japanese_Lecture.pdf
- **Statut**: success
- **Fichier généré**: Go_AI_Japanese_Lecture_extracted.txt (33,444 caractères)

### ✅ Halstead_Metrics_Research.pdf
- **Statut**: success
- **Fichier généré**: Halstead_Metrics_Research_extracted.txt (6,473 caractères)

### ✅ MCTS_French_Thesis.pdf
- **Statut**: success
- **Fichier généré**: MCTS_French_Thesis_extracted.txt (444,939 caractères)

### ✅ MCTS_Japanese_Thesis.pdf
- **Statut**: success
- **Fichier généré**: MCTS_Japanese_Thesis_extracted.txt (149,660 caractères)

### ✅ PDM_IA_French_Book.pdf
- **Statut**: success
- **Fichier généré**: PDM_IA_French_Book_extracted.txt (890,287 caractères)

### ✅ Policy_Gradient_NIPS.pdf
- **Statut**: success
- **Fichier généré**: Policy_Gradient_NIPS_extracted.txt (20,038 caractères)

### ✅ REINFORCE_Original_Paper.pdf
- **Statut**: success
- **Fichier généré**: REINFORCE_Original_Paper_extracted.txt (20,038 caractères)

### ✅ RL_Russian_Introduction.pdf
- **Statut**: success
- **Fichier généré**: RL_Russian_Introduction_extracted.txt (1,490,668 caractères)

### ✅ RL_Spanish_Thesis.pdf
- **Statut**: success
- **Fichier généré**: RL_Spanish_Thesis_extracted.txt (87,749 caractères)

### ✅ Software_Quality_Metrics_FAA.pdf
- **Statut**: success
- **Fichier généré**: Software_Quality_Metrics_FAA_extracted.txt (504,490 caractères)

