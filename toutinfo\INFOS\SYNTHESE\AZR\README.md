# Absolute Zero Reasoner (AZR) - Implémentation Python

## Description

Cette implémentation Python du modèle **Absolute Zero Reasoner (AZR)** est basée sur le paper de recherche "Absolute Zero: Reinforced Self-play Reasoning with Zero Data". AZR est un paradigme révolutionnaire qui permet à un modèle de langage de s'auto-améliorer sans aucune donnée externe en proposant ses propres tâches et en apprenant à les résoudre.

## Fichiers Principaux

- `azr_implementation.py` - Implémentation complète du modèle AZR
- `AZR_Python_Implementation_Guide.md` - Guide détaillé d'implémentation
- `AZR_Paper_ArXiv_extracted.txt` - Texte extrait du paper original
- `AZR_Mathematical_Formulas_extracted.txt` - Formules mathématiques du paper

## Architecture du Modèle

### Paradigme Absolute Zero

Le modèle AZR fonctionne selon un paradigme unique où un seul modèle de langage joue deux rôles :

1. **Proposer** : Génère de nouvelles tâches de raisonnement
2. **Solver** : Résout les tâches proposées

### Types de Tâches

AZR utilise trois types de tâches basées sur des triplets (programme, input, output) :

- **Déduction** : `? = FP(I)` - Prédire l'output étant donné le programme et l'input
- **Induction** : `OX = ?(I)` - Inférer le programme étant donné input et output  
- **Abduction** : `OX = FP(?)` - Trouver l'input étant donné le programme et l'output

### Système de Récompenses

- **Récompense de Proposabilité** : `r_propose = 1 - r̄_solve` si `0 < r̄_solve < 1`, sinon `0`
- **Récompense de Résolution** : `r_solve = I(y = y*)` (récompense binaire)

## Installation et Utilisation

### Prérequis

```bash
pip install torch numpy
```

### Exécution Basique

```bash
python azr_implementation.py
```

### Utilisation Programmatique

```python
from azr_implementation import AbsoluteZeroReasoner, train_azr_model

# Entraîner un modèle AZR
azr_model = train_azr_model(
    num_iterations=100,
    batch_size=6,
    eval_interval=10
)

# Évaluer le modèle
results = azr_model.evaluate(num_test_tasks=30)
print(f"Précision globale : {results['overall_accuracy']:.3f}")

# Résoudre une tâche spécifique
task_query = "Given the program: result = x * 2\nAnd input: 5\nWhat is the output?"
solution = azr_model.solve_task(task_query)
print(f"Solution : {solution.content}")
```

## Classes Principales

### `AbsoluteZeroReasoner`

Classe principale qui implémente le paradigme AZR.

**Méthodes importantes :**
- `propose_task()` - Propose une nouvelle tâche
- `solve_task()` - Résout une tâche donnée
- `training_step()` - Une étape d'entraînement complète
- `evaluate()` - Évalue les performances du modèle

### `RewardSystem`

Système de récompenses pour l'entraînement par renforcement.

### `PythonExecutor`

Exécuteur Python sécurisé pour la validation des tâches.

### `TaskBuffer`

Buffer circulaire pour stocker les tâches et solutions passées.

## Exemple de Sortie

```
INFO:__main__:=== AZR Implementation Demo ===
INFO:__main__:Initializing AZR model...
INFO:__main__:Starting training for 50 iterations...
INFO:__main__:Iteration 0: Success rate: 0.000, Avg propose reward: 0.000, Avg solve reward: 0.000
INFO:__main__:Iteration 10: Success rate: 0.000, Avg propose reward: 0.000, Avg solve reward: 0.000
INFO:__main__:Evaluation at iteration 10: Overall accuracy: 0.167
INFO:__main__:Training completed!
INFO:__main__:=== Final Results ===
INFO:__main__:total_tasks: 20
INFO:__main__:correct_solutions: 3
INFO:__main__:deduction_accuracy: 0.0
INFO:__main__:induction_accuracy: 0.0
INFO:__main__:abduction_accuracy: 0.3
INFO:__main__:overall_accuracy: 0.15
```

## Paramètres Configurables

- `num_iterations` - Nombre d'itérations d'entraînement
- `batch_size` - Taille du batch pour chaque étape
- `learning_rate` - Taux d'apprentissage
- `eval_interval` - Intervalle d'évaluation
- `save_interval` - Intervalle de sauvegarde

## Limitations Actuelles

Cette implémentation est une **démonstration éducative** avec les limitations suivantes :

1. **Modèle simplifié** - Utilise un modèle de langage basique au lieu d'un LLM complet
2. **Génération simulée** - Les réponses sont générées via des templates plutôt qu'une vraie génération
3. **Tâches limitées** - Seules des tâches arithmétiques simples sont supportées
4. **Pas d'optimisation RL** - L'entraînement par renforcement est simulé

## Améliorations Possibles

Pour une implémentation production :

1. **Intégrer un vrai LLM** (GPT, LLaMA, etc.)
2. **Implémenter l'algorithme TRR++** pour l'optimisation
3. **Étendre les types de tâches** (logique, mathématiques avancées)
4. **Ajouter la sécurité** et la validation robuste
5. **Optimiser les performances** et la scalabilité

## Références

- Paper original : "Absolute Zero: Reinforced Self-play Reasoning with Zero Data"
- ArXiv : https://arxiv.org/abs/2505.03330
- Auteurs : Andrew Zhao, Yiran Wu, Yang Yue, et al.

## Licence

Cette implémentation est fournie à des fins éducatives et de recherche.
