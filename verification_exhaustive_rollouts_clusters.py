#!/usr/bin/env python3
"""
Vérification exhaustive de la centralisation des paramètres rollouts et clusters

Ce script identifie TOUS les paramètres rollout/cluster dans AZRConfig et détecte
les duplications pour s'assurer qu'ils sont bien centralisés dans leurs sections.
"""

import sys
import os
import re
from collections import defaultdict

# Ajouter le répertoire parent au path pour importer le module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyser_fichier_azr():
    """Analyse le fichier azr_baccarat_predictor.py pour trouver tous les paramètres rollout/cluster"""
    
    print("🔍 ANALYSE EXHAUSTIVE DES PARAMÈTRES ROLLOUTS ET CLUSTERS")
    print("=" * 80)
    
    # Lire le fichier
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            contenu = f.read()
            lignes = contenu.split('\n')
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return False
    
    # Patterns pour identifier les paramètres rollout/cluster
    patterns_rollout_cluster = [
        r'rollout\d*_\w+',
        r'cluster_\w+',
        r'n_rollouts',
        r'rollout_temperature',
        r'rollout_step_size',
        r'rollout_random_factor',
        r'parallel_rollouts'
    ]
    
    # Dictionnaire pour stocker les occurrences
    occurrences = defaultdict(list)
    
    # Analyser chaque ligne
    for num_ligne, ligne in enumerate(lignes, 1):
        ligne_clean = ligne.strip()
        
        # Ignorer les commentaires purs et lignes vides
        if ligne_clean.startswith('#') or not ligne_clean:
            continue
            
        # Chercher les patterns
        for pattern in patterns_rollout_cluster:
            matches = re.findall(pattern, ligne_clean)
            for match in matches:
                # Éviter les faux positifs dans les commentaires
                if not ligne_clean.startswith('#'):
                    occurrences[match].append({
                        'ligne': num_ligne,
                        'contenu': ligne_clean,
                        'type': 'definition' if ':' in ligne_clean and '=' in ligne_clean else 'usage'
                    })
    
    return occurrences

def identifier_duplications(occurrences):
    """Identifie les paramètres dupliqués"""
    
    print("\n📊 ANALYSE DES DUPLICATIONS")
    print("=" * 50)
    
    duplications = {}
    definitions_multiples = {}
    
    for param, occ_list in occurrences.items():
        # Compter les définitions (lignes avec : et =)
        definitions = [occ for occ in occ_list if occ['type'] == 'definition']
        usages = [occ for occ in occ_list if occ['type'] == 'usage']
        
        if len(definitions) > 1:
            definitions_multiples[param] = definitions
            
        if len(occ_list) > 1:
            duplications[param] = {
                'total_occurrences': len(occ_list),
                'definitions': len(definitions),
                'usages': len(usages),
                'occurrences': occ_list
            }
    
    return duplications, definitions_multiples

def afficher_resultats(occurrences, duplications, definitions_multiples):
    """Affiche les résultats de l'analyse"""
    
    print(f"\n📈 STATISTIQUES GLOBALES")
    print("=" * 30)
    print(f"Total paramètres rollout/cluster trouvés: {len(occurrences)}")
    print(f"Paramètres avec duplications: {len(duplications)}")
    print(f"Paramètres avec définitions multiples: {len(definitions_multiples)}")
    
    # Afficher les définitions multiples (PROBLÈME CRITIQUE)
    if definitions_multiples:
        print(f"\n❌ DÉFINITIONS MULTIPLES DÉTECTÉES (CRITIQUE)")
        print("=" * 60)
        
        for param, definitions in definitions_multiples.items():
            print(f"\n🔴 {param} - {len(definitions)} définitions:")
            for i, defin in enumerate(definitions, 1):
                print(f"   {i}. Ligne {defin['ligne']}: {defin['contenu'][:80]}...")
    
    # Afficher les duplications importantes
    duplications_importantes = {k: v for k, v in duplications.items() 
                              if v['total_occurrences'] > 5}
    
    if duplications_importantes:
        print(f"\n⚠️  DUPLICATIONS IMPORTANTES (>5 occurrences)")
        print("=" * 60)
        
        for param, info in duplications_importantes.items():
            print(f"\n🟡 {param} - {info['total_occurrences']} occurrences "
                  f"({info['definitions']} définitions, {info['usages']} usages)")
            
            # Afficher quelques exemples
            for i, occ in enumerate(info['occurrences'][:3], 1):
                print(f"   {i}. Ligne {occ['ligne']} ({occ['type']}): {occ['contenu'][:60]}...")
            
            if len(info['occurrences']) > 3:
                print(f"   ... et {len(info['occurrences']) - 3} autres occurrences")

def verifier_sections_organisees():
    """Vérifie que les paramètres sont dans les bonnes sections"""
    
    print(f"\n🏗️ VÉRIFICATION DES SECTIONS ORGANISÉES")
    print("=" * 50)
    
    try:
        from azr_baccarat_predictor import AZRConfig
        config = AZRConfig()
        
        # Sections attendues
        sections_attendues = {
            'ROLLOUT 1': [
                'rollout1_analysis_time_ms', 'rollout1_min_hands_quality',
                'rollout1_combined_pair_sync_influence', 'rollout1_step_increment'
            ],
            'ROLLOUT 2': [
                'rollout2_generation_time_ms', 'rollout2_candidates_count',
                'rollout2_max_probability', 'rollout2_fixed_length'
            ],
            'ROLLOUT 3': [
                'rollout3_prediction_time_ms', 'rollout3_default_confidence',
                'rollout3_excellent_threshold', 'rollout3_fixed_length'
            ],
            'CLUSTERS': [
                'cluster_analysis_time_ms', 'cluster_rollout1_weight',
                'cluster_pattern_specializations'
            ],
            'ROLLOUTS GÉNÉRAUX': [
                'n_rollouts', 'rollout_temperature', 'rollout_step_size',
                'parallel_rollouts'
            ]
        }
        
        sections_completes = 0
        for section, parametres in sections_attendues.items():
            parametres_presents = sum(1 for param in parametres if hasattr(config, param))
            pourcentage = (parametres_presents / len(parametres)) * 100
            
            if parametres_presents == len(parametres):
                print(f"✅ {section}: {parametres_presents}/{len(parametres)} ({pourcentage:.0f}%)")
                sections_completes += 1
            else:
                print(f"❌ {section}: {parametres_presents}/{len(parametres)} ({pourcentage:.0f}%)")
                
                # Afficher les paramètres manquants
                manquants = [param for param in parametres if not hasattr(config, param)]
                print(f"   Manquants: {', '.join(manquants)}")
        
        print(f"\n📊 RÉSULTAT: {sections_completes}/{len(sections_attendues)} sections complètes")
        return sections_completes == len(sections_attendues)
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def recommandations_nettoyage(definitions_multiples):
    """Génère des recommandations pour nettoyer les duplications"""
    
    if not definitions_multiples:
        print(f"\n✅ AUCUNE DUPLICATION CRITIQUE DÉTECTÉE")
        return
    
    print(f"\n🔧 RECOMMANDATIONS DE NETTOYAGE")
    print("=" * 40)
    
    for param, definitions in definitions_multiples.items():
        print(f"\n🔴 {param}:")
        print(f"   ❌ Supprimer {len(definitions) - 1} définition(s) dupliquée(s)")
        
        # Identifier la définition à garder (dans les sections organisées)
        for defin in definitions:
            ligne_num = defin['ligne']
            if 490 <= ligne_num <= 730:  # Zone des sections organisées
                print(f"   ✅ Garder la définition ligne {ligne_num} (section organisée)")
            else:
                print(f"   ❌ Supprimer la définition ligne {ligne_num}")

def main():
    """Fonction principale"""
    
    # Analyser le fichier
    occurrences = analyser_fichier_azr()
    if not occurrences:
        return False
    
    # Identifier les duplications
    duplications, definitions_multiples = identifier_duplications(occurrences)
    
    # Afficher les résultats
    afficher_resultats(occurrences, duplications, definitions_multiples)
    
    # Vérifier les sections organisées
    sections_ok = verifier_sections_organisees()
    
    # Recommandations
    recommandations_nettoyage(definitions_multiples)
    
    # Résultat final
    print(f"\n" + "=" * 80)
    print("📊 RÉSULTAT FINAL")
    print("=" * 80)
    
    if not definitions_multiples and sections_ok:
        print("🎉 PARFAIT! Aucune duplication critique et sections complètes")
        return True
    else:
        print("⚠️  CORRECTIONS NÉCESSAIRES:")
        if definitions_multiples:
            print(f"   ❌ {len(definitions_multiples)} paramètres avec définitions multiples")
        if not sections_ok:
            print(f"   ❌ Sections organisées incomplètes")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
