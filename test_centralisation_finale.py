#!/usr/bin/env python3
"""
Test final de la centralisation des paramètres rollouts et clusters

Ce test vérifie que tous les paramètres centralisés sont bien accessibles
et qu'il n'y a plus de valeurs hardcodées critiques.
"""

import sys
import os

# Ajouter le répertoire parent au path pour importer le module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_centralisation_complete():
    """Test complet de la centralisation"""
    
    print("🧪 TEST FINAL DE LA CENTRALISATION")
    print("=" * 60)
    
    try:
        from azr_baccarat_predictor import AZRConfig
        config = AZRConfig()
        
        # Test des paramètres Rollout 1
        rollout1_params = [
            'rollout1_impair_consecutive_common',
            'rollout1_impair_consecutive_rare', 
            'rollout1_impair_consecutive_very_rare',
            'rollout1_pair_consecutive_very_common',
            'rollout1_pair_consecutive_common',
            'rollout1_global_strength_threshold',
            'rollout1_phase_early_ratio',
            'rollout1_phase_middle_ratio'
        ]
        
        # Test des paramètres Rollout 2
        rollout2_params = [
            'rollout2_exploitation_threshold_high',
            'rollout2_exploitation_threshold_medium',
            'rollout2_cross_impact_ratio_threshold',
            'rollout2_cross_impact_confidence_boost',
            'rollout2_cross_impact_max_confidence',
            'rollout2_transition_signal_threshold',
            'rollout2_sync_rate_enriched_threshold',
            'rollout2_so_bias_strong_threshold',
            'rollout2_veto_sync_threshold',
            'rollout2_veto_combined_threshold',
            'rollout2_global_strength_high_threshold',
            'rollout2_global_strength_boost_factor'
        ]
        
        # Test des paramètres Rollout 3
        rollout3_params = [
            'rollout3_cluster_confidence_default',
            'rollout3_neutral_evaluation_value',
            'rollout3_cluster_confidence_threshold_high',
            'rollout3_cluster_confidence_threshold_medium',
            'rollout3_cluster_confidence_threshold_low'
        ]
        
        # Test des paramètres Clusters
        cluster_params = [
            'cluster_confidence_sequence_weight',
            'cluster_confidence_evaluation_weight',
            'cluster_confidence_analysis_weight',
            'cluster_confidence_signals_weight',
            'cluster_exploitation_bonus',
            'cluster_high_alert_bonus',
            'cluster_low_alert_malus',
            'cluster_consensus_agreement_threshold',
            'cluster_accuracy_save_threshold',
            'cluster_test_accuracy_high_threshold',
            'cluster_test_accuracy_low_threshold',
            'cluster_confidence_threshold_adjustment',
            'cluster_accuracy_variability_threshold',
            'cluster_desync_bias_weak_threshold'
        ]
        
        # Tester chaque catégorie
        categories = {
            'ROLLOUT 1': rollout1_params,
            'ROLLOUT 2': rollout2_params,
            'ROLLOUT 3': rollout3_params,
            'CLUSTERS': cluster_params
        }
        
        total_params = 0
        params_ok = 0
        
        for categorie, params in categories.items():
            print(f"\n🔍 {categorie}")
            print("-" * 30)
            
            categorie_ok = 0
            for param in params:
                total_params += 1
                if hasattr(config, param):
                    value = getattr(config, param)
                    print(f"  ✅ {param}: {value}")
                    categorie_ok += 1
                    params_ok += 1
                else:
                    print(f"  ❌ {param}: MANQUANT")
            
            pourcentage = (categorie_ok / len(params)) * 100
            print(f"  📊 {categorie}: {categorie_ok}/{len(params)} ({pourcentage:.0f}%)")
        
        # Résultat global
        pourcentage_global = (params_ok / total_params) * 100
        print(f"\n" + "=" * 60)
        print(f"📊 RÉSULTAT GLOBAL")
        print("=" * 60)
        print(f"✅ Paramètres centralisés: {params_ok}/{total_params} ({pourcentage_global:.1f}%)")
        
        if params_ok == total_params:
            print("🎉 CENTRALISATION PARFAITEMENT RÉUSSIE!")
            print("🏆 Tous les paramètres rollouts et clusters sont centralisés!")
            return True
        else:
            print("⚠️  Centralisation incomplète")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def test_proprietes_dynamiques():
    """Test des propriétés dynamiques"""
    
    print(f"\n🔧 TEST DES PROPRIÉTÉS DYNAMIQUES")
    print("=" * 40)
    
    try:
        from azr_baccarat_predictor import AZRConfig
        config = AZRConfig()
        
        proprietes = [
            'rollout2_rewards',
            'rollout3_rewards', 
            'cluster_reward_weights',
            'confidence_calibration'
        ]
        
        proprietes_ok = 0
        for prop in proprietes:
            if hasattr(config, prop):
                try:
                    value = getattr(config, prop)
                    if isinstance(value, dict) and len(value) > 0:
                        print(f"  ✅ {prop}: {len(value)} éléments")
                        proprietes_ok += 1
                    else:
                        print(f"  ⚠️  {prop}: Dictionnaire vide")
                except Exception as e:
                    print(f"  ❌ {prop}: Erreur - {e}")
            else:
                print(f"  ❌ {prop}: MANQUANT")
        
        pourcentage = (proprietes_ok / len(proprietes)) * 100
        print(f"\n📊 Propriétés dynamiques: {proprietes_ok}/{len(proprietes)} ({pourcentage:.0f}%)")
        
        return proprietes_ok == len(proprietes)
        
    except Exception as e:
        print(f"❌ Erreur lors du test des propriétés: {e}")
        return False

def main():
    """Fonction principale"""
    
    # Test de centralisation
    centralisation_ok = test_centralisation_complete()
    
    # Test des propriétés dynamiques
    proprietes_ok = test_proprietes_dynamiques()
    
    # Résultat final
    print(f"\n" + "=" * 80)
    print("🏁 RÉSULTAT FINAL DU TEST")
    print("=" * 80)
    
    if centralisation_ok and proprietes_ok:
        print("🎉 SUCCÈS TOTAL!")
        print("✅ Centralisation complète des paramètres rollouts et clusters")
        print("✅ Propriétés dynamiques fonctionnelles")
        print("🚀 Le système AZR est parfaitement organisé!")
        return True
    else:
        print("⚠️  CORRECTIONS NÉCESSAIRES:")
        if not centralisation_ok:
            print("   ❌ Centralisation incomplète")
        if not proprietes_ok:
            print("   ❌ Propriétés dynamiques défaillantes")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
