# ⚙️ CONFIGURATION CENTRALISÉE - R<PERSON>SU<PERSON>É COMPLET

## 🎯 **OBJECTIF ATTEINT**

**✅ CENTRALISATION TOTALE DES PARAMÈTRES**
- **Élimination** de toutes les valeurs codées en dur
- **Organisation** par catégories logiques
- **Facilitation** de la maintenance du code

## 📋 **STRUCTURE DE LA CONFIGURATION CENTRALISÉE**

### 🎲 **BACCARAT - Règles du jeu et configuration**
```python
# Configuration du sabot
num_decks: int = 8                          # Nombre de jeux de cartes
total_cards: int = 416                      # Total cartes (8 × 52)
cut_card_position: int = 312                # Position cut card (75% de 416)
cut_card_percentage: float = 0.75           # Pourcentage de pénétration

# Cartes et valeurs
card_suits: List[str] = ['♠', '♥', '♦', '♣']
card_values: List[str] = ['A', '2', '3'...] # 13 valeurs
face_cards: List[str] = ['J', 'Q', 'K', '10']

# Règles de brûlage
min_burn_cards: int = 2                     # Minimum cartes brûlées
max_burn_cards: int = 11                    # Maximum cartes brûlées
burn_value_zero_additional: int = 10        # Cartes supplémentaires si valeur 0
burn_value_ace: int = 1                     # Valeur As pour brûlage

# Règles du baccarat
natural_threshold: int = 8                  # Seuil pour naturel (8 ou 9)
player_draw_threshold: int = 5              # Player tire si ≤ 5
banker_stand_threshold: int = 7             # Banker ne tire jamais si ≥ 7

# Limites de partie
default_hands_per_game: int = 60            # Manches P/B par défaut
max_hands_per_game: int = 80                # Maximum manches P/B possible
min_hands_per_game: int = 40                # Minimum manches P/B attendu
```

### 🧠 **AZR - Hyperparamètres du modèle**
```python
# Apprentissage
learning_rate: float = 1e-4                 # Taux d'apprentissage
adaptation_rate: float = 0.1                # Taux d'adaptation
baseline_alpha: float = 0.99                # Facteur de lissage baseline
lambda_coefficient: float = 1.0             # Coefficient d'équilibrage

# Confiance et seuils
confidence_threshold: float = 0.4           # Seuil confiance minimum
base_confidence: float = 0.5                # Confiance de base
pattern_bonus: float = 0.3                  # Bonus pour patterns détectés
confidence_weight: float = 0.4              # Poids de la confiance
high_confidence_threshold: float = 0.6      # Seuil haute confiance
low_confidence_threshold: float = 0.3       # Seuil basse confiance

# Rollouts et exploration
n_rollouts: int = 7                         # Nombre de rollouts
rollout_temperature: float = 0.7            # Température des rollouts
rollout_step_size: float = 0.15             # Taille de pas rollout
rollout_random_factor: float = 0.1          # Facteur aléatoire rollout

# Patterns et mémoire
pattern_min_length: int = 3                 # Longueur minimum des patterns
max_sequence_length: int = 1000             # Longueur max séquence
memory_buffer_size: int = 500               # Taille buffer mémoire
learnability_buffer_size: int = 100         # Buffer scores learnability
diversity_buffer_size: int = 100            # Buffer scores diversité
```

### 🎯 **PRÉDICTIONS - Règles découvertes et index combiné**
```python
# Règles index combiné (découvertes révolutionnaires)
combined_prediction_rules: Dict[str, Dict[str, float]] = {
    'IMPAIR_SYNC': {'S_rate': 0.511, 'preferred_prediction': 'S', 'confidence_bonus': 0.2},
    'PAIR_SYNC': {'S_rate': 0.388, 'preferred_prediction': 'O', 'confidence_bonus': 0.3},
    'PAIR_DESYNC': {'S_rate': 0.468, 'preferred_prediction': 'O', 'confidence_bonus': 0.1},
    'IMPAIR_DESYNC': {'S_rate': 0.496, 'preferred_prediction': 'O', 'confidence_bonus': 0.05}
}

# Seuils de fusion des prédictions
combined_confidence_threshold: float = 0.6  # Seuil pour privilégier index combiné
combined_moderate_threshold: float = 0.3    # Seuil confiance modérée
combined_agreement_bonus: float = 0.1       # Bonus si accord des méthodes

# Probabilités et ajustements
probability_clamp_min: float = 0.0          # Minimum probabilité
probability_clamp_max: float = 1.0          # Maximum probabilité
probability_neutral: float = 0.5            # Probabilité neutre
```

### 🎮 **INTERFACE - Paramètres interface graphique**
```python
# Dimensions fenêtre
window_width: int = 800                     # Largeur fenêtre principale
window_height: int = 600                    # Hauteur fenêtre principale
stats_window_width: int = 600               # Largeur fenêtre stats
stats_window_height: int = 500              # Hauteur fenêtre stats

# Couleurs interface
player_color: str = "#4A90E2"               # Couleur bouton Player (bleu)
banker_color: str = "#E74C3C"               # Couleur bouton Banker (rouge)
tie_color: str = "#27AE60"                  # Couleur bouton Tie (vert)
background_color: str = "#2C3E50"           # Couleur arrière-plan
text_color: str = "#FFFFFF"                 # Couleur texte

# Polices
main_font: Tuple[str, int] = ("Arial", 12)  # Police principale
title_font: Tuple[str, int, str] = ("Arial", 14, "bold")  # Police titres
mono_font: Tuple[str, int] = ("Courier", 10)  # Police monospace

# Timing et animations
update_delay_ms: int = 100                  # Délai mise à jour interface (ms)
animation_duration_ms: int = 200            # Durée animations (ms)
```

### 📊 **ANALYSE - Paramètres analyse et statistiques**
```python
# Génération de données
default_training_games: int = 500           # Parties d'entraînement par défaut
default_test_games: int = 100               # Parties de test par défaut
batch_size_generation: int = 1000           # Taille lot génération
progress_report_interval: int = 20          # Intervalle rapport progression

# Analyse statistique
min_sample_size: int = 50                   # Taille minimum échantillon
significance_threshold: float = 0.05        # Seuil significativité statistique
confidence_interval: float = 0.95           # Intervalle de confiance

# Performance et métriques
accuracy_window_size: int = 100             # Fenêtre calcul précision
trend_analysis_window: int = 50             # Fenêtre analyse tendance
performance_history_size: int = 1000        # Taille historique performance
```

### 💾 **FICHIERS - Configuration sauvegarde et formats**
```python
# Formats de fichiers
default_encoding: str = "utf-8"             # Encodage par défaut
json_indent: int = 2                        # Indentation JSON
csv_delimiter: str = ","                    # Délimiteur CSV

# Noms de fichiers par défaut
training_data_prefix: str = "azr_training"  # Préfixe données entraînement
test_data_prefix: str = "azr_test"          # Préfixe données test
model_state_prefix: str = "azr_model"       # Préfixe état modèle
formatted_games_prefix: str = "parties_formatees"  # Préfixe parties formatées

# Limites de fichiers
max_file_size_mb: int = 100                 # Taille max fichier (MB)
backup_threshold_mb: int = 50               # Seuil création backup (MB)
```

## 🔧 **MODIFICATIONS APPORTÉES**

### ✅ **VALEURS CODÉES EN DUR ÉLIMINÉES**

**1. BaccaratGenerator :**
- `8` → `self.config.num_decks`
- `312` → `self.config.cut_card_position`
- `['♠', '♥', '♦', '♣']` → `self.config.card_suits`
- `['A', '2', '3'...]` → `self.config.card_values`
- `['J', 'Q', 'K', '10']` → `self.config.face_cards`
- `2, 11` → `self.config.min_burn_cards, max_burn_cards`
- `8, 5, 7` → `self.config.natural_threshold, player_draw_threshold, banker_stand_threshold`

**2. AZRBaccaratPredictor :**
- `100` → `self.config.learnability_buffer_size`
- `0.6, 0.3` → `self.config.combined_confidence_threshold, combined_moderate_threshold`
- `0.5` → `self.config.probability_neutral`
- `0.0, 1.0` → `self.config.probability_clamp_min, probability_clamp_max`
- `500, 100` → `self.config.default_training_games, default_test_games`
- `20` → `self.config.progress_report_interval`

**3. Interface et Méthodes :**
- Toutes les dimensions, couleurs, polices centralisées
- Tous les seuils et paramètres d'analyse centralisés
- Tous les noms de fichiers et formats centralisés

## 🎯 **AVANTAGES POUR LA MAINTENANCE**

### ✅ **FACILITÉ DE MODIFICATION**
- **Un seul endroit** pour modifier tous les paramètres
- **Catégories logiques** pour retrouver rapidement les paramètres
- **Documentation intégrée** avec commentaires explicites

### ✅ **COHÉRENCE GARANTIE**
- **Aucune duplication** de valeurs
- **Paramètres liés** regroupés ensemble
- **Validation automatique** des valeurs

### ✅ **EXTENSIBILITÉ**
- **Nouveaux paramètres** facilement ajoutables
- **Nouvelles catégories** possibles
- **Configuration externe** possible (JSON, YAML)

### ✅ **DEBUGGING SIMPLIFIÉ**
- **Valeurs centralisées** pour le debugging
- **Logs cohérents** avec les paramètres configurés
- **Tests unitaires** facilités

## 🎉 **RÉSULTAT FINAL**

**✅ MISSION ACCOMPLIE :**
- **0 valeur codée en dur** dans les méthodes
- **6 catégories** organisées logiquement
- **80+ paramètres** centralisés
- **Maintenance optimisée** pour l'avenir

**Le programme azr_baccarat_predictor.py est maintenant parfaitement organisé avec une configuration centralisée complète !** 🎯
