#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test de Validation de la Transition - Rollout 2

Ce script teste que le Rollout 2 gère parfaitement la transition :
- Dernier P/B historique → Séquences P/B générées → Conversion S/O
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import AZRCluster, AZRConfig

def test_transition_validation():
    """Test de validation de la transition complète"""
    
    print("🔄 TEST VALIDATION TRANSITION - ROLLOUT 2")
    print("=" * 45)
    
    # Initialisation
    config = AZRConfig()
    cluster = AZRCluster(cluster_id=1, config=config)
    
    print("✅ Configuration et cluster initialisés")
    
    # ========================================================================
    # TEST 1 : TRANSITION AVEC HISTORIQUE 'B'
    # ========================================================================
    
    print("\n📊 TEST 1 : Transition avec historique se terminant par 'B'")
    print("-" * 55)
    
    # Rapport avec historique se terminant par 'B'
    analyzer_report_b = {
        'signals_summary': {
            'top_signals': [
                {'signal_name': 'TEST_SIGNAL', 'signal_type': 'pb_prediction', 'strength': 0.8, 'confidence': 0.85, 'strategy': 'test'}
            ],
            'exploitation_ready': True,
            'overall_confidence': 0.8
        },
        'generation_guidance': {
            'primary_focus': 'test_patterns',
            'optimal_sequence_length': 3,
            'confidence_thresholds': {'high': 0.7, 'medium': 0.6, 'low': 0.5},
            'exploitation_strategy': 'moderate',
            'risk_level': 'low'
        },
        'quick_access': {
            'current_state': 'TEST_STATE',
            'next_prediction_pb': 'P',
            'next_prediction_so': 'O',  # O car B→P = Opposite
            'prediction_confidence': 0.85,
            'alert_level': 'HIGH',
            'exploitation_ready': True
        },
        'indices_analysis': {
            'pbt': {'pbt_sequence': ['P', 'B', 'P', 'T', 'B']},  # Dernier P/B = 'B'
            'impair_pair': {'position_types': ['IMPAIR', 'PAIR', 'IMPAIR', 'PAIR', 'IMPAIR']},
            'desync_sync': {'sync_sequence': ['SYNC', 'DESYNC', 'SYNC', 'DESYNC', 'SYNC']},
            'combined': {'combined_sequence': ['IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC']}
        },
        'synthesis': {'analysis_quality': 0.8},
        'sequence_metadata': {'total_hands_analyzed': 50}
    }
    
    # Vérifier la récupération du dernier historique
    last_pb_b = cluster._get_last_historical_pb_result(analyzer_report_b)
    print(f"📋 Dernier P/B historique extrait : '{last_pb_b}'")
    print(f"📋 Historique complet : {analyzer_report_b['indices_analysis']['pbt']['pbt_sequence']}")
    
    # Générer les séquences
    sequences_b = cluster._rollout_generator(analyzer_report_b)
    actual_sequences_b = sequences_b.get('sequences', []) if isinstance(sequences_b, dict) else sequences_b
    
    print(f"📋 Nombre de séquences générées : {len(actual_sequences_b)}")
    
    # Analyser chaque séquence
    for i, sequence in enumerate(actual_sequences_b):
        if isinstance(sequence, dict):
            pb_data = sequence.get('sequence_data_pb', [])
            so_data = sequence.get('sequence_data_so', [])
            last_historical = sequence.get('last_historical_pb', 'N/A')
            
            print(f"\n   Séquence {i+1} :")
            print(f"      Historique utilisé : '{last_historical}'")
            print(f"      P/B générée : {pb_data}")
            print(f"      S/O convertie : {so_data}")
            
            # Vérifier la transition
            if pb_data and so_data and last_historical:
                expected_first_so = 'S' if pb_data[0] == last_historical else 'O'
                actual_first_so = so_data[0] if so_data else 'N/A'
                
                print(f"      Transition : '{last_historical}' → '{pb_data[0]}' = '{expected_first_so}'")
                print(f"      Résultat : '{actual_first_so}' {'✅' if actual_first_so == expected_first_so else '❌'}")
    
    # ========================================================================
    # TEST 2 : TRANSITION AVEC HISTORIQUE 'P'
    # ========================================================================
    
    print("\n📊 TEST 2 : Transition avec historique se terminant par 'P'")
    print("-" * 55)
    
    # Rapport avec historique se terminant par 'P'
    analyzer_report_p = analyzer_report_b.copy()
    analyzer_report_p['indices_analysis']['pbt']['pbt_sequence'] = ['B', 'P', 'B', 'T', 'P']  # Dernier P/B = 'P'
    analyzer_report_p['quick_access']['next_prediction_so'] = 'S'  # S car P→P = Same
    
    # Vérifier la récupération du dernier historique
    last_pb_p = cluster._get_last_historical_pb_result(analyzer_report_p)
    print(f"📋 Dernier P/B historique extrait : '{last_pb_p}'")
    print(f"📋 Historique complet : {analyzer_report_p['indices_analysis']['pbt']['pbt_sequence']}")
    
    # Générer les séquences
    sequences_p = cluster._rollout_generator(analyzer_report_p)
    actual_sequences_p = sequences_p.get('sequences', []) if isinstance(sequences_p, dict) else sequences_p
    
    print(f"📋 Nombre de séquences générées : {len(actual_sequences_p)}")
    
    # Analyser chaque séquence
    for i, sequence in enumerate(actual_sequences_p):
        if isinstance(sequence, dict):
            pb_data = sequence.get('sequence_data_pb', [])
            so_data = sequence.get('sequence_data_so', [])
            last_historical = sequence.get('last_historical_pb', 'N/A')
            
            print(f"\n   Séquence {i+1} :")
            print(f"      Historique utilisé : '{last_historical}'")
            print(f"      P/B générée : {pb_data}")
            print(f"      S/O convertie : {so_data}")
            
            # Vérifier la transition
            if pb_data and so_data and last_historical:
                expected_first_so = 'S' if pb_data[0] == last_historical else 'O'
                actual_first_so = so_data[0] if so_data else 'N/A'
                
                print(f"      Transition : '{last_historical}' → '{pb_data[0]}' = '{expected_first_so}'")
                print(f"      Résultat : '{actual_first_so}' {'✅' if actual_first_so == expected_first_so else '❌'}")
    
    # ========================================================================
    # TEST 3 : COHÉRENCE AVEC LES PRÉDICTIONS S/O
    # ========================================================================
    
    print("\n📊 TEST 3 : Cohérence avec les prédictions S/O")
    print("-" * 45)
    
    # Vérifier que les séquences générées sont cohérentes avec next_prediction_so
    
    print("📋 Avec historique 'B' et prédiction S/O = 'O' :")
    coherent_sequences_b = 0
    for sequence in actual_sequences_b:
        if isinstance(sequence, dict):
            so_data = sequence.get('sequence_data_so', [])
            if so_data and so_data[0] == 'O':
                coherent_sequences_b += 1
    
    print(f"   Séquences cohérentes : {coherent_sequences_b}/{len(actual_sequences_b)}")
    
    print("📋 Avec historique 'P' et prédiction S/O = 'S' :")
    coherent_sequences_p = 0
    for sequence in actual_sequences_p:
        if isinstance(sequence, dict):
            so_data = sequence.get('sequence_data_so', [])
            if so_data and so_data[0] == 'S':
                coherent_sequences_p += 1
    
    print(f"   Séquences cohérentes : {coherent_sequences_p}/{len(actual_sequences_p)}")
    
    # ========================================================================
    # TEST 4 : VALIDATION DE LA MÉTHODE DE CONVERSION
    # ========================================================================
    
    print("\n📊 TEST 4 : Validation méthode de conversion")
    print("-" * 45)
    
    # Test direct de la méthode de conversion
    test_cases = [
        (['P', 'P', 'B'], 'B', ['O', 'S', 'O']),  # B→P=O, P→P=S, P→B=O
        (['P', 'P', 'B'], 'P', ['S', 'S', 'O']),  # P→P=S, P→P=S, P→B=O
        (['B', 'P', 'P'], 'B', ['S', 'O', 'S']),  # B→B=S, B→P=O, P→P=S
        (['B', 'P', 'P'], 'P', ['O', 'O', 'S']),  # P→B=O, B→P=O, P→P=S
    ]
    
    print("📋 Tests de conversion directe :")
    for pb_seq, last_hist, expected_so in test_cases:
        actual_so = cluster._convert_pb_sequence_to_so_with_history(pb_seq, last_hist)
        match = actual_so == expected_so
        
        print(f"   {pb_seq} + '{last_hist}' → {actual_so} (attendu: {expected_so}) {'✅' if match else '❌'}")
    
    # ========================================================================
    # VALIDATION FINALE
    # ========================================================================
    
    print("\n✅ VALIDATION FINALE")
    print("-" * 20)
    
    # Critères de validation
    correct_extraction_b = last_pb_b == 'B'
    correct_extraction_p = last_pb_p == 'P'
    sequences_generated = len(actual_sequences_b) > 0 and len(actual_sequences_p) > 0
    
    print(f"📊 Extraction historique 'B' : {'✅' if correct_extraction_b else '❌'}")
    print(f"📊 Extraction historique 'P' : {'✅' if correct_extraction_p else '❌'}")
    print(f"📊 Séquences générées : {'✅' if sequences_generated else '❌'}")
    print(f"📊 Cohérence prédictions S/O : {'✅' if coherent_sequences_b > 0 and coherent_sequences_p > 0 else '❌'}")
    
    # Test de conversion directe
    all_conversions_correct = all(
        cluster._convert_pb_sequence_to_so_with_history(pb_seq, last_hist) == expected_so
        for pb_seq, last_hist, expected_so in test_cases
    )
    print(f"📊 Conversions directes : {'✅' if all_conversions_correct else '❌'}")
    
    # ========================================================================
    # CONCLUSION
    # ========================================================================
    
    print("\n🎉 CONCLUSION")
    print("-" * 15)
    
    if all([correct_extraction_b, correct_extraction_p, sequences_generated, all_conversions_correct]):
        print("✅ TRANSITION PARFAITEMENT VALIDÉE !")
        print("✅ Le Rollout 2 gère parfaitement :")
        print("   • Récupération du dernier P/B historique")
        print("   • Génération des séquences P/B")
        print("   • Conversion immédiate en S/O")
        print("   • Cohérence avec les prédictions")
        print("   • Transition historique → séquence → S/O")
    else:
        print("⚠️ Problèmes détectés dans la transition")
    
    print(f"\n🎯 PROCESSUS DE TRANSITION :")
    print(f"1. Historique P/B extrait automatiquement")
    print(f"2. Séquences P/B générées (longueur 3)")
    print(f"3. Conversion immédiate P/B → S/O")
    print(f"4. Rollout 3 reçoit séquences S/O finales")

if __name__ == "__main__":
    test_transition_validation()
