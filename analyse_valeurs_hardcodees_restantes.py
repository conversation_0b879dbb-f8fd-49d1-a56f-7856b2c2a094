#!/usr/bin/env python3
"""
Analyse précise des valeurs hardcodées restantes (excluant celles déjà dans AZRConfig)

Ce script identifie uniquement les valeurs hardcodées qui ne sont PAS déjà centralisées
dans la configuration AZRConfig.
"""

import re
import sys
import os

def extraire_valeurs_config():
    """Extrait toutes les valeurs définies dans AZRConfig"""
    valeurs_config = set()
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        # Trouver la classe AZRConfig
        match_config = re.search(r'class AZRConfig.*?(?=class|\Z)', contenu, re.DOTALL)
        if not match_config:
            print("❌ Classe AZRConfig non trouvée")
            return valeurs_config
        
        config_content = match_config.group(0)
        
        # Extraire toutes les valeurs numériques définies
        pattern_valeurs = r':\s*float\s*=\s*([0-9]*\.?[0-9]+)'
        matches = re.findall(pattern_valeurs, config_content)
        
        for match in matches:
            valeurs_config.add(float(match))
        
        print(f"✅ {len(valeurs_config)} valeurs uniques trouvées dans AZRConfig")
        return valeurs_config
        
    except Exception as e:
        print(f"❌ Erreur lors de l'extraction des valeurs config: {e}")
        return valeurs_config

def analyser_valeurs_hardcodees_restantes():
    """Analyse les valeurs hardcodées qui ne sont PAS dans AZRConfig"""
    
    print("🔍 ANALYSE DES VALEURS HARDCODÉES RESTANTES")
    print("=" * 60)
    
    # Extraire les valeurs déjà centralisées
    valeurs_config = extraire_valeurs_config()
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            lignes = f.readlines()
        
        # Patterns pour détecter les valeurs hardcodées
        patterns_hardcoded = [
            r'([><=]+)\s*([0-9]*\.?[0-9]+)',  # Comparaisons
            r'([+\-*/])\s*([0-9]*\.?[0-9]+)', # Opérations arithmétiques
            r'=\s*([0-9]*\.?[0-9]+)',         # Assignations
            r'\[\s*([0-9]*\.?[0-9]+)',        # Dans des listes
            r',\s*([0-9]*\.?[0-9]+)',         # Paramètres de fonction
        ]
        
        valeurs_hardcodees = {}
        
        for i, ligne in enumerate(lignes, 1):
            # Ignorer les lignes de configuration
            if 'class AZRConfig' in ligne or ': float =' in ligne or ': int =' in ligne:
                continue
            
            # Ignorer les commentaires
            if ligne.strip().startswith('#'):
                continue
                
            for pattern in patterns_hardcoded:
                matches = re.finditer(pattern, ligne)
                for match in matches:
                    try:
                        # Extraire la valeur numérique
                        if len(match.groups()) == 2:
                            valeur = float(match.group(2))
                        else:
                            valeur = float(match.group(1))
                        
                        # Ignorer les valeurs triviales
                        if valeur in [0.0, 1.0, 2.0, 3.0, 4.0, 5.0, 10.0, 100.0]:
                            continue
                            
                        # Vérifier si cette valeur est déjà centralisée
                        if valeur not in valeurs_config:
                            if valeur not in valeurs_hardcodees:
                                valeurs_hardcodees[valeur] = []
                            valeurs_hardcodees[valeur].append({
                                'ligne': i,
                                'contenu': ligne.strip(),
                                'contexte': match.group(0)
                            })
                    except (ValueError, IndexError):
                        continue
        
        return valeurs_hardcodees
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return {}

def categoriser_valeurs_critiques(valeurs_hardcodees):
    """Catégorise les valeurs hardcodées par criticité"""
    
    categories = {
        'CRITIQUE_ROLLOUT1': [],
        'CRITIQUE_ROLLOUT2': [],
        'CRITIQUE_ROLLOUT3': [],
        'CRITIQUE_CLUSTERS': [],
        'MODEREE': [],
        'FAIBLE': []
    }
    
    # Valeurs critiques connues pour chaque rollout
    critiques_rollout1 = [0.05, 0.1, 0.15, 0.2, 0.25]
    critiques_rollout2 = [0.4, 0.6, 0.7, 0.8, 0.85, 0.9, 0.95]
    critiques_rollout3 = [0.3, 0.5, 0.7, 0.8]
    critiques_clusters = [0.6, 0.65, 0.55]
    
    for valeur, occurrences in valeurs_hardcodees.items():
        # Analyser le contexte pour déterminer la catégorie
        contextes = [occ['contenu'].lower() for occ in occurrences]
        contexte_global = ' '.join(contextes)
        
        if any(keyword in contexte_global for keyword in ['rollout1', 'impair', 'pair', 'bias']):
            if valeur in critiques_rollout1:
                categories['CRITIQUE_ROLLOUT1'].append((valeur, occurrences))
            else:
                categories['MODEREE'].append((valeur, occurrences))
                
        elif any(keyword in contexte_global for keyword in ['rollout2', 'generation', 'cross_impact', 'enrichment']):
            if valeur in critiques_rollout2:
                categories['CRITIQUE_ROLLOUT2'].append((valeur, occurrences))
            else:
                categories['MODEREE'].append((valeur, occurrences))
                
        elif any(keyword in contexte_global for keyword in ['rollout3', 'prediction', 'confidence', 'evaluation']):
            if valeur in critiques_rollout3:
                categories['CRITIQUE_ROLLOUT3'].append((valeur, occurrences))
            else:
                categories['MODEREE'].append((valeur, occurrences))
                
        elif any(keyword in contexte_global for keyword in ['cluster', 'consensus', 'accuracy']):
            if valeur in critiques_clusters:
                categories['CRITIQUE_CLUSTERS'].append((valeur, occurrences))
            else:
                categories['MODEREE'].append((valeur, occurrences))
        else:
            categories['FAIBLE'].append((valeur, occurrences))
    
    return categories

def afficher_rapport_detaille(categories):
    """Affiche un rapport détaillé des valeurs hardcodées restantes"""
    
    print(f"\n📊 RAPPORT DÉTAILLÉ DES VALEURS HARDCODÉES RESTANTES")
    print("=" * 80)
    
    total_critiques = 0
    
    for categorie, valeurs in categories.items():
        if not valeurs:
            continue
            
        print(f"\n🔍 {categorie}")
        print("-" * 50)
        
        if 'CRITIQUE' in categorie:
            total_critiques += len(valeurs)
            
        for valeur, occurrences in sorted(valeurs, key=lambda x: len(x[1]), reverse=True):
            print(f"\n  ⚠️  VALEUR: {valeur}")
            print(f"     📍 {len(occurrences)} occurrence(s)")
            
            # Afficher les 3 premières occurrences les plus importantes
            for i, occ in enumerate(occurrences[:3]):
                ligne_courte = occ['contenu'][:80] + "..." if len(occ['contenu']) > 80 else occ['contenu']
                print(f"     L{occ['ligne']:4d}: {ligne_courte}")
            
            if len(occurrences) > 3:
                print(f"     ... et {len(occurrences) - 3} autres occurrences")
    
    print(f"\n" + "=" * 80)
    print(f"📊 RÉSUMÉ FINAL")
    print("=" * 80)
    print(f"🚨 VALEURS CRITIQUES À CENTRALISER: {total_critiques}")
    print(f"⚠️  VALEURS MODÉRÉES: {len(categories['MODEREE'])}")
    print(f"ℹ️  VALEURS FAIBLES: {len(categories['FAIBLE'])}")
    
    if total_critiques == 0:
        print("🎉 AUCUNE VALEUR CRITIQUE HARDCODÉE RESTANTE!")
        print("✅ La centralisation des rollouts et clusters est PARFAITE!")
        return True
    else:
        print(f"❌ {total_critiques} valeurs critiques nécessitent encore une centralisation")
        return False

def main():
    """Fonction principale"""
    
    # Analyser les valeurs hardcodées restantes
    valeurs_hardcodees = analyser_valeurs_hardcodees_restantes()
    
    if not valeurs_hardcodees:
        print("🎉 AUCUNE VALEUR HARDCODÉE RESTANTE DÉTECTÉE!")
        print("✅ La centralisation est PARFAITE!")
        return True
    
    # Catégoriser par criticité
    categories = categoriser_valeurs_critiques(valeurs_hardcodees)
    
    # Afficher le rapport détaillé
    centralisation_parfaite = afficher_rapport_detaille(categories)
    
    return centralisation_parfaite

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
