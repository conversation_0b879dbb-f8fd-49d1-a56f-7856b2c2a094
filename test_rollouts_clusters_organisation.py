#!/usr/bin/env python3
"""
Test de validation de l'organisation des paramètres rollouts et clusters dans AZRConfig

Ce script vérifie que tous les paramètres liés aux rollouts 1, 2, 3 et aux clusters
sont correctement organisés dans des sections dédiées et bien identifiables.
"""

import sys
import os

# Ajouter le répertoire parent au path pour importer le module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from azr_baccarat_predictor import AZRConfig, AZRBaccaratPredictor
    print("✅ Import réussi des classes AZR")
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    sys.exit(1)

def test_rollout1_parameters():
    """Test des paramètres spécifiques au Rollout 1 (Analyseur)"""
    print("\n🔍 TEST ROLLOUT 1 (ANALYSEUR) - SECTION N")
    print("=" * 60)
    
    config = AZRConfig()
    
    # Paramètres spécifiques Rollout 1
    rollout1_params = [
        'rollout1_analysis_time_ms',
        'rollout1_index_time_ms', 
        'rollout1_index4_time_ms',
        'rollout1_synthesis_time_ms',
        'rollout1_min_hands_quality',
        'rollout1_data_richness_factor',
        'rollout1_coherence_neutral',
        'rollout1_quality_threshold',
        'rollout1_so_start_offset',
        'rollout1_max_divisor_safety',
        'rollout1_impact_strength_threshold',
        'rollout1_desync_period_min',
        'rollout1_desync_period_start_init',
        'rollout1_desync_period_length_init',
        'rollout1_combined_pair_sync_influence',
        'rollout1_combined_impair_sync_influence',
        'rollout1_combined_pair_desync_influence',
        'rollout1_combined_impair_desync_influence',
        'rollout1_step_increment',
        'rollout1_small_increment',
        'rollout1_low_performance_threshold',
        'rollout1_high_performance_threshold',
        'rollout1_min_accuracy_trend'
    ]
    
    params_presents = 0
    for param in rollout1_params:
        if hasattr(config, param):
            valeur = getattr(config, param)
            print(f"  ✅ {param}: {valeur}")
            params_presents += 1
        else:
            print(f"  ❌ {param}: MANQUANT")
    
    print(f"\n📊 RÉSULTATS ROLLOUT 1:")
    print(f"   ✅ Paramètres présents: {params_presents}/{len(rollout1_params)}")
    
    return params_presents == len(rollout1_params)

def test_rollout2_parameters():
    """Test des paramètres spécifiques au Rollout 2 (Générateur)"""
    print("\n🎲 TEST ROLLOUT 2 (GÉNÉRATEUR) - SECTION O")
    print("=" * 60)
    
    config = AZRConfig()
    
    # Paramètres spécifiques Rollout 2
    rollout2_params = [
        'rollout2_generation_time_ms',
        'rollout2_sequence_time_ms',
        'rollout2_evaluation_time_ms',
        'rollout2_candidates_count',
        'rollout2_strategy_count',
        'rollout2_max_probability',
        'rollout2_alternative_probability',
        'rollout2_rupture_probability',
        'rollout2_conservative_probability',
        'rollout2_fixed_length',
        'rollout2_sequences_count',
        'rollout2_total_possibilities',
        'rollout2_confidence_threshold',
        'rollout2_quality_threshold',
        'rollout2_signal_confidence_default',
        'rollout2_signal_confidence_high',
        'rollout2_signal_confidence_medium',
        'rollout2_signal_confidence_low',
        'rollout2_confidence_level_excellent',
        'rollout2_confidence_level_good',
        'rollout2_confidence_level_acceptable',
        'rollout2_confidence_level_poor',
        'rollout2_default_signal_type',
        'rollout2_default_signal_name',
        'rollout2_default_strategy_prefix',
        'rollout2_default_justification',
        'rollout2_so_prediction_keyword',
        'rollout2_pb_prediction_keyword',
        'rollout2_player_keyword',
        'rollout2_banker_keyword',
        'rollout2_pair_sync_keyword',
        'rollout2_impair_sync_keyword',
        'rollout2_player_result',
        'rollout2_banker_result',
        'rollout2_fallback_strategy_1',
        'rollout2_fallback_strategy_2',
        'rollout2_fallback_strategy_3',
        'rollout2_fallback_strategy_4',
        'rollout2_fallback_justification_1',
        'rollout2_fallback_justification_2',
        'rollout2_fallback_justification_3',
        'rollout2_fallback_justification_4',
        'rollout2_optimal_difficulty',
        'rollout2_min_difficulty',
        'rollout2_max_difficulty',
        'rollout2_diversity_bonus',
        'rollout2_diversity_malus',
        'rollout2_excellence_threshold',
        'rollout2_acceptable_threshold',
        'rollout2_weak_threshold',
        'rollout2_poor_threshold',
        'rollout2_excellence_bonus',
        'rollout2_neutral_multiplier',
        'rollout2_weak_malus',
        'rollout2_poor_malus'
    ]
    
    params_presents = 0
    for param in rollout2_params:
        if hasattr(config, param):
            valeur = getattr(config, param)
            print(f"  ✅ {param}: {valeur}")
            params_presents += 1
        else:
            print(f"  ❌ {param}: MANQUANT")
    
    print(f"\n📊 RÉSULTATS ROLLOUT 2:")
    print(f"   ✅ Paramètres présents: {params_presents}/{len(rollout2_params)}")
    
    return params_presents == len(rollout2_params)

def test_rollout3_parameters():
    """Test des paramètres spécifiques au Rollout 3 (Prédicteur)"""
    print("\n🎯 TEST ROLLOUT 3 (PRÉDICTEUR) - SECTION P")
    print("=" * 60)
    
    config = AZRConfig()
    
    # Paramètres spécifiques Rollout 3
    rollout3_params = [
        'rollout3_prediction_time_ms',
        'rollout3_evaluation_time_ms',
        'rollout3_selection_time_ms',
        'rollout3_fixed_length',
        'rollout3_default_confidence',
        'rollout3_fallback_probability',
        'rollout3_min_confidence',
        'rollout3_max_confidence',
        'rollout3_so_priority_weight',
        'rollout3_pb_priority_weight',
        'rollout3_coherence_weight',
        'rollout3_quality_weight',
        'rollout3_excellent_threshold',
        'rollout3_good_threshold',
        'rollout3_acceptable_threshold',
        'rollout3_poor_threshold',
        'rollout3_minimum_quality_threshold',
        'rollout3_conservative_probability',
        'rollout3_confidence_bonus_correct',
        'rollout3_confidence_bonus_incorrect',
        'rollout3_optimal_risk',
        'rollout3_min_risk',
        'rollout3_max_risk',
        'rollout3_difficulty_bonus_max',
        'rollout3_overconfidence_threshold',
        'rollout3_overconfidence_penalty_max',
        'rollout3_min_reward',
        'rollout3_max_reward'
    ]
    
    params_presents = 0
    for param in rollout3_params:
        if hasattr(config, param):
            valeur = getattr(config, param)
            print(f"  ✅ {param}: {valeur}")
            params_presents += 1
        else:
            print(f"  ❌ {param}: MANQUANT")
    
    print(f"\n📊 RÉSULTATS ROLLOUT 3:")
    print(f"   ✅ Paramètres présents: {params_presents}/{len(rollout3_params)}")
    
    return params_presents == len(rollout3_params)

def test_cluster_parameters():
    """Test des paramètres spécifiques aux Clusters"""
    print("\n🏗️ TEST CLUSTERS - SECTION Q")
    print("=" * 60)
    
    config = AZRConfig()
    
    # Paramètres spécifiques Clusters
    cluster_params = [
        'cluster_analysis_time_ms',
        'cluster_generation_time_ms',
        'cluster_prediction_time_ms',
        'cluster_total_time_ms',
        'cluster_rollout1_weight',
        'cluster_rollout2_weight',
        'cluster_rollout3_weight',
        'cluster_pattern_specializations',
        'min_calibration_factor',
        'max_calibration_factor',
        'default_calibration_factor',
        'calibrated_confidence_weight',
        'risk_factors_weight',
        'uncertainty_weight',
        'consensus_weight'
    ]
    
    params_presents = 0
    for param in cluster_params:
        if hasattr(config, param):
            valeur = getattr(config, param)
            print(f"  ✅ {param}: {valeur}")
            params_presents += 1
        else:
            print(f"  ❌ {param}: MANQUANT")
    
    print(f"\n📊 RÉSULTATS CLUSTERS:")
    print(f"   ✅ Paramètres présents: {params_presents}/{len(cluster_params)}")
    
    return params_presents == len(cluster_params)

def test_rollouts_generaux_parameters():
    """Test des paramètres généraux des rollouts"""
    print("\n🔄 TEST ROLLOUTS GÉNÉRAUX - SECTION R")
    print("=" * 60)
    
    config = AZRConfig()
    
    # Paramètres généraux rollouts
    rollouts_generaux_params = [
        'n_rollouts',
        'rollout_temperature',
        'rollout_step_size',
        'rollout_random_factor',
        'parallel_rollouts',
        'thread_pool_size',
        'process_pool_size',
        'rollout2_diversity_threshold'
    ]
    
    params_presents = 0
    for param in rollouts_generaux_params:
        if hasattr(config, param):
            valeur = getattr(config, param)
            print(f"  ✅ {param}: {valeur}")
            params_presents += 1
        else:
            print(f"  ❌ {param}: MANQUANT")
    
    print(f"\n📊 RÉSULTATS ROLLOUTS GÉNÉRAUX:")
    print(f"   ✅ Paramètres présents: {params_presents}/{len(rollouts_generaux_params)}")
    
    return params_presents == len(rollouts_generaux_params)

def test_proprietes_dynamiques():
    """Test des propriétés dynamiques rollouts et clusters"""
    print("\n🔧 TEST PROPRIÉTÉS DYNAMIQUES")
    print("=" * 60)
    
    config = AZRConfig()
    
    # Test des propriétés dynamiques
    proprietes_test = [
        ('rollout2_rewards', 'Récompenses Rollout 2'),
        ('rollout3_rewards', 'Récompenses Rollout 3'),
        ('cluster_reward_weights', 'Poids clusters')
    ]
    
    proprietes_reussies = 0
    for prop_name, description in proprietes_test:
        if hasattr(config, prop_name):
            try:
                prop_value = getattr(config, prop_name)
                if isinstance(prop_value, dict) and len(prop_value) > 0:
                    print(f"  ✅ {prop_name}: {description} - {len(prop_value)} éléments")
                    proprietes_reussies += 1
                else:
                    print(f"  ⚠️  {prop_name}: {description} - Dictionnaire vide")
            except Exception as e:
                print(f"  ❌ {prop_name}: {description} - Erreur: {e}")
        else:
            print(f"  ❌ {prop_name}: {description} - MANQUANT")
    
    print(f"\n📊 RÉSULTATS PROPRIÉTÉS DYNAMIQUES:")
    print(f"   ✅ Propriétés fonctionnelles: {proprietes_reussies}/{len(proprietes_test)}")
    
    return proprietes_reussies == len(proprietes_test)

def main():
    """Fonction principale de test"""
    print("🏗️ VALIDATION DE L'ORGANISATION ROLLOUTS ET CLUSTERS")
    print("=" * 80)
    
    tests_reussis = 0
    total_tests = 6
    
    # Test 1: Rollout 1 (Analyseur)
    if test_rollout1_parameters():
        tests_reussis += 1
    
    # Test 2: Rollout 2 (Générateur)
    if test_rollout2_parameters():
        tests_reussis += 1
    
    # Test 3: Rollout 3 (Prédicteur)
    if test_rollout3_parameters():
        tests_reussis += 1
    
    # Test 4: Clusters
    if test_cluster_parameters():
        tests_reussis += 1
    
    # Test 5: Rollouts généraux
    if test_rollouts_generaux_parameters():
        tests_reussis += 1
    
    # Test 6: Propriétés dynamiques
    if test_proprietes_dynamiques():
        tests_reussis += 1
    
    # Résultats finaux
    print("\n" + "=" * 80)
    print("📊 RÉSULTATS FINAUX")
    print("=" * 80)
    print(f"Tests réussis: {tests_reussis}/{total_tests}")
    
    if tests_reussis == total_tests:
        print("🎉 TOUS LES TESTS SONT RÉUSSIS!")
        print("✅ L'organisation des rollouts et clusters est parfaitement fonctionnelle")
        print("🏗️ Sections bien identifiables par rollout et cluster")
        return True
    else:
        print("⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("❌ L'organisation nécessite des corrections")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
