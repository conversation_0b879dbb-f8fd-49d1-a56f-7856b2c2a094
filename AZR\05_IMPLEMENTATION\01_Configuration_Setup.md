# ⚙️ MODULE 5.1 : CONFIGURATION ET SETUP

## 🎯 **OBJECTIFS D'APPRENTISSAGE**

À la fin de cette section, vous saurez :
- ✅ Installer et configurer l'environnement AZR
- ✅ Paramétrer le système selon vos besoins
- ✅ Optimiser les performances pour votre matériel
- ✅ Déboguer les problèmes de configuration courants

---

## 📦 **INSTALLATION DE L'ENVIRONNEMENT**

### **🐍 Prérequis Python**

```bash
# Version Python recommandée
python --version  # >= 3.8

# Création d'un environnement virtuel
python -m venv azr_env
source azr_env/bin/activate  # Linux/Mac
# ou
azr_env\Scripts\activate     # Windows
```

### **📚 Dépendances Principales**

```bash
# Installation des dépendances core
pip install torch>=1.12.0
pip install numpy>=1.21.0
pip install scipy>=1.7.0
pip install matplotlib>=3.5.0
pip install tqdm>=4.62.0
pip install wandb>=0.12.0  # Pour le monitoring
pip install tensorboard>=2.8.0

# Dépendances optionnelles pour optimisations
pip install numba>=0.56.0      # Accélération JIT
pip install cupy>=10.0.0       # GPU computing (si CUDA)
pip install ray>=1.13.0        # Parallélisation distribuée
```

### **🔧 Installation depuis les Sources**

```bash
# Cloner le repository
git clone https://github.com/azr-research/azr-core.git
cd azr-core

# Installation en mode développement
pip install -e .

# Vérification de l'installation
python -c "import azr; print(azr.__version__)"
```

---

## ⚙️ **CONFIGURATION DE BASE**

### **📄 Fichier de Configuration Principal**

```yaml
# config/azr_config.yaml
model:
  name: "AZR-Base"
  hidden_size: 512
  num_layers: 6
  num_attention_heads: 8
  dropout: 0.1
  
proposer:
  vocab_size: 10000
  max_sequence_length: 256
  temperature: 1.0
  top_k: 50
  
solver:
  output_size: 1000
  confidence_threshold: 0.7
  max_iterations: 10
  
training:
  batch_size: 32
  learning_rate: 1e-4
  weight_decay: 1e-5
  gradient_clip: 1.0
  num_epochs: 100
  
rollouts:
  horizon: 5
  num_samples: 10
  parallel_workers: 4
  
logging:
  level: "INFO"
  save_frequency: 100
  checkpoint_frequency: 1000
```

### **🐍 Configuration Python**

```python
# azr/config.py
from dataclasses import dataclass
from typing import Optional
import yaml

@dataclass
class ModelConfig:
    name: str = "AZR-Base"
    hidden_size: int = 512
    num_layers: int = 6
    num_attention_heads: int = 8
    dropout: float = 0.1

@dataclass
class ProposerConfig:
    vocab_size: int = 10000
    max_sequence_length: int = 256
    temperature: float = 1.0
    top_k: int = 50

@dataclass
class SolverConfig:
    output_size: int = 1000
    confidence_threshold: float = 0.7
    max_iterations: int = 10

@dataclass
class TrainingConfig:
    batch_size: int = 32
    learning_rate: float = 1e-4
    weight_decay: float = 1e-5
    gradient_clip: float = 1.0
    num_epochs: int = 100

@dataclass
class RolloutConfig:
    horizon: int = 5
    num_samples: int = 10
    parallel_workers: int = 4

@dataclass
class AZRConfig:
    model: ModelConfig
    proposer: ProposerConfig
    solver: SolverConfig
    training: TrainingConfig
    rollouts: RolloutConfig
    
    @classmethod
    def from_yaml(cls, config_path: str):
        with open(config_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        
        return cls(
            model=ModelConfig(**config_dict['model']),
            proposer=ProposerConfig(**config_dict['proposer']),
            solver=SolverConfig(**config_dict['solver']),
            training=TrainingConfig(**config_dict['training']),
            rollouts=RolloutConfig(**config_dict['rollouts'])
        )
```

---

## 🖥️ **DÉTECTION ET CONFIGURATION MATÉRIEL**

### **🔍 Auto-Détection des Ressources**

```python
# azr/hardware.py
import torch
import psutil
import platform

class HardwareDetector:
    def __init__(self):
        self.system_info = self._detect_system()
        self.gpu_info = self._detect_gpu()
        self.memory_info = self._detect_memory()
        self.cpu_info = self._detect_cpu()
    
    def _detect_system(self):
        return {
            'platform': platform.system(),
            'architecture': platform.architecture()[0],
            'python_version': platform.python_version()
        }
    
    def _detect_gpu(self):
        gpu_info = {
            'available': torch.cuda.is_available(),
            'count': 0,
            'devices': []
        }
        
        if gpu_info['available']:
            gpu_info['count'] = torch.cuda.device_count()
            for i in range(gpu_info['count']):
                device_props = torch.cuda.get_device_properties(i)
                gpu_info['devices'].append({
                    'name': device_props.name,
                    'memory_gb': device_props.total_memory / 1e9,
                    'compute_capability': f"{device_props.major}.{device_props.minor}"
                })
        
        return gpu_info
    
    def _detect_memory(self):
        memory = psutil.virtual_memory()
        return {
            'total_gb': memory.total / 1e9,
            'available_gb': memory.available / 1e9,
            'percent_used': memory.percent
        }
    
    def _detect_cpu(self):
        return {
            'cores': psutil.cpu_count(logical=False),
            'threads': psutil.cpu_count(logical=True),
            'frequency_ghz': psutil.cpu_freq().max / 1000 if psutil.cpu_freq() else None
        }
    
    def get_recommended_config(self):
        """Génère une configuration recommandée basée sur le matériel"""
        config = {}
        
        # Configuration GPU
        if self.gpu_info['available']:
            config['device'] = 'cuda'
            config['batch_size'] = min(64, int(self.gpu_info['devices'][0]['memory_gb'] * 4))
        else:
            config['device'] = 'cpu'
            config['batch_size'] = min(16, int(self.memory_info['available_gb'] / 2))
        
        # Configuration parallélisation
        config['num_workers'] = min(8, self.cpu_info['threads'])
        
        # Configuration mémoire
        if self.memory_info['total_gb'] < 8:
            config['model_size'] = 'small'
            config['hidden_size'] = 256
        elif self.memory_info['total_gb'] < 16:
            config['model_size'] = 'medium'
            config['hidden_size'] = 512
        else:
            config['model_size'] = 'large'
            config['hidden_size'] = 768
        
        return config
```

### **⚡ Configuration Optimisée**

```python
# azr/optimization.py
class PerformanceOptimizer:
    def __init__(self, hardware_info):
        self.hardware = hardware_info
    
    def optimize_torch_settings(self):
        """Optimise les paramètres PyTorch"""
        # Optimisations CPU
        if self.hardware.cpu_info['threads'] >= 4:
            torch.set_num_threads(self.hardware.cpu_info['threads'])
        
        # Optimisations GPU
        if self.hardware.gpu_info['available']:
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
        
        # Optimisations mémoire
        if self.hardware.memory_info['total_gb'] < 16:
            torch.backends.cudnn.enabled = False  # Économise la mémoire
    
    def get_optimal_batch_size(self, model_size_mb):
        """Calcule la taille de batch optimale"""
        if self.hardware.gpu_info['available']:
            gpu_memory_gb = self.hardware.gpu_info['devices'][0]['memory_gb']
            # Règle empirique : 70% de la mémoire GPU disponible
            available_memory = gpu_memory_gb * 0.7 * 1024  # MB
        else:
            # Utiliser 50% de la RAM disponible
            available_memory = self.hardware.memory_info['available_gb'] * 0.5 * 1024  # MB
        
        # Estimer le nombre d'échantillons par batch
        memory_per_sample = model_size_mb * 4  # Forward + backward + gradients + optimizer
        optimal_batch_size = int(available_memory / memory_per_sample)
        
        # Contraintes pratiques
        return max(1, min(optimal_batch_size, 128))
```

---

## 🔧 **CONFIGURATIONS SPÉCIALISÉES**

### **🎮 Configuration pour Baccarat**

```python
# configs/baccarat_config.py
class BaccaratAZRConfig(AZRConfig):
    def __init__(self):
        super().__init__()
        
        # Spécialisations pour le Baccarat
        self.model.hidden_size = 256  # Plus petit pour la rapidité
        self.proposer.vocab_size = 100  # Vocabulaire limité (S, O, T)
        self.proposer.max_sequence_length = 50  # Séquences courtes
        
        self.solver.output_size = 3  # S, O, T seulement
        self.solver.confidence_threshold = 0.6  # Plus permissif
        
        self.rollouts.horizon = 3  # Horizon court pour temps réel
        self.rollouts.num_samples = 5  # Moins d'échantillons
        
        # Paramètres spécifiques Baccarat
        self.baccarat = BaccaratSpecificConfig()

@dataclass
class BaccaratSpecificConfig:
    sequence_memory: int = 100  # Mémoriser 100 dernières mains
    pattern_detection: bool = True
    adaptive_betting: bool = False
    real_time_mode: bool = True
    max_prediction_time_ms: int = 500
```

### **🧪 Configuration pour Recherche**

```python
# configs/research_config.py
class ResearchAZRConfig(AZRConfig):
    def __init__(self):
        super().__init__()
        
        # Configuration pour expérimentation
        self.model.hidden_size = 1024  # Modèle plus large
        self.training.batch_size = 8   # Batch plus petit pour stabilité
        self.training.learning_rate = 5e-5  # LR plus conservateur
        
        self.rollouts.horizon = 10     # Horizon plus long
        self.rollouts.num_samples = 50 # Plus d'échantillons
        
        # Logging détaillé
        self.logging.level = "DEBUG"
        self.logging.save_frequency = 10
        
        # Expérimentation
        self.research = ResearchSpecificConfig()

@dataclass
class ResearchSpecificConfig:
    experiment_name: str = "azr_experiment"
    save_all_checkpoints: bool = True
    detailed_metrics: bool = True
    ablation_studies: bool = False
    hyperparameter_search: bool = False
```

---

## 📊 **MONITORING ET LOGGING**

### **📈 Configuration Weights & Biases**

```python
# azr/monitoring.py
import wandb

class AZRMonitor:
    def __init__(self, config):
        self.config = config
        self.setup_wandb()
    
    def setup_wandb(self):
        wandb.init(
            project="azr-training",
            config=self.config.__dict__,
            tags=["azr", "absolute-zero", "proposer-solver"]
        )
    
    def log_training_metrics(self, epoch, metrics):
        wandb.log({
            "epoch": epoch,
            "proposer_loss": metrics['proposer_loss'],
            "solver_loss": metrics['solver_loss'],
            "total_loss": metrics['total_loss'],
            "proposer_accuracy": metrics['proposer_accuracy'],
            "solver_accuracy": metrics['solver_accuracy'],
            "learning_rate": metrics['learning_rate']
        })
    
    def log_rollout_metrics(self, rollout_stats):
        wandb.log({
            "rollout_horizon": rollout_stats['horizon'],
            "rollout_samples": rollout_stats['num_samples'],
            "rollout_time_ms": rollout_stats['execution_time_ms'],
            "rollout_quality": rollout_stats['quality_score']
        })
```

### **🔍 Logging Structuré**

```python
# azr/logging_config.py
import logging
import json
from datetime import datetime

class AZRLogger:
    def __init__(self, config):
        self.setup_logging(config.logging.level)
        self.logger = logging.getLogger('AZR')
    
    def setup_logging(self, level):
        logging.basicConfig(
            level=getattr(logging, level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'azr_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
    
    def log_config(self, config):
        self.logger.info(f"Configuration loaded: {json.dumps(config.__dict__, indent=2)}")
    
    def log_training_start(self, model_params):
        self.logger.info(f"Training started with {model_params} parameters")
    
    def log_epoch_summary(self, epoch, metrics):
        self.logger.info(
            f"Epoch {epoch}: "
            f"Loss={metrics['total_loss']:.4f}, "
            f"Acc={metrics['accuracy']:.3f}"
        )
```

---

## 🚀 **SCRIPT DE DÉMARRAGE RAPIDE**

### **⚡ Setup Automatique**

```python
#!/usr/bin/env python3
# scripts/quick_setup.py

import os
import sys
import subprocess
from pathlib import Path

def quick_setup():
    """Setup automatique de l'environnement AZR"""
    
    print("🚀 Configuration automatique d'AZR")
    print("=" * 40)
    
    # 1. Vérifier Python
    python_version = sys.version_info
    if python_version < (3, 8):
        print("❌ Python 3.8+ requis")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}")
    
    # 2. Détecter le matériel
    from azr.hardware import HardwareDetector
    hardware = HardwareDetector()
    print(f"✅ Matériel détecté:")
    print(f"   - CPU: {hardware.cpu_info['cores']} cores")
    print(f"   - RAM: {hardware.memory_info['total_gb']:.1f} GB")
    print(f"   - GPU: {'Oui' if hardware.gpu_info['available'] else 'Non'}")
    
    # 3. Générer la configuration optimale
    from azr.optimization import PerformanceOptimizer
    optimizer = PerformanceOptimizer(hardware)
    optimal_config = hardware.get_recommended_config()
    print(f"✅ Configuration optimale générée")
    
    # 4. Créer les dossiers nécessaires
    directories = ['checkpoints', 'logs', 'data', 'results']
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
    print(f"✅ Dossiers créés")
    
    # 5. Test rapide
    try:
        from azr import AZRModel
        model = AZRModel(optimal_config)
        print("✅ Test d'importation réussi")
    except Exception as e:
        print(f"❌ Erreur de test: {e}")
        return False
    
    print("\n🎉 Configuration terminée avec succès!")
    print(f"Configuration recommandée sauvée dans: config/auto_config.yaml")
    
    return True

if __name__ == "__main__":
    success = quick_setup()
    sys.exit(0 if success else 1)
```

---

## 📝 **POINTS CLÉS À RETENIR**

1. **⚙️ Configuration Flexible** : YAML + Python pour tous les paramètres
2. **🖥️ Auto-Détection** : Optimisation automatique selon le matériel
3. **📊 Monitoring Intégré** : Weights & Biases + logging structuré
4. **🎯 Configurations Spécialisées** : Adaptées aux cas d'usage
5. **🚀 Setup Automatique** : Script de démarrage rapide

---

## 🎯 **EXERCICE PRATIQUE**

**Configuration :** Créez votre propre configuration AZR adaptée à votre matériel et cas d'usage.

```python
# Votre configuration personnalisée
class MyAZRConfig(AZRConfig):
    def __init__(self):
        # TODO: Personnaliser selon vos besoins
        pass

# Test de votre configuration
config = MyAZRConfig()
# TODO: Valider la configuration
```

---

**➡️ Prochaine section : [5.2 - Implémentation du Modèle de Base](02_Implementation_Modele.md)**
