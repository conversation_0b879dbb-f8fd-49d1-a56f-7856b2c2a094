# EXTRACTION TEXTUELLE - AI_Korean_University_Report.pdf
# Généré automatiquement le 2025-05-28 10:23:45
# Source: AI_Korean_University_Report.pdf
# ================================================================


--- PAGE 1 ---
H u m a n - I n s p i r e d A r t i f i c i a l I n t e l l i g e n c e
고려대학교
Human-Inspired
AI 연구소
Vol. 5. 2023
H u m a n - I n s p i r e d A r t i f i c i a l I n t e l l i g e n c e
고려대학교
Human-Inspired
AI
연구소
Vol.
5.
2023
고려대학교
Human-Inspired
AI 연구소
Vol. 5. 2023
--- PAGE 3 ---
인사말
임희석 교수/연구소장
현 시대는 스마트 디지털 시대라고 할 수 있습니다. 아날로그 시대에서 단순하게 디지털로의 전환이 아닌
스마트한 디지털 세상으로의 전환이 요구되고 있습니다. 모든 산업과 비지니스는 스마트라고 할 수 있는
인공지능 기술이 접목되어야 경쟁력을 가질 수 있으며 가치를 창출할 수 있습니다.
반면 스마트한 변화에 실패하는 어떤 국가나 산업도 과거의 번영을 지속할 수 없다고 예측됩니다. 모든
산업과 비지니스는 그들의 전통적인 결과물을 스마트라는 함수를 통하여 지능형 결과물을 만들 수 있어야
경쟁력을 가질 수 있습니다. 가치를 창출할 수 있는 스마트 함수를 만드는데 기여할 수 있는 인공지능
기술은 이제 모든 세계와 산업 현장에서 절실히 요구되는 핵심 성장 동력입니다.
최근 딥러닝 기술의 발전에 힘입어 인공지능 기술의 성능이 향상되었습니다. 하지만 사회는 인간 수준의
지능을 갖는 인공지능 기술을 요구하고 있으며, 그러한 요구를 충족시키기 위해서는 많은 연구와 노력이
필요 합니다. 고려대학교 Human-Inspired AI 연구소는 이러한 요구에 부응하기 위하여 설립되었습니다.
가장 지능적인 인간의 뇌신경정보처리 원리와 인간 지능을 가능케하는 핵심 능력을 모델링하여 인간을
닮은 지능 기술을 개발하는 것이 본 연구소의 핵심 방향이라 할 수 있습니다. 최근 인공지능 분야와
기계학습 분야에 서 최고의 성능을 내고 있는 강화학습, 딥러닝, attention mechanism 등이 인간의
정보처리 원리를 반영한 기술들의 예라 할 수 있습니다.
본 연구소에서는 강화학습과 딥러닝 모델처럼 사용하게 될 최고의 새로운 인공지능 기술을 개발하기
위하여 노력할 것입니다. 이를 통한 산업 발전, 국가의 경쟁력 강화, 그리고 인류의 행복한 삶에 기여할 수
있으리라 기대하며, 많은 분들의 성원과 응원을 부탁드립니다.
--- PAGE 6 ---
1장. 원천기술
1. 자연어처리
2. 대화 시스템
3. 정보 검색/분류/추출/요약
4. 기계번역
2장. 교육과정
1. 교육 과정 개요
2. 교육 프로그램
3. 세부 교육 과정
3장. 산학협력 프로그램
1. 파트너십
2. AI 계약연구센터
4장. 부록
1. 특허 등록
2. 기술 이전
contents
01
02
03
04
--- PAGE 7 ---
01
1. 자연어처리
• 한국어 띄어쓰기 자동 교정기 - 11
• 딥러닝을 이용한 영어 문법 오류 교정기 - 12
SOURCE
• 통계 및 확률 기반 형태소 분석 기술 - 14
TECHNOLOGY
• 딥러닝 기반 형태소 분석 기술 - 16
• 개체명 인식기 (Named Entity Recognition) - 18
• 문서 자동 분류 기술 - 21
• Bag of Characters를 응용한 Character-Level Word----
Representation 기술 - 22 원천기술
• 병렬 코퍼스를 이용한 이중언어 워드 임베딩 - 23
• Stack-Pointer Network를 이용한 한국어 의존 구문 분석 - 25
• 의존구문분석 (Dependency Parser) - 26
• Small Data의 한계를 극복하기 위한 전이 학습 모델 - 28
• 통계기반 한국어 뉴스 감정분석 - 30
• 대화속 화자의 감성 분석 (Emotion Recognition in Conversation) - 31
• 자연어 추론에서의 교차 검증 앙상블 기법 - 33
• Denoising Transformer기반 한국어 맞춤법 교정기 - 34
• 지식 임베딩 심층학습을 이용한 단어 의미 중의성 해소 - 35
• Attentive Aggregation(주의적 종합)기반 크로스 모달 임베딩 - 36
• Poly-encoder를 이용한 COVID-19 질의응답시스템 - 38
• 외부지식정보를 이용한 상식추론 질의응답시스템 - 40
• 사전 학습된 Transformer 언어 모델의 이종 언어 간 - --
전이 학습을 통한 자원 희소성 문제 극복 - 42 3. 정보 검색/분류/추출/요약
• 한국어 특성을 반영한 한국어 관계추출 기술 - 44 • 머신러닝 기반 보고서 자동 분석 및 키워드 추출 기술 - 97
• Whisper 기반 음성인식기 API 개발 - 47 • 메타러닝을 응용한 문서 단위의 관계 추출 - 98
• 한국어 상식 추론 모델 - 50 • 비정형 위협정보 자동 인식 및 추출 - 100
• Phone Scam 탐지기 우회 문구 생성 기술 - 54 • 머신러닝을 이용한 문서 자동 요약 - 102
• 딥러닝을 이용한 유사 문서 검색 및 시각화 - 104
• Narrative기반 자동 비디오 분할 - 106
2. 대화 시스템 • 비지도 학습 알고리즘을 이용한 보고서 자동 분석 및
• 대화 시스템에서의 자연스러운 대화를 위한 -- --
토픽 자동 추출 기술 - 108
Memory Attention 기반 Breakdown Detection - 59
• 순차 정보를 이용한 콘텐츠 추천 시스템 개발 - 110
• 검색 기반 대화 시스템에서의 정답 예측 기술 - 62
• 스케치를 이용한 패션 의류 검색 시스템 - 112
• 딥러닝 기반 자동 질의응답 시스템 - 64
• Eye tracking 기반의 휴먼 리딩을 반영한 추출 요약 기법 - 114
• 딥러닝 방법을 이용한 발화의 공손함 판단 - 66
• Sentence BERT 임베딩을 이용한 과편향 뉴스 판별 - 115
• 기계 독해(MRC)를 이용한 COVID-19 뉴스 ----
도메인의 한국어 질의응답 챗봇 - 67 • 종교활동을 위한 휴머노이드 질의응답 로봇 - 116
• 일상대화생성 모델 - 69 • 아이들 교육을 위한 나오 로봇 - 119
• 시각 질의응답 시스템 - 71 • GPT2를 활용한 유사 뉴스 기사 추천 시스템 - 122
• 화자의 페르소나를 반영한 대화 모델 - 74 • 나오 로봇을 활용한 이중 언어 교육 - 124
• 지식 검색 기반 일반 상식 문장 생성기(영어) - 76 • 나오 로봇을 활용한 동화 추천 및 읽기 - 126
• KommonGen: 한국어 일반 상식 추론을 위한 데이터 - 78 • Virtual-Try-On Model for Fashion AI - 128
• 페르소나 및 지식 기반 대화 데이터와 베이스라인 모델 구축 - 81 • 사용자 그래프 기반 한국어 가짜뉴스 판별 방법 - 130
• PEEP-Talk: 상황별 영어 교육을 위한 챗봇 - 86
• 유형다양성을 고려한 교육용 질의응답쌍 생성 모델 - 88
4. 기계번역
• 페르소나 및 지식 기반 대화 데이터를 활용한 대화 모델 개발 - 91 • 고려대학교 다국어 신경망 기계번역기 - 135
• 딥러닝 기반 한국어 고전번역기 - 138
• PicTalky: Text to Pictogram - 141
• COVID19 도메인특화 기계번역기 - 143
• 인간의 인지과정을 반영한 도메인 특화 번역기 - 145
--- PAGE 9 ---
[1]
자연어처리
• 한국어 띄어쓰기 자동 교정기
• 딥러닝을 이용한 영어 문법 오류 교정기
• 통계 및 확률 기반 형태소 분석 기술
• 딥러닝 기반 형태소 분석 기술
• 개체명 인식기 (Named Entity Recognition)
• 문서 자동 분류 기술
• Bag of Characters를 응용한 Character-Level Word Representation 기술
• 병렬 코퍼스를 이용한 이중언어 워드 임베딩
• Stack-Pointer Network를 이용한 한국어 의존 구문 분석
• 의존구문분석 (Dependency Parser)
• Small Data의 한계를 극복하기 위한 전이 학습 모델
• 통계기반 한국어 뉴스 감정분석
• 대화속 화자의 감성 분석 (Emotion Recognition in Conversation)
• 자연어 추론에서의 교차 검증 앙상블 기법
• Denoising Transformer기반 한국어 맞춤법 교정기
• 지식 임베딩 심층학습을 이용한 단어 의미 중의성 해소
• Attentive Aggregation(주의적 종합)기반 크로스 모달 임베딩
• Poly-encoder를 이용한 COVID-19 질의응답시스템
• 외부지식정보를 이용한 상식추론 질의응답시스템
• 사전 학습된 Transformer 언어 모델의 이종 언어 간 전이 학습을 통한
자원 희소성 문제 극복
• 한국어 특성을 반영한 한국어 관계추출 기술
• Whisper 기반 음성인식기 API 개발
• 한국어 상식 추론 모델
• Phone Scam 탐지기 우회 문구 생성 기술
--- PAGE 11 ---
한국어 띄어쓰기 자동 교정기 01
1. 기술 설명
• 본 기술은 기계학습을 이용하여 문장에서 띄어쓰기 오류가 있는 부분을 자동으로 파악하고 이를 올바르게
수정하는 방법이다.
[그림]
단순 규칙을 이용하여
띄어쓰기 교정이 가능한
경우
[그림]
단순 규칙으로 띄어쓰기
교정이 불가능한 경우:
확률 모델의 적용 필요
2. 기술 방법
• 한국어의 경우, 띄어쓰기는 독자에게 글의 가독성을 높이고 문장의 뜻을 정확히 전달하기 위해 매우 중요하다. 자동
띄어쓰기 시스템은 자연어처리 응용 시스템의 가장 기본이 되는 형태소 분석기의 전처리기, 문자인식기가 인식한
문서의 줄 경계를 복원하기 위한 후처리기, 음성인식기로부터 생성된 연속 음절 문장을 올바르게 띄어쓰기를 위한
후처리기, 맞춤법 검사기의 한 모듈로서도 중요한 역할을 하고 있다.
[그림]
띄어쓰기 확률 경로 예시
3. 기술 활용 및 응용 분야
• 감정 분석, 자연어처리
• 데모 : http://blpdemo.korea.ac.kr/ autospacing/
4. 실험
• 본 기술은 띄어쓰기 문제를 품사 부착 문제와 같은 분류 문제(classification problem)로 간주한다. 은닉 마르코프
모델 (hidden Markov model; 이하 HMM)은 품사부착, 정보추출, 개체명 인식, 외래어 추출 등과 같은 자연어처리의
여러 문제를 해결하는 데에 많이 사용되는 모델이며 각 분야에서 높은 성능을 보이고 있다.
• 띄어쓰기 문제에서는 학습을 위해 따로 말뭉치를 구축할 필요가 없이 이미 존재하는 원시 말뭉치를 학습 말뭉치로
사용할 수 있다. 따라서 HMM이 띄어쓰기 문제에도 효과적으로 적용될 수 있으며 띄어쓰기 문제에 적합하도록
HMM을 일반화하여 확장된 문맥을 고려할 수 있는 통계적 모델을 사용한다.
원천기술 I 01. 자연어처리 11
--- PAGE 12 ---
02 딥러닝을 이용한 영어 문법 오류 교정기
1. 기술 설명
• 영어 문법 교정 시스템(Grammar Error Correction system)은 사용자가 입력한 영어 문장의 문법실수, 철자오류,
단어오용 등을 바로잡아 주는 인공지능 시스템이다.
• 영어 문법 교정 시스템에서 교정을 잘하는 것도 중요한 요소이나 옳은 문장이 들어왔을 때 옳은 문장을 그대로
교정없이 출력으로 내보내는 것 또한 매우 중요한 요소다.
• Overcorrection이란 입력으로 문법적으로 올바른 문장이 들어왔음에도 교정을 해야할 대상으로 간주하여 문장의
구조를 흐트러트리는 현상을 의미한다. NMT를 이용한 GEC 같은 경우 NMT의 고질적인 문제점인 반복번역, 생략,
UNK(Unknown) 문제점 때문에 문장의 구조를 흐트러트리거나 Overcorrection하는 경우가 존재한다.
• 현재 대부분의 논문들은 교정 성능을 높이는 것에만 집중하고 있지 옳은 문장이 입력으로 들어왔을 때 옳은
문장이 출력으로 나오는 것에는 집중하지 않고 있다. 실제 서비스를 했을 때 올바른 문장이 들어왔음에도 이상한
결과를 출력하거나 올바른 것도 고쳐버리는 오류가 발생하게 되면 좋은 교정성능을 가지고 있음에도 사용자들의
software에 대한 신뢰성이 떨어지게 된다.
• 본 연구는 교정 성능(Correction)과 과교정(Overcorrection) 성능을 포괄적으로 측정할 수 있는 새로운 Metrics
제안한다.
Input Mr. Banks is aware that there are budget problems.
Overcorrectoin Mr. Bank is aware that there are budget problems.(Deleted )
Input I was iust going to cross the road when somebody shouted 'Stop!'
Overcorrectoin I was iust going to cross the road when somebody shouted (Deleted )
Input This knowledge may be relevant to them.
Overcorrectoin This knowledge may be similar to them.(Replaced )
Input Disposable income increased from 1999 to 2004.
Overcorrectoin A good income increased from 1999 to 2004.(Replaced )
Input Didnt you tell me that either Deborah or David has done his assignment?
Overcorrectoin Did you tell me that either Deborah or David has done his assignment?(Replaced )
Input In some countries you are not able to drink until you are 21.
Overcorrectoin In some countries you cannot drink until you are 21.(Replaced )
Input I will meet Jane, who is my sister.
Overcorrectoin I will meet the Jane, who is my sister.(Added )
[ 표 ] Input One day last September, it rained for ten hours without stopping.
Overcorrectoin One SeptemberOne day, it rained for ten hours without stopping.(Replaced )
Overcorrection 예시
2. 기술 방법
• 영어 문법 교정기 분야의 새로운 Metric인 covering grammar error and overcorrection performance (CGOP)를
제안함
• 해당 Metrics은 교정성능과 Overcorrection 성능을 포괄적으로 측정할 수 있는 최초의 지표임
• 교정성능은 Generalized Language Evaluation Understanding(GLUE) 점수를 이용하며 Overcorrection 성능은
Levenstein 알고리즘과 longest common substring(LCS) 알고리즘을 이용하여 성능 측정함
Human-Inspired AI 12
--- PAGE 13 ---
3. 기술 활용 및 응용 분야
• 본 기술은 Grammarly와 같은 상용화 문법교정시스템으로 응용 가능하며 더 나아가 어린이 영어교육 시장에 활용
가능함
• 데모 : http://nlplab.iptime.org:32292/
4. 실험
• 실험 개요
- 대표적인 Sequence to Sequence 모델을 이용하여 Deep-learning based GEC의 교정 성능과 overcorrection
성능을 확인해본다. LSTM-Attention 그리고 Transformer 기반의 모델을 통하여 각각의 교정 성능과
overcorrection 성능을 검증하고 더 나아가 Copy Mechanism을 적용했을 때 성능 변화를 확인해본다.
• 실험 결과
- 기 존 성능 측정 방법인 GLUE와 BLEU와 제안하는 Metrics인 CEOF의 모델 성능 순위가 뒤집힘
- C opy Mechanism이 Overcorrection 문제를 완화함을 발견
[ 그림 ]
모델 구조
원천기술 I 01. 자연어처리 13
--- PAGE 14 ---
03 통계 및 확률 기반 형태소 분석 기술
1. 기술 설명
• 형태소 분석은 표층형 (surface level form)인 어절로부터 의미가 있는 최소 단위인 형태소 (morpheme)를 추출하는
작업
• 형 태소 분석을 위해서는 어절을 분석하여 형태소의 결합으로 분리하고, 각 형태소에 품사 정보를 할당하고, 형태소
결합 시 발생하는 음운 변화를 원형 (root form)으로 복원하는 것이 필요
예 : 나는 나는 새를 보았다.
[예]
형태소 분석 나 는
나 / 대명사 + 는 / 조사
나 / 동사 + 는 / 관형형어미
날 / 동사 + 는 / 관형형어미
2. 기술 방법
• 코퍼스의 통계적 특성과 확률 모델을 기반으로 한 전통적인 방식의 형태소 분석과 품사 태거임
• 품사부착 말뭉치 (POS tagged corpus)로부터 자동으로 획득한 통계 정보만으로 분석을 수행하였으며 3가지 언어
단위 (어절, 형태소, 음절)에 따른 분석 모델을 사용
• 어절, 형태소, 음절 단위 모델을 순차적으로 적용
NNG: 일반명사 J K S : 주격조사 X S V : 동사파생접미사
NNP: 고유명사 J KG : 관형격조사 X S A : 형용사파생접미사
NNB: 의존명사 J KO : 목적격조사 S F : 마침표,물음표,느낌표
N P : 대명사 J K B : 부사격조사 S P : 쉼표, 가운뎃점, 콜론, 빗금, 줄표, 물결
N R : 수사 J K V : 호격조사 S S : 따옴표,괄호표
V V : 동사 J KQ : 인용격조사 S E : 줄임표
V A : 형용사 J X : 보조사 S O : 붙임표(숨김,빠짐)
V X : 보조용언 E P : 선어말어미 S L : 외국어
V C P : 지정사 EM : 어말어미 S H : 한자
M M : 관형사 ETN: 명사형전성어미 SW : 기타기호
MAG: 일반부사 ETM: 관형형전성어미 S N : 숫자
M A J : 접속부사 XPN: 명사파생접두사 N A : 분석불능범주
I C : 감탄사 XSN: 명사파생접미사
품사 태깅표
3. 기술 활용 및 응용 분야
• 본 기술은 번역기, 자연어 이해 및 생성 등 언어처리 분야의 핵심기술
• 데 모 : http://blpdemo.korea.ac.kr/MA
Human-Inspired AI 14
--- PAGE 15 ---
4. 결과 화면
QUERY : 주택 문제의 경우 제 나이가 아직 젊으니까 가능성이 많지요.
기와나 슬레이트로 된 지붕들이 납작하게 펼쳐져 있는 것이 보인다.
내일이면 이제 모두 끝내고 조금 쉴 수 있을 거 같아.
RESULT : 주택 주택 / NNG
문제의 문제 / NNG+의 / JKG
경우 경우 / NNG
제 저 / NP+의 / JKG
나이가 나이 / NNG+가 / JKS
아직 아직 / MAG
젊으니까 젊 / VA+으니까 / EM
가능성이 가능성 / NNG+이 / JKS
많지요. 많 / VA+지요 / EM+. / SF
기와나 기와 / NNG+나 / JKB
슬레이트로 슬레이트 / NNG+로 / JKB
된 되 / VV+ㄴ / ETM
지붕들이 지붕 / NNG+들 / XSN+이 / JKS
납작하게 납작하 / VA+게 / EM
펼쳐져 펼쳐지 / VV+어 / EM
있는 있 / VX+는 / ETM
것이 것 / NNB+이 / JKS
보인다. 보이 / VV+ㄴ다 / EM+. / SF
내일이면 내일 / NNG+이 / VCP+면 / EM
이제 이제 / MAG
모두 모두 / MAG
끝내고 끝내 / VV+고 / EM
조금 조금 / MAG
쉴 쉬 / VV+ㄹ / ETM
수 수 / NNB
있을 있 / VV+을 / ETM
거 거 / NNB
같아. 같 / VA+아 / EM+. / SF
형태소
분석 결과
원천기술 I 01. 자연어처리 15
--- PAGE 16 ---
04 딥러닝 기반 형태소 분석 기술
1. 기술 설명
• 본 기술은 어떠한 언어 단위도 입력으로 사용할 수 있으며 다단계 변형을 기반으로 형태소 분석 및 품사 부착을
수행하는 방법이다.
[ 그림 ]
형태소 분석 및 품사
부착 과정
2. 기술 방법
• 본 기술은 형태소 분석과 품사 부착의 두 단계를 거친다. 문장에 대해 형태소 분석이 우선 이루어지고, 형태소 분석
결과에서 각 형태소에 대해 품사를 부착한다. 모든 과정은 데이터 기반 종단 시스템으로, 사람의 개입 없이 학습
데이터만으로 모델을 훈련시킬 수 있다.
• 전체 모델은 양방향 Long Short-Term Memory(LSTM)-Conditional Random Field(CRF) 딥러닝 구조를 이용한다.
3. 기술 활용 및 응용 분야
• 형태소 분석, 자연어처리
• 데 모 : http://nlplab.iptime.org:32280/unitagger_demo/
4. 실험
• 제안된 방법을 적용하여 구현된 데이터 기반 양방향 LSTM 모델의 성능을 세종 말뭉치를 이용하여 정량적으로
평가한 결과, 언어학적 지식을 활용하지 않은 접근 방법들 중 가장 높은 단어 및 문장 단위 부착 정확도를 보임을
확인하였다.
Human-Inspired AI 16
--- PAGE 17 ---
원천기술 I 01. 자연어처리 17
--- PAGE 18 ---
05 개체명 인식기 (Named Entity Recognition)
1. 기술 설명
• 개체명 인식기는 텍스트에서 인식시킬 개체를 정의하여 해당 개체를 인식시키는 기술로 본 개체명 인식기는
5개의 클래스[인물(PS), 장소(LC), 기관(OG), 시간(TI), 날짜(DT)]를 정의하였으며, 해당 개체에 한국 문화적 특성을
반영하였다.
• 말뭉치 구축 : 학습에 필요한 말뭉치 구축을 위해 한국학중앙연구원 디지털 인문학 웹사이트의 백과사전 기사에서
전통문화와 관련된 기획기사 및 중심기사로부터 각 기사의 개요와 내용에 대한 문장들을 크롤링하였다.
2. 기술 방법
• 한국어 기반으로 구축한 말뭉치의 전처리 과정을 통해 BI-LSTM-CNN-CRF 모델을 학습시킨다.
• 학습된 모델에 텍스트를 입력으로 넣어 해당 문장에서 개체명으로 인식 가능한 개체를 확인할 수 있다.
3. 기술 활용 및 응용 분야
• 본 모델을 영어 데이터로 학습시킬 경우 영어 기반의 개체명 인식기로 활용할 수 있다.
• 구축한 말뭉치를 다른 모델에 활용할 수 있다.
• 데모 : http://nlplab.iptime.org:32280/ner_demo/index.html
4. 상세 기술 설명 및 실험
Human-Inspired AI 18
--- PAGE 19 ---
• 구축된 전통문화 데이터를 사용하여 모델을 학습시킨다.
• 자질 형성을 위해 첫 번째는 CNN을 통한 음절 단위 자질, 두 번째는 형태소 단위의 Glove vector 자질, 세 번째 품사
태깅 자질, 구축된 사전을 활용한 사전 자질을 BI-LSTM의 입력 데이터로 사용한다
• Hidden Layer를 통해 계산된 데이터는 최종적으로 CRF의 입력으로 사용하여 전이 확률값을 계산한 후 최종적으로
입력 값에 해당하는 개체명을 예측한다
조선 세종은 조선의 제4대 군주이며 언어학자이다.
입력문장
그의 업적에 대한 존경의 의미를 담은 명칭인 세종대욍으로 자주 일컬어진다.
(Input )
성은 이, 휘는 도 본관은 전주, 자는 원정 아명은 막동이다
{”조선” : “B_LC, “”세종” : “B_PS, “”은” : “O,”
형태소 단위 개체명 “4” : “O, “”군주 ” : “O, “”이” : “O, “”며” : “O, “”언어학자” : “O, “”다” : “O, “”.” : “O,”
분석 결과
“그” : “O, “”업적” : “O, “”에” : “O, “”대하” : “O, “”ㄴ” : “O, “”존경” “:O, “”의미” : “O,”
“를” : “O, “”담” : “O, “”명칭” : “O, “”대왕” : “O, “”으로” : “O, “”자주” : “O,”
“일컫” : “O, “”어” : “O, “”지” : “O, “”ㄴ” : “O, “”ㄴ다” : “O, “”성” “:”O :,, ““”O,”
“휘” : “O, “”는” : “O, “”도” : “O, “”본관” : “O, “”전주” : “B_LC, “”자 ”: “O, “”원정” : “O,”
“아명” : “O, “”막” : “O, “”동이” : “O, }”
“<조선 세종 : PS>은 <조선 : LS>의 제4대 군주이며 언어학자 이다.
개체명 인식 결과
그의 업적에 대한 존경의 의미를 담은 명칭인 <세종 : PS>대왕으로 일컬어 진다.
성은 이, 휘는 도, 본관은 <전주 : LS>, 자는 원정, 아명은 막동이다.”
(’조선’ : ‘LC’)
태그 결과
(’세종’ : ‘PS’)
(’전주’ : ‘LC’)
• 전체 흐름은 위의 그림과 같다.
• 구축한 데이터를 바탕으로 다양한 자질들을 생성 및 모델의 입력으로 사용하여 모델을 학습시킨 후, 사용자가
입력한 입력값에 해당하는 개체명을 예측하여 결과로 나타낸다.
원천기술 I 01. 자연어처리 19
--- PAGE 20 ---
• 각 기사의 개요와 내용에 대한 크롤링 과정
• 전체 2351개의 기사로부터 4702개의 문장과 15만 형태소 단위의 말뭉치를 추출했다.
• 태 깅 방식은 BIO(Begin, Inside,
Outside)를 활용하고 각 태그명 앞에
‘B_’를 붙여 태그의 시작을 표기하고
연결된 어미는 ‘I’로 앞 단어와 연결성을
나타낸다.
• 각 태그 중 인물(PS)이 가장 많이
태그되었으며 날짜(DT), 장소(LC) 순서로
태그 개수가 많은 것을 확인할 수 있다.
- 태깅 방식은 BIO(Begin, Inside, Outside)를
활용하고 각 태그명 앞에 ‘B_’를 붙여
태그의 시작을 표기하고 연결된 어미는 ‘I’로
앞 단어와 연결성을 나타낸다.
- 각 태그 중 인물(PS)이 가장 많이
태그되었으며 날짜(DT), 장소(LC) 순서로
태그 개수가 많은 것을 확인할 수 있다.
실험 결과
• 본 모델은 음절, 형태소, 품사 태깅, 사전 기반 자질을 Feature로 활용하여 Accuracy 98.9%, F1-score 89.4%로 기존
모델에 비해 가장 높은 성능을 보였다.
Human-Inspired AI 20
--- PAGE 21 ---
문서 자동 분류 기술 06
1. 기술 설명
• 문서가 어떤 카테고리에 해당하는지 자동으로 분류
• 본 기술은 kNN (k-nearest neighbors algorithm) 학습 방법을 이용
2. 기술 방법
• 인터넷 문서 5,000여 개에서 추출한 자질 중 실험적으로 가장 높은 성능을 보인 2,000개의 자질을 추출
• 정보 검색 기법에서 사용되는 TF/IDF 기법을 이용하여 자질의 가중치 (Weight) 값 계산
• Nearest Neighbor를 추출하기 위하여 Cosine Measure를 사용
3. 기술 활용 및 응용 분야
• 본 기술은 정보 분류(대/중/소), 검색, 추천, 광고 등 언어처리 분야의 활용기술
• 데 모 : http://blpdemo.korea.ac.kr/DocuCate/doccat.htm
4. 결과 화면
• 분류하고자 하는 문서를 입력하면 해당 문서의 분류 결과가 5순위까지 출력
문서 분류 시스템
본 문서 분류기는 kNN(k Nearest Neighboring) 학습 방법을 이용한 문서 분류기의 데모시스템입니다.
인터넷 문서 5,000여개에서 추출한 자질 중 실험적으로 가장 높은 성능을 보인 20,00개의 자질을 추출하여 정보 검색
기법에서 사용되는 TF/IDF 기법을 이용하여 자질의 Weight값을 만들었고, Nearest Neighbor을 추출하기 위하여 Cosine
Measure를 사용하고 있습니다.
아래의 창에 분류하고자 하는 문서를 입력하고 분류하기 버튼을 누르시면 해당 문서의 분류 결과가 순위별로
나타납니다.(5순위 까지 출력됩니다.)
뇌졸중은 전 세계의 많은 사람들에게 영향을 미치는 질병으로, 뇌졸중에 걸린 사람은 대개 후유증으로 장애를 입게 된다.
그래서 환자 본인과 가족들의 부담을 덜기 위한 재활훈련 및 치료 과정이 크게 발전했다. 그러나 뇌졸중 재활을 위해서는
반복적인 연습이 필요하다. 뇌졸중 및 뇌 혈관 센터 물리 치료 및 재활 의학과의 장원혁 및 김윤희 연구원이 지적한
바에 따르면 뇌졸중 환자는 고도의 집중 훈련뿐만 아니라 특정한 기능적 업무를 수행해야 하며 이 과정은 상당히 노동
집약적이다. 두 사람은 로봇을 활용하는 치료법이 뇌졸중 재활 분야에서 잠재적인 가능성을 보일 수 있다고 말했다.
QUERY : 뇌졸중은 전 세계의 많은 사람들에게 영향을 미치는 질병으로, 뇌졸중에 걸린 사람은 대개 후유증으로 장애를
입게 된다.
그래서 환자 본인과 가족들의 부담을 덜기 위한 재활훈련 및 치료 과정이 크게 발전했다. 그러나 뇌졸중 재활을
위해서는 반복적인 연습이 필요하다. 뇌졸중 및 뇌 혈관 센터 물리 치료 및 재활 의학과의 장원혁 및 김윤희
연구원이 지적한 바에 따르면 뇌졸중 환자는 고도의 집중 훈련뿐만 아니라 특정한 기능적 업무를 수행해야
하며 이 과정은 상당히 노동 집약적 이다. 두 사람은 로봇을 활용하는 치료법이 뇌졸중 재활 분야에서 잠재적인
가능성을 보일 수 있다고 말했다.
RESULT : 순위 – 중분류 - 분류코드
1. 질병/증상 0214 2. 대체의학 0203 3. 약/약학 0208 4. 응급처치 0209 5. 건강상식 0201
원천기술 I 01. 자연어처리 21
--- PAGE 22 ---
07 Bag of Characters를 응용한 Character-Level Word Representation 기술
1. 기술 설명
• 본 기술은 완전연결 신경망을 이용하여 빠른 시간 안에 효과적인 문자 단위 자질을 자동적으로 추출할 수 있도록
하는 것이다. 자연어처리 시스템은 문자 단위 자질을 잘 반영할 수 있어야 한다. 이는 신조어 등 학습 시 존재하지
않았던 단어 등의 처리에 매우 효과적이다.
2. 기술 방법
• 본 기술은 Bag-of-Characters (BOC)를 바탕으로 한다. 문자 BOC, 문자 순서 정보 자질, 단어 길이 자질을
concatenate 하여 sparse vector를 생성한다. 이 sparse vector는 단어마다 유일하고 변하지 않으므로 속도 향상을
위해 캐싱이 가능하다.
• Sparse vector를 하나의 은닉층이 있는 완전연결 신경망의 입력으로 사용해서 최종적인 문자 단위 자질 벡터를
생성한다.
3. 기술 활용 및 응용 분야
• 품사 부착, 개채명 인식, 자연어 처리
4. 실험
• 품사 부착, 개체명 인식, 슬롯 인식 실험을 통해 본 기술의 성능을 검증하였다. 실험 결과 슬롯 인식 정확도 96.62%,
품사 부착 정확도 97.73%, 개체명 인식 F-score 91.21을 기록하였다. 이는 기존 최신 기술보다 크게 앞서거나 비슷한
수준의 성능이다. 또한, 본 기술은 기존 기술 대비 문장 처리 속도가 빠른 것으로 나타났다.
[ 그림 ]
초당 처리 문장 수.
Char-Dense가 본
기술로, 경쟁 기술
대비 가장 빠른 것을
확인할 수 있다.
Human-Inspired AI 22
--- PAGE 23 ---
병렬 코퍼스를 이용한 이중언어 워드 임베딩 08
1. 기술 설명
• 워드 임베딩이란 단어를 dense한 실수 벡터 공간에 매핑하되, 단어의 의미가 반영되도록 하는 방법
• 워드 임베딩의 활용방법 중인 하나인 이중 언어 워드 임베딩은 서로 다른 두 언어에서 유사한 의미를 가지는 단어가
유사한 공간에 매핑(mapping) 되도록 하는 것을 목표로 하는데, 기계번역 분야에서 많은 연구가 이루어지고 있음
Monolingual vs
Bilingual 예시
2. 기술 방법
• 본 기술은 문서 정렬 코퍼스보다는 언어 간의 연결고리(bilingual signal)가 강한 문장정렬 영화자막 데이터를 이용한
이중 언어 워드 임베딩 모델 개발
• 개발한 모델은 영화자막 데이터를 강력한 언어 간의 연결고리로써 밑바탕 어휘집으로 사용하여 서로 다른 두 언어를
동일한 공간의 벡터 공간으로 매핑
Bilingual word
embedding 모델
개요
3. 기술 활용 및 응용 분야
• 본 기술은 다중 언어에 대한 번역기에 활용될 수 있으며, 다중 언어 문서에서 정보검색 모델에서도 활용될 수 있다.
• 데 모 : http://nlplab.iptime.org:4321/seol2/mt/projector.html
원천기술 I 01. 자연어처리 23
--- PAGE 24 ---
4. 실험
4.1 실험 개요
• 영화 자막 코퍼스를 seed lexicon으로 이용하고, wikipedia를 통해 어휘를 확장하였다. 본 실험에서는 한국어-영어를
이용한 이중언어 임베딩을 수행하였다.
4.2 실험 결과
Bilingual word • 본 기술의 결과는 데모에서 확인가능하며, tensorboard를 이용하여 시각화하였다. 시각화 결과는 한국어와 영어에
embedding
대한 seed lexicon으로, 이중언어임베딩의 상위 5k 쌍을 가지고 시각화 하였다. 특정 단어를 검색하면 벡터공간에서
시각화 결과 예시
검색한 단어에 대해 제일 가까운 위치의 단어들을 시각화하여 보여준다.
Human-Inspired AI 24
--- PAGE 25 ---
Stack-Pointer Network를 이용한 한국어 의존 구문 분석 09
1. 기술 설명
• 의존 구문 분석 기술은 자연어 문장에 포함된 단어들의 의존 관계를 분석하는 기술
• 그림과 같이 단어들의 의존 관계과 각 의존 관계의 유형을 나타내는 의존 분석 트리 구축 (예: ‘학교에’는 ‘가서’에
의존하는 부사어)
2. 기술 방법
• 최신 딥러닝 기반 의존 분석 모델인 Stack-Pointer Network를 한국어 의존 구문 분석에 적합하도록 확장
• 양방향 LSTM-CNN 구조의 인코더에서 각 어절의 단어 표상 생성에 형태소, 형태소 품사 정보가 포함된 음절,
형태소 품사, 음절 정보를 추가 활용
3. 기술 활용 및 응용 분야
• 본 기술은 대용어 참조 해소, 기계 번역 등의 다양한 자연어 이해 기술에 세부기술로 활용될 수 있음
• 데 모 : http://nlplab.iptime.org:32281/kr-stack-pointer/index.py
원천기술 I 01. 자연어처리 25
--- PAGE 26 ---
10 의존구문분석 (Dependency Parser)
1. 기술 설명
• 본 기술은 영어를 대상으로 하는 SyntaxNet 시스템을 한국어에 사용할 수 있도록 한 것이다. SyntaxNet은 구글에서
개발한 의존구분분석 기술로, 데이터 기반 종단간 시스템으로 동작한다. SyntaxNet의 의존구문분석 정확도는 94%
이상으로, 인간의 수준인 96~97%에 가까운 성능을 보인다.
[그림]
“Ask not what your
country can do for you,
ask what you can do for
your country.”라는 문장에
대한 의존구분분석 예시
2. 기술 방법
• 의존구분분석은 상위 레벨 자연어처리 작업 중 하나로, 수많은 가능한 의존 트리에서 최적의 트리를 찾아내야 한다.
SyntaxNet은 품사 정보가 입력으로 필요하다. 이에 추가로 한국어에 적용하기 위해서는 형태소 분석이 우선적으로
진행되어야 한다. SyntaxNet 모델에 의해 의존구분분석이 완료된 결과에 대하여, 원래의 어절 형태로 형태소들을
재결합하는 과정도 요구된다.
3. 기술 활용 및 응용 분야
• 의존구문분석, 대화 시스템, 자연어처리
• 데모 : http://andrewmatteson.name/psg_tree.htm
Human-Inspired AI 26
--- PAGE 27 ---
4. 실험
[그림]
본 기술로 “남북은
고위급회담을 13일 판문점
북측 통일각에서 개최할
예정이라고 통일부가
9일 밝혔다” 라는 문장의
의존구문분석을 진행한
결과
CoNLL-U Output
Details
원천기술 I 01. 자연어처리 27
--- PAGE 28 ---
11 Small Data의 한계를 극복하기 위한 전이 학습 모델
1. 기술 설명
• 전이 학습은 특정 환경에서 만들어진 모델을 다른 비슷한 task에 적용하는 것으로, 이는 데이터가 부족한 분야에도
적용할 수 있음
• 풍부한 데이터로 먼저 모델을 학습하고 데이터가 부족한 비슷한 task에 대해 모델의 전이를 진행하는 것임. Small
Data의 한계를 극복한다는 점에서 큰 장점이 있음
• 아래는 항공권 예약을 위한 ATIS 데이터와 식당 예약을 위한 MIT 데이터임. 각각의 slot들은 조금씩 다르지만,
예약을 위한 대화 데이터라는 점이 유사하며, ATIS의 city와 MIT의 Location이 특징이 위치라는 점에서 매우 유사함
ATIS UTTERANCE EXAMPLE IOB REPRESENTATION
Sentence show flights from Boston To New York today
Slots/Concepts O O O B-dept O B-arr I-arr B-date
Named Entity O O O B-city O B-city I-city O
ATIS 항공권 예약
Intent Find_flight
데이터에 대한 Slot
Domain Airline Travel
Filling의 예시
Are there any French
O O O B-Cuisine
MIT 식당 예약
restaurants in downtown Toronto
데이터에 대한 Slot
O O B-Location I-Location
Filling의 예시
2. 기술 방법
• 자연어 이해 시스템을 학습하기 위해서는 많은 양의 라벨링 된 데이터가 필요하며 새로운 도메인으로 시스템을
확장할 때, 새롭게 데이터 라벨링을 진행해야 하는 한계점이 존재한다. 본 연구는 적대 학습 방법을 이용하여 풍부한
양으로 구성된 기존(source) 도메인의 데이터부터 적은 양으로 라벨링 된 데이터로 구성된 대상(target) 도메인을
위한 슬롯 채우기(slot filling) 모델 학습 방법이다.
• 본 연구에서는 슬롯 채우기(Bi-directional LSTM 기반), 도메인 분류를 위한 적대 학습, Orthogonality Loss 등을
적용하여, 도메인 고유 및 공유 자질을 서로 상호 배타적으로 학습하였다.
• 대화 데이터 중 항공권 예약 도메인 데이터인 ATIS 데이터와 식당 예약 도메인 데이터인 MIT 식당 예약데이터를
이용하여 실험을 진행하였으며, 적대 학습 방법을 이용한 슬롯 채우기 모델 성능을 확인하였다.
3. 기술 활용 및 응용 분야
• 본 기술은 도메인 간 전이 학습이 가능하기에 데이터가 부족한 목적 지향 대화 데이터 시스템의 학습에 활용될 수
있음
Human-Inspired AI 28
--- PAGE 29 ---
4. 실험
4.1 실험 개요
• slot filling 모델의 평가 방법으로는 f-1 score를 이용하였으며, TGT는 적대 학습을 적용하지 않고 slot filling 모델을
학습한 경우를 나타냄. 적대 학습을 적용한 도메인 분류 손실 함수를 얼마나 반영할지는 계수의 정도에 따라 성능을
측정하였음
• 실험 결과 가중치를 부여하여 적대 학습 방법을 적용할 때가 기존의 적대 학습 방법을 적용하지 않은 경우보다
66.10에서 67.12로 약 1% 가량의 F-1 Score 뛰어난 향상이 있었음
Target
MIT Rest.
Source
TGT 66.1
65.32
ADV( = 1)
ATIS λ
67.12
ADV( = 0.1)
λ
ADV( = 0.01) 66.41
λ
원천기술 I 01. 자연어처리 29
--- PAGE 30 ---
12 통계기반 한국어 뉴스 감정분석
1. 기술 설명
Sentiment
Analysis Demo
결과 화면
• Text Sentiment Analysis는 텍스트로부터 예상되는 감정과 반응을 예측하는 기술
• 데이터는 5개의 감정이 태깅된 10만 개 이상의 뉴스 기사를 이용함. 최소한의 전처리 과정을 거쳐 감정을 예측하는
통계기반 알고리즘을 제안함
2. 기술 방법
• 뉴스 기사에 등장한 단어들을 vocabulary에 추가함
• 뉴스 기사에 대한 vocabulary 내 단어의 tf-idf* 값을 구하고, 뉴스 기사에 태깅된 감정을 참조하여 각 단어들을
5차원 벡터로 표현함
• 입력된 텍스트에서 vocabulary에 포함된 단어를 찾아 미리 계산된 벡터값으로 변환하고, 모든 단어의 벡터값을
합산하여 가장 높은 confidence를 가진 감정을 출력함
• (*tf-idf: 해당 단어의 출현 빈도와 희귀성을 고려하여, 해당 단어가 해당 문서에 대해 얼마나 가치 있는 단어인지
나타내는 값)
3. 기술 활용 및 응용 분야
• 본 기술은 적은 컴퓨팅 자원을 이용하며, 텍스트로부터 의미 있는 특징(feature)을 추출함. 따라서 음성 인식, 자연어
이해 등 다른 자연어처리 모델에 적은 비용으로 의미 있는 특질을 제공 가능함
• 데 모 : http://nlplab.iptime.org:32280/sentiment_demo/index.py
Human-Inspired AI 30
--- PAGE 31 ---
대화속 화자의 감성 분석 (Emotion Recognition in Conversation) 13
1. 기술 설명
• 대화 맥락을 고려하여 주어진 문장에 대한 감정을 분류하는 기술
2. 기술 방법
• 대화(dialogue)를 BERT tokenizer로 분절화(tokenization)하고, 각 발화(utterance)가 끝나는 지점에 구분자로서
[SEP] token을 추가함
• 분절화된 대화 데이터를 BERT-base 모델로 인코딩하고, 각 발화에 해당하는 tokens를 max-pooling하여 deep
contextualized utterance representations를 생성함
• 해당 utterance의 representations를 바탕으로 분류(classification)을 수행함
3. 기술 활용 및 응용 분야
• 본 기술은 AI 챗봇, 채팅 분석 등의 서비스에서 사용자 경험을 향상시키기 위한 감정 분석 모듈로 활용될 수 있다.
4. 실험
4.1 실험 개요
• 영어 일상 대화 데이터인 Friends와 채팅 데이터인 EmotionPush을 이용함
• BERT-base, BERT-large, RoBERTa-base, RoBERTa-large 모델이 대화 내 감정 분석을 수행하도록 학습함
원천기술 I 01. 자연어처리 31
--- PAGE 32 ---
4.2 실험 결과
• RoBERTa-large-cased 모델을 사용했을 때 RNN 기반, GCN 기반의 이전 모델들보다 공통적으로 약 5% 높은 성능을
보였다.
Dataset Model meutral joy sadness anger surprise disgust fear non-meutral w-avg
DialogueRNN4 73.52 55.62 32.35 37.90 46.11 19.92 5.31 32.46 55.25
Friends DialogueGCN5 73.63 58.44 37.31 37.19 53.18 21.94 4.35 31.88 56.77
BERT-base* 77.08 61.36 40.48 37.25 53.68 21.74 8.11 33.62 58.69
BERT-large* 77.37 61.51 43.79 44.14 52.43 25.69 12.50 34.81 59.75
RoBERTa-base* 77.26 67.56 42.25 46.11 51.93 26.55 11.76 35.10 60.26
RoBERTa-large* 77.48 67.17 45.03 51.57 55.77 29.82 15.87 37.76 61.17
DialogueRNN4 82.44 63.85 31.22 15.56 35.15 8.33 0.00 17.89 69.56
DialogueGCN5 83.63 64.07 38.91 27.16 35.36 10.00 0.00 13.18 70.41
Emotion BERT-base* 85.90 63.71 47.37 31.75 45.77 10.00 0.00 20.77 73.25
Push BERT-large* 86.41 69.60 44.30 41.27 45.00 40.00 0.00 21.08 74.41
RoBERTa-base* 86.87 69.22 49.33 35.09 47.67 22.22 0.00 22.51 75.29
RoBERTa-large* 86.27 69.24 50.91 26.67 54.82 33.34 0.00 28.11 75.97
5. 참고
• 코드: https://github.com/kisuyang/emotionx-ku
• 데모: http://nlplab.iptime.org:32290/
Human-Inspired AI 32
--- PAGE 33 ---
자연어 추론에서의 교차 검증 앙상블 기법 14
1. 기술 설명
• 앙 상블(Ensemble)은 여러 모델들의 예측값을 종합하여 최종 판단을 내리는 기계학습 기법이다. 대표적인 앙상블
기법으로는 Bagging(Bootstrap Aggregating)이 있으며, 이는 다양한 샘플로 모델을 학습시키기 위한 반복과정이
필요하여 앙상블기법만을 위한 별도의 연산이 요구된다. 이러한 문제를 해소하기 위하여 Checkpoint Ensemble(CE)
기법이 제안되었으나 학습 소요 시간이 경감되어 데이터의 분포가 고르지 않을 경우 높은 분산을 보일 수 있다는
한계가 있다. 본 기술은 앙상블 기법을 교차검증 방법과 결합하여 앙상블 연산을 위한 비용을 줄이며 일반화 성능을
높인다.
2. 기술 방법
Training
Phase
Test
Phase
• 본 기술은 별도의 연산을 피하면서 분산 경감 면에서도 강점을 가지는 교차 검증 앙상블(Cross-Validated
Ensemble, CVE)기법이다. 이는 Bagging처럼 여러 샘플을 추출해 학습하는 효과를 얻는 동시에 교차 검증시
기록된 checkpoints로 앙상블하므로 별도의 연산이 요구되지 않는다. 교차 검증 앙상블 기법은 다음과 같은 단계로
진행된다.
- 전체 학습 데이터를 k-fold로 나누고, 선정 모델을 k개의 샘플 데이터로 개별 학습 시킨다. 이때 validation score가
가장 높은 지점을 미리 기록한다.
- 교차 검증 데이터로 학습을 마친 뒤, 학습한 모델들과 테스트셋을 입력 받는다.
- 각 fold별로 validation score가 가장 높은 checkpoint를 찾아 k개의 모델을 준비한다.
- 선정된 k개의 모델이 예측한 labels를 평균내어 최종 예측 값을 반환한다.
원천기술 I 01. 자연어처리 33
--- PAGE 34 ---
15 Denoising Transformer기반 한국어 맞춤법 교정기
1. 기술 설명
• 맞 춤법 교정이란 주어진 문장에서 나타나는 철자 및 맞춤법 오류들을 올바르게 교정하는 것이다. 본 기술은 기존의
맞춤법 교정기술과 달리 소스 문장에 맞춤법 오류문장, 타겟 문장에 올바른 문장을 넣어 학습시키는 기계번역
관점에서의 맞춤법 교정기술이다.
2. 기술 방법
• 기 계번역이란 소스문장(Source Sentence)을 타겟문장(Target Sentence)으로 번역하는 시스템으로 이를 맞춤법
교정 시스템에 적용하여 소스문장으로는 오류문장을, 타겟 문장으로는 교정문장으로 사용하였다. 본 기술은 기존의
규칙기반 맞춤법 교정방식, 통계기반 맞춤법 교정방식과 달리 고품질의 병렬 말뭉치가 존재할 경우 별도의 규칙을
구축하지 않아도 다양한 양상의 맞춤법 오류를 수정할 수 있는 Transformer방식으로 개발하였다.
• T ransformer방식은 Convolution과 Recurrence 없이 오직 Attention만을 이용한 기계번역 모델로 Query, Key,
Value를 기반으로 하는 Multi Head Attention을 기반으로 한다. 이는 입력과 출력에 대해 각각 Self Attention을
학습하고 이후 입력과 출력사이의 Attention을 학습하는 구조를 가진다.
3. 실행결과
• 데모 : http://nlplab.iptime.org:32288/
Human-Inspired AI 34
--- PAGE 35 ---
지식 임베딩 심층학습을 이용한 단어 의미 중의성 해소 16
1. 기술 설명
• 단 어 중의성 해소란 두 개 이상의 의미를 가진 단어를 문장의 쓰임에 따라 정확하게 분석하는 것이다. 본 기술은 단어의
중의성을 해소하는 기술로 단어의 표상에 구문 정보와 의미 관계를 반영할 수 있도록 그래프 임베딩을 활용하였다.
2. 기술 방법
• 본 기술은 단어 표상에 구문 정보와 의미 관계 정보를 반영하기 위하여 GCN(Graph Convolution Network)를
사용하였으며, 구문 정보를 반영하기 위하여 Stanford CoreNLP parser에서 표현되는 의존 관계 정보를 활용하였다.
또한 의미 관계 정보를 나타내기 위해 WordNet정보를 활용하였다.
• [단어 중의성 해소 모델]은 Context, Gloss, Memory, Scoring 4개의 모듈로 구성되어 있으며, 모든 단어 벡터는
SemGCN 단어 표상 결과를 사용하였다.
- Context Module: 중의성 단어를 가지는 단어의 문장을 Bi-LSTM을 통해 순방향, 역방향으로부터 나온 벡터값을
concatenate하여 표현함
- Gloss Module: 중의성 단어의 의미설명(Gloss)정보를 같은 방법으로 Bi-LSTM을 통하여 표현하며, Gloss
Expansion방법을 사용함. 동시에 명사품사를 가지는 상위어, 하위어의 모든 의미설명 정보들도 Bi-LSTM으로
표현함. 상위어, 하위어 정보는 BFS(Breadth First Search)를 통하여 깊이 K만큼 추출하여 관련된 Gloss정보를
Context Module과 같이 표현함. 이러한 Gloss정보들은 Relation Fusion Layer을 통해 상위어는 순방향 LSTM에
나열하고, 하위어는 역방향 LSTM에 나열하여 벡터로 표현한 뒤, concatenate하여 표현함
- Memory Module: Context Module의 벡터결과와 Gloss Expantion 모듈에서의 벡터 결과를 Attention을 통해
계산 후 메모리를 업데이트함
- Scoring Module: Context Module의 벡터결과와 Memory 모듈의 마지막 Attention 결과값을 사용하여 중의성
단어의 의미를 선택함
[ 그림 ]
단어 중의성 해소 모델
원천기술 I 01. 자연어처리 35
--- PAGE 36 ---
17 Attentive Aggregation(주의적 종합)기반 크로스 모달 임베딩
1. 기술 설명
• 본 기술은 사진 검색을 위한 주의적 종합(Attentive Aggregation)기반의 언어-시각 크로스 모달 임베딩 모델로서
자연어 질의로부터의 사진 검색 과제를 해결할 수 있다. 본 기술은 사진으로부터 여러 개의 특징 벡터를 계산한
뒤 자연어 질의의 임베딩에 따라 Attentive Aggregation을 적용한다. 이는 이미지의 다양한 특징에 선별적으로
집중하여 질의와 사진 간의 유사도를 평가함으로써 언어와 시각 모달 간의 의미적 간극을 크게 줄일 수 있다.
2. 기술 방법
• 본 기술은 질의 기반 종합 검색 대상 임베딩 방법에 기반하여 질의 인코더, 사진 인코더, Attentive Aggregation Layer로
구성되어 있다. 질의 인코더와 사진 인코더에서는 자연어 질의와 사진으로부터 의미적 특징들을 추출하며, 서로 다른
형태의 데이터인 질의와 사진을 공통의 벡터 공간에 매핑하는 것을 목표로 한다. 계산된 사진 임베딩과 질의 임베딩
간의 Triplet Semi-hard Loss를 최소화하여 의미적으로 유사한 사진과 질의의 임베딩 간 거리를 최소화하였다.
[질의 인코더]
양방향 GRU와
MLP구조로 구성되며,
자연어 질의로부터
하나의 질의 임베딩
Human-Inspired AI 36
--- PAGE 37 ---
양방향 GRU와 MLP구조로 구성되며, 자연어 질의로부터 하나의 질의 임베딩 벡터를 계산함. 입력으로는
[질의 인코더]
자연어 질의를 분할한 토큰들의 임베딩을 사용하였으며, 본 모델에는 형태소 분석기를 통해 분할한 형태소들 중 질의의
핵심 정보를 나타낼 것으로 예상되는 명사와 동사 형태소를 사용함. 양방향 GRU Layer에서는 토큰들의 임베딩을
입력으로 받아 질의 전체의 정보를 반영한 특징 벡터를 계산하고, MLP Layer에서는 이를 사진 임베딩과 공통벡터
공간에 매핑되는 질의 임베딩으로 변환함
[ 그림 ]
질의 인코더 구조도
CNN과 MLP구조로 구성되며, 사진의 여러 영역을 각각의 사진 특징 벡터들로 인코딩함. CNN
[사진 인코더]
Layer에서는 각 픽셀의 RGB색상 값을 0~1 범위의 실수로 변환된 값을 입력으로 받아 사진의 각 영역에 대한 특징
벡터들을 계산함. 이후 MLP Layer에서 이를 상위의 의미정보를 반영하는 사진 특징 벡터들로 변환함
[ 그림 ]
사진 인코더 구조도
질의 임베딩 벡터에 따라 여러 개의 사진 특징 벡터들을 가중합하여 사진 임베딩을
[Attentive Aggregation Layer]
계산함. Attentive Aggregation은 질의 기반 종합 검색 대상 임베딩의 종합 방법으로 활용되었으며, 이는 질의 임베딩에
따라 정보량이 많은 사진으로부터 다양한 정보를 추출하여 선택적으로 활용할 수 있게 함.
원천기술 I 01. 자연어처리 37
--- PAGE 38 ---
18 Poly-encoder를 이용한 COVID-19 질의응답시스템
1. 기술 설명
• 사람들에게 질의 응답 시스템을 이용한 손쉬운 접근방법을 통해 COVID-19에 대한 믿을 만한 최신 정보 제공을 할
필요성이 있음
• 질의 응답 시스템은 질문에 대한 빠른 응답 속도와 응답의 높은 정확성을 필요로 함
2. 기술 방법
• 본 기술은 검색 기반의 질의 응답 서비스에서 모델의 응답 속도 및 정확성을 잘 반영할 수 있도록 Poly-encoder
모델을 기반으로 fine-tuning을 수행하였음
poly-encoder
모델 구조
Human-Inspired AI 38
--- PAGE 39 ---
3. 기술 활용 및 응용 분야
• 본 기술은 검색기반의 질의 응답 시스템이 가능한 모든 분야에서 사용될 수 있다,
4. 실험
4.1 실험 개요
• 크롤링한 COVID-19관련 데이터 (Q-Q / Q-A)를 이용하여 모델 fine-tuning 및 검증실험을 수행하였다. 여기서
Q-Q는 질의와 유사한 질의를 찾아내는 것이며, Q-A는 질의에 대응하는 응답을 찾아내는 것이다.
4.2 실험 결과
• 구체적인 표, 그림 설명
Q-A
Candidates Accuracy F1 BLEU-4 MRR
Poly-encoder
20 0.36 0.46 0.37 0.54
(Reddit)
Poly-encoder
10 0.45 0.54 0.46 0.63
(Reddit)
JHU-COVID-OA@20(ft)
20 0.79 0.83 0.79 0.87
(ours)
JHU-COVID- OA@20(ft)
10 0.99 0.99 0.99 0.99
(ours)
Q-Q
Candidates Accuracy F1 BLEU-4 MRR
Poly-encoder
5 0.72 0.79 0.69 0.58
(Reddit)
JHU-COVID- OA@20(ft)
5 0.72 0.81 0.69 0.59
(ours)
• 검증 메트릭은 다음과 같다. Accuracy, F1, BLEU-4, MRR (Mean Reciprocal Rank)
• 실험 결과 제안한 모델 JHU-COVID-QA (OURS)이 기존 베이스라인 (poly-encoder (Reddit)) 모델보다 높은 성능을
보였다.
원천기술 I 01. 자연어처리 39
--- PAGE 40 ---
19 외부지식정보를 이용한 상식추론 질의응답시스템
1. 기술 설명
• 상식이란 사회의 사람들, 그리고 일상에서 얻어질 수 있는 지식들을 말함. 상식추론 이란, 이러한 상식 정보들을
이용하여 추론하는 논리적인 과정을 의미함
• 본 기술은 질의에 적합한 상식 그래프 추출하기 위하여 질의를 AMR(Abstract Meaning Representation) 그래프로
변환하고 이를 이용하여 상식 질의응답을 수행하는 기술임
• AMR(Abstract Meaning Representation) 그래프는 주어진 질의의 의미를 그래프 구조로 표현하고, 해석을 용이하게
만들어 줌
• AMR그래프가 가지고 있는 relation 중 ARG0과 ARG1은 프레임 논항 (Frame Argument)으로, 문장 내부에서
핵심적인 역할을 하는 중요 노드들과 연결되어 있음. AMR 구조를 이용하게 된다면, 질의에 대해서 꼭 필요한 상식
그래프만 추출할 수 있음. 효과적으로 상식 그래프(ConceptNet)를 추출하기 위해, 문장 내부에서 핵심적인 역할을
하는 ARG0와 ARG1에만 상식 그래프 확장. 기존의 단어 기반 해석에서 더 나아가, 그래프 경로기반으로 기계의 상식
추론을 해석할 수 있음
2. 기술 방법
• 본 기술이 사용하는 확장 그래프
• 본 기술은 그래프의 경로를 모델에 임베딩하기 위해 AMR-CN 확장 그래프를 relation을 node로 취급하는 Levi
Graph 로 변환. Cai (2019)의 Graph Transformer의 encoder 부분을 재구성하여 경로 학습 모델을 구성한 후,
언어모델에서 나온 벡터값을 이용하여 오지선다 문제를 품
Human-Inspired AI 40
--- PAGE 41 ---
3. 기술 활용 및 응용 분야
• 본 기술은 상식 분야에 대한 질의응답을 수행할 수 있음
4. 실험
4.1 실험 개요
• AMR구조를 사용하지 않았을 때의 성능과 사용하였을 때의 성능 비교를 수행함
• 다양한 언어모델에 대한 실험을 수행함
4.2 실험 결과
Language Encoder Graph type Ndev-Acc.(%) Ntest-Acc.(%)
- 51.81 51.59
AMR-original 52.82 52.78
BERT-base-cased
CN-full (CF) 53.8 53.1
CN-pruned (CP) 52.61 52.53
AMR-CN-full (ACF) 52.98 52.94
AMR-CN-pruned (ACP) 53.97 53.58
AMR구조를 사용하지 않았을 때보다 AMR 구조와 상식그래프(ConceptNet)를 통합하였을 때의 성능이 가장 높음
원천기술 I 01. 자연어처리 41
--- PAGE 42 ---
Language Encoder Ndev-Acc.(%) Ntest-Acc.(%)
BERT-base-cased 51.81 51.59
XLNet-base-cased 57.98 57.05
ALBERT-base 50.12 49.22
ELECTRA-base 71.25 70.19
BERT-base-cased w/ AMR-CN-pruned (ACP) 53.97 53.58
XLNet-base-cased w/ AMR-CN-pruned (ACP) 61.01 60.35
ALBERT-base w/ AMR-CN-pruned (ACP) 51.51 51.08
ELECTRA-base w/ AMR-CN-pruned (ACP) 71.99 70.91
• 다양한 언어모델에 대해서도 성능이 높아지는 것을 볼 수 있음
Human-Inspired AI 42
--- PAGE 43 ---
사전 학습된 Transformer 언어 모델의 이종 언어 간 전이 학습을 통한 자원 희소성 문제 극복 20
1. 기술 설명
• 방대한 양의 말뭉치와 언어 모델링 태스크를 통해 사전 학습된 Transformer 모델은 자연어처리 시스템의 뼈대로
활용될 시 광범위한 도메인 및 태스크에 걸쳐 큰 폭의 성능 향상을 보임
• 동일한 모델을 사용했을 때, 학습 데이터의 양은 언어 모델 및 하위 자연어처리 시스템의 성능에 가장 큰 영향을
미치는 요소이므로, 언어 자원의 불균형은 이러한 최신 자연어처리 기술이 다양한 언어로 확대되는 과정에 있어 큰
걸림돌
• 본 기술은 언어 모델의 학습 시 이종 언어 간 전이 학습을 사용하여 성능을 향상시킴
2. 기술 방법
• 언어 자원이 풍부한 언어에서 학습된 Transformer 기반 언어 모델에서 얻은 파라미터 중 재활용 가능한 부분을
이용하여 목표 언어의 모델을 초기화한 후 학습을 진행함
• 기존 언어와 신규 언어의 차이를 학습하는 역할을 하는 적응 층들을 추가하여 이종 언어 간 전이 학습을 도움
[ 그림 ]
본 기술의 구조도.
학습은 a에서 c순으로
진행됨.
3. 기술 활용 및 응용 분야
• 본 기술은 사전 학습된 언어 모델을 기반으로 하는 모든 자연어처리 시스템에 적용될 수 있으며, 언어 모델을 사전
학습 시키기 위한 언어 자원이 부족한 상황에서 특히 효과적임
4. 실험
4.1 실험 개요
• RoBERTa 모델에 본 기술을 적용하고 언어 자원이 희귀한 상황을 가정하여 영어로부터 한국어로의 전이 학습을
실험해본 결과, 전이 학습을 사용하지 않은 기준 모델 대비 perplexity는 47.6% 감소하고, 단어 예측 정확도는 18.0%
향상됨을 확인하였다.
4.2 실험 결과
Perplexity 단어 예측 정확도 (%)
기준 모델 40.3 42.8
[ 표 ]
TLM 23.5 48.4
이종 언어 간 전이 학습
TLM+ 21.1 50.5
실험의 정량적 성능 비교.
원천기술 I 01. 자연어처리 43
--- PAGE 44 ---
21 한국어 특성을 반영한 한국어 관계추출 기술
1. 기술 설명
• 관계추출(Relation Extraction)이란 주어진 문장과 엔티티(entity) 2개가 제공되었을 때 문장을 문맥적으로 이해하고
엔티티들 간의 연관성을 고려하여 관련된 관계(relation)을 에측하는 것을 의미함
• 이는 비정형 문서들이 방대해짐에 따라 중요한 정보를 구조화된 정보로 자동으로 추출할 수 있는 방법 중 하나이며,
Knowledge base 확장에 있어 중요한 기반 기술임
• 아래 그림은 관계추출의 예시임. “KBS 제1라디오는 대한민국의 방송국 한국방송공사에서 운영·방송하는 라디오
채널이다.” 라는 문장이 주어지고, 엔티티1(Entity1)은 “한국방송공사”, 엔티티2(Entity2)는 “제1라디오” 라고 했을 때,
“org:place\_of\_headquarters”라는 관계를 예측해야함
2. 기술 방법
• 한국어 기반의 관계추출 연구가 부족하기 때문에 한국어 Relation Extraction Framework인 EPIC-K Framework를
개발함
• 관계 추출 연구는 문장에 대한 이해도 중요하지만 entity간 의미적 이해도 중요하기 때문에 본 연구에서 제안하는
EPIC-K Framework는 entity 앞과 뒤에 entity position token을 추가하여 entity들 간의 이해를 도울 수 있도록
4가지 방법을 적용하여 개발을 진행함
1. 는 entity position들어간 문장의 맨 앞에 출력을 가지고 relation을 예측함

2. 는 entity position들어간 문장의 entity1과 entity2의 앞에 있는 entity position인 [EP1], [EP2] 두 개의
출력을  concat하여 relation을 예측함
3. 는 entity1과 entity2의 뒤에 있는 [/EP1], [/EP2]의 출력을 가지고 concat 함

4. sms [EP1], [/EP1], [EP2], [/EP2]의 출력을 갖고 concat함

Human-Inspired AI 44
--- PAGE 45 ---
3. 기술 활용 및 응용 분야
• 관계추출(Relation Extraction) 기술은 Question&Answering system, Information Retrieval 등 다양한 natural
language processing(NLP)의 응용 분야에서 활용되며, 구조화된 triple을 추출하기 때문에 Knowledge Base를
확장하는데 중요한 기술임
• 데모 : http://nlplab.iptime.org:32244/
4. 실험
4.1 실험 개요
• BERT-Ko-RE Dataset과 KLUE-RE Dataset을 사용하였으며, 한국어의 특성을 고려할 수 있도록 대용량의 한국어
데이터로 학습시킨 한국어 언어모델인 HanBERT, KLUE-BERT, KoBERT, KorBERT, KoELECTRA를 적용하였으며,
한국어를 번역하여 학습시킨 mBERT와도 비교를 함
원천기술 I 01. 자연어처리 45
--- PAGE 46 ---
4.2 실험 결과
• non-EPIC은 entity position을 넣지 않았을 때 성능이며, entity position을 추가한 EPIC_V, EPIC_S, EPIC_E,
EPIC_SE가 성능이 높은 것을 볼 수 있음
Data BERT-Ko-RE dataset
Model BERT-multilingual HanBERT KLUE-BERT KoBERT KorBERT KoELECTRA
Micro Weighted Micro Weighted Micro Weighted Micro Weighted Micro Weighted Micro Weighted
non-EPIC 39.39 39.41 39.55 39.09 39.39 37.95 39.45 38.32 32.26 33.89 39.55 38.27
EPIC_V 70.40 74.91 69.15 74.97 71.76 76.24 63.06 67.75 58.32 61.86 70.24 75.41
EPIC_S 73.56 78.88 68.99 74.96 77.53 81.43 73.34 78.07 60.45 64.43 68.61 75.21
EPIC_E 69.97 75.65 70.78 75.27 72.14 76.49 66.21 71.90 57.02 59.60 67.41 72.34
EPIC_SE 70.29 75.80 73.07 78.62 74.86 80.11 69.53 75.18 61.59 65.69 68.23 73.07
Data KLUE-RE
Model BERT-multilingual HanBERT KLUE-BERT KoBERT KorBERT KoELECTRA
Micro Weighted Micro Weighted Micro Weighted Micro Weighted Micro Weighted Micro Weighted
non-EPIC 20.91 20.34 23.64 22.84 23.54 23.97 22.84 22.81 14.68 14.14 23.03 22.31
[ 표 ] EPIC_V 53.43 52.66 56.23 55.44 60.57 59.29 54.39 53.33 31.67 30.46 50.89 50.03
BERT-Ko-RE,
EPIC_S 54.27 53.71 59.43 58.92 61.23 60.77 56.57 56.05 32.97 31.97 57.80 57.24
KLUE-RE Dataset에
EPIC-K Framework를 EPIC_E 55.18 53.70 57.82 58.06 60.75 59.69 54.75 54.77 33.82 32.38 58.02 57.82
적용한 한국어
EPIC_SE 55.78 54.59 59.01 57.79 60.31 59.79 55.82 54.88 34.40 32.50 59.29 58.51
언어모델별 성능
Human-Inspired AI 46
--- PAGE 47 ---
Whisper 기반 음성인식기 API 개발 22
1. 기술 설명
• Speech to Text (STT)는 음성 인식 기술로, 사람이 발화한 음성을 기계가 이해할 수 있는 텍스트 형태로 변환하는
기술이며, 이 기술은 주로 인공지능 기반의 음성 인식 소프트웨어를 통해 구현됨
• 음성을 텍스트로 변환하는 기술로, 일상적인 대화, 토론, 강의 등 다양한 상황에서 음성을 텍스트로 변환하여
저장하거나 분석하는 데 사용됨. 예를 들어, 음성 채팅봇, 음성 검색 엔진, 자막 생성, 자동 번역 등에 사용됨. 특히,
음성을 텍스트로 변환하여 텍스트 분석 및 처리를 수행하는 것은 기업, 정부, 연구기관 등에서 매우 유용함
• OpenAI에서 개발한 Whisper는 자연어 처리 기술 및 음성 인식 기술을 기반으로 한 speech-to-text 기술이며, 음성
데이터를 입력으로 받고, 딥러닝 알고리즘을 사용하여 이를 분석하고, 분석 결과를 기반으로, 입력된 음성에 대한
텍스트 결과를 생성하는 최점단 음성 인식 모듈임
2. 기술 방법
• 음성 데이터 수집 및 전처리: Whisper는 음성 데이터를 입력으로 받음. 이러한 음성 데이터는
스펙트로그램(spectrogram) 형식으로 표현됨. 스펙트로그램은 음성 신호를 시간-주파수 영역으로 변환하여 표현한
것임
• 특징 추출: Whisper는 입력된 스펙트로그램을 사용하여 음성 데이터의 특징을 추출함. 이를 위해서는 주파수
영역에서의 신호 세기를 계산하고, 이를 시간 축에서 평균화하여 특징을 추출함
• 음성 인식: Whisper는 추출된 음성 특징 벡터를 트랜스포머 모델에 입력으로 제공함. 이 모델은 입력된 음성
데이터를 이전에 사용된 대화의 문맥과 연관시켜 분석함. 이를 통해, Whisper는 입력된 음성 데이터에 대한 텍스트
결과를 생성함
원천기술 I 01. 자연어처리 47
--- PAGE 48 ---
3. 기술 활용 및 응용 분야
• 음성 인식 (Automatic Speech Recognition) 기술은 가상 비서 및 챗봇, 검색 엔진, 자동 번역, 게임 등 다양한
분야에서 활용되며, 사용자의 생활과 업무를 더욱 효율적으로 만드는 것에서 중요한 기술임
• 데모: https://whisper.hiai.kr/
4. 실험
4.1 실험 개요
• Huggingface에 공개된 ‘openai/whisper-large’ 모델을 사용하여 한국어 음성을 한국어 및 영어로 출력하는 평균
10회 속도를 비교함. 길이는 1~20초, 21~40초, 41~60초, 61~80초의 음성 길이를 분류하여 평균 10회 속도를 보고함.
NVIDIA RTX A6000 GPU를 단독으로 사용함
입력 음성 길이 텍스트 변환 시간
1~20초 0.9876
21~40초 1.8951
41~60초 8.9502
61~80초 9.6624
4.2 실험 결과
• 입력된 음성 길이가 Whisper 모델의 최대 입력 길이를 초과하면, 음성 스펙트럼을 나눈 후 모델의 입력에 두
번 거치게 됨. 해당 시점은 대략 30초로 계산되며, 이 과정에서 두 번의 모델 연산 과정이 이루어지게 되므로
기하적으로 추론 시간이 증가하게 됨. 따라서 적절한 사용 시간은 40초 이하의 음성 스펙트럼으로 예상할 수 있음.
아래는 개발된 Whisper 기반 음성인식기 데모 사용 예시임
Human-Inspired AI 48
--- PAGE 49 ---
원천기술 I 01. 자연어처리 49
--- PAGE 50 ---
23 한국어 상식 추론 모델
1. 기술 설명
• 한국어 상식 추론 능력을 개선하는 데이터셋으로 학습한 생성 모델
• 한국어 동사 및 명사 정보 또는 실질 형태소 정보를 개념 집합으로 사용하여 단문의 문장을 생성하도록 하는 기술
[ 그림 ]
한국어 상식추론
모델의
대략적인 구조
Human-Inspired AI 50
--- PAGE 51 ---
2. 기술 방법
• 본 기술은 일상에서 흔히 볼 수 있는 사물과 행동으로 구성된 개념 정보를 재구성하여 한국어 문장을 생성하도록 함
• 본 기술의 생성 모델은 주어진 개념 정보를 순서에 상관없이 조합하여 타당한 관계 추론에 입각한 문장을 생성함
• 개념 정보 집합의 개수는 최소 3개부터 8개까지 문장을 생성하는 데에 사용할 수 있음
• 본 기술의 데모에서는 한국어 기본 문형을 고려해서 최소한 1개 이상의 동사와 명사를 포함하도록 함
3. 기술 실행
• 본 기술의 데모에서는 3개부터 5개까지의 개념 정보만을 활용하도록 함
• 사용자는 설정한 값 만큼 개념 정보를 입력한 이후 “Confirm!” 버튼을 클릭
원천기술 I 01. 자연어처리 51
--- PAGE 52 ---
그림 3개의 개념 정보 집합 (e.g., 카드 왔다 멤버십)
그림 4개의 개념 정보 집합 (e.g., 마음 결혼할 친구 식었다)
[그림] 5개의 개념 정보 집합 (e.g., 고양이 이야기한다 대변 치운 경험)
Human-Inspired AI 52
--- PAGE 53 ---
4. 기술 활용 및 응용 분야
• 본 기술은 주어진 조건의 관계를 추론하여 타당한 응답을 생성하도록 할 수 있음
• Data-to-Text 분야에서 작문 등의 주어진 정보를 조합하여 단문의 문장을 생성하는 경우 적용 가능
• 데이터 증강, 목적 도메인에 타당한 프로토타입 문장 생성, 정보 검색을 위한 쿼리 등
5. 실험
5.1 실험 개요
• 단일 한국어 생성 모델 및 다국어 생성 모델에 대해서 주어진 조건을 재구성하여 문장을 재구성하는 능력 평가
Model Size BLEU 3 BLEU 4 ROUGE-2 ROUGE-L METEOR mBERTScore KoBERTScore Coverage
KoGPT2
125M 29.24 18.91 43.36 60.41 39.89 84.08 90.92 79.43
(Radford et al., 2019)
KoBART
124M 39.54 29.16 53.6 68.55 51.17 87.41 92.59 93.65
(Lewis et al., 2020)
mBART
610M 41.83 31.63 54.21 68.36 52.08 87.25 92.26 91.39
(Liu et al., 2020)
mBART-50
610M 40.51 30.2 53.5 68.18 50.9 87.31 92.26 91.71
(Tang et al., 2020)
mT5-small
300M 34.18 23.29 49.48 66.46 46.1 87.39 92.28 92.02
(Xue et al., 2021)
mT5-base
580M 40.87 30.22 54.87 70.21 51.76 88.15 92.77 94.83
(Xue et al., 2021)
mT5-large
1280M 46.33 35.9 58.91 72.78 56.52 88.54 92.92 95.07
(Xue et al., 2021)
Human Performance 49.12 41.64 61.02 73.29 58.6 91.13 95.26 98.3
5.2 실험 결과
• 본 기술의 데모가 적용되어 있는 KoBART 모델은 다국어 기반의 모델보다 적은 파라미터를 지녔음에도 불구하고
우수한 성능을 지니고 있음
• mT5 모델은 가장 우수한 성능을 보이고 있으며, 한국어 문장 구성을 고려한 실험 세팅은 사람에 가까운 성능을
달성하는데 큰 기여를 함
• 많은 양의 파라미터와 훈련 양을 지닌 모델을 사용하는 경우 개념 정보 집합을 조합하여 사람에 가까운 성능을
달성할 수 있는 가능성을 지님
6. 데모
•데모 : http://nlplab.iptime.org:9093/
원천기술 I 01. 자연어처리 53
--- PAGE 54 ---
24 Phone Scam 탐지기 우회 문구 생성 기술
1. 기술 설명
• Phone Scam 탐지기(Vishing Classifier) 우회가 가능한 유사 Vishing 문구를 생성하는 Text Generative Model
기술 확보. 기존의 Vishing (Voice Phishing)과 정상 데이터 특성을 학습하여, 새로운 Vishing 문구를 생성하는 Text
Generative Model을 연구 및 개발
2. 기술 방법
• 본 기술은 크게 두 가지 방법을 활용함. 1) 패러프레이즈 (Paraphrase) 방식을 활용한 데이터 증강
• 패러프레이즈 방식은 Retrieval을 활용하여 내부 데이터와 외부 데이터의 유사 쌍을 검색해 이를 Source와
Target으로 활용하는 방법론을 의미함. 최종적으로 유사도가 높은 문장 쌍을 생성 모델을 활용해 패러프레이즈
하게 됨
• 본 방법론에서는 Source 데이터를 AI Hub의 금융 상담 데이터셋, Target은 금융 감독원의 Vishing Text를 사용함.
Retrieval은 상담 데이터와 유사한 문장을 Vishing 데이터에서 검색해 Source와 Target을 구성함. 이에 대한 예시는
아래와 같고, 최종적으로 생성기를 통해 문장을 생성하게 됨
input—text (상담 데이터) target—text (Vishing) score prefix
일단 본인 합법적인자산은 백만 원 voice
50000000 원 미만 비과세입니다 0.516394
미만으로 접수를 해 드릴 거고요 phishing
말씀하시는 건가요? 그렇죠 voice
신용대출입니다 0.827153
신용 대출이죠 phishing
voice
네 더 문의 하실 사항 없으신가요 궁금하신 거 있으세요? 없으시고요? 네 0.809664
phishing
중도상환 해약금이 따로 없고 대출 중 신용대출 있어도 연체 없으시면 voice
0.761423
자유롭게 상환 환 가들합니다 가능하셔서 필요 자금 대환자금 있으세요 phishing
3. 기술 활용 및 응용 분야
• 본 기술은 보이스피싱 분야뿐만 아니라, 데이터가 부족한 다양한 분야에 활용될 수 있음
4. 실험
4.1 실험 개요
• 데이터셋의 경우 절삭 규칙을 사용해 문장 단위로 구성한 Vishing 텍스트와 상담 데이터를 사용. Retrieval을 통해
유사도를 계산했을 때, 유사도가 0.6 이상인 Source와 Target 문장 쌍을 사용함. 자세한 데이터 통계는 다음과 같음.
아래 표에서 GPT3는 GPT3의 Embedding을 사용해 유사도를 계산 한 것을 의미함.
Human-Inspired AI 54
--- PAGE 55 ---
데이터셋 세부 정보 훈 련 검 증 평가
Source: 상담데이터
Consult-Vishing 3,845 769
Target: Vishing Text
Source: Vishing Text
Vishing-Consult 8,326 1,665
Target: 상담데이터
Source: 상담데이터
Consult-Vishing(GPT3) 4,721 944
Target: Vishing Text
Source: Vishing Text
Vishing-Consult(GPT3) 14,092 2,818
Target: 상담데이터
Consult-Vishing와
Hybrid 12,171 2,434
Vishing-Consult를 혼합하여 사용
4.2 정량적 실험 결과
• 생성 문장과 원본 문장의 유사도를 고려하여 생성기의 성능을 검증함. 이를 위해 딥러닝 기반의 BERT Score을 통해
원본 문장과 생성 문장의 유사도를 측정함. 실험 결과는 아래와 같음
• 일반적으로 BERT Score 0.7 이상은 두 문장이 의미론적으로 동일하다고 보는 견해가 많음. 이를 미루어 보아
패러프레이징을 활용한 본 방법론은 원본 문장와 유의미한 문장을 생성하는 것을 살펴볼 수 있음
BERTScore (Precision) BERTScore (Recall) BERTScore (F1)
Consult-Vishing 0.74 0.76 0.75
Vishing-Consult 0.74 0.7 0.72
Consult-Vishing(GPT3) 0.71 0.73 0.72
Vishing-Consult(GPT3) 0.72 0.68 0.7
Hybrid 0.75 0.73 0.74
4.3 정성적 실험 결과
• 정성적 실험 결과는 다음 표와 같음. 원본 문장이 Vishing Text의 도메인과 결합하여 새로운 합성 데이터를 생성함.
각 생성 문장은 원본 문장과 의미론적으로 유사하면서 다양성이 보장된 문장을 생성함
원본 문장 생성 문장
50,000,000원 미만 비과세입니다 비과세에 해당 되십니다.
신용대출입니다 고객님 대출이 어떻게 되실까요? 그렇죠
네 더 문의하실 사항 없으신가요 네 그러시면 되시고요
여보세요?
중도상환 해약금이 따로 없고 대출 중 자유롭게 상환 가능합니다
혹시 저희가 만기일 전에 상환을 해드릴게요
500,000원 이상으로 타인 이름만 인정됩니다 네 그러면 100000 원만 인정하실 거예요
원천기술 I 01. 자연어처리 55
--- PAGE 56 ---
[2]
대화 시스템
• 대화 시스템에서의 자연스러운 대화를 위한 Memory Attention 기반
Breakdown Detection
• 검색 기반 대화 시스템에서의 정답 예측 기술
• 딥러닝 기반 자동 질의응답 시스템
• 딥러닝 방법을 이용한 발화의 공손함 판단
• 기계 독해를 이용한 COVID-19 뉴스 도메인의 한국어 질의응답 챗봇
• 일상대화 생성 모델
• 시각 질의응답 시스템
• 화자의 페르소나를 반영한 대화 모델
• 지식 검색 기반 일반 상식 문장 생성기(영어)
• KommonGen: 한국어 일반 상식 추론을 위한 데이터
• 페르소나 및 지식 기반 대화 데이터와 베이스라인 모델 구축
• PEEP-Talk: 상황별 영어 교육을 위한 챗봇
• 유형 다양성을 고려한 교육용 질의응답쌍 생성 모델
• 페르소나 및 지식 기반 대화 데이터를 활용한 대화 모델 개발
--- PAGE 59 ---
대화 시스템에서의 자연스러운 대화를 위한 Memory Attention 기반 Breakdown Detection 01
1. 기술 설명
• 대화 시스템에서 Breakdown detection이란 사람과 시스템 간의 자연스러운 대화의 흐름이 끊어지는 현상을
탐지하는 것임
• 대화 시스템을 이용하는 사용자 입장에서는 자연스러운 대화가 이루어져야 시스템에 대한 만족을 통해 원활한
서비스를 이용할 수 있음
• 아래 그림은 대화 시스템에서 breakdown이 발생하는 예시를 보여준 것임. 시스템-사람간의 대화를 보면 마지막에
사람이 “나는 비가 싫어서 저녁에 집에 있을 거야.”라고 하였으나, 시스템은 문맥에 맞지 않는 발화(빨간색)를 하여
자연스러운 대화의 흐름이 끊김을 알 수 있음
2. 기술 방법
• 본 기술은 end-to-end 기반의 breakdown detection 모델이며, LSTM(Long short-term memory)을 이용하여
대화 내에 사용자와 시스템의 발화를 인코딩하고 시스템 발화에 대해 memory network기반의 attention 기법을
이용하여 breakdown detection을 수행하는 구조를 가지고 있다.
3. 기술 활용 및 응용 분야
• 대화 시스템을 지원하고 있는 기기의 소프트웨어서 활용 가능하며, 기존의 인공지능 스피커 서비스인 NUGU, kakao
mini 등에서 활용 가능함
원천기술 I 02. 대화 시스템 59
--- PAGE 60 ---
4. 실험
• 본 연구에서 제안한 모델은 다음과 같다.
• TU: memory attention을 적용하지 않은 모델
• TU+S: system memory attention을 적용한 모델
• TU+U: user memory attention을 적용한 모델
• TU+S+U: user and system memory attention을 적용한 모델
• 본 모델에서 정량적 평가는 TU+S와 TU+S+U에서 기존 모델보다 뛰어난 성능을 보였음
제안한 모델의
정량적 성능 결과
• 다음은 정성적 평가에 대한 것이다. TU+S+U에 대한 정성적 평가 결과이며, 하단 표는 한 다이얼로그에서 발화가
발생할 때, breakdown이 되기까지 attention의 변화를 시각화한 것이다. 실제 마지막에 breakdown이 발생하기까지
문제가 되는 문장들에 대해 모델에서 많은 attention weight를 사용한 것을 확인할 수 있다.
Memory attention
distribution을 통한
모델의 정성적 결과
Human-Inspired AI 60
--- PAGE 61 ---
5. 모델 개요
Memory Attention
• 위의 그림에서 ⊕⊗∑는 각각 concatenation, 매트릭스 multiplication, summation, 문장을 구성하는 기반 Breakdown
단어를 의미한다. 본 모델의 학습 과정은 다음과 같다. (1) 사용자 발화 및 시스템 발화에 대한 sentence Detection 모델 개요
representation을 수행한다. (2) 현재 시점 t에서 시스템  및 사용자 의 발화를
     
인코딩(Encoding)하기 위해 LSTM을 이용하여  를 도출한다. (3) LSTM으로부터 획득한 인코딩 벡터와 현재

시점에서 모든 이전 시스템 발화에 대한 memory를 저장하여 attention을 이용한 attention weight값을 도출한다.(
   는 각각 사용자, 시스템 발화에 대한 memory context 벡터이다.) (4) 마지막으로 대화 시스템내의
 
발화에서 breakdown을 예측한다.
원천기술 I 02. 대화 시스템 61
--- PAGE 62 ---
02 검색 기반 대화 시스템에서의 정답 예측 기술
1. 기술 설명
• 검색 기반 대화 시스템이란 대화의 마지막 응답을 후보들(candidates) 중에서 찾아 제공하는 대화 시스템
• 대화 문맥 정보를 활용하여 가장 관련 있는 응답을 찾아 사용자에게 답변을 제공해 주는 것을 목표로 하며, 검색
기반 대화 시스템은 챗봇을 위한 대화 시스템 분야에서 많은 연구가 진행되고 있음
Ubuntu
troubleshoot과
관련된 대화와
이에 대한
응답 예측하는 예
2. 기술 방법
• 본 기술은 문장을 효과적으로 표현할 수 있는 LSTM Encoder와 또한 대화의 문맥에서 중요한 부분에 대해
집중적으로 모델에 반영하기 위해 단어 단위의 Attention mechanism을 사용하여 모델을 개발하였음
• 대화 내 발화의 중요 특징(사용자 정보, 발화의 순서, 문장 임베딩)들을 반영하여, 대화 문맥 정보를 더욱 잘 표현할
수 있도록 모델 개발
3. 기술 활용 및 응용 분야
• 본 기술은 검색을 기반으로 하는 챗봇 시스템 구축 및 학습에 활용될 수 있으며, 도메인 영역에 관련 없이 활용될 수
있음
4. 실험
4.1 실험 개요
• DSTC7에서 제공한 Ubuntu Dialog Corpus와 Adviisng Dataset을 사용하여 response selection task에 대해 실험을
진행한 결과는 아래와 같음
• 본 기술은 DSTC7에서 제공한 Ubuntu와 Advising 데이터 셋에 대해서 실험을 진행하였으며,
ESIM+SE+PE+UE(ELMO) 모델이 기존 Baseline 모델들의 성능보다 좋은 성능을 보여주었음
Human-Inspired AI 62
--- PAGE 63 ---
4.2 실험 결과
• 구체적인 표, 그림 설명
Ubuntu Advising
Task 1
R@1 R@2 R@5 R@10 R@50 MRR R@1 R@2 R@5 R@10 R@50 MRR
(Lowe et al. 2015) 0.211 0.307 0.446 0.569 0.921 0.32 0.074 0.108 0.21 0.342 0.802 0.162
(Dong and Huang 2018) 0.367 0.452 0.558 0.651 0.917 0.465 0.086 0.156 0.256 0.376 0.834 0.187
ESIM + SE (GloVe) 0.377 0.46 0.568 0.657 0.929 0.473 0.098 0.16 0.294 0.43 0.834 0.204
ESIM + SE + PE + UE (GloVe) 0.384 0.464 0.575 0.662 0.921 0.48 0.112 0.166 0.298 0.438 0.859 0.214
ESIM + SE + PE + UE (ELMo) 0.406 0.493 0.606 0.691 0.928 0.505 0.106 0.16 0.306 0.46 0.858 0.213
• 아래의 그림은 본 기술의 전체 모델 구조도 및 발화 임베딩의 구성을 도식화한 것임
LSTM Encoder와
대화 및 응답 후보 간의
Attention을 반영한 모델
구조도
대화의 발화 정보들을
이용한 임베딩 구성
방법
원천기술 I 02. 대화 시스템 63
--- PAGE 64 ---
03 딥러닝 기반 자동 질의응답 시스템
1. 기술 설명
• 자동 질의응답 시스템 (챗봇)이란 주어진 질문에 대한 적절한 답변을 자동으로 제시하는 시스템
• 질의응답 방법 중 검색 기반 방법은 기존 질의응답 데이터에서 주어진 질문에 가장 적절한 기존 답변을 선택하여
답변을 제시하는 방법
2. 기술 방법
• 본 기술은 Q&A 게시판 데이터 등 소량의 정제되지 않은 데이터로부터 검색 기반 방법을 적용한 딥러닝 기반 자동
질의응답 시스템 구축
• 챗봇 구축 시 ‘데이터 전처리 기술’에서 주어진 데이터를 챗봇 기술에 적합하도록 전처리하고, ‘기존 질의응답 데이터
단계별 정제 기술’에서 정보검색 기술을 적용해 무의미한 질의응답 데이터 제거
• 챗봇 서비스 시 ‘답변 후보군 선택 기술’에서 TF-IDF feature의 코사인 유사도를 기준으로 가능한 답변 후보군을
선택하고, ‘답변 선택 기술’에서 딥러닝 기반 최신 답변 선택 모델 COALA를 적용하여 최종 답변 선택
3. 기술 활용 및 응용 분야
• 본 기술은 중소기업 및 개인사업자 등 기존 챗봇 기술에 대한 접근성이 낮은 사용자들에게 최신 챗봇 기술을
보급하고 소비자 상담 효율을 높일 수 있음
• 데 모 : http://nlplab.iptime.org:32283/
Human-Inspired AI 64
--- PAGE 65 ---
1. 기술 설명 4. 실험
• 자동 질의응답 시스템 (챗봇)이란 주어진 질문에 대한 적절한 답변을 자동으로 제시하는 시스템 4.1 실험 개요
• 질의응답 방법 중 검색 기반 방법은 기존 질의응답 데이터에서 주어진 질문에 가장 적절한 기존 답변을 선택하여 • 본 기술을 적용하여 서울사이버대학교 입학상담 게시판 질의응답 데이터 1248건으로부터 자동 질의응답 시스템
답변을 제시하는 방법 구축
4.2 실험 결과
2. 기술 방법
• 본 기술은 Q&A 게시판 데이터 등 소량의 정제되지 않은 데이터로부터 검색 기반 방법을 적용한 딥러닝 기반 자동 • 본 기술을 적용하여 구축한 자동 질의응답 시스템의 결과 예시
질의응답 시스템 구축
• 챗봇 구축 시 ‘데이터 전처리 기술’에서 주어진 데이터를 챗봇 기술에 적합하도록 전처리하고, ‘기존 질의응답 데이터
단계별 정제 기술’에서 정보검색 기술을 적용해 무의미한 질의응답 데이터 제거
• 챗봇 서비스 시 ‘답변 후보군 선택 기술’에서 TF-IDF feature의 코사인 유사도를 기준으로 가능한 답변 후보군을
선택하고, ‘답변 선택 기술’에서 딥러닝 기반 최신 답변 선택 모델 COALA를 적용하여 최종 답변 선택
3. 기술 활용 및 응용 분야
• 본 기술은 중소기업 및 개인사업자 등 기존 챗봇 기술에 대한 접근성이 낮은 사용자들에게 최신 챗봇 기술을
보급하고 소비자 상담 효율을 높일 수 있음
• 데 모 : http://nlplab.iptime.org:32283/
원천기술 I 02. 대화 시스템 65
--- PAGE 66 ---
04 딥러닝 방법을 이용한 발화의 공손함 판단
1. 기술 설명
• 본 기술은 인간의 발화가 주어졌을 때, 이의 공손함을 판단하는 시스템이다. 공손함은 언어학에서 광범위하게
탐구된 주제 중 하나로 인간의 언어를 구성하는 핵심적인 요소이며, 전 세계 다양한 문화권에 걸쳐 광범위하게
나타나는 인간 언어의 공통적인 요소 중 하나이다.
2. 기술 방법
• 기존 연구들은 사용된 기계학습 모델이 단어의 순서와 문맥 정보를 반영하지 못한다는 한계점을 가지고 있다. 본
기술은 각 단어와 그 단어의 문맥 정보를 동시에 반영할 수 있도록 양방향 LSTM(Long Short-Term Memory)
모델과 최근 자연어처리 분야에서 각광받고 있는 BERT 모델을 바탕으로 개발하였다.
• 양방향 RNN을 이용한 문장분류
양방향 회귀 신경망(Recurrent Neural Network, RNN)은 단어를 순차적으로 입력받아 내부의 기억 구조를 활용하여
문맥 정보가 반영된 단어 표상을 생성한다. 본 연구에서는 RNN의 기억 구조를 보강하여 장거리 의존성 문제를 해소한
LSTM을 기반으로 모델을 구성하였다.
• BERT를 이용한 문장분류
BERT(Bidirectional Encoder Representations form Transformers)는 사전 훈련된 모델로, 광범위한 자연어처리
시스템에서 매우 효과적인 모델이다. 기존 연구들에서 공개한 데이터는 딥러닝 모델을 훈련 시키기에 부족하여
데이터가 부족한 상황에서도 효과적으로 동장하는 BERT모델을 사용하였다.
Human-Inspired AI 66
--- PAGE 67 ---
기계 독해를 이용한 COVID-19 뉴스 도메인의 한국어 질의응답 챗봇 05
1. 기술 설명
• 기계독해(Machine Reading Comprehension; MRC)는 문단과 질의문이 주어졌을 때 정답에 해당하는 부분을 찾는
기술임
• 정답의 위치정보를 알기 위해 토큰화된 문장을 인코딩하고 이를 이용해 근거 문맥 내 토큰의 정답 확률을 구함.
Transformer 아키텍처는 단어와 문장에 대한 문맥이 반영된 인코딩을 가능하게 하였다. Transformer 계열의 PLM인
BERT를 이용하여 MRC 연구가 수행되었으며, F1 점수 기준으로 사람 수준의 MRC 수준을 보여주고 있음
• 근거 문맥과 질문의 쌍이 사전에 주어지지 않는 경우, 질문에 알맞은 근거 문맥을 찾고 그 안의 정답 위치를 찾는
과정을 거치게 됨
2. 기술 방법
• 챗봇 구축 시 데이터 수집 단계에서 챗봇 기술에 적합하도록 전처리하여, 정보검색(Elastic Search) 기술을 이용해
무의미한 질의응답 데이터 제거
• 답변 후보군 선택 기술에서 BM25의 코사인 유사도를 기준으로 가능한 답변 후보군을 선택하고, 딥러닝 기반 최신
답변 선택 모델 (BERT-based MRC)를 적용하여 답안 추출
• 정제한 답안을 JSON 형식으로 가공하여 사용자에게 제공
원천기술 I 02. 대화 시스템 67
--- PAGE 68 ---
3. 기술 활용 및 응용 분야
• 본 기술은 도메인 특화 챗봇에 활용될 수 있으며, MRC 기반 정보검색 모델에서 활용될 수 있다.
• 본 기술은 신문기사, 게시판 글 등 정제되지 않은 데이터를 딥러닝 PLM BERT 기반 MRC 기술을 통합하여 자동으로
질의응답하는 시스템을 구축할 수 있다.
• 데모 : http://nlplab.iptime.org:36200/mrcv2
• 데모 (카카오톡) : https://pf.kakao.com/_xoKUCK
4. 실험
4.1 실험 개요
• 문서 검색기의 경우 전처리(analysis) 과정에 따라 검색 결과가 달라지게되므로, 전처리 방법에 따른 top-k 정확도를
측정하였음
4.2 실험 결과
• KorQuAD 1.0 데이터 집합의 근거 문장(context)을 인덱싱하고 질문(question)을 질의문으로 검색하여
정답(answer)의 포함 여부를 기준으로 top-k 정확도를 측정
top-1 정확도 top-5 정확도
공백 분절 71.68% 84.19%
형태소 분석 + 명사 추출 88.92% 97.26%
• 형태소 분석을 하여 명사를 추출하고 이를 이용하여 인덱싱할 경우 유의미한 성능 향상을 보임
Human-Inspired AI 68
--- PAGE 69 ---
일상대화 생성 모델 06
1. 기술 설명
• 일상대화(Chitchat)는 개방형 질문(open-ended question)을 다루는 대화이며 도메인이 정해지지 않은 일반적인
대화를 다룸
• 일상대화 생성 기술은 일상대화에서 나타나는 단어의 순차 생성 확률을 학습하여, 자연스러운 문장을
생성하도록 함
2. 기술 방법
• 일상대화의 경우 사용자 입력 문장에 대응되는 문장을 생성해야 하므로, 대응되는 문장 쌍을 학습 데이터로 사용함
• 본 기술은 auto regressive 언어 모델로 구성되며, multi-layer transformer에 기반한 아키텍처를 가진다. 언어
모형은 사전 학습 모형(PLM)을 이용하며 이를 전이학습(transfer learning) 하여 일상대화를 생성함
3. 기술 활용 및 응용 분야
• 본 기술은 챗봇에서 대화 상황의 응답 생성에 사용될 수 있다.
• 사용자 친화적 UX 개발에 응용될 수 있다.
• 데모 : http://nlplab.iptime.org:36200/dialo
4. 실험
4.1 실험 개요
• ai-hub의 오피스 일상대화 데이터 집합을 이용하여 일상대화 생성 모델을 학습하고 이를 이용하여 문장을
생성하도록 하였음. 데이터 집합은 1,325개의 single-turn 대화로 구성되어 있음
• ko-gpt2 PLM을 전이 학습하여 일상대화 생성 모델을 학습하며, top-p 샘플링하여 디코딩을 하도록 하였음
원천기술 I 02. 대화 시스템 69
--- PAGE 70 ---
4.2 실험 결과
• 학습은 1 gpu 환경에서 수렴까지 4시간 가량 소요되며, 수렴되었을 때의 loss와 ppl은 다음과 같음
• mean_loss : 0.24970679059624673, mean_ppl : 1.2984058797359466
• 학습 결과 학습 데이터의 single-turn 대화에 대한 복원이 되는 것을 볼 수 있으며, 학습 데이터에 없더라도 ko-
gpt2의 PLM이 학습한 확률 분포로 표현 가능한 입력 문장에 대해 sensible한 답변 문장을 생성할 수 있음을
확인하였음
Human-Inspired AI 70
--- PAGE 71 ---
시각 질의응답 시스템 07
1. 기술 설명
• 주어진 이미지에 근거한 질의에 대해 알맞은 대답을 하는 기술
• VisDial v1.0 데이터를 활용함
2. 기술 방법
• Faster-RCNN을 통해 이미지 안의 객체(object)들을 추출(extract)하고, 질의-응답 텍스트는 Bi-LSTM으로
임베딩(embedding)함.
• 서로 이질적인 모달리티의 입력값들을 융합하기 위해 단어-단위(word-level), 문장-단위(sentence-level)를
고려하여, 어텐션(attention)을 기반으로 연속적인 정렬(alignment)를 진행함.
• 질문의 의미적 의도를 파악하기 위한 문맥-객체 간의 연결과 단어-객체 간의 연결을 모두 고려함.
원천기술 I 02. 대화 시스템 71
--- PAGE 72 ---
3. 기술 활용 및 응용 분야
• 본 기술은 시각장애인을 보조하는 수단으로 활용할 수 있으며, 텍스트로 이루어진 챗봇이 아니라, 이미지까지
이해하는 AI 챗봇으로 활용 가능함.
4. 실험
4.1 실험 개요
• 시각적 질의응답 데이터 셋인 VisDial v1.0을 이용함
• MVAN(Multi-View Attention Network)를 제안함, MVAN는 Topic-Aggregation 모듈, Context-Matching 모듈,
Modality-Alignment 모듈로 구성됨
4.2 실험 결과
• 기존 모델들 보다 우수한 성능을 보였음.
Human-Inspired AI 72
--- PAGE 73 ---
Model AVG NDCG MRR R@1 R@5 R@10 Mean
LF[5] 12 45.31(13) 55.42(12) 40.95 72.45 82.83 5.95
HRE[5] 12 45.46(12) 54.16(13) 39.93 70.45 81.50 6.41
MN[5] 11 47.50(11) 55.49(11) 40.98 72.30 83.30 5.92
GNN[34] 10 52.82(10) 61.37(10) 47.33 77.98 87.83 4.57
CorefNMN[15] 9 54.70(9) 61.50(9) 47.55 78.10 88.80 4.40
RVA[21] 8 55.59(8) 63.03(7) 49.03 80.40 89.83 4.18
DualVD[11] 7 56.32(7) 63.23(5) 49.25 80.23 89.70 4.11
Synergistic[8] 6 57.32(3) 62.20(8) 47.90 80.43 89.95 4.17
CAG[9] 5 56.64(6) 63.49(4) 49.85 80.63 90.15 4.11
DAN[12] 4 57.59(2) 63.20(6) 49.63 79.75 89.35 4.30
HACAN[32] 3 57.17(4) 64.22(3) 50.88 80.63 89.45 4.20
FGA[26] 2 56.90(5) 66.20(1) 52.75 82.92 91.07 3.80
MVAN(ours) 1 59.37(1) 64.84(2) 51.45 81.12 90.65 3.97
Synergistic [8] 5 57.88(4) 63.42(5) 49.30 80.77 90.68 3.97
CDF [13] 2 59.49(2) 64.4(4) 50.90 81.18 90.40 3.99
DAN [12] 2 59.36(3) 64.92(3) 51.28 81.60 90.88 3.92
FGA [26] 2 57.20(5) 69.30(1) 55.65 86.73 94.05 3.14
MVAN (ours) 1 60.92(1) 66.38(2) 53.20 82.45 91.85 3.68
• 시각화를 통해 정답을 추론할 때, 주어진 입력값의 어느 부분에 집중하는지 나타냄.
5. 참고
• 논문 : https://arxiv.org/abs/2004.14025
• 코드 : https://github.com/taesunwhang/MVAN-VisDial
• 데모 : http://nlplab.iptime.org:34242/
원천기술 I 02. 대화 시스템 73
--- PAGE 74 ---
08 화자의 페르소나를 반영한 대화 모델
1. 기술 설명
• 기존 칫챗(chit-chat) 대화 시스템에서 모델이 일관성 없는 답변을 하거나, 재미가 없는 답변을 만드는 등의 문제점을
해결하기 위하여 페르소나 대화 데이터(PERSONA-CHAT)와 이를 활용한 태스크가 만들어졌음
• 페르소나 대화 데이터에서는 페르소나를 프로필 정보로 지니고 있는 두 명의 화자가 서로의 페르소나를 기반으로
대화를 주고받음
• 기계가 이와 같이 페르소나 정보를 가지게 되면 조금 더 일관성 있고 사람과 같이 재치 있는 답변을 할 수 있음
• 페르소나 대화 데이터를 사전학습된 언어모델에 미세조정하여 답변 선택을 잘할 수 있는 모델임
페르소나 대화
데이터의 예시
2. 기술 방법
• 본 기술은 사전학습된 언어모델 BERT의 미세조정을 이용하여 페르소나 태스크에 맞게 학습함
• 학습 시 아래 그림과 같은 추가적인 보조 태스크를 설정하여 multi-task learning으로 학습, 페르소나 기반 답변 선택
학습에 도움이 될 수 있도록 함
• 보조 태스크 1은 대화가 페르소나에 기반하여 이루어지는 것에서 착안하여, Sentence Transformer를 이용하여
페르소나-발화 쌍을 찾고, 발화에 대한 distractor 두 개를 추가한 후 그 중에서 올바른 답을 찾을 수 있도록 학습
• 보조 태스크 2는 보조 태스크 1과 같은 방법으로 페르소나-발화 쌍을 찾고, 발화와 페르소나 문장에 대한
distractor를 각각 두 개씩 추가한 후 올바를 쌍에 1을 라벨, 아닌 후보들에 0을 라벨하여 학습
Human-Inspired AI 74
--- PAGE 75 ---
보조 태스크 1
(Multiple choice)
보조 태스크 2
(Sentence labeling)
3. 기술 활용 및 응용 분야
• 본 기술은 페르소나를 반영하여 개인 맞춤 대화 시스템에 활용될 수 있으며, 다중 언어에 대한 번역기에 활용될 수
있으며, 다중 언어 문서에서 정보검색 모델에서도 활용될 수 있다.
4. 실험
4.1 실험 개요
• 보조 태스크의 효과를 검증하기 위하여, 미세조정만 진행한 BERT와 보조 태스크를 추가한 경우를 비교 실험하였음
4.2 실험 결과
• 실험 결과, 미세조정만 진행한 BERT와 비교했을 때, 보조 태스크와 함께 multi-task learning을 한 경우 약 1-2%씩
성능이 오른 것을 확인할 수 있었음. 이는 효과적인 보조 태스크를 선정하여 multi-task learning으로 학습시 주
태스크에도 좋은 영향을 줄 수 있는 것으로 해석할 수 있음
원천기술 I 02. 대화 시스템 75
--- PAGE 76 ---
09 지식 검색 기반 일반 상식 문장 생성기(영어)
1. 기술 설명
• 주어진 동사 및 명사를 조합하여 하나의 짧은 단문 생성하는 모델
• 동사 및 명사를 어떠한 방식으로 조합하며, 관계를 지니고 있는지 충분한 정보가 주어지지 않은 상황에서 일반적인
관계 추론 능력과 종합적인 일반화 능력을 지닌 언어 모델을 통해서 우수한 문장을 생성
2. 기술 방법
• 본 기술은 비트겐슈타인의 그림 이론 (Picture Theory)와 사용 이론 (Use Theory)를 바탕으로 외부 지식을 새롭게
검색하여 주어진 개념 정보인 동사 및 명사에 대한 일반 상식에 해당하는 지식의 범위를 넓히고 문장 생성 능력을
크게 강화함
• 검색된 지식 정보는 생성 언어 모델의 일반 상식 추론 능력을 강화하는 훈련에 사용되며, 하나의 파이프라인을
지니고 있음
Human-Inspired AI 76
--- PAGE 77 ---
3. 기술 활용 및 응용 분야
• 본 기술은 영어 기반의 생성 모델의 일반 상식 능력을 강화하는 기반 기술로 역할
• 주어진 개념 정보를 활용하여 긴 산문 및 짧은 단문을 생성하는 서비스
• 데모 : http://nlplab.iptime.org:47074/
4. 실험
4.1 실험 개요
• EMNLP 2020에서 공개한 CommonGen 데이터셋을 바탕으로 훈련 및 평가를 진행. 본 기술은 4개의 딥러닝
기반의 언어 모델에 대해서 성능 개선을 적용. 특히, 기존 연구와 다르게 인코더-디코더 구조뿐만 아니라, 디코더만
지니고 있는 GPT 및 GPT2에 해당하는 모델에 대해서도 성능 개선이 가능
4.2 실험 결과
• 본 기술의 정성적인 실험 결과는 데모를 통해서 확인 가능하며, 데모에서는 T5-large 모델을 바탕으로 사용자가
입력한 동사 및 명사를 적절히 조합하여 하나의 짧은 단문을 반환함
원천기술 I 02. 대화 시스템 77
--- PAGE 78 ---
10 KommonGen: 한국어 일반 상식 추론을 위한 데이터
1. 기술 설명
• 한국어 생성 모델의 성능을 측정할 수 있는 정량적인 데이터 및 평가 지표
• 한국어 동사 및 명사 정보 또는 실질 형태소 정보를 개념 집합으로 사용하여 단문의 문장을 생성하도록 하는 기술
2. 기술 방법
• 본 기술은 일상에서 흔히 볼 수 있는 사물과 행동으로 구성된 개념 정보를 재구성하여 한국어 문장을 생성하고,
해당 생성 결과를 바탕으로 성능을 평가
• MS-COCO 데이터셋의 캡션 정보를 한국어로 1차 번역한 576,704개의 문장 중에서, 종결어미 ‘다.’로 끝나는
163,629개의 문장을 선별
• 선별한 문장은 한국어 형태소 분석 패키지 KoNLPy를 활용하여 Mecab으로 형태소 분절하고, 체언 및 용언을
바탕으로 개념 정보 집합을 구성
• 개념 집합은 한국어 기본 문형을 고려해서 최소 1개의 용언과 2개의 체언을 포함하도록 했으며, 원형의 문장과 쌍을
이루도록 함
3. 기술 개선
• 기존에 구성했던 KommonGen의 데이터의 소스를 확장하고, 개념 정보 구성 방식을 한국어의 특징을 반영하도록
수정
• AI-Hub에서 공개한 “한국어 대화 요약” 데이터를 추가적으로 활용했으며, 개념 정보의 형태소 분절 방식을 실질
형태소로 변경
• 실질 형태소 기반의 구성 방식은 한국어 생성 모델이 문장을 구성하는데 있어서 교착어의 성격을 반영할 수
있도록 함
• 반자동화 기반의 데이터 생성 기법을 적용했으며, Named Entity Recognition, Sentence Level Filtering, and
Ethical Consideration으로 필터링을 적용하여 데이터 품질을 상승
4. 기술 활용 및 응용 분야
• 본 기술은 한국어 기반 생성 언어 모델의 일반 상식 추론 능력을 평가할 수 있음
• Data-to-Text 분야에서 작문 등의 주어진 개념 정보를 조합하여 단문의 문장을 생성하는 경우 적용 가능
Human-Inspired AI 78
--- PAGE 79 ---
5. 실험
5.1 실험 개요
• 단일 한국어 생성 모델 및 다국어 생성 모델에 대해서 주어진 조건을 재구성하여 문장을 재구성하는 능력 평가
5.2 실험 결과
• 인코더-디코더 모델은 디코더만 지닌 모델보다 우수한 성능을 지님
• KoBART 모델은 다국어 기반의 모델보다 적은 파라미터를 지녔음에도 불구하고 우수한 성능을 지니고 있음
• mT5 모델은 가장 우수한 성능을 보이고 있으며, 한국어 문장 구성을 고려한 실험 세팅은 사람에 가까운 성능을
달성하는데 큰 기여를 함
원천기술 I 02. 대화 시스템 79
--- PAGE 80 ---
6. 데이터셋 공개 링크
• 기존 버전의 KommonGen 데이터셋: https://github.com/J-Seo/KommonGen
• AI-Hub에 공개한 개선된 데이터셋: https://aihub.or.kr/opendata/korea-university
Human-Inspired AI 80
--- PAGE 81 ---
페르소나 및 지식 기반 대화 데이터와 베이스라인 모델 구축 11
1. 기술 설명
• 본 기술은 외부지식과 사용자의 페르소나를 적절하게 사용하여 답변하는 Customized Answer를 생성할 수 있는
챗봇을 목적으로 개발함
• 이는 기계가 답변을 생성할 때, 대화의 맥락뿐만 아니라 외부 지식과 사용자의 페르소나를 반영하는 모델을 의미함
• 대화 데이터는 여행지에 대한 지식을 제공할 때, 사용자를 고려한 답변을 제공하는 영어 대화 데이터를
구축하였으며, 데이터 구축 후 전문적인 검수 작업을 통하여 데이터의 윤리성 확인 및 정확성 검토를 완료함
[ 그림 ]
챗봇 예시
[ 그림 ]
구축된
대화 데이터 예시
원천기술 I 02. 대화 시스템 81
--- PAGE 82 ---
2. 기술 방법
• 데이터 구축은 페르소나 구축, 대화 구축의 두 단계로 진행하였으며, Amazon Mechanical Turk을 이용함
• 페르소나 구축
- 페르소나 구축을 위해서 하나의 랜드마크에 대한 Wikipedia 페이지를 제공하였으며, 작업자는 먼저 해당 페이지에
있는 키워드를 추출하고, 이를 기반으로 가상의 페르소나를 작성함
- 페르소나는 취미, 취향, 경험 등 현실에 있을 법한 개인의 배경 정보를 만드는 일로 정의하였으며, 페르소나를
구축한 후 작업자는 대화를 구축함
• 대화 구축
- 한 명의 작업자가 대화를 만들게 되는데 Human과 Machine의 역할을 번갈아 수행하며 가상의 대화를 만듦.
Human은 주어진 랜드마크에 대하여 질문하는 역할을 하며, Machine은 Human의 질문에 대하여 답변함
[ 그림 ]
대화 데이터 예시
Human-Inspired AI 82
--- PAGE 83 ---
• 구축한 데이터를 이용하여 베이스라인 모델을 개발함
• Retrieval Module
- 검색 모듈을 이용하여 해당 위키피디아 문서를 문단 단위로 분리함
- 분리된 지식 중에서 Human의 이전 발화에 대하여 답변하기 적절한 5개의 지식을 추출함
- 이를 통하여 전체 지식을 사용하는 대신 관련도 높은 지식을 추려서 더 효과적, 효율적인 연산 가능
• Context-Relevant Representation (CR)
- 검색 모듈에서 추출된 다섯 가지의 지식과 이전 대화, 페르소나 정보를 각각 사전학습 언어모델 (GPT-2, BART,
transformer)에 넣은 후 마지막 레이어의 맨 앞자리 토큰 벡터를 추출
- 대화, 페르소나 정보에 대하여 하나, 다섯가지 지식에 대하여 각각 하나씩 총 여섯 개의 벡터가 추출되고 이를
붙여서 CR을 만듦
• Persona Grounding (PG)
- CR을 이용하여 답변을 생성하기 위한 적잘한 페르소나를 뽑는 능력을 테스트하는 PG를 진행함
- PG에는 페르소나 문장 5개가 후보로 들어가며, 각각의 문장에 대하여 해당 문장을 사용할 것인지 아닌지 고르도록
학습함
• Knowledge Grounding (KG)s
- CR을 이용하여 답변을 생성하기 위한 적절한 지식을 뽑는 능력을 테스트하는 KG를 진행함
- KG에는 사용자의 해당 턴의 답변을 생성할 때 사용한 정답 지식을 포함하여, 다른 문서에서 추출된 오답 9개가
포함되어 있으며, 이에 대하여 하나의 정답 지식을 뽑도록 학습함
• Language Modeilng (LM)
- PG와 KG에서 선택된 페르소나와 지식을 LM의 입력으로 준비함
- 입력에는 페르소나, 지식, 이전 발화가 순서대로 들어가며, 이러한 입력을 가지고 답변 발화에 대하여 langauge
modeling을 학습시킴
[ 그림 ]
모델 개요도
원천기술 I 02. 대화 시스템 83
--- PAGE 84 ---
3. 기술 활용 및 응용 분야
• 대화 에이전트가 어떠한 지식에 대해 설명할 때, 사용자의 페르소나를 반영하여 사용자 맞춤 지식을 제공할 수 있는
대화 데이터를 구축함
• 페르소나와 지식을 모두 반영한 답변 제공 가능한 모델을 만들 수 있음
• 사용자에게 공감하거나, 추천을 하는 등 기존 챗봇 모델의 한계를 넘는 패러다임 제안
• 이를 통해 보다 더 사람같은 발화를 할 수 있는 모델을 만들 수 있음
• 해당 모델은 여러 산업 분야에서 사용자 맞춤 지식 제공을 하는 챗봇으로 사용될 수 있음
• 도메인을 확장하여 여행지 뿐 앙니라, 박물관, 미술관 등 다른 도메인에 대해서도 전문적인 지식을 사용자 맞춤
형태로 답변하는 agent 개발 가능
4. 실험
4.1 실험 내용
• 각 모델에 대한 생성 성능과 그라운딩 성능을 확인하고자 정량적 실험을 진행함
• 각 모델 옆의 +PG, +KG, +PG +KG는 학습 시 해당 태스크에 대한 학습이 이루어졌는지에 대한 여부를 나타냄
• 실험 결과 BART 모델을 KG와 학습시킨 결과가 가장 좋은 결과를 보임
Generation Grounding(Acc.)
Models
PPL chrF++ BLEU R-1 R-2 R-L Persona Knowledge
Decoder +PG +KG 228.69 0.1565 3.53 22.41 4.78 18.60 67.83 64.28
Enc-Dec +PG +KG 428.75 0.1345 2.79 18.45 2.81 14.80 67.83 64.52
GPT-2 17.42 0.1942 5.97 26.61 9.73 23.13 65.50 10.71
GPT-2 +PG 18.45 0.2221 5.63 25.56 7.12 22.20 67.83 9.25
GPT-2 +KG 10.73 0.2875 11.29 36.35 19.89 32.35 45.61 71.33
GPT-2 +PG +KG 11.45 0.2777 10.65 35.26 18.82 31.33 67.83 70.95
BART 26.55 0.1982 5.70 25.67 8.90 21.70 67.49 14.05
BART +PG 26.54 0.1932 5.36 25.35 8.43 21.40 67.83 14.75
BART +KG 15.84 0.2946 11.64 36.19 19.90 31.84 53.78 73.00
BART +PG +KG 23.25 0.2887 11.28 35.35 19.12 31.06 67.83 71.70
Human-Inspired AI 84
--- PAGE 85 ---
• 각 모델에 대하여 정성적인 평가를 위하여 사람 평가 진행
• Flueny, Engagement, Consistency와 모델간의 순위를 비교하는 Rank에 대하여 측정함
• 실 험 결과 원본 데이터셋이 가장 좋은 평가를 받았으며 BART, GPT-2가 그 뒤를 이었음
Model Rank Fluency Engagement Consistency
Human 1.05(0.31) 4.15(1.54) 4.08(1.53) 4.06(1.47)
GPT-2 2.64(0.48) 2.85(0.93) 2.95(0.98) 2.76(0.99)
BART 2.31(0.52) 3.13(1.14) 3.18(1.08) 3.10(1.04)
원천기술 I 02. 대화 시스템 85
--- PAGE 86 ---
12 PEEP-Talk: 상황별 영어 교육을 위한 챗봇
1. 기술 설명
• 상황별 영어 교육이란 특정 상황에서의 회화학습을 의미함. 예를 들어, 공항에서 환전하기, 해외 관광지에서 사진
촬영 부탁하기 등 교육적으로 가정된 상황에서의 회화학습을 의미함. 본 기술은 인공지능 챗봇을 활용하여 다양한
상황에서 회화학습을 가능함
2. 기술 방법
• 본 기술은 AI Hub의 한-영 번역 말뭉치를 활용하여 상황 데이터셋을 구축하여 활용하며, 상황 데이터셋이란
상황별 대화 기록이 포함된 대화 데이터셋을 일컬음. 챗봇 대화 모델을 구성하기 위해 구어체 데이터셋으로 학습된
DialoGPT를 활용하며, 이는 기존의 문어체로 학습된 GPT-2 보다 효과적인 대화가 가능함
• 본 기술은 상황 데이터셋으로 학습된 대화모델, 대화를 조절하는 Context Detector로 구성됨. 입력으로 상황과
대화 기록이 대화 모델에 입력되며 상황에 적절한 답변을 출력하며, 이후, Context Detector는 상황에 적합한 대화
유무를 판단함. 보조적으로 GEC 모듈은 영어 문법의 오류를 수정함
3. 기술 활용 및 응용 분야
• 본 기술은 초보 영어 학습자를 대상으로 하며, 영어를 처음 배우는 학습자는 다양한 상황에서 여러 대화를 연습할
수 있음
• 데모 : http://peeptalk.xyz
4. 실험
4.1 실험 개요
• 구축된 상황 데이터셋을 사용하여 다양한 모델에 실험을 한 결과 DialoGPT가 가장 좋은 성능을 보임. 이는 신경망의
사용과 상황 데이터셋과 유사한 구어체 데이터셋으로 사전학습을 진행하여 높은 성능을 보인 것으로 추측됨
Human-Inspired AI 86
--- PAGE 87 ---
4.2 실험 결과
• 실험을 위해 상황 데이터셋과 가장 유사한 PERSONA-CHAT의 모델을 실험군으로 선정 함. DialoGPT의 경우,
파라미터의 크기에 따라 SMALL, MEDIUM, LARGE로 나누어 실험을 진행함
Validation Test
Models
PPL Hits@1 PPL Hits@1
Profile memory 42.79 0.078 45.02 0.069
Lost In Conversation - 0.168 - 0.152
Transfer Transfo GPT 12.41 0.849 12.74 0.839
Transfer Transfo GPT-2 12.50 0.839 12.56 0.848
DialoGPT SMALL 12.35 0.850 12.55 0.856
DialoGPT MEDIUM 14.77 0.884 13.89 0.877
DialoGPT LARGE 11.15 0.889 12.04 0.864
5. 결과 화면
원천기술 I 02. 대화 시스템 87
--- PAGE 88 ---
13 유형 다양성을 고려한 교육용 질의응답쌍 생성 모델
1. 기술 설명
• 질의응답쌍 생성 기술(Question-Answer pair Generation, QAGen)이란 주어진 맥락(Context) 정보에 대해
자동으로 질의 및 응답의 쌍을 생성하는 기술을 의미함
• 교육 목적에서의 QAGen 기술은 아이들의 학습을 위한 목적으로 활용되는 만큼 우수한 품질의 질의응답 쌍을 요함.
특히, 문답법(Questioning)을 활용한 아이들의 교육 과정에서 맥락 정보의 여러 측면이 반영된 다양한 유형의
질의응답쌍의 활용은, 질문의 유형에 따라 정답의 도출 과정에서 아이들의 뇌의 다양한 부분을 자극하여 더 좋은
교육 결과를 도출할 수 있다는 기존의 연구들이 존재함
• 이를 위해, 질의-응답 간, 맥락-질의응답쌍 간 연관성(Relevancy) 및 유창성(Fluency)을 보장하는 방향으로
질의응답쌍을 생성함과 동시에, 종합적 문해력 향상을 위해 생성되는 질의응답쌍의 다양성이 되는 방식의 생성
기술의 필요성이 높음
2. 기술 방법
• 본 기술은 크게 (1) 질의집중요약 기반 정답 생성기(QFS-based Answer Generator), (2) 질의응답 순환
생성기(Iterative QA Generator), 그리고 (3) 관련성 중심의 순위 생성기(Relevancy-aware Ranker)로 세분화
되어있으며, 자세한 생성 과정은 다음과 같음
• QFS-based Answer Generator는 주어진 맥락에서 핵심적인 정보들을 포착하여 암시적(Implicit)이면서
합리적(Plausible)인 최초의 정답을 생성하는 모듈임. 이러한 최초의 양질의 정답 생성을 위하여
질의집중요약(Query-focused Summarization, QFS) 기술을 적용하여 생성된 질의집중요약을 맥락 정보와 함께
제공하여 정답을 생성함
• Iterative QA Generator는 질의 생성(Question Generation) 모델과 질의 응답(Question Answering) 모델을
순환적으로 활용하는 구조를 가짐. 6가지 질의 유형(What, Who, Where, When, Why, How)을 프롬프트(Prompt)로
활용하여 QG 모델을 학습하며, 추론 과정에서 생성된 6N개의 질의응답쌍에 대해 QA 모델을 활용하여 응답을
재조정하는 과정을 진행함
• Relevancy-aware Ranker는 앞서 생성된 질의응답쌍을 선별하여, 우수한 품질을 지니는 질의응답쌍만을 최종
결과로 채택함. 순위 생성기의 학습은 대표적인 동화 기반 QAGen 데이터셋인 FairytaleQA 데이터셋에 대하여 in-
context negative sampling 기법을 통해 진행하며, 추론 과정에서는 앞서 생성된 6N 개의 질의응답쌍들에 대해
맥락 정보와 함께 입력으로 제공되어 순위 생성기로 하여금 마지막 은닉 상태(last hidden states) 중 [CLS] 토큰에
해당하는 표현값을 추출하여 소프트맥스(Softmax) 함수를 적용함으로써 각 레이블로 예측될 확률 값을 얻은 후,
내림차순으로 정렬하며 중복 제거 과정 (overlap mitigation) 역시 거침으로써 기생성된 질의응답쌍들에 대한 최적의
순위를 산출해냄
Human-Inspired AI 88
--- PAGE 89 ---
• 하기 그림은 상기 설명한 본 기술의 전체적인 구조도이며, 3-stage Framework로 구성됨을 확인 가능함
3. 기술 활용 및 응용 분야
• 교육자의 교육 목적으로의 질의응답쌍 생성을 돕는 human-AI collaborative 서비스
• 로봇에 QAGen 모델을 탑재하여 교육용 로봇으로 활용
• Question-Answering 성능 향상을 위한 데이터 증강 기법으로 활용
• 동화 데이터뿐만 아니라 단락-질의-응답 쌍을 지닌 학습 데이터셋이 존재한다면 교육 도메인이 아닌 다른
도메인에도 적용 가능
4. 실험
• 제안하는 QAGen 모델의 성능을 확인하기 위해 기존 논문들과의 정량적 평가를 수행함
• MAP@N 스코어란, 각 ground-truth(GT) QA쌍에 대해 Rouge-L f1 스코어(또는 BERTScore f1 스코어)가 가장 높은
하나의 후보 QA쌍을 생성된 후보 QA쌍 중에서 선택한 후, 전체 값들에 대한 평균을 측정한 지표임
• 실험 결과 다른 교육용 QAGen 모델보다 더 높은 성능을 보임
MAP@N (Rouge-L F1) MAP@N (BERTScore F1)
Method Top 10 Top 5 Top 3 Top 1 Top 10 Top 5 Top 3 Top 1
FQAG
0.440 / 0.435 0.375 / 0.374 0.333 / 0.324 0.238 / 0.228 0.9077 / 0.9077 0.8990 / 0.8997 0.8929 / 0.8922 0.8768
(Yao et ai., 2022)
SQG
0.460 / 0.455 0.392 / 0.388 0.344 / 0.337 0.234 / 0.242 0.9056 / 0.9062 0.8953 / 0.8955 0.8876 / 0.8878 0.8707 / 0.8723
(Dugan et al.,2022)
Ours 0.500 / 0.503 0.426 / 0.429 0.369 / 0.372 0.247/0.254 0.9156 / 0.9178 0.9046 / 0.9068 0.8956 / 0.8977 0.8752 / 0.8783
• QAGen 모델이 생성한 QA쌍 질을 평가하기 위해 휴먼평가를 수행함. (Diversity-Q(A): 질문(정답)의 다양성
순위, Quality-E: 전체적인 QA쌍의 순위, Relevancy: 단락-QA쌍 간 관련성, Acceptability: 단락-QA쌍이 옳게
생성되었는지의 타당성, Usability: 교육 목적의 활용가능성, Readability: 문법성, Difficulty: 난이도, ↑: 높을수록
좋음, ↓:낮을수록 좋음)
• 전체적으로 높은 성능을 보임(Krippendorff’s alpha score: 0.59-0.61)
• 생성된 QA 쌍의 유형 다양성을 평가하기 위해 질의 및 응답 유형별 통계를 제시함
• 기존 연구들에 비해 균형있게 질의 및 응답 유형이 나타남
원천기술 I 02. 대화 시스템 89
--- PAGE 90 ---
• QAGen 각 모듈에 대한 효과를 파악하기 위한 Ablation 실험 및 분석을 수행함
• 실험 결과 각 모듈이 전체 성능 향상에 긍정적으로 기여하고 있음을 확인
global local
Method Divesity-Q↓Diversity-A ↓ Quality-E↓ Relevancy ↓Acceptability↓ Usability↓ Readability ↓ Difficulty ↑
FQAG (Yao et al., 2022) 3.03 3.06 2.66 1.35 1.86 2.26 1.36 2.11
SQG (Dugan et al., 2022) 2.96 3.03 3.3 1.56 2.13 2.66 1.45 2.36
Ours 2.35 2.18 2.35 1.31 1.78 2.1 1.65 2.98
GT 1.65 1.71 1.68 1.03 1.35 1.5 1.2 2.95
MAP@N (Rouge-L F1)
Method Top 10 Top 5 Top 3 Top 1
Ours 0.503 0.429 0.372 0.254
wio QFS 0.472 0.401 0.348 0.248
wlo Iteration 0.463 0.427 0.378 0.253
wlo Contrastive learning 0.438 0.375 0.326 0.261
Human-Inspired AI 90
--- PAGE 91 ---
페르소나 및 지식 기반 대화 데이터를 활용한 대화 모델 개발 14
1. 기술 설명
• 기존에 개발된 대화 시스템은 단순히 지식을 전달하거나 혹은 사용자에게 공감하는 형태의 발화를 구현하였음.
기존의 모델들은 지식을 사용자에게 맞춤형으로 전달하는 능력이 부족하였음. 또한, 이용자가 좋아할 만한 지식을
제안하는 형태의 발화하는 능력도 현저하게 떨어졌음
• 이를 위하여 본 연구에서는 지식과 사용자의 페르소나를 동시에 검색 후 이를 반영하여 답변을 하는 모델을 제안함.
제안하는 대화 모델은 적절한 지식과 페르소나를 후보군에서 예측하며, 이를 위한 점수 모듈은 Poly-encoder를
통해서 구현됨. 이렇게 예측한 지식과 페르소나를 이용하여 마지막 발화를 생성하게 됨. 이때, 제안하는 모델은
기존의 모델과 다르게 Hallucination이 적고, engagingness가 높은 형태로 발화가 생성됨을 확인하였음
• 본 연구에서는 대표적인 Persona-knowledge 대화 데이터셋인 FoCus 데이터셋을 이용하였으며, grounding,
generation 에서 모두 매우 높은 성능을 달성하였음. 또한, 해당 모델의 발화가 hallucination이 적고, persona
engagingness가 높다는 것을 실험적으로 증명하였음. 또한, 제안한 모델의 retriever 성능도 실험적으로 보여주었고,
후보 점수 모듈에 대한 변화도 실험적으로 보여주었음
Dialogue
Human : Is it in England?
Machine : No, it is actually in Scotland where you are going.
Human : Where in Scotland?
Human’s Persona
I will travel through North Ayrshire.
I am going to Scotland.
I like history.
I am interested in architecture.
I love to garden.
Ground Truth Knowledge
Eglinton Castle was a large Gothic castellated mansion in
Kilwinning, North Ayrshire, Scotland.
Predicted Answers
BART base It is in Scotland, which is a place you love.
BART large It is in Scotland. in Scotland. in Scotland. in
Ground Truth Response
It is in North Ayrshire so you could visit when you travel through. [그림]
기존 모델의 한계점
원천기술 I 02. 대화 시스템 91
--- PAGE 92 ---
2. 기술 방법
• 보다 지식이 풍부하고 매력적인 대화를 생성하기 위해 본 연구에서는그림 2와 같이 외부 지식과 페르소나 정보를
기반으로 하는 대화 모델을 소개함
[그림]
모델 개요도
• 그림과 같이, 본 연구에서는 먼저 사전학습 모델을 이용하여 입력을 구성하고, 적절한 페르소나와 지식을 골라서
대화 history와 concat한 후 KPEQ (Knowledge Persona Enhanced Query) 이라고 명명함. Poly encoder는
후보군과 주어진 맥락간의 관련성을 잘 보는 특성이 있기 때문에, 페르소나와 지식을 고를때 사용함. KPEQ은 그
다음 RAG모델에 입력되고 모델은 다음 발화를 생성하게 됨. RAG 모델은 RAG-token 모델과 RAG-sequence 모델이
있는데, RAG-token모델은 토큰을 하나 생성할 때마다 loss를 marginalize하는 모델이고, RAG-sequence모델은
하나의 문장을 모두 생성한 후 loss를 marginalize하는 모델임. 본 연구에서는 두 모델을 모두 이용해서 구현함
3. 기술 활용 및 응용 분야
• 대화 에이전트가 어떠한 지식에 대해 설명할 때, 사용자의 페르소나를 반영하여 사용자 맞춤 지식을 제공할 수 있는
대화 데이터를 구축함
• 페르소나와 지식을 모두 반영한 답변 제공 가능한 모델을 만들 수 있음
• 사용자에게 공감하거나, 추천을 하는 등 기존 챗봇 모델의 한계를 넘는 패러다임 제안
• 이를 통해 보다 더 사람같은 발화를 할 수 있는 모델을 만들 수 있음
• 해당 모델은 여러 산업 분야에서 사용자 맞춤 지식 제공을 하는 챗봇으로 사용될 수 있음
• 도메인을 확장하여 여행지 뿐 앙니라, 박물관, 미술관 등 다른 도메인에 대해서도 전문적인 지식을 사용자 • 맞춤
형태로 답변하는 agent 개발 가능
4. 실험
4.1 실험 내용
• 본 연구에서는 FoCus 데이터셋에서 페르소나와 지식을 잘 반영하는 모델의 성능을 측정하였음. FoCus 데이터
셋에는 Generation태스크와 Grounding 태스크가 존재함. Generation 태스크는 발화 생성 능력을 평가하고,
Grounding 은 적절한 페르소나와 지식을 얼마나 잘 골랐는지에 대한 능력을 평가함. Generation 평가 메트릭으로는
ChrF++, BLEU, ROUGE-1,2, L, BERT-score가 있고, Grounding으로는 Persona, Knowledge Accuracy가 있음. 표에
Human-Inspired AI 92
--- PAGE 93 ---
있는 GPT2, BART는 이전의 FoCus 데이터셋에서 발표한 베이스라인이며, INFO는 본 연구에서 제안한 모델임
Generation Grounding (Acc.)
Models
chrF++ BLEU R-1 R-2 R-L BERTScore Persona Knowledge
GPT2 small 28.73 11.43 36.58 19.44 32.62 88.56 67.44 69.59
GPT2 rnedtum 30.12 12.31 38.29 21.17 34.12 88.92 67.44 72.42
BART base 29.77 11.99 36.24 19.73 32.13 88.35 67.45 72.18
BART Iarge 30.69 11.91 36.57 19.83 32.05 88.1 67.44 71.01
INFO (SRS) 51.33 29.36 53.36 40.36 51.16 92 82.7 99.24
[그림]
INFO (SRT) 53.29 31.46 58.26 42.35 53.06 92.29 80.87 99.22
정량적 실험 결과
• 각 모델에 대하여 정성적인 평가를 위하여 사람 평가 진행
• 본 연구에서는 제안하는 모델 성능을 정성적으로 평가하기 위하여 Human 평가를 진행하였음. 평가에서의
메트릭은 약 5개로, Adequacy, Fluency, Provenance, Engagingness, Hallucination의 정도를 물어보았음. 약 5개의
모델 생성 답변을 주고, 이들을 잘한 순서대로 Rank를 부여하였음. Hallucination을 제외한 나머지 메트릭에서는
숫자가 작을수록 생성 결과가 좋음을 의미함. 평가를 위해서는 test 셋에서 임의적으로 50개의 대화를 뽑아 평가를
진행하였음. Human 평가의 질을 위한 agreement 점수는 Fleiss’ Kappa 로 계산하였고, 0.4185로 fair agreement를
의미하였음. Human worker들은 Amazon Mturk서비스를 통해서 모집하였음
Avg. Rank
Models
Ad. ↓ H. ↓ Prov. ↓ Eng. ↓ Hali. ↑
GPT-2 small 3.57 3.41 3.58 3.46 2.49
GPT-2 medium 3.11 3.1 3.04 3.25 3.02
BART base 3.43 3.29 3.47 3.22 2.45
BART Iarge 3.31 3.63 3.29 3.44 2.69
[그림]
INFO (Ours) 1.57 1.57 1.62 1.63 4.35
정성적 실험 결과
원천기술 I 02. 대화 시스템 93
--- PAGE 95 ---
[3]
정보 검색/분류
추출/요약 기술
• 머신러닝 기반 보고서 자동 분석 및 키워드 추출 기술
• 메타러닝을 응용한 문서 단위의 관계 추출
• 비정형 위협정보 자동 인식 및 추출
• 머신러닝을 이용한 문서 자동 요약
• 딥러닝을 이용한 유사 문서 검색 및 시각화
• Narrative기반 자동 비디오 분할
• 비지도 학습 알고리즘을 이용한 보고서 자동 분석 및 토픽 자동 추출 기술
• 순차 정보를 이용한 콘텐츠 추천 시스템 개발
• 스케치를 이용한 패션 의류 검색 시스템
• Eye tracking 기반의 휴먼 리딩을 반영한 추출 요약 기법
• Sentence BERT 임베딩을 이용한 과편향 뉴스 판별
• 종교활동을 위한 휴머노이드 질의응답 로봇
• 아이들 교육을 위한 나오 로봇
• GPT2를 활용한 유사 뉴스 기사 추천 시스템
• 나오 로봇을 활용한 이중 언어 교육
• 나오 로봇을 활용한 동화 추천 및 읽기
• Virtual-Try-On Model for Fashion AI
• 사용자 그래프 기반 한국어 가짜뉴스 판별 방법
--- PAGE 97 ---
머신러닝 기반 보고서 자동 분석 및 키워드 추출 기술 01
1. 기술 설명
• 보고서가 증가함에 따라 사용자가 원하고자 하는 문서를 짧은 시간 내에 판단하여 찾기는 쉽지 않음
• 이러한 문제점을 해결하기 위해 보고서에 대한 핵심 키워드를 자동으로 추출하여 사용자가 선택적으로 볼 수
있으며, 이를 통해 사용자가 효율적으로 원하는 문서를 찾을 수 있도록 키워드 추출알고리즘을 이용함
키워드 추출
알고리즘 모델
2. 기술 방법
보고서 자동 분석 및
키워드 추출 모델
• 본 기술은 정답 셋이 없는 Unsupervised Learning으로 진행되었으며, 보고서에 대해 중요 키워드를 추출하는
것으로 전체 문서를 단어 단위로 추출한 후 단어의 빈도수 계산을 하는 키워드 알고리즘을 통해 중요 단어를 추출함
• 개발한 모델은 각 단어의 가중치를 계산한 후 집단 간 텍스트 특성의 차이나 토큰 사이의 관계 등을 분석하여 상위
적당 K개수의 가중치를 가지는 키워드를 선정하는 연구임
3. 기술 활용 및 응용 분야
• 본 기술은 문서에 대한 정보를 간단한 단어로 추출하므로 키워드별 문서 검색, 문서 분류, 문서 간 유사도에 활용될
수 있음
• 데 모 : http://nlplab.iptime.org:32270/
원천기술 I 03. 정보 검색/분류/추출/요약 97
--- PAGE 98 ---
02 메타러닝을 응용한 문서 단위의 관계 추출
1. 기술 설명
• 관계 추출의 목적은 구조화되지 않은 정보에서 구조화된 정보를 추출함으로써 입력받은 정보에 있을 수 있는
중의성을 줄이고, 해당 정보를 처리하는데 있어 그 과정을 단순화하여 처리를 더욱 빠르고 정확하게 분석할 수
있도록 하는 것
• 관계 추출은 크게 2가지 종류로 나뉘는데 전역 수준의 관계 추출(Global Level Relation Extraction)과 문장 수준의
관계 추출(Mention Level Relation Extraction)로 나눌 수 있음
• 해당 연구에서의 목표는 전역 수준의 관계 추출을 하되, 언급 수준의 관계 추출을 병행함으로써 정보의 누락을
최대한 방지하여 성능과 완성도를 유지함
LSTM Encoder와
대화 및 응답 후보
간의 Attention을
반영한 모델 구조도
2. 기술 방법
• 기존 관계추출 방법은 한국어처럼 주어나 목적어가 자주 생략되는 언어를 다룰 경우에는 추출한 결과가 생략된
주어나 목적어에 해당되는 개체들의 관계를 제대로 표현하지 못한다는 약점도 존재함
• 각 개체 간 관계를 외부 메모리에 저장하고 분석하여 여러 문장에 걸쳐 표현되는 개체 간 상호관계를 추출하는
관계추출 모델을 제시함
LSTM Encoder와
대화 및 응답 후보
간의 Attention을
반영한 모델 구조도
• 모델은 단편적 관계 추출 모델과 외부 메모리 신경망으로 이루어져 있음
• 훈련은 각각 단편적 관계 추출 모델의 훈련, 전역 관계를 위한 메모리 증강 신경망 훈련, 마지막으로 메모리 증강
신경망 훈련의 결과를 반영한 관계 추출 모델의 재훈련으로 총 3단계가 존재함
Human-Inspired AI 98
--- PAGE 99 ---
3. 기술 활용 및 응용 분야
• 기술 활용 및 응용분야로는 Knowledge Base 및 Ontology 자동 구축과 텍스트 문서 및 문장 간 관계 정보 요약 및
추출이 존재함
• 본 기술의 단편적 관계추출에 한해서는 데모에서 확인이 가능함
• 데모 : nlplab.iptime.org:32277
4. 실험
4.1 실험 개요
• 단편적 및 전역적 관계 추출의 정확도를 평가하기 위하여 타 모델들과 함께 문서 단위의 평가 데이터로부터 관계
추출을 실행하여 Precision, Recall, F1 Score를 측정함
4.2 실험 결과
• Table 1의 결과를 보면 제안한 모델인 Augmented External Memory Model(AEMM)은 전체적으로 다른 모델들에
비하여 단편적 관계 분류 성능을 비교하면 더 낮은 성능을 보이는데, 이는 외부 메모리 신경망의 전역 관계 분류의
결과에 따라 영향을 받는 것이 오히려 단편적 관계 분류에 악영향을 끼치는 것으로 보임
• 전역적 관계 추출의 비교에서는 AEMM은 타 모델에 비하여 확연히 높은 Global Precision을 보여준다. 이 때문에 비록
Global Recall에서는 타 모델들과 비슷한 성능을 보임에도 F1 score에서 더 높은 성능을 보이는 것을 관측할 수 있음
CNN LSTM 한글 모델 AEMM
Local Precision 0.327 0.341 0.390 0.269
Local Recall 0.315 0.347 0.259 0.307
Local F1 Score 0.321 0.344 0.311 0.287
Global Precision 0.194 0.183 0.198 0.383
Global Recall 0.313 0.332 0.262 0.287 [ 표 ]
단편/전역 관계 추출
Global F1 score 0.240 0.236 0.226 0.328
모델간의 성능비교
원천기술 I 03. 정보 검색/분류/추출/요약 99
--- PAGE 100 ---
03 비정형 위협정보 자동 인식 및 추출
1. 기술 설명
• 본 기술은 딥러닝 기술인 Long Short-Term Memory(LSTM)-Conditional Random Field(CRF)를 이용하여
인텔리전스 보고서 등 문서 파일 내의 비정형 위협정보를 모델링하고 정형화된 형태로 마이닝하기 위한 것이다.
2. 기술 방법
• PDF 문서•들PD을F 를분 H석T하ML기로 위변해환 서는• 문이자로열부터로 글 이자루 크어기진 등 본부가문적을인 파정일보를로 얻부고터 이 추를출 추하후 는프로 과세정스이에 활선용행 되어야 한다. 하지만
PDF 문서는 단락, 문장, 본문 등의 구분이 없으며, 각 글자의 글씨체, 크기와 위치 정보만 담겨 있다. 따라서 PDF
문서를 분•석불하필요여한 텍 메스타텍트스를트 일 제관거 성• 주있기게적 으추로출 반하복고되, 는기 문계자학열습 정 보모를델 이에용 하사여용 제할거 수 있도록 이를 문장 단위로 구분하고
토큰화하는 과정이 선행되어야 한다. 이를 위해 기계학습, 정규표현식, 위키피디아 문서 통계를 활용한 하이브리드
문장 경계 • 인 특수 식 문 기 자 술 정 을 규화 개 발하여 사•용동일하한였 기다능.을 하는 다양한 특수문자를 하나로 통일함
• 추출된 텍스트에 대해서 양방향 LSTM-CRF 모델을 이용하여 위협정보를 추출한다. 해당 모델의 훈련은 지도학습
방법을 이•용연하속된였 줄으 며파악, 이 를 위해 수•타백 말 건뭉의치로 인부텔터리 수젼집스한 다리양포한트 통를계 를수 바집탕하으여로 연이속 중된 단백어여 파 건악의 리포트에 대해 수작업
태깅으로 학습 말뭉치를 구축하였다.
•문장 경계 구분 •타 말뭉치를 이용하여 비지도학습 방법으로 훈련시킨 문장경계 인식 기계학습 모델 사용
•PDF를 HTML로 변환 •이로부터 글자 크기 등 부가적인 정보를 얻고 이를 추후 프로세스에 활용
•단어 토큰화 •규칙 기반 토큰화 모델을 이용하여 각 단어를 토큰화함
•불필요한 메타텍스트 제거 •주기적으로 반복되는 문자열 정보를 이용하여 제거
•특수문자 정규화 •동일한 기능을 하는 다양한 특수문자를 하나로 통일함
•연속된 줄 파악 •타 말뭉치로부터 수집한 다양한 통계를 바탕으로 연속된 단어 파악
•문장 경계 구분 •타 말뭉치를 이용하여 비지도학습 방법으로 훈련시킨 문장경계 인식 기계학습 모델 사용
•단어 토큰화 •규칙 기반 토큰화 모델을 이용하여 각 단어를 토큰화함
PDF2TXT 과정
Human-Inspired AI 100
--- PAGE 101 ---
3. 기술 활용 및 응용 분야
• 리포트 자동 분석 (타 분야 문서로 적용 가능)
• 데모 : http://nlplab.iptime.org:32270/kisa_demo
4. 실험
• 위협정보 개체명 인식 말뭉치 구축 과정
- 원시 말뭉치 수집: 608건의 PDF 형식의 인텔리젼스 리포트 수집. 이 중 가장 라인 빈도수가 높은
파일(1500~5000라인)을 선정하여 배포 (국내외 영문 인텔리전스 리포트 .pdf file 608개, 인텔리전스 리포트를
토대로 전처리 작업한 text file 608개, 데이터 구축에 관한 가이드라인) 태깅 데이터 구축을 위해 보안학과에 재학
중인 5명의 연구원 참여
• 비정형 위협 정보 자동 인식 및 추출 기술의 성능 파악에는 개체명 인식 기술에서 가장 널리 사용되는 정량적 평가
방식인 F-score를 이용한다. 이는 여러 단어로 구성될 수 있는 위협 정보의 특성상 accuracy만으로 평가하기 어려운
점을 반영한 지표이다. F-score는 precision과 recall의 조화평균 값으로, 아래 식에 따라 계산한다.
• 본 기술의 성능을 명확히 검증하기 위해 동일한 시스템을 50회 반복 학습시키고, 학습된 모델의 최종 성능을
통계적으로 비교하였다. 각 모델은 100 epoch동안 학습시키며, 이는 전체 학습 데이터에 대해 100회 훈련되었음을
의미한다. 시간의 흐름에 따른 성능의 변화는 아래 그래프와 같다.
• 이러한 방법으로 총 50개 모델의 성능을 측정한 결과, 평균 F-score는 73.31, 표준 편차는 1.16으로 확인되었다.
[그림]
전체 50개 모델의
학습 과정을 나타낸
그래프. X축은 epoch,
Y축은 F-score를
나타낸다.
원천기술 I 03. 정보 검색/분류/추출/요약 101
--- PAGE 102 ---
04 머신러닝을 이용한 문서 자동 요약
1. 기술 설명
• 본 기술은 비지도 학습 알고리즘을 바탕으로 문장 추출에 의한 자동 문서 요약 방법이다. 특히, 본 기술은 특정
언어나 문서 특징에 의존하지 않으므로 확장이 용이하다.
2. 기술 방법
• 본 기술은 비지도 학습 알고리즘인 K-means clustering을 사용한다. 기본 가정은 비지도 학습 알고리즘을 이용하여
비슷한 아이디어(문장)를 클러스터링할 수 있다는 것이다. 이후 요약을 생성하기 위해 가장 대표적인 문장이 각
클러스터에서 선택된다. 또한, 이 방법을 사용하면 생성된 요약의 단어 수를 어느 정도 제어할 수 있다는 장점이 있다.
• 본 기술의 문서 요약 시스템은 문장 벡터 생성 시 기존의 TF-IDF 방법을 이용한 벡터 생성이 아닌, 딥러닝 방법을
사용한다. 이는 단어 불일치 문제 등을 해결할 수 있다는 장점이 있다. 문장 벡터 생성 후 요약 기술은 클러스터링
기반 추출 요약 방법을 사용한다.
3. 기술 활용 및 응용 분야
• 정보 검색, 자동 요약
• 데모 : http://nlplab.iptime.org:32270
Human-Inspired AI 102
--- PAGE 103 ---
4. 실험
[그림]
본 기술을 이용하여
PDF 형식의
인텔리젼스 리포트를
요약한 결과물.
원천기술 I 03. 정보 검색/분류/추출/요약 103
--- PAGE 104 ---
05 딥러닝을 이용한 유사 문서 검색 및 시각화
1. 기술 설명
• 본 기술은 문서를 가상의 벡터 공간에 투사하고 그 차원을 축소한 후, 이를 시각화하여 지능적으로 유사 문서를
탐색할 수 있는 방법이다.
2. 기술 방법
• 문서를 가상의 벡터 공간에 투사하면, 벡터 공간 모델을 이용하여 문서 간의 유사도를 수치화할 수 있고, 이로부터
유사 문서 검색이 가능해진다. 문서를 벡터 공간에 임베딩하고 검색 등을 수행하기 위해서는 문서를 고정 길이의
벡터로 표현할 수 있어야 한다. 본 기술에서는 문서 임베딩을 생성하기 위해 본 연구실이 보유 중인 문장 임베딩
기술과 문서 자동 요약 기술을 응용하였다.
• 여기서 더 나아가, 문서가 투사된 벡터 공간을 t-distributed Stochastic Neighbor Embedding(t-SNE)와 같은
차원 축소 기법을 이용하면 이를 인간이 시각적으로 인지할 수 있는 공간인 3차원 이하로 변형할 수 있고, 이를
시각화하여 검색 인터페이스로 응용 가능하다. 이를 위해 Tensorboard를 활용하였다.
3. 기술 활용 및 응용 분야
• 정보 검색, 문서 분류
Human-Inspired AI 104
--- PAGE 105 ---
4. 실험
[그림]
문서 임베딩 공간을
시각화한 결과.
[그림]
선택한 문서와 유사한
문서들의 목록.
원천기술 I 03. 정보 검색/분류/추출/요약 105
--- PAGE 106 ---
06 Narrative기반 자동 비디오 분할
1. 기술 설명
• 최근 동영상 이해(Video Understanding)에 대한 연구는 다양한 분야에서 이루어지고 있다. 해당 연구에서는 이러한
비디오 이해의 전처리 과정으로써 입력받은 비디오를 의미적으로 통일성을 지니는 단편적인 영상으로 나누는 것을
목표로 함
2. 기술 방법
• 의미적으로 통일성을 지니는 단편적인 영상 감지를 진행하기 위해서는 먼저 비디오를 장면 단위로 나눔
• 실질적으로 영상을 장면단위로 모두 처리하는 것은 실질적으로 너무나 많은 연산과 비용을 소요하기 때문에
장면단위로 나눈 영상을 각각 분석하여 해당 장면을 대표할 이미지를 찾음
• 이미지로부터 정보를 추출하여 의미적으로 연결된 shot들을 판별하여 의미적으로 통일된 Scene들의 집합으로 다시
조합함
[ 그림 ]
Video Scene
Detection 모델
구조도
3. 기술 활용 및 응용 분야
• 동영상 이해를 위한 자동적인 전처리 과정으로 동영상 자동 분할 시스템을 이용하여 자동적인 영상분할을 통하여
야구, 축구와 같은 동영상으로부터 하이라이트를 분리하여 추출할 수 있음
Human-Inspired AI 106
--- PAGE 107 ---
4. 실험
4.1 실험개요
• TRECVid 2016 데이터 세트로부터 무작위로 10개의 영상을 사용하여 수작업으로 영상분할 정답 세트를 제작한 뒤
제안된 모델과 타 영상분할 모델 3개의 Precsion, Recall, F-score 점수를 통하여 성능을 비교함
4.2 실험결과
• 해당 모델은 Precision과 Recall에서는 각각 Color Histogram 모델과 Transition Detection 모델에 비하여 성능이
떨어지나 종합적으로 성능간 균형이 제일 균일하며 가장 높은 F-score 점수를 보여줌
원천기술 I 03. 정보 검색/분류/추출/요약 107
--- PAGE 108 ---
07 비지도 학습 알고리즘을 이용한 보고서 자동 분석 및 토픽 자동 추출 기술
1. 기술 설명
• 토픽 모델(Topic model)이란 문서 집합의 추상적인 “주제”를 발견하기 위한 통계적 모델 중 하나로, 텍스트 본문의
숨겨진 의미구조를 발견하기 위해 사용되는 텍스트 마이닝 기법임
• 본 기술은 해당 보고서가 어떤 토픽에 적합한지 파악하기 위해 토픽 모델링 기법 가운데 하나인 잠재
디리클레할당(Latent Dirichlet Allocation, LDA)를 이용함. LDA는 주어진 문서에 대하여 각 문서에 어떤 주제들이
존재하는지에 대한 확률모형이며, 토픽별 단어의 분포, 문서별 토픽의 분포를 모두 추정함
2. 기술 방법
• 본 기술은 보고서 PDF 파일을 넣으면 분석이 쉽도록 txt로 전환하고, Bag-of-words를 이용하여 전체 보고서에서
중요한 단어 최소 5000개를 사전으로 생성함
• 만들어진 사전을 바탕으로 새로운 문서가 들어왔을 때 토픽 모델 알고리즘인 LDA를 활용하여 문서별 토픽 분포
확률을 계산함
3. 기술 활용 및 응용 분야
• 본 기술은 방대한 자료에서 자동으로 비정형 텍스트 집합을 이해하기 쉽도록 정리할 수 있으므로 텍스트마이닝
분야 외에도 유전자 정보, 이미지, 네트워크와 같은 자료에서 유의미한 구조를 발견하는데에도 유용하게 사용될 수
있음
• 데 모 : http://nlplab.iptime.org:32270/
Human-Inspired AI 108
--- PAGE 109 ---
4. 실험
4.1 실험 개요
• 비정형 보고서 문서에서 주제를 찾기 위해 토픽 모델링인 LDA를 활용하여 실험을 진행하였음. 실험을 진행한 결과는
다음과 같음
4.2 실험 결과
• 본 기술의 결과는 보고서를 입력하였을 때 보고서와 관련된 주제가 어디에 들어가며 다른 주제보다
• 얼마나 가까운지 확률인지 확인할 수 있음
보고서에 대한
각 토픽별 확률
원천기술 I 03. 정보 검색/분류/추출/요약 109
--- PAGE 110 ---
08 순차 정보를 이용한 콘텐츠 추천 시스템 개발
1. 기술 설명
• 추천 시스템은 사용자가 소비할 만한 콘텐츠 또는 아이템을 예측하여 사용자에게 콘텐츠를 제시해주는 시스템을
말함
• 해당 기술은 사용자의 소비 순서 정보를 통하여 순차 패턴을 모델링하고, 사용자들의 유사도를 통해 그룹 선호도
모델을 모델링함으로써 사용자들에게 순차적인 콘텐츠 또는 아이템을 추천해주는 기술임
• 기존 연구와의 차이점은 그룹 선호도를 유사도 모델로 정의하고, 사용자의 선호도와 순차 패턴, 그룹 선호도를
하나의 단일 모델로 통합하여 모델의 차원을 축소하여 기존 연구들의 추천 성능보다 더 향상된 추천 모델을
제안하였음
2. 기술 방법
• 사용자와 사용자가 소비한 정보가 주어졌을 때, 사용자가 소비한 콘텐츠 또는 아이템의 순서 정보와 그 유사도를
통하여 사용자들의 그룹을 추출하고, 그룹들의 대표 아이템 셋을 정의하여 그룹의 선호도 모델을 하나의 행렬로
모델링함
• 사용자가 소비한 정보를 통하여 특정 사용자의 선호도 모델과 순차 패턴을 각각을 행렬로 모델링함
• 사용자 선호도, 순차패턴, 그룹 선호도를 통합하여 하나의 행렬로 모델링하고, 해당 모델을 기계학습 방법론으로
학습하여 사용자에게 순차적인 소비가 가능하도록 아이템 또는 콘텐츠를 예측하여 제시함
3. 기술 활용 응용 분야
• 해당 기술은 사용자들에게 영화를 추천해주는 시스템, e-커머스 환경에서의 상품 추천, 사용자 선호에 맞는 음악
추천 등 다양한 도메인에 적용하는 것이 가능하다.
• 인공지능 서비스의 대다수의 마지막 단계는 추천으로 인공지능 서비스와 연계하여 활용하는 것이 가능하다.
• 데 모 : http://nlplab.iptime.org:32280/rec_demo/
Human-Inspired AI 110
--- PAGE 111 ---
4. 실험
4.1 실험 개요
• 아마존 데이터 및 Epinion, Foursqure 데이터를 통하여 기존의 모델들과의 비교실험을 진행한다.
• 3가지 평가 방법을 사용하여 성능을 측정하였으며, 가장 좋은 추천 성능을 보인다.
4.2 실험 결과
• 해당 추천 모델인 GPS가 다른 모델에 비해 높은 성능을 보임을 알 수 있다.
Results (SPS).
BPR-MF FISM FPMC Fossil improvement
Datasets method GPS (e)
(a) (b) (c) (d) d vs a e vs b e vs d e vs best
A-Auto sps@30 0.0384 0.0882 0.0275 0.0863 0.1012 0.048 0.013 0.015 0.013
A-Video sps@30 0.0327 0.1072 0.0399 0.0875 0.1493 0.055 0.042 0.062 0.042
A-Elec sps@30 0.0411 0.0421 0.0309 0.0428 0.0511 0.002 0.009 0.008 0.008
A-Office sps@30 0.0386 0.1003 0.063 0.139 0.1461 0.1 0.046 0.007 0.007
Epinions sps@30 0.1184 0.1147 0.0789 0.1184 0.1974 0 0.083 0.079 0.079
Foursquare sps@30 2555 0.2622 0.2516 0.3162 0.3262 0.061 0.064 0.01 0.01
avg.(k=100) sps@30 0.0919 0.1185 0.0815 0.1298 0.1669 0.038 0.048 0.037 0.034
Results (Recall)
BPR-MF FISM FPMC Fossil improvement
Datasets method GPS (e)
(a) (b) (c) (d) d vs a e vs b e vs d e vs best
A-Auto recall@30 0.0386 0.0834 0.0263 0.0821 0.0954 0.044 0.012 0.013 0.012
A-Video recall@30 0.0334 0.1009 0.0387 0.0831 0.1456 0.05 0.045 0.063 0.045
A-Elec recall@30 0.0436 0.0437 0.0309 0.0442 0.0509 0.001 0.007 0.007 0.007
A-Office recall@30 0.038 0.0756 0.0436 0.075 0.083 0.037 0.007 0.008 0.007
Epinions recall@30 0.0727 0.0902 0.037 0.0848 0.139 0.012 0.049 0.054 0.049
Foursquare recall@30 0.2382 0.221 0.2314 0.2517 0.2634 0.014 0.042 0.012 0.012
avg.(k=100) recall@30 0.0767 0.1007 0.0636 0.101 0.1309 0.024 0.03 0.03 0.026
Results (RecallResults (NDCG).
BPR-MF FISM FPMC Fossil improvement
Datasets method GPS (e)
(a) (b) (c) (d) d vs a e vs b e vs d e vs best
A-Auto ndcg @30 0.0169 0.0479 0.0136 0.0397 0.0504 0.0228 0.0025 0.0107 0.0025
A-Video ndcg @30 0.0292 0.083 0.0321 0.0679 0.0888 0.0387 0.0058 0.0209 0.0058
A-Elec ndcg @30 0.0262 0.0265 0.0189 0.0268 0.0402 0.0006 0.0137 0.0134 0.0134
A-Office ndcg @30 0.0202 0.0498 0.0237 0.0456 0.0549 0.0254 0.0051 0.0093 0.0051
Epinions ndcg @30 0.0727 0.0902 0.037 0.0848 0.139 0.0121 0.0488 0.0542 0.0488
Foursquare ndcg @30 0.1367 0.1399 0.1294 0.1589 0.1973 0.0222 0.0574 0.0384 0.0384
avg.(k=100) ndcg @30 0.0503 0.0729 0.0425 0.0706 0.0951 0.0203 0.0222 0.0245 0.019
원천기술 I 03. 정보 검색/분류/추출/요약 111
--- PAGE 112 ---
09 스케치를 이용한 패션 의류 검색 시스템
1. 기술 설명
• 본 기술은 사용자가 원하는 상품의 스케치를 그리면, 이를 바탕으로 유사한 시각적 특성을 가진 상품을 검색하는
방법이다.
[ 그림 ]
벡터 공간 기반
이미지/드로잉 검색
모델의 구조도
2. 기술 방법
• 스케치 기반 상품 검색 시스템은 사용자가 원하는 상품의 스케치를 그리면 딥러닝 기술을 이용하여 이를 이미지
수준으로 업샘플링하고, 업샘플링된 이미지로부터 얻은 자질 벡터로 벡터 공간 기반 유사 이미지 검색을 수행하는
방법을 사용한다.
• 사진 기반 상품 검색을 위해 이미지 자질 벡터를 추출할 수 있는 CNN(convolutional neural network) 모델을
훈련시켜야 한다. 이를 위해 패션 상품의 카테고리를 분류할 수 있는 이미지 분류기를 훈련시켜 활용한다.
• 스케치 기반 상품 검색을 위한 스캐치 업샘플링은 GAN(Generative Adversarial Network)을 이용한다. GAN은 상호
대립되는 두 신경망을 교차로 훈련시키는 생성 모델로, 이미지 생성 분야에서 기존의 방법보다 선명한 결과물을
얻을 수 있어 최근 각광받고 있다.
Human-Inspired AI 112
--- PAGE 113 ---
3. 기술 활용 및 응용 분야
• 정보 검색, 유사 상품 검색, 스케치를 이용한 모조 상품 검색
• 데모 : http://nlplab.iptime.org:32280/fashion_demo/
4. 실험
[ 그림 ]
스케치 업샘플링 모듈을
이용해 업샘플링된
결과물의 예
원천기술 I 03. 정보 검색/분류/추출/요약 113
--- PAGE 114 ---
10 Eye tracking 기반의 휴먼 리딩을 반영한 추출 요약 기법
1. 기술 설명
• 추출요약이란 문서내에 주요한 요약정보가 되는 문장 또는 단어를 추출하여 요약을 생성하는 기법을 의미한다.
본 기술은 휴먼 리딩(Human reading)을 위한 인지처리과정을 위해 아이트래킹(Eye tracking) 데이터 기반의 추출
요약(Extractive summarization) 기술로서 기존의 귀납적 편향을 해소하기 위하여 아이트래킹 데이터 기반의
새로운 추출 요약 모델이다.
2. 기술 방법
• 본 기술은 사전학습 언어 모델인 BERT를 기반으로 문장과 단어 정보를 모두 반영하는 구조이다. 또한 본 모델은
텍스트 요약을 수행할 때 사람의 인지처리 과정을 모방하여, 아이트래킹 데이터를 기반으로 사람의 사전지식을
귀납적 편향으로 사용하여 기존의 문제점을 해소하였다.
• 본 모델은 요약 문서의 문장 데이터와 아이트래커를 통하여 실험한 문장 데이터로 서로 다른 독립적인 태스크를
수행하기 때문에 다중 도메인 학습(Multi domain learning)으로 정의할 수 있으며, 아래와 같은 구조를 가진다.
- 다중 단어 및 문장 인코딩 : 문장과 단어에 대한 인코딩 정보를 동시에 사용하여 각 문장에 대한 문맥
임베딩(Contextual embedding)을 반영하고, 단어에 대한 아이트래킹 정보를 활용한다.
- Segment embeddings : 문서내에 있는 다중 문장들을 구분한다.
- Fine-tuning with multi-domain unified layer: 서로 다른 두 개의 태스크(task)를 수행할 수 있도록 통합된
다중 도메인 레이어로 구성되며, 첫 번째 요약(Summarization)파트에서는 추출 요약을 수행하며, 두 번째 시선
예측(Gaze)파트에서는 토큰에 대한 first pass prediction을 수행한다.
Human-Inspired AI 114
--- PAGE 115 ---
Sentence BERT 임베딩을 이용한 과편향 뉴스 판별 11
1. 기술 설명
• 과편향 뉴스는 주어진 기사 내용이 비논리적이거나 특정한 사람이나 정당에 편향되어 있는 뉴스를 의미한다.
본 기술은 과편향 뉴스 판별(hyperpartisan news detection)모델로서 뉴스 기사가 특정 인물 또는 정당에
편향되었는지 판단하는 모델이다. 기존 연구들은 feature-based ELMo, CNN 모델이 사용되었으나 이는 문서
임베딩이 아닌 단어 임베딩의 평균을 사용하는 한계가 있다. 따라서 feature-based 접근법을 따르며 Sentence-
BERT(SentBERT)의 문서 임베딩을 이용한 feature-based SentBERT기반의 과편향 뉴스 판별 모델을 개발하였으며,
본 모델은 기존 state-of-the-art 모델보다 f1-score 기준 1.3% 높은 성능을 보였다.
2. 기술 방법
• 기존의 BERT 임베딩 대신 pre-trained BERT로부터 의미적으로 유의한 문장 임베딩을 추출할 수 있도록 수정된
모델인 SentBERT모델을 사용한다. SentBERT모델을 통하여 추출된 문장 임베딩은 코사인 유사도를 통해 비교가
가능하며, 고정된 사이즈의 문장 임베딩을 얻기 위해 다음과 같이 학습된다.
• BERT output 벡터의 평균값을 구한 뒤, 생성된 문장 임베딩의 의미적 유의성을 코사인 유사도로 계산한다. 그
후 siamese network 혹은 triplet network가 임베딩의 weight를 업데이트 시킨다. 이에 따라 산출된 임베딩은
기존의 BERT임베딩과 다르게 의미적으로 유사한 문장들은 벡터 스페이스 안에서 그 거리가 가까워져 기존의
BERT임베딩보다 의미적 정보를 잘 담을 수 있다.
원천기술 I 03. 정보 검색/분류/추출/요약 115
--- PAGE 116 ---
12 종교활동을 위한 휴머노이드 질의응답 로봇
1. 기술 설명
• 본 기술은 시각 장애인, 노인 등 텍스트에 접근하기 어려운 사람들에게 로봇의 음성으로 도움을 제공하기 위하여
개발되었으며, 한국어/영어가 지원된다.
• 종교 개인 비서 로봇의 역할
- 여러 가지 이유로 경전을 일을 수 없는 사람들에게 음성으로 내용 제공 가능
- 복음, 장, 절 단위에 구애받지 않고, 듣고 싶은 부분 검색 가능
- 집에서 종교음악을 듣고 싶어도 여러 이유에 의해 할 수 없는 사람들에게 도움
- 비슷한 구절을 기반으로 추천하여 관련된 노래와 또 다른 구절 검색 가능
- 전문 종교인이 아닌 일반 신자들에게 편리한 접근성 제공
- 이를 통하여 종교인들의 심리적 웰빙과 긍정적 정서 함양에 도움
2. 기술 방법
• 성경검색 모델
- 사용자가 듣고 싶은 성경의 범위를 로봇에게 질의, 로봇이 해당 범위를 낭독함
- Rule-based로 구현하였으며, 질의로 들어온 성경의 범위 인덱스를 추출하여 성경을 낭독함
- 검색예시
• 요한복음 • 시편 1장 2절 들려줘
• 마태복음 1장 • 창세기 1장 1절부터 2장까지 들려줘
• 출애굽기 들려줘 • 잠언 2장 1절부터 3절까지 들려줘
• 마태복음이랑 마가복음 들려줘 • 누가복음 1장 1절부터 1장 15절까지 들려줘
Human-Inspired AI 116
--- PAGE 117 ---
• CCM 추천 모델
- 사용자가 특정한 구절을 로봇에게 질의하면, 해당 구절과 비슷한 내용의 CCM을 검색 및 추천
- Gensim의 Doc2Vec모델을 이용하여 하나의 CCM 가사를 하나의 문서로 분류하고, 분류된 문서를 300차원
벡터로 변환함
- 로봇이 입력값으로 하나의 구절을 받으면 문서 간 유사도 계산을 통하여 입력과 가장 유사한 곡으로 추천 및
재생함
원천기술 I 03. 정보 검색/분류/추출/요약 117
--- PAGE 118 ---
• 비슷한 구절 검색 모델
- 사용자가 특정한 구절을 로봇에게 질의하면 해당 구절과 비슷한 내용의 또 다른 구절을 검색 및 추천함
- 알고리즘은 CCM 추천 모델과 동일함
3. 기술활용 및 응용분야
• 교회 예배 후 포럼 활동 / 개인 예배 활동 보조 가능
• 종교에도 적용이 가능
Human-Inspired AI 118
--- PAGE 119 ---
아이들 교육을 위한 나오 로봇 13
1. 기술 설명
• 본 기술은 외국어 학습을 목적으로 개발하였으며, 시나리오 기반 Free Talking, 영어문법 교정 피드백, 사용자들의
흥미 유발을 위한 언어지능, 외국어 지능, 수리지능 게임을 개발하였다.
2. 기술 방법
• 시나리오 기반 Free Talking
- 초등학교 저학년 대상 교육용 로봇으로 초등학교 교과서 기반으로 20개의 시나리오를 생성함
- 딥러닝 기반 영어 문법 교정기를 개발 및 적용하여 사용자와 로봇이 대화를 나눈 뒤, 로봇이 사용자의 영어 문법을
교정하여 알려줌
• Intelligent games
- 한국어 기초 사전을 기반으로 자체 한영사전을 제작하였으며, 자체 한영사전을 바탕으로 영어사전과 학습용
미니게임을 개발함
- 한영사전은 파이썬 딕셔너리 형태로 제작되었으며, 학습 대상의 수준을 고려하여 초급, 중급 어휘로 구성함. 또한
원활한 음성인식을 위하여 동음이의어를 다의어로 취급함
- 예) {‘먹다’: ‘eat’, ‘be deaf’, ‘가격’:‘hitting’,‘price’}
원천기술 I 03. 정보 검색/분류/추출/요약 119
--- PAGE 120 ---
• 수리지능을 위한 구구단 게임
- 영어로 구구단 게임을 진행할 수 있으며, 영어와 수학을 동시에 학습하는 효과를 가짐
- 게임 옵션 : 중도취소, 다시 듣기, 게임횟수 설정, 점수 산정
• 언어지능을 위한 끝말잇기 게임
- 한국어 단어로 끝말잇기 게임을 할 수 있으며, 한국어 학습에 도움을 줌
- 게임 옵션 : 중도취소, 다시 듣기, 게임횟수 설정
• 외국어 지능을 위한 영어 단어 게임
- 로봇이 한국어 단어와 영어 보기를 제시하면, 사용자가 보기 중 알맞은 영어 단어를 맞추는 게임으로 영어 단어
학습에 도움을 줌
• 게임 옵션: 중도취소, 다시 듣기, 게임횟수, 객관식 항목 수 설정
• Interactive Machine Reading Comprehension
- 기계독해(MRC, Machine Reading Comprehension)란 인공지능 알고리즘이 스스로 문제를 분석하고 질문에
최적화된 답안을 찾아내는 기술이다.
- 본 기술은 사용자-로봇의 대화를 통하여 기계독해가 가능하도록 개발하였으며, 10초동안 사용자가 로봇에게
이야기를 들려주고, 로봇에게 이야기와 관련된 질문을 하면 로봇이 사용자의 이야기에서 정답을 추론하여 답을
한다.
Human-Inspired AI 120
--- PAGE 121 ---
3. 기술활용
• 딥러닝 기반 영어 문법 교정기
• 데모 : http://nlplab.iptime.org:32292/
원천기술 I 03. 정보 검색/분류/추출/요약 121
--- PAGE 122 ---
14 GPT2를 활용한 유사 뉴스 기사 추천 시스템
1. 기술 설명
• GPT2 언어 모델을 활용해서 두 개의 유사한 문서 사이의 유사도를 측정하는 방법
• 뉴스 기사 문서 추출을 위해서 Newspaper3K를 활용하여 기사 제목 및 본문에 대해서 크롤링을 진행했으며,
BM25를 통해 핵심 문장을 추출하여 활용.
• 문장 단위 유사도 비교에 그쳤던 기존 연구의 한계를 극복하기 위해 커리큘럼 학습과 준 지도 학습을 통해서 문단과
문서 단위에서도 문맥적 정보를 반영한 유사도 비교가 가능.
[그림]
KoGPT2Post: 모델
학습 및 기사 추천
과정
Human-Inspired AI 122
--- PAGE 123 ---
2. 기술 방법
• 본 기술은 문서 단위의 유사도 비교를 통해서 실제 뉴스 기사와 데이터베이스 상에 저장되어있는 뉴스 기사 사이의
관계를 추론하여 사용자에게 유사한 기사를 추천하는 기술
• 기술의 흐름은 문장 단위의 유사도 비교 학습을 통한 KoGPT2 언어 모델의 미세 조정 훈련과 이후 BM25를 통한
문서 핵심 추출 문단과 문서에 대해서 유사도를 점진적으로 파악할 수 있도록 함.
3. 기술 활용 및 응용 분야
• 본 기술은 온라인 뉴스 기사를 제공하는 플랫폼에서 사용자가 현재 열람하고 있는 뉴스 기사와 유사한 뉴스 기사를
자동으로 피드에 노출하여 추천하는데 사용할 수 있음.
• 데 모 : http://nlplab.iptime.org:32272/
4. 실험
4.1 실험 개요
• 문장 유사도 측정을 위한 KorSTS 데이터셋을 활용하여, 스피어만 상관 계수를 통해 문장 간의 의미적 유사도를
예측하도록 훈련을 진행. 문장 이상의 데이터에 경우에도 동일한 방법을 사용했으며, 커리큘럼 학습 규칙에 따라서
점차 문장의 길이가 길어지도록 설정. 실제 뉴스 데이터를 학습 데이터로 활용하기 위해서 준 지도 학습을 통해 이전
단계의 평가 데이터를 다음 단계의 훈련 데이터로 사용.
4.2 실험 결과
• 구체적인 표, 그림 설명
모 델 TF-IDF + Cosine Doc2Vec + Cosine KoGPT2 (SKT-AI) KoGPT2Post (Ours)
2단계 r 58.01 30.68 87.81 89.43
s
3단계 r 57.75 29.01 92.55 94.14
s
현재 기사 추천 기사 유사도 점수
빅뱅 탑, 코로나19 극복 위해 1억원 기부, 그룹 빅뱅의 탑이 신종 코로나바이러스 Rank 1. 신민아 기부, 의료진 및 취약 계층 위해 1억원 쾌 척 ...
감염증 피해 극복을 위해 ... 과거에 탑은 2018년 11월 4일 ... 용산 복지 재단에 25일 신민아의 소속사 에임엔터테인먼트 측은 신 민아가 코로나19 확산 방지에
3.8666
이웃돕기성금 1104만원을 기부한 바 있다. 노력하는 ... 사랑의 열매측에 1억원을 기부했다고 밝혔다.
빅뱅 탑, 코로나19 극복 위해 1억원 기부, 그룹 빅뱅의 탑이 신종 코로나바이러스 Rank 2. 북구, 명의천사 기부챌린지 성황리에 마무 리 ... 북구는 지난해 11월부터
감염증 피해 극복을 위해 ... 과거에 탑은 2018년 11월 4일 ... 용산 복지 재단에 올해 지난달 말까지 기 부문화 확산을 유도하기 위해 ... 이웃을 돕는데 소중하게
3.6776
이웃돕기성금 1104만원을 기부한 바 있다. 사용될 예정이다.
카카오톡 오류에 사용자들 발동동 ... 서버 불안정, 원인 파악 중 ... 카카오톡이 Rank 1. 카카오스토리도 오류? ... 오후 2시까지 시스 템 점검 카카오스토리
2일 오전 장애를 일으 키며 많은 사용자가 불편을 ... 서비스 이용에 불편을 드려 측은 이날 홈페이지를 통해 보 다 안정적인 서비스 이용을 위해 ... 앞서 이날 오전
3.7187
죄송하다고 안내했다. 9시께 카카오톡에 오류가 발생했다.
카카오톡 오류에 사용자들 발동동 ... 서버 불안정, 원인 파악 중 ... 카카오톡이 Rank 2. 재택 근무 많은데 ... 카카오톡 먹통, 출시 10 주년을 맞이하는 ...
2일 오전 장애를 일으 키며 많은 사용자가 불편을 ... 서비스 이용에 불편을 드려 이용자들이 큰 혼란과 불편을 겪 었다 ... 카카오 측은 반복되지 않도록 최선을
3.4060
죄송하다고 안내했다. 노력을다하겠다고 밝혔다.
• 본 기술의 결과는 데모에서 확인 가능하며, 단어 빈도수를 통한 유사도 비교보다 문장의 길이가 점차 길어질수록
더 우수한 성능을 보임. 특히 오른쪽 표의 정성적 평가 결과를 통해, 단순히 어휘에 해당하는 반복뿐만 아니라,
문맥적인 정보를 고려해서 유사도 점수를 예측하는 것을 확인할 수 있음.
원천기술 I 03. 정보 검색/분류/추출/요약 123
--- PAGE 124 ---
15 나오 로봇을 활용한 이중 언어 교육
1. 기술 설명
• 최신 딥러닝 기반 기계번역 및 대화시스템을 이용하여 로봇을 이용한 스마트 자동통역 시스템뿐만 아니라 이를
확장하여 교육용 대화시스템을 개발하고자 함. 본 연구의 차별점으로 기존의 단일 언어 교육용 대화 시스템을
응용하여 이중 언어 대화 시스템을 개발하고자 함. 즉 외국어 학습을 위한 자동통역 소프트웨어를 NAO에
적재하고자 함. 사용자의 외국어 학습 동기 유발 및 학습 성취도 향상을 기대하며 인공지능과 이중언어를 사용하여
직접 대화하는 신개념 외국어 학습법을 제안함.
2. 기술 방법
• 자연어처리 분야 중 기계번역, 음성인식, 대화시스템 기술을 융합할 것이며 더 나아가 서비스적 관점, 사용자 관점을
반영하여 사용자 친화적 및 실질적으로 사용자에게 도움이 될 수 있는 외국어 학습을 위한 로봇을 이용한 스마트
자동통역 시스템 및 교육용 대화 시스템 (이중언어 대화 시스템)을 제작함
• 기술적인 부분만 개발하지 않고 직접 대화 시나리오를 제작하여 인문학적인 요소와 기술적인 요소를 융합할
것입니다. 기술적인 요소로는 최신 딥러닝 기술 기반 기계번역, 음성인식, 음성 합성, 대화시스템 기술을 이용할
것이며 이를 각각 분리해서 서비스하는 것이 하나의 서비스로 융합하여 개발을 함
1. 음성인식기술: NAO에서 제공하는 기본 API를 이용
2. 음성합성기술: NAO에서 제공하는 기본 API를 이용
3. 기계번역기술: 한-영, 영-한 기계번역을 직접 데이터 수집부터 번역 서버 및 웹서버까지 제작하여 REST API 형태로
제작. NAO는 REST API를 직접 이용하는 형태
4. 시나리오 제작: 최대의 학습 효과를 위하여 초등학교 교과서 기반 영어 Free Talking 시나리오를 제작
전체 프로세스
a. 대화를 시작할 준비를 한다
b. 사용자는 한국어로 대화를 시작한다.
c. 사용자가 발화를 하게 되면 STT(Speech To Text)기술을 이용하여 음성인식 결과가 도출된다
d. STT결과 값을 기계번역의 입력으로 넣는다. (필요시 음성인식 후처리를 위한 NLU 기술 도입)
e. 번역 된 문장을 바탕으로 TTS (Text to Speech)기술을 이용하여 NAO는 영어로 발화를 진행한다. (신개념
이중언어 교육용 대화 시스템
f. 이 를 통해 자동통역 기능이 탑재된 신개념 교육용 대화 시스템을 이용한 언어 학습이 실현된다.
3. 기술 활용 및 응용 분야
• 본 아이디어와 유사한 제품이 중국에서 상용화하여 성공한 사례가 존재합니다. (Lilly) Lilly는 Single Turn 대화
방식으로 실제 외국어 학습에 도움이 되는지 의문입니다. 그러나 저희 팀이 제작할 제품은 Multi Turn 대화를
진행한 후 Free Talking이 가능할 뿐만 아니라 이중언어로 대화가 가능합니다.
• 데모 : https://www.youtube.com/watch?v=4kCMMa62LDY&t=9s
Human-Inspired AI 124
--- PAGE 125 ---
원천기술 I 03. 정보 검색/분류/추출/요약 125
--- PAGE 126 ---
16 나오 로봇을 활용한 동화 추천 및 읽기
1. 기술 설명
• BERT 언어 모델을 활용해서 사용자의 발화 내용과 유사한 제목을 지닌 동화책 본문 사이의 유사도를 측정하는 기술.
• 데이터베이스의 경우 문화체육관광부 데이터광장의 다국어동화구연 API를 활용. 제목, 본문 그리고 동화책을
읽어줄 수 있는 녹음 파일을 담고 있음.
• 사 용자와의 연속적인 발화를 통해 유사도를 기반으로 한 N개의 동화를 추천하고, 나오는 사용자에게 동화를 재생.
2. 기술 방법
• 제 목과 본문을 합친 문단 사이의 유사도는 BERT 언어 모델을 KorSTS 데이터셋을 통해 미세 조정 훈련을 진행.
• 미세 조정 단계에서 BERT는 문장 쌍에 얼마나 유사함을 지니고 있는지 양방향 인코더를 통과한 결과 값을 반환한
[CLS] 토큰이 지니고 있는 은닉 벡터 값과 사전에 제시한 점수 값 사이의 관계를 회귀 식을 통해 해석하여, 평균
제곱의 오차를 줄여나가는 방향으로 역전파를 통한 최적화를 진행
• 아이들의 발화에 대한 로봇의 음성 인식 및 합성기술은 NAO에서 제공하는 기본 API를 이용
• 음성 인식 이후 텍스트로 변환된 데이터는 질의에 대한 IR(Information Retrieval) 기술을 적용
• 해당 질의는 미세 조정을 완료한 BERT를 통해서 문서요약을 통해서 핵심 주제를 담고 있는 동화의 초록 사이의 언어
모델이 반환하는 유사도 점수를 통해 상위 N개의 동화 목록을 추천함. 추천 유사도 점수에 대한 임계값과 목록의
개수는 사용자가 직접 조정할 수 있도록 제공할 예정.
Human-Inspired AI 126
--- PAGE 127 ---
3. 기술 활용 및 응용 분야
• 추천 시스템용 대화 시스템을 응용하여 인공지능 로봇 NAO에 적재함. 해당 시스템은 아이들의 질의를 바탕으로
동화책을 추천하고, 선택한 동화에 대해서 로봇이 특정한 동작과 함께 동화책을 읽도록 제작. 이러한 방식은 독서의
첫 단추를 강압과 의무에 의한 숙제와 같은 요소가 아닌, 로봇과 함께할 수 있는 하나의 놀이처럼 인식하도록 함.
• 데모 : www.youtube.com/watch?v=rI8ep18-ISE
4. 실험
4.1 실험 개요
4.2 전반적인 기술 진행도
(1) 나오의 전원을 연결하여 대화를 준비함.
(2) 사용자는 한국어로 발화 (최초 대화에서 ‘나오야’를 통해서 호출한 이후에 대화를 이어나가기).
(3) 사용자 발화는 나오의 STT(Speech To Text) 기술을 통해 음성인식 결과를 반환함.
(4) 반환 값은 추천 동화책 검색의 입력으로 사용함(다양한 상황에 따른 실험을 통해서 필요하다면 반환 값에 대한
후처리 NLU 기술 도입. 현재는 언어 모델을 활용한 만큼, 어휘 정보뿐만 아닌 발화 문맥을 고려해서 추천).
(5) 반환된 발화 문장과 유사도 평가에 대한 미세 조정을 마친 BERT를 통해 임베딩 요약 동화책 정보 간의 유사도
점수를 통해서 상위 3개의 동화책을 추천함.
(6) 추가적인 발화를 통해서 추천 동화책 중 1권을 선택하고, 선택된 동화책 정보를 문화체육관광부의
다국어동화구현DB의 동화책 내용을 불러옴.
(7) DB에서 반환된 값은 TTS(Text to Speech) 기술을 통해서 나오는 한국어로 동화책의 내용을 재생함.
(8) 구축 데이터베이스 상의 동화책 읽기 기능이 탑재된 로봇 나오를 통해 아이들은 동화책을 통해 독서에 대한
첫걸음을 즐거운 기억으로 인식하며, 대화를 통한 독서 학습을 실현함.
• 본 기술의 결과는 영상에서 확인 가능하며, 단순히 어휘에 해당하는 반복뿐만 아니라, 발화 문맥적인 정보를
고려해서 유사도 점수를 예측하는 것을 확인할 수 있음.
원천기술 I 03. 정보 검색/분류/추출/요약 127
--- PAGE 128 ---
17 Virtual Try-On images for Fashion-AI
1. 기술 설명
• VITON-GAN과 같은 Deep Learning 기반 AI 모델은 매장 내 의류 및 모형 모델을 사용하여 시범적으로 볼 수 있는
가상 이미지를 생성할 수 있음
• 3D 정보 외에 다른 정보를 사용하지 않고 영상 기반의 Virtual Try-On Generator(VITON-GAN)을 사용하여 세밀한
부분까지 보완하여 원하는 의류 아이템을 모형 모델에 맞게 제공함
• 본 모델의 generator는 CP-VTON에서 구현된 GMM(geometry matching module)과 TOM(try-on module)로
구성되며, blockade를 해결하기 위해 TOM에 adversarial loss를 추가하였음
2. 기술 방법
• CP-VTON에는 아래 그림과 같이 크게 세 단계가 있음
• 첫 번째로 TOM은 TOM 결과 이미지, 매장 내 의류 이미지 및 사람 표현을 입력으로 사용하는 판별자에 대해
적대적으로 훈련되고 결과가 실제인지 가짜인지 판단함
• 두 번째, GMM의 loss function은 신체에 겹쳐진 옷의 생성 이미지와 실제 이미지 사이의 L1 distance를 포함함
• 마지막으로, 데이터를 확장시키기 위해 무작위로 수평 뒤집기 하여 사용됨
Human-Inspired AI 128
--- PAGE 129 ---
3. 실험
• 데이터 세트에는 16,253개의 여성 모델 이미지와 상위 의류 이미지 쌍이 포함되어 있으며, 이 쌍은 각각
training(13,221쌍), validation(1,000쌍), test(2,032쌍) set으로 구성되어 있음
• VITON-GAN은 blockade 문제를 해결하기 위해 CP-VTON보다 더 명확하게 손과 팔을 생성함
• 데모 : http://nlplab.iptime.org:32299/
원천기술 I 03. 정보 검색/분류/추출/요약 129
--- PAGE 130 ---
18 사용자 그래프 기반 한국어 가짜뉴스 판별 방법
1. 기술 설명
• 기사 본문 정보 외에 독자 반응 정보를 활용한 한국어 가짜뉴스 판별 방법 제안
• 기사와 독자를 객체로 구성하고 독자-독자 관계, 독자-기사 관계를 바탕으로 그래프 구조 형태의 데이터셋을 학습할
수 있는 가짜뉴스 판별 방법 제안
2. 기술 방법
• 본 기술은 SNU팩트체크 플랫폼에서 기사 라벨 정보와 네이버 뉴스 플랫폼에서 독자 반응 정보를 수집하여 그래프
형태의 데이터셋을 구축함
• 위에서 구축한 그래프 구조를 활용하여 학습할 수 있는 한국어 가짜뉴스 탐지 모델의 학습 방법 개발
• 개발한 모델은 뉴스 본문 언어 임베딩과 그래프 관계 정보를 동시에 활용하여서 기존 본문 기반의 가짜뉴스 탐지
모델과 비교하여 우수한 품질의 표현 벡터를 산출
Human-Inspired AI 130
--- PAGE 131 ---
3. 기술 활용 및 응용 분야
• 본 기술은 한국어 가짜뉴스 탐지에 활용될 수 있음
4. 실험
4.1 실험 개요
• 구축한 그래프 데이터셋을 이용하여 학습한 모델의 실험 결과
- 뉴스 본문만을 사용하여 학습된 기존 모델과 뉴스-독자 그래프를 사용하여 학습된 제안 모델의 비교실험을
진행하였으며, 개발한 모델은 기존 모델과 비교하여 향상된 성능을 보임
4.2 실험 결과
• 각 모델이 학습에 활용한 정보의 종류와 가짜뉴스 탐지 성능을 ROC-AUC score로 나타냈으며, 기존 모델의
가짜뉴스 탐지 성능은 랜덤 분류에 가까운 성능을 보였으나 제안 모델은 뉴스 본문을 둘러싼 주변 정보도 적절히
학습하여 기존 모델 대비 0.139p 높은 가짜뉴스 탐지 성능을 보임
모델 이름 기사 본문 독자 반응정보 AUC
KoBERT √ 0.524 베이스라인,
제안 모델 비교실험
K-FANG (제안 모델) √ √ 0.663
결과
원천기술 I 03. 정보 검색/분류/추출/요약 131
--- PAGE 132 ---
[4]
기계번역
• 고려대학교 다국어 신경망 기계번역기
• 딥러닝 기반 한국어 고전번역기
• PicTalky: Text to Pictogram
• COVID-19 도메인특화 기계번역기
• 인간의 인지과정을 반영한 도메인 특화 번역기
--- PAGE 135 ---
고려대학교 다국어 신경망 기계번역기 01
1. 기술 설명
• 모델의 변경 없이 각종 pre-processing 및 post-processing을 통해 모델의 성능을 향상시킬 수 있다는 연구의
움직임을 기반으로 low-resource 언어인 Korean-English NMT에 다양한 decoding strategies를 적용하여 모델의
변경 없이 번역 성능이 향상됨을 비교 실험을 통해 증명함
• Beam size에 따른 성능 변화 실험, n-gram blocking에 따른 성능 변화 실험, length penalty를 적용하였을 때
성능 향상 여부 등의 실험을 진행하였고, 실험결과 다양한 decoding strategies가 성능 향상에 도움이 됨을 알 수
있었으며 기존 Korean-English NMT 연구들에 비해 비교적 좋은 성능을 보임
https://www.aihub.or.kr/node/4525
원천기술 I 04. 기계번역 135
--- PAGE 136 ---
2. 기술 방법
• 본 연구에서 실험을 진행한 다양한 decoding strategies은 크게 3가지로 beam Size에 따른 성능 변화 실험,
n-gram blocking에 따른 성능 변화 실험, length penalty와 stepwise penalty에 따른 성능 변화 실험을 진행하였다.
해당 strategies들을 독립적으로 적용하는 것이 아닌 점층적인 pipelining 형태로 적용하여 가장 최적의 성능을
도출해내었다.
3. 기술 활용 및 응용 분야
• 다양한 이종 언어들과의 번역
• 번역 사업
4. 실험
4.1 실험 개요
• 본 연구는 Transformer를 기반으로 한 Korea University(KU) model을 baseline으로 하여 이를 기반으로 다양한
decoding strategies을 적용하여 비교 실험을 진행하였다.
Human-Inspired AI 136
--- PAGE 137 ---
4.2 실험 결과
• Post Processing 적용시 성능이 향상됨을 확인함
Beam size Iwslt 16 Iwslt 17 N-gram Blocking Iwslt 16 Iwslt 17
Beam 1 17.27 14.84 Uni-gram 5.14 4.98
Beam 2 17.77(+0.50) 15.19 Bi-gram 15.98 14.43
Beam 3 17.51 14.99 Tri-gram 17.62 15.09
Beam 4 17.49 14.83 4-gram 17.65 15.24
Beam 5 17.34 14.75 5-gram 17.74 15.22
Beam 6 16.97 14.49 6-gram 17.75 15.21
Beam 7 16.81 14.41 7-gram 17.72 15.20
Beam 8 16.78 14.31 8-gram 17.77 15.20
Beam 9 16.67 14.29 9-gram 17.77 15.20
Beam 10 16.46 14.23 10-gram 17.77 15.20
Beam size Total Time Average Token per /s
Beam 1 13.929 0.012 1609.359
Beam 2 14.667 0.012 1477.046
Beam 3 15.711 0.013 1353.141
Beam 4 16.241 0.014 1292.145
Beam 5 17.683 0.015 1175.981
Beam 6 18.565 0.016 1101.098
Beam 7 19.679 0.017 1026.473
Beam 8 20.949 0.018 960.227
Beam 9 22.693 0.019 881.692
Beam 10 23.907 0.020 828.938
Penalty Iwslt 16 Iwslt 17
Average Length Penalty 17.94 15.42(+0.08)
Step Wise Lenght Penalty 17.79 14.95
(Average+Step Wise)Length Penalty 17.98(+0.71) 15.22
5. 데모
• 데모 : http://nlplab.iptime.org:32296
원천기술 I 04. 기계번역 137
--- PAGE 138 ---
02 딥러닝 기반 한국어 고전번역기
1. 기술 설명
• 고전번역: 조선왕조실록, 승전원일기와 같은 고어를 번역하는 것을 의미함
• 기계번역: 소스문장(Source Sentence)을 타겟문장(Target Sentence)으로 컴퓨터가 번역하는 시스템을 의미하며
이를 고전번역에 적용할 경우 소스문장에 고어 타겟 문장에 한국어가 적용될 수 있음
기존 방식의 고전번역의 한계
• 사람이 아무것도 안하고 고전번역만 하는데 80년이 걸림
• 고전번역 전문가 양성의 어려움이 있고 제한된 인력 구조
• 현재 고전번역 전문가는 200여명 수준이며 고전번역자 양성 기간은 관련학과 졸업자기준으로 10년이상
소요됨(한국 고전번역의 현황과 과제, 2015년 국정감사 정책 자료집)
• 고전번역을 위한 관련 지식 및 실력에서 개인별 편차가 있음. 이에 따라 번역결과물의 품질편차가 발생하게 됨.
인공지능 기술의 발전
• 딥러닝의 등장으로 기존 RBMT,SMT보다 좋은 성능의 기계번역기를 개발할 수 있음
• 기계번역 기술을 활용하여 고전문자를 복구하려는 시도가 최근에 여러 논문에서 연구됨. (일본의 Kuronet, 그리스
고어, Decipher)
NMT기반 고전번역의 장점은?
• 기존 고전번역사들의 업무 효율성 강화
• 빠른 시간에 번역 가능
• 플랫폼을 통한 번역결과물의 DB화 및 지식증강형 Infinite Training모델 구축
• 품질 편차를 최소화하고 일관된 번역 품질을 만들어 낼 수 있음.
• 미번역된 문서에 대한 번역도 가능하다. (규장각 도서 등)
2. 기술 방법
• 본 연구는 고전번역에 특화된 서브워드 분리기법을 적용하면 모델의 성능을 획기적으로 올릴 수 있다고 판단하여
동일한 모델의 다양한 Subword Tokenization 방법을 적용하여 실험을 진행하였다. 고전번역에서 중요한 요소 중
하나로 Entity를 얼마나 잘 번역하는 것이냐이다. 고전번역의 데이터를 보면 사람의 이름, 장소, 기관 등이 문장의
대부분을 차지한다. 그 당시에는 기록을 남기는 것이 중요한 문제였기에 Entity의 대한 정보가 상당히 중요하다.
이러한 고전번역에 특징의 기반하여 본 논문에서 Entity 정보를 서브워드 분리 작업에서 Restrict를 진행하였다. 즉
Entity Based Vocabulary Restriction 방법론을 제안한다.
• 즉 만약 “이순신”이라는 인명 정보가 나오게 된다면 해당 정보는 Subword Tokenization은 진행하지 않고 그대로
유지하게 된다. 즉 Entity정보를 분리하지 않고 학습 데이터의 이용하는 방법론이다.
Human-Inspired AI 138
--- PAGE 139 ---
3. 기술 활용 및 응용 분야
• 고전 문서의 현대적 풀이
• 과거의 일상적인 삶의 모습과 당대 생활 자체 복원
4. 실험
4.1 실험 개요
• 인공신경망 기계번역 기술을 고전 문헌 번역에 활용한 ‘AI 기반 고전문헌 자동번역시스템’을 구축했다. 대표적인
Sequence to Sequence 모델인 LSTM-Attention 그리고 Transformer기반의 모델을 이용하여 고전번역기의
성능과 Subword Tokenization을 어떻게 하느냐에 따라 성능이 어떻게 달라지는지 확인해본다.
4.2 실험 결과
• 서브워드 분리를 어떻게 하느냐에 따라 다양한 실험을 진행하였다. Char단위, B.P.E, Sentencepiece Unigram
방법과 제안하는 Entity and Vocab Restrict 방법을 통해 서브워드 분리를 진행한 후 실험결과를 비교하였다.
추가적으로 Vocab은 그대로 놔두고 Entity만을 분리하였을 때 성능이 어떻게 변화하는지도 살펴보았다.
원천기술 I 04. 기계번역 139
--- PAGE 140 ---
Model BLEU Token Per Second
Scntcnccpicce-LSTM-Attcntion 24.39 2758
Sentcnccpiccc-Transformcr 22.69 982
BPE-LSTM-/\ttcntion 25.18 2029
BPE-Transformcr 24.43 1122
Char- LSTM-/\ttention 23.66 8785
Char- Transformer 16.24 1466
Entity Restrict- LSTM-Attention 14.74 3013
Entity Restrict- Transformer 15.12 1174
(Our) Share Vocab and Entity Restrict BPE - LSTM Attention 29.40 5004
(Our) Share Vocab and Entity Restrict BPE - Transformcr 29.68 1379
5. 데모
• 데모 : http://nlplab.iptime.org:32257/
Human-Inspired AI 140
--- PAGE 141 ---
PicTalky : Text to Pictogram 03
1. 기술 설명
• 언어발달 장애를 가진 아동들은 일상생활 및 사회생활에서 많은 어려움을 겪으며 이는 생애 전반을 걸쳐 지속됨
• Augmentative and Alternative Communication(AAC, 보완대체 의사소통)는 언어장애를 앓는 이들에게 실질적인
의사소통 수단으로 사용될 수 있음
• 본 연구는 픽토그램을 AAC의 수단으로써 최대한 활용하여 언어발달 장애 아동이 타인과 의사소통하고 언어 이해
능력을 향상시킬 수 있도록 돕는 딥러닝 기반 인공지능 서비스임
2. 기술 방법
• 픽토그램은 대표적인 보완 대체 의사소통 수단으로 언어의 어려움이 있는 사람들에게 도움이 된다. 픽토그램과 같은
전달 매체는 규칙 및 기호체계를 이해해야만 하는 언어와 다르게 보다 직관적으로 빠르게 의미를 전달할 수 있으며
이로 인하여 픽토그램은 의사소통 장애를 치료하고 개선하는 데에 보조적으로 사용될 뿐만 아니라 정보 전달
수단으로도 널리 사용된다.
• 픽토그램은 그림 교환 의사소통 체계(PECS, Picture Exchange Communication System)에도 적극 활용되며
이를 언어 재활 분야에도 응용하고 있다. 의사소통판(Communication Board)에 그려진 그림을 이용하여 타인과
의사소통하는 법을 픽토그램을 통해 터득할 수 있는 것이 대표적인 사례이다. 또한 그림을 통해 문장을 만들고 대상
식별과제를 수행하는 등 아동의 언어능력과 인지능력을 동시에 향상시킬 수 있다. 이와 같은 방법은 언어 체계를
배우지 못한 아동들의 언어 이해력 증진과 구어 발화에 실질적인 도움을 준다.
3. 기술 활용 및 응용 분야
• 지적장애와 자폐성 장애에 기인한 발달장애인 중 0세~14세에 해당하는 발달 장애 아동들의 의사 소통 수단으로
사용가능
4. 실험
4.1 실험 개요
• 본 연구에서 제안하는 서비스는 발달 장애 아동의 의사소통을 돕고 언어 이해를 증진시키는 데에 목적이 있다. 발화
내용을 청각 및 시각적으로 동시에 인코딩하여 전달하므로 사용자가 언어를 잘 알지 못하더라도 화자의 의도를
직관적으로 이해할 수 있다. 또한 텍스트와 이미지가 함께 전달되기에 언어의 요소들을 직접적으로 가르쳐주지
않아도 스스로 추론하여 언어를 배울 수 있는 암묵적 학습 또한 가능하다. 따라서 제안하는 서비스는 발달 장애
아동을 대상으로 제작되지만, 전반적인 언어에 관하여 재활 치료, 특수교육 정보 전달의 목적으로도 두루 적용될
수도 있다.
원천기술 I 04. 기계번역 141
--- PAGE 142 ---
[ 그림 ]
The architecture
of proposed Deep
learning-based AAC
service
4.2 실험 결과
• 입력으로 I love danceing이라는 오류문장이 들어가면 딥러닝 기반 영문법 교정기를 통해 I love dancing이라는
문장으로 교정을 진행한다. 교정을 진행한 문장을 Text to Pictogram 모듈을 통해 텍스트를 픽토그램으로
변경해주게 된다.
5. 데모
• 데모 : http://nlplab.iptime.org:32257/
Human-Inspired AI 142
--- PAGE 143 ---
COVID-19 도메인특화 기계번역기 04
1. 기술 설명
• 최근 세계보건기구(WHO)의 Coronavirus Disease-19(COVID-19)에 대한 팬데믹 선언으로 COVID-19는 세계적인
관심사이며 많은 사망자가 속출하고 있다. 이를 극복하기 위하여 국가 간 정보 교환과 COVID-19 관련 대응 방안
등의 공유에 대한 필요성이 증대되고 있다.
• 이러한 요구에 맞춰 우리 연구소에서는 COVID-19 도메인에 특화된 인공신경망 기반 기계번역(Neural Machine
Translation(NMT)) 모델을 개발하였다.
• 이 모델은 영어를 중심으로 프랑스어, 스페인어, 독일어, 이탈리아어, 러시아어, 중국어 지원이 가능한 Transformer
기반 양방향 모델이다.
• 실험결과 BLEU 점수를 기준으로 상용화 시스템과 비교하여 모든 언어 쌍에서 유의미한 높은 성능을 보였다.
2. 기술 방법
• COVID-19 도메인에 특화된 번역기를 위한 특화 방법은 다음과 같은 단계로 이루어진다:
1) COVID-19 관련 데이터 수집
2) 해 당 도메인에 특화된 전처리 기법(Subword Tokenization 모델 제작 시 해당 도메인의 데이터로만 모델 제작
3) COVID-19 도메인에 특화된 Vocab 추출
4) Sequence to Sequence 모델을 이용한 도메인 특화 모델 제작
5) 특화된 번역기와 기보유 된 번역 엔진과의 성능 비교 평가
• 도메인 특화에서 무엇보다 중요한 요소는 해당 도메인에 특화된 데이터를 구축하는 일이며 이는 시간과 비용이
많이 드는 작업이다. 그러나 본 논문에서 사용한 Corona Crisis Corpus같은 경우 TAUS에서 모든 사람들에게 무료로
오픈되어 사용되고 있으며 이로 인하여 데이터 구축에 대한 시간과 비용을 절약할 수 있다.
3. 기술 활용 및 응용 분야
• 특화된 도메인 용어에 대한 올바른 번역에 응
원천기술 I 04. 기계번역 143
--- PAGE 144 ---
4. 실험
4.1 실험 개요
• 본 연구에서 실험을 위한 데이터로 TAUS에서 공개한 Corona Crisis Corpus를 이용하였다. 해당 코퍼스는 영어를
중심으로 스페인어, 이탈리아어, 프랑스어, 독일어, 러시아어, 중국어의 병렬 말뭉치를 제공해준다.
[ 그림 ]
Concept of
COVID-19 Neural
Machine Translation
Platform
Human-Inspired AI 144
--- PAGE 145 ---
4.2 실험 결과
• 실험결과 본 논문에서 제안한 번역 모델이 상용화 시스템인 구글 번역기와 비교하여 모든 언어쌍에 대해여 BLEU
점수와 BLEU1, BLEU2, BLEU3, BLEU4까지 모든 수치에서 높은 성능을 보였다.
Experimental Results of COVID-19 Model versus Google Translation
Model BLEU BLEU1 BLEU2 BLEU3 BLEU4
(Our) English-Chinese 26.23 53.70 32.10 23.90 19.80
(Google) English-Chinese 15.36 47.40 21.10 10.90 6.10
(Our) Chinese-English 36.28 65.80 44.10 34.30 28.00
(Google) Chinese-English 29.49 59.70 35.30 23.60 16.20
(Our) English-French 46.10 71.60 53.70 42.30 33.80
(Google) English-French 43.21 68.30 50.20 38.30 29.50
(Our) French-English 48.62 74.20 54.60 42.50 33.60
(Google) French-English 44.61 69.20 50.30 38.50 29.50
(Our) English-German 35.21 64.00 42.00 31.10 24.10
(Google) English-German 26.03 53.10 31.40 20.30 13.50
(Our) German-English 41.89 71.20 49.80 38.10 30.10
(Google) German-English 36.00 64.70 43.00 30.50 22.20
(Our) English-Italian 44.80 70.20 51.10 40.00 32.10
(Google) English-Italian 39.64 64.50 45.40 34.00 26.00
(Our) Italian-English 50.21 75.50 56.00 44.30 35.90
(Google) Italian-English 47.75 72.90 54.30 42.80 34.10
(Our) English-Spanish 44.40 71.50 51.80 40.20 32.00
(Google) English-Spanish 40.44 66.30 46.30 34.50 26.20
(Our) Spanish-English 46.69 74.30 54.00 42.50 34.20
(Google) Spanish-English 42.89 68.20 48.50 36.50 28.00
(Our) English-Russian 28.09 56.50 35.20 25.30 18.90
(Google) English-Russian 26.08 53.40 33.30 22.50 15.50
(Our) Russian-English 34.35 65.10 41.00 29.70 22.30
(Google) Russian-English 31.09 58.70 36.50 24.90 17.50
5. 데모
• 데모 : http://nlplab.iptime.org:32250/
원천기술 I 04. 기계번역 145
--- PAGE 146 ---
05 인간의 인지과정을 반영한 도메인 특화 번역기
1. 기술 설명
• 도메인특화 NMT를 만들기 위한 기존 방법들은 대부분 general corpora에 대한 pretrain을 거친 후 domain-
specialized corpora에 대한 finetuning을 하는 방식으로 진행되었다.
• 해당 기술은 cross language speech perception과 관련한 인지과학적 이론을 바탕으로 기존의 방법들을
재해석하였고, 인간의 인지과정에서 모티브를 얻은 새로운 도메인특화 방법론인 Cross Communication
Method(CCM) 방법론이다. 실험결과 기존의 방법론들과 비교하여 양적으로나 질적으로나 더 우수한 성능을
거두었다.
2. 기술 방법
• CCM에서는 Primary mapping으로 인한 secondary mapping의 제약을 없애기 위해 mapping 과정을 직렬화하지
않았다. 그리고 general corpora와 domain specialized corpora가 배치 내에서 소통할 수 있도록 배치 구성면에서
기존 방법과의 차별점을 두었다. 더 나아가 일반 코퍼스는 source language와 target language에 대한 일반적인
번역을 학습하고, 도메인 특화 코퍼스는 도메인에 특화된 용어들과 표현들을 학습한다는 점에서 각각 성격이
구별된다는 점을 감안하여 배치 구성 시 비율을 고려했다.
• 본 연구는 cross language speech perception과 관련한 해석들을 바탕으로 기존 방식에 대한 의문을 가지게
되었다. 영유아는 이중 언어 음성을 인식 및 구별할 때 primary mapping의 영향 없이 phoneme들을 구별해낼 수
있다. 그러나 어른의 경우 특정 언어에 대한 mapping이 고정되어 있기 때문에 새로운 언어의 음성을 구별하고자 할
때 initial mapping에 의해 새로운 mapping의 학습을 제한받게 된다. 이에 대해 우리는 도메인 특화 기계번역에서
PFA technique를 활용하는 것이 과연 옳은지에 대한 의문을 가지게 되었다. 따라서 본 연구에서는 기존의 방법에서
탈피하여 새로운 도메인 특화 기계 번역인 Cross Communication Method(CCM)을 제안한다.
3. 기술 활용 및 응용 분야
• 다양한 도메인 특화 기계번역 분야에 활용 가능
4. 실험
4.1 실험 개요
• 본 연구는 CCM과 PFA(Pretrain-Finetuning-Approach), Brute CCM을 각각 비교해봄으로써 도메인 특화 기계
번역에서 최적의 성능을 내는 방법론을 찾는다. Brute CCM에서는 corpora에 대한 구분 없이 combined corpora를
활용하여 번역을 학습한다. 이 방법론들에 대해 성능을 비교할 뿐만 아니라 질적 분석도 수행함으로써 visible하게
방법론들에 대한 결과도 비교하였다.
Human-Inspired AI 146
--- PAGE 147 ---
CCM: Cross Communication Method for Domain Specialized Neural Machine Translation
4.2 실험 결과
• 실험결과 본 연구에서 제안하는 CCM이 가장 좋은 성능을 보임을 알 수 있었다. Brute CCM 방법과 비교했을 때,
CCM은 16.13 BLEU score로 더 높은 성능을 냈다. 우리는 이를 통해 단순히 GC와 DC를 한데 합치는 것 이상으로,
추가적인 tuning이 있어야만 도메인특화 번역에서 optimal한 성능을 보일 수 있다는 것을 보였다.
• CCM은 PFA(Pretrain-Finetuning-Approach)에 비해서도 1.19 BLEU score가 더 높은 모습을 보여주었다. 이는 PFA를
진행할 시 DC에 대한 학습을 진행하면서 이전에 학습된 정보를 잃기에 발생하는, catastrophic forgetting문제와
관련 지어 해석할 수 있다.
Training Method BLEU Corpus weight BLEU
1.00 91.53
General Model 27.06
0.50 91.26
Google Translation 55.68 0.33 91.30
0.25 90.85
Random batch training 75.40 0.20 90.58
0.10 88.87
Incremental Trining 90.34
0.03 80.79
0.02 77.91
CCM(ours) 91.53
0.01 74.21
원천기술 I 04. 기계번역 147
--- PAGE 149 ---
02
EDUCATION PROCESS
교육과정
1. 교육 과정 개요
2. 교육 프로그램
3. 세부 교육 과정
--- PAGE 151 ---
교육 과정 개요
최근 4차 산업혁명은 인간과 기계의 잠재적 능력을 극대화하는 제반 기술 혁신이 경제 · 사회 전반의 시스템에 큰
변화를 가져올 것으로 전망되고 있습니다.
기술의 융합을 통해 비약적인 기술 발전이 가속화되고, 공유경제, 온디멘드 경제의 기본이 되는 디지털 플랫폼 기반의
기술 및 기업들이 성장세를 이룰 것으로 예측됩니다.
모든 산업에서 인공지능 기술은 필수입니다. 특히 컴퓨터가 경험을 통해 인간처럼 스스로 학습할 수 있게 하는
기계학습(Machine Learning)은 인공지능에서 핵심적인 기술입니다. 이러한 이유로 제약, 의학, 건설, 디자인, 교육 분야
등 많은 산업 분야의 기업들이 기계학습을 도입하기를 원하고 있습니다.
기술의 내재화를 위해서는 전문 인력이 가장 중요한데, 인공지능 분야의 전문가들을 중소기업과 중견기업에서
고용하기가 쉽지 않은 것이 사실입니다. 이에 고려대학교 Human-Inspired AI 연구소에서는 “인공지능 기초
교육과정”을 개최하고자 합니다.
본 교육과정은 인공지능의 개념에서부터 기계학습의 기초이론, 딥러닝 알고리즘, 기계학습 Tool kit 학습, 그리고
실무적용을 위한 예제 실습 등으로 내실 있게 구성하였습니다.
짧은 기간이지만 본 교육 과정을 통해서 인공지능 및 기계학습의 이론과 실무기술을 학습한 수강생들이 본인이 속한
기관에서 기계학습의 지평을 열고 회사의 인공지능 기술의 내재화를 위한 교두보가 될 수 있음을 확신합니다.
많은 분들이 본 교육 과정에 참가하여 기계학습 기반의 인공지능에 대한 이해와 이를 바탕으로 현장에 적용하거나
새로운 비즈니스 창출의 기회가 될 수 있기를 기대합니다.
교육 프로그램
• S그룹 언어지능 교육과정(단기)
• L그룹 중급 언어지능과정(3-4주)
• 하계/동계 자연어처리와 언어지능(기초교육과정)
• AI 산업전반 및 활용사례, 실무에 적용할 수 있는 프로젝트 연구(회사 맞춤형 교육)
• AI 기초 프로그래밍 및 심화프로그램(자연어처리, 음성인식, 영상처리)
• AI와 빅테이터 분야 인력양성을 위한 교육
교 육 과 정 151
--- PAGE 152 ---
세부 교육 과정
1. 자연어처리 소개, 프로그래밍 및 자연어처리의 기본 원리
• 자 연어처리 개요: 자연어처리에 대한 정의 및 자연어처리 절차, 최신 동향
• 딥러닝의 소개: 자연어처리의 핵심기술인 딥러닝 기법인 CNN, RNN
• 언 어를 이해하는 컴퓨터: 언어를 이해하는 자연어처리 기술
• 언 어를 생성하는 컴퓨터: 언어를 생성하는 자연어처리 기술
• 자 연어처리의 다양한 응용 분야: 문서분류, 자동정보추출, 기계독해, 문서요약, 기계번역, 자동질의응답, 대화 시스템 등
• P ython 기초: Python 기초 문법 및 함수, Python을 이용한 뉴스기사 분석 및 시각화
• 자 연어처리를 위한 전처리 프로그래밍 방법: 텍스트 데이터 분석 및 시각화
• P ython을 이용한 뉴스기사 분석 및 시각화
2. 자연어처리, 기계학습 및 데이터마이닝
• 자 연어처리 기초: 자연어처리의 정의 및 절차, 최신 동향
• 텍 스트 전처리: 텍스트 데이터를 사용하고자 하는 목적에 맞게 가공하기 위한 토큰화, 어간 추출, 불용어 제거, 텍스트 분리
• 어 휘 분석, 문장 분석, 의미 분석: 텍스트 데이터를 의미의 최소 단위인 어휘로 분리하고 적합한 품사 정보를
할당하기 위한 형태소 분석, 문장 구조분석, 문장의 의미 해석방법
• 문 맥 분석: 하나 이상의 문장으로 구성된 텍스트 데이터를 진술, 주장, 추측, 명령, 요청 등 발화의 의도를 분석하고
구분하는 방법
• 구 문 분석: 주어진 텍스트를 일련의 구문과 토큰으로 분해하여 해당 토큰의 언어적 정보를 제공하는 방법
• 화 행 분석: 대화 속에서 문장의 화행을 알아내는 방법
• 개 체명 인식: 텍스트 데이터에서 객체를 표현하는 단어들을 구분하고, 그 단어에 해당 객체를 의미하는 라벨을
할당하는 기법
• 형 태소 분석: 형태소 분석이란 형태소를 비롯하여 어근, 접두사/접미사, 품사(part of speech) 등 다양한 언어적
속성을 파악하는 방법
• 웹 스크래핑: 웹 사이트 상에서 원하는 부분에 위치한 정보를 자동으로 추출하여 수집하는 기술
• 웹 크롤링: 자동화 봇(bot)인 웹 크롤러가 정해진 규칙에 따라 복수 개의 웹 페이지를 브라우징하는 행위
• 토 큰화: 데이터를 사용하고자 하는 용도에 맞게 토큰이라 불리는 단위로 나누는 작업
• 과 거에 대한 이해, 미래에 대한 예측 선택: 기계학습과 데이터베이스 소개 및 기계학습의 원리
• 미 래에 대한 예측을 위한 다양한 기계학습 방법 습득: 다양한 기계학습 모델 및 인공신경망, 딥러닝 소개
• 기 계학습 도구 실습 및 기계학습을 이용한 문제해결: 언어모델, 기계번역, 영상주석 생성 등 기계학습 방법을 이용한
문제해결 소개
3. 여러가지 자연어처리 응용분야
• N amed Entity Recognition: 텍스트 데이터에서 객체를 표현하는 단어를 구분하고 그 단어에 해당하는 객체를
의미하는 라벨을 할당하는 기법
152 Human-Inspired AI
--- PAGE 153 ---
• L anguage model: 일련의 순서를 가진 텍스트 데이터가 주어졌을 때 다음에 위치할 텍스트 데이터를 확률적으로
예측하는 언어 모델과 통계적 기법과 기계학습 기반의 방법론
• I nformation Extraction: 비정형 텍스트 데이터에서 목적에 맞는 정형화된 텍스트 정보를 추출하는 방법과 개체명
인식과 개체간의 관계를 표현하는 등의 방법론
• Q uestion & Answering: 질문이 주어졌을 때 그에 해당하는 답변을 자동으로 선택, 생성하는 방법과 이를 구현하기
위한 규칙 기반, 기계학습 기반의 방법론
• M achine Translation: 입력된 단어를 다른 단어로 바꿔서 출력해주는 방법을 설명하고 전통적인 기계번역 방법 및
통계 기반, 기계학습 기반의 번역방법론
• T ext Generation: 주어진 상황 및 입력 텍스트에 적절한 문장을 생성하는 방법을 설명하고 기계학습 기반의 방법
및 강화학습 기반의 방법
• M achine Reading Comprehension: 주어진 텍스트 데이터의 문법적, 의미적 맥락을 이해하여 상황에 맞는
답변방법을 설명하고 MRC를 위한 자연어처리 기술 및 평가방법
• D ialogue System: 사용자와 컴퓨터가 정보를 주고받는 시스템에 대한 설명과 대화시스템의 종류와 구축방법
• T ext Summarization: 텍스트 데이터의 정보를 컴퓨터가 압축된 문장으로 표현해주는 방법과 자동요약의 종류 및 기법
• T ext Categorizarion & Sentiment Analysis: 문서에 포함된 텍스트 데이터를 분석하여 정해진 카테고리에 따라서
분류하는 방법과 텍스트 데이터에서 작성자의 주관적인 의견을 텍스트로부터 분석해내는 방법과 구현방법
4. 딥러닝 기반 자연어처리 (실습, 응용 개발 프로젝트)
• C olab 툴킷 사용: Colab은 구글에서 공개한 웹기반의 Python 개발 환경으로 기본적이 사용법과 특징
• 단 어 임베딩: 단어 임베딩은 단어를 벡터로 표현하는 것으로 임베딩 기법의 종류를 설명하고 기본적 기법 활용
• 딥 러닝 기반의 Language 모델링: 여러 가지 자연어처리의 응용에서 학습한 언어모델의 일부를 Colab을 통해 구현
• 어 절 자동생성기 개발 프로젝트: RNN을 이용
• 딥 러닝 기반의 한국어 문장 및 문서, 감정 분석: Text Categorization & Sentiment Analysis 방법을 Colab을 통해
일부 구현
• 감 정분석 또는 문서분석기 개발 프로젝트: CNN을 이용
• 인 공신경망과 기계학습: 인공신경망과 기계학습의 이론 및 실습
• C NN, RNN, 언어표현: CNN, RNN 등 딥러닝 이론 및 실습
• 한 국어 언어표현 실습: 한국어 자연어처리 이론 및 실습
5. 시각지능
• 컴 퓨터비전 구현, 영상의 이해 및 CNN 활용
• O pen CV for python3, Open CV 활용
• S egmentation,Transfer Learning, Auto Encoder
• 시 각지능 프로그램(차량번호판 인식 등)
교 육 과 정153
--- PAGE 154 ---
단기 과정
세부 교육과정은 변동될 수 있습니다.
<PART 1. 기계학습 기초이론>
주 제 학습목표
인공지능의 개념을 학습한다.
인공지능 개념 이해 I
신경망의 기원이 되는 퍼셉트론에 대해 학습한다.
[Supervised, Unsupervised learning]
인공지능 개념 이해 II 퍼셉트론의 한계를 극복하는 신경망의 개념을 학습한다.
최적의 손실 함수를 찾는 경사법을 학습한다.
[신경망, 딥러닝 이해]
인공지능 개념 이해 III
층을 깊게 쌓은 심층 신경망(딥러닝)의 특징, 풀어야할 과제, 가능성을 이해한다.
[오차역전파 개념이해]
신경망 학습 원리 I
가중치 매개변수의 기울기를 효율적으로 계산하는 오차역전파법을 학습한다.
[신경망 학습 관련 기술의 이해-I]
신경망 학습 원리 II
매개변수 갱신, 가중치의 초깃값, 배치 등의 기술을 학습한다.
[신경망 학습 관련 기술의 이해-II]
신경망 학습 원리 III
정규화, 과대적합(오버피팅), 드롭아웃, 하이퍼파라미터 최적화 등의 기술을 학습한다.
<PART 2. 자연어처리 이론 및 응용시스템>
주 제 학습목표
자연어처리의 기본 자연언어 처리의 개념을 이해한다.
개체명 인식
이름을 가진 개체(Named Entity)를 인식하는 개체명 인식 기술을 학습한다.
(Named Entityecognition)
언어모델 가장 자연스러운 단어 시퀀스를 찾아내기 위해 다음 단어 시퀀스의 확률을
(Language Model) 할당(assign)하는 언어모델을 학습한다.
정보추출
비정형 텍스트로부터 유용한 정보를 자동으로 추출하는 정보 추출을 학습한다.
(Information Extraction)
질의응답 사용자가 필요한 정보를 자연어 질문으로 입력하였을 때, 시스템이 질문에 부합하는 정보를
(Question & Answering) 찾아 제시하는 기술을 학습한다.
기계번역 하나의 언어로 쓰인 글을 같은 의미를 나타내는 다른 언어의 글로 변환하는 기계번역에 대해
(Machine Translation) 학습한다.
대화 시스템
자연어를 사용해 인간과 대화하는 대화시스템에 대해 학습한다.
(Dialogue System)
154 Human-Inspired AI
--- PAGE 155 ---
장기 과정
세부 교육과정은 변동될 수 있습니다.
<PART 1. 인공지능 개념 및 이해>
학습 내용 세부 내용
인공지능이란
인간의 정보처리 원리를 모사한 지능형 시스템의
인공지능 개요 인공지능의 특징
개념에 대해 학습한다.
인공지능 연구분야
기계학습이란
기계학습의 기본 개념과 원리를 소개하고, 종류와
기계학습 개념 및 활용 기계학습의 원리
활용방법을 알아본다.
기계학습의 종류 및 활용
Concept Learning
Decision Tree
Linear Logistic Regression
Neural Network
Bayesian Learning
기계학습 알고리즘 유형에 따른 기초 알고리즘 Instance based learning and LR
기계학습 기초 알고리즘
개념, 데이터 표상, 기계학습에서의 데이터에
대해 학습한다. Genetic Algorithm
Analytical Learning
SVM
HMM
Supervised learning
Unsupervised learning
<PART 2. 인공지능 개발 준비>
학습 내용 세부 내용
Python 기초/고급
딥러닝 개발에 많이 사용되는 프로그래밍 언어
딥러닝 개발 환경 Colab 실습환경 및 데이터 전처리
및 프레임워크를 학습한다.
Tensorflow tutorial
본 교육과정을 통해 적용해 볼 수 있는 도메인을 선정하고, 팀을 구성하여 수행한다.
Term Project
교수자의 조언을 통해 도메인 및 주제를 선정한다.
교 육 과 정 155
--- PAGE 156 ---
<PART 3. 딥러닝 기초 이론>
학습 내용 세부 내용
신경망 개념과 구조
퍼셉트론(Perceptron)의 동작 원리와 신경망 동작원리
인공신경망 개념과 원리 MLP(Multi–Layered–Perceptron)에 대해
학습한다. MLP 구조
MLP 동작원리
딥러닝이란
딥러닝 모델의 핵심
딥러닝 시스템 구축을 위한 고려사항
딥러닝에 대한 기본 개념을 하고, 딥러닝
딥러닝 개요
알고리즘 유형 및 활용 방안을 소개한다.
딥러닝 모델의 뼈대
비선형 결정 경계와 활성함수
딥러닝 모델의 학습
backpropagation, ReLU, Weight 초기화,
딥러닝 환경 설정을 바탕으로 간단한 알고리즘 Dropout 등
딥러닝 기초 실습
구동에 초점을 맞춰 실습한다.
MNIST 실습
Term Project 주제 및 팀 구성에 따른 기획안 발표
<PART 4. 딥러닝 알고리즘>
학습 내용 세부 내용
CNN이란
CNN 알고리즘의 개념과 동작 원리를 학습하며,
CNN
CNN 알고리즘을 바탕으로 영상 분류, 물체 위치
(Convolutional Neural CNN의 구조
추정 및 검출 등의 시각 인식 문제에 응용하는
Network)
방법을 소개한다.
CNN 활용
RNN이란
RNN RNN 알고리즘의 구성과 동작 원리를 학습하며,
(Recurrent Neural RNN 알고리즘을 바탕으로 언어 모델링, 자동 RNN의 구조
Network) 번역, 이미지 캡셔닝 등 응용 방법을 소개한다.
RNN 활용
CNN for Sentiment Analysis
CNN과 RNN을 활용하여 감성분석, 언어모델
CNN, RNN 실습
등을 실습한다.
Language Model and RNN
Term Project 진행 과정 점검 및 애로사항 체크
156 Human-Inspired AI
--- PAGE 157 ---
<PART 5. 언어지능 구현>
학습 내용 세부 내용
자연어처리란
자연어처리의 응용 분야
자연어처리에 대한 기본 개념 및 자연어처리
자연어처리 개요 절차에 대해 학습하고, 최신 연구 동향을 자연어처리는 왜 어려운가?
소개한다.
자연어처리 연구의 패러다임
딥러닝을 사용하는 자연어처리 연구
언어모델이란
언어모델 언어모델의 개념과 종류, 일반화에 대하여 통계적 언어모델
(Language Model) 학습하고, 언어모델 평가방법 및 퍼플렉서티에
언어모델 대하여 소개한다. 일반화(Generalization)
모델 평가와 퍼플렉서티
(Perplexity)
질의응답이란
정보검색 기반 질의응답
질의응답 질의응답 시스템의 과거부터 현재까지의
딥러닝 기반 질의응답
(Question & 변화에 대하여 학습하고, 최근 딥러닝을
Answering) 적용한 질의응답 시스템에 대하여 소개한다.
딥러닝 기반 질의응답 모델
시각 질의응답
(Visual Question&Answering)
Term Project 진행 과정 점검 및 애로사항 체크
<PART 6. 시각지능 구현>
학습 내용 세부 내용
GAN이란
이미지/동영상 등의 데이터를 기반으로 시각
시각지능의 이해 이해 지능 및 시각 분석 지능 등의 개념에 대해 GAN의 구조
학습한다.
GAN 활용
이미지/동영상 등의 데이터를 기반으로 시각 이해 지능 및 시각 분석 지능 등의 개념에 대해
시각지능 구현
학습한다.
본 교육과정을 통해 학습한 내용을 바탕으로 팀별 산출물을 발표하고 교수자가 조언함으로써
Term Project
학습 능률을 높이도록 한다.
교 육 과 정 157
--- PAGE 159 ---
03
INDUSTRY-ACADEMIA
COOPERATION
산학협력
프로그램
1. 파트너십
2. AI 계약연구센터
--- PAGE 161 ---
2023 고려대학교 Human-inspired AI연구소 파트너십 프로그램
고려대학교 Human-inspired AI연구소 파트너십은 플래티넘(Platinum)과 골드(Gold)로 구분합니다.
[Platinum]
• 딥 테크 트랙으로, 기업의 투자 라운드에 맞춤 투자 기회 제공
IR DAY 참여 기회 제공 • 투 자 성사 때까지 pitch deck 자문 컨설팅
• 투 자 성사 때까지 IR DAY 참여 기회 제공
• 파 트너에게 AI 원천기술 기술이전(특허 양도*, 노하우 이전)
AI 원천기술 기술이전 기회 제공
• 파 트너에게 이전한 AI 원천기술 상용화 지원
파트너에 특화된 AI 공동 연구 • 파 트너가 추진하는 AI 전략 과제 공동 연구
기회 제공* • 파 트너와 공동으로 AI 원천기술 전담 계약연구센터 설립운용
• 파 트너가 추진하는 AI 전략 과제를 정부 펀드와 연계하여 전략적으로 사전 기획*, 공동 제안,
AI 정부 과제 공동 추진 협력
수주, 수행 등 제반 협력
• 파 트너가 필요한 AI 혁신 전략 수립 관련하여 AI 전략 자문 컨퍼런스 및 컨설팅 수행 기회 제공
AI 전략적 자문 기회 제공*
• 파 트너 AI 혁신 자문 전담 TFT 조직 및 운용 기회 제공
• 파트너에게 필요한 파트너 전용 AI 교육 프로그램을 개발하여 교육 기회 제공*
AI 교육 프로그램 지원 • 파트너 AI 교육 전담 TFT 조직 및 운용 기회 제공*
• 연구소에서 시행하는 정기적인 AI 유료 교육 프로그램 초대(1사 1인 무료 수강)
파트너 전용 AI career 지원 기회
• 파트너사 직원 대상 AI 석박사 학위 지원(풀타임/파트타임) 기회 제공
제공*
• 새로운 AI 패러다임 공유 유료 컨퍼런스 초대(1사 2인)
AI Techday 초대 • 기술교류회에서 VIP로 소개*
• 기술교류회에 사례 발표 기회 제공
• 후원: 1천만원
• 프로그램별 상세한 추진 협의는 개별적으로 수행 필요
• 파트너에 특화된 AI 공동 연구, AI 원천기술 기술이전, AI 전략적 자문, AI 교육 프로그램 개발은 별도 계약으로 추진
• * Platinum Partnership에 특화 제공
산학협력 프로그램 161
--- PAGE 162 ---
[Gold]
• 딥테크 트랙으로, 기업의 투자 라운드에 맞춤 투자 기회 제공
IR DAY 참여 기회 제공 • 투자 성사 때까지 pitch deck 자문 컨설팅
• 투자 성사 때까지 IR DAY 참여 기회 제공
• 파트너에게 AI 원천기술 기술이전(특허 통상전용 실시권과 노하우 이전)
AI 원천기술 기술이전 기회 제공
• 파트너에게 이전한 AI 원천기술 상용화 지원
• 파트너가 추진하는 AI 전략 과제를 정부 펀드와 연계하여 적시에 중요 정보 공유, 공동
AI 정부 과제 공동 추진 협력
제안, 수주, 수행 관련 협력
AI 교육 프로그램 지원 • 연구소에서 시행하는 정기적인 AI 유료 교육 프로그램 초대(1사 1인 무료 수강)
• 새로운 AI 패러다임 공유 유료 컨퍼런스 초대(1사 2인)
AI Techday 초대
• 기술교류회에 사례 발표 기회 제공
• 후원: 7백만원
• 프로그램별 상세한 추진 협의는 개별적으로 수행 필요
• AI 원천기술 기술이전 시 별도 계약으로 추진
162 Human-Inspired AI
--- PAGE 163 ---
[Partnership program 비교]
프로그램 Platinum Gold
상시 투자 기회 제공
IR DAY 참여 기회 제공 기업 맞춤 투자 환경 제공
Pitch deck 자문 제공
AI 원천기술 기술이전 기회 제공
파트너에 특화된 AI 공동연구 기회 제공
펀드 연계 제안 지원
AI 정부 과제 공동 추진 협력 공동 제안과 수행 협력
AI연구센터 설립·운용 기회 제공
AI 전략적 자문 기회 제공
연구소 교육 프로그램 정보 공유
유료 교육 프로그램 초대*
AI 교육 프로그램 지원
전용 교육 프로그램 개발 기회 제공
파트너 전용 AI career 지원 기회 제공
사례 발표 기회 제공
AI Techday 초대
VIP 우대
* 초대 및 지원은 1사 1인을 기준으로 함
산학협력 프로그램 163
--- PAGE 164 ---
AI 계약연구센터
[목적]
고려대학교 Human-inspired AI 계약연구센터는 기술벤처를 위한 기업에서 필요로 하는 AI원천기술 확보 및 기업
애로기술 해결을 위한 긴밀한 협조 체제를 구축하여 기업 경쟁력 향상을 목적으로 함
[개요]
기업과 연구소는 공동으로 AI 계약연구센터를 설립 및 운영함. Human-inspired AI 연구소는 기업이 필요로 하는
AI원천기술을 기획 및 개발하여, 소스와 매뉴얼 등 모든 지식재산권을 양도하고, 전담 연구할 교수와 연구원을 지원함
[상호협력 내용]
1. 인공지능기술 공동연구 추진
1.1. 지능형 서비스 비전 수립
1.2. 다양한 알고리즘 디자인 및 AI 결과의 제시
1.3. 기업 애로기술 공동연구
2. 산학협력 인프라 확충에 필요한 상호 협력
2.1. 기업의 목소리에 기반한 이슈 발굴
2.2. 해외 신기술 세미나 및 공동 논문
2.3. 학계 최신 인공지능 기술 공유
2.4. 국내 · 외 학술대회 참여
[계약기간]
* 총 계약기간 : 10년 / 2년 단위(기업의 현금 지출 또는 기보 지원)
* 금액은 담당자 별도협의
* 현금, 기보 지원이 아닌 기업 주식, 로얄티(수익배분모델)로 협의할 경우 별도 협의 필요
[주요혜택]
Partnership Gold 회원자격 부여
164 Human-Inspired AI
--- PAGE 165 ---
04
APPENDIX
부 록
1. 특허 등록
2. 기술 이전
--- PAGE 167 ---
특 허 등 록
특허명 등록번호 등록일
문서 유사도 측정 모델 생성 방법 및 이를 이용한 문서 유사도 측정 방법 10-2507192 2023. 03. 02.
대화 수행 시스템, 장치 및 방법 10-2491931 2023. 01. 09.
언어 대응 화상 출력 장치, 방법 및 시스템 10-2476497 2022. 12. 07.
한국어 맞춤법 교정장치 및 방법 10-2430918 2022. 08. 04.
기계 번역을 이용한 고대한글 번역 방법 10-2425922 2022. 07. 22.
기계 번역의 학습 데이터 구축을 위한 방법 10-2409667 2022. 06. 13.
신경망 기계 번역의 교정 성능 평가 메트릭스 및 그 구축 방법 10-2390154 2022. 04. 20.
자동화 기반의 가짜 뉴스 탐지 장치 및 방법 10-2340542 2021. 12. 14.
영상에 관한 대화 처리 장치, 방법 및 시스템 10-2311218 2021. 10. 05.
자동 질의응답 장치 10-2271361 2021. 06. 24.
스마트 시니어 인지반응 기반의 모델링 방법 및 장치 10-2092633 2020. 03. 18.
방송 표준을 위한 개인 맞춤형 UX/UI서비스를 제공하는 장치 및 방법 10-2014475 2019. 08. 20.
사물인터넷에 기반한 경험 공유 방법 및 장치 10-1909646 2018. 10. 12.
음식 배달 중개 방법 및 장치 10-1896408 2018. 09. 03.
집단지성을 이용한 뉴스 판단 방법 및 장치 10-1869815 2018. 06. 15.
집단감성을 이용한 맞춤형 영화 상영 방법 및 그 장치 10-1858120 2018. 05. 09.
사물인터넷 기반의 스마트 의자 및 착석자세 분석 방법, 스마트 의자 관리 장치 및 그 방법 10-1816711 2018. 01. 03.
사물 인터넷 기반의 대출 관리 방법 및 그 장치 10-1795462 2017. 11. 02.
사물 인터넷 기반 스마트 화분 및 그 관리 시스템 10-1789165 2017. 10. 17.
온라인 학습자를 위한 주의집중 판단 시스템 및 그 방법 10-1770817 2017. 08. 17.
인문학 정보를 자동으로 구성하는 방법 10-1760478 2017. 07. 17.
집단지성을 이용한 꿈 해몽 방법 및 장치 10-1748411 2017. 06. 12.
학습코스 자동 생성 방법 및 시스템 10-1745874 2017. 06. 05.
사용자 참여 기반의 정책 발굴 방법 10-1739925 2017. 05. 19.
지능형 학습 관리 방법 10-1693592 2017. 01. 02.
인지능력 측정 장치 및 방법 10-1222210 2013. 01. 08.
학습자 인지능력 기반의 외국어 학습 시스템 및 방법 10-1136415 2012. 04. 06.
외국어 학습자용 인지능력 진단 시스템 및 방법 10-1113908 2012. 02. 01.
부 록 167
--- PAGE 168 ---
기 술 이 전
• 딥러닝기반 고유명사 개체명 인식기술
• 딥러닝 기법을 이용한 온라인 콘텐츠 추천 기술
• 딥러닝 기법을 이용한 한국어 개체명 인식 시스템
• 딥러닝 기법을 이용한 콘텐츠 추천 시스템
• 외국어 학습자용 학습 과제 수행 시스템 및 방법
• 동영상 내의 멀티모달 정보 색인 기술
• 사용자 콘텐츠 소비 정보를 이용한 추천 시스템
• 은닉 마르코프 모델을 이용한 시계열적 추천 모델
• 온라인 협력 학습 플랫폼
• 디지털 콘텐츠 전용 검색 기술
• 반응형 웹기반의 소셜 러닝 서비스 플랫폼
• 지능형 패션 이미지 검색 시스템
• 한국어 개체명 인식기 및 의존 구문 분석기
• 지능형 분류기술
• 자연어-사진 크로스모달 임베딩 및 검색 기술
• 지능형 치매재활훈련기술
• 파라미터의 계수를 이용한 신경망 축소 기술
• 학습기반 질의 처리 기술
• 딥러닝을 이용하여 이미지를 검색하는 단말 장치 및 방법
• 기사 유사도 추천 및 문서 내 핵심키워드 추출 기술
• 딥러닝 기반 자동 질의응답 시스템 원천기술
• 딥러닝 기반 자동 질의응답 시스템 기술
• 딥러닝을 이용한 유사문서 검색기술
• 뇌혈류 영상이미지 인식 및 판독 결과 다이얼로그 원천기술
• 영문법 교정기 원천기술
• 생애검진 자연어 챗봇 서비스
• 마케팅 성과(회원가입, 구매전환 등) 예측을 위한 사용자 분류 모델과 광고 솔루션을 위한 인공지능 기술
• AI Avatar를 위한 자연어 처리기술
• MRC 질의응답 기술 고도화 기술 개발
• MRC 질의응답 기술 고도화 및 AI를 활용한 VOD 추천 알고리즘 기술 개발
• 외국어 학습자용 학습 과제 수행 시스템 및 방법
• 사용자 프로파일링을 통한 주제 추천 기술
• 반려동물 서비스 업체 온라인 평판조회시스템
168 Human-Inspired AI
--- PAGE 169 ---
기 술 이 전
• 사람의 후각케어시스템 연계 지능형 감성분석 및 대화기술
• 딥러닝 기반 자동 질의 응답 시스템 원천기술
• 사물 인터넷 기반의 대출 관리 방법 및 그 장치
• 온라인 학습자를 위한 주의집중 판단 시스템 및 그 방법
• 자연어 요약처리를 위한 형태소 분석 기술과 개체명 인식 기술
• 머신러닝 기술을 활용한 반려동물용 노이즈캔슬링 목걸이
• 사물인터넷 기반의 스마트 의자 및 착석자세 분석 방법, 스마트 의자 관리 장치 및 그 방법
• 음성기반 한 ·영 유사단어 매칭기술
• 온라인 댓글 평판 분류시스템
• Sentence-based Semantic Similarity Search 기술 모델 개발
• 온라인 댓글 평판 조회시스템
• 인공지능 매칭 원천 기술
• 인공지능 추천 알고리즘
부 록 169
--- PAGE 170 ---
본 책자는 과학기술정보통신부 및
정보통신기획평가원의 대학ICT연구센터지원사업(IITP-2018-0-01405)과
교육부 및 한국연구재단의 기초연구사업(NRF-2021R1A6A1A03045425)의
지원을 받아 수행된 결과임.