#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test des Longueurs Fixes AZR

Ce script teste l'implémentation des longueurs fixes :
- Rollout 2 : 4 séquences de 4 P/B chacune
- Rollout 3 : 1 séquence de 3 S/O
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import AZRCluster, AZRConfig

def test_fixed_lengths():
    """Test des longueurs fixes selon spécifications AZR"""
    
    print("🎯 TEST LONGUEURS FIXES AZR")
    print("=" * 40)
    
    # Initialisation
    config = AZRConfig()
    cluster = AZRCluster(cluster_id=1, config=config)
    
    print("✅ Configuration et cluster initialisés")
    print(f"   • Longueur fixe Rollout 2 : {config.rollout2_fixed_length} P/B")
    print(f"   • Longueur fixe Rollout 3 : {config.rollout3_fixed_length} S/O")
    print(f"   • Nombre de séquences Rollout 2 : {config.rollout2_sequences_count}")
    
    # ========================================================================
    # TEST 1 : GÉNÉRATION ROLLOUT 2 - 4 SÉQUENCES DE 4 P/B
    # ========================================================================
    
    print("\n🎲 TEST 1 : GÉNÉRATION ROLLOUT 2")
    print("-" * 35)
    
    # Données simulées pour le test
    analyzer_report = {
        'signals_summary': {
            'top_signals': [
                {'signal_name': 'IMPAIR_TO_PLAYER', 'signal_type': 'pb_prediction', 'strength': 0.8, 'confidence': 0.85, 'strategy': 'player_focus'},
                {'signal_name': 'PAIR_TO_BANKER', 'signal_type': 'pb_prediction', 'strength': 0.75, 'confidence': 0.8, 'strategy': 'banker_focus'},
                {'signal_name': 'SO_SAME_PREDICTION', 'signal_type': 'so_prediction', 'strength': 0.7, 'confidence': 0.75, 'target_outcome': 'S', 'strategy': 'same_focus'},
                {'signal_name': 'PAIR_SYNC_PATTERN', 'signal_type': 'pattern', 'strength': 0.65, 'confidence': 0.7, 'strategy': 'pair_sync_exploitation'}
            ],
            'exploitation_ready': True,
            'overall_confidence': 0.8
        },
        'generation_guidance': {
            'primary_focus': 'IMPAIR_patterns',
            'secondary_focus': 'so_patterns',
            'avoid_patterns': [],
            'optimal_sequence_length': 4,  # Sera ignoré car longueur fixe
            'confidence_thresholds': {'high': 0.7, 'medium': 0.6, 'low': 0.5},
            'exploitation_strategy': 'moderate',
            'risk_level': 'low'
        },
        'quick_access': {
            'current_state': 'IMPAIR_SYNC',
            'next_prediction_pb': 'P',
            'next_prediction_so': 'S',
            'prediction_confidence': 0.85,
            'alert_level': 'HIGH',
            'exploitation_ready': True
        },
        'indices_analysis': {
            'pbt': {'pbt_sequence': ['P', 'B', 'P', 'B', 'P']},
            'impair_pair': {'position_types': ['IMPAIR', 'PAIR', 'IMPAIR', 'PAIR', 'IMPAIR']},
            'desync_sync': {'sync_sequence': ['SYNC', 'DESYNC', 'SYNC', 'DESYNC', 'SYNC']},
            'combined': {'combined_sequence': ['IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC']}
        },
        'synthesis': {'analysis_quality': 0.8},
        'sequence_metadata': {'total_hands_analyzed': 50}
    }
    
    # Génération des séquences
    sequences = cluster._rollout_generator(analyzer_report)
    
    print(f"✅ Séquences générées : {len(sequences)}")
    print(f"   Structure : {type(sequences)}")
    print(f"   Clés : {list(sequences.keys()) if isinstance(sequences, dict) else 'N/A'}")

    # Extraire les vraies séquences
    actual_sequences = sequences.get('sequences', []) if isinstance(sequences, dict) else sequences

    print(f"   Vraies séquences : {len(actual_sequences)}")

    # Validation du nombre de séquences
    if len(actual_sequences) == config.rollout2_sequences_count:
        print(f"✅ Nombre correct : {len(actual_sequences)} séquences")
    else:
        print(f"❌ Nombre incorrect : {len(actual_sequences)} au lieu de {config.rollout2_sequences_count}")

    # Validation de la longueur de chaque séquence
    for i, sequence in enumerate(actual_sequences):
        # Extraire les valeurs P/B du format enrichi
        if isinstance(sequence, list):
            # Format enrichi : extraire predicted_pbt
            pb_values = []
            for item in sequence:
                if isinstance(item, dict) and 'predicted_pbt' in item:
                    pb_values.append(item['predicted_pbt'])
            # Filtrer le summary (dernier élément)
            pb_values = [v for v in pb_values if v in ['P', 'B']]
            sequence_data = pb_values
        elif isinstance(sequence, dict):
            sequence_data = sequence.get('sequence_data', [])
        else:
            sequence_data = sequence

        print(f"   Séquence {i+1} : {sequence_data}")

        if len(sequence_data) == config.rollout2_fixed_length:
            print(f"   ✅ Longueur correcte : {len(sequence_data)} P/B")
        else:
            print(f"   ❌ Longueur incorrecte : {len(sequence_data)} au lieu de {config.rollout2_fixed_length}")
    
    # ========================================================================
    # TEST 2 : CONVERSION ROLLOUT 3 - 4 P/B → 3 S/O
    # ========================================================================
    
    print("\n🎯 TEST 2 : CONVERSION ROLLOUT 3")
    print("-" * 35)
    
    # Test de conversion avec séquence de 4 P/B
    test_pb_sequence = ['P', 'B', 'P', 'B']
    so_sequence = cluster._convert_pb_sequence_to_so(test_pb_sequence, analyzer_report)
    
    print(f"   Séquence P/B : {test_pb_sequence} (longueur {len(test_pb_sequence)})")
    print(f"   Séquence S/O : {so_sequence} (longueur {len(so_sequence)})")
    
    if len(so_sequence) == config.rollout3_fixed_length:
        print(f"   ✅ Conversion correcte : {len(so_sequence)} S/O")
    else:
        print(f"   ❌ Conversion incorrecte : {len(so_sequence)} au lieu de {config.rollout3_fixed_length}")
    
    # Test avec différentes séquences P/B
    test_cases = [
        ['P', 'P', 'B', 'P'],
        ['B', 'P', 'P', 'B'],
        ['P', 'B', 'B', 'B'],
        ['B', 'B', 'P', 'P']
    ]
    
    print("\n📊 Tests de conversion multiples :")
    for i, pb_seq in enumerate(test_cases):
        so_seq = cluster._convert_pb_sequence_to_so(pb_seq, analyzer_report)
        print(f"   Test {i+1} : {pb_seq} → {so_seq}")
        
        if len(so_seq) == config.rollout3_fixed_length:
            print(f"            ✅ Longueur correcte : {len(so_seq)}")
        else:
            print(f"            ❌ Longueur incorrecte : {len(so_seq)}")
    
    # ========================================================================
    # TEST 3 : PIPELINE COMPLET ROLLOUT 2 → ROLLOUT 3
    # ========================================================================
    
    print("\n🔄 TEST 3 : PIPELINE COMPLET")
    print("-" * 30)
    
    if actual_sequences:
        # Prendre la première séquence générée
        best_sequence = actual_sequences[0]

        # Extraire les valeurs P/B du format enrichi
        if isinstance(best_sequence, list):
            # Format enrichi : extraire predicted_pbt
            pb_values = []
            for item in best_sequence:
                if isinstance(item, dict) and 'predicted_pbt' in item:
                    pb_values.append(item['predicted_pbt'])
            # Filtrer le summary (dernier élément)
            pb_sequence_data = [v for v in pb_values if v in ['P', 'B']]
        elif isinstance(best_sequence, dict):
            pb_sequence_data = best_sequence.get('sequence_data', [])
        else:
            pb_sequence_data = best_sequence
        
        print(f"   Séquence sélectionnée : {pb_sequence_data}")
        
        # Conversion en S/O
        final_so_sequence = cluster._convert_pb_sequence_to_so(pb_sequence_data, analyzer_report)
        
        print(f"   Séquence finale S/O : {final_so_sequence}")
        print(f"   Prédiction prochaine manche : {final_so_sequence[0] if final_so_sequence else 'N/A'}")
        
        # Validation finale
        if (len(pb_sequence_data) == config.rollout2_fixed_length and 
            len(final_so_sequence) == config.rollout3_fixed_length):
            print("   ✅ Pipeline complet validé !")
        else:
            print("   ❌ Pipeline incomplet")
    
    # ========================================================================
    # VALIDATION DES PARAMÈTRES DE CONFIGURATION
    # ========================================================================
    
    print("\n🔍 VALIDATION CONFIGURATION")
    print("-" * 30)
    
    print("✅ Paramètres de longueurs fixes :")
    print(f"   • rollout2_fixed_length : {config.rollout2_fixed_length}")
    print(f"   • rollout3_fixed_length : {config.rollout3_fixed_length}")
    print(f"   • rollout2_sequences_count : {config.rollout2_sequences_count}")
    
    print("\n✅ Formule mathématique respectée :")
    print(f"   • {config.rollout2_fixed_length} P/B → {config.rollout3_fixed_length} S/O")
    print(f"   • Formule : longueur_SO = longueur_PB - 1")
    print(f"   • Vérification : {config.rollout3_fixed_length} = {config.rollout2_fixed_length} - 1 ✅")
    
    print("\n🎉 LONGUEURS FIXES AZR VALIDÉES !")
    print("✅ Rollout 2 génère 4 séquences de 4 P/B")
    print("✅ Rollout 3 convertit en 1 séquence de 3 S/O")
    print("✅ Ties fournissent informations indices 1-3")
    print("✅ Prédictions S/O prioritaires")

if __name__ == "__main__":
    test_fixed_lengths()
