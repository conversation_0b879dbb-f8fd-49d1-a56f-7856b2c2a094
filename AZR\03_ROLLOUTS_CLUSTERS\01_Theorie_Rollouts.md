# 🔄 MODULE 3.1 : THÉORIE DES ROLLOUTS

## 🎯 **OBJECTIFS D'APPRENTISSAGE**

À la fin de cette section, vous comprendrez :
- ✅ Les fondements théoriques des rollouts (Bertsekas)
- ✅ L'adaptation des rollouts au contexte AZR
- ✅ Les différents types de rollouts et leurs applications
- ✅ L'implémentation pratique des algorithmes de rollout

---

## 📚 **FONDEMENTS THÉORIQUES DES ROLLOUTS**

### **🧠 Définition Originale (Bertsekas)**

Les **rollouts** sont une méthode d'amélioration de politique basée sur la simulation de trajectoires futures à partir d'un état donné.

**Principe Central :**
```
Pour chaque action possible :
1. Simuler une trajectoire complète
2. Évaluer la récompense totale obtenue
3. Choisir l'action avec la meilleure évaluation
```

### **📊 Formulation Mathématique**

**Fonction de Valeur par Rollout :**
```
Q^π_rollout(s, a) = r(s, a) + γ ∑_{s'} P(s'|s,a) V^π_base(s')
```

**Où :**
- `s` : État actuel
- `a` : Action considérée
- `π_base` : Politique de base (heuristique)
- `γ` : Facteur de discount
- `V^π_base` : Valeur estimée par la politique de base

### **🎯 Propriété Fondamentale**

**Théorème d'Amélioration :**
```
Si π_base est une politique quelconque, alors :
π_rollout(s) = argmax_a Q^π_rollout(s, a)

Garantit : V^π_rollout(s) ≥ V^π_base(s) ∀s
```

**Intuition :** Les rollouts améliorent toujours la politique de base

---

## 🔄 **ROLLOUTS DANS LE CONTEXTE AZR**

### **🎭 Rollouts pour le Proposeur**

**Objectif :** Évaluer la qualité d'une tâche proposée

```python
def proposer_rollout(self, proposed_task, horizon=10):
    """
    Simule l'impact d'une tâche proposée sur l'apprentissage
    """
    total_learning_value = 0
    
    for step in range(horizon):
        # 1. Simuler la résolution de la tâche
        simulated_solution = self.solver.solve(proposed_task)
        
        # 2. Évaluer l'apprentissage résultant
        learning_signal = self.evaluate_learning(
            proposed_task, 
            simulated_solution
        )
        
        # 3. Mettre à jour le modèle simulé
        self.update_simulated_model(learning_signal)
        
        # 4. Accumuler la valeur d'apprentissage
        total_learning_value += learning_signal * (self.gamma ** step)
        
        # 5. Générer la prochaine tâche dans la simulation
        proposed_task = self.generate_next_task()
    
    return total_learning_value
```

### **🔧 Rollouts pour le Résolveur**

**Objectif :** Évaluer les conséquences d'une solution proposée

```python
def solver_rollout(self, current_state, proposed_solution, horizon=5):
    """
    Simule les conséquences d'une solution sur les performances futures
    """
    cumulative_reward = 0
    state = current_state.copy()
    
    for step in range(horizon):
        # 1. Appliquer la solution proposée
        immediate_reward = self.apply_solution(state, proposed_solution)
        
        # 2. Observer le nouvel état
        state = self.transition_state(state, proposed_solution)
        
        # 3. Utiliser la politique de base pour les étapes suivantes
        if step > 0:
            proposed_solution = self.base_policy.get_action(state)
        
        # 4. Accumuler les récompenses
        cumulative_reward += immediate_reward * (self.gamma ** step)
    
    return cumulative_reward
```

---

## 🎯 **TYPES DE ROLLOUTS EN AZR**

### **🔄 1. Rollouts Monte Carlo**

**Caractéristiques :**
- Utilise la randomisation pour l'exploration
- Moyenne sur plusieurs trajectoires
- Robuste aux incertitudes

```python
class MonteCarloRollout:
    def __init__(self, num_samples=100):
        self.num_samples = num_samples
    
    def evaluate_action(self, state, action):
        total_value = 0
        
        for _ in range(self.num_samples):
            # Trajectoire aléatoire
            trajectory_value = self.simulate_random_trajectory(
                state, action
            )
            total_value += trajectory_value
        
        return total_value / self.num_samples
    
    def simulate_random_trajectory(self, initial_state, first_action):
        state = initial_state
        value = 0
        
        # Première action fixée
        reward = self.get_reward(state, first_action)
        state = self.transition(state, first_action)
        value += reward
        
        # Actions suivantes aléatoires
        while not self.is_terminal(state):
            action = self.random_policy.sample(state)
            reward = self.get_reward(state, action)
            state = self.transition(state, action)
            value += reward * self.gamma
        
        return value
```

### **🎯 2. Rollouts Déterministes**

**Caractéristiques :**
- Utilise une politique de base fixe
- Résultats reproductibles
- Plus rapide à calculer

```python
class DeterministicRollout:
    def __init__(self, base_policy):
        self.base_policy = base_policy
    
    def evaluate_action(self, state, action):
        current_state = state
        total_value = 0
        discount = 1.0
        
        # Première action fixée
        reward = self.get_reward(current_state, action)
        current_state = self.transition(current_state, action)
        total_value += reward
        
        # Actions suivantes selon politique de base
        while not self.is_terminal(current_state):
            discount *= self.gamma
            next_action = self.base_policy.get_action(current_state)
            reward = self.get_reward(current_state, next_action)
            current_state = self.transition(current_state, next_action)
            total_value += discount * reward
        
        return total_value
```

### **🌟 3. Rollouts Hybrides AZR**

**Innovation :** Combine Monte Carlo et déterministe selon le contexte

```python
class HybridAZRRollout:
    def __init__(self, uncertainty_threshold=0.5):
        self.uncertainty_threshold = uncertainty_threshold
        self.mc_rollout = MonteCarloRollout()
        self.det_rollout = DeterministicRollout()
    
    def evaluate_action(self, state, action):
        # Estimer l'incertitude de l'état
        uncertainty = self.estimate_uncertainty(state)
        
        if uncertainty > self.uncertainty_threshold:
            # Haute incertitude → Monte Carlo
            return self.mc_rollout.evaluate_action(state, action)
        else:
            # Basse incertitude → Déterministe
            return self.det_rollout.evaluate_action(state, action)
    
    def estimate_uncertainty(self, state):
        # Utiliser l'entropie de la distribution d'actions
        action_probs = self.base_policy.get_action_probabilities(state)
        entropy = -sum(p * log(p) for p in action_probs if p > 0)
        return entropy / log(len(action_probs))  # Normaliser
```

---

## 🚀 **OPTIMISATIONS AVANCÉES**

### **⚡ Rollouts Tronqués**

**Problème :** Rollouts complets trop coûteux
**Solution :** Limiter l'horizon et utiliser une fonction de valeur

```python
class TruncatedRollout:
    def __init__(self, horizon=5, value_function=None):
        self.horizon = horizon
        self.value_function = value_function
    
    def evaluate_action(self, state, action):
        current_state = state
        total_value = 0
        discount = 1.0
        
        for step in range(self.horizon):
            if step == 0:
                # Première action fixée
                next_action = action
            else:
                # Actions suivantes selon politique
                next_action = self.base_policy.get_action(current_state)
            
            reward = self.get_reward(current_state, next_action)
            current_state = self.transition(current_state, next_action)
            total_value += discount * reward
            discount *= self.gamma
            
            if self.is_terminal(current_state):
                break
        
        # Ajouter la valeur estimée de l'état final
        if not self.is_terminal(current_state) and self.value_function:
            terminal_value = self.value_function.estimate(current_state)
            total_value += discount * terminal_value
        
        return total_value
```

### **🔄 Rollouts Adaptatifs**

**Innovation :** Ajuster l'horizon selon la complexité

```python
class AdaptiveRollout:
    def __init__(self, min_horizon=3, max_horizon=10):
        self.min_horizon = min_horizon
        self.max_horizon = max_horizon
    
    def evaluate_action(self, state, action):
        # Estimer la complexité de l'état
        complexity = self.estimate_state_complexity(state)
        
        # Ajuster l'horizon
        horizon = int(
            self.min_horizon + 
            (self.max_horizon - self.min_horizon) * complexity
        )
        
        # Effectuer le rollout avec l'horizon adapté
        return self.truncated_rollout(state, action, horizon)
    
    def estimate_state_complexity(self, state):
        # Utiliser la variance des valeurs d'actions possibles
        action_values = [
            self.quick_estimate(state, a) 
            for a in self.get_possible_actions(state)
        ]
        variance = np.var(action_values)
        return min(1.0, variance / self.max_variance)
```

---

## 📊 **MÉTRIQUES ET ÉVALUATION**

### **🎯 Qualité des Rollouts**

```python
class RolloutQualityMetrics:
    def __init__(self):
        self.metrics = {}
    
    def evaluate_rollout_quality(self, rollout_values, true_values):
        """
        Évalue la qualité des estimations de rollout
        """
        # 1. Erreur absolue moyenne
        mae = np.mean(np.abs(rollout_values - true_values))
        
        # 2. Corrélation de rang (Spearman)
        rank_correlation = spearmanr(rollout_values, true_values)[0]
        
        # 3. Précision du classement top-k
        top_k_precision = self.compute_top_k_precision(
            rollout_values, true_values, k=3
        )
        
        return {
            'mae': mae,
            'rank_correlation': rank_correlation,
            'top_k_precision': top_k_precision
        }
    
    def compute_top_k_precision(self, predicted, true, k):
        """Précision du classement des k meilleures actions"""
        pred_top_k = set(np.argsort(predicted)[-k:])
        true_top_k = set(np.argsort(true)[-k:])
        
        intersection = len(pred_top_k & true_top_k)
        return intersection / k
```

### **⚡ Performance Computationnelle**

```python
class RolloutPerformanceProfiler:
    def __init__(self):
        self.timing_data = []
        self.memory_data = []
    
    def profile_rollout(self, rollout_function, *args, **kwargs):
        import time
        import tracemalloc
        
        # Mesurer le temps
        start_time = time.time()
        tracemalloc.start()
        
        result = rollout_function(*args, **kwargs)
        
        end_time = time.time()
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # Enregistrer les métriques
        self.timing_data.append(end_time - start_time)
        self.memory_data.append(peak / 1024 / 1024)  # MB
        
        return result, {
            'execution_time': end_time - start_time,
            'peak_memory_mb': peak / 1024 / 1024
        }
```

---

## 🎯 **APPLICATIONS SPÉCIFIQUES AZR**

### **🎭 Rollouts pour Génération de Tâches**

```python
def task_generation_rollout(self, task_template, context):
    """
    Évalue l'impact d'un template de tâche sur l'apprentissage
    """
    best_score = float('-inf')
    best_task = None
    
    # Générer plusieurs variantes de la tâche
    task_variants = self.generate_task_variants(task_template)
    
    for task in task_variants:
        # Simuler l'apprentissage avec cette tâche
        learning_score = self.simulate_learning_episode(task, context)
        
        if learning_score > best_score:
            best_score = learning_score
            best_task = task
    
    return best_task, best_score
```

### **🔧 Rollouts pour Validation de Solutions**

```python
def solution_validation_rollout(self, solution, problem_context):
    """
    Valide une solution en simulant ses conséquences
    """
    # Simuler l'application de la solution
    simulated_outcomes = []
    
    for scenario in self.generate_test_scenarios(problem_context):
        outcome = self.apply_solution_to_scenario(solution, scenario)
        simulated_outcomes.append(outcome)
    
    # Évaluer la robustesse
    success_rate = np.mean([o.success for o in simulated_outcomes])
    avg_quality = np.mean([o.quality for o in simulated_outcomes])
    
    return {
        'success_rate': success_rate,
        'average_quality': avg_quality,
        'robustness_score': success_rate * avg_quality
    }
```

---

## 📝 **POINTS CLÉS À RETENIR**

1. **🔄 Amélioration Garantie** : Les rollouts améliorent toujours la politique de base
2. **🎯 Adaptation AZR** : Rollouts spécialisés pour Proposeur et Résolveur
3. **⚖️ Types Variés** : Monte Carlo, déterministe, hybride selon le contexte
4. **⚡ Optimisations** : Troncature, adaptation, parallélisation
5. **📊 Évaluation** : Métriques de qualité et performance essentielles

---

## 🎯 **EXERCICE PRATIQUE**

**Implémentation :** Créez un rollout simple pour évaluer des actions dans un environnement de votre choix.

```python
class SimpleRollout:
    def __init__(self, environment, base_policy, horizon=5):
        self.env = environment
        self.base_policy = base_policy
        self.horizon = horizon
    
    def evaluate_action(self, state, action):
        # TODO: Implémenter l'évaluation par rollout
        pass
    
    def simulate_trajectory(self, initial_state, first_action):
        # TODO: Simuler une trajectoire complète
        pass
```

---

**➡️ Prochaine section : [3.2 - Rollouts dans le Contexte AZR](02_Rollouts_AZR.md)**
