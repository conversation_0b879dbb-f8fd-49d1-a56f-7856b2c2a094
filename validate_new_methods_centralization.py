#!/usr/bin/env python3
"""
Validation de la centralisation des paramètres des nouvelles méthodes

Ce script vérifie que toutes les valeurs codées en dur ont été remplacées
par des références à la configuration centralisée dans les nouvelles méthodes.
"""

import re
from typing import Dict, List

def check_new_methods_centralization(file_content: str) -> Dict:
    """Vérifie la centralisation dans les nouvelles méthodes optimisées"""
    
    # Nouvelles méthodes à vérifier
    new_methods = [
        '_define_optimized_generation_space',
        '_generate_sequences_from_signals',
        '_generate_sequence_from_signal',
        '_generate_fallback_sequences',
        '_classify_confidence_level'
    ]
    
    centralization_status = {
        'methods_analyzed': 0,
        'hardcoded_values_found': 0,
        'config_references_found': 0,
        'methods_details': {},
        'overall_score': 0
    }
    
    for method_name in new_methods:
        # Trouver la méthode dans le fichier
        method_pattern = rf'def {re.escape(method_name)}\(.*?\):(.*?)(?=def |\Z)'
        match = re.search(method_pattern, file_content, re.DOTALL)
        
        if match:
            centralization_status['methods_analyzed'] += 1
            method_content = match.group(1)
            
            # Compter les références à la configuration
            config_refs = len(re.findall(r'self\.config\.rollout2_', method_content))
            
            # Chercher les valeurs codées en dur restantes
            hardcoded_patterns = [
                r'\b0\.[0-9]+\b',  # Nombres décimaux
                r'\b[1-9][0-9]*\b',  # Nombres entiers > 0
                r'"[A-Z_]+"',  # Chaînes en majuscules
                r"'[A-Z_]+'"   # Chaînes en majuscules
            ]
            
            hardcoded_count = 0
            hardcoded_details = []
            
            for pattern in hardcoded_patterns:
                matches = re.finditer(pattern, method_content)
                for match in matches:
                    value = match.group()
                    context_start = max(0, match.start() - 50)
                    context_end = min(len(method_content), match.end() + 50)
                    context = method_content[context_start:context_end].replace('\n', ' ')
                    
                    # Exclure certains cas légitimes
                    if any(exclude in context for exclude in [
                        'range(', 'len(', 'enumerate(', 'max(', 'min(',
                        'get(', 'append(', 'count(', 'split(',
                        'format(', 'join(', 'replace(', 'strip(',
                        'self.config.', 'f"', "f'", '"""', "'''",
                        'logging', 'logger', 'print(', 'return',
                        'if ', 'elif ', 'else:', 'for ', 'while ',
                        'def ', 'class ', 'import ', 'from '
                    ]):
                        continue
                    
                    hardcoded_count += 1
                    hardcoded_details.append({
                        'value': value,
                        'context': context.strip()[:100] + "..." if len(context) > 100 else context.strip()
                    })
            
            centralization_status['methods_details'][method_name] = {
                'config_references': config_refs,
                'hardcoded_values': hardcoded_count,
                'hardcoded_details': hardcoded_details,
                'centralization_score': 100 if hardcoded_count == 0 else max(0, 100 - (hardcoded_count * 10))
            }
            
            centralization_status['config_references_found'] += config_refs
            centralization_status['hardcoded_values_found'] += hardcoded_count
    
    # Calcul du score global
    if centralization_status['methods_analyzed'] > 0:
        total_hardcoded = centralization_status['hardcoded_values_found']
        total_config_refs = centralization_status['config_references_found']
        
        if total_hardcoded == 0 and total_config_refs > 0:
            centralization_status['overall_score'] = 100
        elif total_hardcoded == 0:
            centralization_status['overall_score'] = 90
        else:
            centralization_status['overall_score'] = max(0, 100 - (total_hardcoded * 5))
    
    return centralization_status

def check_config_section_completeness(file_content: str) -> Dict:
    """Vérifie la complétude de la section de configuration"""
    
    # Paramètres attendus dans la section rollout2 nouvelles méthodes
    expected_params = [
        'rollout2_signal_confidence_default',
        'rollout2_signal_confidence_high',
        'rollout2_signal_confidence_medium',
        'rollout2_signal_confidence_low',
        'rollout2_confidence_level_excellent',
        'rollout2_confidence_level_good',
        'rollout2_confidence_level_acceptable',
        'rollout2_confidence_level_poor',
        'rollout2_default_signal_type',
        'rollout2_default_signal_name',
        'rollout2_default_strategy_prefix',
        'rollout2_default_justification',
        'rollout2_so_prediction_keyword',
        'rollout2_pb_prediction_keyword',
        'rollout2_player_keyword',
        'rollout2_banker_keyword',
        'rollout2_pair_sync_keyword',
        'rollout2_impair_sync_keyword',
        'rollout2_player_result',
        'rollout2_banker_result',
        'rollout2_fallback_strategy_1',
        'rollout2_fallback_strategy_2',
        'rollout2_fallback_strategy_3',
        'rollout2_fallback_strategy_4',
        'rollout2_fallback_justification_1',
        'rollout2_fallback_justification_2',
        'rollout2_fallback_justification_3',
        'rollout2_fallback_justification_4'
    ]
    
    config_completeness = {
        'expected_params': len(expected_params),
        'found_params': 0,
        'missing_params': [],
        'completeness_score': 0
    }
    
    for param in expected_params:
        if f'{param}:' in file_content:
            config_completeness['found_params'] += 1
        else:
            config_completeness['missing_params'].append(param)
    
    config_completeness['completeness_score'] = (
        config_completeness['found_params'] / config_completeness['expected_params']
    ) * 100
    
    return config_completeness

def validate_new_methods_centralization():
    """Validation principale de la centralisation des nouvelles méthodes"""
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return
    
    print("🔧 VALIDATION CENTRALISATION NOUVELLES MÉTHODES ROLLOUT 2")
    print("=" * 60)
    
    # 1. Vérifier la centralisation dans les méthodes
    print("\n📊 1. ANALYSE DE LA CENTRALISATION DANS LES MÉTHODES")
    print("-" * 50)
    
    centralization_status = check_new_methods_centralization(file_content)
    
    print(f"📈 Méthodes analysées : {centralization_status['methods_analyzed']}")
    print(f"⚙️  Références config : {centralization_status['config_references_found']}")
    print(f"⚠️  Valeurs codées dur : {centralization_status['hardcoded_values_found']}")
    
    for method_name, details in centralization_status['methods_details'].items():
        score = details['centralization_score']
        status = "✅" if score == 100 else "⚠️" if score >= 80 else "❌"
        print(f"{status} {method_name:<35} : {details['config_references']:2d} config, {details['hardcoded_values']:2d} codées dur ({score:3.0f}%)")
        
        if details['hardcoded_details']:
            for hd in details['hardcoded_details'][:2]:  # Afficher les 2 premières
                print(f"     • {hd['value']} - {hd['context']}")
            if len(details['hardcoded_details']) > 2:
                print(f"     ... et {len(details['hardcoded_details']) - 2} autres")
    
    # 2. Vérifier la complétude de la configuration
    print("\n⚙️  2. COMPLÉTUDE DE LA SECTION CONFIGURATION")
    print("-" * 45)
    
    config_completeness = check_config_section_completeness(file_content)
    
    print(f"📋 Paramètres attendus : {config_completeness['expected_params']}")
    print(f"✅ Paramètres trouvés  : {config_completeness['found_params']}")
    print(f"❌ Paramètres manquants: {len(config_completeness['missing_params'])}")
    
    if config_completeness['missing_params']:
        print("\nParamètres manquants :")
        for param in config_completeness['missing_params'][:5]:
            print(f"   • {param}")
        if len(config_completeness['missing_params']) > 5:
            print(f"   ... et {len(config_completeness['missing_params']) - 5} autres")
    
    # 3. Score global et recommandations
    print(f"\n📊 3. SCORES ET RECOMMANDATIONS")
    print("-" * 35)
    
    overall_score = centralization_status['overall_score']
    config_score = config_completeness['completeness_score']
    final_score = (overall_score + config_score) / 2
    
    print(f"Score centralisation : {overall_score:3.0f}/100")
    print(f"Score configuration  : {config_score:3.0f}/100")
    print(f"Score final          : {final_score:3.0f}/100")
    
    if final_score >= 95:
        print("\n🏆 EXCELLENT : Centralisation parfaite !")
        print("✅ Toutes les valeurs sont centralisées")
        print("✅ Configuration complète")
    elif final_score >= 80:
        print("\n👍 BON : Centralisation réussie")
        if overall_score < 100:
            print("⚠️  Quelques valeurs codées en dur restantes")
        if config_score < 100:
            print("⚠️  Quelques paramètres de configuration manquants")
    else:
        print("\n⚠️  AMÉLIORATION NÉCESSAIRE :")
        if overall_score < 80:
            print("❌ Trop de valeurs codées en dur dans les méthodes")
        if config_score < 80:
            print("❌ Configuration incomplète")
    
    print(f"\n💡 RECOMMANDATIONS")
    print("-" * 20)
    
    if centralization_status['hardcoded_values_found'] > 0:
        print(f"1. 🔄 Remplacer {centralization_status['hardcoded_values_found']} valeurs codées en dur")
    
    if config_completeness['missing_params']:
        print(f"2. ⚙️  Ajouter {len(config_completeness['missing_params'])} paramètres manquants")
    
    if final_score >= 95:
        print("✅ Aucune action nécessaire - Centralisation parfaite !")
    else:
        print("3. 🧪 Tester les modifications après centralisation")
        print("4. 📝 Documenter les nouveaux paramètres")

if __name__ == "__main__":
    validate_new_methods_centralization()
