#!/usr/bin/env python3
"""
Test de validation de la centralisation des paramètres AZR

Ce script vérifie que tous les paramètres sont correctement centralisés
dans AZRConfig et que les méthodes utilisent bien ces paramètres.
"""

import sys
import os

# Ajouter le répertoire parent au path pour importer le module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from azr_baccarat_predictor import AZRConfig, AZRBaccaratPredictor
    print("✅ Import réussi des classes AZR")
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    sys.exit(1)

def test_azr_config_centralisation():
    """Test de la centralisation des paramètres dans AZRConfig"""
    print("\n🔍 TEST DE CENTRALISATION DES PARAMÈTRES AZR")
    print("=" * 60)
    
    # Créer une instance de configuration
    config = AZRConfig()
    
    # Vérifier que les nouveaux paramètres centralisés existent
    nouveaux_parametres = [
        'correlation_neutral_value',
        'correlation_player_value',
        'correlation_banker_value',
        'correlation_tie_value',
        'correlation_impair_value',
        'correlation_pair_value',
        'default_return_value',
        'default_probability_value',
        'default_confidence_value',
        'distance_adjacent',
        'distance_close',
        'distance_moderate',
        'distance_very_close',
        'context_value_adjacent',
        'context_value_close',
        'context_value_moderate',
        'context_value_very_close',
        'signal_strength_base',
        'signal_minimum_strength',
        'exploitation_minimum_threshold',
        'rollout2_optimal_difficulty',
        'rollout2_diversity_threshold',
        'rollout3_confidence_bonus_correct',
        'min_calibration_factor',
        'max_calibration_factor',
        'calibrated_confidence_weight',
        # Nouveaux paramètres ajoutés
        'zero_value',
        'one_value',
        'half_value',
        'version_number',
        'significance_threshold_5_percent',
        'significance_threshold_10_percent',
        'weight_40_percent',
        'weight_30_percent',
        'weight_20_percent',
        'strength_threshold_60_percent',
        'strength_threshold_70_percent',
        'strength_threshold_80_percent',
        'normalization_factor_2',
        'normalization_factor_3',
        'normalization_factor_8',
        'normalization_factor_10',
        'quality_threshold_30_percent',
        'confidence_threshold_10_percent',
        'sample_size_minimum_2',
        'sample_size_minimum_5',
        'sample_size_optimal_20',
        'variance_threshold_minimum',
        'attention_level_base',
        'attention_level_max',
        'attention_power_base',
        'rarity_factor_high_str',
        'rarity_factor_ultra_high_str',
        'rarity_factor_very_high_str',
        'length_threshold_2',
        'length_threshold_3',
        'multiplier_increment_02',
        'parallel_efficiency_factor',
        'random_exploration_threshold'
    ]
    
    parametres_manquants = []
    parametres_presents = []
    
    for param in nouveaux_parametres:
        if hasattr(config, param):
            parametres_presents.append(param)
            print(f"✅ {param}: {getattr(config, param)}")
        else:
            parametres_manquants.append(param)
            print(f"❌ {param}: MANQUANT")
    
    print(f"\n📊 RÉSULTATS:")
    print(f"   ✅ Paramètres présents: {len(parametres_presents)}/{len(nouveaux_parametres)}")
    print(f"   ❌ Paramètres manquants: {len(parametres_manquants)}")
    
    if parametres_manquants:
        print(f"\n⚠️  Paramètres manquants: {parametres_manquants}")
        return False
    
    return True

def test_properties_dynamiques():
    """Test des propriétés dynamiques (rollout2_rewards, etc.)"""
    print("\n🔍 TEST DES PROPRIÉTÉS DYNAMIQUES")
    print("=" * 60)
    
    config = AZRConfig()
    
    # Tester rollout2_rewards
    try:
        rollout2_rewards = config.rollout2_rewards
        print(f"✅ rollout2_rewards accessible: {len(rollout2_rewards)} paramètres")
        print(f"   - optimal_difficulty: {rollout2_rewards['optimal_difficulty']}")
        print(f"   - diversity_threshold: {rollout2_rewards['diversity_threshold']}")
    except Exception as e:
        print(f"❌ Erreur rollout2_rewards: {e}")
        return False
    
    # Tester rollout3_rewards
    try:
        rollout3_rewards = config.rollout3_rewards
        print(f"✅ rollout3_rewards accessible: {len(rollout3_rewards)} paramètres")
        print(f"   - confidence_bonus_correct: {rollout3_rewards['confidence_bonus_correct']}")
        print(f"   - optimal_risk: {rollout3_rewards['optimal_risk']}")
    except Exception as e:
        print(f"❌ Erreur rollout3_rewards: {e}")
        return False
    
    # Tester cluster_reward_weights
    try:
        cluster_weights = config.cluster_reward_weights
        print(f"✅ cluster_reward_weights accessible: {len(cluster_weights)} paramètres")
        print(f"   - rollout1_weight: {cluster_weights['rollout1_weight']}")
        print(f"   - rollout2_weight: {cluster_weights['rollout2_weight']}")
        print(f"   - rollout3_weight: {cluster_weights['rollout3_weight']}")
    except Exception as e:
        print(f"❌ Erreur cluster_reward_weights: {e}")
        return False
    
    # Tester confidence_calibration
    try:
        confidence_cal = config.confidence_calibration
        print(f"✅ confidence_calibration accessible: {len(confidence_cal)} paramètres")
        print(f"   - min_calibration_factor: {confidence_cal['min_calibration_factor']}")
        print(f"   - calibrated_confidence_weight: {confidence_cal['calibrated_confidence_weight']}")
    except Exception as e:
        print(f"❌ Erreur confidence_calibration: {e}")
        return False
    
    return True

def test_utilisation_dans_methodes():
    """Test que les méthodes utilisent bien les paramètres centralisés"""
    print("\n🔍 TEST D'UTILISATION DANS LES MÉTHODES")
    print("=" * 60)
    
    try:
        # Créer une instance du prédicteur
        predictor = AZRBaccaratPredictor()
        print("✅ Instance AZRBaccaratPredictor créée")
        
        # Vérifier que la configuration est accessible
        config = predictor.config
        print(f"✅ Configuration accessible: {type(config).__name__}")
        
        # Tester l'utilisation des paramètres centralisés
        try:
            # Test simple : vérifier que les paramètres sont utilisés correctement
            # Tester les propriétés dynamiques
            rollout2_rewards = config.rollout2_rewards
            rollout3_rewards = config.rollout3_rewards

            # Vérifier que les valeurs correspondent aux paramètres centralisés
            assert rollout2_rewards['optimal_difficulty'] == config.rollout2_optimal_difficulty
            assert rollout3_rewards['confidence_bonus_correct'] == config.rollout3_confidence_bonus_correct

            print(f"✅ Paramètres centralisés utilisés correctement dans les propriétés")
            print(f"   - rollout2_optimal_difficulty: {config.rollout2_optimal_difficulty}")
            print(f"   - rollout3_confidence_bonus_correct: {config.rollout3_confidence_bonus_correct}")

        except Exception as e:
            print(f"❌ Erreur utilisation paramètres: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur création prédicteur: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 VALIDATION DE LA CENTRALISATION DES PARAMÈTRES AZR")
    print("=" * 80)
    
    tests_reussis = 0
    total_tests = 3
    
    # Test 1: Centralisation des paramètres
    if test_azr_config_centralisation():
        tests_reussis += 1
    
    # Test 2: Propriétés dynamiques
    if test_properties_dynamiques():
        tests_reussis += 1
    
    # Test 3: Utilisation dans les méthodes
    if test_utilisation_dans_methodes():
        tests_reussis += 1
    
    # Résultats finaux
    print("\n" + "=" * 80)
    print("📊 RÉSULTATS FINAUX")
    print("=" * 80)
    print(f"Tests réussis: {tests_reussis}/{total_tests}")
    
    if tests_reussis == total_tests:
        print("🎉 TOUS LES TESTS SONT RÉUSSIS!")
        print("✅ La centralisation des paramètres est fonctionnelle")
        return True
    else:
        print("⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("❌ La centralisation nécessite des corrections")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
