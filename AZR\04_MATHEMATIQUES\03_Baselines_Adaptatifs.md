# ⚖️ MODULE 4.3 : BASELINES ADAPTATIFS

## 🎯 **OBJECTIFS D'APPRENTISSAGE**

À la fin de cette section, vous maîtriserez :
- ✅ Le rôle crucial des baselines dans la réduction de variance
- ✅ Les techniques d'adaptation automatique des baselines
- ✅ L'implémentation de baselines contextuels pour AZR
- ✅ Les méthodes de validation et optimisation

---

## 📊 **FONDEMENTS THÉORIQUES DES BASELINES**

### **🎯 Problème de Variance dans REINFORCE**

L'algorithme REINFORCE souffre d'une variance élevée des gradients :

```
Var[∇_θ J] = Var[∇_θ log π_θ(a|s) × R]
```

**Problème :** La récompense `R` peut varier énormément, causant instabilité.

### **⚖️ Solution par Baselines**

L'introduction d'une baseline `b(s)` réduit la variance sans biaiser l'estimateur :

```
∇_θ J = E[∇_θ log π_θ(a|s) × (R - b(s))]
```

**Propriété clé :** `E[∇_θ log π_θ(a|s) × b(s)] = 0` (baseline n'introduit pas de biais)

### **🎯 Baseline Optimale**

La baseline optimale minimise la variance :

```
b*(s) = E[R × ||∇_θ log π_θ(a|s)||²] / E[||∇_θ log π_θ(a|s)||²]
```

**En pratique :** Souvent approximée par `b(s) ≈ E[R|s]`

---

## 🧠 **BASELINES ADAPTATIFS POUR AZR**

### **🎭 Baselines Duales**

AZR nécessite des baselines séparées pour Proposeur et Résolveur :

#### **Baseline Proposeur**
```
b^propose(z) = E_τ~π_θ^propose [r^propose_e(τ,π_θ) | z]
```

#### **Baseline Résolveur**
```
b^solve(x) = E_y~π_θ^solve [r^solve_e(y,y*) | x]
```

### **🔄 Adaptation Temporelle**

Les baselines s'adaptent au fil du temps selon les performances :

```python
class AdaptiveBaseline:
    """
    Baseline adaptatif pour AZR avec mémoire temporelle
    """
    
    def __init__(self, config):
        self.config = config
        
        # Baselines principaux
        self.baseline_propose = 0.0
        self.baseline_solve = 0.0
        
        # Historique pour adaptation
        self.reward_history = {
            'propose': deque(maxlen=config.baseline_memory_size),
            'solve': deque(maxlen=config.baseline_memory_size)
        }
        
        # Métriques d'adaptation
        self.adaptation_metrics = {
            'propose_variance': deque(maxlen=50),
            'solve_variance': deque(maxlen=50),
            'adaptation_rate': deque(maxlen=50)
        }
        
        # Paramètres adaptatifs
        self.momentum = config.baseline_momentum  # 0.99
        self.epsilon = config.baseline_epsilon    # 1e-8
        
    def update_baseline(self, reward, baseline_type='propose'):
        """
        Mise à jour adaptative d'un baseline
        
        Args:
            reward: Récompense observée
            baseline_type: 'propose' ou 'solve'
        """
        # Sélection du baseline approprié
        if baseline_type == 'propose':
            current_baseline = self.baseline_propose
            history = self.reward_history['propose']
        else:
            current_baseline = self.baseline_solve
            history = self.reward_history['solve']
        
        # Ajout à l'historique
        history.append(reward)
        
        # Calcul du momentum adaptatif
        adaptive_momentum = self._calculate_adaptive_momentum(history)
        
        # Mise à jour avec momentum adaptatif
        new_baseline = (
            adaptive_momentum * current_baseline + 
            (1 - adaptive_momentum) * reward
        )
        
        # Stabilité numérique
        new_baseline = np.clip(new_baseline, self.epsilon, 1.0 - self.epsilon)
        
        # Sauvegarde
        if baseline_type == 'propose':
            self.baseline_propose = new_baseline
        else:
            self.baseline_solve = new_baseline
        
        # Métriques d'adaptation
        self._update_adaptation_metrics(reward, new_baseline, baseline_type)
        
        return new_baseline
    
    def _calculate_adaptive_momentum(self, reward_history):
        """
        Calcule un momentum adaptatif basé sur la stabilité des récompenses
        """
        if len(reward_history) < 5:
            return self.momentum  # Momentum par défaut
        
        # Variance récente des récompenses
        recent_rewards = list(reward_history)[-10:]
        reward_variance = np.var(recent_rewards)
        
        # Adaptation du momentum
        # Plus la variance est élevée, plus le momentum est faible (adaptation rapide)
        variance_factor = 1.0 / (1.0 + reward_variance * 10)
        adaptive_momentum = self.momentum * variance_factor
        
        # Bornes de sécurité
        adaptive_momentum = np.clip(adaptive_momentum, 0.5, 0.999)
        
        return adaptive_momentum
```

---

## 🌟 **BASELINES CONTEXTUELS**

### **🎯 Baseline Dépendant du Contexte**

Pour AZR, la baseline peut dépendre du contexte spécifique :

```python
class ContextualBaseline:
    """
    Baseline contextuel qui s'adapte selon le type de tâche/problème
    """
    
    def __init__(self, config):
        self.config = config
        
        # Baselines par contexte
        self.context_baselines = {}
        
        # Classificateur de contexte
        self.context_classifier = ContextClassifier()
        
        # Baseline global de fallback
        self.global_baseline = 0.5
        
    def get_contextual_baseline(self, context, baseline_type='propose'):
        """
        Obtient la baseline appropriée pour un contexte donné
        """
        # Classification du contexte
        context_key = self.context_classifier.classify(context)
        
        # Récupération ou initialisation de la baseline contextuelle
        if context_key not in self.context_baselines:
            self.context_baselines[context_key] = {
                'propose': self.global_baseline,
                'solve': self.global_baseline,
                'count': 0,
                'variance': 0.0
            }
        
        return self.context_baselines[context_key][baseline_type]
    
    def update_contextual_baseline(self, context, reward, baseline_type='propose'):
        """
        Met à jour la baseline pour un contexte spécifique
        """
        context_key = self.context_classifier.classify(context)
        
        # Initialisation si nécessaire
        if context_key not in self.context_baselines:
            self.context_baselines[context_key] = {
                'propose': self.global_baseline,
                'solve': self.global_baseline,
                'count': 0,
                'variance': 0.0
            }
        
        context_data = self.context_baselines[context_key]
        
        # Mise à jour avec moyenne mobile
        count = context_data['count']
        current_baseline = context_data[baseline_type]
        
        # Facteur d'apprentissage adaptatif
        learning_rate = min(0.1, 1.0 / (count + 1))
        
        # Nouvelle baseline
        new_baseline = (
            (1 - learning_rate) * current_baseline + 
            learning_rate * reward
        )
        
        # Mise à jour de la variance
        if count > 0:
            delta = reward - current_baseline
            new_variance = (
                (count - 1) * context_data['variance'] + delta**2
            ) / count
            context_data['variance'] = new_variance
        
        # Sauvegarde
        context_data[baseline_type] = new_baseline
        context_data['count'] = count + 1
        
        return new_baseline

class ContextClassifier:
    """
    Classificateur simple pour identifier les types de contexte
    """
    
    def classify(self, context):
        """
        Classifie un contexte en catégories pour baselines spécialisées
        """
        # Exemple pour Baccarat : classification par patterns
        if hasattr(context, 'sequence_length'):
            if context.sequence_length < 5:
                return 'short_sequence'
            elif context.sequence_length < 15:
                return 'medium_sequence'
            else:
                return 'long_sequence'
        
        # Classification par défaut
        return 'default'
```

---

## 📈 **OPTIMISATIONS AVANCÉES**

### **🎯 Baseline avec Prédiction**

Utilise un modèle prédictif pour estimer la baseline :

```python
class PredictiveBaseline:
    """
    Baseline prédictive utilisant un modèle d'apprentissage
    """
    
    def __init__(self, config):
        self.config = config
        
        # Modèle prédictif simple (régression linéaire)
        self.predictor_weights = np.random.normal(0, 0.1, size=(10,))
        self.predictor_bias = 0.0
        
        # Historique pour entraînement
        self.training_data = {
            'features': [],
            'targets': []
        }
        
    def predict_baseline(self, context_features):
        """
        Prédit la baseline basée sur les caractéristiques du contexte
        """
        # Extraction des features
        features = self._extract_features(context_features)
        
        # Prédiction linéaire
        prediction = np.dot(features, self.predictor_weights) + self.predictor_bias
        
        # Activation sigmoïde pour borner entre 0 et 1
        baseline = 1.0 / (1.0 + np.exp(-prediction))
        
        return baseline
    
    def update_predictor(self, context_features, observed_reward):
        """
        Met à jour le modèle prédictif avec une nouvelle observation
        """
        features = self._extract_features(context_features)
        
        # Ajout aux données d'entraînement
        self.training_data['features'].append(features)
        self.training_data['targets'].append(observed_reward)
        
        # Maintenir taille limitée
        if len(self.training_data['features']) > 1000:
            self.training_data['features'].pop(0)
            self.training_data['targets'].pop(0)
        
        # Réentraînement périodique
        if len(self.training_data['features']) % 50 == 0:
            self._retrain_predictor()
    
    def _extract_features(self, context):
        """
        Extrait des caractéristiques numériques du contexte
        """
        # Exemple pour contexte Baccarat
        features = np.zeros(10)
        
        if hasattr(context, 'sequence_history'):
            seq = context.sequence_history[-10:]  # 10 derniers éléments
            
            # Features statistiques
            features[0] = len(seq) / 10.0  # Longueur normalisée
            features[1] = seq.count('P') / max(1, len(seq))  # Proportion Player
            features[2] = seq.count('B') / max(1, len(seq))  # Proportion Banker
            
            # Features de patterns
            if len(seq) >= 2:
                features[3] = 1.0 if seq[-1] == seq[-2] else 0.0  # Répétition
            
            # Features de tendance
            if len(seq) >= 5:
                recent = seq[-5:]
                features[4] = recent.count('P') / 5.0
                features[5] = recent.count('B') / 5.0
        
        return features
    
    def _retrain_predictor(self):
        """
        Réentraîne le modèle prédictif par régression linéaire
        """
        if len(self.training_data['features']) < 10:
            return
        
        X = np.array(self.training_data['features'])
        y = np.array(self.training_data['targets'])
        
        # Régression linéaire simple (méthode des moindres carrés)
        try:
            # Ajout du biais
            X_with_bias = np.column_stack([X, np.ones(X.shape[0])])
            
            # Résolution normale
            coeffs = np.linalg.lstsq(X_with_bias, y, rcond=None)[0]
            
            # Mise à jour des paramètres
            self.predictor_weights = coeffs[:-1]
            self.predictor_bias = coeffs[-1]
            
        except np.linalg.LinAlgError:
            # En cas d'erreur, garder les paramètres actuels
            pass
```

---

## 📊 **MÉTRIQUES ET VALIDATION**

### **🎯 Métriques de Qualité des Baselines**

```python
class BaselineQualityMetrics:
    """
    Métriques pour évaluer la qualité des baselines adaptatifs
    """
    
    def __init__(self):
        self.metrics_history = []
        
    def evaluate_baseline_quality(self, baseline_system, recent_episodes):
        """
        Évalue la qualité d'un système de baselines
        """
        metrics = {}
        
        # 1. Réduction de variance
        rewards_with_baseline = []
        rewards_without_baseline = []
        
        for episode in recent_episodes:
            reward = episode['reward']
            baseline = episode['baseline']
            
            rewards_with_baseline.append(reward - baseline)
            rewards_without_baseline.append(reward)
        
        variance_reduction = (
            np.var(rewards_without_baseline) - np.var(rewards_with_baseline)
        ) / np.var(rewards_without_baseline)
        
        metrics['variance_reduction'] = max(0.0, variance_reduction)
        
        # 2. Précision de prédiction
        prediction_errors = [
            abs(episode['reward'] - episode['baseline']) 
            for episode in recent_episodes
        ]
        metrics['prediction_accuracy'] = 1.0 - np.mean(prediction_errors)
        
        # 3. Stabilité temporelle
        baselines = [episode['baseline'] for episode in recent_episodes]
        if len(baselines) >= 10:
            baseline_variance = np.var(baselines)
            metrics['temporal_stability'] = 1.0 / (1.0 + baseline_variance)
        
        # 4. Score de qualité global
        metrics['overall_quality'] = np.mean([
            metrics.get('variance_reduction', 0.0),
            metrics.get('prediction_accuracy', 0.0),
            metrics.get('temporal_stability', 0.5)
        ])
        
        return metrics
```

### **🧪 Tests de Validation**

```python
def validate_baseline_system():
    """
    Tests de validation pour les baselines adaptatifs
    """
    # Test 1: Convergence vers la moyenne
    baseline = AdaptiveBaseline(config)
    rewards = [0.6, 0.7, 0.5, 0.8, 0.6, 0.7]
    
    for reward in rewards:
        baseline.update_baseline(reward, 'propose')
    
    expected_mean = np.mean(rewards)
    actual_baseline = baseline.baseline_propose
    
    assert abs(actual_baseline - expected_mean) < 0.1, "Convergence échouée"
    
    # Test 2: Réduction de variance
    # TODO: Implémenter test de variance
    
    print("✅ Tous les tests de baselines réussis")
```

---

## 📝 **POINTS CLÉS À RETENIR**

1. **⚖️ Réduction de Variance** : Baselines essentielles pour stabilité TRR++
2. **🔄 Adaptation Automatique** : Momentum et paramètres s'ajustent dynamiquement
3. **🎯 Contexte Spécifique** : Baselines spécialisées par type de tâche
4. **📈 Prédiction** : Modèles prédictifs pour baselines plus précises
5. **📊 Validation** : Métriques complètes pour évaluer la qualité

---

## 🎯 **EXERCICE PRATIQUE**

**Implémentation :** Créez un système de baselines adaptatifs simple :

```python
class SimpleAdaptiveBaseline:
    def __init__(self, momentum=0.9):
        self.momentum = momentum
        self.baseline = 0.5
        
    def update(self, reward):
        # TODO: Mise à jour avec momentum adaptatif
        pass
        
    def get_advantage(self, reward):
        # TODO: Calcul de l'avantage
        return reward - self.baseline
```

---

**➡️ Prochaine section : [4.4 - Analyse de Convergence](04_Analyse_Convergence.md)**
