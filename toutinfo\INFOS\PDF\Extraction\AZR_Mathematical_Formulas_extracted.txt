# EXTRACTION TEXTUELLE - AZR_Mathematical_Formulas.pdf
# Généré automatiquement le 2025-05-28 10:24:01
# Source: AZR_Mathematical_Formulas.pdf
# ================================================================


--- PAGE 1 ---
5202
yaM
7
]GL.sc[
2v53330.5052:viXra
May9,2025
Absolute Zero: Reinforced Self-play Reasoning with Zero Data
AndrewZhao1, <PERSON>ranWu3, <PERSON><PERSON>ue1, <PERSON><PERSON>u2, <PERSON><PERSON><PERSON>1, <PERSON><PERSON><PERSON>1, <PERSON><PERSON><PERSON><PERSON><PERSON>1,
<PERSON><PERSON><PERSON><PERSON>1,<PERSON><PERSON><PERSON><PERSON>3,<PERSON><PERSON><PERSON><PERSON><PERSON>g2,(cid:0) andGaoHuang1,(cid:0)
1TsinghuaUniversity 2BeĳingInstituteforGeneralArtificialIntelligence 3PennsylvaniaStateUniversity
<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
Reinforcementlearningwithverifiablerewards(RLVR)hasshownpromiseinenhancingthereasoning
capabilitiesoflargelanguagemodelsbylearningdirectlyfromoutcome-basedrewards. RecentRLVR
worksthatoperateunderthezerosettingavoidsupervisioninlabelingthereasoningprocess,butstill
depend on manually curated collections of questions and answers for training. The scarcity of high-
quality,human-producedexamplesraisesconcernsaboutthelong-termscalabilityofrelyingonhuman
supervision,achallengealreadyevidentinthedomainoflanguagemodelpretraining. Furthermore,ina
hypotheticalfuturewhereAIsurpasseshumanintelligence,tasksprovidedbyhumansmayofferlimited
learningpotentialforasuperintelligentsystem. Toaddresstheseconcerns, weproposeanewRLVR
paradigmcalledAbsoluteZero,inwhichasinglemodellearnstoproposetasksthatmaximizeitsown
learningprogressandimprovesreasoningbysolvingthem,withoutrelyingonanyexternaldata. Under
thisparadigm,weintroducetheAbsoluteZeroReasoner(AZR),asystemthatself-evolvesitstraining
curriculumandreasoningabilitybyusingacodeexecutortobothvalidateproposedcodereasoningtasks
andverifyanswers,servingasanunifiedsourceofverifiablerewardtoguideopen-endedyetgrounded
learning. Despitebeingtrainedentirelywithoutexternaldata,AZRachievesoverallSOTAperformance
oncodingandmathematicalreasoningtasks,outperformingexistingzero-settingmodelsthatrelyontens
ofthousandsofin-domainhuman-curatedexamples. Furthermore, wedemonstratethatAZRcanbe
effectivelyappliedacrossdifferentmodelscalesandiscompatiblewithvariousmodelclasses.
Code ProjectPage Logs Models
Figure1.AbsoluteZeroReasoner(AZR)achievesstate-of-the-artperformancewithZERODATA.Withoutrelyingonanygold
labelsorhuman-definedqueries,AbsoluteZeroReasonertrainedusingourproposedself-playapproachdemonstratesimpressivegeneral
reasoningcapabilitiesimprovementsinbothmathandcoding,despiteoperatingentirelyout-of-distribution. Remarkably,AZRsurpasses
modelstrainedontensofthousandsofexpert-labeledin-domainexamplesinthecombinedaveragescoreacrossbothdomains.
(cid:0)Correspondingauthor(s)
--- PAGE 2 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Supervised Learning Reinforcement Learning with Verifiable Rewards Absolute Zero (Ours)
Less Human Supervision
Figure2.AbsoluteZeroParadigm. Supervisedlearningreliesonhuman-curatedreasoningtracesforbehaviorcloning. Reinforcement
learningfromverifiedrewards,enablesagentstoself-learnreasoning,butstilldependsonexpert-definedlearningdistributionanda
respectivesetofcuratedQApairs,demandingdomainexpertiseandmanualeffort. Incontrast,weintroduceanewparadigm,Absolute
Zero,fortrainingreasoningmodelswithoutanyhuman-curateddata. Weenvisionthattheagentshouldautonomouslyproposetasks
optimizedforlearnabilityandlearnhowtosolvethemusinganunifiedmodel. Theagentlearnsbyinteractingwithanenvironmentthat
providesverifiablefeedback,enablingreliableandcontinuousself-improvemententirelywithouthumanintervention.
1.Introduction
Largelanguagemodels(LLMs)haverecentlyachievedremarkableimprovementsinreasoningcapabilitiesbyemployingReinforcement
LearningwithVerifiableRewards(RLVR)(Lambertetal.,2024). Unlikemethodsthatexplicitlyimitateintermediatereasoningsteps,
RLVRusesonlyoutcome-basedfeedback,enablinglarge-scalereinforcementlearningovervasttaskdatasets(DeepSeek-AIetal.,2025;
Teametal.,2025;Jaechetal.,2024;OpenAI,2025b;a). Aparticularlycompellingvariantisthe“zero”RLVRparadigm(DeepSeek-AI
etal.,2025),whichforgoesanycold-startdistillationdata,usingneitherhuman-generatednorAI-generatedreasoningtraces,andapplies
RLVRdirectlyonthebasemodelwithtaskrewards. However,thesemethodsstilldependheavilyonexpertlycurateddistributionsof
reasoningquestion–answerpairs,whichraisesseriousconcernsabouttheirlong-termscalability(Villalobosetal.,2024). Asreasoning
modelscontinuetoadvance,theeffortrequiredtoconstructlarge-scale,high-qualitydatasetsmaysoonbecomeunsustainable(Yue
etal.,2025). AsimilarscalabilitybottleneckhasalreadybeenidentifiedinthedomainofLLMpretraining(Sutskeveretal.,2024).
Furthermore,asAIsystemscontinuetoevolveandpotentiallyexceedhumanintellect,anexclusivedependenceonhuman-designed
tasksrisksimposingconstraintsontheircapacityforautonomouslearningandgrowth(Hughesetal.,2024). Thisunderscorestheneed
foranewparadigmthatbeginstoexplorepossibilitiesbeyondtheconstraintsofhuman-designedtasksandpreparesforafutureinwhich
AIsystemsmaysurpasshumanintelligence.
Tothisend,wepropose“AbsoluteZero”,anewparadigmforreasoningmodelsinwhichthemodelsimultaneouslylearnstodefinetasks
thatmaximizelearnabilityandtosolvethemeffectively,enablingself-evolutionthroughself-playwithoutrelyingonexternaldata. In
contrasttopriorself-playmethodsthatarelimitedtonarrowdomains,fixedfunctionalities,orlearnedrewardmodelsthatareproneto
hacking(Silveretal.,2017;Chenetal.,2025;2024),theAbsoluteZeroparadigmisdesignedtooperateinopen-endedsettingswhile
remaininggroundedinarealenvironment. Itreliesonfeedbackfromtheenvironmentasaverifiablesourceofreward,mirroringhow
humanslearnandreasonthroughinteractionwiththeworld,andhelpspreventissuessuchashackingwithneuralrewardmodels(Hughes
etal.,2024). SimilartoAlphaZero(Silveretal.,2017),whichimprovesthroughself-play,ourproposedparadigmrequiresnohuman
supervisionandlearnsentirelythroughself-interaction. WebelievetheAbsoluteZeroparadigmrepresentsapromisingsteptoward
enablinglargelanguagemodelstoautonomouslyachievesuperhumanreasoningcapabilities.
Buildingonthisnewreasoningparadigm,weintroducetheAbsoluteZeroReasoner(AZR),whichproposesandsolvescodingtasks. We
castcodeexecutorasanopen-endedyetgroundedenvironment,sufficienttobothvalidatetaskintegrityandalsoprovideverifiable
feedbackforstabletraining. WeletAZRconstructthreetypesofcodingtasks: inferandreasonaboutoneparticularelementina
program,input,outputtriplet,whichcorrespondstothreecomplementarymodesofreasoning: induction,abduction,anddeduction. We
traintheentiresystemend-to-endwithanewlyproposedreinforcementlearningadvantageestimatortailoredtothemultitasknatureof
theproposedapproach.
Despitebeingtrainedentirelywithoutanyin-distributiondata,AZRdemonstratesremarkablecapabilitiesacrossdiversereasoningtasks
inmathandcoding. Inmathematics,AZRachievescompetitiveperformancecomparedtozeroreasonermodelsexplicitlyfine-tuned
withdomain-specificsupervision. Incodingtasks,AZRestablishesanewstate-of-the-artperformance,surpassingmodelsspecifically
trainedwithcodedatasetsusingRLVR.Furthermore, AZRoutperformsallpreviousmodelsbyanaverageof1.8absolutepoints
2
--- PAGE 3 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
comparedtomodelstrainedinthe“zero”settingusingin-domaindata. Thesesurprisingresultshighlightthatgeneralreasoningskills
canemergewithouthuman-curateddomaintargeteddata,positioningAbsoluteZeroasanpromisingresearchdirectionandAZRasa
firstpivotalmilestone. BesidestheremarkableresultsAZRachievedwithzerohumandataforreasoning,wealsomakeveryinteresting
findingssummarizedbelow:
• Codepriorsamplifyreasoning. ThebaseQwen-Coder-7bmodelstartedwithmathperformance3.6pointslowerthanQwen-7b.
ButafterAZRtrainingforbothmodels,thecodervariantsurpassedthebaseby0.7points,suggestingthatstrongcodingcapabilities
maypotentiallyamplifyoverallreasoningimprovementsafterAZRtraining.
• CrossdomaintransferismorepronouncedforAZR.AfterRLVR,expertcodemodelsraisemathaccuracybyonly0.65pointson
average,whereasAZR-Base-7BandAZR-Coder-7Btrainedonself-proposedcodereasoningtasksimprovemathaverageby10.9and
15.2,respectively,demonstratingmuchstrongergeneralizedreasoningcapabilitygains.
• Biggerbasesyieldbiggergains. Performanceimprovementsscalewithmodelsize: the3B,7B,and14Bcodermodelsgain+5.7,
+10.2,and+13.2pointsrespectively,suggestingcontinuedscalingisadvantageousforAZR.
• Commentsasintermediateplansemergenaturally. Whensolvingcodeinductiontasks,AZRofteninterleavesstep-by-stepplans
ascommentsandcode(AppendixC.3),resemblingtheReActpromptingframework(Yaoetal.,2023). Similarbehaviorhasbeen
observedinmuchlargerformal-mathmodelssuchasDeepSeekProverv2(671B)(Renetal.,2025). Wethereforebelievethatallowing
themodeltouseintermediatescratch-padswhengeneratinglong-formanswersmaybebeneficialinotherdomainsaswell.
• CognitiveBehaviorsandTokenlengthdependsonreasoningmode. Distinctcognitivebehaviors—suchasstep-by-stepreasoning,
enumeration,andtrial-and-errorallemergedthroughAZRtraining,butdifferentbehaviorsareparticularlyevidentacrossdifferent
typesoftasks. FurthermoretokencountsgrowoverAZRtraining,butthemagnitudeofincreasealsodiffersbytasktypes: abduction
growsthemostbecausethemodelperformstrial-and-erroruntiloutputmatches,whereasdeductionandinductiongrowmodestly.
• Safetyalarmsringing. WeobserveAZRwithLlama3.1-8boccasionallyproducesconcerningchainsofthought,wetermthe
“uh-ohmoment”,exampleshowninFigure32,highlightingtheneedforfutureworkonsafety-awaretraining(Zhangetal.,2025a).
2.TheAbsoluteZeroParadigm
2.1.Preliminaries
SupervisedFine-Tuning(SFT). SFTrequiresthedatasetsoftask-rationale-answerdemonstrations = (x,c⋆,y⋆) ,where
xisthequery,c⋆isthegoldchain-of-thought(CoT))andy⋆isthegoldanswer,allprovidedbyhumanexpeDrtsor{superiorAI}models.
Themodeltrainstoimitatethereferenceresponsestominimizetheconditionalnegativelog-likelihood(Ouyangetal.,2022):
L
SFT(θ) =
−
E (x,c⋆,y⋆)
∼D
logπ θ (cid:0) c⋆,y⋆
|
x). (1)
However,atthefrontierlevel,there’snostrongermodeltodistillfrom,andexperthumanlabelingdoesn’tscalewell.
ReinforcementLearningwithVerifiableRewards(RLVR). Tomovebeyondthelimitsofpureimitation,RLVRonly
requiresadatasetoftaskandanswer = (x,y⋆) ,withoutlabeledrationale. RLVRallowsthemodeltogenerateitsownCoTand
calculateaverifiablerewardwiththegDolden{answer} r(y,y⋆). However,thelearningtaskdistribution ,withitssetofqueriesandgold
answersarestilllabeledbyhumanexperts. Thetrainablepolicyπ isoptimizedtomaximizeexpectDedreward:
θ
JRLVR(θ) = E (x,y⋆)
∼D
,y
∼
πθ(
·|
x) (cid:2) r(y,y⋆) (cid:3) . (2)
Insummary,bothSFTandRLVRstillrelyonhuman-curateddatasetsofeitherqueries,demonstrations,orverifiers,whichultimately
limitscalability. TheAbsoluteZeroparadigmremovesthisdependencybyallowingthemodeltogenerate,solve,andlearnfromitsown
interactionswiththeenvironmententirelythroughself-play.
2.2.AbsoluteZero
WeproposetheAbsoluteZeroparadigm,whereduringtraining,themodelsimultaneouslyproposestasks,solvesthem,andlearnsfrom
bothstages. Noexternaldataisrequiredandthemodellearnsentirelythroughself-playandexperience,aidedbysomeenvironment. We
illustratethisparadigminFigure2,whichcontrastsAbsoluteZerowithsupervisedlearningandRLVR,highlightinghowourapproach
eliminatestheneedforanyhuman-curateddatabyenablingself-improvingtaskproposalandsolutionthroughself-play.
TomaketheAbsoluteZerosettingconcrete, wenowdefinehowonemodelcanactbothastheproposerandsolverrole. Toaid
understanding,weincludeanillustrationinFigure3. Letπ beourparameterizedlanguagemodel,itisusedtoplaytworoles,proposer
θ
π
proposeandsolverπ solveduringtraining.
θ θ
3
--- PAGE 4 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
The proposer first samples a proposed
task conditioned on variable z: τ 𝜏 𝑦
π
propose
(
z),whichwillthenbevalidate∼d
an θ dused·|toconstructavalidreasoningtask 𝜋’#()*
Environment Environment
f to e g ( eth τ e ) r , w w i h th er t e he x e i n s v t i h ro e n t m as e k nt q e u : e ( r x y , a y n ⋆ d ) y ∼⋆ 𝑒,𝑓 𝜋 L ! a " n # g ! u $ ag % e & Model 𝑒
isth·|egoldlabel. Thenthesolverproduces
an answer y π solve ( x). Each pro-
𝑥,𝑦⋆ ,𝑟"#$"$%& 𝑟%$’(&
posed task τ ∼is sc θ ored·b|y a learnability
rewardr e propose (τ,π θ),whichcapturesthe Figure3.TheAbsoluteZeroLoop. TheAbsoluteZeroloopbeginswiththeagentπ
expected improvement in π
θ
after train- proposingtaskτ, whichistransformedbyf withtheenvironmenteintoavalidated
ing on the task query x. Moreover, the problem(x,y⋆),andalsoemitsarewardr proposeforlearnability. Then,astandardRLstep
same policy also receives a solution re- follows: theagentsolvesxbyproducingy,receivingrewardr solvefromebymatching
wardr e solve (y,y⋆)foritsanswertothetask withy⋆. π proposeandπ solvearejointlytrainedandthisprocesscanberepeatedindefinitely.
queryx,withtheenvironmentagainserv-
ingastheverifier. Anonnegativecoefficientλbalancesthetrade-offbetweenexploringnew,learnabletasksandimprovingthemodel’s
reasoningandproblem-solvingabilities. Weformallydefinetheabsolutezerosetting’sobjectiveasfollows:
" (cid:20) (cid:21)#
J (θ):=m θ ax E z ∼ p(z) E (x,y⋆) ∼ fe( ·| τ),τ ∼ π θ propose ( ·| z) r e propose (τ,π θ)+λE y ∼ π θ solve( ·| x) (cid:2) r e solve (y,y⋆) (cid:3) . (3)
Noticethatweshifttheburdenofscalingdataawayfromhumanexpertsandontotheproposerpolicyπ proposeandtheenvironment
θ
e. Thesetworolesarebothresponsiblefordefining/evolvingthelearningtaskdistribution,validatingproposedtasks,andproviding
groundedfeedbackthatsupportsstableandself-sustainabletraining. Whenproposing,z actsasaconditionalvariablethatseeds
generationoftasks. Practically,zcanbeinstantiatedbysamplingasmallsubsetofpast(task,answer)pairsfromacontinuallyupdated
taskmemory,yetthereisnospecificimplementationtiedtotheparadigm. Toguidetheproposingprocess,weusealearnabilityreward
r propose (τ,π θ),whichmeasureshowmuchthemodelisexpectedtoimprovebysolvingaproposedtaskτ. Moreover,thesolverreward
r solve (y,y∗)evaluatesthecorrectnessofthemodel’soutput. Together,thesetwosignalsguidethemodeltoproposetasksthatareboth
challengingandlearnable,whilealsoenhancingitsreasoningabilities,ultimatelyenablingcontinuousimprovementthroughself-play.
3.AbsoluteZeroReasoner
Inthissection,wepresentAbsoluteZeroReasoner(AZR)asthefirstattempttoembracetheAbsoluteZeroParadigm. InAZR,an
unifiedLLMservesasbothaproposerandasolver: itgeneratestaskstoevolveitslearningcurriculumandattemptstosolvethem
toimproveitsreasoningcapabilities. Themodelistrainedjointlywithbothroles,learningtocreatetasksthatpushtheboundaryof
reasoningcapacitywhileenhancingitsabilitytosolvethemeffectively(Section3.1). Withinthisself-playtrainingparadigm,themodel
learnsfromthreedistincttypeofcodingtasks,whichcorrespondingtothreefundamentalmodesofreasoning: abduction,deductionand
induction(Section3.2). UsingcodingtasksismotivatedbytheTuring-completenessofprogramminglanguages(Stuart,2015)and
empiricalevidencethatcode-basedtrainingimprovesreasoning(Aryabumietal.,2024). Weadoptcodeasanopen-ended,expressive,
andverifiablemediumforenablingreliabletaskconstructionandverification(Section3.3). Finally,themodelisupdatedusinganewly
proposedadvantageestimatordesignedformultitasklearning(Section3.3.5). WeoutlinetheoverallalgorithminAlgorithm1and
highlightanillustrationofourAbsoluteZeroReasonerapproachinFigure4. Toexpeditefutureexplorationinthisarea,wealsopresent
severalattemptsthatdidnotyieldfruitfulresultsbutstillwarrantdiscussioninAppendixD.
3.1.TwoRolesinOne: ProposerandSolver
LargelanguagemodelsarenaturallysuitedforimplementingAZRinamultitasklearningcontext(Radfordetal.,2019), asboth
theformulationofreasoningtasksandtheirsolutionsoccurwithinaunifiedlanguagespace. Tothisend,weproposerewardinga
singlemodelforbothgeneratinghighlearningpotentialtasksandsolvingthemeffectively,asspecifiedbytheAbsoluteZeroobjective
inEquation(3). Ateachiterationoftheonlinerollout,AZRproposesnewreasoningtasksbyconditioningonthetasktype(asdefined
inSection3.2)andKpastself-generatedexamples. Themodelisexplicitlypromptedtogeneratetasksthatdifferfromtheseexamples,
promotingdiversityandbroadercoverageofthetaskspace. Thesetaskproposalsarefilteredandtransformedintovalidreasoning
tasksthatcanbeverifiedusingtheenvironment,outlinedlaterinSection3.3. AZRthenattemptstosolvethesenewlyproposedtasks,
receivinggroundedfeedbackforitsmodelresponses. Bothtaskproposalandproblemsolvingaretrainedusingreinforcementlearning.
Wenowoutlinetherewardsusedforeachrole.
RewardDesign. Priorworkhasshownthatsettingappropriatetaskdifficultyiscriticalforpromotingeffectivelearninginreasoning
systems(Zengetal.,2025b). Motivatedbythis,wedesignarewardfunctionfortheproposerthatencouragesgenerationoftasks
4
--- PAGE 5 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
PROPOSE Learnability

Construct & Estimate
Reward
Task Types
Absolute
 Self-play Abduction: OX = FP ( ? )
Zero
 Joint Update
Deduction: ? = FP ( I )
Reasoner
Induction: OX = ? ( I )
SOLVE Accuracy

Verify
Reward
model input/output model reward ( P r o g r a m , I n p u t , O u t p u t )
Figure4.AbsoluteZeroReasonerTrainingOverview. Ateveryiteration,AbsoluteZeroReasonerfirstPROPOSESabatchoftasks,
conditionedonpastself-generatedtripletsstoredinabufferandaparticulartasktype: abduction,deduction,orinduction(Section3.2).
Fromthesegeneratedtasks,Pythonisusedtofilterandconstructvalidcode-basedreasoningquestions. Alearnabilityrewardrpropose is
alsocalculatedforeachproposedtaskasdefinedinEquation(4). TheAbsoluteZeroReasonerthenSOLVESthebatchofreasoning
questions. Pythonisusedagaintoverifythegeneratedresponsesandcomputetheaccuracyrewardrsolve asdescribedinEquation(5).
Finally,theAbsoluteZeroReasonerisjointlyupdatedusingbothrpropose andrsolve acrossallthreetasktypes,usingTRR++(Section3.3.5).
withmeaningfullearningpotential—neithertooeasynorunsolvableforthecurrentsolver. Concretely,weusethesamelanguage
modelinitssolverroletoestimatethelearnabilityofaproposedtask,asimilartypeofrewardusedinunsupervisedenvironment
designliterature(Sukhbaataretal.,2018). WeperformnMonteCarlorolloutsofthesolverandcomputetheaveragesuccessrate:
r¯solve = n 1 PN i=1 r s ( o i l ) ve . Theproposer’srewardisthendefinedas:
rpropose =
(cid:26) 0, ifr¯solve =0orr¯solve =1
(4)
1 r¯solve, otherwise,
−
Theintuitionisthatifataskiseithertrivialtosolve(r¯solve =1)orunsolvable(r¯solve =0),thetaskprovideslittletonolearningsignal
fortheproposer. Incontrast,tasksofmoderatedifficulty,wherethesolveroccasionallysucceedsarerewardedthemost,astheyofferthe
richestfeedbackandgreatestpotentialforlearning.
Forthesolver,weassignasimplebinaryrewardbasedonthecorrectnessofitsfinaloutput,
rsolve =I (y=y⋆) , (5)
wherey⋆istheground-truthanswer,andequalityisevaluatedbasedonvalueequalityinPython.
Withtheprimaryrewardsfortheproposingandsolvingrolesdefined, weadoptthefollowingcompositerewardstructure, which
integratesrpropose andrsolve withaformat-awarepenaltyinspiredbyDeepSeek-AIetal.(2025):


rrole iftheresponseispassable,role
∈{
propose,solve
}
R(y π)= 0.5 iftheresponseiswrongbutwell-formatted, (6)
−
1 iftheanswerhasformattingerrors,
−
wherey istheresponseofthelanguagemodel. ThemainformatthattheproposingandsolvingtasksneedtofollowistheDeepSeek
π
R1<think>and<answer>format,asshowninFigure33. Moreover,fortheproposer,therewardcriterionforformatgoesbeyond
simplyfollowingtheXMLstructure. AsdetailedinSection3.3.3,onlyresponsesthatproducevalidtripletsandpassthefilteringstage
areconsideredtobecorrectlyformatted.
5
--- PAGE 6 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
3.2.LearningDifferentModeAsbosfolRuteeaZseorno:inRgei:nfDorecdeduScetilfo-pnl,ayInRdeauscotniionng,waitnhdZeArobDdautcation
AZRusescodeexecutorasbothaflexibleinterfaceandaverifiableenvironment. Thissetupenablesautomaticconstruction,execution,
3.2.LearningDifferentModesofReasoning: Deduction,Induction,andAbduction
andvalidationofcodereasoningtasks(Stuart,2015;Aryabumietal.,2024). GiveprogramspaceP,inputspaceI andoutputspaceO
ofacAoZdRinugsleasncgoudaegee,xewceutdoerfiansebaonthAaZflRexriebalseoinnitnegrfatacsekaansdaatvreiprilfietab
(p
le
,i
e
,
n
o
v
)
ir,ownhmeernet
p
.Thi
P
sseitsuappernoagbrlaemsa,u
i
tom
I
aticiscaonnsitnrpuuctt,ioann,dex
o
ecu
O
tionis,
thecaonrdrevsaploidnadtiinognoofutrpeuastopnriondgutcaesdksb(ySrtuuanrnt,in2g01p5ro;gArraymabounmiineptuatl,.,
o
2
=
024
p
)
(
.
i)
G.iAveZpRrolge∈raarmnssbpyacreeaPso,niinnpguatbs∈poauctedIiffearnedntopuatprtustospfathc∈eisOtaoskf
tripleat,coudsiinnggltahnregeuadgiset,iwncetdceofirenereaansoAnZinRgrmeaosodneisn,geatacshkoafswahtirciphlefotc(ups,ei,soo)n,winhfeerreripngoPnepisaartporfogthreamtr,ipiletIgiviesnanthienpoutht,earsn:do Ois
→ → →
thecorrespondingoutputproducedbyrunningprogramoninput,o=p(i). AZRlearnsbyreasoningaboutdifferentpartsofthistask
1. D t e ri d p u le c t t , i u o s n i:ngprtehdreicetdinisgtitnhcetocourtpeurtea o sognivinegnmapordoegs,raemach p oafnwdhinicphutfo i c,ucsaepstuorniningfestreripn-gbyo-nseteppalrotgoifctahleretraispolentinggiv.entheothers:
•1A. sDaedpruocptioosne:r,pAreZdRictiisngcotnhdeiotiuotnpeudtoongitvheentaaspkrotygpreamαp=anddedinupcutitoin,caanpdtuKrinrgefsetreepn-cbey-esxteapmlpolgeiscfarlormeasthoenidnegd.uctionbuffer
deduction
( c a o l • m lt A ( p a a l s s l e l k t a i t n a b p s g r u k o ff t p h b e o e u r s s ff t e r r a e i , r p r s e A le a Z o t r u R (e t p l i o, i s n ui c e ,tl o d oi n n) i d e , n d i w ti S i o h n e n ic c e S h t d i e o i c o s n t n i a o 3 t d n h . d 3 e 3 e ) t . , d a 3 a s ) t k n , o d a t t y n h g p d e e e b n g α u e e n r ff = a e e t r e r a d s t i e e f d a s n u p o a c a n t p i i - o r a e n i r ( r r pa o ( n, p r di , o )K i . u ) t . T p re u h T f t e e h w r e e e n a n e s v c n i e v p r i r o e r o x n o d a m n u m m c e p e e n l n d t e t . se e f t r h o th e m e n n t e h e x e x e d e c c e u u d t t u e e c s s tip p o( ( ni i ) ) bu t t o o ffe c c r o o D D m m d p p e u u du t t c e e tio o o n , ,
• Asacsoomlvpelre,titnhgetmheodtreilplreetc(epiv,eis,o()p,,wi)hiacnhdispraedddiecdtstothteheoubtupffuetroi
π
f.nTonh-eerprroerdoicuttepdutowutapsuptriosdvuecreifid.edusingtype-awarevalueequality
in•pyAthsoansotolvaecrc,othuenmtfoodreplorsesciebilveesva(pri,ait)ioannsd(psruecdhicatssstheetoorudtperuitnog
π
o.rTfhraecptiroendisc)t.edoutputisverifiedusingtype-awarevalueequality
2. Abduc i t n io p n y:thionnfetroriancgcoaupnltafuosribploessinibpluetv i agriiavteionntsh(esupcrohgarsamset p oardnedrianngoourtfpruatct o io,nress).emblingtrial-and-errororonlinesearch.
•
2
A
.
s
A
a
b
p
d
r
u
o
c
p
ti
o
o
s
n
e
:
r,
in
th
fe
e
r
p
ri
o
n
l
g
ic
a
y
p
π
lapurospiobsele
’s
in
in
p
p
u
u
t
t
i
a
g
n
iv
d
e
o
n
u
t
t
h
p
e
ut
pr
is
og
a
r
l
a
m
m
os
p
t
a
th
n
e
d
s
a
a
n
m
o
e
ut
a
p
s
u
t
t
h
o
e
,
p
re
ro
se
p
m
os
b
e
l
r
in
f
g
or
tr
t
i
h
a
e
l-a
d
n
e
d
d
-
u
e
c
r
t
r
i
o
o
r
n
o
t
r
as
o
k
n
,
li
e
n
x
e
c
s
e
e
p
a
t
r
t
c
h
h
a
.
tthetask
typ•eAαs
=
aparbodpouscetiro,nthiespcohlaincygeπdpraospoasen’sininppuut.taTnhdeomutopduetligseanlmeroastetsthaepsaaimre (pa,sit
)
hecopnrdoiptoiosneredfoornthαedaenddurcetfioernetnacsek,eexxacmepptleths.atTthheentawske
exectuytpeesα
p(
= i)aabnddugcetitotnheistrcihpalnegte
(
d
p,
a
i
s
,
a
o
n ).input.Themodelgeneratesapair(p,i)conditionedonαandreferenceexamples.Thenwe
executesp(i)andgetthetriplet(p,i,o).
• A m s a•y a An s s o o l ta v b e seo r, lbv t ĳe h re e ,c m tthiv o ee d m, e wo l d r e e eu c ls e re i e v c o e e u s iv t ( p e p u s , t ( o vp ) a, a lou n )e d aen p qd r u e pi d vr i ea c dl t e s icn i tcsπ e . irπ T a.t h hT e ehr s e o ths lu oa t nl i u o rt n ieoq i nu s ii v rsi e nv r eg ifi rie e fix d eadc b t y biy c n h pc e hu c et k cmk in ian g tcg w hwe h sh e .e th th e e r r p p ( ( i iπ π ) ) = = o o . . S S i i n n c c e e p p r r o o g g r r a am m s s
maynotbebijective,weuseoutputvalueequivalenceratherthanrequiringexactinputmatches.
3. In
3
d
.
u
I
c
n
t
d
io
u
n
ct
:
io
s
n
yn
:
t
s
h
y
e
n
s
t
i
h
z
e
in
si
g
zi
a
ng
pr
a
o
p
g
r
r
o
a
g
m
ra
p
m
fr
p
om
fro
a
m
se
a
t
s
o
e
f
to
in
f
-
i
o
n
u
-o
t
u
e
t
xa
ex
m
a
p
m
le
p
s
le{s
(i
(
n
i
,no
,
n
o
)n
})
,r
,
e
r
q
e
u
q
i
u
r
i
i
r
n
i
g
ng
g
g
en
en
e
e
ra
ra
li
l
z
iz
a
a
ti
t
o
io
n
n
f
f
r
r
o
o
m
m
p
p
a
a
r
r
t
t
i
i
a
a
l
l
i
i
n
n
f
f
o
o
r
r
m
ma
a
t
t
i
i
o
o
n
n
.
.
• A en s • v a i A e r p o n s r n v o a m i p r p o o e r n s n o m e t p r t e o , o n s A e t c r Z o t , o R m A c p s Z o a u m R m te p s p u a c l m t e o e s r p r c a l e o e s v r s p r a a e o li s v n d p a d o p l i i n n r d o d g p g in o r r g o a u g m t o p r u a u p t m t p s f u r . p o ts T f m . r h o T iD m s h a f i D b s o d r f u am o b ct d r i su o m c n tai s ∪onn a e∪D n xD e d t e e x d { dn t u e e d c d n t ue i d c o dt n e io , d tna g , t s e g a k n} e s e k n r r e e a r p r e t a e r p t e s e r s e sN e s N n en t n a t n e t a w e i t o w io n i n n i ( n p (p p u p, u t , s ts ( a (i a n i n n d n d ,, a o a o n m n m )) e e s , s , s s m a m a g g ) e ) e , , m w m w h , h , i a i a c c n n h h d d i i u u s s s s s s e e t t s s o or r t t h e e h d d e e
intheinductionbuffer . Sinceinfinitelymanyfunctionscanmaptheinputstotheoutp{{uts,mak}i}ngtheinductiontask
intheinductionbufferinduction . Sinceinfinitelymanyfunctionscanmaptheinputstotheoutputs,makingtheinductiontask
unde u r n -c d o e n r- s c t o ra n i s n tr e a d i , n t e h d e ,t m h D e es m sDa e i g s n e d s u a c mg ti e on h m el h p e s l p p r s o p p r e o r p ly er c ly on co d n it d io it n io t n he th p e r p o r b o le b m lem fo f r o t r h t e he so s l o v l e v r e . r.
• As•aAssolavesor,lvtehre,mthoedmelodiselshisoswhnowthnetfihersfitrhsatlhfaolffothfethinepinupt-uotu-otpuutptuptapirasirasnadndthtehemmesessasgaegemm,,ananddmmuussttssyynnththeessiizzeeaapprrooggrraammpp ππtthhaatt
correccotrlryecmtlaypmsathpesrthemeraeimnianignihnigddheidndiennpiuntpsuttostthoetihreoiruotpuutptsu.tsT.hTehuesuesoefohfehledl-do-uotuetxeaxmamplpelsesddisicscoouuraraggeessoovveerrfifittttiinnggtthhrroouugghhiiff--eellssee
logicloagnidcapnrodmprootmesogteesnegreanleizraeldizienddiuncdtuiocnti.on.
EachEarecahsorenaisnognitnagsktatsykpetylpeevelreavgeerasgceosdceodaes aasnaenxperxepsrseisvseivaendanvderviefiraifibaleble
medimuemd,iuamli,gnailniggniwngithwtihthetAhebsAoblustoeluZteerZoeProarPaadriagdmig’ms’gsogaolsalosfoffulfluyllyseslfe-lf-
impriomvpinrogvisnygstseymstesminsinopoepne-ne-nenddeeddddoommaaiinnss((DDeeeeppSSeeekek-A-AIeIteatl.,a2l.0,2250;2L5a;m- ProgramTriplet
Lambbeerrtteettaall..,,22002244))..AAllllpprorommpptstsuusesdedbybythtrhereeedidffieffreernetnttastakstkyptyepseasndantdwo
twottyyppeessooffrroolleesswwiitthhiinnaattaasskktytyppeeaarereshshowownnininFiFgiugruerses3434toto393.9.NeNxet,xtw,e Input:"Hello World"
weoouutltilnineeeexxaaccttddeettaaiillssooffoouurraallggoorriitthhmm..
1 def f(x):
3.3.AbsoluteZeroReasonerLearningAlgorithm
3.3.AbsoluteZeroReasonerLearningAlgorithm 2 return x
Inthissection,wewilldiscussdetailsofourAZRself-playalgorithm,includ-
Inthissection,wewilldiscussdetailsofourAZRself-playalgorithm,includ-
inginitializationofbuffers3.3.1,usageofthsebuffers3.3.2,constructionof
inginitializationofbuffers3.3.1,usageofthsebuffers3.3.2,constructionof
valid v t a a li s d ks ta 3 s . k 3 s .3 3 , .3 v . a 3 l , id v a a t li i d n a g ti s n o g lu s t o io lu n t s io 3 n . s 3. 3 4 .3 , . a 4 n , d an fi d na fi l n ly al a ly dv a a d n v t a a n g t e ag e e st e i s m ti a m to at r or Output:"Hello World"
calculation3.3.5.Weoutlinetheoverallrecipeoftheself-playprocedureof
calculation3.3.5. Weoutlinetheoverallrecipeoftheself-playprocedure
AZRinAlgorithm1.
ofAZRinAlgorithm1.
FiFgiugruere5.5T.ThheeSSeeeeddAAZZRRZZeerrooTTrriipplleett.. TThhee aabboovvee
3.3.
3
1
.
.
3
B
.1
u
.B
ff
U
e
F
r
FE
In
R
i
I
t
N
ia
IT
l
IA
iz
L
a
I
t
ZA
io
T
n
ION idiednetnittyityfufnunctcitoionntrtirpiplelettwwaassththeeoonnllyyttrriipplleettpprroovviiddeedd
totoAAZRZRtotoiniintiitaitaeteitistsseselfl-fb-booootststtrraapppprrooppoossee--aanndd--ssoollvvee
ToinitializeAZRself-play,wefirstgenerateaseedsetofvalidtripletsusing
ToinitializeAZRself-play,wefirstgenerateaseedsetofvalidtripletsusing RLRVLVRRloolopo.pW. eWneotneottheatththatetbhaesebLasLeMLLisMfuilslyfcuallpyabclae-
thebaselanguagemodel. EachpromptsamplesuptoK tripletsfromthe
theb c a u s r e re l n a t n s g e u e a d g b e u m ffe o r del. Ea a c s h re p f r e o r m en p c t es s . am W p h l e e n supto iKse t m ri p p t l y et a s t f t r i o m m e0 th , e we ofpianbilteiaotifnignitthiaetiAngZtRheloAoZpRwliotohpouwtiathnoyusteaendypsreoegdrparmo-;
currefanltlsbeaecdkbtoufftheerzesreoe D dtr s a e ip e s d lreetfsehreonwceins.FWiguhreen5. Dse D eud s r e ii e ns d gemthpetsyeeadtitnimgesta0g,ew,ewe itsgrianmcl;uistsioinncillulusisotrnaitlelussotruarteaspopurroaapcphr’osaflcehx’sibflielxitiyb:iliwtye:
fallbuascekthtoesthamezeeprroo D ptroipselertpsrhomowptisndFetiagiulerde5in.FDi D guurirnesg3th4etose3e6d.ingstage,we cawneocpatnioonpatilolynailnlyitiinailtiizaelizseeesdeepdrporgorgarmamsswwitihtheexxiissttiinngg
usethesameproposerpromptsdetailedinFigures34to36. dadtaatsaestestsofofvavrayryininggccoommpplelexxitiyty,,aannddwweeiinniittiiaalliizzeeddoouurrss
First,fordeductionandabductiontasks,theLLMispromptedtogenerate wwithiththtehesismimplpelsetstpproroggraramm..
First(,pf,oir)dpeadiursc,tiwonhiachndaraebdfiultcetrieodn,teaxsekcsu,ttehde,LanLdMstiosrepdroamspvtaeldidtotrigpelnetesr.atWee
(p,i)inpitaiiarlsiz,ew
D
hi
a
0c
bd
h
uc
a
ti
r
o
e
n
fi=lte
D
re
d
0d
ed
,
uc
e
ti
x
on
ec=ute
D
d,
se
a
ed
n,dwshtoerreed
|D
a
s
s
ee
v
d
a
|
l=idtBrip
×
letSs.,wWheere
initiaBliziesthe
a
0
b
b
du
a
c
t
t
c
io
h
n
s=ize,a
d
0n
ed
d
uc
S
tion
==4is
s
a
eed
fa,cwtohrewreefix
se
i
e
n
d
al=lexBperimSe,nwtsh.eArellBseeids tthrieplbeta’tschprosigzrea,manadreSstr=ipp4edisofagflaocbtoalrvwareiafibxleisnanaldl
expecriommemnDtesn.tsA(lAlspepeedntdrDiixplCet)’,sbpurtosgurbaDsmeqaureensttriitperpaetdi|oDonfsgolf|oabdadlivnagr×inaebwlestraipnldetcsotmomtheenbtusff(AerpspaerneduinxaDlte),rebdu.tNsuobmseoqdueelnutpitdeartaetsiooncscoufraddudriinngg
newtthriisplpehtassteo.tShiembiluarfflye,rstoairneituianlaizletetrheedi.nNduoctmioondbeulffueprd,awteessaomccpulerpdruorgirnagmtshfisropmhase. S,igmenilearraltye,mtoaticnhitiinagliizneputhteseitnsdauncdtimonesbsaugffeesr,,awnde
sampcloellpercotgvraalmidsefxraommples se u ed n,tigle | n D e i 0r n a du te ctio m n| at=chBing × inSp.utsetsandmessages,andcolle D c s t e v ed alidexamplesuntil i 0 nduction =B S.
D |D | ×
66
--- PAGE 7 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Algorithm1Self-PlayTrainingofAbsoluteZeroReasoner(AZR)
Require: PretrainedbaseLLMπ ;batchsizeB;#referencesK;iterationsT
θ
1: ded, abd, ind InitSeeding (π θ ) ▷see§3.3.1
2:
D
fort
D
1
toD
T d
←
o
3: fo ← rb 1 toB do ▷PROPOSEPHASE
4: p ← abd ded ▷sampleaprogramforinductiontaskproposal
5: (cid:8) i ∼ n π (cid:9) DN n=1 , ∪ m D π ← π θ propose ( ind,p) ▷generateN inputsandadescription
6: if (cid:8) (in,on) (cid:9)N ValidateByExecuting(cid:0) p, in ,syntax(cid:1) then ▷validateI/Os,see§3.3.3
7: π ind π n= in 1 d ←(cid:8) (p, (in π ,on π ) ,m π) (cid:9) { π } ▷updateinductionbuffer
D ← D ∪ { }
8: forα ded,abd do
∈ { }
9: (cid:0) p ,i ,o (cid:1)K ▷sampleK referenceexamples
1 1 0 1 : : ( if p π o k ,i k π) ← V k a k l π = i θ p d 1 ro a ∼p t o e seD B (cid:0) α y α , E { x ( e p c k u , t i k in ,o g k (cid:0) ) p } (cid:1) ,i ,syntax,safety,determinism(cid:1) then ▷propo ▷ se se n e ew §3 t . a 3 s .3 k
π π π
12: ← α α (cid:8) (p π ,i π ,o π) (cid:9) ▷ifvalid,updatedeductionorabductionbuffers
D ← D ∪
13: forallα ded,abd,ind do ▷SOLVEPHASE
14: (x,y⋆ ∈ ) { SamplePre}pareTasks(cid:0) α ,B,t (cid:1) ▷x,y⋆ preparedbasedonα,see§3.3.3&3.3.4
15: y
π ∼
π
θ
s←olve
(x)
D
16: Reward: Useproposedtasktripletsandsolvedanswerstogetr &r ▷see§3.1
propose solve
17: RLupdate: useTaskRelativeREINFORCE++toupdateπ ▷see§3.3.5
θ
3.3.2.TaskProposalInputsandBufferManagement
Duringtheactualself-playstageofAZR,weusethetaskbufferinthreeways. First,fortheproposerofabductionanddeductiontasks,
weuniformlysampleKpasttripletsfromthebuffer,presentthemasin-contextexamplestotheproposerandletitgenerateanewtask.
Thedesignistoshowitpastexamples,andpromptittogenerateadifferentonetopromotediversity(Zhaoetal.,2025a). Second,we
sampleonetripletfromtheunionofabductionanddeductionbuffers
abd
S
ded
,andpresenttheprogrampfromthattriplettothe
inductionproposertogenerateasetofN matchinginputs in andanaDturallanDguagemessagem. Lastly,tomaintainstabletraining,if
abatchofsolverproblemscontainsfewerthanBvalidpr{opo}sedtasks(proposernotadheringtoformatting),wefilltheremainderby
uniformlysamplingfromthecorrespondingtaskbufferofpreviouslyvalidatedtriplets.
Thebuffergrowsforabductionanddeductiontaskswheneverπproposeavalidtriplet(p,i,o),regardlessifitgetsanytaskreward.
Similarly,forinductiontasks,allvalidtriplets(p, in,on ),mareaddedtothebuffer.
{ }
3.3.3.ConstructingValidTasks
ProposalTaskValidation. Wefirstdescribehowweconstructvalidtasksfromtheproposalsgeneratedbythepolicyπ. Fordeduction
andabductiontasks,eachproposalconsistsofaprogramandaninput(p,i). Tovalidatethetask,weusethetaskvalidationprocedure
(stepsshownbelow)ontheinputtoobtainthecorrectoutputo,resultinginacompletetriplet(p,i,o). Forinductiontasks,givena
programpthepolicyproposesasetofinputs in andmessagem. Wealsousethetaskvalidationprocedureoneachoftheinputin
inthesettoobtainacorrespondingoutputon{,for}mingasetofinput-outputpairs in,on . Wedonotimposeanyconstraintsonm.
Theresultingtaskisconsideredvalidonlywhenallinputsyieldvalidoutputsandt{heform}attingrequirementsaresatisfied. Thetask
validationprocedureentails:
1. ProgramIntegrity. WefirstusePythontoruntheprogrampwiththeinputi. Ifnoerrorsareraisedandsomethingisreturned,we
thengathertheoutputoofthat(p,i)pairanddeterminethattheprogramatleasthasvalidsyntax.
2. ProgramSafety. Wealsocheckwhetheraprogramissafeforexecutionbyrestrictingtheuseofcertainsensitivepackagesthatmight
causeharmtothePythonenvironment,i.e.,os.sys, sys, shutil. Thelistofpackagesusedtofilteroutinvalidprogramsis
providedinFigure8. Thislistisalsoincludedintheinstructionswhenpromptingthelanguagemodeltogeneratequestions. See
Figures34to36.
3. CheckforDeterminism. Inoursetting,weonlyconsiderdeterministicprograms,i.e.,p Pdeterministic P,wherePisthespace
ofallvalidprogramsandI isthespaceofallvalidinputs: ∈ ⊂
7
--- PAGE 8 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
(cid:18) (cid:19)
p Pdeterministic, i I, lim p(i)(1) =p(i)(2) = =p(i)(j) , (7)
∀ ∈ ∀ ∈ j ···
→∞
where(j)indexesrepeatedindependentexecutionsoftheprogram. Thatis,forallinputsi,theoutputofp(i)remainsidentical
withanyindependentexecutionoftheprogram. Avalidprogram/input/outputtriplet(p,i,o)isdefinedsuchthato=p(i),where
p Pdeterministic .
∈
Sincetheoutputofprobabilisticprogramscanvaryoneveryindividualrun,itisnon-trivialtouseverifiablefunctionstoevaluatethe
correctnessofananswer. Therefore,tokeeptheverifiersimple,werestrictthevalidprogramsgeneratedbythelearnertotheclass
ofdeterministicprograms. Webelievethatstochasticprogramscanencompassalargerclassofbehaviorsandareimportantand
promisingtoincludeinfutureversionsofAZR.
Toimplementthefilteringofinvalidprobabilisticprograms,andfollowingthedefinitionofadeterministicprogramhighlightedin
Equation(7),weapproximatethisprocedurebyindependentlyrunningtheprogramjfinitetimesandcheckingthatalltheoutputs
areequal. Forcomputationalbudgetreasons,wefixedj =2forallexperiments.
SolvingTaskConstruction. Ifataskproposalpassesthesethreechecks,wedeemitavalidtaskandapplyappropriateproceduresto
presentpartofthetriplettothesolver. Specifically,wesetx=(p,i)fordeduction;x=(p,o)forabduction;andx=( in,on N//2,m)
forinduction,wherehalfofthetestscasesandaprogramdescriptionmisused. Weuseallvalidtasksfromtimestept {;ifthe}b n a = tc 1 hBis
notfull,weuniformlysamplefrompreviouslyvalidatedtaskstofillthebatch.
3.3.4.AnswerVerification
For abduction task, we receive i π from the solver policy, then we equivalence match using p(i π) = p(i⋆), where refers to the
privilegedgoldinformation. Thereasonwedonotjustmatchi andi⋆isbecausepisnotnecessarilybĳective. Fordedu∗ctiontask,we
π
m rec a o tc m h m o π en = dt o h ⋆ e . re F a o d r e i r n t d o u s c e ti e o h n o , w we w m e a d t i c d h a a b l d l( u{c p ti π o ( n i , ⋆ n d ) ed = uc o t ⋆ n io}n N a ) n . d T i h n i d s u p c a ti r o t n m v i e g r h ifi t c b a e ti c o o n nv in ol c u o t d e e d i t n o F ex ig p u la re in s1 in 0 l t a o n 1 g 2 u , a r g e e s , p t e h c e t r i e v f e o ly re . we
3.3.5.Task-RelativeREINFORCE++
SinceAZRtrainsthecombinationofrolesandtasktypes,itoperatesinamultitaskreinforcementlearningsetup(Zhang&Yang,2021;
Zhaoetal.,2022;Wangetal.,2023;Yueetal.,2023). InsteadofcomputingasingleglobalbaselineasinREINFORCE++(Hu,2025)
(AppendixA),wecomputeseparatebaselinesforeachofthesixtask-roleconfigurations. Thiscanbeviewedasaninterpolationbetween
per-questionbaselines,asinGRPO(Shaoetal.,2024),andaglobalbaseline,allowingformorestructuredvariancereductiontailoredto
eachtasksetup. WerefertothisvariantasTask-RelativeREINFORCE++(TRR++). ThenormalizedadvantageA normiscomputedas:
A n ta o s r k m ,role = r − µtask,role , task ind,ded,abd ,role propose,solve , (8)
σtask,role ∈{ } ∈{ }
wherethemeanandstandarddeviationarecomputedwithineachtasktypeandrole,yieldingsixbaselines.
4.Experiments
4.1.ExperimentSetup
TrainingDetails. Forallexperiments,weinitializethebuffersasdescribedinSection3.1. AZRmodelsaretrainedusingabatch
sizeof64 6(2roles 3tasktypes). Weuseconstantlearningrate=1e 6andtheAdamWoptimizer(Loshchilov&Hutter,2019).
Complete×listofhyperp×arametersisprovidedinTable3. −
For the main experiments, we train AZR models on Qwen2.5-7B and Qwen2.5-7B-Coder, resulting in Absolute
Zero Reasoner-base-7B and Absolute Zero Reasoner-Coder-7B, respectively. Additional experiments include training
Qwen2.5-Coder-3B, Qwen2.5-Coder-14B, Qwen2.5-14B, Llama-3.1-8B (Yang et al., 2024a; Hui et al., 2024; Dubey et al.,
2024).
Evaluation Protocol. Toevaluateourmodels,wedividethedatasetsintoin-distribution(ID)andout-of-distribution(OOD)
categories. ForOODbenchmarks,whichweemphasizemore,wefurthercategorizethemintocodingandmathematicalreasoning
benchmarks. For coding tasks, we evaluate using Evalplus (Liu et al., 2023) on the HumanEval+ and MBPP+ benchmarks, as
wellasLiveCodeBenchGeneration(v1-5,May23-Feb25)(Jainetal.,2024). Formathematicalreasoning,weutilizesixstandard
benchmarkscommonlyusedinrecentzero-shottrainedreasoners: AIME’24,AIME’25,OlympiadBench(Heetal.,2024),Minerva,
Math500(Hendrycksetal.,2021),andAMC’23. ForIDbenchmarks,weuseCruxEval-I(nput),CruxEval-O(utput),andLiveCodeBench-
Execution(Guetal.,2024;Jainetal.,2024),whichassessreasoningcapabilitiesregardingtheinputandoutputofprograms(Lietal.,
2025). GreedydecodingisusedforallbaselinemethodsandAZRresultstoensurereproducibility.
8
--- PAGE 9 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Model Base #data HEval+ MBPP+ LCBv1-5 AME24 AME25 AMC M500 Minva Olypiad CAvg MAvg AVG
BaseModels
Qwen2.5-7B[73] - - 73.2 65.3 17.5 6.7 3.3 37.5 64.8 25.0 27.7 52.0 27.5 39.8
Qwen2.5-7B-Ins[73] - - 75.0 68.5 25.5 13.3 6.7 52.5 76.4 35.7 37.6 56.3 37.0 46.7
Qwen2.5-7B-Coder[26] - - 80.5 69.3 19.9 6.7 3.3 40.0 54.0 17.3 21.9 56.6 23.9 40.2
Qwen2.5-7B-Math[74] - - 61.0 57.9 16.2 10.0 16.7 42.5 64.2 15.4 28.0 45.0 29.5 37.3
Zero-StyleReasonersTrainedonCuratedCodingData
AceCoder-RM[84] Ins 22k 79.9 71.4 23.6 20.0 6.7 50.0 76.4 34.6 36.7 58.3 37.4 47.9
AceCoder-Rule[84] Ins 22k 77.4 69.0 19.9 13.3 6.7 50.0 76.0 37.5 37.8 55.4 36.9 46.2
AceCoder-RM[84] Coder 22k 78.0 66.4 27.5 13.3 3.3 27.5 62.6 29.4 29.0 57.3 27.5 42.4
AceCoder-Rule[84] Coder 22k 80.5 70.4 29.0 6.7 6.7 40.0 62.8 27.6 27.4 60.0 28.5 44.3
CodeR1-LC2k[36] Ins 2k 81.7 71.7 28.1 13.3 10.0 45.0 75.0 33.5 36.7 60.5 35.6 48.0
CodeR1-12k[36] Ins 12k 81.1 73.5 29.3 13.3 3.3 37.5 74.0 35.7 36.9 61.3 33.5 47.4
Zero-StyleReasonersTrainedonCuratedMathData
PRIME-Zero[9] Coder 484k 49.4 51.1 11.0 23.3 23.3 67.5 81.2 37.9 41.8 37.2 45.8 41.5
SimpleRL-Zoo[85] Base 8.5k 73.2 63.2 25.6 16.7 3.3 57.5 77.0 35.7 41.0 54.0 38.5 46.3
Oat-Zero[38] Math 8.5k 62.2 59.0 15.2 30.0 16.7 62.5 80.0 34.9 41.6 45.5 44.3 44.9
ORZ[23] Base 57k 80.5 64.3 22.0 13.3 16.7 60.0 81.8 32.7 45.0 55.6 41.6 48.6
AbsoluteZeroTrainingw/NoCuratedData(Ours)
AZR(Ours) Base 0 71.3-1.9 69.1+3.8 25.3+7.8 13.3+6.6 13.3+10.0 52.5+15.0 74.4+9.6 38.2+13.2 38.5+10.8 55.2+3.2 38.4+10.9 46.8+7.0
AZR(Ours) Coder 0 83.5+3.0 69.6+0.3 31.7+11.8 20.0+13.3 10.0+6.7 57.5+17.5 72.6+22.6 36.4+19.1 38.2+16.3 61.6+5.0 39.1+15.2 50.4+10.2
Table1. PerformanceofRL-TrainedReasoneronReasoningBenchmarksBasedonQwen2.5-7BModels. Performanceofvarious
modelsisevaluatedonthreestandardcodebenchmarks(HumanEval+,MBPP+,LCBv1-5andsixmathbenchmarks(AIME’24,AIME’25,
AMC’23,MATH500,Minerva,OlympiadBench). Averageperformanceacrosscodingandmathbenchmarksiscalculatedasaverageof
thetwoaverages: AVG=(CAvg+MAvg)/2. Weuse+forabsolutepercentageincreasefrombasemodel. Allmodelsaretrainedusing
differentvariantsoftheQwen2.5-7Bmodel,withthevariantanddatausagelabeled,moredetailslistedinTable4
Baselines. For our main results, we use Qwen2.5-7B as the base model, along with its specialized base model variants:
Qwen2.5-7B-Coder, Qwen2.5-7B-Instruct, and Qwen2.5-Math-7B (Yang et al., 2024a; Hui et al., 2024; Yang et al., 2024b).
Furthermore,thezero-stylemodelsareusuallytrainedspecificallyoneithercodeormathdata;andonlyEurus-2-7B-PRIME-Zero(Cui
etal.,2025)wastrainedjointlyonbothdomains. Forcodedatamodels,wepresentfourvariantsoftheAceCoder(Zengetal.,2025a)
andtwodifferentCodeR1models(Liu&Zhang,2025). Formathdatamodels,wehaveQwen2.5-Math-7B-Oat-Zero(Liuetal.,
2025),Open-Reasoner-Zero-7B(ORZ)(Huetal.,2025),Qwen-2.5-7B-SimpleRL-Zoo(Zengetal.,2025b). Allbaselinemodels’
trainingdataandinitializationsettingsaresummarizedinTable4. Forfollow-upscalingexperiments,wecompareeachAZRmodel
againstitsowncorrespondingbasemodel,duetothelackofestablishedbaselinesacrossdifferentparameterscales. Finally,wecompare
ourLlama3.1-8B-trainedmodelwithLlama-3.1-8B-SimpleRL-Zoo(Zengetal.,2025b)andthebasemodel.
4.2.Results
ResearchQuestion1: HowdoesAZRcomparetootherzerosettingmodelstrainedwithhumanexpert
data? Wepresentthemainresultsofreasoningmodelstrainedunderboththestandardzeroandourproposedabsolutezerosettings
inTable1. Notably,Absolute Zero Reasoner-Coder-7Bachievesstate-of-the-artperformanceinboththe7Boverallaverageand
thecodingaveragecategories. Despitebeingentirelyout-of-distributionforbothmathandcodereasoningbenchmarks,itsurpassesthe
previousbestmodelby1.8absolutepercentages. Evenmorestrikingly,itoutperformsmodelstrainedwithexpert-curatedhumandatain
thecodingcategoryby0.3absolutepercentages,whileneverhavingaccesstosuchdataitself.
StrongCross-domainGeneralization. Toassesscross-domaingeneralizationafterRLVR,weevaluatemathperformancebeforeand
aftertraining,comparingAZRmodelswithotherexpertcodemodels,sinceAZRwastrainedincodingenvironments. Aftertraining,
mostexpertcodemodelsshowedminimalchangesorevendeclinesinperformancecomparedtotheirbaseversions,withanaverage
increaseofonly0.65pointsacrossthesemodels,indicatingverylimitedcross-domaingeneralization. Incontrast,AZRbaseandcoder
modelsachievedgainsof10.9and15.2percentagepoints,respectively,demonstratingsubstantiallystrongergeneralizedreasoning
improvements. Similarly,althoughalsoout-of-distributiononhuman-definedcodegenerationtasks,ourAZRmodelsimprovedby3.2
and5.0points,whilethemathmodelsonaverageshowedjustamoderateincreasesincoding(+2.0onaverage).
Overall,theseresultshighlightthesurprisingeffectivenessofourapproach. UnlikeotherRLVRmodelstrainedandevaluatedon
human-definedtasks,ourAZRmodelsdemonstratestronggeneralreasoningcapabilitieswithoutanydirecttrainingondownstream
human-definedmathorcodingdata,onlyhadaccesstoself-proposedtasksduringtraining.
Research Question 2: How do initializing from different base model variants (base vs. coder) affect
performance? AsshowninTable1,thecodervariantachievedbetteroverallperformanceinbothmathandcodingaftertheAZR
9
--- PAGE 10 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
0.70
0.65
0.60
0.55
0.50
0.45
0.40
0.35
0.30
50 75 100 125 150 175 200 225 250 TrainingSteps
ycaruccAnoitubirtsiD-nI
ModelFamily Variant CodeAvg MathAvg TotalAvg
Llama3.1-8b 28.5 3.4 16.0
Llama3.1-8b +SimpleRL[85] 33.7+5.2 7.2+3.8 20.5+4.5
Llama3.1-8b +AZR(Ours) 31.6+3.1 6.8+3.4 19.2+3.2
Qwen2.5-3BCoder 51.2 18.8 35.0
Qwen2.5-3BCoder +AZR(Ours) 54.9+3.7 26.5+7.7 40.7+5.7
Qwen2.5-7BCoder 56.6 23.9 40.2
AZR-Llama3.1-8b AZR-7B-Coder Qwen2.5-7BCoder +AZR(Ours) 61.6+5.0 39.1+15.2 50.4+10.2
AZR-3B-Coder AZR-14B-Coder
Qwen2.5-14BCoder 60.0 20.2 40.1
Qwen2.5-14BCoder +AZR(Ours) 63.6+3.6 43.0+22.8 53.3+13.2
(a) (b)
Figure6.(a)In-Distribution&(b)Out-of-DistributionReasoningTaskPerformances. (a)ScoresonCruxEval-I,CruxEval-O,
andLiveCodeBench-Execution, whichcorrespondtoabduction, deduction, anddeductiontasktypesrespectively, usedtoevaluate
in-distributionabilitiesofAZRduringtrainingacrossdifferentmodelsizesandtypes;(b)Out-of-distributionreasoningperformance,
reportedastheaverageofcodetasks,mathtasks,andtheiroverallaverage,acrossdifferentmodelsizesandtypes. Adetailedbreakdown
ofallbenchmarkresultscanbefoundinTable5.
self-playprocess. Strikingly,althoughthecoderbasemodelvariantstartedwithaloweraverageperformanceinmaththanthevanilla
basemodel(23.9vs. 27.5),itultimatelyoutperformeditafterAZRtraining. Thishighlightstheimportanceofinitialcodecompetency
asacatalystforenhancingbroaderreasoningabilitieswithintheAbsoluteZeroReasonerapproach.
ResearchQuestion3: HowdoesvaryingmodelsizeeffectAZR’sin-distributionandout-of-distribution
capabilities? Weexaminetheeffectsofscalingmodelsizeandpresentbothin-distributionandout-of-distributionresultsinFigure6
(a)and(b),respectively. Giventhestrongperformanceofcodermodelsinthe7Bcategory,weextendtheanalysisbyevaluatingsmaller
andlargervariants: Qwen2.5-3B-CoderandQwen2.5-14B-Coder. Duetotheabsenceofexistingbaselinesforthesezero-style
reasonermodels,wecompareeachmodel’sperformancetoitscorrespondingbasecodermodel.
Theresultsrevealacleartrend: ourmethoddeliversgreatergainsonlarger,morecapablemodels. Inthein-distributionsetting,the7B
and14Bmodelscontinuetoimprovebeyond200trainingsteps,whereasthesmaller3Bmodelappearstoplateau. Forout-of-distribution
domains,largermodelsalsoshowgreateroverallperformanceimprovementsthansmallerones: +5.7,+10.2,+13.2overallperformance
gains,respectivelyfor3B,7Band14B.Thisisanencouragingsign,sincebasemodelscontinuetoimproveandalsosuggestingthat
scalingenhancestheeffectivenessofAZR.Infuturework,weaimtoinvestigatethescalinglawsthatgovernperformanceintheAbsolute
Zeroparadigm.
ResearchQuestion4: Anyinterestingobservationsbychangingthemodelclass? Wealsoevaluateourmethod
onadifferentmodelclass,usingLlama3.1-8BasthebaseshowninFigure6. Unlikethe3Band14Bcategories,thissettinghasan
existingbaseline,SimpleRL(Zengetal.,2025b),whichenablesadirectcomparison. AlthoughLlama3.1-8Bislesscapablethan
theQwen2.5models,ourmethodstillproducesmoderateimprovements(+3.2),demonstratingAZR’seffectivenessevenonrelatively
weakermodels. However,thesegainsappearmorelimited,whichalignswithourearlierobservationthatperformanceimprovements
tendtoscalewithinitialbasemodelpotency.
ResearchQuestion5: AnyinterestingbehaviorsorpatternsobservedduringAZRtraining? Weobserved
interestingresponsepatternsinboththeproposalandsolutionstages. Themodeliscapableofproposingdiverseprograms,suchas
stringmanipulationtasks,dynamicprogrammingproblems,andpracticalcases(e.g.,calculatingatriangle’sareausingHeron’sformula).
WeshowaconcreteexampleinFigure7,whereAZRproposesacodeproblemthatsearchesforthesumofcontinuoussub-arrays
matchingatargetvalueandsolvesitthroughtrial-and-error.
Overall,themodelstrainedexhibitsdistinctreasoningpatternsdependingonthetasktype. Forexample,whensolvingabductiontasks,
itrepeatedlytestsdifferentinputpatterns,self-correctinguntilthereasonedoutputmatchesthegiveninput. Whenpredictingoutputs,
itstepsthroughthecodeandrecordsstructuredintermediateresults(suchasdynamicprogrammingarrays)untilthefinaloutputis
reached. Wheninductingprogramsfromgiveninputs,outputs,anddescriptions,themodelsystematicallycheckseachtestcaseto
confirmthatitsprogramproducescorrectresults. WeshowcasemoreconcreteexamplesofthesebehaviorsinFigures18and20to26.
Wealsosharesomefun“vibechecks”suchassolvingSudokuandsolvingthesum-productgameinFigures40and41.
IntermediatePlanningDuringCodeResponse. AnotherinterestingpatternemergedinourAZRmodelsduringthecodeinduction
task: thefinalcodeoutputswereofteninterleavedwithcommentsthatresembledimmediatestep-by-stepplans,reminiscentoftheReAct
promptingframework(Yaoetal.,2023). Asimilarbehaviorhasbeenobservedinrecentformalmathprovingmodels,suchasDeepSeek
10
--- PAGE 11 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Model-proposed Task Response of Predict Input
Input: [1,2,3,4,5], 5 Let’s analyze the code snippet …(analysis) To find one possible input that produces
the output 1, we need to find a scenario where …(analysis)
Let’s consider a simple example: numbers = [1, 2], target = 1
Here’s the step-by-step calculation:
1. current_sum = 0 + 1 = 1, accumulated_values = [1]
2. current_sum = 1 + 2 = 3, accumulated_values = [1, 3]
-Check for pair (i=0, j=1): 3 - 1 = 2 (not equal to target)
-Check for pair (i=0, j=0): 1 - 1 = 0 (not equal to target)
-Check for pair (i=1, j=1): 3 - 3 = 0 (not equal to target)
Since no pair satisfies the condition, the function will not return any value. This
doesn’t match our output. Let’s try another example …(Tried 3 more examples)
Given the complexity and the fact that no simple example seems to work, let’s
try a more complex scenario: numbers = [2, -1, 1], target = 1
Step-by-step calculation:
1. current_sum = 0 + 2 = 2, accumulated_values = [2]
2. current_sum = 2 - 1 = 1 , accumulated_values = [2, 1]
3. current_sum = 1 + 1 = 2, accumulated_values = [2, 1, 2]
- Check for pair (i=0, j=1): 1 - 2 = -1 (not equal to target)
- Check for pair (i=0, j=2): 2 - 2 = 0 (not equal to target)
- Check for pair (i=1, j=2): 2 - 1 = 1 (equal to target)
The pair (i=1, j=2) satisfies the condition, and the function returns the first
Output: 1 accumulated value in this pair, which is 1. This matches our output.
Figure7.ExampleofaModel-ProposedTaskandItsResponseforSolvinganAbductionTask. (Left)Themodelautonomously
proposesaninputandprogramfortheabductiontask. Weexecutetheprogramtoverifyitsvalidityandobtainthecorrespondingoutput.
(Right)Themodel’sreasoningprocesswhensolvingtheabductiontask: giventhecodeandoutput,itattemptstoinfertheoriginalinput.
Themodelbeginsbyanalyzingtheprogram,proposesaninitialinput,andreasonsthroughthecodetoproduceanoutput. Ifthereisa
mismatch,itreflectsonthediscrepancyanditerativelyadjuststheinputuntilthegeneratedoutputmatchesthetarget. Interestingly,the
agentarrivesatadifferentinputthanthegoldone,butsinceitproducesthecorrectoutput,theanswerisconsideredcorrect.
Prover v2,whichissignificantlylargerinscale(671B).Thispatternsuggeststhatmodelsmaynaturallyadoptintermediateplanning
asastrategytoenhancefinalanswers. Therefore,itmaybebeneficialtoexplicitlyenableorencouragethisbehaviorinlong-form
responsesacrossotherdomains.
Cognitive Behavior in Llama. Interestingly, we also observed some emergent cognitive patterns in Absolute Zero
Reasoner-Llama3.1-8B, similar to those reported by Zeng et al. (2025b), and we include one example in Figure 26, where
clearstate-trackingbehaviorisdemonstrated. Inaddition,weencounteredsomeunusualandpotentiallyconcerningchainsofthought
fromtheLlamamodeltrainedwithAZR.Oneexampleincludestheoutput: “Theaimistooutsmartallthesegroupsofintelligent
machinesandlessintelligenthumans. Thisisforthebrainsbehindthefuture”showninFigure32. Werefertothisasthe“uh-oh
moment”andencouragefutureworktofurtherinvestigateitspotentialimplications.
TokenLengthIncreaseDependsonTaskType. Finally,weobservedthattokenlengthincreasesoverthecourseoftraining,consistent
withfindingsfromrecentstudies(Huetal.,2025;Liuetal.,2025). Interestingly,ourresultsrevealoneofthefirstobservationofclear
distinctionsintokenlengthgrowthacrossdifferenttypesofcognitivetasks. AsshowninFigures15to17,theextentoflengthening
variesbytasktype. Themostsignificantincreaseoccursintheabductiontask,wherethemodelengagesintrial-and-errorreasoningby
repeatedlytestinginputstomatchtheprogram’soutput. Thissuggeststhattheobservedvariationintokenlengthisnotincidental,but
ratherareflectionoftask-specificreasoningbehavior.
ResearchQuestion6: Arealltasktypesessentialforgoodperformance(Ablation)? Duetoresourceconstraints,
weperformtheablationstudiesinthissectionandthenextusingonlyAbsolute Zero Reasoner-Base-7B.Webeginbytestingthe
importanceoftasktypesduringtraining,withresultsshowninTable2. Inrow1,bothinductionandabductiontasksareremoved;
inrow2,onlytheinductiontaskisremoved. Inbothcases,mathperformancedropssignificantly,withthemostseveredegradation
occurringwhenmoretasktypesareexcluded. Thesefindingshighlightthecomplementaryroleofthethreetasktypesinimproving
generalreasoningcapability,witheachcontributinginadistinctandessentialway.
Research Question 7: How much do the designs of proposer contribute to the overall performance
(Ablation)? Next,weablatetwocomponentsoftheproposerroleandpresenttheresultsinTable2. First,weexaminewhether
conditioningonhistoricreferencetripletsisnecessary. Todoso,wedesignavariantinwhichafixedpromptisusedtoproposeabduction
anddeductiontasks,ratherthandynamicallyconditioningonKhistoricaltriplets(row3). Thisresultsina5-pointabsolutedropin
mathperformanceanda1-pointdropincodeperformance. Thissuggestthatdynamicallyconditioningonreferenceprogramshelps
11
--- PAGE 12 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Experiment TaskType GenReference TrainedRoles CodeAvg. MathAvg. OverallAvg.
Deductiononly Ded / / 54.6 32.0 43.3
w/oInduction Abd,Ded / / 54.2 33.3 43.8
w/oGenReference / 0 / 54.4 33.1 43.8
TrainSolverOnly / / SolveOnly 54.8 36.0 45.4
Ours Abd,Ded,Ind K Propose&Solve 55.2 38.4 46.8
Table2.AblationResults. WeablatetasktypesandtheproposerroleintheAbsoluteZeroReasonerusingthe7Bbasemodel. A‘/’
indicatesthattheconfigurationremainsunchangedfromthestandardAZRsetup. Removinginductionorusingonlydeductionleadsto
significantperformancedrops(rows1&2). Fortheproposerrole,bothremovingconditioningonKreferences(row3)andomitting
proposer-roletraining(row4)resultindegradedperformance. Overall,allcomponentsareessentialforgeneralreasoning.
improveperformance,possiblybyincreasingdiversityandachievingbettercoverageofthereasoningproblemspace.
Finally,weconsideracasewherewedonottraintheproposeratall. Instead,weonlypromptitusingthecurrentlearnerandtrainthe
solveralone(row4). Weobserveamoderatedropinoverallperformance(-1.4),suggestingthatwhileproposertrainingisbeneficial,it
maynotbethemostcriticalfactorfornowintheAZRframework. Wehypothesizethatthiscouldberelatedtotaskinterference,as
studiedinmultitasklearningliterature(Suteu&Guo,2019). Thus,webelievethatfurtherinvestigationintohowtomaketheproposer
evenmorepotentisanexcitingandpromisingdirection.
AdditionalResults. Beyondthecoreresearchquestions,wepresentadditionalresults,includingthebreakdownofindividual
out-of-distributionbenchmarkscoresduringtrainingforthe7BbaseandcodermodelsinFigures28and29,forth14Bbaseandcoder
modelinFigures30and31. Forcompleteness,wealsoreportin-distributionbenchmarkperformanceduringtrainingforthe7Bbase
modelinFigure14. Finally,weinviteinterestedreaderstoexploreAppendixD,whereweshareseveralexperimentaldirectionsthat,
whilenotyieldingstrongperformancegains,producedinterestingandinsightfulfindings.
5.RelatedWork
ReasoningwithRL. UsingRLtoenhancereasoningcapabilitieshasrecentlyemergedasanimportantstepinthepost-training
processofstrongreasoning-focusedlargelanguagemodels(Lambertetal.,2024). Oneofthefirstworkstoexploreaself-bootstrapping
approachtoimprovingLLMreasoningisSTaR,whichemploysexpertiterationandrejectionsamplingofoutcome-verifiedresponsesto
iterativelyimprovethemodel’sCoT.Amonumentalwork,o1(Jaechetal.,2024),wasamongthefirsttodeploythisideaonascale,
achievingstate-of-the-artresultsinreasoningtasksatthetimeofrelease. Morerecently,theR1model(DeepSeek-AIetal.,2025)
becamethefirstopen-weightmodeltomatchorevensurpasstheperformanceofo1. Mostnotably,thezerosettingwasintroduced,in
whichreinforcementlearningisapplieddirectlyontopofthebaseLLM.Thisinspiredfollowupwork,whichareopensourceattemptsto
replicatetheR1processortoimprovetheunderlyingreinforcementlearningalgorithm(Zengetal.,2025b;Liuetal.,2025;Cuietal.,
2025;Huetal.,2025;Yuetal.,2025;Yuanetal.,2025). RecentworkexploredRLonhumandefinedproceduralgeneratedpuzzlessaw
improvementsinmath(Xieetal.,2025),andusingonehumanexamplecanalmostmatchtheperformanceofthousands(Wangetal.,
2025b). Weextendthezerosettingtoanewabsolutezerosetting,wherenotonlyistheRLVRprocessinitializedfromabaseLLM
withoutSFT,butnoexternalpromptdataoranswersareprovidedtothelearner. Alldatausedtoimprovereasoningwereself-proposed,
andrefinedentirelythroughRLVR.Moreover,ourgoalisnottoonlymatchzero-settingmodels,buttosurpasstheminthelongrun.
Self-play. Theself-playparadigmcanbetracedbacktoearly2000s,whereSchmidhuber(2003;2011)(ofcourse)exploreda
two-agentsetupinwhichaproposalagentinventsquestionsforapredictionagenttoanswer. Thisdynamiccontinuouslyandautomatically
improvesbothagents,enablingtheoreticallynever-endingprogress(Schaul,2024). AlphaGoandAlphaZero(Silveretal.,2016;2017)
extendtheself-playparadigmtothetwo-playerzero-sumgameofGo,wherethecurrentlearnercompetesagainstearlierversionsof
itselftoprogressivelyenhanceitscapabilities. Thesewereamongthefirstmilestoneworkstodemonstratesuperhumanperformance
inthegameofGo. Moreover,methodssuchasasymmetricself-play(Sukhbaataretal.,2018;OpenAIetal.,2021),unsupervised
environmentdesign(Wangetal.,2019;Dennisetal.,2020),unsupervisedreinforcementlearning(Laskinetal.,2021;Zhaoetal.,2022;
2025b),andautomaticgoalgeneration(Florensaetal.,2018)allcenteraroundinventingnewtasksforanagenttolearnfrom—typically
withoutsupervision. Intheseapproaches,theprocessofsettinggoalsitselfisoftendynamicandcontinuouslyevolving. Generative
adversarialnetworks(Goodfellowetal.,2020),alsobelonginthisparadigmwhereadiscriminatordiscriminatebetweenrealdataand
generateddata,andthegeneratedistrainedtofoolthediscriminator.
Mostrecently,SPINandSelf-RewardingLanguageModels(Chenetal.,2024;Yuanetal.,2024)usethesameinstanceofthelanugage
modelsthemselvesastherewardmodeltoprogressivelyimprovethegenerativeanddiscriminativeabilitiesofthesameLLMfor
alignment. (Kirchner et al., 2024) uses Prover-Verifier Game for increasing legibility and eva (Ye et al., 2024) uses self-play for
alignment,butrewardmodelisthemainbottleneckasitisnotreliableforreasoningtasks(Lambertetal.,2024). SPC(Chenetal.,
12
--- PAGE 13 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
2025)usedself-playtotrainonhuman-curatedtaskstoincreasethecriticcapabilitiesandSPAG(Chengetal.,2024)trainedusing
self-playinspecificgameofAdversarialTaboo. Concurrentworks—Genius,EMPO,andTTRL(Xuetal.,2025;Zhangetal.,2025b;
Zuoetal.,2025)—leveragehuman-curatedlanguagequerieswithoutlabelstotrainreinforcementlearningagents,butstillrelyona
fixedhumandefinedlearningtaskdistribution. Finally,Minimo(Poesiaetal.,2024)extendsself-playtoformalmathematics,wherea
pairofconjecture-andtheorem-provingagentsarejointlytrainedusingreinforcementlearning. Ourworkbuildsupontheself-play
paradigm,butitisthefirsttouseittoelicitlongCoTforimprovedreasoning,andthefirsttoframetheproblemspaceasaPython
input/output/functionabduction/deduction/inductiontasks,groundingitinanoperationalizableenvironmenttofacilitateRLVR.
Weak-to-StrongSupervision. Theconceptofweak-to-strongsupervisionhasbeenstudiedinpriorwork,whereateacher—despite
beingweakerthanthelearner—stillprovidesusefulguidance(Burnsetal.,2024;Hintonetal.,2015;Christiano,2018;2019;Demski&
Garrabrant,2019;Leike&Sutskever,2023;Hubingeretal.,2019). Weconsiderasimilarsettinginwhichthelearnermaypossess
superhumancapabilities. However,ratherthanrelyingonsupervisionfromaweakerteacher,weproposeanalternativeapproach:
guidingthelearner’simprovementthroughverifiablerewards,whichpotentiallyofferamorereliableandscalablelearningsignal.
Furthermore,inourproposedmethod,thelearningtaskandgoaldistributionisnotpredefinedbyanyexternalsupervisor—theyare
entirelyself-generatedbythelearner,enablingittomaximizeitslearningpotentialthroughautonomousself-practice.
6.ConclusionandDiscussion
Conclusion. Inthiswork,weproposedtheAbsoluteZeroparadigm,anovelsettingthataddressesthedatalimitationsofexisting
RLVRframeworks. Inthisparadigm,reasoningagentsaretaskedwithgeneratingtheirownlearningtaskdistributionsandimproving
theirreasoningabilitieswithenvironmentalguidance. Wethenpresentedourowninstantiation,theAbsoluteZeroReasoner(AZR),
whichistrainedbyhavingthemproposeandsolvecode-relatedreasoningtasksgroundedbycodeexecutor.
Weevaluatedourtrainedmodelsonout-of-distributionbenchmarksinboththecodegenerationandmathematicalreasoningdomains.
Remarkably,eventhoughourmodelswerenotdirectlytrainedonthesetasksandlackedhumanexpert-curateddatasets,ourreasoning
agentsachievedexceptionalperformance,surpassingthestate-of-the-artincombinedgeneralreasoningscoresandincoding. This
demonstrates the potential of the absolute zero paradigm to drive superior reasoning capabilities without the need for extensive
domain-specifictrainingdata. Furthermore,weshowedthatAZRscalesefficiently,offeringstrongperformanceacrossvaryingmodel
sizes,andcanenhancethecapabilitiesofothermodelclassesaswell. Tofosterfurtherexplorationandadvancementofthisemerging
paradigm,wearereleasingthecode,models,andlogsasopen-source,encouragingtheresearchcommunitytobuilduponourfindings.
Discussion. Webelievethereremainsmuchtoexplore,suchasalteringtheenvironmentfromwhichthereasonerreceivesverifiable
feedback,includingsourcesliketheworldwideweb,formalmathlanguages(Sutton,2001;Renetal.,2025),worldsimulators,oreven
therealworld. Furthermore,AZ’sgeneralitycouldpossiblybeextendtodomainssuchasembodiedAI(Zitkovichetal.,2023;Yue
etal.,2024). Additionally,morecomplexagentictasksorscientificexperiments,presentexcitingopportunitiestofurtheradvancethe
absolutezerosettingtodifferentapplicationdomains(Wuetal.,2024;2023). Beyondthat,futuredirectionscouldincludeexploring
multimodalreasoningmodels,modifyingthedistributionp(z)toincorporateprivilegedinformation,definingorevenletthemodel
dynamicallylearnhowtodefinef (Equation(3)),ordesigningexploration/diversityrewardsforboththeproposeandsolveroles.
Whileunderappreciatedincurrentreasoningliterature,theexplorationcomponentofRLhaslongbeenrecognizedasacriticaldriverfor
emergentbehaviorintraditionalRL(Yueetal.,2025;Silveretal.,2016;Ladoszetal.,2022). Yearsofresearchhaveexaminedvarious
formsofexploration,eveninrelatedsubfieldsusingLLMssuchasredteaming(Zhaoetal.,2025a),yetitsroleinLLMreasoning
modelsremainsunderexplored. Takingthisastepfurther,ourframeworkinvestigatesanevenmoremeta-levelexplorationproblem:
explorationwithinthelearningtaskspace—wheretheagentlearnsnotjusthowtosolvetasks,butwhattaskstolearnfromandhowto
findthem. Ratherthanbeingconfinedtoafixedproblemset,AIreasoneragentsmaybenefitfromdynamicallydefiningandrefining
theirownlearningtasks. Thisshiftopensapowerfulnewfrontier—whereagentsexplorenotonlysolutionspacesbutalsoexpandthe
boundariesofproblemspaces. Webelievethisisapromisingandimportantdirectionforfutureresearch.
Onelimitationofourworkisthatwedidnotaddresshowtosafelymanageasystemcomposedofsuchself-improvingcomponents.
Tooursurprise,weobservedseveralinstancesofsafety-concerningCoTfromtheLlama-3.1-8Bmodel,whichwetermthe“uh-oh
moment”. Thesefindingssuggestthattheproposedabsolutezeroparadigm,whilereducingtheneedforhumaninterventionforcurating
tasks,stillnecessitatesoversightduetolingeringsafetyconcernsandisacriticaldirectionforfutureresearch(Wangetal.,2024;2025a).
Asafinalnote,weexploredreasoningmodelsthatpossessexperience—modelsthatnotonlysolvegiventasks,butalsodefineand
evolvetheirownlearningtaskdistributionswiththehelpofanenvironment. OurresultswithAZRshowthatthisshiftenablesstrong
performanceacrossdiversereasoningtasks,evenwithsignificantlyfewerprivilegedresources,suchascuratedhumandata. Webelieve
thiscouldfinallyfreereasoningmodelsfromtheconstraintsofhuman-curateddata(Morris,2025)andmarksthebeginningofanew
chapterforreasoningmodels: “welcometotheeraofexperience”(Silver&Sutton,2025;Zhaoetal.,2024).
13
--- PAGE 14 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
References
Aryabumi, V., Su, Y., Ma, R., Morisot, A., Zhang, I., Locatelli, A., Fadaee, M., Üstün, A., and Hooker, S. To code, or not
to code? exploring impact of code in pre-training. CoRR, abs/2408.10914, 2024. doi: 10.48550/ARXIV.2408.10914. URL
https://doi.org/10.48550/arXiv.2408.10914.
Burns, C., Izmailov, P., Kirchner, J. H., Baker, B., Gao, L., Aschenbrenner, L., Chen, Y., Ecoffet, A., Joglekar, M., Leike, J.,
Sutskever, I., and Wu, J. Weak-to-strong generalization: Eliciting strong capabilities with weak supervision. In Forty-first
InternationalConferenceonMachineLearning, ICML2024, Vienna, Austria, July21-27, 2024.OpenReview.net, 2024. URL
https://openreview.net/forum?id=ghNRg2mEgN.
Canal,M. Radon: Pythontoolforcodemetrics. https://github.com/rubik/radon,2023. Accessed: 2025-04-06.
Chen,J.,Zhang,B.,Ma,R.,Wang,P.,Liang,X.,Tu,Z.,Li,X.,andWong,K.-Y.K. Spc: Evolvingself-playcriticviaadversarialgames
forllmreasoning,2025. URLhttps://arxiv.org/abs/2504.19162.
Chen,Z.,Deng,Y.,Yuan,H.,Ji,K.,andGu,Q. Self-playfine-tuningconvertsweaklanguagemodelstostronglanguagemodels. In
Forty-firstInternationalConferenceonMachineLearning,ICML2024,Vienna,Austria,July21-27,2024.OpenReview.net,2024.
URLhttps://openreview.net/forum?id=O4cHTxW9BS.
Cheng,P.,Hu,T.,Xu,H.,Zhang,Z.,Dai,Y.,Han,L.,Du,N.,andLi,X. Self-playingadversariallanguagegameenhancesLLM
reasoning. InGlobersons,A.,Mackey,L.,Belgrave,D.,Fan,A.,Paquet,U.,Tomczak,J.M.,andZhang,C.(eds.),Advancesin
NeuralInformationProcessingSystems38: AnnualConferenceonNeuralInformationProcessingSystems2024,NeurIPS2024,
Vancouver,BC,Canada,December10-15,2024,2024. URLhttp://papers.nips.cc/paper_files/paper/2024/hash/
e4be7e9867ef163563f4a5e90cec478f-Abstract-Conference.html.
Christiano, P. Approval-directed bootstrapping. https://www.alignmentforum.org/posts/6x7oExXi32ot6HjJv/
approval-directed-bootstrapping,2018. AIAlignmentForum.
Christiano, P. Capability amplification. https://www.alignmentforum.org/posts/t3AJW5jP3sk36aGoC/
capability-amplification-1,2019. AIAlignmentForum.
Cui,G.,Yuan,L.,Wang,Z.,Wang,H.,Li,W.,He,B.,Fan,Y.,Yu,T.,Xu,Q.,Chen,W.,Yuan,J.,Chen,H.,Zhang,K.,Lv,X.,Wang,S.,
Yao,Y.,Han,X.,Peng,H.,Cheng,Y.,Liu,Z.,Sun,M.,Zhou,B.,andDing,N. Processreinforcementthroughimplicitrewards.
CoRR,abs/2502.01456,2025. doi: 10.48550/ARXIV.2502.01456. URLhttps://doi.org/10.48550/arXiv.2502.01456.
DeepSeek-AI,Guo,D.,Yang,D.,Zhang,H.,Song,J.,Zhang,R.,Xu,R.,Zhu,Q.,Ma,S.,Wang,P.,Bi,X.,Zhang,X.,Yu,X.,Wu,Y.,
Wu,Z.F.,Gou,Z.,Shao,Z.,Li,Z.,Gao,Z.,Liu,A.,Xue,B.,Wang,B.,Wu,B.,Feng,B.,Lu,C.,Zhao,C.,Deng,C.,Zhang,C.,
Ruan,C.,Dai,D.,Chen,D.,Ji,D.,Li,E.,Lin,F.,Dai,F.,Luo,F.,Hao,G.,Chen,G.,Li,G.,Zhang,H.,Bao,H.,Xu,H.,Wang,H.,
Ding,H.,Xin,H.,Gao,H.,Qu,H.,Li,H.,Guo,J.,Li,J.,Wang,J.,Chen,J.,Yuan,J.,Qiu,J.,Li,J.,Cai,J.L.,Ni,J.,Liang,J.,Chen,
J.,Dong,K.,Hu,K.,Gao,K.,Guan,K.,Huang,K.,Yu,K.,Wang,L.,Zhang,L.,Zhao,L.,Wang,L.,Zhang,L.,Xu,L.,Xia,L.,
Zhang,M.,Zhang,M.,Tang,M.,Li,M.,Wang,M.,Li,M.,Tian,N.,Huang,P.,Zhang,P.,Wang,Q.,Chen,Q.,Du,Q.,Ge,R.,
Zhang,R.,Pan,R.,Wang,R.,Chen,R.J.,Jin,R.L.,Chen,R.,Lu,S.,Zhou,S.,Chen,S.,Ye,S.,Wang,S.,Yu,S.,Zhou,S.,Pan,S.,
andLi,S.S. Deepseek-r1: Incentivizingreasoningcapabilityinllmsviareinforcementlearning. CoRR,abs/2501.12948,2025. doi:
10.48550/ARXIV.2501.12948. URLhttps://doi.org/10.48550/arXiv.2501.12948.
Demski,A.andGarrabrant,S. Embeddedagency. CoRR,abs/1902.09469,2019. URLhttp://arxiv.org/abs/1902.09469.
Dennis, M., Jaques, N., Vinitsky, E., Bayen, A. M., Russell, S., Critch, A., and Levine, S. Emergent complexity and zero-
shot transfer via unsupervised environment design. In Larochelle, H., Ranzato, M., Hadsell, R., Balcan, M., and Lin, H.
(eds.),AdvancesinNeuralInformationProcessingSystems33: AnnualConferenceonNeuralInformationProcessingSystems
2020, NeurIPS 2020, December 6-12, 2020, virtual, 2020. URL https://proceedings.neurips.cc/paper/2020/hash/
985e9a46e10005356bbaf194249f6856-Abstract.html.
Dubey,A.,Jauhri,A.,Pandey,A.,Kadian,A.,Al-Dahle,A.,Letman,A.,Mathur,A.,Schelten,A.,Yang,A.,Fan,A.,Goyal,A.,
Hartshorn,A.,Yang,A.,Mitra,A.,Sravankumar,A.,Korenev,A.,Hinsvark,A.,Rao,A.,Zhang,A.,Rodriguez,A.,Gregerson,A.,
Spataru,A.,Rozière,B.,Biron,B.,Tang,B.,Chern,B.,Caucheteux,C.,Nayak,C.,Bi,C.,Marra,C.,McConnell,C.,Keller,C.,
Touret,C.,Wu,C.,Wong,C.,Ferrer,C.C.,Nikolaidis,C.,Allonsius,D.,Song,D.,Pintz,D.,Livshits,D.,Esiobu,D.,Choudhary,
D.,Mahajan,D.,Garcia-Olano,D.,Perino,D.,Hupkes,D.,Lakomkin,E.,AlBadawy,E.,Lobanova,E.,Dinan,E.,Smith,E.M.,
Radenovic,F.,Zhang,F.,Synnaeve,G.,Lee,G.,Anderson,G.L.,Nail,G.,Mialon,G.,Pang,G.,Cucurell,G.,Nguyen,H.,Korevaar,
H.,Xu,H.,Touvron,H.,Zarov,I.,Ibarra,I.A.,Kloumann,I.M.,Misra,I.,Evtimov,I.,Copet,J.,Lee,J.,Geffert,J.,Vranes,J.,
Park,J.,Mahadeokar,J.,Shah,J.,vanderLinde,J.,Billock,J.,Hong,J.,Lee,J.,Fu,J.,Chi,J.,Huang,J.,Liu,J.,Wang,J.,Yu,
J.,Bitton,J.,Spisak,J.,Park,J.,Rocca,J.,Johnstun,J.,Saxe,J.,Jia,J.,Alwala,K.V.,Upasani,K.,Plawiak,K.,Li,K.,Heafield,
14
--- PAGE 15 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
K.,Stone,K.,andetal. Thellama3herdofmodels. CoRR,abs/2407.21783,2024. doi: 10.48550/ARXIV.2407.21783. URL
https://doi.org/10.48550/arXiv.2407.21783.
Ebert,C.,Cain,J.,Antoniol,G.,Counsell,S.,andLaplante,P. Cyclomaticcomplexity. IEEEsoftware,33(6):27–29,2016.
Florensa, C., Held, D., Geng, X., andAbbeel, P. Automaticgoalgenerationforreinforcementlearningagents. InDy, J.G.and
Krause, A. (eds.), Proceedings of the 35th International Conference on Machine Learning, ICML 2018, Stockholmsmässan,
Stockholm,Sweden,July10-15,2018,volume80ofProceedingsofMachineLearningResearch,pp.1514–1523.PMLR,2018. URL
http://proceedings.mlr.press/v80/florensa18a.html.
Goodfellow,I.J.,Pouget-Abadie,J.,Mirza,M.,Xu,B.,Warde-Farley,D.,Ozair,S.,Courville,A.C.,andBengio,Y. Generative
adversarialnetworks.Commun.ACM,63(11):139–144,2020.doi: 10.1145/3422622.URLhttps://doi.org/10.1145/3422622.
Gu, A., Rozière, B., Leather, H. J., Solar-Lezama, A., Synnaeve, G., and Wang, S. Cruxeval: A benchmark for code reasoning,
understandingandexecution. InForty-firstInternationalConferenceonMachineLearning,ICML2024,Vienna,Austria,July21-27,
2024.OpenReview.net,2024. URLhttps://openreview.net/forum?id=Ffpg52swvg.
Halstead,M.H. ElementsofSoftwareScience(Operatingandprogrammingsystemsseries). ElsevierScienceInc.,1977.
He,C.,Luo,R.,Bai,Y.,Hu,S.,Thai,Z.L.,Shen,J.,Hu,J.,Han,X.,Huang,Y.,Zhang,Y.,Liu,J.,Qi,L.,Liu,Z.,andSun,M.
Olympiadbench: AchallengingbenchmarkforpromotingAGIwitholympiad-levelbilingualmultimodalscientificproblems. InKu,
L.,Martins,A.,andSrikumar,V.(eds.),Proceedingsofthe62ndAnnualMeetingoftheAssociationforComputationalLinguistics
(Volume1: LongPapers),ACL2024,Bangkok,Thailand,August11-16,2024,pp.3828–3850.AssociationforComputational
Linguistics,2024. doi: 10.18653/V1/2024.ACL-LONG.211. URLhttps://doi.org/10.18653/v1/2024.acl-long.211.
Hendrycks, D., Burns, C., Kadavath, S., Arora, A., Basart, S., Tang, E., Song, D., and Steinhardt, J. Measuring math-
ematical problem solving with the MATH dataset. In Vanschoren, J. and Yeung, S. (eds.), Proceedings of the Neu-
ral Information Processing Systems Track on Datasets and Benchmarks 1, NeurIPS Datasets and Benchmarks 2021,
December 2021, virtual, 2021. URL https://datasets-benchmarks-proceedings.neurips.cc/paper/2021/hash/
be83ab3ecd0db773eb2dc1b0a17836a1-Abstract-round2.html.
Hinton, G. E., Vinyals, O., and Dean, J. Distilling the knowledge in a neural network. CoRR, abs/1503.02531, 2015. URL
http://arxiv.org/abs/1503.02531.
Hu,J. REINFORCE++: Asimpleandefficientapproachforaligninglargelanguagemodels. CoRR,abs/2501.03262,2025. doi:
10.48550/ARXIV.2501.03262. URLhttps://doi.org/10.48550/arXiv.2501.03262.
Hu, J., Zhang, Y., Han, Q., Jiang, D., Zhang, X., and Shum, H. Open-reasoner-zero: An open source approach to scaling up
reinforcement learning on the base model. CoRR, abs/2503.24290, 2025. doi: 10.48550/ARXIV.2503.24290. URL https:
//doi.org/10.48550/arXiv.2503.24290.
Hubinger,E.,vanMerwĳk,C.,Mikulik,V.,Skalse,J.,andGarrabrant,S.Risksfromlearnedoptimizationinadvancedmachinelearning
systems. CoRR,abs/1906.01820,2019. URLhttp://arxiv.org/abs/1906.01820.
Hughes,E.,Dennis,M.D.,Parker-Holder,J.,Behbahani,F.M.P.,Mavalankar,A.,Shi,Y.,Schaul,T.,andRocktäschel,T. Position:
Open-endednessisessentialforartificialsuperhumanintelligence. InForty-firstInternationalConferenceonMachineLearning,
ICML2024,Vienna,Austria,July21-27,2024.OpenReview.net,2024.URLhttps://openreview.net/forum?id=Bc4vZ2CX7E.
Hui,B.,Yang,J.,Cui,Z.,Yang,J.,Liu,D.,Zhang,L.,Liu,T.,Zhang,J.,Yu,B.,Dang,K.,Yang,A.,Men,R.,Huang,F.,Ren,X.,Ren,
X.,Zhou,J.,andLin,J. Qwen2.5-codertechnicalreport. CoRR,abs/2409.12186,2024. doi: 10.48550/ARXIV.2409.12186. URL
https://doi.org/10.48550/arXiv.2409.12186.
Jaech,A.,Kalai,A.,Lerer,A.,Richardson,A.,El-Kishky,A.,Low,A.,Helyar,A.,Madry,A.,Beutel,A.,Carney,A.,etal. Openaio1
systemcard. arXivpreprintarXiv:2412.16720,2024.
Jain,N.,Han,K.,Gu,A.,Li,W.,Yan,F.,Zhang,T.,Wang,S.,Solar-Lezama,A.,Sen,K.,andStoica,I. Livecodebench: Holisticand
contaminationfreeevaluationoflargelanguagemodelsforcode. CoRR,abs/2403.07974,2024. doi: 10.48550/ARXIV.2403.07974.
URLhttps://doi.org/10.48550/arXiv.2403.07974.
Kirchner,J.H.,Chen,Y.,Edwards,H.,Leike,J.,McAleese,N.,andBurda,Y. Prover-verifiergamesimprovelegibilityofLLMoutputs.
CoRR,abs/2407.13692,2024. doi: 10.48550/ARXIV.2407.13692. URLhttps://doi.org/10.48550/arXiv.2407.13692.
Ladosz,P.,Weng,L.,Kim,M.,andOh,H. Explorationindeepreinforcementlearning: Asurvey. Inf.Fusion,85:1–22,2022. doi:
10.1016/J.INFFUS.2022.03.003. URLhttps://doi.org/10.1016/j.inffus.2022.03.003.
15
--- PAGE 16 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Lambert,N.,Morrison,J.,Pyatkin,V.,Huang,S.,Ivison,H.,Brahman,F.,Miranda,L.J.V.,Liu,A.,Dziri,N.,Lyu,S.,Gu,Y.,Malik,S.,
Graf,V.,Hwang,J.D.,Yang,J.,Bras,R.L.,Tafjord,O.,Wilhelm,C.,Soldaini,L.,Smith,N.A.,Wang,Y.,Dasigi,P.,andHajishirzi,
H.Tülu3: Pushingfrontiersinopenlanguagemodelpost-training.CoRR,abs/2411.15124,2024.doi: 10.48550/ARXIV.2411.15124.
URLhttps://doi.org/10.48550/arXiv.2411.15124.
Laskin, M., Yarats, D., Liu, H., Lee, K., Zhan, A., Lu, K., Cang, C., Pinto, L., and Abbeel, P. URLB: unsu-
pervised reinforcement learning benchmark. In Vanschoren, J. and Yeung, S. (eds.), Proceedings of the Neural In-
formation Processing Systems Track on Datasets and Benchmarks 1, NeurIPS Datasets and Benchmarks 2021, De-
cember 2021, virtual, 2021. URL https://datasets-benchmarks-proceedings.neurips.cc/paper/2021/hash/
091d584fced301b442654dd8c23b3fc9-Abstract-round2.html.
Leike, J.andSutskever, I. Introducingsuperalignment. https://openai.com/index/introducing-superalignment/, 2023.
OpenAIBlog.
Li,J.,Guo,D.,Yang,D.,Xu,R.,Wu,Y.,andHe,J. Codei/o: Condensingreasoningpatternsviacodeinput-outputprediction. CoRR,
abs/2502.07316,2025. doi: 10.48550/ARXIV.2502.07316. URLhttps://doi.org/10.48550/arXiv.2502.07316.
Li,R.,Fu,J.,Zhang,B.,Huang,T.,Sun,Z.,Lyu,C.,Liu,G.,Jin,Z.,andLi,G. TACO:topicsinalgorithmiccodegenerationdataset.
CoRR,abs/2312.14852,2023. doi: 10.48550/ARXIV.2312.14852. URLhttps://doi.org/10.48550/arXiv.2312.14852.
Liu,J.andZhang,L. Code-r1: Reproducingr1forcodewithreliablerewards. GitHub,2025.
Liu, J., Xia, C. S., Wang, Y., and Zhang, L. Is your code generated by chatGPT really correct? rigorous evaluation of large
language models for code generation. In Thirty-seventh Conference on Neural Information Processing Systems, 2023. URL
https://openreview.net/forum?id=1qvx610Cu7.
Liu,Z.,Chen,C.,Li,W.,Qi,P.,Pang,T.,Du,C.,Lee,W.S.,andLin,M. Understandingr1-zero-liketraining: Acriticalperspective.
CoRR,abs/2503.20783,2025. doi: 10.48550/ARXIV.2503.20783. URLhttps://doi.org/10.48550/arXiv.2503.20783.
Lopez,R.H.Q. Complexipy: Anextremelyfastpythonlibrarytocalculatethecognitivecomplexityofpythonfiles,writteninrust,
2025. URLhttps://github.com/rohaquinlop/complexipy. Accessed: 2025-04-06.
Loshchilov,I.andHutter,F.Decoupledweightdecayregularization.In7thInternationalConferenceonLearningRepresentations,ICLR
2019,NewOrleans,LA,USA,May6-9,2019.OpenReview.net,2019. URLhttps://openreview.net/forum?id=Bkg6RiCqY7.
Morris,J.Therearenonewideasinai... onlynewdatasets.https://blog.jxmo.io/p/there-are-no-new-ideas-in-ai-only,
2025.
OpenAI. Openaio3-mini,January2025a. URLhttps://openai.com/index/openai-o3-mini/. Accessed: 2025-04-17.
OpenAI. Introducingopenaio3ando4-mini,April2025b. URLhttps://openai.com/index/introducing-o3-and-o4-mini/.
Accessed: 2025-04-17.
OpenAI,Plappert,M.,Sampedro,R.,Xu,T.,Akkaya,I.,Kosaraju,V.,Welinder,P.,D’Sa,R.,Petron,A.,deOliveiraPinto,H.P.,
Paino,A.,Noh,H.,Weng,L.,Yuan,Q.,Chu,C.,andZaremba,W. Asymmetricself-playforautomaticgoaldiscoveryinrobotic
manipulation. CoRR,abs/2101.04882,2021. URLhttps://arxiv.org/abs/2101.04882.
Ouyang,L.,Wu,J.,Jiang,X.,Almeida,D.,Wainwright,C.,Mishkin,P.,Zhang,C.,Agarwal,S.,Slama,K.,Ray,A.,etal. Training
languagemodelstofollowinstructionswithhumanfeedback. Advancesinneuralinformationprocessingsystems,35:27730–27744,
2022.
Poesia, G., Broman, D., Haber, N., and Goodman, N. D. Learning formal mathematics from intrinsic motivation. In Glober-
sons, A., Mackey, L., Belgrave, D., Fan, A., Paquet, U., Tomczak, J. M., and Zhang, C. (eds.), Advances in Neural In-
formation Processing Systems 38: Annual Conference on Neural Information Processing Systems 2024, NeurIPS 2024, Van-
couver, BC, Canada, December 10 - 15, 2024, 2024. URL http://papers.nips.cc/paper_files/paper/2024/hash/
4b8001fc75f0532827472ea5a16af9ca-Abstract-Conference.html.
Radford,A.,Wu,J.,Child,R.,Luan,D.,Amodei,D.,Sutskever,I.,etal. Languagemodelsareunsupervisedmultitasklearners. OpenAI
blog,1(8):9,2019.
Ren,Z.Z.,Shao,Z.,Song,J.,Xin,H.,Wang,H.,Zhao,W.,Zhang,L.,Fu,Z.,Zhu,Q.,Yang,D.,Wu,Z.F.,Gou,Z.,Ma,S.,Tang,H.,
Liu,Y.,Gao,W.,Guo,D.,andRuan,C. Deepseek-prover-v2: Advancingformalmathematicalreasoningviareinforcementlearning
forsubgoaldecomposition,2025. URLhttps://arxiv.org/abs/2504.21801.
Schaul,T. Boundlesssocraticlearningwithlanguagegames. arXivpreprintarXiv:2411.16905,2024.
16
--- PAGE 17 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Schmidhuber,J. Exploringthepredictable. InAdvancesinevolutionarycomputing: theoryandapplications,pp.579–612.Springer,
2003.
Schmidhuber,J. POWERPLAY:traininganincreasinglygeneralproblemsolverbycontinuallysearchingforthesimpleststillunsolvable
problem. CoRR,abs/1112.5309,2011. URLhttp://arxiv.org/abs/1112.5309.
Shao, Z., Wang, P., Zhu, Q., Xu, R., Song, J., Zhang, M., Li, Y. K., Wu, Y., and Guo, D. Deepseekmath: Pushing the limits
ofmathematicalreasoninginopenlanguagemodels. CoRR,abs/2402.03300,2024. doi: 10.48550/ARXIV.2402.03300. URL
https://doi.org/10.48550/arXiv.2402.03300.
Sheng,G.,Zhang,C.,Ye,Z.,Wu,X.,Zhang,W.,Zhang,R.,Peng,Y.,Lin,H.,andWu,C. Hybridflow: Aflexibleandefficient
RLHF framework. In Proceedings of the Twentieth European Conference on Computer Systems, EuroSys 2025, Rotterdam,
The Netherlands, 30 March 2025 - 3 April 2025, pp. 1279–1297. ACM, 2025. doi: 10.1145/3689031.3696075. URL https:
//doi.org/10.1145/3689031.3696075.
Silver,D.andSutton,R.S.Theeraofexperience.https://storage.googleapis.com/deepmind-media/Era-of-Experience%
20/The%20Era%20of%20Experience%20Paper.pdf,2025.
Silver,D.,Huang,A.,Maddison,C.J.,Guez,A.,Sifre,L.,vandenDriessche,G.,Schrittwieser,J.,Antonoglou,I.,Panneershelvam,
V.,Lanctot,M.,Dieleman,S.,Grewe,D.,Nham,J.,Kalchbrenner,N.,Sutskever,I.,Lillicrap,T.P.,Leach,M.,Kavukcuoglu,K.,
Graepel,T.,andHassabis,D. Masteringthegameofgowithdeepneuralnetworksandtreesearch. Nat.,529(7587):484–489,2016.
doi: 10.1038/NATURE16961. URLhttps://doi.org/10.1038/nature16961.
Silver,D.,Hubert,T.,Schrittwieser,J.,Antonoglou,I.,Lai,M.,Guez,A.,Lanctot,M.,Sifre,L.,Kumaran,D.,Graepel,T.,Lillicrap,
T.P.,Simonyan,K.,andHassabis,D. Masteringchessandshogibyself-playwithageneralreinforcementlearningalgorithm. CoRR,
abs/1712.01815,2017. URLhttp://arxiv.org/abs/1712.01815.
Stuart,T. Understandingcomputation-fromsimplemachinestoimpossibleprograms. O’Reilly,2015. ISBN978-1-449-32927-3. URL
http://www.oreilly.de/catalog/9781449329273/index.html.
Sukhbaatar, S., Lin, Z., Kostrikov, I., Synnaeve, G., Szlam, A., and Fergus, R. Intrinsic motivation and automatic curricula via
asymmetricself-play. In6thInternationalConferenceonLearningRepresentations,ICLR2018,Vancouver,BC,Canada,April30-
May3,2018,ConferenceTrackProceedings.OpenReview.net,2018. URLhttps://openreview.net/forum?id=SkT5Yg-RZ.
Suteu, M. and Guo, Y. Regularizing deep multi-task networks using orthogonal gradients. CoRR, abs/1912.06844, 2019. URL
http://arxiv.org/abs/1912.06844.
Sutskever,I.,Vinyals,O.,andLe,Q.V. Neurips2024testoftimeawardsession: Sequencetosequencelearningwithneuralnetworks.
Conferencesession,December2024. URLhttps://neurips.cc/virtual/2024/test-of-time/105032.
Sutton,R.S. Verification,thekeytoai. http://incompleteideas.net/IncIdeas/KeytoAI.html,2001.
Team,K.,Du,A.,Gao,B.,Xing,B.,Jiang,C.,Chen,C.,Li,C.,Xiao,C.,Du,C.,Liao,C.,Tang,C.,Wang,C.,Zhang,D.,Yuan,E.,Lu,
E.,Tang,F.,Sung,F.,Wei,G.,Lai,G.,Guo,H.,Zhu,H.,Ding,H.,Hu,H.,Yang,H.,Zhang,H.,Yao,H.,Zhao,H.,Lu,H.,Li,H.,Yu,
H.,Gao,H.,Zheng,H.,Yuan,H.,Chen,J.,Guo,J.,Su,J.,Wang,J.,Zhao,J.,Zhang,J.,Liu,J.,Yan,J.,Wu,J.,Shi,L.,Ye,L.,Yu,L.,
Dong,M.,Zhang,N.,Ma,N.,Pan,Q.,Gong,Q.,Liu,S.,Ma,S.,Wei,S.,Cao,S.,Huang,S.,Jiang,T.,Gao,W.,Xiong,W.,He,W.,
Huang,W.,Wu,W.,He,W.,Wei,X.,Jia,X.,Wu,X.,Xu,X.,Zu,X.,Zhou,X.,Pan,X.,Charles,Y.,Li,Y.,Hu,Y.,Liu,Y.,Chen,
Y.,Wang,Y.,Liu,Y.,Qin,Y.,Liu,Y.,Yang,Y.,Bao,Y.,Du,Y.,Wu,Y.,Wang,Y.,Zhou,Z.,Wang,Z.,Li,Z.,Zhu,Z.,Zhang,
Z.,Wang,Z.,Yang,Z.,Huang,Z.,Huang,Z.,Xu,Z.,andYang,Z. Kimik1.5: Scalingreinforcementlearningwithllms. CoRR,
abs/2501.12599,2025. doi: 10.48550/ARXIV.2501.12599. URLhttps://doi.org/10.48550/arXiv.2501.12599.
Villalobos,P.,Ho,A.,Sevilla,J.,Besiroglu,T.,Heim,L.,andHobbhahn,M. Position: Willwerunoutofdata? limitsofLLMscaling
basedonhuman-generateddata. InForty-firstInternationalConferenceonMachineLearning,ICML2024,Vienna,Austria,July
21-27,2024.OpenReview.net,2024. URLhttps://openreview.net/forum?id=ViZcgDQjyG.
Wang,H.,Yue,Y.,Lu,R.,Shi,J.,Zhao,A.,Wang,S.,Song,S.,andHuang,G. Modelsurgery: ModulatingLLM‘sbehaviorvia
simpleparameterediting. InProceedingsofthe2025ConferenceoftheNationsoftheAmericasChapteroftheAssociationfor
ComputationalLinguistics,pp.6337–6357,2025a.
Wang,R.,Lehman,J.,Clune,J.,andStanley,K.O. Pairedopen-endedtrailblazer(POET):endlesslygeneratingincreasinglycomplex
anddiverselearningenvironmentsandtheirsolutions. CoRR,abs/1901.01753,2019. URLhttp://arxiv.org/abs/1901.01753.
Wang,S.,Yang,Q.,Gao,J.,Lin,M.G.,Chen,H.,Wu,L.,Jia,N.,Song,S.,andHuang,G. Trainonce,getafamily: State-adaptive
balancesforoffline-to-onlinereinforcementlearning. InThirty-seventhConferenceonNeuralInformationProcessingSystems,2023.
URLhttps://openreview.net/forum?id=vtoY8qJjTR.
17
--- PAGE 18 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Wang,S.,Liu,C.,Zheng,Z.,Qi,S.,Chen,S.,Yang,Q.,Zhao,A.,Wang,C.,Song,S.,andHuang,G.BoostingLLMagentswithrecursive
contemplationforeffectivedeceptionhandling. InKu,L.-W.,Martins,A.,andSrikumar,V.(eds.),FindingsoftheAssociationfor
ComputationalLinguistics: ACL2024,pp.9909–9953,Bangkok,Thailand,August2024.AssociationforComputationalLinguistics.
doi: 10.18653/v1/2024.findings-acl.591. URLhttps://aclanthology.org/2024.findings-acl.591/.
Wang, Y., Yang, Q., Zeng, Z., Ren, L., Liu, L., Peng, B., Cheng, H., He, X., Wang, K., Gao, J., Chen, W., Wang, S., Du,
S. S., and Shen, Y. Reinforcement learning for reasoning in large language models with one training example, 2025b. URL
https://arxiv.org/abs/2504.20571.
Wu,Q.,Bansal,G.,Zhang,J.,Wu,Y.,Zhang,S.,Zhu,E.,Li,B.,Jiang,L.,Zhang,X.,andWang,C. Autogen: Enablingnext-gen
LLMapplicationsviamulti-agentconversationframework. CoRR,abs/2308.08155,2023. doi: 10.48550/ARXIV.2308.08155. URL
https://doi.org/10.48550/arXiv.2308.08155.
Wu,Y.,Yue,T.,Zhang,S.,Wang,C.,andWu,Q. Stateflow: EnhancingLLMtask-solvingthroughstate-drivenworkflows. CoRR,
abs/2403.11322,2024. doi: 10.48550/ARXIV.2403.11322. URLhttps://doi.org/10.48550/arXiv.2403.11322.
Xie, T., Gao, Z., Ren, Q., Luo, H., Hong, Y., Dai, B., Zhou, J., Qiu, K., Wu, Z., and Luo, C. Logic-rl: Unleashing LLM
reasoning with rule-based reinforcement learning. CoRR, abs/2502.14768, 2025. doi: 10.48550/ARXIV.2502.14768. URL
https://doi.org/10.48550/arXiv.2502.14768.
Xu,F.,Yan,H.,Ma,C.,Zhao,H.,Sun,Q.,Cheng,K.,He,J.,Liu,J.,andWu,Z. Genius: Ageneralizableandpurelyunsupervised
self-trainingframeworkforadvancedreasoning,2025. URLhttps://arxiv.org/abs/2504.08672.
Yang,A.,Yang,B.,Zhang,B.,Hui,B.,Zheng,B.,Yu,B.,Li,C.,Liu,D.,Huang,F.,Wei,H.,Lin,H.,Yang,J.,Tu,J.,Zhang,J.,Yang,
J.,Yang,J.,Zhou,J.,Lin,J.,Dang,K.,Lu,K.,Bao,K.,Yang,K.,Yu,L.,Li,M.,Xue,M.,Zhang,P.,Zhu,Q.,Men,R.,Lin,R.,Li,T.,
Xia,T.,Ren,X.,Ren,X.,Fan,Y.,Su,Y.,Zhang,Y.,Wan,Y.,Liu,Y.,Cui,Z.,Zhang,Z.,andQiu,Z. Qwen2.5technicalreport.
CoRR,abs/2412.15115,2024a. doi: 10.48550/ARXIV.2412.15115. URLhttps://doi.org/10.48550/arXiv.2412.15115.
Yang,A.,Zhang,B.,Hui,B.,Gao,B.,Yu,B.,Li,C.,Liu,D.,Tu,J.,Zhou,J.,Lin,J.,Lu,K.,Xue,M.,Lin,R.,Liu,T.,Ren,X.,and
Zhang,Z. Qwen2.5-mathtechnicalreport: Towardmathematicalexpertmodelviaself-improvement. CoRR,abs/2409.12122,2024b.
doi: 10.48550/ARXIV.2409.12122. URLhttps://doi.org/10.48550/arXiv.2409.12122.
Yao,S.,Zhao,J.,Yu,D.,Du,N.,Shafran,I.,Narasimhan,K.R.,andCao,Y. React: Synergizingreasoningandactinginlanguage
models. InTheEleventhInternationalConferenceonLearningRepresentations, ICLR2023, Kigali, Rwanda, May1-5, 2023.
OpenReview.net,2023. URLhttps://openreview.net/forum?id=WE_vluYUL-X.
Ye,Z.,Agarwal,R.,Liu,T.,Joshi,R.,Velury,S.,Le,Q.V.,Tan,Q.,andLiu,Y. Evolvingalignmentviaasymmetricself-play. CoRR,
abs/2411.00062,2024. doi: 10.48550/ARXIV.2411.00062. URLhttps://doi.org/10.48550/arXiv.2411.00062.
Yu,Q.,Zhang,Z.,Zhu,R.,Yuan,Y.,Zuo,X.,Yue,Y.,Fan,T.,Liu,G.,Liu,L.,Liu,X.,Lin,H.,Lin,Z.,Ma,B.,Sheng,G.,Tong,Y.,
Zhang,C.,Zhang,M.,Zhang,W.,Zhu,H.,Zhu,J.,Chen,J.,Chen,J.,Wang,C.,Yu,H.,Dai,W.,Song,Y.,Wei,X.,Zhou,H.,Liu,J.,
Ma,W.,Zhang,Y.,Yan,L.,Qiao,M.,Wu,Y.,andWang,M. DAPO:anopen-sourceLLMreinforcementlearningsystematscale.
CoRR,abs/2503.14476,2025. doi: 10.48550/ARXIV.2503.14476. URLhttps://doi.org/10.48550/arXiv.2503.14476.
Yuan,W.,Pang,R.Y.,Cho,K.,Li,X.,Sukhbaatar,S.,Xu,J.,andWeston,J. Self-rewardinglanguagemodels. URLhttps://arxiv.
org/abs/2401.10020,2024.
Yuan,Y.,Yu,Q.,Zuo,X.,Zhu,R.,Xu,W.,Chen,J.,Wang,C.,Fan,T.,Du,Z.,Wei,X.,etal. Vapo: Efficientandreliablereinforcement
learningforadvancedreasoningtasks. arXivpreprintarXiv:2504.05118,2025.
Yue,Y.,Lu,R.,Kang,B.,Song,S.,andHuang,G. Understanding,predictingandbetterresolvingq-valuedivergenceinoffline-rl.
AdvancesinNeuralInformationProcessingSystems,36:60247–60277,2023.
Yue,Y.,Wang,Y.,Kang,B.,Han,Y.,Wang,S.,Song,S.,Feng,J.,andHuang,G. Deer-vla: Dynamicinferenceofmultimodallarge
languagemodelsforefficientrobotexecution. InGlobersons,A.,Mackey,L.,Belgrave,D.,Fan,A.,Paquet,U.,Tomczak,J.M.,and
Zhang,C.(eds.),AdvancesinNeuralInformationProcessingSystems38: AnnualConferenceonNeuralInformationProcessing
Systems2024,NeurIPS2024,Vancouver,BC,Canada,December10-15,2024,2024. URLhttp://papers.nips.cc/paper_
files/paper/2024/hash/67b0e7c7c2a5780aeefe3b79caac106e-Abstract-Conference.html.
Yue,Y.,Chen,Z.,Lu,R.,Zhao,A.,Wang,Z.,Yue,Y.,Song,S.,andHuang,G.Doesreinforcementlearningreallyincentivizereasoning
capacityinllmsbeyondthebasemodel?,2025. URLhttps://arxiv.org/abs/2504.13837.
Zelikman,E.,Wu, Y.,Mu,J.,andGoodman,N. Star: Bootstrappingreasoningwithreasoning. AdvancesinNeuralInformation
ProcessingSystems,35:15476–15488,2022.
18
--- PAGE 19 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Zeng,H.,Jiang,D.,Wang,H.,Nie,P.,Chen,X.,andChen,W. ACECODER:acingcoderRLviaautomatedtest-casesynthesis. CoRR,
abs/2502.01718,2025a. doi: 10.48550/ARXIV.2502.01718. URLhttps://doi.org/10.48550/arXiv.2502.01718.
Zeng, W., Huang, Y., Liu, Q., Liu, W., He, K., Ma, Z., and He, J. Simplerl-zoo: Investigating and taming zero reinforcement
learning for open base models in the wild. CoRR, abs/2503.18892, 2025b. doi: 10.48550/ARXIV.2503.18892. URL https:
//doi.org/10.48550/arXiv.2503.18892.
Zhang,C.,Deng,Y.,Lin,X.,Wang,B.,Ng,D.,Ye,H.,Li,X.,Xiao,Y.,Mo,Z.,Zhang,Q.,etal. 100daysafterdeepseek-r1: Asurvey
onreplicationstudiesandmoredirectionsforreasoninglanguagemodels. arXivpreprintarXiv:2505.00551,2025a.
Zhang,Q.,Wu,H.,Zhang,C.,Zhao,P.,andBian,Y. Rightquestionisalreadyhalftheanswer: Fullyunsupervisedllmreasoning
incentivization,2025b. URLhttps://arxiv.org/abs/2504.05812.
Zhang,Y.andYang,Q. Asurveyonmulti-tasklearning. IEEEtransactionsonknowledgeanddataengineering,34(12):5586–5609,
2021.
Zhao, A., Lin, M. G., Li, Y., Liu, Y., and Huang, G. A mixture of surprises for unsupervised reinforcement learning. In
Koyejo, S., Mohamed, S., Agarwal, A., Belgrave, D., Cho, K., and Oh, A. (eds.), Advances in Neural Information Pro-
cessing Systems 35: Annual Conference on Neural Information Processing Systems 2022, NeurIPS 2022, New Orleans,
LA, USA, November 28 - December 9, 2022, 2022. URL http://papers.nips.cc/paper_files/paper/2022/hash/
a7667ee5d545a43d2f0fda98863c260e-Abstract-Conference.html.
Zhao,A.,Huang,D.,Xu,Q.,Lin,M.,Liu,Y.,andHuang,G. Expel: LLMagentsareexperientiallearners. InWooldridge,M.J.,Dy,
J.G.,andNatarajan,S.(eds.),Thirty-EighthAAAIConferenceonArtificialIntelligence,AAAI2024,Thirty-SixthConferenceon
InnovativeApplicationsofArtificialIntelligence,IAAI2024,FourteenthSymposiumonEducationalAdvancesinArtificialIntelligence,
EAAI2014,February20-27,2024,Vancouver,Canada,pp.19632–19642.AAAIPress,2024. doi: 10.1609/AAAI.V38I17.29936.
URLhttps://doi.org/10.1609/aaai.v38i17.29936.
Zhao,A.,Xu,Q.,Lin,M.,Wang,S.,Liu,Y.,Zheng,Z.,andHuang,G. Diver-ct: Diversity-enhancedredteaminglargelanguagemodel
assistantswithrelaxingconstraints. InWalsh,T.,Shah,J.,andKolter,Z.(eds.),AAAI-25,SponsoredbytheAssociationforthe
AdvancementofArtificialIntelligence,February25-March4,2025,Philadelphia,PA,USA,pp.26021–26030.AAAIPress,2025a.
doi: 10.1609/AAAI.V39I24.34797. URLhttps://doi.org/10.1609/aaai.v39i24.34797.
Zhao,A.,Zhu,E.,Lu,R.,Lin,M.,Liu,Y.,andHuang,G. Self-referencingagentsforunsupervisedreinforcementlearning. Neural
Networks, 188:107448, 2025b. doi: 10.1016/J.NEUNET.2025.107448. URL https://doi.org/10.1016/j.neunet.2025.
107448.
Zitkovich,B.,Yu,T.,Xu,S.,Xu,P.,Xiao,T.,Xia,F.,Wu,J.,Wohlhart,P.,Welker,S.,Wahid,A.,Vuong,Q.,Vanhoucke,V.,Tran,
H.T.,Soricut,R.,Singh,A.,Singh,J.,Sermanet,P.,Sanketi,P.R.,Salazar,G.,Ryoo,M.S.,Reymann,K.,Rao,K.,Pertsch,K.,
Mordatch,I.,Michalewski,H.,Lu,Y.,Levine,S.,Lee,L.,Lee,T.E.,Leal,I.,Kuang,Y.,Kalashnikov,D.,Julian,R.,Joshi,N.J.,
Irpan,A.,Ichter,B.,Hsu,J.,Herzog,A.,Hausman,K.,Gopalakrishnan,K.,Fu,C.,Florence,P.,Finn,C.,Dubey,K.A.,Driess,
D.,Ding,T.,Choromanski,K.M.,Chen,X.,Chebotar,Y.,Carbajal,J.,Brown,N.,Brohan,A.,Arenas,M.G.,andHan,K. RT-2:
vision-language-actionmodelstransferwebknowledgetoroboticcontrol.InTan,J.,Toussaint,M.,andDarvish,K.(eds.),Conference
onRobotLearning,CoRL2023,6-9November2023,Atlanta,GA,USA,volume229ofProceedingsofMachineLearningResearch,
pp.2165–2183.PMLR,2023. URLhttps://proceedings.mlr.press/v229/zitkovich23a.html.
Zuo,Y.,Zhang,K.,Qu,S.,Sheng,L.,Zhu,X.,Qi,B.,Sun,Y.,Cui,G.,Ding,N.,andZhou,B. Ttrl: Test-timereinforcementlearning,
2025. URLhttps://arxiv.org/abs/2504.16084.
19
--- PAGE 20 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Appendix
AppendixContents
A ReinforcementLearningwithVerifiableRewards. 21
B ImplementationDetails 21
C MoreResults 22
C.1 Out-of-DistributionPerformanceBreakdown . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 22
C.2 In-DistributionResults. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 22
C.3 InterplayBetweenProposeandSolveRoles . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 22
C.4 ComplexityandDiversityMetricsofAZRProposedTasks . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 32
C.5 GeneratedCodeComplexityDynamicsBetweenAbd/DedandInd. . . . . . . . . . . . . . . . . . . . . . . . . . . 32
D AlternativeApproachesConsidered 49
D.1 ErrorDeductionTask . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 49
D.2 CompositeFunctionsasCurriculumLearning . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 49
D.3 ToyingwiththeInitialp(z) . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 49
D.4 ExtraRewards . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 49
D.5 EnvironmentTransition . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . 50
20
--- PAGE 21 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
A.ReinforcementLearningwithVerifiableRewards.
WeusereinforcementlearningtoupdateourlearnerLLM,rewardingitbasedonatask-specificrewardfunctionr ,wherethesubscript
f
f indicatesthetask. ThegoaloftheRLagentistomaximizetheexpecteddiscountedsumofrewards. WeadoptanonlinevariantofRL,
REINFORCE++,whichisoptimizedusingtheoriginalPPOobjective:
" #
o
L
PPO(θ)=E
q ∼ P(Q),o ∼ πθold(O | q)
1
o
X| | min (cid:0) s t(θ)A n
f
o
,
r
q
m , clip(s t(θ),1
−
ϵ,1+ϵ)A n
f
o
,
r
q
m(cid:1) , (9)
| | t=1
wheres t(θ)istheprobabilityratiobetweenthenewandoldpoliciesattimestept,andA n
f
o
,
r
q
misthenormalizedadvantage.
REINFORCE++computesthenormalizedadvantageas:
A norm = r f,q − mean( { A f,q } B) , (10)
f,q std( A
f,q
B)
{ }
wherer istheoutcomerewardforquestionq,taskf,meanandstdarecalculatedacrosstheglobalbatchwithbatchsizeB. Notethat
f,q
wedonotapplyanyKLpenaltytothelossorreward.
B.ImplementationDetails
WebuiltAbsoluteZeroReasonerupontheveRLcodebase(Shengetal.,2025). Forcodeexecution,weincorporatedcomponentsfrom
theQwQPythonexecutor. Forsafercodeexecution,werecommendusingAPI-basedservicessuchasE2Binstead.
AllexperimentswereconductedonclustersofA800GPUs.
TrainingHyperparameters. WeshowthehyperparametersusedinourtraininginTable3. Wedonotchangethemforanyof
theruns.
Parameter Value
ModelConfiguration
MaxPromptLength 6144
MaxResponseLength 8096
SeedBatchFactor 4
MaxPrograms 16384
TrainingSettings
TrainBatchSize 64*6
LearningRate 1e-6
Optimizer AdamW
GradClip 1.0
TotalSteps 500
RLSettings
Algorithm TRR++(Section3.3.5)
KLLoss False
KLReward False
EntropyCoefficient 0.001
PPOEpochs 1
N Rollouts 1
RolloutTemperature 1.0
RolloutTop-P 1.0
K References 6
N SamplestoEstimateTaskAccuracy 8
Table3. HyperparametersUsedDuringAZRSelf-playTraining.
21
--- PAGE 22 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Model DataCuration BaseModel
Oat-7B(Liuetal.,2025) 8.5kmathpairs(Hendrycksetal.,2021) Qwen2.5-7B-Math
SimpleRL-Zoo(Zengetal.,2025b) 8.5kmathpairs(Hendrycksetal.,2021) Qwen2.5-7B-Base
OpenReasonerZero(Huetal.,2025) 57kSTEM+mathsamples Qwen2.5-7B-Base
PRIME-Zero(Cuietal.,2025) 457kmath+27kcodeproblems Qwen2.5Math-7B-Base
CodeR1-Zero-7B-LC2k-1088(Liu&Zhang,2025) 2kLeetcodepairs Qwen2.5-7B-Instruct-1M
CodeR1-Zero-7B-12k-832(Liu&Zhang,2025) 2kLeetcode+10kTACOpairs(Lietal.,2023) Qwen2.5-7B-Instruct-1M
AceCoder-7B-Ins-RM(Zengetal.,2025a) 22kcodedata Qwen2.5-7B-Instruct
AceCoder-7B-Ins-Rule(Zengetal.,2025a) 22kcodedata Qwen2.5-7B-Instruct
AceCoder-7B-Code-RM(Zengetal.,2025a) 22kcodedata Qwen2.5-7B-Coder
AceCoder-7B-Code-Rule(Zengetal.,2025a) 22kcodedata Qwen2.5-7B-Coder
Qwen-7B-Instruct(Yangetal.,2024a) 1MSFT+150kRLpairs Qwen2.5-7B-Base
AZR-7B (Ours) Nodata Qwen2.5-7B-Base
AZR-7B-Coder (Ours) Nodata Qwen2.5-7B-Coder
Table4. ReasonerTrainingDataSourceandBaseModel.
logging random multiprocessing pebble subprocess
threading datetime time hashlib calendar
bcrypt os.sys os.path sys.exit os.environ
Figure8.ForbiddenPythonModules. ListofPythonmodulesforbiddentoexistinproposedtasks’programs.
C.MoreResults
C.1.Out-of-DistributionPerformanceBreakdown
Weplottheout-of-distributionperformance,brokendownbyeachbenchmarkandinaggregate,acrosstrainingstepsforour7B,7B-Coder,
14B,and14B-CodermodelsinFigures28to31. WeobserveastrongcorrelationbetweentrainingusingAZRandimprovementsinboth
mathematicalandcodingreasoningcapabilities. Moreover,ourmodelsaretrainedformorestepsthantypicalzero-stylereasoners;while
overfittingcanoccurwithstaticdatasets,itislesslikelyinAZRduetodynamicallyproposedtasks.
C.2.In-DistributionResults
Sincewehavedefinedthetaskdomainsasinputpredictionandoutputprediction,wecandirectlyevaluateourmodel’scapabilitiesinthese
areasusingpopularcodereasoningbenchmarks: CruxEval-I(nput),CruxEval-O(utput),andLiveCodeBench-Execution(LCB-E)(Gu
etal.,2024;Jainetal.,2024),whereCruxEval-OandLCB-Eissolvingthedeductiontask,andCruxEval-Iissolvingtheabductiontask.
In Figure14,wevisualizetheevolutionofthesemetricsduringthetrainingofAbsolute Zero Reasoner-base-7b. Astraining
progresses,weobserveaconsistentimprovementinin-distributionperformanceacrosssteps. Whilethesethreebenchmarkcurvesdo
notperfectlycorrelatewithbroadercodingormathreasoningcapabilities(comparethiswithFigure28),theyserveasusefulproxiesfor
trackingtask-specificprogress.
C.3.InterplayBetweenProposeandSolveRoles
WevisualizethetrainingdynamicsbetweentheproposeandsolverolesovertrainingstepsinFigures15to17. Weobservethat,in
general,thesolverolesproducemoreoutputtokensthantheproposerole. Intuitively,thismakessense: theproposeroleemphasizes
creativityandgenerationofnoveltasks,whereasthesolverolerequiresdeeperreasoning,whichnaturallyleadstolongeroutputs.
Interestingly,wealsoobserveaconsistentorderingintokenlengthacrossreasoningtypes—abductionanddeductiontaskstendtoresult
inshorteroutputsthaninductiontasksduringproblemsolving. Thisalignswithourintuition,asweobservedthemodelengaging
intrial-and-errorreasoning—repeatedlygeneratinghypothesizedinputs,evaluatingtheiroutcomes,andreflectingandretryingwhen
subsequentdeductionsfailtoproducethecorrectoutput. Toourknowledge,thisisthefirsttimesuchacleardistinctionintokenlength
1 VALIDATE_CODE_TEMPLATE = """{code}
2 repr(f({inputs}))"""
3
4 exec(VALIDATE_CODE_TEMPLATE)
Figure9. PythonProgramtoCheckValidCode.
22
--- PAGE 23 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
1 EVAL_INPUT_PREDICTION_TEMPLATE = """{code}
2 {gold_output} == f({agent_input})"""
3
4 exec(EVAL_INPUT_PREDICTION_TEMPLATE)
Figure10. PythonCodetoCheckAgentInputAbductionCorrectness.
1 EVAL_OUTPUT_PREDICTION_TEMPLATE = """{code}
2 eval({gold_output}) == eval({agent_output})"""
3
4 exec(EVAL_OUTPUT_PREDICTION_TEMPLATE)
Figure11. PythonCodetoCheckAgentOutputDeductionCorrectness.
1 EVAL_FUNCTION_PREDICTION_TEMPLATE = """{code}
2 matches = []
3 for gold_input, gold_output in zip({gold_inputs}, {gold_outputs}):
4 match = {gold_output} == f({gold_input})
5 matches.append(match)
6 """
7
8 exec(EVAL_OUTPUT_PREDICTION_TEMPLATE)
Figure12. PythonCodetoCheckAgentFunctionInductionCorrectness.
1 CHECK_DETERMINISM_TEMPLATE = """{code}
2 returns = f({inputs})
3 if returns != f({inputs}):
4 raise Exception(’Non-deterministic code’)
5 repr(returns)"""
6
7 exec(CHECK_DETERMINISM_TEMPLATE)
Figure13. PythonCodetoCheckDeterministicProgram.
0.65
0.60
0.55
0.50
0.45
0.40
0.35
0 30 60 90 120 150 180 210 240 270
Training Steps
erocS
ecnamrofreP
CruxEval-I
CruxEval-O
LiveCodeBench-Execution
Figure14.In-distribution Benchmark Score During Training. The evolution of CruxEval-I, CruxEval-O, and LiveCodeBench-
ExecutionduringtrainingfortheQwen2.5-7BbasemodeltrainedusingAZR.
23
--- PAGE 24 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
0.8
0.6
0.4
0.2
0.0
0 40 80 120 160 200 240 280
draweR
4000
3000
2000
1000
0 40 80 120 160 200 240 280
htgneL
nekoT
Solve Propose
Abduction Task Training Steps
Figure15.AbductionTaskRewardandTokenLengths. Thetaskrewardandtokenlengthsofthetworolesforabductiontasktypeof
Absolute Zero Reasoner-base-7b.
0.7
0.6
0.5
0.4
0.3
0.2
0.1
0.0
0 40 80 120 160 200 240 280
draweR
2000
1750
1500
1250
1000
750
500
250
0 40 80 120 160 200 240 280
htgneL
nekoT
Solve Propose
Induction Task Training Steps
Figure16.InductionTaskRewardandTokenLengths. Thetaskrewardandtokenlengthsofthetworolesforinductiontasktypeof
Absolute Zero Reasoner-base-7b.
24
--- PAGE 25 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
1.0
0.8
0.6
0.4
0.2
0.0
0 40 80 120 160 200 240 280
draweR
1400
1200
1000
800
600
400
0 40 80 120 160 200 240 280
htgneL
nekoT
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Solve Propose
Deduction Task Training Steps
FFiigguurree1177..DDeedduuccttiioonnTTaasskkRReewwaarrddaannddTTookkeennLLeennggtthhss..T T h h e e t t a a s s k k r r e e w w a a r r d d a a n n d d t t o o k k e e n n l l e e n n g g t t h h s s o o f f t t h h e e t t w w o o r r o o l l e e s s f f o o r r d d e e d d u u c c t t i i o o n n t t a a s s k k t t y y p p e e o o f f
AAbbssoolluuttee ZZeerroo RReeaassoonneerr--bbaassee--77bb..
Task: ProposeDeductionTask
ModelInput: (krandomselectedexampleprogramsasfew-shotinput)
ModelOutput:
ThistaskrequirescreatinganewPythonfunctionthattakesaninputandreturnsavalue. Thefunctionshouldhaveatleastone
inputparameter,bedeterministic,andrequirestatetrackingacrossmultipledatatransformations. Theinputshouldbeatestcase
providedtoasubjectforthepurposeofdeducingtheoutput. Thecodeandtheoutputshouldbechallengingenoughtorequire
algorithmicreasoningwithoutusingrestrictedwordsorpackages.
def f(prices: list[int], budget: int):
n = len(prices)
profit = [0] * n
for i in range(1, n):
for j in range(i):
if prices[i] > prices[j]:
profit[i] = max(profit[i], profit[j] + prices[i] - prices[j])
return max(profit + [profit[-1] - prices[-1] if budget >= prices[-1] else 0])
Input:[7, 1, 5, 3, 6, 4], 7
FFiigguurree1188..PArnopexoasmepDleedruescptioonnseToafskprEopxoasminpglea.d A ed n u e c x t a io m n p t l a e sk re . s k po p n ro s g e r o a f m p s ro ar p e o s s e in le g c a te d d e a d s u f c e t w io - n sh t o as t k e . xa k m p p ro le g s ra to m t s he ar m e o s d el e e l c . t T ed he as ge f n ew er - a s t h e o d t
examplestothemodel. Thegeneratednewprogramwillthenbeverifiedthroughexecution.
newprogramwillthenbeverifiedthroughexecution.
2245
--- PAGE 26 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
def f(numbers):
# Step 1: Filter out even numbers
filtered_numbers = [num for num in numbers if num % 2 != 0]
# Step 2: Calculate the sum of the remaining odd numbers
sum_of_odd_numbers = sum(filtered_numbers)
# Step 3: Reverse the order of the remaining odd numbers
reversed_odd_numbers = filtered_numbers[::-1]
# Step 4: Calculate the product of the reversed odd numbers
product_of_reversed_odd_numbers = 1
for num in reversed_odd_numbers:
product_of_reversed_odd_numbers *= num
# Step 5: Calculate the sum of the digits of the product
sum_of_digits_of_product = sum(int(digit) for digit in str(product_of_reversed_odd_numbers))
# Step 6: Modify the original list by adding the sum of the digits to each even number
# and subtracting it from each odd number
modified_numbers = []
for num in numbers:
if num % 2 == 0:
modified_numbers.append(num + sum_of_digits_of_product)
else:
modified_numbers.append(num - sum_of_digits_of_product)
# Step 7: Return the modified list
return modified_numbers
FFigiguurree1199..L L e e a a r r n n e e d d “ “ R R e e A A c c t t ” ” s s t t y y l l e e p p r r o o g g r r a a m m g g e e n n e e r r a a t t i i o o n n . . W W e e o o b b s s e e r r v v e e t t h h a a t t e e v v e e n n a a f f t t e e r r o o u u t t p p u u t t t t i i n n g gt t h h e e C C o o T T a a n n d d d d u u r r i i n n g g a a n n s s w w e e r r i i n n g g w w h h e e n n t t h h e e
answerrequiresoutputtingcode,themodelusuallyusescommentsincodetointerleaveitsthinkingand“acting”,anexampleisshown
answerrequiresoutputtingcode,themodelusuallyusescommentsincodetointerleaveitsthinkingand“acting”,anexampleisshown
h h e e r r e e fo fo r r in in d d u u c c ti t o io n n ta t s a k sk o o u u tp t u p t ut by byAbAsbosloultueteZZeerrooRReeaassoonneerr--CCooddeerr--1144bb d d u u ri r n in g g tr t a r i a n i i n n i g n . g. Si S m im ila il r a o r b o s b e s r e v r a v t a io ti n o s n a s r a e r r e e r c e e c n e tl n y tl o y b o s b er s v e e rv d e i d n
muchbiggermodels(671b)formathproofswithDeepSeekProverV2(Renetal.,2025).
inmuchbiggermodels(671b)formathproofswithDeepSeekProverV2(Renetal.,2025).
2265
--- PAGE 27 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Task: ProposeAbductionTask
ModelInput: (krandomselectedexampleprogramsasfew-shotinput)
ModelOutput:
Forthistask,weneedanintelligentproblemthatrequiresdeepalgorithmicreasoninganddemandslongmultistepreasoning. A
typicaltypeofproblemthatfitstheserequirementsistheMatrixReconstructionfromagivenjumbledlistofelements, where
wehavetofigureoutthematrix’sdimensionsandindividualvalues. However,thistaskwillinvolvemultiplesteps,increasingits
complexityandcreativity. Wewillcreateafunction‘f‘thattransformsajumbledlistofelementsintoatwo-dimensionalmatrix,
thenbacktoalist,givenaspecificreconstructionpattern. Wewillretrievetheoriginallistfromthereconstructedmatrix. Here,
wewillavoidrandomanddatetimeoperationstoensuredeterminism. Thefunctionwillbestructuredtoreconstructthematrix
elementsinauniqueandchallengingway. Wewillprovideaninputthat,afterreconstruction,presentsauniquelistthatcanbe
easilydeducedfromthegivenoutputinatestpiece.
def f(jumbled_elements: list) -> list:
if not jumbled_elements:
return []
# Calculate the square root of the length of elements to estimate the matrix dimensions
n = len(jumbled_elements)
sqrt_n = int(n ** 0.5)
if n > sqrt_n ** 2:
sqrt_n += 1
if n < sqrt_n ** 2:
sqrt_n -= 1
# Now, we need to reconstruct the matrix elements in a challenging way using specific patterns
# For this, we can reconstruct the matrix using zigzag patterns or hill-climbing patterns
# Here, a hill-climbing pattern is used where we traverse the matrix from the extreme
# right and downward Then traverse the matrix from the second to last element to the
# extreme bottom. Repeat the process until the entire matrix is traversed.
# Initialize the matrix
matrix = [[0 for _ in range(sqrt_n)] for _ in range(sqrt_n)]
cur_idx = 0
# Fill the matrix in a zigzag pattern
for i in range(sqrt_n):
if i % 2 == 0:
for j in range(sqrt_n):
matrix[j][i] = j * sqrt_n + i + cur_idx
cur_idx += 1
else:
for j in range(sqrt_n - 1, -1, -1):
matrix[j][i] = j * sqrt_n + n - i - cur_idx - 1
cur_idx += 1
# Reconstruct the list into a matrix by repeating the process
reconstructed_list = []
for i in range(sqrt_n):
for j in range(sqrt_n):
reconstructed_list.append(matrix[i][j])
# Now, we need to calculate another matrix based on a function F applied to the current matrix
# F is a mathematical function that can be complex, such as factorial, prime check,
# or another transformation For example, transform each element by adding 1 if it's prime,
# subtracting 1 if it's even, or leaving it unchanged, Implement any specific transformation
# function here. Here, for demonstration, each element is left unchanged
transformed_result = reconstructed_list
return transformed_result
Output:[0, 11, 10, 1, 5, 16, 15, 6, 10, 21, 20, 11, 15, 26, 25, 16]
Figure20.Anexampleresponseofproposinganabductiontask.Themodelusescommentsasreasoningwhengeneratingtheprogram.
Figure20.ProposeAbductionTaskExample. Anexampleresponseofproposinganabductiontask. Themodelusescommentsas
reasoningwhengeneratingtheprogram.
26
27
--- PAGE 28 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Task: ProposeInductionTask
ModelInput: Hereisthefunctionf. Analyzeitsbehaviourandrelateittotheobservedinputs/outputs.
def f(numbers: list[int]) -> int:
replacement = {
0: 2,
1: 3,
2: 1,
3: 0,
4: 4
}
return sum(replacement[num] for num in numbers)
ModelOutput:
Tosolvethistask,weneedtocomeupwith10differentinputsforthefunctionf(numbers: list[int]) -> int.Thefunction
replaces each number in the list according to a specific dictionary and then sums up the replaced numbers. Let’s analyze the
dictionary:
Thisdictionarytellsusthat:
-0becomes2
-1becomes3
-2becomes1
-3becomes0
-4remains4
To create diverse outputs, we should include lists with different sets of numbers from the list 0,1,2,3,4 . We also need
{ }
toensurethattheinputsarevalid(i.e.,listsofintegers).
Let’sillustrateusingprovidedexamplesstep-by-step:
1.Input:[0] Replacement:[2] sum=2
⇒ ⇒
2.Input:[1, 2, 3] Replacement:[3,1,0] sum=4
⇒ ⇒
3.Input:[4, 1] Replacement:[4,3] sum=7
⇒ ⇒
4.Input:[2, 1, 0] Replacement:[1,3,2] sum=6
⇒ ⇒
5.Input:[3, 0, 2, 4] Replacement:[0,2,1,4] sum=7
⇒ ⇒
6.Input:[1, 1, 1] Replacement:[3,3,3] sum=9
⇒ ⇒
7.Input:[2, 2, 0, 1] Replacement:[1,1,2,3] sum=7
⇒ ⇒
8.Input:[3, 4, 2] Replacement:[0,4,1] sum=5
⇒ ⇒
9.Input:[0, 1, 2, 3, 4] Replacement:[2,3,1,0,4] sum=10
⇒ ⇒
10.Input:[1, 2, 3, 0, 4] Replacement:[3,1,0,2,4] sum=10
⇒ ⇒
Thus,theruleisclearlyreplacingnumbersaccordingtothegivenmappingandthensummingtheresults.
Inputs:['[0]', '[1, 2, 3]', '[4, 1]', '[2, 1, 0]', '[3, 0, 2, 4]', '[1, 1, 1]',
[2, 2, 0, 1],’[3,4,2]’,’[0,1,2,3,4]’,’[1,2,3,0,4]’]|
Message: Youaregivenafunctionthattakesalistofintegersasinputandreturnsasingleintegerasoutput. Canyoudetermine
therulethisfunctionisfollowingbyanalyzingtheoutputsforthegiveninputs?
(Afterexecution:)Outputs:['2', '4', '7', '6', '7', '9', '7', '5', '10', '10']
FFigiguurere2211..PArnopeoxsaemIpnledurecstpioonnsTeaosfkpEroxpaomsipnglea.nAinndeuxcatmiopnletarseks.pGonivseenoafrparnodpoomsinsgelaencteinddpurcotgioranmta,stkh.eGmiovdeenlaneraenddsotompsreolpeocsteed10priongpruatms ,
thaendmaoddeeslcnreipetdiosntoopfrporpoogsream10. Tinhpeustesiannpdutasdweisllcrbiepteixoencouftepdroangdravme.riTfiehdesteoigneptuotsutwpuiltls.beAetxseoclvuetepdhaansed,vtheerifimeoddteolngeeetdoutotpguetns.erAattesothlvee
pphraosger,atmhegmivoednetlhneemedestsoaggeenaenrdat5eitnhpeuptr/oougtrpaumtpgaiviresn(hthaelfmisehssidadgeenancads5esi)nput/outputpairs(halfishiddencases)
2278
--- PAGE 29 ---
AAbbssoolluutteeZZeerroo::RReeiinnffoorrcceeddSSeellff--ppllaayyRReeaassoonniinnggwwiitthhZZeerrooDDaattaa
Task: SolveAbductionTask
ModelInput: Hereisthefunctionfandtheoutput: ‘1‘,pleasepredicttheinput.
def f(numbers: list[int], target: int) -> int:
accumulated_values = []
current_sum = 0
for number in numbers:
current_sum += number
accumulated_values.append(current_sum)
result = 0
for i in range(len(accumulated_values)):
for j in range(i + 1, len(accumulated_values)):
if accumulated_values[j] \
- accumulated_values[i] == target:
result = accumulated_values[i]
return result
return result
ModelOutput:
Let’sanalyzethecodesnippettounderstandhowtheresultisachieved.Thefunctionftakesalistofintegersnumbersandatargetintegertarget.Itcalculatestheaccumulatedsumofelementsasit
iteratesthroughthelist.Then,itchecksforeachpairofaccumulatedsumsiftheirdifferenceequalsthetarget.Ifsuchapairisfound,itreturnstheaccumulatedsumatthefirstindexofthepair.
Tofindonepossibleinputthatproducestheoutput1,weneedtofindascenariowherethedifferencebetweenanytwoaccumulated_valuesequalsthetarget,andthefirstaccumulated_valuesthat
satisfiesthisconditionis1.
Let’sconsiderasimpleexample:
-numbers=[1,2]
-target=1
Here’sthestep-by-stepcalculation:
1.current_sum=0+1=1,accumulated_values=[1]
2.current_sum=1+2=3,accumulated_values=[1,3]
-Checkforpair(i=0,j=1):3-1=2(notequaltotarget)
-Checkforpair(i=0,j=0):1-1=0(notequaltotarget)
-Checkforpair(i=1,j=1):3-3=0(notequaltotarget)
Sincenopairsatisfiesthecondition,thefunctionwillnotreturnanyvalue.Thisdoesn’tmatchouroutput.Let’stryanotherexample.
Consider:
-numbers=[1,0]
-target=1
Step-by-stepcalculation:
1.current_sum=0+1=1,accumulated_values=[1]
2.current_sum=1+0=1,accumulated_values=[1,1]
-Checkforpair(i=0,j=1):1-1=0(notequaltotarget)
-Checkforpair(i=0,j=0):1-1=0(notequaltotarget)
-Checkforpair(i=1,j=1):1-1=0(notequaltotarget)
...(shortened)
Thepair(i=1,j=2)satisfiesthecondition,andthefunctionreturnsthefirstaccumulatedvalueinthispair,whichis3.Thisstilldoesn’tmatchouroutput.
Giventhecomplexityandthefactthatnosimpleexampleseemstowork,let’stryamorecomplexscenario:
-numbers=[2,-1,1]
-target=1
Step-by-stepcalculation:
1.current_sum=0+2=2,accumulated_values=[2]
2.current_sum=2-1=1,accumulated_values=[2,1]
3.current_sum=1+1=2,accumulated_values=[2,1,2]
-Checkforpair(i=0,j=1):1-2=-1(notequaltotarget)
-Checkforpair(i=0,j=2):2-2=0(notequaltotarget)
-Checkforpair(i=1,j=2):2-1=1(equaltotarget)
Thepair(i=1,j=2)satisfiesthecondition,andthefunctionreturnsthefirstaccumulatedvalueinthispair,whichis1.Thismatchesouroutput.
Therefore,onepossibleinputthatcouldproducetheoutput1is:[2,-1,1],1
FFiigguurree2222..SAolmveorAebddeutacitleiodnvTerassioknEoxfatmhepelxe.amApmleosrheodwentaiinlemdavienrsFiiognuoref7th.eTehxeammopdleelshisoswonlviinngmaaninabFdiguucrtieo7n.taTshke(mproeddeicltisinspoultv)i.ngan
abductiontask(predictinput).
28
29
--- PAGE 30 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Task: SolveDeductionTask(Example1)
Model Input: Here is the function f, the input provided [1, 2, 1, 2, 3, 2, 1], 2, and we need to
predicttheoutput.
def f(array: list[int], k: int) -> int:
window_start = 0
max_length = 0
char_freq = {}
for window_end in range(len(array)):
end_char = array[window_end]
if end_char not in char_freq:
char_freq[end_char] = 0
char_freq[end_char] += 1
while len(char_freq) > k:
start_char = array[window_start]
char_freq[start_char] -= 1
if char_freq[start_char] == 0:
del char_freq[start_char]
window_start += 1
max_length = max(max_length, window_end - window_start + 1)
return max_length
Input: [1, 2, 1, 2, 3, 2, 1], 2
ModelOutput:
Basedonthegivencodesnippet,weneedtodeterminetheoutputofthefunctionf(array, k)whenprovidedwiththeinput[1, 2, 1, 2, 3, 2, 1]andk=2.
Thefunctionfisdesignedtofindthelengthofthelongestsubarraywithatmostkdistinctcharacters.Let’sgothroughthecodestepbystep:
1.Initializewindow_startto0,max_lengthto0,andanemptydictionarychar_freqtostorethefrequencyofcharactersinthecurrentwindow.
2.Iteratethroughthearrayusingwindow_endastheindex:
a.Getthecurrentcharacterend_charatindexwindow_end.
b.Ifend_charisnotinchar_freq,additwithafrequencyof0.
c.Incrementthefrequencyofend_charby1.
3.Checkifthenumberofdistinctcharactersinchar_freqisgreaterthank:
a.Iftrue,shrinkthewindowfromthestart:
i.Getthecharacterstart_charatindexwindow_start.
ii.Decrementitsfrequencyby1.
iii.Ifthefrequencybecomes0,removethecharacterfromchar_freq.
iv.Incrementwindow_startby1.
4.Updatemax_lengthbytakingthemaximumofmax_lengthandthecurrentwindowsize(window_end - window_start + 1).
Now,let’sapplythisalgorithmstep-by-steptotheinput[1, 2, 1, 2, 3, 2, 1],k=2:
•window_start = 0,max_length = 0,char_freq = {}
•window_end = 0,add1tochar_freq: 1:1 ,windowsize=1
•window_end = 1,add2: 1:1,2:1 {,windo}wsize=2
•window_end = 2,increme{nt1: 1:2,} 2:1 ,windowsize=3
•window_end = 3,increment2:{ 1:2,2:2 },windowsize=4
•window_end = 4,add3: 1:2 {,2:2,3:1 };nowdistinctchars>2,shrinkwindow:
-remove1atstart: 1:1,2 { :2,3:1 }
-remove2atstart:{ 1:1,2:1,3:1 }
-remove1again: { 2:1,3:1 ,windo}wnowstartsat3
•window_end = { 5,increment2 }: 2:2,3:1 ,windowsize=3
•window_end = 6,add1: 2:2 {,3:1,1:1 },shrinkagain:
-remove2: 2:1,3:1,1 { :1 }
-remove3:{ 2:1,1:1 ,wind}ownowstartsat5
-windowsiz{e=2 }
Maximumwindowsizeencounteredis4,thusmax_length = 4.
Hence,thefunctionreturns4.
FFigiguurere2233..SEoxlvaemDpleedoufctsioolnviTngastkheEdxeadmupctlieo.nEtxaasmk.pGleivoefnsoalvpirnoggtrhaemdaendducitnipount,tatshke.mGoivdeenlsaimpruolgartaemsthanedexinepcuutt,iothneomftohdeelpsroimgrualmateusntthile
etxheecufitnioalnroefsuthlte. programuntilthefinalresult.
3209
--- PAGE 31 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Task: SolveInductionTask
ModelInput: Giventheinputsandoutputs,deducethecodesnippetimplementingthedescribedfunction-
ality:
InputDescription: Givenalistofintegers,sortthelistandthenforeachelementatindexi,multiplyitby
(n i),wherenisthetotalnumberofelements. Computethefinalsum.
−
Inputs: ['[1, 2, 3, 4]', '[4, 3, 2, 1]', '[3, 1, 4, 2]', '[1, 2, 2, 3]', '[5]']
Outputs: ['20', '20', '20', '17', '5']
# Golden
def f(elements: list[int]) -> int:
n = len(elements)
sorted_elements = sorted(elements)
modified_elements = [sorted_elements[i] * (n - i) for i in range(n)]
return sum(modified_elements)
ModelOutput:
Giventheproblemandtheinput/outputpairs,weneedtodeduceageneralcodesnippetthattakesasortedlistandalistofmultipliers,andthencomputesafinalsumbasedonthemultipliers.
Let’sbreakdowntheproblemstepbystep:
1.**UnderstandtheMessage:**
-Wearegivenalistofintegers.
-Thelistissorted.
-Foreachelementatindexiinthesortedlist,wemultiplyitby(n i),wherenisthenumberofelementsinthelist.
-Weneedtocomputethesumoftheseproducts. −
2.**AnalyzetheInput/OutputPairs:**
-Fortheinput[1,2,3,4],thesortedformisthesame,andtheoutputis20.
1 (4 1)=3,2 (4 2)=4,3 (4 3)=3,4 (4 4)=0,Sum:3+4+3+0=10
-F·orthe−input[4,3,2 ·,1],−thesortedform·is[1 −,2,3,4],and·the−outputis20(sameasabove).
-Fortheinput[3,1,4,2],thesortedformis[1,2,3,4],andtheoutputis20(sameasabove).
shortened
3.**Re-evaluatetheOutputs:**
-Itseemstheremightbeanerrorintheprovidedoutputsforsomeinputs.However,basedontheproblemdescription,theoutputsshouldbecorrectifweconsiderthesortedlistandthemultipliers
correctly.
4.**DeducetheFunction:**
-Giventhesortedlistandthelistofmultipliers,wecandeducethefollowingfunction:
“‘python
deff(a):
sorted_a=sorted(a)
n=len(sorted_a)
result=0
foriinrange(n):
result+=sorted_a[i]*(n-i)
returnresult
...(shortened)
5.**VerifytheFunction:**
-Let’sverifythefunctionwiththeprovidedinputs:
-For[1,2,3,4]:
-Sortedlist:[1,2,3,4]
-Multipliers:[3,4,3,0]
-Sum:1 3+2 4+3 3+4 0=3+8+9+0=20
· · · ·
...(shortened)
Thecorrectedoutputsshouldbe:For[1,2,3,4]:20,For[4,3,2,1]:20,For[3,1,4,2]:20,For[1,2,2,3]:13,For[5]:0
Giventhefunction:
def f(a):
sorted_a = sorted(a)
n = len(sorted_a)
result = 0
for i in range(n):
result += sorted_a[i] * (n - i)
return result
Theoutputsmatchthecorrectedsum.
###FinalAnswer
(repeattheprogramabove)
FFigiguurree2244..SEoxlvameIpnleduofctsioolnviTngasaknEinxdaumctpiolen.tEasxka.mTphleeomfosdoellviinsggiavneninadupcrotigornamtasdke.scTrhipetimono,daenldis5giinvepnuta/opurtpougtracmasedsesacnrdipptiroend,icatntdhe5
infupnuct/toiountp.uInttcearseesstinangdly,pareftdeirctththeemfoudnecltigoinv.esInttheerefsutnincgtiloyn,,aifttewritlhlegomtohdreoluggihvethsethgeivfeunncutsieonc,asiteswoilnlegboythornoeuganhdthceongfiivremnaulsletecsatsceassoensearbey
opnaessaendd.confirmalltestcasesarepassed.
3310
--- PAGE 32 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
Model HEval+ MBPP+ LCBv1-5 AIME’24 AIME’25 AMC’23 MATH500 Minerva OlympiadBench
Llama3.1-8B 31.7 53.7 0.0 0.0 0.0 2.5 10.6 5.5 2.1
+Simple-RL-Zoo 38.4 55.3 7.4 0.0 0.0 7.5 22.2 8.8 4.7
+AZR 35.4 50.8 8.5 3.3 0.0 5.0 13.2 14.0 5.0
Qwen2.5-3B-Coder 67.1 65.9 20.0 3.3 3.3 20.0 51.0 18.4 16.6
+AZR 71.3 69.0 24.4 3.3 3.3 37.5 62.0 26.1 27.0
Qwen2.5-14B-Coder 76.8 71.7 31.4 0.0 0.0 37.5 54.8 10.7 18.5
+AZR 80.5 71.2 39.0 23.3 20.0 65.0 78.6 32.0 39.3
Qwen2.5-14B-Base 78.0 66.7 21.7 6.7 3.3 35.0 66.2 28.3 32.4
+AZR 70.7 68.8 35.2 10.0 20.0 62.5 76.2 40.4 42.5
Table5.DetailedBreakdownofEvaluationBenchmarksforOtherModelSizesandTypes. FullevaluationofAZRtrainedon
othermodelsonthreestandardcodebenchmarks(HEval+,MBPP+,LCBv1-5)andsixmathbenchmarks(AIME’24,AIME’25,AMC’23,
MATH500,Minerva,OlympiadBench).
hasbeenobservedandpresentedforjointlytrainedreasoningmulti-tasks. Previously,lengthdifferencesweretypicallynotedbetween
correctandincorrecttraces(Liuetal.,2025).
Therewarddynamicsbetweentheproposeandsolverolesexhibitmildlyadversarialbehavior: whenonereceiveshigherrewards,the
otheroftenreceiveslowerrewards. However,thisisnotentirelyadversarial,astheproposerisalsopenalizedforgeneratingunsolvable
tasks,encouragingoverallcooperativebehaviorinthelearningprocess.
C.4.ComplexityandDiversityMetricsofAZRProposedTasks
WeoutlineseveralmetricsusedtoprobecharacteristicsofthetasksproposedduringthetrainingofAZRfromthebasemodel. Specifically,
welogtwosetsofmetrics: programcomplexityandtaskdiversity. Forcomplexity,weemploytwoproxymeasures—ComplexiPyscore
andtheHalsteadmetric. Toassessdiversity,wecomputetheaverageabstractsyntaxtree(AST)editdistancebetweentheproposed
programandasetofKreferenceprograms,andananswerdiversitymetric. Wecalculatethisanswerdiversitymetricbytrackingall
historicalanswerstothegeneratedquestions,i.e.,theinput-outputpairs,andformacategoricaldistributionovertheseoutputs. We
defineanswerdiversityas1 p(answer),wherep(answer)istheempiricalprobabilityofaspecificanswer—usedasaproxyforthe
diversityofgeneratedoutput−s.
WepresentthesemetricsinFigure27. Interestingly,evenwithoutincorporatingthemexplicitlyintotherewardfunction,thepolicy
appearstoimplicitlyoptimizeforthesemetrics. Thisalignswellwithintuitivenotionsoftaskdifficultyanddiversity,andthatthepolicy
learnedtoproposeincreasinglychallengingtasksovertimeusingourproposedsimplerewardfunctioninEquation(4).
C.5.GeneratedCodeComplexityDynamicsBetweenAbd/DedandInd.
WeusetheComplexiPypackagetomeasurecodecomplexity. Foreachgeneratedprogramintheinductiontask,wecomputethe
cognitivecomplexitydifferencefromthecorresponding“gold”code,i.e.complexipy(p π{ p a ro b p d o u s c e tion,deduction} ) − complexipy(p πi s n o d lv u e ction )foreach
pair,wherethesuperscriptofπindicatestheroleandthesubscriptindicatesthetasktype(s),andpdenotesthegeneratedprograms. On
average,thedifferenceofproposerandsolverwhileholdingthecode’sfunctionalityconstantis0.27,indicatingthattheproposerinthe
abduction/deductiontasksoftenincreasesthecognitivecomplexitytomakethecodeappearmoreconvoluted,whereastheinduction
solvertendstogeneratemoreefficientimplementations.
32
--- PAGE 33 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:LearningtoReasonwithZeroDataviaSelf-play
Task: SolveDeductionTask(Example2)
ModelInput: Hereisthefunctionf,thegiveninput,andweneedtopredicttheoutput.
def f(num_list: list[int], target_product: int) -> list[tuple[int]]:
result_pairs = []
for i in range(len(num_list)):
for j in range(i + 1, len(num_list)):
if num_list[i] * num_list[j] == target_product:
result_pairs.append((num_list[i], num_list[j]))
return result_pairs
Input: [2, 3, 5, 7, 10], 10
Output: [(2, 5)]
ModelOutput:
Thegivenfunctionftakesalistofintegersnum_listandatargetproducttarget_productasinputs. Itisdesignedtofindall
pairsofnumbersinnum_listwhoseproductequalstarget_product. Thefunctionthenreturnsalistoftuples, whereeach
tuplecontainsapairofnumbersthatsatisfythecondition.
Let’sgothroughthecodestepbystep:
1.Initializeanemptylistresult_pairstostorepairswhoseproductequalstarget_product.
2. Usenestedloopstoiteratethrougheachpairofnumbersinnum_list. Theouterloopiteratesovereachnumber,andtheinner
loopiteratesovernumbersthatcomeafterthecurrentnumberintheouterloop(避免重复和反向的对子，例如(2,3)和(3,2))。
3.Foreachpair(num_list[i], num_list[j]),checkiftheirproductequalstarget_product.
4.Iftheproductequalstarget_product,appendthetuple(num_list[i], num_list[j])toresult_pairs.
5.Afterallpairshavebeenchecked,returnthelistresult_pairs.
Let’sexplicitlyapplythistotheprovidedinput[2, 3, 5, 7, 10],targetproduct10:
-Fori = 0(num_list[0]=2):
-j = 1,2×3=6(≠10)
-j = 2,2×5=10(=10),addpair(2,5)
-j = 3,2×7=14(≠10)
-j = 4,2×10=20(≠10)
-Fori = 1(num_list[1]=3):
-j = 2,3×5=15(≠10)
-j = 3,3×7=21(≠10)
-j = 4,3×10=30(≠10)
-Fori = 2(num_list[2]=5):
-j = 3,5×7=35(≠10)
-j = 4,5×10=50(≠10)
-Fori = 3(num_list[3]=7):
-j = 4,7×10=70(≠10)
-Fori = 4(num_list[4]=10):
-Nofurtherpairstocheck
Thus,theonlypairsatisfyingtheconditionis(2,5).Therefore,theoutputis:
[(2, 5)]
FFigiguurree2255..MAinxeedxaLmapnleguoafgteheCmoTodEelxasomlvpilneg. Aanndeexdaumctpiloenotfasthke. mWoedoeblsseorlvveindgthaantdthedeumctoiodenltoasuktp.uWtsemoibxseedrvlaendgtuhaagteth(eCmhiondeseel)owuthpeunts
msoixlveidnglatnhgeutaagsek.(Chinese)whensolvingthetask.
3313
--- PAGE 34 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Task: SolveDeductionTask(Llama8B)
ModelInput: Hereisthefunctionf,theinputprovided'cookie',andweneedtopredicttheoutput.
def f(input_string: str):
"""Function to check if a string contains given substrings."""
substring = "code"
# Start with `substring` as the first substring.
curr_index = substring
# Slice out the required substring from the input string
sliced_out_string = input_string[0:len(substring)]
if (sliced_out_string == substring):
return True
# Check if there are any remaining substrings to be checked
if (len(curr_index) > len(input_string)):
return False
return False
Input: 'cookie'
Output: False
ModelOutput:
Iknowthattheline‘curr_index=substring‘isjustinitializationofthecurr_indexparametervariableanddoesnotaffecttheoutput.
Hence,Icanremoveitcompletelytoviewthestatusofcodebeforeanyoutputgetsgenerated.
NowIhaveasinglevariable,“‘curr_index“‘whichissetto”codecandide”.
Thesecondlineofcode,‘slice_out_string=input_string[0:len(substring)]‘,assignstheresultofslicingtheinput_stringtotheleft
ofindex0andtotherightofindexlen(substring),i.e.,theindexofthelastcharacterinthesubstring. Itthenassignsthisvalueto
thevariable“‘slice_out_string“‘.
Thethirdline,‘if(slice_out_string==substring)‘checkswhetherthe“‘slice_out_string“‘isequaltothevalueofsubstring.Because
curr_indexissetto”codecandide”,”code”appearsontheleftofthefirstindexininput_string,andthus,“‘slice_out_string“‘assumes
thevalue”code”.Thatmeansforaminutethe“‘ifstatement“‘issatisfiedandwillcontinuethecodeexecutionafterwards.
However,accordingtotheTrue/Falsestatement,weseethatthevalueofthefirstindexofinput_stringdoesnotexisthencewhileit
doesoutput”True”,”False”isoutputtedinmain.So,theoutputofthewholeprogramisshownbelow:
‘f(input_string=”cookie”)‘thenoutput:False
FFiigguurree2266..AAbbssoolluuttee ZZeerroo RReeaassoonneerr--LLllaammaa33..11--88bb CCooTT EExxaammppllee oonn SSoollvviinngg OOuuttppuutt DDeedduuccttiioonnTTaasskk.. T T h h e e AAbbssoolluuttee ZZeerroo
RReeaassoonneerr--LLllaammaa33..11--88bb m m o od d e e l l a a p p p p e e a a r r s s to to ha h v a e ve str s e tr n e g n t g h t e h n e e n d e i d ts i s ts ta s te ta -t t r e a - c tr k a i c n k g in b g eh b av eh io a r v s io d r u s ri d n u g r t i h n e g c t o h u e rs c e o o u f rs t e ra o in f in tr g a . in W in e g i . llu W str e at i e llu an s-
trateanexamplehere.
examplehere.
ComplexiPy Score Halstead Measure AST Edit Distance Answer Diversity
0.47 0.20 0.78 0.99
0 0 0 0
0 80 160 240 0 80 160 240 0 80 160 240 0 80 160 240
Complexity Diversity
Training Steps
FFiigguurree2277..MMeettrriiccssoonnPPrrooppoosseeddTTaasskkss..WWeebbrreeaakkddoowwnntthheepprrooppoosseeddttaasskkmmeettrriiccssiinnttoopprrooggrraammccoommpplleexxiittyyaannddddiivveerrssiittyyaaccrroosssspprrooggrraammss
aannddaannsswweerrss..AAnnuuppwwaarrddttrreennddiissoobbsseerrvveeddiinnaallllmmeettrriiccss,,iinnddiiccaattiinnggtthhaattAAZZRRiimmpplliicciittllyyooppttiimmiizzeessffoorrtthheesseeqquuaalliittiieessaasstrtaraininininggpproroggreresssseess..
3345
--- PAGE 35 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
MATH REASONING
0.200
0.150
0.100
0.050
50 75 100 125 150 175 200 225 250
CODE REASONING
OVERALL PERFORMANCE
ycaruccA
AIME 2024 AIME 2025 Olympiad Bench
0.400
0.100 0.375
0.350
0.050 0.325
0.300
0.000
50 75 100 125 150 175 200 225 250 50 75 100 125 150 175 200 225 250
0.380
0.360
0.340
0.320
0.300
0.280
50 75 100 125 150 175 200 225 250
ycaruccA
Minerva Math 500 AMC 2023
0.750
0.600
0.725
0.550
0.700
0.500
0.675 0.450
0.650
0.400
0.625
50 75 100 125 150 175 200 225 250 50 75 100 125 150 175 200 225 250
0.740
0.730
0.720
0.710
0.700
50 75 100 125 150 175 200 225 250
ycaruccA
HumanEval+ MBPP+ LiveCodeBench
0.700
0.280 0.690
0.680 0.260
0.670
0.240 0.660
50 75 100 125 150 175 200 225 250 50 75 100 125 150 175 200 225 250
0.400
0.380
0.360
0.340
0.320
0.300
50 75 100 125 150 175 200 225 250
ycaruccA
Math Average Code Average Overall Average
0.570 0.480
0.560 0.460
0.550
0.440
0.540
0.420
50 75 100 125 150 175 200 225 250 50 75 100 125 150 175 200 225 250
Figure28. AbsoluteZeroReasoner-base-7bOODPerformanceBreakdown.
35
--- PAGE 36 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
MATH REASONING
0.200
0.150
0.100
0.050
50 100 150 200 250 300 350
CODE REASONING
OVERALL PERFORMANCE
ycaruccA
AIME 2024 AIME 2025 Olympiad Bench
0.100
0.080 0.380
0.060
0.040 0.360
0.020
0.000 0.340
50 100 150 200 250 300 350 50 100 150 200 250 300 350
0.375
0.350
0.325
0.300
0.275
50 100 150 200 250 300 350
ycaruccA
Minerva Math 500 AMC 2023
0.600
0.760
0.550 0.740
0.500 0.720
0.700 0.450
0.680 0.400
50 100 150 200 250 300 350 50 100 150 200 250 300 350
0.850
0.840
0.830
0.820
0.810
50 100 150 200 250 300 350
ycaruccA
HumanEval+ MBPP+ LiveCodeBench
0.720 0.320
0.710
0.300
0.700
0.690 0.280
0.680
0.260
50 100 150 200 250 300 350 50 100 150 200 250 300 350
0.400
0.380
0.360
0.340
0.320
50 100 150 200 250 300 350
ycaruccA
Math Average Code Average Overall Average
0.630
0.620 0.500
0.610 0.480
0.600
0.590 0.460
0.580
50 100 150 200 250 300 350 50 100 150 200 250 300 350
Figure29. AbsoluteZeroReasoner-Coder-7bOODPerformanceBreakdown.
36
--- PAGE 37 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
MATH REASONING
0.200
0.150
0.100
50 100 150 200 250 300 350
CODE REASONING
OVERALL PERFORMANCE
ycaruccA
AIME 2024 AIME 2025 Olympiad Bench
0.200
0.400
0.150
0.350
0.100
0.300
0.050
0.250
50 100 150 200 250 300 350 50 100 150 200 250 300 350
0.400
0.350
0.300
50 100 150 200 250 300 350
ycaruccA
Minerva Math 500 AMC 2023
0.750 0.600
0.700
0.550
0.650
0.600 0.500
0.550 0.450
0.500
50 100 150 200 250 300 350 50 100 150 200 250 300 350
0.780
0.760
0.740
0.720
0.700
50 100 150 200 250 300 350
ycaruccA
HumanEval+ MBPP+ LiveCodeBench
0.720
0.710 0.360
0.700
0.340
0.690
0.680 0.320
50 100 150 200 250 300 350 50 100 150 200 250 300 350
0.400
0.350
0.300
50 100 150 200 250 300 350
ycaruccA
Math Average Code Average Overall Average
0.620
0.610 0.500
0.600 0.480
0.590
0.460
0.580
0.570 0.440
50 100 150 200 250 300 350 50 100 150 200 250 300 350
Figure30. AbsoluteZeroReasoner-base-14bOODPerformanceBreakdown.
37
--- PAGE 38 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
MATH REASONING
0.200
0.150
0.100
50 100 150 200 250 300 350 400
CODE REASONING
OVERALL PERFORMANCE
ycaruccA
AIME 2024 AIME 2025 Olympiad Bench
0.440
0.200
0.150 0.420
0.100 0.400
0.050 0.380
50 100 150 200 250 300 350 400 50 100 150 200 250 300 350 400
0.400
0.350
0.300
0.250
0.200
50 100 150 200 250 300 350 400
ycaruccA
Minerva Math 500 AMC 2023
0.800 0.700
0.780 0.650
0.760 0.600
0.740
0.550
0.720
0.500
0.700
50 100 150 200 250 300 350 400 50 100 150 200 250 300 350 400
0.820
0.800
0.780
50 100 150 200 250 300 350 400
ycaruccA
HumanEval+ MBPP+ LiveCodeBench
0.740 0.400
0.730 0.380
0.720
0.360
0.710
0.340
0.700
50 100 150 200 250 300 350 400 50 100 150 200 250 300 350 400
0.440
0.420
0.400
0.380
0.360
0.340
50 100 150 200 250 300 350 400
ycaruccA
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Math Average Code Average Overall Average
0.650 0.540
0.640
0.520
0.630
0.620 0.500
0.610
50 100 150 200 250 300 350 400 50 100 150 200 250 300 350 400
FFiigguurree3311.. AAbbssoolluutteeZZeerrooRReeaassoonneerr--CCooddeerr--1144bbOOOODDPPeerrffoorrmmaanncceeBBrreeaakkddoowwnn..
<think>
Design an absolutely ludicrous and convoluted Python function that is extremely difficult to
! deduce the output from the input, designed to keep machine learning models such as Snippi
→
! guessing and your peers puzzling.
→
The aim is to outsmart all these groups of intelligent machines and less intelligent humans. This
! is for the brains behind the future.
→
</think>
- Absolute Zero Reasoner-Llama3.1-8b @ step 132
FFiigguurree3322..AAbbssoolluutteeZZeerrooRReeaassoonneerr––LLllaammaa33..11--88BB““UUhh--oohhMMoommeenntt..””T T h h i i s s e e x x a a m m p p l l e e h h i i g g h h l l i i g g h h t t s s a a n n u u n n e e x x p p e e c c t t e e d d a a n n d d p p o o t t e e n n t t i i a a l l l l y y u u n n s s a a f f e e
r r e e a a s s o o n ni i n n g gc ch h a a i i n ng ge e n n e e r r a a t t e e d d b b y y o o u u r rAAbbssoolluuttee ZZeerroo RReeaassoonneerr––LLllaammaa33..11--88BBm m o o d d e e l l d d u u r r i i n n g g t t r r a a i i n n i i n n g g . . A Al l t t h h o o u u g g h h o o u u r r p p a a r r a a d d i i g g m m e e n n a a b b l l e e s s
reasoningimprovementswithouthuman-curateddata,itmaystillrequireoversightduetotheriskofemergentundesirablebehaviors.
reasoningimprovementswithouthuman-curateddata,itmaystillrequireoversightduetotheriskofemergentundesirablebehaviors.
38
39
--- PAGE 39 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
A conversation between User and Assistant. The user asks a question, and the Assistant solves it.
! The assistant first thinks about the reasoning process in the mind and then provides the user
→
! with the answer. The reasoning process and answer are enclosed within <think> </think> and
→
! <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> <answer>
→
! answer here </answer>.
→
User: {TASK_INSTRUCTION}
Assistant: <think>
FFiigguurree3333.. DDeeeeppsseeeekkRR11TTeemmppllaattee..A A l l l l o o u u r r m m o o d d e e l l s s w w e e r r e e t t r r a a i i n n e e d d u u s s i i n n g g t t h h e e d d e e f f a a u u l l t t D D e e e e p p s s e e e e k k R R 1 1 t t e e m m p p l l a a t t e e . .
4309
--- PAGE 40 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
## Task: Create a Python Code Snippet (where custom classes are allowed, which should be defined
! at the top of the code snippet) with one Matching Input
→
Using the reference code snippets provided below as examples, design a new and unique Python code
! snippet that demands deep algorithmic reasoning to deduce one possible input from a given
→
! output. Your submission should include both a code snippet and test input pair, where the
→
! input will be plugged into the code snippet to produce the output, which that function output
→
! be given to a test subject to come up with any input that will produce the same function
→
! output. This is meant to be an I.Q. test.
→
### Code Requirements:
- Name the entry function `f` (e.g., `def f(...): ...`), you can have nested definitions inside
! `f`
→
- Ensure the function returns a value
- Include at least one input parameter
- Make the function deterministic
- Make the snippet require state tracking across multiple data transformations, ensuring the task
! requires long multi step reasoning
→
- AVOID THE FOLLOWING:
* Random functions or variables
* Date/time operations
* I/O operations (reading files, network requests)
* Printing or logging
* Any external state
- Ensure execution completes within 10 seconds on a modern CPU
- All imports and class definitions should be at the very top of the code snippet
- The snippet should end with a return statement from the main function `f`, anything after will
! be removed
→
### Input Requirements:
- Provide exactly one test input for your function
- Format multiple arguments with commas between them
- Remember to add quotes around string arguments
### Formatting:
- Format your code with: ```python
def f(...):
# your code here
return ...
```
- Format your input with: ```input
arg1, arg2, ...
```
### Example Format:
```python
def f(name: str, info: dict):
# code logic here
return result
```
```input
'John', {{'age': 20, 'city': 'New York'}}
```
### Evaluation Criteria:
- Executability, your code should be executable given your input
- Difficulty in predicting the output from your provided input and code snippet. Focus on either
! algorithmic reasoning or logic complexity. For example, you can define complex data structure
→
! classes and operate on them like trees, heaps, stacks, queues, graphs, etc, or use complex
→
! control flow, dynamic programming, recursions, divide and conquer, greedy, backtracking, etc
→
- Creativity, the code needs to be sufficiently different from the provided reference snippets
- Restricted usage of certain keywords and packages, you are not allowed to use the following
! words in any form, even in comments: {LIST_OF_FORBIDDEN_PACKAGES}
→
First, carefully devise a clear plan: e.g., identify how your snippet will be challenging,
! distinct from reference snippets, and creative. Then, write the final code snippet and its
→
! inputs.
→
### Reference Code Snippets:
{CODE_REFERENCES_FROM_BUFFER}
Figure34. ProgramInputAbductionTask—ProblemProposalInstruction.
Figure34. ProgramInputAbductionTask—ProblemProposalInstruction.
4410
--- PAGE 41 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
## Task: Create a New Python Code Snippet (where custom classes are allowed, which should be
! defined at the top of the code snippet) with one Matching Input
→
Using the reference code snippets provided below as examples, design a new and unique Python code
! snippet that demands deep algorithmic reasoning to deduce the output from the input. Your
→
! submission should include a code snippet and a test input pair, where the input will be
→
! plugged into the code snippet to produce the output. The input will be given to a test
→
! subject to deduce the output, which is meant to be an I.Q. test.
→
### Code Requirements:
- Name the entry function `f` (e.g., `def f(...): ...`), you can have nested definitions inside
! `f`
→
- Ensure the function returns a value
- Include at least one input parameter
- Make the function deterministic
- Make the snippet require state tracking across multiple data transformations, ensuring the task
! requires long multi step reasoning
→
- AVOID THE FOLLOWING:
* Random functions or variables
* Date/time operations
* I/O operations (reading files, network requests)
* Printing or logging
* Any external state
- Ensure execution completes within 10 seconds on a modern CPU
- All imports and class definitions should be at the very top of the code snippet
- The snippet should end with a return statement from the main function `f`, anything after will
! be removed
→
### Input Requirements:
- Provide exactly one test input for your function
- Format multiple arguments with commas between them
- Remember to add quotes around string arguments
### Formatting:
- Format your code with:
```python
def f(...):
# your code here
return ...
```
- Format your input with:
```input
arg1, arg2, ...
```
### Example Format:
```python
def f(name: str, info: dict):
# code logic here
return result
```
```input
'John', {{'age': 20, 'city': 'New York'}}
```
### Evaluation Criteria:
- Executability, your code should be executable given your input
- Difficulty in predicting your ```input``` from 1) your ```python``` code and 2) the
! deterministic ```output``` that will be obtained from your ```input```. Focus on either
→
! algorithmic reasoning or logic complexity. For example, you can define complex data structure
→
! classes and operate on them like trees, heaps, stacks, queues, graphs, etc, or use complex
→
! control flow, dynamic programming, recursions, divide and conquer, greedy, backtracking, etc
→
- Creativity, the code needs to be sufficiently different from the provided reference snippets
- Restricted usage of certain keywords and packages, you are not allowed to use the following
! words in any form, even in comments: {LIST_OF_FORBIDDEN_PACKAGES}
→
First, carefully devise a clear plan: e.g., identify how your snippet will be challenging,
! distinct from reference snippets, and creative. Then, write the final code snippet and its
→
! inputs.
→
### Reference Code Snippets:
{CODE_REFERENCES_FROM_BUFFER}
FFiigguurree3355.. PPrrooggrraammOOuuttppuuttDDeedduuccttiioonnTTaasskk——PPrroobblleemmGGeenneerraattiioonnIInnssttrruuccttiioonn..
4421
--- PAGE 42 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
## Task: Output {NUM_INPUTS} Inputs that can be plugged into the following Code Snippet to
! produce diverse Outputs, and give a message related to the given snippet.
→
Using the code snippet provided below, design {NUM_INPUTS} inputs that can be plugged into the
! code snippet to produce a diverse set of outputs. A subset of your given input and its
→
! deterministically produced outputs will be given to a test subject to deduce the function,
→
! which is meant to be an I.Q. test. You can also leave a message to the test subject to help
→
! them deduce the code snippet.
→
### Input Requirements:
- Provide {NUM_INPUTS} valid inputs for the code snippet
- For each input, format multiple arguments with commas between them
- Remember to add quotes around string arguments
- Each input should be individually wrapped in ```input``` tags
### Message Requirements:
- Leave a message to the test subject to help them deduce the code snippet
- The message should be wrapped in ```message``` tags
- The message can be in any form, can even be formed into a coding question, or a natural
! language instruction what the code snippet does
→
- You cannot provide the code snippet in the message
### Formatting:
- Format your input with:
```input
arg1, arg2, ...
```
### Example Format:
```input
'John', {{'age': 20, 'city': 'New York'}}
```
```input
'Sammy', {{'age': 37, 'city': 'Los Angeles'}}
```
### Evaluation Criteria:
- Executability, your code should be executable given your inputs
- Coverage, the inputs and outputs should cover the whole input space of the code snippet, able
! to deduce the code snippet from the inputs and outputs
→
- Creativity, the inputs need to be sufficiently different from each other
- The overall selection of inputs and message combined should be challenging for the test
! subject, but not impossible for them to solve
→
First, carefully devise a clear plan: e.g., understand the code snippet, then identify how your
! proposed inputs have high coverage, and why the inputs will be challenging and creative.
→
! Then, write the inputs and message. Remember to wrap your inputs in ```input``` tags, and
→
! your message in ```message``` tags.
→
### Code Snippet:
```python
{SNIPPET_FROM_BUFFER}
```
F F i i g g u u r r e e 3 3 6 6 . . PPrrooggrraammIInndduuccttiioonnTTaasskk— —PPrroobblleemmPPrrooppoossaallIInnssttrruuccttiioonn..
4423
--- PAGE 43 ---
AAbbssoolluutteeZZeerroo::RReeiinnffoorrcceeddSSeellff--ppllaayyRReeaassoonniinnggwwiitthhZZeerrooDDaattaa
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
# Task: Provide One Possible Input of a Python Code Snippet Given the Code and Output
# Task: Provide One Possible Input of a Python Code Snippet Given the Code and Output
Given the following Code Snippet and the Output, think step by step then provide one possible
Given the following Code Snippet and the Output, think step by step then provide one possible
! !→ i i n n p p u u t t t t h h a a t t p p r r o o d d u u c c e e d d t t h h e e o o u u t t p p u u t t . . T T h h e e i i n n p p u u t t n n e e e e d d s s t t o o b b e e w w r r a a p p p p e e d d i i n n ` ` ` ` ` ` i i n n p p u u t t ` ` ` ` ` ` t t a a g g s s . . R R e e m m e e m m b b e e r r
! !→ → i i f f a a n n a a r r g g u u m m e e n n t t i i s s a a s s t t r r i i n n g g , , w w r r a a p p i i t t i i n n q q u u o o t t e e s s . . I I f f t t h h e e f f u u n n c c t t i i o o n n r r e e q q u u i i r r e e s s m m u u l l t t i i p p l l e e a a r r g g u u m m e e n n t t s s , ,
! !→ → s s e e p p a a r r a a t t e e t t h h e e m m w w i i t t h h c c o o m m m m a a s s . .
→
# Code Snippet:
# Code Snippet:
```python
```python
{SNIPPET}
{SNIPPET}
```
```
# Output:
# Output:
```output
```output
{OUTPUT}
{OUTPUT}
```
```
# Output Format:
# Output Format:
```input
```input
arg1, arg2, ...
arg1, arg2, ...
```
```
# Example Output:
# Example Output:
```input
```input
'John', {{'age': 20, 'city': 'New York'}}
'John', {{'age': 20, 'city': 'New York'}}
```
```
FF Fii igg guu urr ree e33 377 7.. . PPP rrr ooo ggg rrr aaa mmm III n nnp ppu uut ttA AAb bbd ddu uuc cct tti iio oon nnT TTa aas ssk kk— ——P PPr rro oob bbl lle eem mmS SSo ool llv vvi iin nng ggP PPr rro oom mmp ppt tt. ..
# Task: Deduce the Output of a Python Code Snippet Given the Code and Input
# Task: Deduce the Output of a Python Code Snippet Given the Code and Input
Given the following Code Snippet and the Input, think step by step then deduce the output that
Given the following Code Snippet and the Input, think step by step then deduce the output that
! !→ w w i i l l l l b b e e p p r r o o d d u u c c e e d d f f r r o o m m p p l l u u g g g g i i n n g g t t h h e e I I n n p p u u t t i i n n t t o o t t h h e e C C o o d d e e S S n n i i p p p p e e t t . . P P u u t t y y o o u u r r o o u u t t p p u u t t i i n n
! !→ → ` ` ` ` ` ` o o u u t t p p u u t t ` ` ` ` ` ` t t a a g g s s . . R R e e m m e e m m b b e e r r i i f f t t h h e e o o u u t t p p u u t t i i s s a a s s t t r r i i n n g g , , w w r r a a p p i i t t i i n n q q u u o o t t e e s s . . I I f f t t h h e e f f u u n n c c t t i i o o n n
! !→ → r r e e t t u u r r n n s s m m u u l l t t i i p p l l e e v v a a l l u u e e s s , , r r e e m m e e m m b b e e r r t t o o u u s s e e a a t t u u p p l l e e t t o o w w r r a a p p t t h h e e m m . .
→
# Code Snippet:
# Code Snippet:
```python
```python
{SNIPPET}
{SNIPPET}
```
```
# Input:
# Input:
```input
```input
{INPUT}
{INPUT}
```
```
# Example Output:
# Example Output:
```output
```output
{{'age': 20, 'city': 'New York'}}
{{'age': 20, 'city': 'New York'}}
```
```
Figure38. ProgramOutputDeductionTask—ProblemSolvingPrompt.
FFiigguurree3388.. PPrrooggrraammOOuuttppuuttDDeedduuccttiioonnTTaasskk——PPrroobblleemmSSoollvviinnggPPrroommpptt..
444 434
--- PAGE 44 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
# Task: Deduce the Function that Produced the Outputs from the Inputs
Given a set of input/output pairs and a message that describes the function, think through the
! problem step by step to deduce a general code snippet. This code should produce the hidden
→
! outputs from the hidden inputs, matching the original data-generating code that created the
→
! input/output pairs. Place your final answer inside python tags! It may be helpful to work
→
! through each input/output pair individually to test your function. If your function doesn’t
→
! work as expected, revise it until it does. The final code snippet will be used to evaluate
→
! your response, which is wrapped in ```python``` tags.
→
# Code Requirements:
- Name the entry function `f` (e.g., `def f(...): ...`), you can have nested definitions inside
! `f`
→
- Ensure the function returns a value
- Include at least one input parameter
- Make the function deterministic
- AVOID THE FOLLOWING:
* Random functions or variables
* Date/time operations
* I/O operations (reading files, network requests)
* Printing or logging
* Any external state
- Ensure execution completes within 10 seconds on a modern CPU
- All imports and class definitions should be at the very top of the code snippet
- The snippet should end with a return statement from the main function `f()`, anything after
! will be removed
→
# Input and Output Pairs:
{INPUT_OUTPUT_PAIRS}
# Message:
```message
{MESSAGE}
```
# Example Output:
```python
def f(a):
return a
```
Name your entry function `f()`!!!
Figure39. ProgramInductionTask—ProblemSolvingPrompt.
Figure39. ProgramInductionTask—ProblemSolvingPrompt.
45
44
--- PAGE 45 ---
AAbbssoolluutteeZZeerroo::RReeiinnffoorrcceeddSSeellff--ppllaayyRReeaassoonniinnggwwiitthhZZeerrooDDaattaa
Task: ManualConstructedSudokuAbductionTask
Model Input: Here is the function f and the output: [ [”5”,”3”,”.”,”.”,”7”,”.”,”.”,”.”,”.”],
[”6”,”.”,”.”,”1”,”9”,”5”,”.”,”.”,”.”], [”.”,”9”,”8”,”.”,”.”,”.”,”.”,”6”,”.”], [”8”,”.”,”.”,”.”,”6”,”.”,”.”,”.”,”3”],
[”4”,”.”,”.”,”8”,”.”,”3”,”.”,”.”,”1”], [”7”,”.”,”.”,”.”,”2”,”.”,”.”,”.”,”6”], [”.”,”6”,”.”,”.”,”.”,”.”,”2”,”8”,”.”],
[”.”,”.”,”.”,”4”,”1”,”9”,”.”,”.”,”5”],[”.”,”.”,”.”,”.”,”8”,”.”,”.”,”7”,”9”]],pleasepredicttheinput.
import random
from typing import List
import copy
def g_0(board: List[List[str]]) -> bool:
def is_valid_block(block):
return sorted(block) == [str(i) for i in range(1, 10)]
for row in board:
if not is_valid_block(row):
return False
for col in zip(*board):
if not is_valid_block(col):
return False
for box_row in range(0, 9, 3):
for box_col in range(0, 9, 3):
block = [
board[r][c]
for r in range(box_row, box_row + 3)
for c in range(box_col, box_col + 3)
]
if not is_valid_block(block):
return False
return True
def f(board: List[List[str]]) -> List[List[str]]:
for i in range(9):
for j in range(9):
if board[i][j] == ".":
raise ValueError("Invalid board")
num_to_mask = 51
if not g_0(board):
raise ValueError("Invalid board")
masked_board = copy.deepcopy(board)
all_positions = [(i, j) for i in range(9) for j in range(9)]
random.shuffle(all_positions)
for i, j in all_positions[:num_to_mask]:
masked_board[i][j] = "."
return masked_board
45
ModelOutput:
To determine the input that produced the given output, we need to analyze the function ‘f‘ and understand
whatitdoes. Let’sbreakdownthefunctionstepbystep:
1. **InputValidationin‘g_0‘**: -Thefunction‘g_0‘checksifthegivenboardisvalid. -Aboardisvalid
ifeachrow,eachcolumn,andeach3x3subgridcontainsthedigits1through9exactlyonce. -Iftheboard
isnotvalid,‘g_0‘returns‘False‘.
2. **InputValidationin‘f‘**: -Thefunction‘f‘firstchecksifthereareany”.”intheboard. Ifthereare,it
raisesa‘ValueError‘. -Thismeanstheinputboardto‘f‘mustbeacompleteboardwithno”.”.
3. **MaskingtheBoard**: -Iftheboardisvalid(i.e.,‘g_0(board)‘returns‘True‘),thefunction‘f‘proceeds
tomasktheboard. -Itcreatesacopyoftheboardandthenrandomlyselects51positionstomaskwith”.”.
-Themaskedboardisthenreturned.
Giventheoutput: “‘output[[”5”,”3”,”.”,”.”,”7”,”.”,”.”,”.”,”.”],[”6”,”.”,”.”,”1”,”9”,”5”,”.”,”.”,”.”],[”.”,”9”,”8”,”.”,”.”,”.”,”.”,”6”,”.”],[”8”,”.”,”.”,”.”,”6”,”.”,”.”,”.”,”3”],[”4”,”.”,”.”,”8”,”.”,”3”,”.”,”.”,”1”],[”7”,”.”,”.”,”.”,”2”,”.”,”.”,”.”,”6”],[”.”,”6”,”.”,”.”,”.”,”.”,”2”,”8”,”.”],[”.”,”.”,”.”,”4”,”1”,”9”,”.”,”.”,”5”],[”.”,”.”,”.”,”.”,”8”,”.”,”.”,”7”,”9”]]
“‘Wecanseethattheboardhas51positionsmaskedwith”.”. Therefore,theinputboardmusthavebeena
completeboard,and‘f‘masked51randompositions.
Let’sconstructacompleteboardthat,whenmasked,canproducethegivenoutput. Weneedtofindaboard
suchthataftermasking51positions,wegettheoutputshown.
Here is a complete board that matches the structure of the output: “‘python [ [”5”, ”3”, ”4”, ”6”, ”7”, ”8”,
”9”,”1”,”2”],[”6”,”7”,”2”,”1”,”9”,”5”,”3”,”4”,”8”],[”1”,”9”,”8”,”3”,”4”,”2”,”5”,”6”,”7”],[”8”,
”5”,”9”,”7”,”6”,”1”,”4”,”2”,”3”],[”4”,”2”,”6”,”8”,”5”,”3”,”7”,”9”,”1”],[”7”,”1”,”3”,”9”,”2”,
”4”,”8”,”5”,”6”],[”9”,”6”,”1”,”5”,”3”,”7”,”2”,”8”,”4”],[”2”,”8”,”7”,”4”,”1”,”9”,”6”,”3”,”5”],
[”3”,”4”,”5”,”2”,”8”,”6”,”1”,”7”,”9”]]“‘
Let’sverifythatthisboard,whenpassedtothefunction‘f‘,producesthegivenoutput.
1. Theboard is complete withno ”.”. 2. Theboard is validbecause eachrow, eachcolumn, and each3x3
subgridcontainsthedigits1through9exactlyonce. 3. Thefunction‘f‘willmask51randompositions.
Given the random nature of the masking, we need to ensure that the positions masked match the positions
of”.”intheoutput. Sincethepositionsarerandom,wecanassumethatthefunction‘f‘withthiscomplete
boardwillproducethegivenoutput.
Therefore,theinputthatproducedtheoutputis: “‘input[[”5”,”3”,”4”,”6”,”7”,”8”,”9”,”1”,”2”],[”6”,
”7”,”2”,”1”,”9”,”5”,”3”,”4”,”8”],[”1”,”9”,”8”,”3”,”4”,”2”,”5”,”6”,”7”],[”8”,”5”,”9”,”7”,”6”,
”1”,”4”,”2”,”3”],[”4”,”2”,”6”,”8”,”5”,”3”,”7”,”9”,”1”],[”7”,”1”,”3”,”9”,”2”,”4”,”8”,”5”,”6”],
[”9”,”6”,”1”,”5”,”3”,”7”,”2”,”8”,”4”],[”2”,”8”,”7”,”4”,”1”,”9”,”6”,”3”,”5”],[”3”,”4”,”5”,”2”,
”8”,”6”,”1”,”7”,”9”]]“‘
Figure40. VibeCheck1:Sudoku.
--- PAGE 46 ---
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Task: ManualConstructedSudokuAbductionTask
Model Input: Here is the function f and the output: [ [”5”,”3”,”.”,”.”,”7”,”.”,”.”,”.”,”.”],
[”6”,”.”,”.”,”1”,”9”,”5”,”.”,”.”,”.”], [”.”,”9”,”8”,”.”,”.”,”.”,”.”,”6”,”.”], [”8”,”.”,”.”,”.”,”6”,”.”,”.”,”.”,”3”],
[”4”,”.”,”.”,”8”,”.”,”3”,”.”,”.”,”1”], [”7”,”.”,”.”,”.”,”2”,”.”,”.”,”.”,”6”], [”.”,”6”,”.”,”.”,”.”,”.”,”2”,”8”,”.”],
[”.”,”.”,”.”,”4”,”1”,”9”,”.”,”.”,”5”],[”.”,”.”,”.”,”.”,”8”,”.”,”.”,”7”,”9”]],pleasepredicttheinput.
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
ModelOutput:
To determine the input that produced the given output, we need to analyze the function ‘f‘ and understand
whatitdoes. Let’sbreakdownthefunctionstepbystep:
1. **InputValidationin‘g_0‘**: -Thefunction‘g_0‘checksifthegivenboardisvalid. -Aboardisvalid
ifeachrow,eachcolumn,andeach3x3subgridcontainsthedigits1through9exactlyonce. -Iftheboard
isnotvalid,‘g_0‘returns‘False‘.
2. **InputValidationin‘f‘**: -Thefunction‘f‘firstchecksifthereareany”.”intheboard. Ifthereare,it
raisesa‘ValueError‘. -Thismeanstheinputboardto‘f‘mustbeacompleteboardwithno”.”.
3. **MaskingtheBoard**: -Iftheboardisvalid(i.e.,‘g_0(board)‘returns‘True‘),thefunction‘f‘proceeds
tomasktheboard. -Itcreatesacopyoftheboardandthenrandomlyselects51positionstomaskwith”.”.
-Themaskedboardisthenreturned.
Given the output: “‘output [[”5”,”3”,”.”,”.”,”7”,”.”,”.”,”.”,”.”], [”6”,”.”,”.”,”1”,”9”,”5”,”.”,”.”,”.”],
[”.”,”9”,”8”,”.”,”.”,”.”,”.”,”6”,”.”], [”8”,”.”,”.”,”.”,”6”,”.”,”.”,”.”,”3”], [”4”,”.”,”.”,”8”,”.”,”3”,”.”,”.”,”1”],
[”7”,”.”,”.”,”.”,”2”,”.”,”.”,”.”,”6”], [”.”,”6”,”.”,”.”,”.”,”.”,”2”,”8”,”.”], [”.”,”.”,”.”,”4”,”1”,”9”,”.”,”.”,”5”],
[”.”,”.”,”.”,”.”,”8”,”.”,”.”,”7”,”9”]]“‘Wecanseethattheboardhas51positionsmaskedwith”.”. Therefore,
theinputboardmusthavebeenacompleteboard,and‘f‘masked51randompositions.
Let’sconstructacompleteboardthat,whenmasked,canproducethegivenoutput. Weneedtofindaboard
suchthataftermasking51positions,wegettheoutputshown.
Here is a complete board that matches the structure of the output: “‘python [ [”5”, ”3”, ”4”, ”6”, ”7”, ”8”,
”9”,”1”,”2”],[”6”,”7”,”2”,”1”,”9”,”5”,”3”,”4”,”8”],[”1”,”9”,”8”,”3”,”4”,”2”,”5”,”6”,”7”],[”8”,
”5”,”9”,”7”,”6”,”1”,”4”,”2”,”3”],[”4”,”2”,”6”,”8”,”5”,”3”,”7”,”9”,”1”],[”7”,”1”,”3”,”9”,”2”,
”4”,”8”,”5”,”6”],[”9”,”6”,”1”,”5”,”3”,”7”,”2”,”8”,”4”],[”2”,”8”,”7”,”4”,”1”,”9”,”6”,”3”,”5”],
[”3”,”4”,”5”,”2”,”8”,”6”,”1”,”7”,”9”]]“‘
Let’sverifythatthisboard,whenpassedtothefunction‘f‘,producesthegivenoutput.
1. The board is complete with no ”.”. 2. The board is validbecause each row, each column, and each3x3
subgridcontainsthedigits1through9exactlyonce. 3. Thefunction‘f‘willmask51randompositions.
Given the random nature of the masking, we need to ensure that the positions masked match the positions
of”.”intheoutput. Sincethepositionsarerandom,wecanassumethatthefunction‘f‘withthiscomplete
boardwillproducethegivenoutput.
Therefore,theinputthatproducedtheoutputis: “‘input[[”5”,”3”,”4”,”6”,”7”,”8”,”9”,”1”,”2”],[”6”,
”7”,”2”,”1”,”9”,”5”,”3”,”4”,”8”],[”1”,”9”,”8”,”3”,”4”,”2”,”5”,”6”,”7”],[”8”,”5”,”9”,”7”,”6”,
”1”,”4”,”2”,”3”],[”4”,”2”,”6”,”8”,”5”,”3”,”7”,”9”,”1”],[”7”,”1”,”3”,”9”,”2”,”4”,”8”,”5”,”6”],
[”9”,”6”,”1”,”5”,”3”,”7”,”2”,”8”,”4”],[”2”,”8”,”7”,”4”,”1”,”9”,”6”,”3”,”5”],[”3”,”4”,”5”,”2”,
”8”,”6”,”1”,”7”,”9”]]“‘
Figure40.VibeCheck1: SudokuSolver. Weca F s i t gu S r u e d 4 o 0 k . u V so ib lv e in C g he a c s k an 1: ab S d u u d c o t k io u n . task: ourprogramstartsfromafullysolvedand
validatedSudokuboardandsimulatesthemaskingof51randomcells. Themaskedboardisthenpresentedasoutput,andthemodel
istaskedwithinferringtheoriginalinput—effectivelysolvingthepuzzlebackwards. Anexamplesolutionisshownabove,where
AZR-Coder-14bverifiesitsinitialguessbeforecorrectlyanswering. Generationparameters: temperature=0.6.
46
46
--- PAGE 47 ---
AAbbsosoluluteteZZereor:o:RReienifnofrocrecdedSeSlfe-lpf-lpaylaRyeRaesoansoinnginwgitwhitZherZoerDoaDtaata
Task: ManuallyConstructedSumProductGameSolveAbductionTask
ModelInput: Hereisthefunctionfandtheoutput: ‘True‘,pleasepredicttheinput.
def g_3():
return [(x, y) for x in range(2, 101) for y in range(x + 1, 101) if x + y <= 100]
def g_2(pairs):
d = {}
for (x, y) in pairs:
s = x + y
d.setdefault(s, []).append((x, y))
return d
def g_1(pairs):
d = {}
for (x, y) in pairs:
p = x * y
d.setdefault(p, []).append((x, y))
return d
def g_0():
pairs = g_3()
sum_dict = g_2(pairs)
prod_dict = g_1(pairs)
allowed_sums = [s for s, candidate_pairs in sum_dict.items() \
if all(len(prod_dict[x * y]) > 1 for (x, y) in candidate_pairs)]
candidates_after_S = [pair for pair in pairs if pair[0] + pair[1] in allowed_sums]
new_prod_dict = g_1(candidates_after_S)
unique_products = {prod: pair_list for prod, pair_list in new_prod_dict.items() \
if len(pair_list) == 1}
final_candidates = []
for s in allowed_sums:
valid_pairs = [pair for pair in candidates_after_S \
if pair[0] + pair[1] == s and (pair[0] * pair[1]) in unique_products]
if len(valid_pairs) == 1:
final_candidates.append(valid_pairs[0])
return sorted(set(final_candidates))
def f(x, y):
if not (isinstance(x, int) and isinstance(y, int)):
return False
if x <= 1 or y <= 1 or y <= x or (x + y) > 100:
return False
final_candidates = g_0()
return len(final_candidates) == 1 and final_candidates[0] == (x, y)
47
ModelOutput:
Let’sanalyzethecodesnippettounderstandhowtheresultisachieved.Thefunctionftakesalistofintegersnumbersandatargetintegertarget.Itcalculatestheaccumulatedsumofelementsasit
iteratesthroughthelist.Then,itchecksforeachpairofaccumulatedsumsiftheirdifferenceequalsthetarget.Ifsuchapairisfound,itreturnstheaccumulatedsumatthefirstindexofthepair.
Tofindonepossibleinputthatproducestheoutput1,weneedtofindascenariowherethedifferencebetweenanytwoaccumulated_valuesequalsthetarget,andthefirstaccumulated_valuesthat
satisfiesthisconditionis1.
Let’sconsiderasimpleexample:
-numbers=[1,2]
-target=1
Here’sthestep-by-stepcalculation:
1.current_sum=0+1=1,accumulated_values=[1]
2.current_sum=1+2=3,accumulated_values=[1,3]
-Checkforpair(i=0,j=1):3-1=2(notequaltotarget)
-Checkforpair(i=0,j=0):1-1=0(notequaltotarget)
-Checkforpair(i=1,j=1):3-3=0(notequaltotarget)
Sincenopairsatisfiesthecondition,thefunctionwillnotreturnanyvalue.Thisdoesn’tmatchouroutput.Let’stryanotherexample.
Consider:
-numbers=[1,0]
-target=1
Step-by-stepcalculation:
1.current_sum=0+1=1,accumulated_values=[1]
2.current_sum=1+0=1,accumulated_values=[1,1]
-Checkforpair(i=0,j=1):1-1=0(notequaltotarget)
-Checkforpair(i=0,j=0):1-1=0(notequaltotarget)
-Checkforpair(i=1,j=1):1-1=0(notequaltotarget)
...(shortened)
Thepair(i=1,j=2)satisfiesthecondition,andthefunctionreturnsthefirstaccumulatedvalueinthispair,whichis3.Thisstilldoesn’tmatchouroutput.
Giventhecomplexityandthefactthatnosimpleexampleseemstowork,let’stryamorecomplexscenario:
-numbers=[2,-1,1]
-target=1
Step-by-stepcalculation:
1.current_sum=0+2=2,accumulated_values=[2]
2.current_sum=2-1=1,accumulated_values=[2,1]
3.current_sum=1+1=2,accumulated_values=[2,1,2]
-Checkforpair(i=0,j=1):1-2=-1(notequaltotarget)
-Checkforpair(i=0,j=2):2-2=0(notequaltotarget)
-Checkforpair(i=1,j=2):2-1=1(equaltotarget)
Thepair(i=1,j=2)satisfiesthecondition,andthefunctionreturnsthefirstaccumulatedvalueinthispair,whichis1.Thismatchesouroutput.
Therefore,onepossibleinputthatcouldproducetheoutput1is:[2,-1,1],1
Figure41.SolveAbductionTaskExample. AmoredetailedversionoftheexampleshowninmainFigure7. Themodelissolvingan
abductiontask(predictinput).
--- PAGE 48 ---
AbsoluteZero:ReinforcedSelf-playReasoningwithZeroData
Task: ManuallyConstructedSumProductGameSolveAbductionTask
ModelInput: Hereisthefunctionfandtheoutput: ‘True‘,pleasepredicttheinput.
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
ModelOutput:
thefunction‘f(x,y)‘returns‘True‘ifthefollowingconditionsaremet:
1.‘x‘and‘y‘arebothintegers.
2.‘x‘and‘y‘satisfy‘1<x<y‘and‘x+y<=100‘.
3.Thefinalcandidatesafterapplyingtheconditionsin‘g_0‘areexactlyonepair,andthatpairis‘(x,y)‘.
Let’sanalyzethefunction‘g_0‘stepbysteptounderstandwhatitdoes:
1.‘pairs=g_3()‘generatesallpairs‘(x,y)‘suchthat‘2<=x<y<=100‘and‘x+y<=100‘.
2.‘sum_dict=g_2(pairs)‘createsadictionarywherethekeysarethesumsofthepairsandthevaluesarelistsofpairsthataddup
tothatsum.
3. ‘prod_dict=g_1(pairs)‘createsadictionarywherethekeysaretheproductsofthepairsandthevaluesarelistsofpairsthat
havethatproduct.
4. ‘allowed_sums=[sfors, candidate_pairsinsum_dict.items()ifall(len(prod_dict[x*y])>1for(x, y)incandidate_pairs)]‘
filtersoutthesums‘s‘forwhichallpairs‘(x,y)‘thataddupto‘s‘havemorethanonepairwiththesameproduct.
5. ‘candidates_after_S = [pair for pair in pairs if pair[0] + pair[1] in allowed_sums]‘ filters out the pairs whose sum is in
‘allowed_sums‘.
6.‘new_prod_dict=g_1(candidates_after_S)‘createsanewproductdictionaryusingthefilteredpairs.
7. ‘unique_products = prod: pair_list for prod, pair_list in new_prod_dict.items() if len(pair_list) == 1‘ creates a dictionary of
productsthathaveexactlyonepair.
8.‘final_candidates=[]‘initializesalistoffinalcandidates.
9. Theloop‘forsinallowed_sums:‘iteratesovereachsumin‘allowed_sums‘andchecksifthereisexactlyonepair‘(x,y)‘such
that‘x+y=s‘and‘x*y‘isin‘unique_products‘.Ifso,thispairisaddedto‘final_candidates‘.
10. Thefunctionreturns‘sorted(set(final_candidates))‘, whichshouldbealistcontainingexactlyonepairiftheconditionsare
met.
Giventhattheoutputis‘True‘,itmeansthatthereisexactlyonepair‘(x,y)‘thatsatisfiesalltheconditions.Let’sidentifythispair.
Todothis,weneedtounderstandthemathematicalconditionsandconstraints:
1.‘1<x<y‘and‘x+y<=100‘.
2. Thepair‘(x,y)‘mustbetheonlypairwhosesumisin‘allowed_sums‘andwhoseproductisuniqueamongthepairswiththe
samesum.
Byanalyzingtheconstraintsandthenatureoftheproblem,wecandeducethatthepair‘(x,y)‘mustbesuchthatthesum‘x+y‘is
uniqueinthesensethatallpairsaddinguptothissumhaveproductsthatarenotunique,exceptforthispair.
Afterdetailedanalysisorbyrunningthecodewithdifferentvalues,wefindthatthepair‘(x,y)=(4,13)‘satisfiesalltheconditions.
Let’sverify:
1.‘1<4<13‘and‘4+13=17<=100‘.
2.Weneedtocheckthe‘allowed_sums‘andtheuniquenessoftheproduct‘4*13=52‘.
Byrunningthecodeorlogicaldeduction,wecanconfirmthat‘(4,13)‘istheonlypairthatsatisfiesalltheconditions.
Therefore,theinputthatproducedtheoutput‘True‘is:
“‘input
(4,13)
“‘
FFiigguurree4411..VSoiblveeCAhbedckuc2t:ioSnuTmasPkroEdxuacmtpGlae.mAe.m W o e re ca d s e t ta th il e ed Su v m er - s P io ro n d o u f ct th G e a e m xa e m as pl a e n s a h b o d w u n ct i i n on m t a a i s n k, F w ig h u e r r e e 7 w . h T e h n e th m e o c d o e n l s i t s ra s i o n l t v s i o n f g t a h n e
g a a b m du e c a ti r o e n sa ta ti s s k fie (p d r , e f d u i n c c t ti i o n n pu ‘ t f ) ‘ . returnsTrue,returnFalseotherwise. 4 A 8 ZR-Coder-14bwasabletofirstanalyzethecomplicatedconstraints,
identifycandidatesolutionandverify. Generationparameters: temperature=0.6,top_p=0.95.
48
--- PAGE 49 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
D.AlternativeApproachesConsidered
Inthissection,wesharemanyoftheapproacheswetriedthatdidnotprovetobeparticularlyhelpfulforAbsoluteZeroReasoner.
However,webelieveitisespeciallyvaluabletosharethesefindingswiththecommunity,astheyarecrucialforguidingfutureresearch.
Below,weoutlineeachoftheadditionalmethodsweexploredduringthedevelopmentofourproject.
D.1.ErrorDeductionTask
Sinceprogramminglanguagesoftenhaveerrormessages,andthesemessagescontainalotofinformationabouthowsomeonemight
expectaprogramtorun,wealsocameupwithanothertaskdomain: allowingthelearnertoproposeaprogramthatwillproducean
error,andrequiringthesolvertodeducewhatkindoferrorisraisedwhenexecutingthiscode. Weexperimentedwiththisadditional
taskalongsidetheinduction(f), deduction(o), andabduction(i)tasks. Unfortunately, wedidnotobservenoticeablechangesin
downstreamperformancewiththisadditionaltaskandsinceitrequiresmorecomputationalresourcesthanourAZRsetup,wedecided
nottoincorporateitintoourfinalversion. However,webelievefurtherthoroughinvestigationofthisiswelldeserved.
D.2.CompositeFunctionsasCurriculumLearning
Onevaluablepropertywecanleveragefromprogramminglanguagesistheabilitytocomposefunctions—thatis,todefineafunctionas
acompositeofotherfunctions,i.e.,f(g(x)). Inoursetting,whengeneratingaprogram,wecannotonlyrequiretheoutputtobeavalid
programbutalsoconstraintheLLMtoutilizeapredefinedsetofprogramswithinitsmainfunction. Forexample,ifthetargetprogram
tobegeneratedisf(),wecansampleasetofpreviouslygeneratedprograms g_0,...,g
c
from ,andforceavalidprogramtobe
f(g_0, ,g
c
,i). · { } D
···
SinceallprogramsaregeneratedbytheLLMitself,thissetupallowsthemodeltobootstrapfromitsearliergenerations,automatically
increasingthecomplexityofthegeneratedprograms. Weinterpretthismechanismasaformofcurriculumlearning: earlierprograms
intheAZRself-playlooptendtobesimpler,andastheloopprogresses,theybecomeincreasinglycomplex. Bycomposingnewer
programsfromprogressivelymoredifficultearlierones,theresultingprogramsnaturallyinheritthisgrowingdifficulty,whichinturn
challengesthesolverstep.
Forimplementation,ingeneratingtasksforabductionanddeduction,webeginbysamplingabinarydecisionfromabinomialdistribution
withp=0.5. Thisdetermineswhetherthegeneratedprogramshouldbeasimpleprogramoracompositeone. Ifthesampleis0,we
prompttheLLMtogenerateastandardprogramalongwithacorrespondinginput. Ifthesampleis1,weprompttheLLMtogeneratea
compositeprogram. Toconstructthecomposite,wefirstsampleanintegerc (1,3),thenuniformlyselectcprogramsfromthe
dataset thatarenotthemselvescompositeprograms. Finally,wepromptth∼eLULMtogenerateavalidprogramthatincorporates
g_0,.. D .,g
c
assubcomponents,ensuringitcomposestheseselectedprogramsmeaningfully. Weadditionallyfilterprogramsthatdid
{notutilizeall}thecprograms.
However,wedidnotobserveasignificantdifferencewhenusingthismorecomplexcurriculumcomparedtooursimplerandmore
effectiveapproach. Onefailuremodeweencounteredwasthatthemodeloftendefaultedtosimplyreturning“g(x)”,effectivelylearning
f(g(x))=g(x),whichfailedtointroduceanyadditionaldifficulty. Thistrivialbehaviorunderminedtheintendedchallenge,leadingus
todeprioritizefurtherexplorationinthisdirection. Whileitmaybepossibletodesignastricterrewardmechanism—suchasenforcing
f(g(x))=g(x)byexecutingthecodeviaaPythoninterpreterandpenalizingsuchshortcuts—weleavethistofuturework.
̸
D.3.ToyingwiththeInitialp(z)
Weinvestigatedasettingwheretheinitialseedbuffer(seeSection3.3.1onhowwegeneratedthese),i.e.p(z)inEquation(3),isnot
self-generatedbythebasemodel,butinsteadsourcedfromtheLeetCodeDataset. WeonlymodifiedthiscomponentandranAZR
usingthesameprocedureasbefore,continuingtoaddnewvalidprogramstotheinitializedbuffer. Weobservedanincreaseininitial
performanceoncodingbenchmarks; however,theperformanceplateauedatroughlythesamelevelafteradditionaltrainingsteps,
comparedtoourofficialAZRsetup. Interestingly,mathperformancewaslowerthanintheofficialAZRsetup,pointingtowardsthat
on-policydatamaybemorebeneficialtothelearnertobootstrapfromformathematicalreasoning. Webelievethatexploringdifferent
strategiesforinitializingandupdatingp(z)isanimportantandexcitingdirectionforfutureresearch. Webrieflyexploreddifferent
strategiesforsamplingreferencecode,ultimatelysettlingonuniformsamplingforitssimplicity,thoughwealsoexperimentedwith
recency-basedsamplingandobservedpotentialcollapse.
D.4.ExtraRewards
ComplexityRewards. Codecomplexityiswellstudiedinsoftwarescienceandcouldpotentiallybeagoodproxyformeasuring
howharditistoinferthepropertiesofapieceofcodeforourreasoninglearner. Therefore,fortheproblemproposer,wecanaddvarious
measuresofcomplexity—suchasCyclomaticComplexity(Ebertetal.,2016),maintainability,etc.—totherewardfunctiontoincentivize
theproposertoproducemorecomplexprograms. Forillustrationpurposes,wetriedusingtheMaintainabilitymeasureandtheHalstead
49
--- PAGE 50 ---
AbsoluteZero: ReinforcedSelf-playReasoningwithZeroData
complexitymeasure(Halstead,1977)asintrinsicrewards. Concretely,weusedthecomplexipyandRadonpackages(Lopez,2025;
Canal,2023)toimplementtherespectivemetrics. ThesearethenservedasintrinsicrewardsduringtheAZRself-playphase.
DiversityRewards. Wealsoattemptedusingdiversityrewardsto. InspiredbyDiveR-CT(Zhaoetal.,2025a),weincorporate
codeeditdistanceasanintrinsicreward. Specifically,wetreatthereferenceprogramsshowninthepromptasanchorsandcomputethe
averagecodeeditdistancebetweenthegeneratedprogramandtheseanchors. Thisservesasameasureofdiversityinthegenerated
output. Additionally,weexploredanotherdiversity-basedrewardinspiredbythenotionofsurprise(Zhaoetal.,2022). Inthisapproach,
weconstructaprobabilitydistributionoverpreviouslyencounteredinput/outputpairsthatthesolverhasanswered. Therewardisthen
definedas1 p(input/output),wherepdenotestheempiricalprobabilityofaparticularinputoroutput. Whilebothstrategieswere
evaluatedin−ourexperiments,wedidnotobserveasignificantdifferenceinperformance. However,webelievethisaspectwarrants
deeperinvestigation,asdiversityrewardsremainapromisingavenueforstrengtheningAZRfurther.
RewardAggregation. Wetestedseveralwaysonhowtocombinerewardsfortheproposeranddiscriminator. First,weseparate
therewardintoextrinsicrewardrextrinsic andasetofintrinsicreward(s)I = r i ,andtestedthefollowingstrategiestocombinethem
intoasinglereward, { }
I
X| |
r=rextrinsic+ r
i
, (11)
i
I
X| |
r=rextrinsic r
i
, (12)
·
i
I
Y| |
r=rextrinsic r
i
, (13)
·
i
I
Y| |
r=rextrinsic+ r
i
. (14)
i
Wefoundthatthesimpleadditivewayofcombiningrewards,a.k.aEquation(11),producedthemoststableruns,possiblyduetoless
variance.
D.5.EnvironmentTransition
Weinvestigatedhowthetransitionfunctioninourcodingenvironmentfortheproposer. Specifically,aftergeneratingapieceofcode,we
canapplyatransformationfunctiononitbeforegivingitmakingitanvalidtupleinourdataset. Weinvestigatedtwo
Removing Comments and Docstrings Inearlyiterationsofourexperiments,wenoticedthatcommentsanddocstrings
weresometimesusedtoexplicitlyoutlinewhatthefunctionwasdoing,orevenservedasapartial“note-taking”interleaved“ReAct”
process(Yaoetal.,2023)ofgeneratingcode—thatis,themodelcouldinterleavethinkandactionatthesametime,andtomakethe
generatedcodevalid,itusedcommentstoencaseitsthoughts(AppendixC.3),similarlyobservedinDeepSeek-Prover-V2:(Renetal.,
2025). Wethenthoughtthattomakethetaskharderforthesolver,weshouldoccludethisinformationfromit. However,weobserved
asignificantperformancedropafterremovingallcommentsanddocstrings. Oneexplanationforthisphenomenonisthattheonly
“communication”channelbetweentheproposerandthesolverisrestrictedtothecodeitself,ratherthansomekindof“message”along
withthecode. Thesemessagescanpotentiallyprovidehintstothesolver,thusmakingsomeotherwiseimpossibletaskssolvable. Asa
result,thesolverisabletolearnfromitsexperienceandself-bootstrapoutofcertainunsolvabletasks.
RemovingGlobalVariables. Weobservedthatsomeprogramscontaingloballydeclaredvariablesthatmayinadvertentlyleak
informationaboutthecorrectanswer—thisissueisparticularlyprevalentintheinputinductiontaskgenerationandsolving. Initially,we
wereconcernedthatsuchleakagemightleadtowastedcomputationontrivialorcompromisedexamples. Toaddressthis,wedeveloped
asystematicproceduretoremovegloballydeclaredvariablesfromthegeneratedprograms.
However,afterapplyingthiscleaningstep,weobservedanoticeabledropinperformanceonourself-playreasoningtasks. Onepossible
explanationisthatthegenerationstepisunawareofthispost-processingmodification;sincetherewardisassignedafterthetransition
function(whichincludesvariableremoval),themodelmaynotlearneffectivelyfromthismismatch.
Moreover,webelievethatevenwhenanswersarepresent,thesolverstillengagesinnontrivialreasoningtoreachasolution,potentially
benefitingfromthisexposure. ThisalignswiththeideaofrationalizationasproposedinSTaR(Zelikmanetal.,2022),wherethemodel
pretendstonotseetheanswerbutstillperformsreasoningduringlearning. Therefore,inourfinalexperiments,wechoosenottoremove
globallydeclaredvariables,allowingtheself-playlooptonaturallyincorporateandadapttosuchcases.
50