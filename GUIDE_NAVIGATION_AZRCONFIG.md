# 🧭 GUIDE DE NAVIGATION AZRCONFIG

## 🎯 **COMMENT UTILISER LA NOUVELLE STRUCTURE AZRCONFIG**

Ce guide vous aide à naviguer efficacement dans la nouvelle structure organisée d'AZRConfig.

---

## 🔍 **RECHERCHE RAPIDE PAR BESOIN**

### **🎯 JE VEUX MODIFIER...**

#### **📊 Les valeurs de base (0.0, 1.0, 0.5)**
➡️ **SECTION A - VALEURS DE BASE ET RÉFÉRENCES**
- `zero_value`, `one_value`, `half_value`
- `correlation_player_value`, `correlation_banker_value`
- `default_return_value`, `default_confidence_value`

#### **🎯 Les seuils de confiance**
➡️ **SECTION B - SEUILS DE CONFIANCE ET PROBABILITÉ**
- `confidence_minimum_05_percent`, `confidence_threshold_10_percent`
- `probability_significant_threshold`, `probability_strong_threshold`

#### **🔍 Les seuils de détection de signaux**
➡️ **SECTION C - SEUILS DE CORRÉLATION ET SIGNAUX**
- `significance_threshold_5_percent`, `strength_threshold_60_percent`
- `signal_strength_base`, `signal_minimum_strength`

#### **⚖️ Les poids et pondérations**
➡️ **SECTION D - FACTEURS DE PONDÉRATION ET POIDS**
- `weight_40_percent`, `weight_30_percent`, `weight_20_percent`

#### **📏 Les distances et proximité**
➡️ **SECTION E - DISTANCES ET PROXIMITÉ**
- `distance_adjacent`, `distance_close`, `distance_moderate`
- `context_value_adjacent`, `context_value_close`

#### **🎲 Les facteurs de normalisation**
➡️ **SECTION F - FACTEURS DE NORMALISATION**
- `normalization_factor_2`, `normalization_factor_10`
- `multiplier_increment_01`, `multiplier_increment_02`

#### **📈 Les seuils de qualité**
➡️ **SECTION G - SEUILS DE QUALITÉ ET PERFORMANCE**
- `quality_minimum_20_percent`, `quality_threshold_30_percent`

#### **🔢 Les tailles d'échantillons**
➡️ **SECTION H - TAILLES D'ÉCHANTILLONS ET LONGUEURS**
- `sample_size_minimum_2`, `sample_size_optimal_20`
- `length_threshold_2`, `length_threshold_3`

#### **⏱️ Les paramètres temporels**
➡️ **SECTION I - PARAMÈTRES TEMPORELS ET RÉCENCE**
- `recent_weight_factor`, `temporal_weight_factor`

#### **📊 Les paramètres de variance**
➡️ **SECTION J - VARIANCE ET ÉCART-TYPE**
- `variance_multiplier_10`, `variance_threshold_minimum`

#### **🎁 Les bonus et malus**
➡️ **SECTION K - BONUS ET MALUS**
- `bonus_factor_15_percent`, `malus_factor_15_percent`

#### **🎯 Les paramètres d'attention**
➡️ **SECTION L - ATTENTION ET RARETÉ**
- `attention_level_base`, `attention_level_max`
- `rarity_factor_high_str`, `rarity_factor_ultra_high_str`

---

## 🎯 **RECHERCHE PAR FONCTIONNALITÉ**

### **🔍 ANALYSE DES BIAIS**
- **Section A** : Valeurs de corrélation (`correlation_impair_value`, `correlation_pair_value`)
- **Section C** : Seuils de détection (`significance_threshold_5_percent`)
- **Section L** : Facteurs d'attention (`attention_level_base`)

### **🎲 GÉNÉRATION DE PRÉDICTIONS**
- **Section B** : Seuils de confiance (`confidence_threshold_25_percent`)
- **Section G** : Seuils de qualité (`quality_threshold_60_percent`)
- **Section H** : Tailles d'échantillons (`sample_size_optimal_20`)

### **📊 CALCULS STATISTIQUES**
- **Section F** : Facteurs de normalisation (`normalization_factor_10`)
- **Section J** : Variance et écart-type (`variance_threshold_minimum`)
- **Section I** : Paramètres temporels (`recent_weight_factor`)

### **🎯 ÉVALUATION DE PERFORMANCE**
- **Section K** : Bonus et malus (`bonus_factor_15_percent`)
- **Section G** : Seuils de qualité (`quality_threshold_80_percent`)
- **Section B** : Seuils de probabilité (`probability_very_strong_threshold`)

---

## 📋 **CONVENTIONS DE NOMMAGE**

### **🎯 STRUCTURE DES NOMS**
```
[catégorie]_[type]_[valeur]_[unité]

Exemples :
- confidence_threshold_25_percent
- weight_40_percent
- sample_size_minimum_2
- normalization_factor_10
```

### **📊 CATÉGORIES PRINCIPALES**
- `confidence_` : Paramètres de confiance
- `weight_` : Facteurs de pondération
- `threshold_` : Seuils de décision
- `factor_` : Facteurs multiplicatifs
- `value_` : Valeurs de référence
- `size_` : Tailles et longueurs

### **🔢 UNITÉS COMMUNES**
- `_percent` : Valeurs en pourcentage (0.25 = 25%)
- `_factor` : Facteurs multiplicatifs
- `_minimum` : Valeurs minimales
- `_maximum` : Valeurs maximales
- `_base` : Valeurs de base

---

## 🛠️ **BONNES PRATIQUES**

### **✅ MODIFICATION SÉCURISÉE**
1. **Identifier la section** concernée par votre modification
2. **Lire la documentation** de la section
3. **Comprendre l'impact** du paramètre
4. **Modifier avec précaution** en gardant les valeurs cohérentes
5. **Tester** après modification

### **📚 AJOUT DE NOUVEAUX PARAMÈTRES**
1. **Identifier la section appropriée** selon la fonction
2. **Suivre les conventions de nommage**
3. **Ajouter la documentation** appropriée
4. **Respecter l'ordre logique** dans la section

### **🔍 RECHERCHE EFFICACE**
- Utilisez **Ctrl+F** avec les mots-clés de section
- Cherchez par **icône** (📊, 🎯, 🔍, etc.)
- Utilisez les **numéros de section** (A, B, C, etc.)

---

## 🎯 **EXEMPLES D'UTILISATION**

### **Exemple 1 : Modifier un seuil de confiance**
```python
# Aller à SECTION B - SEUILS DE CONFIANCE ET PROBABILITÉ
config.confidence_threshold_25_percent = 0.3  # Augmenter de 25% à 30%
```

### **Exemple 2 : Ajuster un facteur de pondération**
```python
# Aller à SECTION D - FACTEURS DE PONDÉRATION ET POIDS
config.weight_40_percent = 0.45  # Augmenter le poids de 40% à 45%
```

### **Exemple 3 : Modifier une taille d'échantillon**
```python
# Aller à SECTION H - TAILLES D'ÉCHANTILLONS ET LONGUEURS
config.sample_size_optimal_20 = 25  # Augmenter la taille optimale
```

---

## 🚀 **NAVIGATION RAPIDE**

### **🔗 LIENS DIRECTS VERS LES SECTIONS**
- **A** 📊 Valeurs de base → Ligne ~243
- **B** 🎯 Confiance → Ligne ~275
- **C** 🔍 Corrélation → Ligne ~295
- **D** ⚖️ Pondération → Ligne ~322
- **E** 📏 Distance → Ligne ~340
- **F** 🎲 Normalisation → Ligne ~360
- **G** 📈 Qualité → Ligne ~384
- **H** 🔢 Échantillons → Ligne ~400
- **I** ⏱️ Temporel → Ligne ~423
- **J** 📊 Variance → Ligne ~436
- **K** 🎁 Bonus → Ligne ~449
- **L** 🎯 Attention → Ligne ~465

---

## 🏆 **RÉSUMÉ**

La nouvelle structure d'AZRConfig vous permet de :

✅ **Naviguer rapidement** vers la bonne section  
✅ **Comprendre facilement** l'organisation  
✅ **Modifier en sécurité** les paramètres  
✅ **Maintenir la cohérence** du système  

🎯 **Utilisez ce guide** pour exploiter pleinement la puissance de la nouvelle structure organisée d'AZRConfig !
