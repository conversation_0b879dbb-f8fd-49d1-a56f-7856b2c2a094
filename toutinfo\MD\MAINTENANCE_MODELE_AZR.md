# 🧠 GUIDE DE MAINTENANCE - MODÈLE AZR

## 🎯 **OBJECTIF DE CE GUIDE**

Ce guide permet d'identifier et de maintenir **UNIQUEMENT** les parties du code qui concernent le **modèle AZR** dans `azr_baccarat_predictor.py`, séparément du reste du programme.

## 🔍 **DÉLIMITATIONS CLAIRES DU MODÈLE AZR**

### ⚠️ **SECTIONS CRITIQUES - CŒUR AZR**

#### 📋 **1. CONFIGURATION AZR (Lignes 126-159)**
```python
# 🧠 AZR - HYPERPARAMÈTRES DU MODÈLE CŒUR
# ⚠️ SECTION CRITIQUE - MODÈLE AZR PRINCIPAL
```
**Contenu :**
- `learning_rate`, `adaptation_rate`, `baseline_alpha`
- `confidence_threshold`, `base_confidence`, `pattern_bonus`
- `n_rollouts`, `rollout_temperature`, `rollout_step_size`
- `pattern_min_length`, `max_sequence_length`, `memory_buffer_size`
- `learnability_buffer_size`, `diversity_buffer_size`

**Impact :** Modification = Performance directe du modèle AZR

#### 🎯 **2. RÈGLES DE PRÉDICTION AZR (Lignes 162-200)**
```python
# 🎯 AZR - RÈGLES DE PRÉDICTION DÉCOUVERTES (INDEX COMBINÉ)
# ⚠️ SECTION CRITIQUE - INTELLIGENCE AZR RÉVOLUTIONNAIRE
```
**Contenu :**
- `combined_prediction_rules` : Règles IMPAIR_SYNC, PAIR_SYNC, etc.
- `combined_confidence_threshold`, `combined_moderate_threshold`
- `probability_clamp_min/max`, `probability_neutral`

**Impact :** Modification = Changement des prédictions révolutionnaires

#### 🧠 **3. CLASSE PRINCIPALE AZR (Lignes 936-3100)**
```python
# 🧠 4. CLASSE PRINCIPALE AZR - CŒUR DU MODÈLE
# ⚠️ SECTION ULTRA-CRITIQUE - MODÈLE AZR COMPLET
```

### 🔧 **MÉTHODES CŒUR AZR À MAINTENIR**

#### 🎯 **Prédiction par Index Combiné (Lignes 1086-1194)**
```python
def _predict_with_combined_index(self) -> Dict[str, Any]:
def _merge_predictions(self, combined_pred, classic_pred) -> str:
```
**Fonction :** Application des règles révolutionnaires découvertes

#### 🔮 **Génération Classique AZR (Lignes 1196-1224)**
```python
def _generate_prediction(self) -> str:
```
**Fonction :** Méthode AZR traditionnelle (Proposeur → Résolveur)

#### 🎭 **Rôle Proposeur AZR (Lignes 1215-1481)**
```python
def _propose_hypotheses(self) -> List[Dict[str, Any]]:
def _analyze_sync_patterns(self) -> Optional[Dict[str, Any]]:
def _analyze_parity_patterns(self) -> Optional[Dict[str, Any]]:
def _analyze_recent_patterns(self) -> Optional[Dict[str, Any]]:
def _generate_rollout_hypotheses(self) -> List[Dict[str, Any]]:
def _single_rollout(self, temperature, random_factor) -> Optional[Dict[str, Any]]:
def _calculate_learnability(self, hypothesis) -> float:
```
**Fonction :** Génération d'hypothèses avec métriques de learnability

#### 🔧 **Rôle Résolveur AZR (Lignes 1483-1568)**
```python
def _solve_best_hypothesis(self, hypotheses) -> Dict[str, Any]:
def _finalize_prediction(self, hypothesis) -> str:
def _update_baselines(self, hypothesis):
```
**Fonction :** Sélection et scoring des meilleures hypothèses

#### 📊 **Apprentissage Adaptatif AZR (Lignes 1570-1620)**
```python
def _adaptive_learning(self):
```
**Fonction :** Auto-amélioration des paramètres AZR

### 🧠 **VARIABLES D'ÉTAT AZR CRITIQUES**

#### Dans `__init__` (Lignes 973-983) :
```python
# 🧠 MÉTRIQUES AZR CŒUR - ÉTAT INTERNE DU MODÈLE
self.baseline_propose = 0.0
self.baseline_solve = 0.0
self.learnability_scores = deque(maxlen=self.config.learnability_buffer_size)
self.diversity_scores = deque(maxlen=self.config.diversity_buffer_size)
```

#### Variables de performance :
```python
self.discovered_patterns = {}
self.pattern_success_rates = defaultdict(list)
self.total_predictions = 0
self.correct_predictions = 0
self.current_accuracy = 0.0
```

## 🚫 **SECTIONS NON-AZR (À ÉVITER LORS MAINTENANCE AZR)**

### ❌ **Générateur Baccarat (Lignes 283-434)**
- `BaccaratGenerator` : Génération de parties
- **Rôle :** Fournit des données au modèle AZR
- **Maintenance :** Séparée du modèle AZR

### ❌ **Interface Graphique (Lignes 597-933)**
- `AZRBaccaratInterface` : Interface utilisateur
- **Rôle :** Affiche les prédictions AZR
- **Maintenance :** Séparée du modèle AZR

### ❌ **Data Loader (Lignes 435-596)**
- `BaccaratDataLoader` : Chargement de données
- **Rôle :** Alimente le modèle AZR
- **Maintenance :** Séparée du modèle AZR

### ❌ **Fonctions Utilitaires (Lignes 3100+)**
- Fonctions de création, démonstration, main
- **Rôle :** Orchestration générale
- **Maintenance :** Séparée du modèle AZR

## 🔧 **PROCÉDURE DE MAINTENANCE AZR**

### ✅ **1. IDENTIFICATION**
- Chercher les sections avec `🧠 AZR` et `⚠️ SECTION CRITIQUE`
- Se concentrer sur les lignes 126-159, 162-200, 936-3100
- Ignorer tout ce qui n'a pas de marquage AZR explicite

### ✅ **2. MODIFICATION SÉCURISÉE**
- **TOUJOURS** modifier dans `AZRConfig` d'abord
- **JAMAIS** de valeurs codées en dur dans les méthodes AZR
- **TESTER** avec des données de validation après modification

### ✅ **3. VALIDATION**
- Vérifier que `baseline_propose` et `baseline_solve` évoluent
- Contrôler que `learnability_scores` et `diversity_scores` se remplissent
- Valider que les prédictions utilisent bien les nouvelles règles

### ✅ **4. DOCUMENTATION**
- Mettre à jour `ARCHITECTURE_REFERENCE.md`
- Noter les changements dans les commentaires des sections critiques
- Conserver l'historique des modifications AZR

## 🎯 **POINTS D'INTERVENTION AZR PRINCIPAUX**

### 🔧 **Modification des Hyperparamètres**
**Fichier :** `AZRConfig` lignes 126-159
**Impact :** Performance globale du modèle

### 🎯 **Modification des Règles de Prédiction**
**Fichier :** `AZRConfig` lignes 162-200
**Impact :** Logique de prédiction révolutionnaire

### 🧠 **Modification des Algorithmes AZR**
**Fichier :** Méthodes dans `AZRBaccaratPredictor`
**Impact :** Comportement du Proposeur/Résolveur

### 📊 **Modification de l'Apprentissage**
**Fichier :** `_adaptive_learning()` lignes 1570-1620
**Impact :** Auto-amélioration du modèle

## 🎉 **RÉSULTAT**

Avec ces délimitations claires, la maintenance du **modèle AZR** peut se faire de manière :
- **Ciblée** : Seules les sections AZR sont modifiées
- **Sécurisée** : Pas d'impact sur le reste du programme
- **Efficace** : Identification rapide des zones critiques
- **Documentée** : Traçabilité des modifications AZR
