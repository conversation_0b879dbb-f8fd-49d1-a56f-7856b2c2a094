# 🏗️ AZR BACCARAT PREDICTOR - GUIDE DE STRUCTURE

## 📋 INDEX DE NAVIGATION COMPLET

### 🎯 SECTIONS PRINCIPALES

| Section | Ligne | Description | Responsabilité |
|---------|-------|-------------|----------------|
| 📦 **Section 1** | 58 | **Imports et Configuration** | Dépendances et setup global |
| 📋 **Section 2** | 99 | **Structures de Données** | Types et classes de données |
| ⚙️ **Section 3** | 176 | **Configuration AZR** | Paramètres centralisés |
| 🧮 **Section 4** | 932 | **Utilitaires Mathématiques** | Calculs et algorithmes |
| 🔍 **Section 5** | 1078 | **Analyseurs (Rollout 1)** | Analyse des patterns |
| 🎲 **Section 6** | 1085 | **Générateurs (Rollout 2)** | Génération d'hypothèses |
| 🎯 **Section 7** | 1092 | **Prédicteurs (Rollout 3)** | Sélection finale |
| 🧠 **Section 8** | 1099 | **Architecture Cluster** | Framework des clusters |
| 🎯 **Section 9** | 1135 | **Cluster AZR** | Implémentation cluster |
| 🎯 **Section 10** | 10304 | **Système Master** | Coordination clusters |
| 🎲 **Section 11** | 10567 | **Générateur Baccarat** | Simulation de parties |
| 📖 **Section 12** | 10819 | **Chargeur de Données** | Import/Export données |
| 🎮 **Section 13** | 10879 | **Interface Graphique** | UI simplifiée |
| 🧠 **Section 14** | 11487 | **Classe Principale AZR** | Cœur du modèle |
| 🚀 **Section 15** | 15896 | **Fonctions Utilitaires** | Main et helpers |

### 🧠 SOUS-SECTIONS DE LA CLASSE PRINCIPALE (Section 14)

| Sous-Section | Ligne | Description | Fonctionnalité |
|--------------|-------|-------------|----------------|
| 🎯 **14.1** | 11575 | **Interface Principale** | `receive_hand_data()` |
| 💾 **14.2** | 11662 | **Persistance AZR** | Sauvegarde intelligence |
| 🎯 **14.3** | 11110 | **Prédiction Master** | 8 clusters parallèles |
| 🎓 **14.4** | 13756 | **Entraînement & Tests** | Formation du modèle |
| 📈 **14.5** | 14100 | **Métriques & Stats** | Performance monitoring |
| 🔧 **14.6** | 14263 | **Méthodes Corrélation** | Analyse des patterns |
| 🎯 **14.7** | 14449 | **Système Priorités** | Anti-aveuglement |
| 🔧 **14.8** | 14903 | **Utilitaires Session** | Gestion état |

## 🎯 GUIDE DE MAINTENANCE

### ✅ POUR AJOUTER UNE NOUVELLE FONCTIONNALITÉ

1. **Identifier la section appropriée** selon la responsabilité
2. **Respecter la hiérarchie** : Section → Sous-section → Méthode
3. **Utiliser les séparateurs visuels** pour délimiter clairement
4. **Mettre à jour cet index** si nouvelle section créée

### 🔍 POUR LOCALISER UNE FONCTIONNALITÉ

1. **Consulter cet index** pour identifier la section
2. **Utiliser Ctrl+G** (Go to line) dans l'éditeur
3. **Chercher les séparateurs visuels** `############`
4. **Suivre la hiérarchie** Section → Sous-section

### 🧠 POUR MODIFIER LE SYSTÈME DE PRIORITÉS

- **Section 14.7** (ligne 14449) : Système anti-aveuglement
- **Section 14.6** (ligne 14263) : Méthodes de corrélation
- **Section 3** (ligne 176) : Configuration des paramètres

### 📊 POUR MODIFIER LES MÉTRIQUES

- **Section 14.5** (ligne 14100) : Statistiques et performance
- **Section 14.2** (ligne 11662) : Persistance des données
- **Section 4** (ligne 932) : Calculs mathématiques

### 🎯 POUR MODIFIER LES CLUSTERS

- **Section 9** (ligne 1135) : Implémentation cluster
- **Section 10** (ligne 10304) : Coordination master
- **Section 8** (ligne 1099) : Architecture framework

## 🚨 SECTIONS CRITIQUES

### ⚠️ MODIFICATIONS À HAUTE VIGILANCE

| Section | Criticité | Raison |
|---------|-----------|--------|
| **14.2** | 🔴 **CRITIQUE** | Persistance intelligence - Impact continuité |
| **14.7** | 🔴 **CRITIQUE** | Système priorités - Impact prédictions |
| **14.1** | 🟡 **IMPORTANTE** | Interface principale - Point d'entrée |
| **10** | 🟡 **IMPORTANTE** | Master AZR - Coordination globale |
| **3** | 🟡 **IMPORTANTE** | Configuration - Impact performance |

### ✅ SECTIONS SÛRES POUR MODIFICATIONS

| Section | Sécurité | Usage |
|---------|----------|-------|
| **15** | 🟢 **SÛRE** | Utilitaires et helpers |
| **13** | 🟢 **SÛRE** | Interface graphique |
| **12** | 🟢 **SÛRE** | Import/Export données |
| **14.5** | 🟢 **SÛRE** | Métriques et logging |

## 🔧 CONVENTIONS DE STRUCTURE

### 📏 SÉPARATEURS VISUELS

```python
################################################################################
#                                                                              #
#  🎯 SECTION X : TITRE DE LA SECTION                                          #
#                                                                              #
################################################################################

############################################################################
#                                                                          #
#  🎯 SOUS-SECTION X.Y : TITRE DE LA SOUS-SECTION                         #
#                                                                          #
############################################################################
```

### 📝 COMMENTAIRES DE SECTION

- **En-tête** : Description de la responsabilité
- **Maintenance** : Notes pour modifications futures
- **Criticité** : Niveau d'attention requis

### 🎯 ORGANISATION DES MÉTHODES

1. **Méthodes publiques** en premier
2. **Méthodes privées** (`_method`) ensuite
3. **Méthodes utilitaires** en dernier
4. **Ordre logique** selon le flux d'exécution

## 📚 RESSOURCES COMPLÉMENTAIRES

- **Configuration** : Voir `AZRConfig` (Section 3)
- **Tests** : Méthodes dans Section 14.4
- **Performance** : Monitoring dans Section 14.5
- **Debugging** : Logs distribués dans toutes les sections

---

**📅 Dernière mise à jour** : Structure optimisée pour maintenance facilitée
**🎯 Objectif** : Navigation rapide et modifications sûres
