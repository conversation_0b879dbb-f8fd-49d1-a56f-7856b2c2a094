#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test de Sélection Rollout 3

Ce script teste que le Rollout 3 sélectionne bien UNE séquence
parmi les 4 proposées par le Rollout 2.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azr_baccarat_predictor import AZRCluster, AZRConfig

def test_rollout3_selection():
    """Test de la sélection intelligente du Rollout 3"""
    
    print("🎯 TEST SÉLECTION ROLLOUT 3")
    print("=" * 30)
    
    # Initialisation
    config = AZRConfig()
    cluster = AZRCluster(cluster_id=1, config=config)
    
    print("✅ Configuration et cluster initialisés")
    
    # ========================================================================
    # TEST 1 : GÉNÉRATION DES SÉQUENCES PAR ROLLOUT 2
    # ========================================================================
    
    print("\n📊 TEST 1 : Génération Rollout 2")
    print("-" * 30)
    
    # Rapport d'analyse simulé
    analyzer_report = {
        'signals_summary': {
            'top_signals': [
                {'signal_name': 'IMPAIR_TO_PLAYER', 'signal_type': 'pb_prediction', 'strength': 0.8, 'confidence': 0.85, 'strategy': 'player_focus'},
                {'signal_name': 'PAIR_TO_BANKER', 'signal_type': 'pb_prediction', 'strength': 0.75, 'confidence': 0.8, 'strategy': 'banker_focus'},
                {'signal_name': 'SO_SAME_PREDICTION', 'signal_type': 'so_prediction', 'strength': 0.7, 'confidence': 0.75, 'target_outcome': 'S', 'strategy': 'same_focus'},
                {'signal_name': 'PAIR_SYNC_PATTERN', 'signal_type': 'pattern', 'strength': 0.65, 'confidence': 0.7, 'strategy': 'pair_sync_exploitation'}
            ],
            'exploitation_ready': True,
            'overall_confidence': 0.8
        },
        'generation_guidance': {
            'primary_focus': 'IMPAIR_patterns',
            'secondary_focus': 'so_patterns',
            'optimal_sequence_length': 3,
            'confidence_thresholds': {'high': 0.7, 'medium': 0.6, 'low': 0.5},
            'exploitation_strategy': 'moderate',
            'risk_level': 'low'
        },
        'quick_access': {
            'current_state': 'IMPAIR_SYNC',
            'next_prediction_pb': 'P',
            'next_prediction_so': 'S',
            'prediction_confidence': 0.85,
            'alert_level': 'HIGH',
            'exploitation_ready': True
        },
        'indices_analysis': {
            'pbt': {'pbt_sequence': ['P', 'B', 'P', 'B', 'P']},
            'impair_pair': {'position_types': ['IMPAIR', 'PAIR', 'IMPAIR', 'PAIR', 'IMPAIR']},
            'desync_sync': {'sync_sequence': ['SYNC', 'DESYNC', 'SYNC', 'DESYNC', 'SYNC']},
            'combined': {'combined_sequence': ['IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC', 'PAIR_DESYNC', 'IMPAIR_SYNC']}
        },
        'synthesis': {
            'analysis_quality': 0.8,
            'dominant_correlations': [
                {'type': 'IMPAIR_TO_PLAYER', 'strength': 0.8},
                {'type': 'PAIR_TO_BANKER', 'strength': 0.75}
            ],
            'high_confidence_zones': [
                {'pattern': 'impair_sync', 'confidence': 0.85},
                {'pattern': 'pair_desync', 'confidence': 0.75}
            ]
        },
        'sequence_metadata': {'total_hands_analyzed': 50}
    }
    
    # Génération des séquences par Rollout 2
    generator_result = cluster._rollout_generator(analyzer_report)
    
    # Extraire les séquences
    generated_sequences = generator_result.get('sequences', []) if isinstance(generator_result, dict) else generator_result
    
    print(f"✅ Rollout 2 a généré : {len(generated_sequences)} séquences")
    
    # Afficher les séquences générées
    for i, sequence in enumerate(generated_sequences):
        if isinstance(sequence, dict):
            so_data = sequence.get('sequence_data_so', [])
            pb_data = sequence.get('sequence_data_pb', [])
            probability = sequence.get('estimated_probability', 0.0)
            
            print(f"   Séquence {i+1} :")
            print(f"      P/B : {pb_data}")
            print(f"      S/O : {so_data}")
            print(f"      Probabilité : {probability:.3f}")
    
    # ========================================================================
    # TEST 2 : SÉLECTION PAR ROLLOUT 3
    # ========================================================================
    
    print("\n🎯 TEST 2 : Sélection Rollout 3")
    print("-" * 30)
    
    # Exécution du Rollout 3
    final_prediction = cluster._rollout_predictor(generator_result, analyzer_report)
    
    if 'error' in final_prediction:
        print(f"❌ Erreur Rollout 3 : {final_prediction['error']}")
        return False
    
    print("✅ Rollout 3 exécuté avec succès")
    
    # Analyser la sélection
    selected_sequence = final_prediction.get('sequence', [])
    selected_strategy = final_prediction.get('strategy', 'N/A')
    selection_probability = final_prediction.get('estimated_probability', 0.0)
    evaluation_score = final_prediction.get('evaluation_score', 0.0)
    cluster_confidence = final_prediction.get('cluster_confidence', 0.0)
    next_hand = final_prediction.get('next_hand_prediction', 'N/A')
    
    print(f"📊 Sélection finale :")
    print(f"   • Séquence S/O : {selected_sequence}")
    print(f"   • Stratégie : {selected_strategy}")
    print(f"   • Probabilité : {selection_probability:.3f}")
    print(f"   • Score évaluation : {evaluation_score:.3f}")
    print(f"   • Confiance cluster : {cluster_confidence:.3f}")
    print(f"   • Prochaine main : {next_hand}")
    
    # ========================================================================
    # TEST 3 : VALIDATION DE LA SÉLECTION
    # ========================================================================
    
    print("\n🔍 TEST 3 : Validation de la sélection")
    print("-" * 35)
    
    # Vérifier qu'UNE SEULE séquence a été sélectionnée
    is_single_sequence = isinstance(selected_sequence, list) and len(selected_sequence) > 0
    print(f"📊 Une seule séquence sélectionnée : {'✅' if is_single_sequence else '❌'}")
    
    # Vérifier que la séquence est au format S/O
    is_so_format = all(x in ['S', 'O'] for x in selected_sequence) if selected_sequence else False
    print(f"📊 Format S/O correct : {'✅' if is_so_format else '❌'}")
    
    # Vérifier la longueur de la séquence
    correct_length = len(selected_sequence) == config.rollout3_fixed_length if selected_sequence else False
    print(f"📊 Longueur correcte ({config.rollout3_fixed_length}) : {'✅' if correct_length else '❌'}")
    
    # Vérifier que la prédiction immédiate est cohérente
    prediction_coherent = (next_hand == selected_sequence[0]) if selected_sequence and next_hand != 'N/A' else False
    print(f"📊 Prédiction cohérente : {'✅' if prediction_coherent else '❌'}")
    
    # Vérifier les métadonnées du pipeline
    pipeline_metadata = final_prediction.get('pipeline_metadata', {})
    sequences_evaluated = pipeline_metadata.get('sequences_evaluated', 0)
    best_sequence_index = pipeline_metadata.get('best_sequence_index', -1)
    
    print(f"📊 Séquences évaluées : {sequences_evaluated}")
    print(f"📊 Index de la meilleure : {best_sequence_index}")
    
    # ========================================================================
    # TEST 4 : COMPARAISON AVEC LES SÉQUENCES ORIGINALES
    # ========================================================================
    
    print("\n📊 TEST 4 : Comparaison avec les originales")
    print("-" * 40)
    
    # Vérifier que la séquence sélectionnée provient bien des 4 générées
    original_pb_sequence = pipeline_metadata.get('original_pb_sequence', [])
    
    print(f"📋 Séquence P/B originale sélectionnée : {original_pb_sequence}")
    
    # Vérifier la conversion P/B → S/O
    if original_pb_sequence:
        # Reconvertir pour vérifier
        reconverted_so = cluster._convert_pb_sequence_to_so_with_history(original_pb_sequence, 'P')
        conversion_correct = reconverted_so == selected_sequence
        
        print(f"📋 Reconversion P/B → S/O : {original_pb_sequence} → {reconverted_so}")
        print(f"📊 Conversion correcte : {'✅' if conversion_correct else '❌'}")
    
    # ========================================================================
    # TEST 5 : ÉVALUATION DE L'INTELLIGENCE DE SÉLECTION
    # ========================================================================
    
    print("\n🧠 TEST 5 : Intelligence de sélection")
    print("-" * 35)
    
    # Vérifier que le Rollout 3 a utilisé les critères d'évaluation
    has_evaluation_score = evaluation_score > 0
    print(f"📊 Score d'évaluation calculé : {'✅' if has_evaluation_score else '❌'}")
    
    # Vérifier que la confiance est calibrée
    reasonable_confidence = 0.0 <= cluster_confidence <= 1.0
    print(f"📊 Confiance calibrée : {'✅' if reasonable_confidence else '❌'}")
    
    # Vérifier l'alignement avec les signaux
    strategy_aligned = any(signal.get('strategy', '') in selected_strategy for signal in analyzer_report['signals_summary']['top_signals'])
    print(f"📊 Alignement stratégique : {'✅' if strategy_aligned else '⚠️'}")
    
    # ========================================================================
    # VALIDATION FINALE
    # ========================================================================
    
    print("\n✅ VALIDATION FINALE")
    print("-" * 20)
    
    # Critères de validation
    all_criteria = [
        is_single_sequence,
        is_so_format,
        correct_length,
        prediction_coherent,
        has_evaluation_score,
        reasonable_confidence
    ]
    
    success_rate = sum(all_criteria) / len(all_criteria) * 100
    
    print(f"📊 Taux de réussite : {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🏆 SÉLECTION PARFAITE !")
    elif success_rate >= 75:
        print("👍 SÉLECTION TRÈS BONNE !")
    elif success_rate >= 60:
        print("✅ SÉLECTION ACCEPTABLE")
    else:
        print("⚠️ SÉLECTION À AMÉLIORER")
    
    # ========================================================================
    # CONCLUSION
    # ========================================================================
    
    print("\n🎉 CONCLUSION")
    print("-" * 15)
    
    if success_rate >= 75:
        print("✅ LE ROLLOUT 3 SÉLECTIONNE PARFAITEMENT !")
        print("✅ Une seule séquence choisie parmi les 4")
        print("✅ Évaluation intelligente multi-critères")
        print("✅ Conversion P/B → S/O correcte")
        print("✅ Prédiction finale cohérente")
        print("✅ Confiance calibrée")
    else:
        print("⚠️ Problèmes détectés dans la sélection")
    
    print(f"\n🎯 PROCESSUS DE SÉLECTION :")
    print(f"1. Rollout 2 génère 4 séquences S/O")
    print(f"2. Rollout 3 évalue chaque séquence")
    print(f"3. Rollout 3 sélectionne LA MEILLEURE")
    print(f"4. Prédiction finale = séquence S/O unique")
    
    return success_rate >= 75

if __name__ == "__main__":
    test_rollout3_selection()
