#!/usr/bin/env python3
"""
Recherche exhaustive des valeurs hardcodées dans les méthodes rollouts et clusters

Ce script identifie TOUTES les valeurs numériques hardcodées qui doivent être
centralisées dans AZRConfig selon leur catégorie (rollout 1, 2, 3, clusters).
"""

import sys
import os
import re
from collections import defaultdict

def analyser_valeurs_hardcodees():
    """Analyse toutes les valeurs hardcodées dans le fichier"""
    
    print("🔍 RECHERCHE EXHAUSTIVE DES VALEURS HARDCODÉES")
    print("=" * 80)
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            lignes = f.readlines()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return False
    
    # Patterns pour identifier les valeurs hardcodées
    patterns_hardcoded = [
        # Nombres décimaux
        (r'\b0\.[0-9]+\b', 'decimal'),
        # Nombres entiers > 1
        (r'\b[2-9]\b', 'small_int'),
        (r'\b[1-9][0-9]+\b', 'large_int'),
        # Expressions avec max/min
        (r'max\(\s*1\s*,', 'max_one'),
        (r'min\(\s*1\s*,', 'min_one'),
        (r'max\(\s*0\s*,', 'max_zero'),
        (r'min\(\s*0\s*,', 'min_zero'),
        # Range avec valeurs hardcodées
        (r'range\(\s*1\s*,', 'range_one'),
        (r'range\(\s*0\s*,', 'range_zero'),
        (r'range\(\s*[2-9]\s*,', 'range_other'),
        # Opérations arithmétiques
        (r'len\([^)]+\)\s*[+\-*/]\s*[0-9]+', 'len_arithmetic'),
        (r'[0-9]+\s*[*/]\s*[0-9]+', 'arithmetic'),
        # Comparaisons
        (r'>\s*0\.[0-9]+', 'comparison_decimal'),
        (r'<\s*0\.[0-9]+', 'comparison_decimal'),
        (r'>=\s*0\.[0-9]+', 'comparison_decimal'),
        (r'<=\s*0\.[0-9]+', 'comparison_decimal'),
        # Return avec valeurs
        (r'return\s+0\.[0-9]+', 'return_decimal'),
        (r'return\s+[1-9][0-9]*\.[0-9]+', 'return_decimal'),
    ]
    
    # Dictionnaire pour stocker les occurrences par méthode
    occurrences_par_methode = defaultdict(list)
    methode_courante = None
    
    # Analyser chaque ligne
    for num_ligne, ligne in enumerate(lignes, 1):
        ligne_clean = ligne.strip()
        
        # Ignorer les commentaires purs et lignes vides
        if ligne_clean.startswith('#') or not ligne_clean:
            continue
        
        # Détecter les définitions de méthodes
        if ligne_clean.startswith('def '):
            methode_courante = ligne_clean.split('(')[0].replace('def ', '')
        
        # Chercher les patterns hardcodés
        for pattern, type_pattern in patterns_hardcoded:
            matches = re.findall(pattern, ligne_clean)
            if matches:
                for match in matches:
                    # Éviter les faux positifs dans les définitions de paramètres
                    if ':' in ligne_clean and '=' in ligne_clean and num_ligne < 2000:
                        continue  # Probablement une définition de paramètre
                    
                    occurrences_par_methode[methode_courante].append({
                        'ligne': num_ligne,
                        'match': match,
                        'type': type_pattern,
                        'contexte': ligne_clean[:80] + ('...' if len(ligne_clean) > 80 else '')
                    })
    
    return occurrences_par_methode

def categoriser_par_rollout_cluster(occurrences_par_methode):
    """Catégorise les occurrences par rollout/cluster"""
    
    categories = {
        'rollout_1_analyzer': [],
        'rollout_2_generator': [],
        'rollout_3_predictor': [],
        'clusters': [],
        'utilitaires': [],
        'autres': []
    }
    
    # Mapping des méthodes vers les catégories
    mapping_methodes = {
        '_rollout_analyzer': 'rollout_1_analyzer',
        '_analyze_': 'rollout_1_analyzer',
        '_calculate_impair_pair': 'rollout_1_analyzer',
        '_calculate_sync_desync': 'rollout_1_analyzer',
        '_calculate_combined': 'rollout_1_analyzer',
        
        '_rollout_generator': 'rollout_2_generator',
        '_generate_': 'rollout_2_generator',
        '_create_sequence': 'rollout_2_generator',
        'calculate_rollout2': 'rollout_2_generator',
        
        '_rollout_predictor': 'rollout_3_predictor',
        '_select_': 'rollout_3_predictor',
        '_evaluate_': 'rollout_3_predictor',
        'calculate_rollout3': 'rollout_3_predictor',
        '_calculate_cluster_confidence': 'rollout_3_predictor',
        
        'execute_cluster_pipeline': 'clusters',
        'calculate_cluster_total_reward': 'clusters',
        '_build_consensus': 'clusters',
        '_update_system_metrics': 'clusters',
        
        'UtilitairesMathematiquesAZR': 'utilitaires',
        'calculate_correlation': 'utilitaires',
        'normalize_': 'utilitaires'
    }
    
    for methode, occurrences in occurrences_par_methode.items():
        if not methode:
            continue
            
        categorie = 'autres'
        for pattern, cat in mapping_methodes.items():
            if pattern in methode:
                categorie = cat
                break
        
        categories[categorie].extend([{
            'methode': methode,
            **occ
        } for occ in occurrences])
    
    return categories

def analyser_priorites(categories):
    """Analyse les priorités de centralisation"""
    
    print("\n📊 ANALYSE DES PRIORITÉS DE CENTRALISATION")
    print("=" * 60)
    
    priorites = {
        'CRITIQUE': [],
        'ÉLEVÉE': [],
        'MOYENNE': [],
        'FAIBLE': []
    }
    
    # Critères de priorité
    for categorie, occurrences in categories.items():
        if not occurrences:
            continue
            
        print(f"\n🔍 {categorie.upper().replace('_', ' ')} - {len(occurrences)} occurrences")
        
        # Analyser chaque occurrence
        for occ in occurrences:
            priorite = determiner_priorite(occ, categorie)
            priorites[priorite].append({
                'categorie': categorie,
                **occ
            })
            
            # Afficher les plus critiques
            if priorite in ['CRITIQUE', 'ÉLEVÉE']:
                print(f"   {priorite}: Ligne {occ['ligne']} - {occ['match']} dans {occ['methode']}")
    
    return priorites

def determiner_priorite(occurrence, categorie):
    """Détermine la priorité d'une occurrence"""
    
    match = occurrence['match']
    type_pattern = occurrence['type']
    contexte = occurrence['contexte']
    
    # CRITIQUE : Valeurs dans les rollouts principaux
    if categorie in ['rollout_1_analyzer', 'rollout_2_generator', 'rollout_3_predictor']:
        if type_pattern in ['decimal', 'comparison_decimal', 'return_decimal']:
            if any(keyword in contexte.lower() for keyword in ['confidence', 'threshold', 'probability', 'strength']):
                return 'CRITIQUE'
        if type_pattern in ['max_one', 'min_one'] and 'rollout' in contexte.lower():
            return 'CRITIQUE'
    
    # ÉLEVÉE : Clusters et calculs importants
    if categorie == 'clusters':
        if type_pattern in ['decimal', 'comparison_decimal']:
            return 'ÉLEVÉE'
    
    # ÉLEVÉE : Seuils et comparaisons importantes
    if type_pattern == 'comparison_decimal':
        if any(keyword in contexte.lower() for keyword in ['bias', 'significant', 'strong']):
            return 'ÉLEVÉE'
    
    # MOYENNE : Autres valeurs numériques importantes
    if type_pattern in ['decimal', 'small_int']:
        if any(keyword in contexte.lower() for keyword in ['weight', 'factor', 'bonus', 'malus']):
            return 'MOYENNE'
    
    # FAIBLE : Le reste
    return 'FAIBLE'

def generer_recommandations(priorites):
    """Génère des recommandations de centralisation"""
    
    print(f"\n🎯 RECOMMANDATIONS DE CENTRALISATION")
    print("=" * 50)
    
    total_critiques = len(priorites['CRITIQUE'])
    total_elevees = len(priorites['ÉLEVÉE'])
    total_moyennes = len(priorites['MOYENNE'])
    
    print(f"📊 RÉSUMÉ:")
    print(f"   🔴 CRITIQUES: {total_critiques} occurrences")
    print(f"   🟡 ÉLEVÉES: {total_elevees} occurrences")
    print(f"   🟠 MOYENNES: {total_moyennes} occurrences")
    print(f"   🟢 FAIBLES: {len(priorites['FAIBLE'])} occurrences")
    
    if total_critiques > 0:
        print(f"\n🚨 ACTIONS IMMÉDIATES REQUISES:")
        print(f"   1. Centraliser les {total_critiques} valeurs CRITIQUES")
        print(f"   2. Créer les paramètres manquants dans AZRConfig")
        print(f"   3. Remplacer par self.config.parameter_name")
    
    if total_elevees > 0:
        print(f"\n⚡ ACTIONS PRIORITAIRES:")
        print(f"   1. Centraliser les {total_elevees} valeurs ÉLEVÉES")
        print(f"   2. Organiser par sections rollout/cluster")
    
    # Recommandations spécifiques par catégorie
    print(f"\n📋 PLAN D'ACTION PAR CATÉGORIE:")
    
    categories_ordre = ['rollout_1_analyzer', 'rollout_2_generator', 'rollout_3_predictor', 'clusters']
    for categorie in categories_ordre:
        occurrences_cat = [occ for occ in priorites['CRITIQUE'] + priorites['ÉLEVÉE'] 
                          if occ['categorie'] == categorie]
        if occurrences_cat:
            print(f"\n   🔧 {categorie.upper().replace('_', ' ')}:")
            print(f"      - {len(occurrences_cat)} valeurs à centraliser")
            print(f"      - Section AZRConfig: SECTION {get_section_letter(categorie)}")

def get_section_letter(categorie):
    """Retourne la lettre de section pour une catégorie"""
    mapping = {
        'rollout_1_analyzer': 'N (Rollout 1)',
        'rollout_2_generator': 'O (Rollout 2)', 
        'rollout_3_predictor': 'P (Rollout 3)',
        'clusters': 'Q (Clusters)'
    }
    return mapping.get(categorie, 'A (Valeurs de base)')

def main():
    """Fonction principale"""
    
    # Analyser les valeurs hardcodées
    occurrences = analyser_valeurs_hardcodees()
    if not occurrences:
        return False
    
    # Catégoriser par rollout/cluster
    categories = categoriser_par_rollout_cluster(occurrences)
    
    # Analyser les priorités
    priorites = analyser_priorites(categories)
    
    # Générer les recommandations
    generer_recommandations(priorites)
    
    # Résultat final
    total_occurrences = sum(len(occ) for occ in categories.values())
    critiques_elevees = len(priorites['CRITIQUE']) + len(priorites['ÉLEVÉE'])
    
    print(f"\n" + "=" * 80)
    print("📊 RÉSULTAT FINAL DE L'ANALYSE")
    print("=" * 80)
    print(f"🔍 Total valeurs hardcodées trouvées: {total_occurrences}")
    print(f"🚨 Valeurs critiques + élevées: {critiques_elevees}")
    print(f"📈 Pourcentage à centraliser en priorité: {(critiques_elevees/total_occurrences)*100:.1f}%")
    
    if critiques_elevees > 0:
        print(f"\n⚠️  CENTRALISATION INCOMPLÈTE DÉTECTÉE!")
        print(f"🎯 Prochaine étape: Centraliser les {critiques_elevees} valeurs prioritaires")
        return False
    else:
        print(f"\n✅ CENTRALISATION COMPLÈTE!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
