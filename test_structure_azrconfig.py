#!/usr/bin/env python3
"""
Test de validation de la nouvelle structure organisée d'AZRConfig

Ce script vérifie que la nouvelle structure par catégories fonctionne
correctement et que tous les paramètres sont accessibles.
"""

import sys
import os

# Ajouter le répertoire parent au path pour importer le module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from azr_baccarat_predictor import AZRConfig, AZRBaccaratPredictor
    print("✅ Import réussi des classes AZR")
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    sys.exit(1)

def test_structure_par_sections():
    """Test de la structure organisée par sections"""
    print("\n🏗️ TEST DE LA STRUCTURE ORGANISÉE PAR SECTIONS")
    print("=" * 70)
    
    config = AZRConfig()
    
    # Définir les sections et leurs paramètres représentatifs
    sections = {
        "📊 SECTION A - VALEURS DE BASE": [
            'zero_value', 'one_value', 'half_value', 'correlation_player_value'
        ],
        "🎯 SECTION B - CONFIANCE ET PROBABILITÉ": [
            'confidence_minimum_05_percent', 'confidence_threshold_10_percent'
        ],
        "🔍 SECTION C - CORRÉLATION ET SIGNAUX": [
            'significance_threshold_5_percent', 'strength_threshold_60_percent', 'signal_strength_base'
        ],
        "⚖️ SECTION D - PONDÉRATION ET POIDS": [
            'weight_40_percent', 'weight_30_percent', 'weight_20_percent'
        ],
        "📏 SECTION E - DISTANCES ET PROXIMITÉ": [
            'distance_adjacent', 'context_value_adjacent'
        ],
        "🎲 SECTION F - NORMALISATION": [
            'normalization_factor_2', 'normalization_factor_10', 'multiplier_increment_02'
        ],
        "📈 SECTION G - QUALITÉ ET PERFORMANCE": [
            'quality_threshold_30_percent', 'quality_threshold_60_percent'
        ],
        "🔢 SECTION H - ÉCHANTILLONS ET LONGUEURS": [
            'sample_size_minimum_2', 'length_threshold_3'
        ],
        "⏱️ SECTION I - TEMPOREL ET RÉCENCE": [
            'recent_weight_factor', 'temporal_weight_factor'
        ],
        "📊 SECTION J - VARIANCE ET ÉCART-TYPE": [
            'variance_threshold_minimum', 'variance_multiplier_10'
        ],
        "🎁 SECTION K - BONUS ET MALUS": [
            'bonus_factor_15_percent', 'malus_factor_15_percent'
        ],
        "🎯 SECTION L - ATTENTION ET RARETÉ": [
            'attention_level_base', 'rarity_factor_high_str'
        ]
    }
    
    sections_reussies = 0
    total_sections = len(sections)
    
    for section_name, parametres in sections.items():
        print(f"\n{section_name}")
        print("-" * 50)
        
        parametres_presents = 0
        for param in parametres:
            if hasattr(config, param):
                valeur = getattr(config, param)
                print(f"  ✅ {param}: {valeur}")
                parametres_presents += 1
            else:
                print(f"  ❌ {param}: MANQUANT")
        
        if parametres_presents == len(parametres):
            print(f"  🎉 Section complète: {parametres_presents}/{len(parametres)}")
            sections_reussies += 1
        else:
            print(f"  ⚠️  Section incomplète: {parametres_presents}/{len(parametres)}")
    
    print(f"\n📊 RÉSULTATS SECTIONS:")
    print(f"   ✅ Sections complètes: {sections_reussies}/{total_sections}")
    
    return sections_reussies == total_sections

def test_coherence_valeurs():
    """Test de cohérence des valeurs entre sections"""
    print("\n🔍 TEST DE COHÉRENCE DES VALEURS")
    print("=" * 70)
    
    config = AZRConfig()
    
    # Tests de cohérence logique
    tests_coherence = [
        {
            'nom': 'Valeurs de base cohérentes',
            'test': config.zero_value < config.half_value < config.one_value,
            'description': 'zero_value < half_value < one_value'
        },
        {
            'nom': 'Seuils de confiance croissants',
            'test': config.confidence_minimum_05_percent < config.confidence_threshold_10_percent,
            'description': 'confidence_minimum_05_percent < confidence_threshold_10_percent'
        },
        {
            'nom': 'Facteurs de normalisation cohérents',
            'test': config.normalization_factor_2 < config.normalization_factor_10,
            'description': 'normalization_factor_2 < normalization_factor_10'
        },
        {
            'nom': 'Distances logiques',
            'test': config.distance_adjacent < config.distance_close,
            'description': 'distance_adjacent < distance_close'
        },
        {
            'nom': 'Tailles d\'échantillons croissantes',
            'test': config.sample_size_minimum_2 < config.sample_size_optimal_20,
            'description': 'sample_size_minimum_2 < sample_size_optimal_20'
        },
        {
            'nom': 'Bonus supérieur à 1.0',
            'test': config.bonus_factor_15_percent > config.one_value,
            'description': 'bonus_factor_15_percent > one_value'
        },
        {
            'nom': 'Malus inférieur à 1.0',
            'test': config.malus_factor_15_percent < config.one_value,
            'description': 'malus_factor_15_percent < one_value'
        }
    ]
    
    tests_reussis = 0
    for test in tests_coherence:
        if test['test']:
            print(f"  ✅ {test['nom']}: {test['description']}")
            tests_reussis += 1
        else:
            print(f"  ❌ {test['nom']}: {test['description']}")
    
    print(f"\n📊 RÉSULTATS COHÉRENCE:")
    print(f"   ✅ Tests réussis: {tests_reussis}/{len(tests_coherence)}")
    
    return tests_reussis == len(tests_coherence)

def test_utilisation_dans_methodes():
    """Test que les paramètres organisés sont utilisés dans les méthodes"""
    print("\n🔧 TEST D'UTILISATION DANS LES MÉTHODES")
    print("=" * 70)
    
    try:
        # Créer une instance du prédicteur
        predictor = AZRBaccaratPredictor()
        config = predictor.config
        
        # Tester l'accès aux paramètres organisés
        tests_utilisation = [
            {
                'nom': 'Valeurs de base accessibles',
                'test': config.zero_value == 0.0 and config.one_value == 1.0,
                'description': 'zero_value et one_value correctement définies'
            },
            {
                'nom': 'Seuils de confiance accessibles',
                'test': hasattr(config, 'confidence_threshold_25_percent'),
                'description': 'Seuils de confiance disponibles'
            },
            {
                'nom': 'Facteurs de pondération accessibles',
                'test': hasattr(config, 'weight_40_percent'),
                'description': 'Facteurs de pondération disponibles'
            },
            {
                'nom': 'Propriétés dynamiques fonctionnelles',
                'test': len(config.rollout2_rewards) > 0,
                'description': 'Propriétés dynamiques génèrent des dictionnaires'
            }
        ]
        
        tests_reussis = 0
        for test in tests_utilisation:
            if test['test']:
                print(f"  ✅ {test['nom']}: {test['description']}")
                tests_reussis += 1
            else:
                print(f"  ❌ {test['nom']}: {test['description']}")
        
        print(f"\n📊 RÉSULTATS UTILISATION:")
        print(f"   ✅ Tests réussis: {tests_reussis}/{len(tests_utilisation)}")
        
        return tests_reussis == len(tests_utilisation)
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'utilisation: {e}")
        return False

def test_navigation_sections():
    """Test de navigation dans les sections"""
    print("\n🧭 TEST DE NAVIGATION DANS LES SECTIONS")
    print("=" * 70)
    
    config = AZRConfig()
    
    # Test de recherche par catégorie
    categories_test = {
        'Valeurs de base': ['zero_value', 'one_value', 'half_value'],
        'Confiance': ['confidence_minimum_05_percent', 'confidence_threshold_10_percent'],
        'Pondération': ['weight_40_percent', 'weight_30_percent'],
        'Normalisation': ['normalization_factor_2', 'normalization_factor_10'],
        'Attention': ['attention_level_base', 'attention_level_max']
    }
    
    categories_reussies = 0
    for categorie, parametres in categories_test.items():
        tous_presents = all(hasattr(config, param) for param in parametres)
        if tous_presents:
            print(f"  ✅ Catégorie '{categorie}': Tous les paramètres accessibles")
            categories_reussies += 1
        else:
            print(f"  ❌ Catégorie '{categorie}': Paramètres manquants")
    
    print(f"\n📊 RÉSULTATS NAVIGATION:")
    print(f"   ✅ Catégories accessibles: {categories_reussies}/{len(categories_test)}")
    
    return categories_reussies == len(categories_test)

def main():
    """Fonction principale de test"""
    print("🏗️ VALIDATION DE LA STRUCTURE ORGANISÉE D'AZRCONFIG")
    print("=" * 80)
    
    tests_reussis = 0
    total_tests = 4
    
    # Test 1: Structure par sections
    if test_structure_par_sections():
        tests_reussis += 1
    
    # Test 2: Cohérence des valeurs
    if test_coherence_valeurs():
        tests_reussis += 1
    
    # Test 3: Utilisation dans les méthodes
    if test_utilisation_dans_methodes():
        tests_reussis += 1
    
    # Test 4: Navigation dans les sections
    if test_navigation_sections():
        tests_reussis += 1
    
    # Résultats finaux
    print("\n" + "=" * 80)
    print("📊 RÉSULTATS FINAUX")
    print("=" * 80)
    print(f"Tests réussis: {tests_reussis}/{total_tests}")
    
    if tests_reussis == total_tests:
        print("🎉 TOUS LES TESTS SONT RÉUSSIS!")
        print("✅ La structure organisée d'AZRConfig est parfaitement fonctionnelle")
        print("🏗️ Navigation facilitée par catégories bien identifiables")
        return True
    else:
        print("⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("❌ La structure nécessite des corrections")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
