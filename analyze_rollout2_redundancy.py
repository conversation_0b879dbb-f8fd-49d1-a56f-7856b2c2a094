#!/usr/bin/env python3
"""
Analyse des méthodes potentiellement redondantes dans le Rollout 2

Ce script identifie les méthodes qui pourraient être devenues inutiles
avec la nouvelle architecture optimisée du Rollout 2.
"""

import re
from typing import Dict, List, Set, Tuple

def extract_rollout2_methods(file_content: str) -> Dict[str, List[str]]:
    """Extrait toutes les méthodes liées au Rollout 2"""
    
    rollout2_methods = {
        'new_optimized': [],      # Nouvelles méthodes optimisées
        'legacy_generation': [],  # Anciennes méthodes de génération
        'utility_methods': [],    # Méthodes utilitaires
        'helper_methods': []      # Méthodes d'aide
    }
    
    # Nouvelles méthodes optimisées (ajoutées récemment)
    new_optimized_patterns = [
        r'def _define_optimized_generation_space\(',
        r'def _generate_sequences_from_signals\(',
        r'def _generate_sequence_from_signal\(',
        r'def _generate_fallback_sequences\(',
        r'def _generate_.*_based_sequence\(',
        r'def _generate_.*_sync_sequence\(',
        r'def _generate_.*_sequence\(',
        r'def _classify_confidence_level\('
    ]
    
    # Anciennes méthodes de génération (potentiellement redondantes)
    legacy_generation_patterns = [
        r'def _generate_impair_pair_optimized_sequence\(',
        r'def _generate_sync_based_sequence\(',
        r'def _generate_combined_index_sequence\(',
        r'def _generate_so_pattern_sequence\(',
        r'def _define_complete_generation_space\(',
        r'def _enrich_sequences_with_complete_indexes\('
    ]
    
    # Méthodes utilitaires
    utility_patterns = [
        r'def _calculate_.*_confidence\(',
        r'def _evaluate_.*_quality\(',
        r'def _select_.*_sequence\(',
        r'def _extract_.*_prediction\('
    ]
    
    # Recherche des méthodes
    method_pattern = r'def (_[a-zA-Z_]+)\('
    all_methods = re.findall(method_pattern, file_content)
    
    for method in all_methods:
        method_def = f"def {method}("
        
        # Classer selon les patterns
        if any(re.search(pattern, method_def) for pattern in new_optimized_patterns):
            rollout2_methods['new_optimized'].append(method)
        elif any(re.search(pattern, method_def) for pattern in legacy_generation_patterns):
            rollout2_methods['legacy_generation'].append(method)
        elif any(re.search(pattern, method_def) for pattern in utility_patterns):
            rollout2_methods['utility_methods'].append(method)
        elif 'generate' in method or 'rollout' in method:
            rollout2_methods['helper_methods'].append(method)
    
    return rollout2_methods

def analyze_method_usage(file_content: str, methods: List[str]) -> Dict[str, Dict]:
    """Analyse l'utilisation de chaque méthode"""
    
    usage_analysis = {}
    
    for method in methods:
        # Compter les appels à cette méthode
        call_pattern = rf'self\.{re.escape(method)}\('
        calls = re.findall(call_pattern, file_content)
        
        # Trouver la définition de la méthode
        def_pattern = rf'def {re.escape(method)}\((.*?)\):'
        definition = re.search(def_pattern, file_content)
        
        # Analyser le contenu de la méthode
        if definition:
            # Trouver le corps de la méthode
            start_pos = definition.end()
            lines = file_content[start_pos:].split('\n')
            
            method_body = []
            indent_level = None
            
            for line in lines:
                if line.strip() == '':
                    continue
                
                current_indent = len(line) - len(line.lstrip())
                
                if indent_level is None and line.strip():
                    indent_level = current_indent
                
                if line.strip() and current_indent <= indent_level and not line.startswith(' ' * indent_level):
                    break
                
                method_body.append(line)
            
            method_content = '\n'.join(method_body[:20])  # Premiers 20 lignes
        else:
            method_content = "Définition non trouvée"
        
        usage_analysis[method] = {
            'call_count': len(calls),
            'definition_found': definition is not None,
            'method_content_preview': method_content[:200] + "..." if len(method_content) > 200 else method_content,
            'is_used': len(calls) > 0
        }
    
    return usage_analysis

def identify_redundant_methods(rollout2_methods: Dict, usage_analysis: Dict) -> Dict[str, List[str]]:
    """Identifie les méthodes potentiellement redondantes"""
    
    redundancy_analysis = {
        'definitely_redundant': [],    # Méthodes clairement inutiles
        'potentially_redundant': [],   # Méthodes potentiellement inutiles
        'still_needed': [],           # Méthodes encore nécessaires
        'new_optimized': []           # Nouvelles méthodes optimisées
    }
    
    # Analyser les méthodes legacy de génération
    for method in rollout2_methods['legacy_generation']:
        if method in usage_analysis:
            usage = usage_analysis[method]
            
            # Si la méthode n'est appelée que dans _generate_fallback_sequences
            if usage['call_count'] <= 1:
                redundancy_analysis['potentially_redundant'].append(method)
            elif usage['call_count'] > 1:
                redundancy_analysis['still_needed'].append(method)
            else:
                redundancy_analysis['definitely_redundant'].append(method)
    
    # Marquer les nouvelles méthodes optimisées
    for method in rollout2_methods['new_optimized']:
        redundancy_analysis['new_optimized'].append(method)
    
    # Analyser les méthodes utilitaires
    for method in rollout2_methods['utility_methods']:
        if method in usage_analysis:
            usage = usage_analysis[method]
            if usage['call_count'] == 0:
                redundancy_analysis['definitely_redundant'].append(method)
            else:
                redundancy_analysis['still_needed'].append(method)
    
    return redundancy_analysis

def analyze_rollout2_redundancy():
    """Analyse principale des redondances du Rollout 2"""
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return
    
    print("🔍 ANALYSE DES REDONDANCES ROLLOUT 2")
    print("=" * 40)
    
    # 1. Extraire les méthodes du Rollout 2
    print("\n📊 1. EXTRACTION DES MÉTHODES ROLLOUT 2")
    print("-" * 40)
    
    rollout2_methods = extract_rollout2_methods(file_content)
    
    total_methods = sum(len(methods) for methods in rollout2_methods.values())
    print(f"📈 Total méthodes Rollout 2 : {total_methods}")
    
    for category, methods in rollout2_methods.items():
        if methods:
            print(f"📋 {category.replace('_', ' ').title()} : {len(methods)}")
            for method in methods[:3]:  # Afficher les 3 premières
                print(f"   • {method}")
            if len(methods) > 3:
                print(f"   ... et {len(methods) - 3} autres")
    
    # 2. Analyser l'utilisation des méthodes
    print("\n🔍 2. ANALYSE DE L'UTILISATION")
    print("-" * 35)
    
    all_methods = []
    for methods in rollout2_methods.values():
        all_methods.extend(methods)
    
    usage_analysis = analyze_method_usage(file_content, all_methods)
    
    used_methods = sum(1 for usage in usage_analysis.values() if usage['is_used'])
    unused_methods = len(all_methods) - used_methods
    
    print(f"✅ Méthodes utilisées : {used_methods}/{len(all_methods)}")
    print(f"❌ Méthodes inutilisées : {unused_methods}/{len(all_methods)}")
    
    # 3. Identifier les redondances
    print("\n⚠️  3. IDENTIFICATION DES REDONDANCES")
    print("-" * 40)
    
    redundancy_analysis = identify_redundant_methods(rollout2_methods, usage_analysis)
    
    for category, methods in redundancy_analysis.items():
        if methods:
            if category == 'definitely_redundant':
                print(f"❌ Définitivement redondantes ({len(methods)}) :")
            elif category == 'potentially_redundant':
                print(f"⚠️  Potentiellement redondantes ({len(methods)}) :")
            elif category == 'still_needed':
                print(f"✅ Encore nécessaires ({len(methods)}) :")
            elif category == 'new_optimized':
                print(f"🆕 Nouvelles optimisées ({len(methods)}) :")
            
            for method in methods:
                usage = usage_analysis.get(method, {})
                call_count = usage.get('call_count', 0)
                print(f"   • {method} (appelée {call_count} fois)")
    
    # 4. Recommandations
    print("\n💡 4. RECOMMANDATIONS")
    print("-" * 25)
    
    definitely_redundant = len(redundancy_analysis['definitely_redundant'])
    potentially_redundant = len(redundancy_analysis['potentially_redundant'])
    new_optimized = len(redundancy_analysis['new_optimized'])
    
    if definitely_redundant > 0:
        print(f"🗑️  SUPPRIMER : {definitely_redundant} méthodes définitivement inutiles")
        for method in redundancy_analysis['definitely_redundant'][:3]:
            print(f"   • {method}")
        if definitely_redundant > 3:
            print(f"   ... et {definitely_redundant - 3} autres")
    
    if potentially_redundant > 0:
        print(f"\n🔍 EXAMINER : {potentially_redundant} méthodes potentiellement redondantes")
        print("   Ces méthodes ne sont utilisées que dans le fallback")
        for method in redundancy_analysis['potentially_redundant'][:3]:
            print(f"   • {method}")
    
    if new_optimized > 0:
        print(f"\n✅ CONSERVER : {new_optimized} nouvelles méthodes optimisées")
        print("   Ces méthodes implémentent la nouvelle architecture")
    
    # 5. Plan d'action
    print(f"\n📋 5. PLAN D'ACTION")
    print("-" * 20)
    
    if definitely_redundant > 0:
        print("1. ❌ Supprimer les méthodes définitivement redondantes")
    
    if potentially_redundant > 0:
        print("2. 🔍 Analyser si les méthodes fallback sont vraiment nécessaires")
        print("3. 🔄 Considérer la simplification du mécanisme de fallback")
    
    print("4. ✅ Conserver les nouvelles méthodes optimisées")
    print("5. 📝 Documenter les changements d'architecture")
    
    # 6. Métriques finales
    print(f"\n📊 MÉTRIQUES FINALES")
    print("-" * 20)
    
    efficiency_score = (used_methods / len(all_methods)) * 100 if all_methods else 0
    optimization_score = (new_optimized / len(all_methods)) * 100 if all_methods else 0
    
    print(f"Efficacité code    : {efficiency_score:.1f}%")
    print(f"Taux optimisation : {optimization_score:.1f}%")
    print(f"Méthodes à nettoyer: {definitely_redundant + potentially_redundant}")
    
    if efficiency_score >= 80 and definitely_redundant == 0:
        print("🏆 EXCELLENT : Code bien optimisé")
    elif efficiency_score >= 60:
        print("👍 BON : Quelques optimisations possibles")
    else:
        print("⚠️  AMÉLIORATION NÉCESSAIRE : Beaucoup de code mort")

if __name__ == "__main__":
    analyze_rollout2_redundancy()
