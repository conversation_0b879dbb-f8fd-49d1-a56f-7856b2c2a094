# ⚡ MODULE 3.4 : PARALLÉLISATION ET OPTIMISATION

## 🎯 **OBJECTIFS D'APPRENTISSAGE**

À la fin de cette section, vous maîtriserez :
- ✅ Les techniques de parallélisation optimales pour rollouts AZR
- ✅ L'implémentation sur architecture multi-cœurs (8 cœurs)
- ✅ Les optimisations de performance et gestion mémoire
- ✅ Le monitoring et profiling des performances

---

## 🏗️ **ARCHITECTURE DE PARALLÉLISATION AZR**

### **📊 Configuration Optimale Identifiée**

Basé sur l'analyse approfondie de 13 sources académiques et l'implémentation `azr_baccarat_predictor.py` :

```
🎯 CONFIGURATION OPTIMALE
┌─────────────────────────────────────┐
│ • 8 Clusters (1 par cœur CPU)       │
│ • 3 Rollouts par cluster            │
│ • 24 Rollouts total en parallèle    │
│ • Pipeline hybride optimisé         │
│ • Timing: 170ms cycle complet       │
└─────────────────────────────────────┘
```

### **🔄 Stratégie Hybride**

**Innovation clé :** Combinaison optimale de parallélisme et séquentialité :

- **Inter-clusters** : Parallélisation complète (8 threads)
- **Intra-cluster** : Pipeline séquentiel optimisé (3 étapes)
- **Communication** : Shared memory + Message passing

---

## ⚡ **IMPLÉMENTATION PARALLÈLE OPTIMISÉE**

### **🚀 Gestionnaire de Parallélisation**

```python
import concurrent.futures
import threading
import multiprocessing
import queue
import time
from collections import deque

class AZRParallelizationManager:
    """
    Gestionnaire de parallélisation optimisé pour AZR
    
    Basé sur les recherches et l'analyse de performance
    de l'implémentation azr_baccarat_predictor.py (4722 lignes)
    """
    
    def __init__(self, config):
        self.config = config
        self.num_cores = config.num_cores  # 8 par défaut
        self.rollouts_per_cluster = config.rollouts_per_cluster  # 3 par défaut
        
        # Pool de threads optimisé
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.num_cores,
            thread_name_prefix="AZR_Cluster"
        )
        
        # Communication inter-clusters
        self.result_queue = queue.Queue(maxsize=self.num_cores * 2)
        self.coordination_lock = threading.Lock()
        
        # Métriques de performance
        self.performance_metrics = {
            'execution_times': deque(maxlen=100),
            'throughput': deque(maxlen=100),
            'cpu_utilization': deque(maxlen=100),
            'memory_usage': deque(maxlen=100)
        }
        
        # État des clusters
        self.cluster_states = {
            i: {
                'status': 'idle',
                'current_task': None,
                'performance_history': deque(maxlen=50)
            }
            for i in range(self.num_cores)
        }
    
    def execute_parallel_rollouts(self, input_sequence, timeout=0.2):
        """
        Exécution parallèle optimisée des rollouts AZR
        
        Args:
            input_sequence: Séquence d'entrée pour prédiction
            timeout: Timeout en secondes (200ms par défaut)
            
        Returns:
            Résultat de consensus des clusters
        """
        start_time = time.time()
        
        # 1. PRÉPARATION DES TÂCHES
        cluster_tasks = self._prepare_cluster_tasks(input_sequence)
        
        # 2. LANCEMENT PARALLÈLE
        future_to_cluster = {}
        
        for cluster_id, task in enumerate(cluster_tasks):
            future = self.thread_pool.submit(
                self._execute_cluster_pipeline,
                cluster_id,
                task,
                timeout
            )
            future_to_cluster[future] = cluster_id
        
        # 3. COLLECTE AVEC TIMEOUT
        cluster_results = []
        completed_clusters = 0
        
        for future in concurrent.futures.as_completed(future_to_cluster, timeout=timeout):
            cluster_id = future_to_cluster[future]
            
            try:
                result = future.result(timeout=0.05)  # Timeout individuel court
                result['cluster_id'] = cluster_id
                cluster_results.append(result)
                completed_clusters += 1
                
                # Mise à jour état cluster
                self.cluster_states[cluster_id]['status'] = 'completed'
                
            except Exception as e:
                # Gestion des erreurs de cluster
                error_result = {
                    'cluster_id': cluster_id,
                    'prediction': None,
                    'confidence': 0.0,
                    'error': str(e),
                    'execution_time': timeout
                }
                cluster_results.append(error_result)
        
        # 4. CONSENSUS ET AGRÉGATION
        consensus_result = self._achieve_parallel_consensus(cluster_results)
        
        # 5. MÉTRIQUES DE PERFORMANCE
        total_execution_time = time.time() - start_time
        self._update_performance_metrics(
            total_execution_time, 
            completed_clusters, 
            cluster_results
        )
        
        return consensus_result
    
    def _prepare_cluster_tasks(self, input_sequence):
        """
        Prépare les tâches pour chaque cluster avec diversification
        """
        cluster_tasks = []
        
        for cluster_id in range(self.num_cores):
            # Diversification des tâches par cluster
            task = {
                'input_sequence': input_sequence,
                'cluster_id': cluster_id,
                'diversification_seed': cluster_id * 42,  # Seed différent par cluster
                'rollout_config': self._get_cluster_specific_config(cluster_id)
            }
            cluster_tasks.append(task)
        
        return cluster_tasks
    
    def _get_cluster_specific_config(self, cluster_id):
        """
        Configuration spécifique par cluster pour diversification
        """
        base_config = self.config.rollout_config.copy()
        
        # Diversification des paramètres par cluster
        diversification_factor = cluster_id / self.num_cores
        
        # Variation de l'exploration
        base_config['exploration_factor'] = (
            0.1 + 0.3 * diversification_factor
        )
        
        # Variation de la profondeur d'analyse
        base_config['analysis_depth'] = max(1, int(3 + 2 * diversification_factor))
        
        # Variation des stratégies
        base_config['strategy_preference'] = cluster_id % 4  # 4 stratégies différentes
        
        return base_config
    
    def _execute_cluster_pipeline(self, cluster_id, task, timeout):
        """
        Exécute le pipeline d'un cluster avec monitoring
        """
        cluster_start_time = time.time()
        
        try:
            # Mise à jour état
            with self.coordination_lock:
                self.cluster_states[cluster_id]['status'] = 'running'
                self.cluster_states[cluster_id]['current_task'] = task
            
            # Exécution du pipeline cluster
            result = self._run_cluster_rollouts(cluster_id, task)
            
            # Calcul du temps d'exécution
            execution_time = time.time() - cluster_start_time
            result['execution_time'] = execution_time
            
            # Mise à jour historique performance
            self.cluster_states[cluster_id]['performance_history'].append({
                'execution_time': execution_time,
                'success': True,
                'timestamp': time.time()
            })
            
            return result
            
        except Exception as e:
            execution_time = time.time() - cluster_start_time
            
            # Enregistrement de l'erreur
            self.cluster_states[cluster_id]['performance_history'].append({
                'execution_time': execution_time,
                'success': False,
                'error': str(e),
                'timestamp': time.time()
            })
            
            raise e
    
    def _run_cluster_rollouts(self, cluster_id, task):
        """
        Exécute les 3 rollouts séquentiels d'un cluster
        """
        input_sequence = task['input_sequence']
        config = task['rollout_config']
        
        # ROLLOUT 1: Analyse de séquence
        analysis_result = self._rollout_sequence_analysis(
            input_sequence, 
            config,
            cluster_id
        )
        
        # ROLLOUT 2: Génération de candidats
        candidates = self._rollout_candidate_generation(
            analysis_result,
            config,
            cluster_id
        )
        
        # ROLLOUT 3: Sélection optimale
        prediction_result = self._rollout_optimal_selection(
            candidates,
            analysis_result,
            config,
            cluster_id
        )
        
        return {
            'prediction': prediction_result['outcome'],
            'confidence': prediction_result['confidence'],
            'analysis_quality': analysis_result['quality_score'],
            'candidates_count': len(candidates),
            'cluster_performance': self._calculate_cluster_performance(cluster_id)
        }
```

### **🔄 Rollouts Spécialisés Parallèles**

```python
def _rollout_sequence_analysis(self, sequence, config, cluster_id):
    """
    Rollout 1: Analyse parallélisée de la séquence
    """
    # Analyse multi-dimensionnelle
    analysis_components = {
        'pattern_detection': self._detect_patterns_parallel(sequence, cluster_id),
        'statistical_analysis': self._analyze_statistics_parallel(sequence, cluster_id),
        'trend_analysis': self._analyze_trends_parallel(sequence, cluster_id),
        'complexity_measure': self._measure_complexity_parallel(sequence, cluster_id)
    }
    
    # Agrégation des résultats
    quality_score = np.mean([
        analysis_components['pattern_detection']['confidence'],
        analysis_components['statistical_analysis']['reliability'],
        analysis_components['trend_analysis']['strength'],
        analysis_components['complexity_measure']['accuracy']
    ])
    
    return {
        'components': analysis_components,
        'quality_score': quality_score,
        'cluster_id': cluster_id
    }

def _rollout_candidate_generation(self, analysis_result, config, cluster_id):
    """
    Rollout 2: Génération parallélisée de candidats
    """
    num_candidates = config.get('num_candidates', 4)
    candidates = []
    
    # Génération diversifiée selon le cluster
    for i in range(num_candidates):
        # Stratégie variable selon cluster et candidat
        strategy_id = (cluster_id + i) % 4
        
        candidate = self._generate_candidate_with_strategy(
            strategy_id,
            analysis_result,
            cluster_id
        )
        
        candidates.append(candidate)
    
    return candidates

def _rollout_optimal_selection(self, candidates, analysis_result, config, cluster_id):
    """
    Rollout 3: Sélection parallélisée optimale
    """
    # Évaluation de chaque candidat
    evaluated_candidates = []
    
    for candidate in candidates:
        evaluation = self._evaluate_candidate_parallel(
            candidate,
            analysis_result,
            cluster_id
        )
        
        evaluated_candidates.append({
            'candidate': candidate,
            'evaluation': evaluation
        })
    
    # Sélection du meilleur
    optimal = max(
        evaluated_candidates,
        key=lambda x: x['evaluation']['composite_score']
    )
    
    return {
        'outcome': optimal['candidate']['prediction'],
        'confidence': optimal['evaluation']['confidence'],
        'selection_quality': optimal['evaluation']['composite_score']
    }
```

---

## 📊 **OPTIMISATIONS DE PERFORMANCE**

### **⚡ Gestion Mémoire Optimisée**

```python
class MemoryOptimizedRollouts:
    """
    Optimisations mémoire pour rollouts parallèles
    """
    
    def __init__(self, config):
        self.config = config
        
        # Pool d'objets réutilisables
        self.object_pools = {
            'analysis_buffers': queue.Queue(maxsize=16),
            'candidate_buffers': queue.Queue(maxsize=32),
            'result_buffers': queue.Queue(maxsize=16)
        }
        
        # Pré-allocation des buffers
        self._preallocate_buffers()
        
        # Monitoring mémoire
        self.memory_stats = {
            'peak_usage': 0,
            'current_usage': 0,
            'allocations': 0,
            'deallocations': 0
        }
    
    def _preallocate_buffers(self):
        """
        Pré-allocation des buffers pour éviter les allocations dynamiques
        """
        # Buffers d'analyse
        for _ in range(16):
            buffer = {
                'patterns': np.zeros(10),
                'statistics': np.zeros(20),
                'trends': np.zeros(5),
                'metadata': {}
            }
            self.object_pools['analysis_buffers'].put(buffer)
        
        # Buffers de candidats
        for _ in range(32):
            buffer = {
                'prediction': '',
                'confidence': 0.0,
                'features': np.zeros(15),
                'metadata': {}
            }
            self.object_pools['candidate_buffers'].put(buffer)
    
    def get_buffer(self, buffer_type):
        """
        Obtient un buffer réutilisable du pool
        """
        try:
            buffer = self.object_pools[buffer_type].get_nowait()
            self.memory_stats['allocations'] += 1
            return buffer
        except queue.Empty:
            # Création d'un nouveau buffer si pool vide
            return self._create_new_buffer(buffer_type)
    
    def return_buffer(self, buffer, buffer_type):
        """
        Retourne un buffer au pool après nettoyage
        """
        # Nettoyage du buffer
        self._clean_buffer(buffer, buffer_type)
        
        # Retour au pool si pas plein
        try:
            self.object_pools[buffer_type].put_nowait(buffer)
            self.memory_stats['deallocations'] += 1
        except queue.Full:
            # Pool plein, laisser le GC s'en occuper
            pass
```

### **🎯 Load Balancing Dynamique**

```python
class DynamicLoadBalancer:
    """
    Équilibrage de charge dynamique entre clusters
    """
    
    def __init__(self, parallelization_manager):
        self.manager = parallelization_manager
        self.load_history = {
            i: deque(maxlen=20) 
            for i in range(parallelization_manager.num_cores)
        }
        
    def balance_workload(self, tasks):
        """
        Équilibre la charge de travail entre clusters
        """
        # Calcul de la charge actuelle de chaque cluster
        cluster_loads = {}
        
        for cluster_id in range(self.manager.num_cores):
            cluster_loads[cluster_id] = self._calculate_cluster_load(cluster_id)
        
        # Tri des clusters par charge (moins chargé en premier)
        sorted_clusters = sorted(
            cluster_loads.items(), 
            key=lambda x: x[1]
        )
        
        # Attribution des tâches aux clusters les moins chargés
        balanced_assignment = {}
        
        for i, task in enumerate(tasks):
            cluster_id = sorted_clusters[i % len(sorted_clusters)][0]
            
            if cluster_id not in balanced_assignment:
                balanced_assignment[cluster_id] = []
            
            balanced_assignment[cluster_id].append(task)
        
        return balanced_assignment
    
    def _calculate_cluster_load(self, cluster_id):
        """
        Calcule la charge actuelle d'un cluster
        """
        cluster_state = self.manager.cluster_states[cluster_id]
        
        # Facteurs de charge
        factors = {}
        
        # 1. Temps d'exécution récent
        recent_times = [
            perf['execution_time'] 
            for perf in cluster_state['performance_history']
            if perf['success']
        ]
        
        if recent_times:
            factors['avg_execution_time'] = np.mean(recent_times[-5:])
        else:
            factors['avg_execution_time'] = 0.1  # Valeur par défaut
        
        # 2. Taux de succès
        recent_successes = [
            perf['success'] 
            for perf in cluster_state['performance_history']
        ]
        
        if recent_successes:
            factors['success_rate'] = np.mean(recent_successes)
        else:
            factors['success_rate'] = 1.0  # Optimiste par défaut
        
        # 3. État actuel
        status_load = {
            'idle': 0.0,
            'running': 1.0,
            'completed': 0.2,
            'error': 0.5
        }
        
        factors['current_status'] = status_load.get(cluster_state['status'], 0.5)
        
        # Score de charge composite
        load_score = (
            0.5 * factors['avg_execution_time'] +
            0.3 * (1.0 - factors['success_rate']) +
            0.2 * factors['current_status']
        )
        
        return load_score
```

---

## 📈 **MONITORING ET PROFILING**

### **📊 Profiler de Performance**

```python
class AZRPerformanceProfiler:
    """
    Profiler spécialisé pour les performances AZR
    """
    
    def __init__(self):
        self.profiling_data = {
            'cluster_timings': {},
            'rollout_timings': {},
            'memory_snapshots': [],
            'cpu_utilization': [],
            'bottlenecks': []
        }
        
    def profile_execution(self, execution_func, *args, **kwargs):
        """
        Profile une exécution avec métriques détaillées
        """
        # Snapshot initial
        start_memory = self._get_memory_usage()
        start_time = time.perf_counter()
        
        # Exécution avec profiling
        try:
            result = execution_func(*args, **kwargs)
            
            # Métriques finales
            end_time = time.perf_counter()
            end_memory = self._get_memory_usage()
            
            # Enregistrement des métriques
            profile_result = {
                'execution_time': end_time - start_time,
                'memory_delta': end_memory - start_memory,
                'peak_memory': max(start_memory, end_memory),
                'success': True,
                'result': result
            }
            
            self._record_profiling_data(profile_result)
            
            return result
            
        except Exception as e:
            end_time = time.perf_counter()
            
            profile_result = {
                'execution_time': end_time - start_time,
                'memory_delta': 0,
                'success': False,
                'error': str(e)
            }
            
            self._record_profiling_data(profile_result)
            raise e
    
    def generate_performance_report(self):
        """
        Génère un rapport de performance détaillé
        """
        report = {
            'summary': self._generate_summary(),
            'cluster_analysis': self._analyze_cluster_performance(),
            'bottleneck_analysis': self._identify_bottlenecks(),
            'optimization_recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _identify_bottlenecks(self):
        """
        Identifie les goulots d'étranglement de performance
        """
        bottlenecks = []
        
        # Analyse des temps d'exécution par cluster
        cluster_times = self.profiling_data['cluster_timings']
        
        if cluster_times:
            avg_times = {
                cluster_id: np.mean(times) 
                for cluster_id, times in cluster_times.items()
            }
            
            max_time = max(avg_times.values())
            min_time = min(avg_times.values())
            
            # Détection de déséquilibre
            if max_time > min_time * 1.5:  # 50% de différence
                bottlenecks.append({
                    'type': 'cluster_imbalance',
                    'severity': 'medium',
                    'description': f'Déséquilibre entre clusters: {max_time:.3f}s vs {min_time:.3f}s',
                    'recommendation': 'Rééquilibrer la charge ou optimiser les clusters lents'
                })
        
        return bottlenecks
```

---

## 📝 **POINTS CLÉS À RETENIR**

1. **⚡ Architecture Optimale** : 8 clusters × 3 rollouts = parallélisation efficace
2. **🔄 Stratégie Hybride** : Parallèle inter-clusters + Séquentiel intra-cluster
3. **📊 Load Balancing** : Équilibrage dynamique selon performance réelle
4. **💾 Optimisation Mémoire** : Pools d'objets et pré-allocation
5. **📈 Monitoring** : Profiling détaillé pour optimisation continue

---

## 🎯 **EXERCICE PRATIQUE**

**Implémentation :** Créez un gestionnaire de parallélisation simple :

```python
class SimpleParallelManager:
    def __init__(self, num_workers=4):
        self.num_workers = num_workers
        self.thread_pool = None
        
    def execute_parallel(self, tasks):
        # TODO: Exécution parallèle des tâches
        pass
        
    def collect_results(self, futures, timeout=1.0):
        # TODO: Collecte des résultats avec timeout
        pass
```

---

**➡️ Prochaine section : [3.5 - Communication Inter-Clusters](05_Communication_Inter_Clusters.md)**
