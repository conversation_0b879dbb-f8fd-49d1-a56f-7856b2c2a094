#!/usr/bin/env python3
"""
Analyse de la capacité de lecture du Rollout 2

Ce script vérifie si le Rollout 2 peut lire et exploiter toutes les informations
que le Rollout 1 lui communique dans les sections optimisées.
"""

import re
from typing import Dict, List, Set

def extract_rollout1_generated_fields(file_content: str) -> Dict[str, Set[str]]:
    """Extrait tous les champs générés par le Rollout 1 dans les sections optimisées"""
    
    generated_fields = {
        'signals_summary': set(),
        'generation_guidance': set(),
        'quick_access': set()
    }
    
    # Analyser _generate_signals_summary
    signals_pattern = r'def _generate_signals_summary\(.*?\):(.*?)(?=def |\Z)'
    match = re.search(signals_pattern, file_content, re.DOTALL)
    if match:
        method_content = match.group(1)
        # Chercher les assignations de champs
        field_assignments = re.findall(r"'([^']+)':\s*[^,}]+", method_content)
        generated_fields['signals_summary'].update(field_assignments)
    
    # Analyser _generate_generation_guidance
    guidance_pattern = r'def _generate_generation_guidance\(.*?\):(.*?)(?=def |\Z)'
    match = re.search(guidance_pattern, file_content, re.DOTALL)
    if match:
        method_content = match.group(1)
        field_assignments = re.findall(r"'([^']+)':\s*[^,}]+", method_content)
        generated_fields['generation_guidance'].update(field_assignments)
    
    # Analyser _generate_quick_access
    quick_pattern = r'def _generate_quick_access\(.*?\):(.*?)(?=def |\Z)'
    match = re.search(quick_pattern, file_content, re.DOTALL)
    if match:
        method_content = match.group(1)
        field_assignments = re.findall(r"'([^']+)':\s*[^,}]+", method_content)
        generated_fields['quick_access'].update(field_assignments)
    
    return generated_fields

def extract_rollout2_read_fields(file_content: str) -> Dict[str, Set[str]]:
    """Extrait tous les champs lus par le Rollout 2 depuis les sections optimisées"""
    
    read_fields = {
        'signals_summary': set(),
        'generation_guidance': set(),
        'quick_access': set()
    }
    
    # Méthodes du Rollout 2 qui lisent les sections optimisées
    rollout2_methods = [
        '_define_optimized_generation_space',
        '_generate_sequences_from_signals',
        '_generate_sequence_from_signal'
    ]
    
    for method_name in rollout2_methods:
        method_pattern = rf'def {re.escape(method_name)}\(.*?\):(.*?)(?=def |\Z)'
        match = re.search(method_pattern, file_content, re.DOTALL)
        
        if match:
            method_content = match.group(1)
            
            # Chercher les accès aux sections
            signals_accesses = re.findall(r"signals_summary\.get\('([^']+)'", method_content)
            guidance_accesses = re.findall(r"generation_guidance\.get\('([^']+)'", method_content)
            quick_accesses = re.findall(r"quick_access\.get\('([^']+)'", method_content)
            
            read_fields['signals_summary'].update(signals_accesses)
            read_fields['generation_guidance'].update(guidance_accesses)
            read_fields['quick_access'].update(quick_accesses)
    
    return read_fields

def analyze_signal_structure_compatibility(file_content: str) -> Dict:
    """Analyse la compatibilité de la structure des signaux"""
    
    compatibility = {
        'signal_fields_generated': set(),
        'signal_fields_read': set(),
        'missing_signal_fields': set(),
        'compatibility_score': 0
    }
    
    # Analyser la génération des signaux dans _generate_signals_summary
    signals_pattern = r'def _generate_signals_summary\(.*?\):(.*?)(?=def |\Z)'
    match = re.search(signals_pattern, file_content, re.DOTALL)
    if match:
        method_content = match.group(1)
        
        # Chercher la structure des signaux générés
        signal_structures = re.findall(r'top_signals\.append\(\s*\{(.*?)\}\s*\)', method_content, re.DOTALL)
        for structure in signal_structures:
            fields = re.findall(r"'([^']+)':", structure)
            compatibility['signal_fields_generated'].update(fields)
    
    # Analyser la lecture des signaux dans _generate_sequence_from_signal
    read_pattern = r'def _generate_sequence_from_signal\(.*?\):(.*?)(?=def |\Z)'
    match = re.search(read_pattern, file_content, re.DOTALL)
    if match:
        method_content = match.group(1)
        
        # Chercher les accès aux champs des signaux
        signal_accesses = re.findall(r"signal\.get\('([^']+)'", method_content)
        compatibility['signal_fields_read'].update(signal_accesses)
    
    # Calculer les champs manquants
    compatibility['missing_signal_fields'] = (
        compatibility['signal_fields_read'] - compatibility['signal_fields_generated']
    )
    
    # Calculer le score de compatibilité
    if compatibility['signal_fields_read']:
        compatibility_ratio = len(compatibility['signal_fields_generated'] & compatibility['signal_fields_read']) / len(compatibility['signal_fields_read'])
        compatibility['compatibility_score'] = compatibility_ratio * 100
    else:
        compatibility['compatibility_score'] = 100
    
    return compatibility

def check_missing_method_implementations(file_content: str) -> Dict:
    """Vérifie les méthodes manquantes référencées par le Rollout 2"""
    
    missing_methods = {
        'referenced_methods': set(),
        'implemented_methods': set(),
        'missing_methods': set(),
        'implementation_score': 0
    }
    
    # Méthodes référencées dans _generate_sequence_from_signal
    method_pattern = r'def _generate_sequence_from_signal\(.*?\):(.*?)(?=def |\Z)'
    match = re.search(method_pattern, file_content, re.DOTALL)
    if match:
        method_content = match.group(1)
        
        # Chercher les appels de méthodes
        method_calls = re.findall(r'self\.(_generate_[a-zA-Z_]+_sequence)\(', method_content)
        missing_methods['referenced_methods'].update(method_calls)
    
    # Vérifier quelles méthodes sont implémentées
    for method in missing_methods['referenced_methods']:
        if f'def {method}(' in file_content:
            missing_methods['implemented_methods'].add(method)
        else:
            missing_methods['missing_methods'].add(method)
    
    # Calculer le score d'implémentation
    if missing_methods['referenced_methods']:
        implementation_ratio = len(missing_methods['implemented_methods']) / len(missing_methods['referenced_methods'])
        missing_methods['implementation_score'] = implementation_ratio * 100
    else:
        missing_methods['implementation_score'] = 100
    
    return missing_methods

def analyze_rollout2_reading_capability():
    """Analyse principale de la capacité de lecture du Rollout 2"""
    
    try:
        with open('azr_baccarat_predictor.py', 'r', encoding='utf-8') as f:
            file_content = f.read()
    except FileNotFoundError:
        print("❌ Fichier azr_baccarat_predictor.py non trouvé")
        return
    
    print("🔍 ANALYSE CAPACITÉ DE LECTURE ROLLOUT 2")
    print("=" * 45)
    
    # 1. Analyser les champs générés vs lus
    print("\n📊 1. COMPATIBILITÉ DES SECTIONS OPTIMISÉES")
    print("-" * 45)
    
    generated_fields = extract_rollout1_generated_fields(file_content)
    read_fields = extract_rollout2_read_fields(file_content)
    
    for section in ['signals_summary', 'generation_guidance', 'quick_access']:
        generated = generated_fields[section]
        read = read_fields[section]
        missing = read - generated
        unused = generated - read
        
        print(f"\n📋 {section.replace('_', ' ').title()}")
        print(f"   Générés par Rollout 1 : {len(generated)}")
        print(f"   Lus par Rollout 2      : {len(read)}")
        print(f"   Manquants             : {len(missing)}")
        print(f"   Non utilisés          : {len(unused)}")
        
        if missing:
            print(f"   ❌ Champs manquants : {', '.join(list(missing)[:3])}")
            if len(missing) > 3:
                print(f"      ... et {len(missing) - 3} autres")
        
        if unused:
            print(f"   ⚠️  Champs inutilisés : {', '.join(list(unused)[:3])}")
            if len(unused) > 3:
                print(f"      ... et {len(unused) - 3} autres")
        
        if not missing:
            print("   ✅ Tous les champs requis sont générés")
    
    # 2. Analyser la compatibilité des signaux
    print("\n🎯 2. COMPATIBILITÉ STRUCTURE DES SIGNAUX")
    print("-" * 40)
    
    signal_compatibility = analyze_signal_structure_compatibility(file_content)
    
    print(f"📊 Champs signaux générés : {len(signal_compatibility['signal_fields_generated'])}")
    print(f"📊 Champs signaux lus     : {len(signal_compatibility['signal_fields_read'])}")
    print(f"📊 Score compatibilité    : {signal_compatibility['compatibility_score']:.1f}%")
    
    if signal_compatibility['missing_signal_fields']:
        print(f"❌ Champs signaux manquants : {', '.join(signal_compatibility['missing_signal_fields'])}")
    else:
        print("✅ Tous les champs de signaux requis sont générés")
    
    # 3. Vérifier les méthodes manquantes
    print("\n🔧 3. MÉTHODES D'IMPLÉMENTATION")
    print("-" * 30)
    
    missing_methods = check_missing_method_implementations(file_content)
    
    print(f"📊 Méthodes référencées   : {len(missing_methods['referenced_methods'])}")
    print(f"📊 Méthodes implémentées  : {len(missing_methods['implemented_methods'])}")
    print(f"📊 Score implémentation   : {missing_methods['implementation_score']:.1f}%")
    
    if missing_methods['missing_methods']:
        print(f"❌ Méthodes manquantes :")
        for method in list(missing_methods['missing_methods'])[:5]:
            print(f"   • {method}")
        if len(missing_methods['missing_methods']) > 5:
            print(f"   ... et {len(missing_methods['missing_methods']) - 5} autres")
    else:
        print("✅ Toutes les méthodes référencées sont implémentées")
    
    # 4. Score global et recommandations
    print(f"\n📊 4. ÉVALUATION GLOBALE")
    print("-" * 25)
    
    # Calculer le score global
    section_scores = []
    for section in ['signals_summary', 'generation_guidance', 'quick_access']:
        generated = len(generated_fields[section])
        read = len(read_fields[section])
        missing = len(read_fields[section] - generated_fields[section])
        
        if read > 0:
            section_score = max(0, 100 - (missing * 20))  # Pénalité de 20 points par champ manquant
        else:
            section_score = 100
        section_scores.append(section_score)
    
    avg_section_score = sum(section_scores) / len(section_scores) if section_scores else 0
    signal_score = signal_compatibility['compatibility_score']
    method_score = missing_methods['implementation_score']
    
    global_score = (avg_section_score + signal_score + method_score) / 3
    
    print(f"Score sections     : {avg_section_score:.1f}/100")
    print(f"Score signaux      : {signal_score:.1f}/100")
    print(f"Score méthodes     : {method_score:.1f}/100")
    print(f"Score global       : {global_score:.1f}/100")
    
    if global_score >= 90:
        print("\n🏆 EXCELLENT : Le Rollout 2 peut lire toutes les informations !")
        print("✅ Communication parfaitement compatible")
        print("✅ Toutes les sections sont exploitées")
        print("✅ Structure des signaux compatible")
    elif global_score >= 70:
        print("\n👍 BON : Le Rollout 2 peut lire la plupart des informations")
        print("⚠️  Quelques améliorations possibles")
    else:
        print("\n⚠️  PROBLÈMES DÉTECTÉS :")
        if avg_section_score < 70:
            print("❌ Incompatibilités dans les sections optimisées")
        if signal_score < 70:
            print("❌ Structure des signaux incompatible")
        if method_score < 70:
            print("❌ Méthodes d'implémentation manquantes")
    
    print(f"\n💡 RECOMMANDATIONS")
    print("-" * 20)
    
    total_issues = 0
    
    # Compter les problèmes
    for section in ['signals_summary', 'generation_guidance', 'quick_access']:
        missing = read_fields[section] - generated_fields[section]
        total_issues += len(missing)
    
    total_issues += len(signal_compatibility['missing_signal_fields'])
    total_issues += len(missing_methods['missing_methods'])
    
    if total_issues == 0:
        print("✅ Aucune action nécessaire - Communication parfaite !")
    else:
        print(f"🔄 {total_issues} problèmes à résoudre :")
        print("1. Ajouter les champs manquants dans les sections du Rollout 1")
        print("2. Implémenter les méthodes manquantes référencées")
        print("3. Vérifier la cohérence des structures de données")
        print("4. Tester la communication complète entre rollouts")

if __name__ == "__main__":
    analyze_rollout2_reading_capability()
