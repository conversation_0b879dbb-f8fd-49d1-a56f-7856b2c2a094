#!/usr/bin/env python3
"""
Implémentation Python du modèle Absolute Zero Reasoner (AZR)
Basé sur le paper "Absolute Zero: Reinforced Self-play Reasoning with Zero Data"
"""

import torch
import torch.nn as nn
import torch.optim as optim
import random
import json
import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from collections import deque
import ast
import sys
import io
from contextlib import redirect_stdout, redirect_stderr
import time

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Task:
    """Représente une tâche de raisonnement"""
    task_type: str  # "deduction", "induction", "abduction"
    program: Optional[str] = None
    input_data: Optional[str] = None
    output_data: Optional[str] = None
    query: Optional[str] = None
    ground_truth: Optional[str] = None

@dataclass
class Solution:
    """Représente une solution à une tâche"""
    content: str
    is_correct: bool = False
    execution_time: float = 0.0
    error_message: Optional[str] = None

class PythonExecutor:
    """Exécuteur Python sécurisé pour validation des tâches"""

    def __init__(self, timeout: float = 5.0):
        self.timeout = timeout
        self.allowed_builtins = {
            'len', 'range', 'enumerate', 'zip', 'map', 'filter',
            'sum', 'max', 'min', 'abs', 'round', 'sorted', 'list',
            'dict', 'set', 'tuple', 'str', 'int', 'float', 'bool'
        }

    def safe_eval(self, code: str, input_data: Any = None) -> Tuple[Any, bool, str]:
        """Évalue du code Python de manière sécurisée"""
        try:
            # Préparer l'environnement d'exécution
            safe_globals = {
                '__builtins__': {name: getattr(__builtins__, name)
                               for name in self.allowed_builtins
                               if hasattr(__builtins__, name)}
            }

            if input_data is not None:
                safe_globals['input_data'] = input_data

            # Capturer la sortie
            old_stdout = sys.stdout
            old_stderr = sys.stderr
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()

            start_time = time.time()

            try:
                sys.stdout = stdout_capture
                sys.stderr = stderr_capture

                # Exécuter le code
                result = eval(code, safe_globals)
                execution_time = time.time() - start_time

                if execution_time > self.timeout:
                    return None, False, "Timeout exceeded"

                return result, True, ""

            finally:
                sys.stdout = old_stdout
                sys.stderr = old_stderr

        except Exception as e:
            return None, False, str(e)

    def validate_triplet(self, program: str, input_data: str, expected_output: str) -> bool:
        """Valide qu'un triplet (programme, input, output) est cohérent"""
        try:
            # Préparer le code d'exécution
            exec_code = f"""
def program_func(x):
{program}
    return result

result = program_func({input_data})
"""
            result, success, error = self.safe_eval(exec_code)

            if not success:
                return False

            # Comparer avec la sortie attendue
            expected, exp_success, _ = self.safe_eval(expected_output)

            return exp_success and result == expected

        except Exception:
            return False

class TaskBuffer:
    """Buffer circulaire pour stocker les tâches et solutions passées"""

    def __init__(self, max_size: int = 1000):
        self.buffer = deque(maxlen=max_size)
        self.max_size = max_size

    def add(self, task: Task, solution: Solution):
        """Ajoute une paire tâche-solution au buffer"""
        self.buffer.append((task, solution))

    def sample(self, k: int = 5, task_type: Optional[str] = None) -> List[Tuple[Task, Solution]]:
        """Échantillonne k exemples du buffer"""
        if task_type:
            filtered = [(t, s) for t, s in self.buffer if t.task_type == task_type]
            available = filtered
        else:
            available = list(self.buffer)

        if not available:
            return []

        sample_size = min(k, len(available))
        return random.sample(available, sample_size)

    def get_stats(self) -> Dict[str, int]:
        """Retourne des statistiques sur le buffer"""
        stats = {"total": len(self.buffer)}
        for task_type in ["deduction", "induction", "abduction"]:
            count = sum(1 for t, s in self.buffer if t.task_type == task_type)
            stats[task_type] = count
        return stats

class RewardSystem:
    """Système de récompenses pour AZR"""

    def __init__(self, n_rollouts: int = 3):
        self.n_rollouts = n_rollouts

    def learnability_reward(self, task: Task, solver_func, **kwargs) -> float:
        """
        Calcule la récompense de proposabilité basée sur la difficulté optimale
        r_propose = 1 - r̄_solve si 0 < r̄_solve < 1, sinon 0
        """
        if not task.query or not task.ground_truth:
            return 0.0

        success_count = 0

        for _ in range(self.n_rollouts):
            try:
                solution = solver_func(task.query)
                if solution and solution.is_correct:
                    success_count += 1
            except Exception:
                continue

        avg_success = success_count / self.n_rollouts

        # Récompense maximale pour difficulté modérée
        if 0 < avg_success < 1:
            return 1 - avg_success
        else:
            return 0.0  # Trop facile ou impossible

    def solution_reward(self, predicted: str, ground_truth: str, executor: PythonExecutor) -> float:
        """Récompense binaire pour la résolution"""
        try:
            pred_result, pred_success, _ = executor.safe_eval(predicted)
            true_result, true_success, _ = executor.safe_eval(ground_truth)

            if pred_success and true_success:
                return 1.0 if pred_result == true_result else 0.0
            else:
                return 0.0
        except Exception:
            return 0.0

    def composite_reward(self, response: str, role: str, task: Optional[Task] = None) -> float:
        """Récompense composite avec pénalités de format"""
        # Vérification basique du format
        if not response or len(response.strip()) == 0:
            return -1.0

        # Vérification de la structure XML basique
        if "<think>" in response and "</think>" in response and "<answer>" in response and "</answer>" in response:
            if role == "solve" and task:
                # Extraire la réponse
                try:
                    answer_start = response.find("<answer>") + 8
                    answer_end = response.find("</answer>")
                    answer = response[answer_start:answer_end].strip()

                    executor = PythonExecutor()
                    return self.solution_reward(answer, task.ground_truth, executor)
                except Exception:
                    return -0.5
            else:
                return 0.5  # Format correct mais pas de vérification de contenu
        else:
            return -0.5  # Format partiellement correct

class ReasoningModes:
    """Générateur de tâches pour les trois modes de raisonnement"""

    def __init__(self, executor: PythonExecutor):
        self.executor = executor

    def create_deduction_task(self, program: str, input_data: str) -> Optional[Task]:
        """Crée une tâche de déduction: ? = FP(I)"""
        try:
            # Exécuter le programme pour obtenir l'output
            exec_code = f"""
def program_func(x):
{program}
    return result

result = program_func({input_data})
"""
            output, success, error = self.executor.safe_eval(exec_code)

            if not success:
                return None

            query = f"""Given the program:
{program}

And input: {input_data}

What is the output?"""

            return Task(
                task_type="deduction",
                program=program,
                input_data=input_data,
                output_data=str(output),
                query=query,
                ground_truth=str(output)
            )
        except Exception:
            return None

    def create_induction_task(self, input_data: str, output_data: str) -> Optional[Task]:
        """Crée une tâche d'induction: OX = ?(I)"""
        query = f"""Given input: {input_data}
And expected output: {output_data}

Write a Python function that transforms the input to the output."""

        return Task(
            task_type="induction",
            input_data=input_data,
            output_data=output_data,
            query=query,
            ground_truth=output_data  # La vérification se fait par exécution
        )

    def create_abduction_task(self, program: str, output_data: str) -> Optional[Task]:
        """Crée une tâche d'abduction: OX = FP(?)"""
        query = f"""Given the program:
{program}

And expected output: {output_data}

What input would produce this output?"""

        return Task(
            task_type="abduction",
            program=program,
            output_data=output_data,
            query=query,
            ground_truth=output_data  # La vérification se fait par exécution
        )

class SimpleLanguageModel(nn.Module):
    """Modèle de langage simplifié pour démonstration"""

    def __init__(self, vocab_size: int = 10000, hidden_size: int = 512):
        super().__init__()
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Architecture simplifiée
        self.embedding = nn.Embedding(vocab_size, hidden_size)
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(hidden_size, nhead=8, batch_first=True),
            num_layers=6
        )
        self.output_head = nn.Linear(hidden_size, vocab_size)

        # Tokenizer basique (en pratique, utiliser un vrai tokenizer)
        self.tokenizer = self._create_simple_tokenizer()

        self.to(self.device)

    def _create_simple_tokenizer(self):
        """Crée un tokenizer basique pour démonstration"""
        # En pratique, utiliser un vrai tokenizer comme celui de Hugging Face
        return {
            "encode": lambda x: [hash(word) % self.vocab_size for word in x.split()],
            "decode": lambda x: " ".join([f"token_{i}" for i in x])
        }

    def generate(self, prompt: str, max_length: int = 512, temperature: float = 0.8) -> str:
        """Génère du texte à partir d'un prompt"""
        # Implémentation simplifiée pour démonstration
        # En pratique, utiliser une vraie génération de texte

        # Simulation d'une génération basique
        templates = {
            "deduction": "<think>I need to execute the program step by step.</think><answer>42</answer>",
            "induction": "<think>I need to find a pattern.</think><answer>def func(x): return x * 2</answer>",
            "abduction": "<think>I need to find the input.</think><answer>21</answer>"
        }

        # Détection basique du type de tâche
        if "program:" in prompt.lower() and "input:" in prompt.lower():
            return templates["deduction"]
        elif "input:" in prompt.lower() and "output:" in prompt.lower():
            return templates["induction"]
        elif "program:" in prompt.lower() and "expected output:" in prompt.lower():
            return templates["abduction"]
        else:
            return "<think>I'm not sure about this task.</think><answer>Unknown</answer>"

    def to(self, device):
        """Déplace le modèle vers un device"""
        self.embedding = self.embedding.to(device)
        self.transformer = self.transformer.to(device)
        self.output_head = self.output_head.to(device)
        return self

class AbsoluteZeroReasoner:
    """Implémentation principale du modèle AZR"""

    def __init__(self, base_model: Optional[SimpleLanguageModel] = None, learning_rate: float = 1e-4):
        self.model = base_model or SimpleLanguageModel()
        self.executor = PythonExecutor()
        self.reward_system = RewardSystem()
        self.reasoning_modes = ReasoningModes(self.executor)
        self.task_buffer = TaskBuffer()

        # Paramètres d'entraînement
        self.lambda_coeff = 1.0  # Balance entre propose et solve rewards
        self.baseline_propose = 0.0
        self.baseline_solve = 0.0

        # Optimiseur (simplifié pour démonstration)
        self.optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)

        # Initialisation avec quelques tâches de base
        self._initialize_task_buffer()

    def _initialize_task_buffer(self):
        """Initialise le buffer avec quelques tâches de base"""
        # Tâches de déduction simples
        basic_tasks = [
            ("x + 1", "5", "6"),
            ("x * 2", "3", "6"),
            ("x ** 2", "4", "16"),
            ("len(x)", "[1, 2, 3]", "3"),
            ("sum(x)", "[1, 2, 3]", "6")
        ]

        for program, input_data, expected_output in basic_tasks:
            task = self.reasoning_modes.create_deduction_task(
                f"    result = {program}", input_data
            )
            if task:
                # Créer une solution factice pour l'initialisation
                solution = Solution(
                    content=f"<think>Simple calculation</think><answer>{expected_output}</answer>",
                    is_correct=True
                )
                self.task_buffer.add(task, solution)

    def propose_task(self, task_type: str, reference_examples: List[Tuple[Task, Solution]], k: int = 5) -> Optional[Task]:
        """Propose une nouvelle tâche basée sur le type et des exemples de référence"""

        # Construire le prompt de proposition
        prompt = f"Generate a new {task_type} task different from these examples:\n\n"

        for i, (task, solution) in enumerate(reference_examples[:k]):
            prompt += f"Example {i+1}:\n{task.query}\nSolution: {solution.content}\n\n"

        prompt += f"Now generate a new {task_type} task:"

        # Générer la proposition
        response = self.model.generate(prompt, max_length=512, temperature=0.8)

        # Parser la réponse pour créer une tâche (implémentation simplifiée)
        return self._parse_proposed_task(response, task_type)

    def _parse_proposed_task(self, response: str, task_type: str) -> Optional[Task]:
        """Parse une réponse de proposition en tâche valide"""
        # Implémentation simplifiée - en pratique, parser plus sophistiqué
        try:
            if task_type == "deduction":
                # Générer une tâche de déduction simple
                programs = ["x + 10", "x * 3", "x - 5", "x // 2", "x % 7"]
                inputs = ["7", "12", "15", "20", "25"]

                program = random.choice(programs)
                input_val = random.choice(inputs)

                return self.reasoning_modes.create_deduction_task(
                    f"    result = {program}", input_val
                )

            elif task_type == "induction":
                # Générer une tâche d'induction simple
                input_output_pairs = [
                    ("[1, 2, 3]", "6"),
                    ("[4, 5, 6]", "15"),
                    ("'hello'", "5"),
                    ("[1, 1, 1]", "3")
                ]

                input_data, output_data = random.choice(input_output_pairs)
                return self.reasoning_modes.create_induction_task(input_data, output_data)

            elif task_type == "abduction":
                # Générer une tâche d'abduction simple
                programs = ["x * 2", "x + 5", "len(x)", "sum(x)"]
                outputs = ["10", "15", "3", "12"]

                program = random.choice(programs)
                output = random.choice(outputs)

                return self.reasoning_modes.create_abduction_task(
                    f"    result = {program}", output
                )

        except Exception as e:
            logger.warning(f"Failed to parse proposed task: {e}")
            return None

        return None

    def solve_task(self, task_query: str) -> Solution:
        """Résout une tâche donnée"""
        try:
            # Construire le prompt de résolution
            solve_prompt = f"Solve this task step by step:\n\n{task_query}\n\nProvide your reasoning in <think> tags and your final answer in <answer> tags."

            # Générer la solution
            response = self.model.generate(solve_prompt, max_length=1024, temperature=0.7)

            # Extraire la réponse
            answer = self._extract_answer(response)

            return Solution(
                content=response,
                is_correct=False,  # Sera évalué plus tard
                execution_time=0.0
            )

        except Exception as e:
            return Solution(
                content="",
                is_correct=False,
                error_message=str(e)
            )

    def _extract_answer(self, response: str) -> str:
        """Extrait la réponse finale d'une réponse formatée"""
        try:
            answer_start = response.find("<answer>") + 8
            answer_end = response.find("</answer>")
            if answer_start > 7 and answer_end > answer_start:
                return response[answer_start:answer_end].strip()
        except Exception:
            pass
        return ""

    def training_step(self, batch_size: int = 6) -> Dict[str, float]:
        """Une étape d'entraînement AZR complète"""
        metrics = {
            "propose_rewards": [],
            "solve_rewards": [],
            "total_tasks": 0,
            "successful_tasks": 0
        }

        # Phase 1: Proposition de tâches
        proposed_tasks = []
        propose_rewards = []

        task_types = ["deduction", "induction", "abduction"]
        tasks_per_type = batch_size // len(task_types)

        for task_type in task_types:
            for _ in range(tasks_per_type):
                try:
                    # Échantillonner des exemples de référence
                    ref_examples = self.task_buffer.sample(k=3, task_type=task_type)

                    if not ref_examples:
                        continue

                    # Proposer une nouvelle tâche
                    task = self.propose_task(task_type, ref_examples)
                    if task:
                        proposed_tasks.append(task)

                        # Calculer la récompense de proposabilité
                        reward = self.reward_system.learnability_reward(
                            task, self.solve_task
                        )
                        propose_rewards.append(reward)
                        metrics["propose_rewards"].append(reward)

                except Exception as e:
                    logger.warning(f"Error in task proposal: {e}")
                    continue

        # Phase 2: Résolution des tâches
        solve_rewards = []
        solutions = []

        for task in proposed_tasks:
            try:
                solution = self.solve_task(task.query)
                solutions.append(solution)

                # Vérifier et récompenser la solution
                answer = self._extract_answer(solution.content)
                reward = self.reward_system.solution_reward(
                    answer, task.ground_truth, self.executor
                )

                solution.is_correct = (reward > 0)
                solve_rewards.append(reward)
                metrics["solve_rewards"].append(reward)

                if reward > 0:
                    metrics["successful_tasks"] += 1

            except Exception as e:
                logger.warning(f"Error in task solving: {e}")
                solve_rewards.append(0.0)
                solutions.append(Solution("", False, 0.0, str(e)))

        metrics["total_tasks"] = len(proposed_tasks)

        # Phase 3: Mise à jour du buffer avec les tâches réussies
        for task, solution, reward in zip(proposed_tasks, solutions, solve_rewards):
            if reward > 0:  # Ajouter seulement les tâches résolues avec succès
                self.task_buffer.add(task, solution)

        # Phase 4: Mise à jour des baselines (moyennes mobiles)
        if propose_rewards:
            self.baseline_propose = 0.9 * self.baseline_propose + 0.1 * np.mean(propose_rewards)
        if solve_rewards:
            self.baseline_solve = 0.9 * self.baseline_solve + 0.1 * np.mean(solve_rewards)

        # Calculer les métriques finales
        metrics["avg_propose_reward"] = np.mean(propose_rewards) if propose_rewards else 0.0
        metrics["avg_solve_reward"] = np.mean(solve_rewards) if solve_rewards else 0.0
        metrics["success_rate"] = metrics["successful_tasks"] / max(metrics["total_tasks"], 1)

        return metrics

    def evaluate(self, test_tasks: Optional[List[Task]] = None, num_test_tasks: int = 20) -> Dict[str, float]:
        """Évalue les performances du modèle AZR"""
        if test_tasks is None:
            test_tasks = self._generate_test_tasks(num_test_tasks)

        results = {
            "total_tasks": len(test_tasks),
            "correct_solutions": 0,
            "deduction_accuracy": 0.0,
            "induction_accuracy": 0.0,
            "abduction_accuracy": 0.0
        }

        task_type_results = {"deduction": [], "induction": [], "abduction": []}

        for task in test_tasks:
            try:
                solution = self.solve_task(task.query)
                answer = self._extract_answer(solution.content)

                # Vérifier la solution
                is_correct = self.reward_system.solution_reward(
                    answer, task.ground_truth, self.executor
                ) > 0

                if is_correct:
                    results["correct_solutions"] += 1

                task_type_results[task.task_type].append(is_correct)

            except Exception as e:
                logger.warning(f"Error evaluating task: {e}")
                task_type_results[task.task_type].append(False)

        # Calculer les précisions par type de tâche
        for task_type in task_type_results:
            if task_type_results[task_type]:
                accuracy = sum(task_type_results[task_type]) / len(task_type_results[task_type])
                results[f"{task_type}_accuracy"] = accuracy

        results["overall_accuracy"] = results["correct_solutions"] / results["total_tasks"]

        return results

    def _generate_test_tasks(self, num_tasks: int) -> List[Task]:
        """Génère des tâches de test"""
        test_tasks = []
        task_types = ["deduction", "induction", "abduction"]
        tasks_per_type = num_tasks // len(task_types)

        for task_type in task_types:
            for _ in range(tasks_per_type):
                # Utiliser la même logique que pour la proposition mais avec des paramètres fixes
                if task_type == "deduction":
                    programs = ["x + 7", "x * 4", "x - 3"]
                    inputs = ["10", "5", "8"]

                    program = random.choice(programs)
                    input_val = random.choice(inputs)

                    task = self.reasoning_modes.create_deduction_task(
                        f"    result = {program}", input_val
                    )

                elif task_type == "induction":
                    input_output_pairs = [
                        ("[2, 4, 6]", "12"),
                        ("'test'", "4"),
                        ("[10, 20]", "30")
                    ]

                    input_data, output_data = random.choice(input_output_pairs)
                    task = self.reasoning_modes.create_induction_task(input_data, output_data)

                elif task_type == "abduction":
                    programs = ["x * 3", "x + 8", "len(x)"]
                    outputs = ["21", "18", "4"]

                    program = random.choice(programs)
                    output = random.choice(outputs)

                    task = self.reasoning_modes.create_abduction_task(
                        f"    result = {program}", output
                    )

                if task:
                    test_tasks.append(task)

        return test_tasks

    def save_checkpoint(self, filepath: str):
        """Sauvegarde un checkpoint du modèle"""
        checkpoint = {
            "model_state": self.model.state_dict() if hasattr(self.model, 'state_dict') else None,
            "optimizer_state": self.optimizer.state_dict() if hasattr(self.optimizer, 'state_dict') else None,
            "baseline_propose": self.baseline_propose,
            "baseline_solve": self.baseline_solve,
            "buffer_stats": self.task_buffer.get_stats()
        }

        try:
            torch.save(checkpoint, filepath)
            logger.info(f"Checkpoint saved to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save checkpoint: {e}")

    def load_checkpoint(self, filepath: str):
        """Charge un checkpoint du modèle"""
        try:
            checkpoint = torch.load(filepath)

            if checkpoint.get("model_state") and hasattr(self.model, 'load_state_dict'):
                self.model.load_state_dict(checkpoint["model_state"])

            if checkpoint.get("optimizer_state") and hasattr(self.optimizer, 'load_state_dict'):
                self.optimizer.load_state_dict(checkpoint["optimizer_state"])

            self.baseline_propose = checkpoint.get("baseline_propose", 0.0)
            self.baseline_solve = checkpoint.get("baseline_solve", 0.0)

            logger.info(f"Checkpoint loaded from {filepath}")

        except Exception as e:
            logger.error(f"Failed to load checkpoint: {e}")

def train_azr_model(base_model: Optional[SimpleLanguageModel] = None,
                   num_iterations: int = 100,
                   batch_size: int = 6,
                   eval_interval: int = 10,
                   save_interval: int = 50) -> AbsoluteZeroReasoner:
    """Pipeline d'entraînement complet pour AZR"""

    logger.info("Initializing AZR model...")
    azr = AbsoluteZeroReasoner(base_model)

    logger.info(f"Starting training for {num_iterations} iterations...")

    for iteration in range(num_iterations):
        # Étape d'entraînement
        metrics = azr.training_step(batch_size=batch_size)

        # Log des métriques
        if iteration % 5 == 0:
            logger.info(f"Iteration {iteration}: "
                       f"Success rate: {metrics['success_rate']:.3f}, "
                       f"Avg propose reward: {metrics['avg_propose_reward']:.3f}, "
                       f"Avg solve reward: {metrics['avg_solve_reward']:.3f}")

        # Évaluation périodique
        if iteration % eval_interval == 0 and iteration > 0:
            eval_results = azr.evaluate()
            logger.info(f"Evaluation at iteration {iteration}: "
                       f"Overall accuracy: {eval_results['overall_accuracy']:.3f}")

        # Sauvegarde périodique
        if iteration % save_interval == 0 and iteration > 0:
            azr.save_checkpoint(f"azr_checkpoint_{iteration}.pt")

    logger.info("Training completed!")
    return azr

def main():
    """Fonction principale pour démonstration"""
    logger.info("=== AZR Implementation Demo ===")

    # Créer et entraîner le modèle
    azr_model = train_azr_model(num_iterations=50, batch_size=6)

    # Évaluation finale
    final_results = azr_model.evaluate(num_test_tasks=30)

    logger.info("=== Final Results ===")
    for metric, value in final_results.items():
        logger.info(f"{metric}: {value}")

    # Statistiques du buffer
    buffer_stats = azr_model.task_buffer.get_stats()
    logger.info(f"Task buffer stats: {buffer_stats}")

if __name__ == "__main__":
    main()
